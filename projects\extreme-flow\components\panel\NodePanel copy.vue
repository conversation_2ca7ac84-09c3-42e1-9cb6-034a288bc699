<!-- <template>
    <div class="drag-panel" style="position: fixed;z-index:9999;">
        <div v-for="(item, index) in componentsList" :key="index"
            @mousedown="item.type && lf.dnd.startDrag({ type: item.type, properties: item.properties })"
            @mouseup="lf.dnd.stopDrag()">
            <img :src="item.icon" alt="" />
            <p>{{ item.label }}</p>
        </div>
    </div>
</template>
<script>
import LogicFlow from "@logicflow/core"
import config from "./config"

export default {
    props: {
        lf: {
            type: LogicFlow,
            required: true
        }
    },
    data() {
        return {
            componentsList: [
                ...config
            ]
        }
    },
    methods: {
    }
};
</script>
<style lang="scss" scoped>
.drag-panel {
    width: 130px;
    margin: 5px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    div {
        height: 34px;
        background: #fff;
        border-radius: 9px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding-left: 25px;
        font-size: 13px;
        font-weight: bold;
        margin-bottom: 10px;
        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.2));

        &:hover {
            filter: drop-shadow(2px 2px 5px rgba(0, 0, 0, 0.4));
        }

        img {
            height: 20px;
            margin-right: 10px;
        }
    }

}
</style> -->