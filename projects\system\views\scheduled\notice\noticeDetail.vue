<template>
  <el-card shadow="always">
    <div class="label" slot="header">
      <label>通知详情</label>
    </div>
    <el-descriptions>
      <el-descriptions-item label="名称">{{ value.name }}</el-descriptions-item>
      <el-descriptions-item label="任务组">{{ value.groupName }}</el-descriptions-item>
      <el-descriptions-item label="通知预设">{{ presetTask.name }}</el-descriptions-item>
      <el-descriptions-item label="是否开启">
        <el-switch v-show="'undefined' !== typeof(value.enable)" v-model="value.enable" @change="statusChange" :disabled="loading" />
      </el-descriptions-item>
      <el-descriptions-item label="时间间隔">{{ value.intervalTime}} {{dict.timeUnit[value.intervalUnit] || '' }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ value.createTime }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{ value.updateTime }}</el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>
<script>
import notice from '@system/api/scheduled/notice'
import {getPresetTaskDetail} from '@system/api/notice/manager'
import dict from './dict'
export default {
  name: 'noticeDetail',
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      }
    },
    statuses: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    dict() {
      return dict
    }
  },
  data() {
    return {
      loading: false,
      presetTask:{}
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  watch: {
    "value.noticeId": {
      handler(val) { 
        if (val) {
          getPresetTaskDetail(val).then(res => { 
            this.presetTask = res
          })
        } else {
          this.presetTask = {}
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    statusChange() {
      this.$confirm(`是否将通知状态修改为: ${this.value.enable ? '启用' : '停用'}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        notice.update({id: this.value.id, enable: this.value.enable}).then(res => {
          this.$message({
            type: 'success',
            message: '修改成功!'
          });
          this.loading = false
        }).catch(e => {
          this.loading = false
          this.$set(this.value, 'enable', !this.value.enable)
        })
      })
    }
  }
}
</script>

<style scoped>

.label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>