<template>
  <div class="dict-container">
    <!-- 左侧字典树 -->
    <div class="dict-tree">
      <div class="tree-header">
        <div class="header-title">
          <span>字典列表</span>
          <el-button type="primary" size="small" @click="handleAddDict">
            <i class="el-icon-plus"></i> 新增字典
          </el-button>
        </div>
        <div class="search-box">
          <el-input v-model="searchKeyword" placeholder="搜索字典名称/键名" prefix-icon="el-icon-search" clearable size="small"
            @clear="handleSearchClear" />
        </div>
      </div>
      <div class="tree-container">
        <el-tree ref="dictTree" :data="treeData" :props="defaultProps" :filter-node-method="filterNode" node-key="id"
          highlight-current lazy :load="loadNode" @node-click="handleNodeClick"
          :default-expanded-keys="expandedKeys"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse">
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <div class="node-content">
              <i :class="data.type === 'dict' ? 'el-icon-notebook-2' : 'el-icon-collection-tag'"></i>
              <span :class="{ 'highlight': isHighlighted(node, searchKeyword) }">
                {{ node.label }}
              </span>
              <el-tag size="mini" :type="data.type === 'dict' ? 'primary' : 'info'" effect="plain">
                {{ data.type === 'dict' ? data.dataKey : data.value }}
              </el-tag>
            </div>
          </span>
        </el-tree>
      </div>
    </div>

    <!-- 右侧详情 -->
    <div class="dict-detail" v-if="currentNode">
      <div class="detail-content">
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-info"></i>
            <span>基本信息</span>
            <div class="section-actions">
              <el-button type="primary" size="small" icon="el-icon-edit" class="action-button"
                @click="handleEdit(currentNode)">
                {{ currentNode.type === 'dict' ? '编辑字典' : '编辑字典项' }}
              </el-button>
              <el-button type="danger" size="small" icon="el-icon-delete" class="action-button"
                @click="handleDelete(currentNode)">
                {{ currentNode.type === 'dict' ? '删除字典' : '删除字典项' }}
              </el-button>
            </div>
          </div>
          <div class="info-list">
            <template v-if="currentNode.type === 'dict'">
              <div class="info-item">
                <label>字典名称：</label>
                <div class="value-with-copy">
                  <span>{{ currentNode.name }}</span>
                  <el-tooltip content="复制名称" placement="top" :hide-after="1000">
                    <el-button type="text" size="mini" class="copy-btn" @click="handleCopy(currentNode.name)">
                      <i class="el-icon-document-copy"></i>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
              <div class="info-item">
                <label>字典键名：</label>
                <div class="value-with-copy">
                  <span>{{ currentNode.dataKey }}</span>
                  <el-tooltip content="复制键名" placement="top" :hide-after="1000">
                    <el-button type="text" size="mini" class="copy-btn" @click="handleCopy(currentNode.dataKey)">
                      <i class="el-icon-document-copy"></i>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
              <div class="info-item">
                <label>公开：</label>
                <span>
                  <el-tag size="small" :type="currentNode.isPublic ? 'success' : 'info'">
                    {{ currentNode.isPublic ? '是' : '否' }}
                  </el-tag>
                </span>
              </div>
              <div class="info-item">
                <label>是否级联：</label>
                <span>
                  <el-tag size="small" :type="currentNode.isCascade ? 'primary' : 'info'">
                    {{ currentNode.isCascade ? '是' : '否' }}
                  </el-tag>
                </span>
              </div>
            </template>
            <template v-else>
              <div class="info-item">
                <label>字典项名称：</label>
                <div class="value-with-copy">
                  <span>{{ currentNode.name }}</span>
                  <el-tooltip content="复制名称" placement="top" :hide-after="1000">
                    <el-button type="text" size="mini" class="copy-btn" @click="handleCopy(currentNode.name)">
                      <i class="el-icon-document-copy"></i>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
              <div class="info-item">
                <label>字典项值：</label>
                <div class="value-with-copy">
                  <span>{{ currentNode.value }}</span>
                  <el-tooltip content="复制值" placement="top" :hide-after="1000">
                    <el-button type="text" size="mini" class="copy-btn" @click="handleCopy(currentNode.value)">
                      <i class="el-icon-document-copy"></i>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
              <div class="info-item">
                <label>排序：</label>
                <span>{{ currentNode.sort }}</span>
              </div>
            </template>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ currentNode.createTime }}</span>
            </div>
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ currentNode.updateTime }}</span>
            </div>
            <div class="info-item full">
              <label>备注：</label>
              <span>{{ currentNode.remark || '暂无备注' }}</span>
            </div>
          </div>
        </div>

        <!-- 子项列表 -->
        <div class="info-section" v-if="currentNode">
          <div class="section-title">
            <i class="el-icon-collection-tag"></i>
            <span>{{ currentNode.type === 'dict' ? '字典项列表' : '子项列表' }}</span>
            <div class="section-actions">
              <template v-if="isEditing">
                <el-button type="primary" size="small" icon="el-icon-plus" class="action-button add-button"
                  @click="handleAddRow">
                  新增行
                </el-button>
                <el-button type="success" size="small" icon="el-icon-check" class="action-button save-button"
                  @click="handleBatchSave" :loading="saving">
                  保存
                </el-button>
                <el-button type="info" size="small" icon="el-icon-close" class="action-button cancel-button"
                  @click="handleCancelEdit">
                  取消
                </el-button>
              </template>
              <el-button v-else type="primary" size="small" icon="el-icon-edit" class="action-button edit-button"
                @click="handleEnterEdit">
                编辑
              </el-button>
            </div>
          </div>
          <el-table v-loading="loading" :data="dictItems" style="width: 100%" size="small" height="calc(100% - 110px)" class="custom-table">
            <el-table-column label="名称" min-width="150">
              <template slot-scope="{row}">
                <el-input v-if="isEditing" v-model="row.name" placeholder="请输入名称" size="small">
                  <template slot="append">
                    <span class="required-star">*</span>
                  </template>
                </el-input>
                <span v-else>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数据值" min-width="120">
              <template slot-scope="{row}">
                <el-input v-if="isEditing" v-model="row.value" placeholder="请输入数据值" size="small">
                  <template slot="append">
                    <span class="required-star">*</span>
                  </template>
                </el-input>
                <div v-else class="value-cell">
                  <span>{{ row.value }}</span>
                  <el-tag v-if="isDuplicateValue(row.value)" size="mini" type="danger">重复值</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="排序" :width=rpx(90) align="center">
              <template slot-scope="{row}">
                <el-input-number v-if="isEditing" v-model="row.sort" :min="0" controls-position="right" size="small"
                  style="width: 80px" />
                <span v-else>{{ row.sort }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="150">
              <template slot-scope="{row}">
                <el-input v-if="isEditing" v-model="row.remark" placeholder="请输入备注" size="small" />
                <span v-else>{{ row.remark }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="isEditing" label="操作" width="80" align="center">
              <template slot-scope="{row, $index}">
                <el-button size="mini" type="text" class="danger" @click="handleRemoveRow($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 添加/修改字典对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dictDialogVisible" width="580px" :close-on-click-modal="false"
      :append-to-body="true" custom-class="dict-dialog">
      <el-form ref="dictForm" :model="dictForm" :rules="dictRules" label-width="100px" size="small">
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="dictForm.name" placeholder="请输入字典名称" clearable>
            <i slot="prefix" class="el-icon-notebook-2"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="字典键名" prop="dataKey">
          <el-input v-model="dictForm.dataKey" placeholder="请输入字典键名" clearable>
            <i slot="prefix" class="el-icon-key"></i>
          </el-input>
        </el-form-item>
        <el-form-item label="字典属性">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-switch v-model="dictForm.isPublic" active-text="公开" inactive-text="私有" />
            </el-col>
            <el-col :span="12">
              <el-switch v-model="dictForm.isCascade" active-text="级联" inactive-text="非级联" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="备注说明">
          <el-input type="textarea" v-model="dictForm.remark" placeholder="请输入备注说明" :rows="3" resize="none" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dictDialogVisible = false" plain>取 消</el-button>
        <el-button type="primary" @click="submitDictForm" :loading="saving">
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 添加/修改字典项对话框 -->
    <el-dialog :title="itemDialogTitle" :visible.sync="itemDialogVisible" width="500px" :close-on-click-modal="false"
      :append-to-body="true">
      <el-form ref="itemForm" :model="itemForm" :rules="itemRules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="itemForm.name" placeholder="请输入字典项名称" />
        </el-form-item>
        <el-form-item label="数据值" prop="value">
          <el-input v-model="itemForm.value" placeholder="请输入字典项值" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="itemForm.sort" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="itemForm.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="itemDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitItemForm" :loading="saving">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDictList, addDict, updateDict, deleteDict, getDictItemList, addDictItem, updateDictItem, deleteDictItem } from '@system/api/sys/treeDict'

export default {
  data() {
    return {
      searchKeyword: '',
      treeData: [],
      expandedKeys: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: (data, node) => {
          return data.leaf === true
        }
      },
      currentNode: null,
      dictDialogVisible: false,
      itemDialogVisible: false,
      dialogTitle: '',
      itemDialogTitle: '',
      dictForm: {
        id: undefined,
        name: '',
        dataKey: '',
        isPublic: true,
        isCascade: false,
        remark: ''
      },
      itemForm: {
        id: undefined,
        dictId: undefined,
        parentId: undefined,
        name: '',
        value: '',
        sort: 0,
        remark: ''
      },
      dictRules: {
        name: [
          { required: true, message: '请输入字典名称', trigger: 'blur' }
        ],
        dataKey: [
          { required: true, message: '请输入字典键名', trigger: 'blur' }
        ]
      },
      itemRules: {
        name: [
          { required: true, message: '请输入字典项名称', trigger: 'blur' }
        ],
        value: [
          { required: true, message: '请输入字典项值', trigger: 'blur' }
        ]
      },
      saving: false,
      loading: false,
      dictItems: [],
      deletedItems: [],
      isEditing: false,
      tableRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        value: [
          { required: true, message: '请输入数据值', trigger: 'blur' }
        ]
      },
      duplicateValues: new Set(),
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 节点展开时的处理
    handleNodeExpand(data, node) {
      // 将展开的节点 id 添加到 expandedKeys 数组中
      if (data && data.id && !this.expandedKeys.includes(data.id)) {
        this.expandedKeys.push(data.id);
      }
    },

    // 节点收起时的处理
    handleNodeCollapse(data, node) {
      // 从 expandedKeys 数组中移除收起的节点 id
      if (data && data.id) {
        const index = this.expandedKeys.indexOf(data.id);
        if (index !== -1) {
          this.expandedKeys.splice(index, 1);
        }
      }
    },
    // 获取字典和字典项列表
    async getList() {
      try {
        // 获取字典列表
        const dictData = await getDictList({
          size: -1,
          current: 1,
          'orders[createTime]': 'desc'
        })

        // 为每个字典添加类型标识
        const dicts = dictData.records.map(dict => ({
          ...dict,
          type: 'dict',
          leaf: false
        }))

        this.treeData = dicts

        // 如果有数据，选中第一个节点
        if (this.treeData && this.treeData.length > 0) {
          this.$nextTick(() => {
            const firstNode = this.treeData[0]
            this.$refs.dictTree.setCurrentKey(firstNode.id)
            this.handleNodeClick(firstNode)
          })
        }
      } catch (error) {
        this.$message.error('获取字典列表失败')
      }
    },
    // 懒加载节点
    async loadNode(node, resolve) {
      if (node.level === 0) {
        return resolve([])
      }

      try {
        if (node.data.type === 'dict') {
          const items = await this.loadAllDictItems(node.data.id)
          resolve(items)
        } else {
          // 如果是字典项节点，直接使用其children
          resolve(node.data.children || [])
        }
      } catch (error) {
        console.error('加载字典项失败:', error)
        resolve([])
      }
    },
    // 加载字典项（用于右侧表格显示）
    async loadDictItems(dictId, parentId = null) {
      this.loading = true
      try {
        const params = {
          dictId,
          size: -1,
          current: 1
        }

        // 如果是字典项节点，添加 parentId 参数
        if (parentId !== null) {
          params.parentId = parentId
        }

        const data = await getDictItemList(params)

        // 只保留直接子项（parentId 匹配当前节点的 id）
        this.dictItems = data.records.filter(item => {
          if (parentId === null) {
            // 如果是字典节点，只显示顶级项（parentId 为 null 的项）
            return item.parentId === null
          } else {
            // 如果是字典项节点，只显示其直接子项
            return item.parentId === parentId
          }
        })
      } catch (error) {
        console.error('加载字典项失败:', error)
        this.$message.error('加载字典项失败')
      } finally {
        this.loading = false
      }
    },
    // 递归加载所有字典项
    async loadAllDictItems(dictId) {
      try {
        const params = {
          dictId,
          size: -1,
          current: 1
        }
        const itemData = await getDictItemList(params)

        // 将字典项按照父子关系组织成树形结构
        const itemMap = new Map()
        const rootItems = []

        // 第一步：先把所有项放入 map
        itemData.records.forEach(item => {
          const node = {
            ...item,
            type: 'item',
            dictId,
            children: [],
            leaf: true // 默认设置为叶子节点，后面会更新
          }
          itemMap.set(String(item.id), node)
        })

        // 第二步：构建树形结构
        itemData.records.forEach(item => {
          const currentNode = itemMap.get(String(item.id))

          if (item.parentId) {
            // 有父节点，作为子节点
            const parentNode = itemMap.get(String(item.parentId))
            if (parentNode) {
              parentNode.children.push(currentNode)
              parentNode.leaf = false // 有子节点，不是叶子节点
            } else {
              // 找不到父节点，作为根节点
              rootItems.push(currentNode)
            }
          } else {
            // 没有父节点，作为根节点
            rootItems.push(currentNode)
          }
        })

        // 第三步：递归设置 leaf 属性
        const setLeafStatus = (nodes) => {
          nodes.forEach(node => {
            if (node.children && node.children.length > 0) {
              node.leaf = false
              setLeafStatus(node.children)
            } else {
              node.leaf = true
            }
          })
        }
        setLeafStatus(rootItems)

        // 第四步：对所有层级进行排序
        const sortNodes = (nodes) => {
          nodes.sort((a, b) => (a.sort || 0) - (b.sort || 0))
          nodes.forEach(node => {
            if (node.children && node.children.length > 0) {
              sortNodes(node.children)
            }
          })
        }
        sortNodes(rootItems)

        return rootItems
      } catch (error) {
        console.error('加载字典项失败:', error)
        return []
      }
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      const searchValue = value.toLowerCase()
      const nameMatch = data.name.toLowerCase().includes(searchValue)
      // 只在字典类型节点中搜索 dataKey
      const keyMatch = data.type === 'dict' && data.dataKey
        ? data.dataKey.toLowerCase().includes(searchValue)
        : false
      return nameMatch || keyMatch
    },
    // 清除搜索
    handleSearchClear() {
      this.$refs.dictTree.filter('')
    },
    // 判断节点是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false
      const searchValue = keyword.toLowerCase()
      const nameMatch = node.label.toLowerCase().includes(searchValue)
      // 只在字典类型节点中搜索 dataKey
      const keyMatch = node.data.type === 'dict' && node.data.dataKey
        ? node.data.dataKey.toLowerCase().includes(searchValue)
        : false
      return nameMatch || keyMatch
    },
    // 节点点击
    async handleNodeClick(data, node) {
      this.currentNode = data
      if (data.type === 'dict') {
        await this.loadDictItems(data.id, null)
      } else {
        await this.loadDictItems(data.dictId, data.id)
      }

      // 如果节点有子节点但未展开，则展开节点
      if (!data.leaf && node && !node.expanded) {
        node.expand()
      }
    },
    // 新增字典
    handleAddDict() {
      this.resetDictForm()
      this.dialogTitle = '新增字典'
      this.dictDialogVisible = true
    },
    // 编辑字典
    handleEditDict(data) {
      this.resetDictForm()
      this.dictForm = { ...data }
      this.dialogTitle = '修改字典'
      this.dictDialogVisible = true
    },
    // 删除字典
    handleDeleteDict(data) {
      this.$confirm('确定要删除该字典及其所有字典项吗？', '警告', {
        type: 'warning'
      }).then(() => {
        return deleteDict(data.id)
      }).then(() => {
        this.$message.success('删除成功')
        this.getList()
        if (this.currentNode && this.currentNode.id === data.id) {
          this.currentNode = null
        }
      }).catch(() => { })
    },
    // 新增字典项
    handleAddItem(data) {
      this.resetItemForm()
      this.itemForm.dictId = data.id
      this.itemDialogTitle = '新增字典项'
      this.itemDialogVisible = true
    },
    // 新增子字典项
    handleAddChild(data) {
      this.resetItemForm()
      this.itemForm.dictId = data.dictId
      this.itemForm.parentId = data.id
      this.itemDialogTitle = '新增子项'
      this.itemDialogVisible = true
    },
    // 编辑字典项
    handleEditItem(data) {
      this.resetItemForm()
      this.itemForm = {
        ...data,
        dictId: data.dictId
      }
      this.itemDialogTitle = '修改字典项'
      this.itemDialogVisible = true
    },
    // 删除字典项
    handleDeleteItem(node, data) {
      const hasChildren = data.children && data.children.length > 0
      const message = hasChildren
        ? '确定要删除该字典项及其所有子项吗？'
        : '确定要删除该字典项吗？'

      this.$confirm(message, '警告', {
        type: 'warning'
      }).then(() => {
        return deleteDictItem(data.id)
      }).then(() => {
        this.$message.success('删除成功')
        // 刷新树节点
        if (data.type === 'item') {
          const parentNode = node.parent
          if (parentNode.data.type === 'dict') {
            this.$refs.dictTree.loadNode(parentNode, true)
          }
        }
        // 刷新表格数据
        if (this.currentNode && this.currentNode.type === 'dict') {
          this.loadDictItems(this.currentNode.id)
        }
        // 清除当前选中节点（如果被删除的是当前节点）
        if (this.currentNode && this.currentNode.id === data.id) {
          this.currentNode = null
        }
      }).catch(() => { })
    },
    // 提交字典表单
    async submitDictForm() {
      try {
        await this.$refs.dictForm.validate()
        this.saving = true

        if (this.dictForm.id) {
          await updateDict(this.dictForm)
          this.$message.success('修改成功')
          
          // 局部刷新树节点
          const node = this.$refs.dictTree.getNode(this.dictForm.id)
          if (node) {
            // 更新节点数据
            const updatedData = { ...node.data, ...this.dictForm }
            this.$set(node, 'data', updatedData)
            // 如果当前选中的就是这个节点，更新currentNode
            if (this.currentNode && this.currentNode.id === this.dictForm.id) {
              this.currentNode = updatedData
            }
          }
        } else {
          await addDict(this.dictForm)
          this.$message.success('新增成功')
          // 只需要获取新字典列表，不需要重置展开状态
          await this.getList()
        }

        this.dictDialogVisible = false
      } catch (error) {
        console.error(error)
        this.$message.error('请检查是否存在重复的字典键名')
      } finally {
        this.saving = false
      }
    },
    // 提交字典项表单
    async submitItemForm() {
      try {
        await this.$refs.itemForm.validate()
        this.saving = true

        if (this.itemForm.id) {
          // 更新字典项
          await updateDictItem(this.itemForm)
          this.$message.success('修改成功')
          
          // 找到并刷新节点
          const node = this.$refs.dictTree.getNode(this.itemForm.id)
          if (node) {
            // 如果是当前节点，更新currentNode
            if (this.currentNode && this.currentNode.id === this.itemForm.id) {
              this.currentNode = { ...this.currentNode, ...this.itemForm }
            }
            
            // 找到字典节点并重新加载其子节点
            let dictNode = node
            while (dictNode && dictNode.data.type !== 'dict') {
              dictNode = dictNode.parent
            }
            
            if (dictNode) {
              dictNode.loaded = false
              dictNode.expand()
            }
          }
          
          // 刷新表格数据
          if (this.currentNode) {
            if (this.currentNode.type === 'dict') {
              await this.loadDictItems(this.currentNode.id)
            } else {
              await this.loadDictItems(this.currentNode.dictId, this.currentNode.id)
            }
          }
        } else {
          // 添加新字典项
          await addDictItem(this.itemForm)
          this.$message.success('新增成功')
          
          // 找到并刷新父节点
          if (this.itemForm.parentId) {
            // 如果有parentId，是添加子项
            const parentNode = this.$refs.dictTree.getNode(this.itemForm.parentId)
            if (parentNode) {
              parentNode.loaded = false
              parentNode.expand()
            }
          } else if (this.itemForm.dictId) {
            // 否则是添加顶级字典项
            const dictNode = this.$refs.dictTree.getNode(this.itemForm.dictId)
            if (dictNode) {
              dictNode.loaded = false
              dictNode.expand()
            }
          }
          
          // 刷新表格数据
          if (this.currentNode) {
            if (this.currentNode.type === 'dict') {
              await this.loadDictItems(this.currentNode.id)
            } else {
              await this.loadDictItems(this.currentNode.dictId, this.currentNode.id)
            }
          }
        }

        this.itemDialogVisible = false
      } catch (error) {
        console.error(error)
        this.$message.error('操作失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    },
    // 重置字典表单
    resetDictForm() {
      this.dictForm = {
        id: undefined,
        name: '',
        dataKey: '',
        isPublic: true,
        isCascade: false,
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs.dictForm && this.$refs.dictForm.clearValidate()
      })
    },
    // 重置字典项表单
    resetItemForm() {
      this.itemForm = {
        id: undefined,
        dictId: undefined,
        parentId: undefined,
        name: '',
        value: '',
        sort: 0,
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs.itemForm && this.$refs.itemForm.clearValidate()
      })
    },
    // 通用编辑处理
    handleEdit(node) {
      if (node.type === 'dict') {
        this.handleEditDict(node)
      } else {
        this.handleEditItem(node)
      }
    },
    // 新增行
    handleAddRow() {
      this.dictItems.push({
        id: undefined,
        dictId: this.currentNode.type === 'dict' ? this.currentNode.id : this.currentNode.dictId,
        parentId: this.currentNode.type === 'dict' ? null : this.currentNode.id,
        name: '',
        value: '',
        sort: 0,
        remark: ''
      })
    },
    // 删除行
    handleRemoveRow(index) {
      const item = this.dictItems[index]
      // 如果是已有的数据（有id），添加到删除列表中
      if (item.id) {
        this.deletedItems.push(item.id)
      }
      // 从列表中移除
      this.dictItems.splice(index, 1)
    },
    // 检查值是否重复
    isDuplicateValue(value) {
      if (!value) return false;
      const valueCount = this.dictItems.filter(item => item.value === value).length;
      return valueCount > 1;
    },
    // 修改批量保存方法
    async handleBatchSave() {
      // 验证必填字段
      let hasError = false;
      this.dictItems.forEach(item => {
        if (!item.name || !item.value) {
          hasError = true;
        }
      });

      if (hasError) {
        this.$message.error('名称和数据值为必填项，请检查');
        return;
      }

      // 检查重复值
      const duplicateValues = new Set();
      const valueMap = new Map();
      this.dictItems.forEach(item => {
        if (valueMap.has(item.value)) {
          duplicateValues.add(item.value);
        } else {
          valueMap.set(item.value, true);
        }
      });

      if (duplicateValues.size > 0) {
        try {
          await this.$confirm(
            `检测到以下数据值重复：${Array.from(duplicateValues).join(', ')}，是否继续保存？`,
            '警告',
            {
              confirmButtonText: '继续保存',
              cancelButtonText: '取消',
              type: 'warning'
            }
          );
        } catch (err) {
          return;
        }
      }

      try {
        this.saving = true;

        // 区分新增和更新的数据
        const newItems = []
        const updateItems = []

        this.dictItems.forEach(item => {
          const itemData = {
            ...item,
            dictId: this.currentNode.type === 'dict' ? this.currentNode.id : this.currentNode.dictId,
            parentId: this.currentNode.type === 'dict' ? null : this.currentNode.id
          }

          if (item.id) {
            updateItems.push(itemData)
          } else {
            newItems.push(itemData)
          }
        })

        const promises = []

        // 处理新增数据
        if (newItems.length > 0) {
          promises.push(addDictItem(...newItems))
        }

        // 处理更新数据
        if (updateItems.length > 0) {
          promises.push(updateDictItem(...updateItems))
        }

        // 处理删除数据
        if (this.deletedItems && this.deletedItems.length > 0) {
          promises.push(deleteDictItem(this.deletedItems))
        }

        await Promise.all(promises)

        this.$message.success('保存成功')

        // 清空删除列表
        this.deletedItems = []

        // 刷新树节点
        if (this.currentNode) {
          if (this.currentNode.type === 'dict') {
            // 如果是字典节点，直接刷新该节点
            const node = this.$refs.dictTree.getNode(this.currentNode.id)
            if (node) {
              node.loaded = false
              node.expand()
            }
          } else if (this.currentNode.type === 'item') {
            // 如果是字典项节点，找到并刷新字典节点
            const treeNode = this.$refs.dictTree.getNode(this.currentNode.id)
            if (treeNode) {
              let current = treeNode
              // 向上查找直到找到字典节点
              while (current && current.data.type !== 'dict') {
                current = current.parent
              }
              if (current) {
                current.loaded = false
                current.expand()
              }
            }
          }
        }

        // 刷新表格数据
        if (this.currentNode) {
          if (this.currentNode.type === 'dict') {
            await this.loadDictItems(this.currentNode.id)
          } else if (this.currentNode.type === 'item') {
            await this.loadDictItems(this.currentNode.dictId, this.currentNode.id)
          }
        }

        // 保存成功后退出编辑模式
        this.isEditing = false
      } catch (error) {
        console.error(error)
        this.$message.error('保存失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    },
    // 进入编辑模式
    handleEnterEdit() {
      this.isEditing = true
    },
    // 取消编辑
    handleCancelEdit() {
      this.$confirm('确定要取消编辑吗？未保存的修改将会丢失', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.isEditing = false
        // 重新加载数据
        if (this.currentNode.type === 'dict') {
          this.loadDictItems(this.currentNode.id)
        } else {
          this.loadDictItems(this.currentNode.dictId, this.currentNode.id)
        }
      }).catch(() => { })
    },
    // 添加复制方法
    handleCopy(text) {
      if (!text) return

      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('复制成功')
        }).catch(() => {
          this.fallbackCopy(text)
        })
      } else {
        this.fallbackCopy(text)
      }
    },

    // 复制功能的降级处理
    fallbackCopy(text) {
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed'
      textarea.style.opacity = '0'
      document.body.appendChild(textarea)
      textarea.select()
      try {
        document.execCommand('copy')
        this.$message.success('复制成功')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      document.body.removeChild(textarea)
    },
    // 通用删除处理
    handleDelete(node) {
      if (!node) return

      const isDict = node.type === 'dict'
      const message = isDict
        ? '确定要删除该字典及其所有字典项吗？'
        : node.children && node.children.length > 0
          ? '确定要删除该字典项及其所有子项吗？'
          : '确定要删除该字典项吗？'

      this.$confirm(message, '警告', {
        type: 'warning'
      }).then(() => {
        if (isDict) {
          return deleteDict(node.id)
        } else {
          return deleteDictItem(node.id)
        }
      }).then(() => {
        this.$message.success('删除成功')

        if (isDict) {
          // 如果删除的是字典，更新树，但保持展开状态
          // 获取当前展开的节点
          const expandedIds = [...this.expandedKeys]
          // 移除被删除的字典及其子项
          const removeDeletedIds = (ids, nodeId) => {
            return ids.filter(id => {
              if (id === nodeId) return false;
              // 如果有子节点的ID在expandedKeys中，也需要移除
              const nodeInTree = this.$refs.dictTree.getNode(id);
              if (nodeInTree && nodeInTree.data && 
                  nodeInTree.data.dictId === nodeId) {
                return false;
              }
              return true;
            });
          }
          this.expandedKeys = removeDeletedIds(expandedIds, node.id)
          
          // 获取新的字典列表
          this.getList()
        } else {
          // 如果删除的是字典项，只需要刷新其父节点
          // 获取要刷新的字典节点
          let dictNode = null
          const treeNode = this.$refs.dictTree.getNode(node.id)
          if (treeNode && treeNode.parent) {
            let current = treeNode.parent
            // 向上查找直到找到字典节点
            while (current && current.data.type !== 'dict') {
              current = current.parent
            }
            if (current) {
              dictNode = current
            }
          }

          // 刷新字典节点
          if (dictNode) {
            dictNode.loaded = false
            dictNode.expand()
          }

          // 如果当前显示的是字典的直接子项列表，也需要刷新表格
          if (this.currentNode && this.currentNode.type === 'dict') {
            this.loadDictItems(this.currentNode.id)
          } else if (this.currentNode && this.currentNode.type === 'item') {
            // 如果当前显示的是某个字典项的子项列表，也需要刷新
            this.loadDictItems(this.currentNode.dictId, this.currentNode.id)
          }
        }

        // 清除当前选中节点（如果被删除的是当前节点）
        if (this.currentNode && this.currentNode.id === node.id) {
          this.currentNode = null
        }
      }).catch(() => { })
    }
  },
  watch: {
    searchKeyword(val) {
      this.$refs.dictTree.filter(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.dict-container {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  gap: 24px;
  overflow: hidden;
  
  @media (max-width: 1024px) {
    flex-direction: column;
    height: auto;
  }

  .dict-tree {
    width: 380px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    
    @media (max-width: 1024px) {
      width: 100%;
      max-height: 500px;
    }

    .tree-header {
      padding: 20px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .header-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        span {
          font-size: 18px;
          font-weight: 600;
          color: #1a1f36;
          letter-spacing: 0.5px;
          position: relative;
          padding-left: 12px;
          
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: linear-gradient(to bottom, #409EFF, #64B5F6);
            border-radius: 3px;
          }
        }

        .el-button {
          padding: 9px 18px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 10px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            transform: translateY(-2px);
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }

      .search-box {
        .el-input {
          width: 100%;
          ::v-deep .el-input__inner {
            border-radius: 10px;
            height: 36px;
            background: #f7f9fc;
            border: 1px solid #e7ebf3;
            
            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
          
          ::v-deep .el-input__prefix {
            left: 10px;
            i {
              color: #8492a6;
            }
          }
        }
      }
    }

    .tree-container {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 16px;
      background-color: #fff;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      ::v-deep .el-tree {
        background: transparent;
        
        .el-tree-node__content {
          height: auto;
          min-height: 32px;
          padding: 4px 0;
          
          &:hover {
            background-color: #f5f7fa;
          }

          .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            padding-right: 8px;
            
            .node-content {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;
              min-width: 0;
              padding: 2px 0;
              white-space: nowrap;
              overflow: hidden;

              i {
                color: #409EFF;
                font-size: 14px;
                flex-shrink: 0;
              }

              span {
                font-size: 14px;
                color: #1a1f36;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 180px;
              }

              .el-tag {
                padding: 0 6px;
                height: 20px;
                line-height: 18px;
                border-radius: 4px;
                font-size: 12px;
                flex-shrink: 0;
                background: #f0f7ff;
                border-color: #e0efff;
                color: #409EFF;
                max-width: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                
                &.el-tag--info {
                  background: #f4f4f5;
                  border-color: #e9e9eb;
                  color: #909399;
                }
              }
            }

            .node-actions {
              display: none !important;
            }
          }
        }

        .el-tree-node.is-current > .el-tree-node__content {
          background-color: #ecf5ff !important;
          color: #409EFF;
          font-weight: 500;
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
          
          .node-content {
            span {
              color: #409EFF;
            }
          }

          .node-actions {
            display: none !important;
          }
        }
      }
    }
  }

  .dict-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;
    padding: 0;
    height: 100%;
    
    @media (max-width: 1024px) {
      height: auto;
      min-height: 500px;
    }

    .detail-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: 100%;
      overflow: hidden;

      .info-section {
        background-color: #fff;
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

        &:hover {
          box-shadow: 0 6px 24px rgba(31, 45, 61, 0.1);
        }

        &:first-child {
          flex-shrink: 0;
        }

        &:last-child {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;
          flex-wrap: wrap;
          gap: 12px;

          i {
            margin-right: 8px;
            color: #409EFF;
          }

          .section-actions {
            margin-left: auto;
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            
            @media (max-width: 768px) {
              margin-left: 0;
              width: 100%;
              margin-top: 8px;
            }

            .action-button {
              padding: 6px 12px;
              font-weight: 500;
              transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
              border-radius: 8px;
              
              &.el-button--primary {
                background-color: #409EFF;
                border-color: #409EFF;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
                
                &:hover {
                  transform: translateY(-2px);
                  background-color: #5aacff;
                  border-color: #5aacff;
                  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                }
              }

              &.el-button--danger {
                background-color: #F56C6C;
                border-color: #F56C6C;
                box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
                
                &:hover {
                  transform: translateY(-2px);
                  background-color: #f78989;
                  border-color: #f78989;
                  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
                }
              }

              i {
                margin-right: 4px;
                font-size: 14px;
              }
            }
          }
        }

        .info-list {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;
          
          @media (max-width: 1200px) {
            grid-template-columns: 1fr;
          }

          .info-item {
            display: flex;
            align-items: flex-start;
            flex-wrap: nowrap;
            min-width: 0;
            
            @media (max-width: 768px) {
              flex-direction: column;
              align-items: flex-start;
            }

            &.full {
              grid-column: span 2;
              
              @media (max-width: 1200px) {
                grid-column: span 1;
              }
              
              > span {
                white-space: normal;
                word-break: break-word;
              }
            }

            label {
              width: 90px;
              flex-shrink: 0;
              color: #606266;
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              
              @media (max-width: 768px) {
                width: 100%;
                margin-bottom: 4px;
              }
            }
            
            > span {
              flex: 1;
              color: #2c3e50;
              line-height: 1.4;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              min-width: 0;
            }

            .value-with-copy {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;
              min-width: 0;
              overflow: hidden;

              span {
                color: #2c3e50;
                line-height: 1.4;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                min-width: 0;
                max-width: calc(100% - 30px);
              }

              .copy-btn {
                flex-shrink: 0;
                padding: 2px;
                color: #909399;
                transition: all 0.3s;
                width: 22px;
                text-align: center;

                &:hover {
                  color: #409EFF;
                }
              }
            }
          }
        }

        ::v-deep .el-table {
          &.el-table--border {
            border-radius: 12px;
            overflow: hidden;
            margin-top: 12px;
          }
          
          th {
            background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
            font-weight: 600;
            color: #1a1f36;
            height: 40px;
            padding: 8px 0;
          }
          
          td {
            padding: 6px 0;
          }

          .el-button {
            padding: 2px 4px;
            margin: 0;
            
            i {
              margin-right: 0;
              font-size: 13px;
            }

            &.danger {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }
}

/* 弹出框样式 */
::v-deep .dict-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    background: #f8f9fb;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    
    .el-button {
      border-radius: 10px;
      padding: 10px 24px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);
        
        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .el-form {
    .el-form-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
      }

      .el-input__inner,
      .el-textarea__inner {
        border-radius: 10px;
        border: 1px solid #e0e5ee;
        
        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
        }
        
        &:hover {
          border-color: #c0d0e9;
        }
      }

      .el-input-group__prepend {
        background: #f7f9fc;
        border: 1px solid #e0e5ee;
        border-right: none;
        border-radius: 10px 0 0 10px;
        padding: 0 12px;

        i {
          color: #409EFF;
        }
      }
    }
  }
}

.required-star {
  color: #F56C6C;
  margin-left: 4px;
}

.el-table {
  ::v-deep .el-input-group__append {
    padding: 0 5px;
    background-color: transparent;
    border: none;
  }
}

.custom-table {
  ::v-deep {
    .el-table__body-wrapper::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(144, 147, 153, 0.3);

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }

    .el-table__body-wrapper::-webkit-scrollbar-track {
      background: transparent;
    }
  }
}

.value-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .el-tag {
    margin-left: 4px;
    
    &.el-tag--danger {
      background-color: #fff0f0;
      border-color: #ffd9d9;
      color: #ff4d4f;
    }
  }
}
</style>