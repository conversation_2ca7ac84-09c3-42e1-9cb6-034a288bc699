<template>
  <div class="app-edit">
    <div class="page-header">
      <div class="header-content">
        <el-page-header @back="$router.push('/integration/sso-apps/list')">
          <template #content>
            <div class="page-title">
              <i class="el-icon-edit"></i>
              <span>编辑应用：{{ appName }}</span>
            </div>
          </template>
        </el-page-header>
        <div class="header-desc">
          修改单点登录应用配置信息，保存后即时生效
        </div>
      </div>
    </div>

    <div class="edit-content">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-form ref="basicForm" :model="basicInfo" :rules="basicRules" label-width="120px">
            <el-form-item label="应用编码" prop="code">
              <el-input v-model="basicInfo.code" placeholder="请输入应用编码" />
            </el-form-item>
            <el-form-item label="应用名称" prop="name">
              <el-input v-model="basicInfo.name" placeholder="请输入应用名称" />
            </el-form-item>
            <el-form-item label="应用描述" prop="description">
              <el-input type="textarea" v-model="basicInfo.description" :rows="3" placeholder="请输入应用描述" />
            </el-form-item>
            <el-form-item label="应用地址" prop="address">
              <el-input v-model="basicInfo.address" placeholder="请输入应用访问地址" />
            </el-form-item>
            <el-form-item label="启用状态" prop="enabled">
              <el-switch v-model="basicInfo.enabled" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="认证配置" name="auth">
          <auth-config ref="authConfig" />
        </el-tab-pane>
      </el-tabs>

      <div class="form-actions">
        <el-button @click="$router.go(-1)">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import AuthConfig from './components/auth-config.vue'
import { getAppDetail, updateApp } from '@system/api/integration/sso-app'

export default {
  name: 'AppEdit',
  components: {
    AuthConfig
  },
  data() {
    return {
      activeTab: 'basic',
      saving: false,
      loading: false,
      appName: '',
      basicInfo: {
        code: '',
        name: '',
        icon: '',
        description: '',
        address: '',
        enabled: true,
        ssoConfigJson: {}
      },
      basicRules: {
        code: [
          { required: true, message: '请输入应用编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入应用地址', trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '不能超过200个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 获取应用ID
    const appId = this.$route.params.id
    // TODO: 根据ID获取应用详情
    this.loadAppData(appId)
  },
  methods: {
    async loadAppData(appId) {
      try {
        this.loading = true
        const appData = await getAppDetail(appId)
        this.appName = appData.name
        this.basicInfo = {
          id: appData.id,
          code: appData.code,
          name: appData.name,
          icon: appData.icon,
          description: appData.description,
          address: appData.address,
          enabled: appData.enabled,
        }

        if (this.$refs.authConfig) {
          this.$refs.authConfig.setConfig({
            ssoConfigUniqueId: appData.ssoConfigUniqueId,
            authType: appData.protocol,
            clientId: appData.ssoConfigJson?.clientId || '',
            clientSecret: appData.ssoConfigJson?.clientSecret || '',
            redirectUri: appData.ssoConfigJson?.redirectUri || '',
            verifyMode: appData.ssoConfigJson?.verifyMode || 'LOOSE'
          })
        }

      } catch (error) {
        console.error(error)
        this.$message.error('加载应用数据失败')
      } finally {
        this.loading = false
      }
    },
    async handleSave() {
      try {
        this.saving = true
        await this.$refs.basicForm.validate()

        if (this.$refs.authConfig) {
          const authConfig = await this.$refs.authConfig.validate()

          // 构建更新数据
          const updatedApp = {
            ...this.basicInfo,
            protocol: authConfig.authType,
            ssoConfigUniqueId: authConfig.ssoConfigUniqueId,
            // 构建 ssoConfigJson
            ssoConfigJson: {
              clientId: authConfig.clientId,
              clientSecret: authConfig.clientSecret,
              redirectUri: authConfig.redirectUri,
              verifyMode: authConfig.verifyMode
            }
          }

          await updateApp(this.basicInfo.id, updatedApp)

          this.$message.success('保存成功')
          this.$router.push('/integration/sso-apps/list')
        }
      } catch (error) {
        console.error(error)
        this.$message.error('保存失败，请检查表单')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-edit {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 48px);

  .page-header {
    margin-bottom: 24px;
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);


    .header-content {
      margin: 0 auto;

      ::v-deep .el-page-header {
        .el-page-header__left {
          .el-icon-back {
            font-size: 18px;
            color: #909399;
            margin-right: 20px;
          }

          .el-page-header__title {
            font-size: 14px;
            color: #909399;
          }
        }

        .el-page-header__content {
          font-size: 16px;
          color: #303133;
        }
      }

      .header-desc {
        margin-top: 16px;
        color: #909399;
        font-size: 14px;
        padding-left: 32px;
      }
    }

    .page-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      min-width: 200px;

      i {
        margin-right: 8px;
        font-size: 18px;
        color: #409EFF;
      }
    }
  }

  .edit-content {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }

  .form-actions {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;

    .el-button {
      min-width: 120px;
      margin-left: 12px;
    }
  }
}
</style>