<template>
    <div class="chat-container">
        <div v-if="$route.query.title != 'no'" class="title">
            <div style="height: 10px;"></div>
            <div class="t" v-text="$route.query.title || '献血AI助手'"></div>
        </div>
        <div ref="history-container" class="history-container">
            <top @chat="(msg) => sendMessage(msg)"></top>
            <history style="margin-bottom: 120px;" :messages="messages" @ai-event="handleAiEvent"></history>
        </div>
        <div class="input-container">
            <div class="input-box">
                <el-input v-model="inputValue" placeholder="开始探索未知的问题吧" type="textarea"
                    :autosize="{ minRows: 1, maxRows: 3 }" @input="change">
                </el-input>
                <div class="append">
                    <i class="send" :state="state" @click="send(state)"></i>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import top from './top.vue'
import history from '../history/index.vue'
import { sendAppMessage, sendMessage, stopTask } from '../../api/ai'

export default {
    components: {
        top,
        history
    },
    data() {
        return {
            inputValue: '',
            stateEnum: {
                WAITING: 'waiting',
                SENDING: 'sending',
                SENDED: 'sended'
            },
            state: 'waiting',
            messages: [],
            curTaskId: '',
            // appkey: this.$route.query.appkey || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiNjNjNzBjYjQtYjVjMS00MjRlLWE0YjQtYjExM2M4MTc5ZWI3IiwiZXhwIjoxNzQyNTMxMzg1LCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.yuwzlDvdYpvO5ufmMJMwR0XEc5PokP1V5uN72csmfvs',
            // appid: this.$route.query.appid || 'ceee3bbf-68b1-4a1e-a323-4b9d4c7cf507',
            // inputs: this.$route.query.inputs || encodeURIComponent(JSON.stringify({
            //     "company": "九域聚合",
            //     "p": "公司报销制度"
            // })),
            appkey: this.$route.query.appkey,
            appid: this.$route.query.appid,
            inputs: this.$route.query.inputs
        }
    },
    mounted() {
        if (this.inputs) {
            try {
                this.inputs = JSON.parse(decodeURIComponent(this.inputs))
            } catch (e) {
                this.inputs = {}
            }
        }
    },
    methods: {
        change() {
            if (this.state != this.stateEnum.SENDING) {
                if (this.inputValue.length < 1) {
                    this.state = this.stateEnum.WAITING;
                } else {
                    this.state = this.stateEnum.SENDED;
                }
            }
        },
        handleAiEvent() {
            if (this.state != this.stateEnum.SENDING) {
                this.inputValue = '';
                this.sendMessage(this.messages[this.messages.length - 2]?.content)
            } else {
                if (this.curTaskId) {
                    stopTask(this.curTaskId, this.appkey)
                }
            }
        },
        send(state) {
            if (state == this.stateEnum.SENDED) {
                this.sendMessage();
            }
        },
        sendMessage(msg) {
            if (!this.inputValue && !msg) {
                return
            }

            this.messages.push({
                content: this.inputValue || msg,
                type: 'user',
                // time: new Date().getTime(),
            });
            this.state = this.stateEnum.SENDING;
            const aiMsg = {
                content: '',
                type: 'ai',
                // time: new Date().getTime(),
                task_id: ''
            }
            this.messages.push(aiMsg);

            const callBack = {
                onNext: (data, other) => {
                    this.inputValue = '';
                    aiMsg.content = data
                    aiMsg.task_id = other.task_id
                    this.curTaskId = other.task_id
                    this.$nextTick(() => {
                        this.$refs['history-container'].scrollTop = this.$refs['history-container'].scrollHeight;
                    })
                },
                onComplete: (data, retriever_resources) => {
                    this.state = this.stateEnum.WAITING;
                    this.curTaskId = ''
                    aiMsg.task_id = ''
                    if (retriever_resources) {
                        aiMsg.retriever_resources = retriever_resources
                    }
                },
                onError: (error) => {
                    this.state = this.stateEnum.WAITING;
                    this.curTaskId = ''
                    aiMsg.task_id = ''
                }
            }

            if (this.appid && this.appkey) {
                sendAppMessage(this.inputValue || msg, this.appid, this.appkey, this.inputs || {}, callBack)
            } else {
                sendMessage(this.inputValue || msg, this.appkey, this.inputs || {}, callBack)
            }
        }
    },
}
</script>
<style lang="scss" scoped>
.title {
    height: 36px;
    text-align: center;


    .t {
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 1);
    }

}

.chat-container {
    background: url(../../assets/bg.png) no-repeat;
    background-size: 100% 100%;
    height: 100vh;
}

.history-container {
    height: calc(100% - 36px);
    overflow-y: scroll;
}

.input-container {

    .input-box {
        position: fixed;
        z-index: 100;
        left: 15px;
        right: 15px;
        bottom: 12px;
        width: calc(100% - 30px);
        border-radius: 6px;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0px 2px 4px rgba(224, 220, 222, 1);
        display: flex;
        align-items: center;
        min-height: 42px;

        ::v-deep .el-textarea__inner {
            border: none;
            resize: none;
        }

        .append {
            padding: 0 10px;

            i.send {
                width: 24px;
                height: 24px;
                display: block;
                cursor: pointer;

                &:hover {
                    filter: brightness(1.2);
                }

                &[state='waiting'] {
                    background: url(../../assets/disable-send.png) no-repeat;
                    background-size: 100% 100%;
                }

                &[state='sended'] {
                    background: url(../../assets/send.png) no-repeat;
                    background-size: 100% 100%;
                }

                &[state='sending'] {
                    background: url(../../assets/ongoing.png) no-repeat;
                    background-size: 100% 100%;
                }
            }
        }

    }

}
</style>
