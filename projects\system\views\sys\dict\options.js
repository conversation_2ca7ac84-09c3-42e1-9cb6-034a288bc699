export default {
  api:'sys/dict',
  search: {
    isShow: true,
    showReset: true
  },
  table: {
    // isHasChildren: false, //表格树形结构数据
  },
  formRule:[
    {
      type: "input",
      field: "dataKey",
      className: "dataKey-dom",
      title: "字典键",
      isSearch:true,
      isSearchCol:{md:{span:7}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 20}},
      props:{
        placeholder:"请输入",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"字典键不能为空"
        }
      ]
    },
    {
      type: "input",
      field: "name",
      className: "name-dom",
      title: "字典名",
      isSearch:true,
      isSearchCol:{md:{span:8}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 20}},
      props:{
        placeholder:"请输入",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"字典名不能为空"
        }
      ]
    },
    {
      type: "input",
      field: "value",
      className: "value-dom",
      title: "字典值",
      isSearch:false,
      isTable:false,
      col:{md:{span: 20}},
      props:{
        placeholder:"请输入",
        readonly: false,
        clearable: true,
        disabled: false
      },
    },
    {
      type: "select",
      field: "isValid",
      className: "isValid-dom",
      title: "是否有效",
      isSearch: true,
      isSearchValidate: [],
      isSearchCol: {md: {span: 7}},
      isTable: true,
      col: {md: {span: 20}},
      options: [
        {value: 1, label: "是", disabled: false},
        {value: 0, label: "否", disabled: false}
      ],
      props: {
        multiple: false,
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true // 是否显示清空按钮
      },
      validate: [
        {
          trigger: 'change',
          required: true,
          message: "请选择是否有效"
        }
      ],
    },
    {
      type: "DatePicker",
      field: "createTime",
      className: "createTime-dom",
      title: "创建时间",
      isSearch: false,
      isTable: true,
      //不显示在表单
      isHidden:true,
    },
    {
      type: "input",
      field: "remark",
      className: "remark-dom",
      title: "备注",
      isSearch:false,
      isTable:false,
      col:{md:{span: 20}},
      props:{
        type: "textarea",
        placeholder:"请输入备注",
        readonly: false,
        clearable: true,
        disabled: false
      },
    },

  ]
}
