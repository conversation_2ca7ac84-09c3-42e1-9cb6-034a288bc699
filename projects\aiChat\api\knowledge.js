import request from '../utils/request'

const API = 'knowledge'
// const API = 'http://192.168.1.189:7861'

export default {
  // 获取知识库列表
  getList: () => request.get(`${API}/knowledge_base/list_knowledge_bases`),

  // 创建知识库
  create: (data) => request.post(`${API}/knowledge_base/create_knowledge_base`, {
    knowledge_base_name: data.name,
    vector_store_type: data.vectorStoreType,
    embed_model: data.embedModel
  }),

  // 删除知识库
  delete: (name) => request.post(`${API}/knowledge_base/delete_knowledge_base`, name),

  // 获取知识库中的文件列表
  getFiles: (name) => request.get(`${API}/knowledge_base/list_files`, {
    params: { knowledge_base_name: name }
  }),

  // 上传文件到知识库
  uploadFiles: (data) => {
    const formData = new FormData();

    // 添加文件
    if (Array.isArray(data.files)) {
      data.files.forEach(file => {
        formData.append('files', file);
      });
    } else {
      formData.append('files', data.files);
    }

    // 添加必需参数
    formData.append('knowledge_base_name', data.knowledge_base_name);

    // 添加可选参数
    if (data.override !== undefined) {
      formData.append('override', data.override);
    }
    if (data.to_vector_store !== undefined) {
      formData.append('to_vector_store', data.to_vector_store);
    }
    if (data.chunk_size !== undefined) {
      formData.append('chunk_size', data.chunk_size);
    }
    if (data.chunk_overlap !== undefined) {
      formData.append('chunk_overlap', data.chunk_overlap);
    }
    if (data.zh_title_enhance !== undefined) {
      formData.append('zh_title_enhance', data.zh_title_enhance);
    }
    if (data.not_refresh_vs_cache !== undefined) {
      formData.append('not_refresh_vs_cache', data.not_refresh_vs_cache);
    }
    if (data.docs) {
      formData.append('docs', data.docs);
    }

    return request.post(`${API}/knowledge_base/upload_docs`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // 删除知识库中的文件（同时从向量库中删除）
  deleteFile: (data) => request.post(`${API}/knowledge_base/delete_docs`, {
    knowledge_base_name: data.knowledge_base_name,
    file_names: [data.file_name],
    delete_content: data.delete_content || false,  // 同时删除文件内容
    not_refresh_vs_cache: data.not_refresh_vs_cache || false
  }),

  // 搜索知识库内容
  search: (data) => request.post(`${API}/knowledge_base/search_docs`, {
    knowledge_base_name: data.knowledge_base_name,
    query: data.query,
    top_k: data.top_k || 3,
    score_threshold: data.score_threshold || 1,
    embed_model: data.embed_model || "text2vec-base",
    search_type: data.search_type || "semantic"
  }),

  // 重新加载知识库
  reload: (name) => request.post(`${API}/knowledge_base/reload`, {
    knowledge_base_name: name,
    not_refresh_vs_cache: false
  }),

  // 下载文件
  downloadFile: (data) => request.get(`${API}/knowledge_base/download_doc`, {
    params: {
      knowledge_base_name: data.knowledge_base_name,
      file_name: data.file_name
    },
    responseType: 'blob'
  }),

  // 重新加载文件到向量库
  reloadFile: (data) => request.post(`${API}/knowledge_base/update_docs`, {
    knowledge_base_name: data.knowledge_base_name,
    file_names: Array.isArray(data.file_name) ? data.file_name : [data.file_name],
    chunk_size: data.chunk_size || 750,
    chunk_overlap: data.chunk_overlap || 150,
    zh_title_enhance: data.zh_title_enhance || false,
    override_custom_docs: data.override_custom_docs || false,
    docs: data.docs || '',
    not_refresh_vs_cache: data.not_refresh_vs_cache || false
  }),

  // 更新知识库信息
  update: (data) => request.post(`${API}/knowledge_base/update_knowledge_base`, {
    knowledge_base_name: data.name,
    kb_info: data.description,
    embed_model: data.embedModel
  }),

  // 获取知识库统计信息
  getStatus: (name) => request.get(`${API}/knowledge_base/get_knowledge_base_info`, {
    params: { knowledge_base_name: name }
  }),

  // 获取文档分段内容
  getDocs: (data) => request.post(`${API}/knowledge_base/get_doc_content`, {
    knowledge_base_name: data.knowledge_base_name,
    file_name: data.file_name,
    metadata: data.metadata || {}
  }),

  // 获取文件分段信息
  getFileSegments: (data) => request.get(`${API}/knowledge_base/list_file_segments`, {
    params: {
      knowledge_base_name: data.knowledge_base_name,
      file_name: data.file_name
    }
  })
} 