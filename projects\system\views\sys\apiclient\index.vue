<template>
  <div class="api-client-container">
    <div class="api-client-left">
      <div class="page-header">
        <div class="header-tools">
          <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
            <div style="font-weight: bolder; padding-left: 15px; border-left: 2px #409EFF solid;">客户端API</div>
            <div>
              <el-button type="primary"  @click="handleAdd" icon="el-icon-plus" size="mini">新增
              </el-button>
            </div>
          </div>
          <el-input v-model="keyword" placeholder="请输入客户端名称搜索" prefix-icon="el-icon-search" clearable
            class="search-input" >
            <el-button slot="append" :icon="loading ? 'el-icon-loading':'el-icon-refresh'" type="primary" @click="refreshList" ></el-button>
          </el-input>

        </div>
      </div>
      <div class="table-wrapper">
        <div v-if="apiClientList.length === 0" class="empty-data">
          <i class="el-icon-guide"></i>
          <p>暂无API客户端</p>
          <el-button size="small" type="primary" @click="handleAdd">新增客户端</el-button>
        </div>
        <template v-else>
          <div 
            class="client-item"
            :class="{ active: selectForm?.id === item.id }"
            v-for="(item, index) in apiClientList" 
            v-show="!keyword || item.clientName.includes(keyword)"
            :key="index"
            @click="selectForm = item "
          >
            <div class="client-header">
              <div class="client-icon" :style="{'background': item.enabled ? 'linear-gradient(135deg, #409eff, #64b5f6)' : '#d1d1d1'}">
                <i class="el-icon-user" ></i>
              </div>
              <div class="client-basic">
                <div class="client-name">{{ item.clientName }}</div>
                <div class="client-key">
                  <i class="key-label el-icon-key" />
                  <span class="key-value">{{ item.apiKey }}</span>
                </div>
              </div>
              <div class="client-status">
                <el-switch v-model="item.enabled" @change="toggleClientStatus(item)" style="scale: 0.7;" />
              </div>
            </div>
            <div class="client-footer">
              <div class="client-time" v-if="item.createTime">
              过期时间: {{ getExpireTimeText(item.expireTime) }}
              </div>
              <div class="client-actions">
                <el-tooltip content="查看详情" placement="top">
                  <el-link type="primary" :underline="false" icon="el-icon-view" size="mini" @click.stop="handleDetail(item)" />
                </el-tooltip>
                <el-tooltip content="编辑客户端" placement="top">
                  <el-link type="success" :underline="false" icon="el-icon-edit" size="mini" @click.stop="handleEdit(item)" />
                </el-tooltip>
                <el-tooltip content="重置密钥" placement="top">
                  <el-link type="warning" :underline="false" icon="el-icon-refresh-left" size="mini" @click.stop="handleResetSecret(item)" />
                </el-tooltip>
                <el-tooltip content="删除客户端" placement="top">
                  <el-link type="danger" :underline="false" icon="el-icon-delete" size="mini" @click.stop="handleDelete(item)" />
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 客户端表单对话框 -->
      <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false"
        append-to-body destroy-on-close>
        <el-form ref="apiClientForm" :model="apiClientForm" :rules="rules" label-width="100px" class="api-client-form">
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <el-form-item label="客户端名称" prop="clientName">
              <el-input v-model="apiClientForm.clientName" placeholder="请输入客户端名称" />
            </el-form-item>

            <el-form-item label="过期时间" prop="expireTime">
              <div class="expire-time-wrapper">
                <el-radio-group v-model="expireTimeType" @change="handleExpireTypeChange">
                  <el-radio-button label="custom">自定义</el-radio-button>
                  <el-radio-button label="7">7天</el-radio-button>
                  <el-radio-button label="30">30天</el-radio-button>
                  <el-radio-button label="90">90天</el-radio-button>
                  <el-radio-button label="never">永不过期</el-radio-button>
                </el-radio-group>

                <el-date-picker v-if="expireTimeType === 'custom'" v-model="apiClientForm.expireTime" type="datetime"
                  placeholder="请选择过期时间" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="{
                    disabledDate(time) {
                      return time.getTime() < Date.now() - 8.64e7
                    }
                  }" />
              </div>
            </el-form-item>

            <el-form-item label="备注说明" prop="remark">
              <el-input v-model="apiClientForm.remark" type="textarea" :rows="3" placeholder="请输入备注说明" />
            </el-form-item>

            <template v-if="dialogType === 'add'">
              <div class="key-secret-preview">
                <div class="preview-item">
                  <label>API Key:</label>
                  <span class="preview-value">系统自动生成</span>
                </div>
                <div class="preview-item">
                  <label>API Secret:</label>
                  <span class="preview-value">系统自动生成</span>
                </div>
              </div>
            </template>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            确 定
          </el-button>
        </div>
      </el-dialog>

      <!-- 详情对话框 -->
      <el-dialog title="客户端详情" :visible.sync="detailDialogVisible" width="600px" :close-on-click-modal="false"
        append-to-body>
        <div class="detail-container">
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <div class="detail-list">
              <div class="detail-item">
                <label>客户端名称：</label>
                <div class="detail-content">
                  <span>{{ apiClientForm.clientName }}</span>
                </div>
              </div>
              <div class="detail-item">
                <label>创建时间：</label>
                <div class="detail-content">
                  <span>{{ apiClientForm.createTime }}</span>
                </div>
              </div>
              <div class="detail-item">
                <label>过期时间：</label>
                <div class="detail-content">
                  <el-tag :type="getExpireTagType(apiClientForm.expireTime)">
                    {{ getExpireTimeText(apiClientForm.expireTime) }}
                  </el-tag>
                </div>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <div class="detail-content">
                  <el-tag :type="apiClientForm.enabled ? 'success' : 'info'">
                    {{ apiClientForm.enabled ? '启用' : '禁用' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <div class="form-section">
            <div class="section-title">密钥信息</div>
            <div class="detail-list">
              <div class="detail-item">
                <label>API Key：</label>
                <div class="detail-content">
                  <div class="key-value">
                    <el-tag type="info" class="key-tag">{{ apiClientForm.apiKey }}</el-tag>
                    <el-button type="text" icon="el-icon-document-copy" v-clipboard:copy="apiClientForm.apiKey"
                      v-clipboard:success="onCopySuccess">
                      复制
                    </el-button>
                  </div>
                </div>
              </div>
              <div class="detail-item">
                <label>API Secret：</label>
                <div class="detail-content">
                  <div class="key-value">
                    <el-tag type="info" class="key-tag">
                      <template v-if="apiClientForm.showSecret">
                        {{ apiClientForm.apiSecret }}
                      </template>
                      <template v-else>
                        ••••••••••••••••••••••••••••••••••••••••
                      </template>
                    </el-tag>
                    <el-button type="text" icon="el-icon-document-copy" v-clipboard:copy="apiClientForm.apiSecret"
                      v-clipboard:success="onCopySuccess">
                      复制
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-section" v-if="apiClientForm.remark">
            <div class="section-title">其他信息</div>
            <div class="detail-list">
              <div class="detail-item">
                <label>备注说明：</label>
                <div class="detail-content">
                  <span>{{ apiClientForm.remark }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="api-client-right">
      <div style="display: flex; align-items: center; justify-content: space-between; ">
        <div style="font-weight: bolder; padding-left: 15px; border-left: 2px #409EFF solid;">权限配置</div>
        <div>
          <el-button type="primary" @click="updatePermission">保存</el-button>
        </div>
      </div>
      <div style="height: 83vh">
        <ApiPermission
          ref="apiPermission"
          :selected-role="selectForm"
          @selection-change="selectionChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import clipboard from '@/directive/clipboard/index.js'
import ApiPermission from '@system/views/sys/permission/components/ApiPermission.vue'
import { list, save, update, remove, secretReset } from '@system/api/sys/apiclient'

export default {
  name: 'ApiClient',
  directives: {
    clipboard
  },
  components: { ApiPermission },
  data() {
    return {
      loading: false,
      submitLoading: false,
      configSaving: false,
      apiClientList: [],
      total: 0,
      keyword: '',
      queryParams: {
        size: -1
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: 'add',
      apiClientForm: {},
      rules: {
        clientName: [
          { required: true, message: '请输入客户端名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      configDialogVisible: false,
      currentClient: null,
      apiSearchKeyword: '',
      selectedApis: [],
      searchTimer: null,
      expireTimeType: 'custom', // 过期时间类型：custom/7/30/90/never
      detailDialogVisible: false,
      selectForm: {},
      permissions:[]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    toggleClientStatus(row) {
      var {id, enabled} = row
      update({id, enabled}).then(res => {
        this.$message.success('状态修改成功')
      }).catch(e => {
        row.enabled = !enabled
      })
    },
    updatePermission() {
      var {id} = this.selectForm
      update({id, permissions: this.permissions}).then(() => { 
        this.selectForm.permissions = this.permissions
        this.$message.success('权限修改成功')
      })
    },
    selectionChange(permissions) {
      this.permissions = permissions
    },
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const {records} = await list(this.queryParams)
        this.apiClientList = records || []
        if (this.apiClientList.length) {
          if (this.selectForm.id) {
            var client = this.apiClientList.find(item => item.id === this.selectForm.id)
            if (client) {
              this.selectForm = client
              return 
            }
          }
          this.selectForm = this.apiClientList[0]
        }
      } finally {
        this.loading = false
      }
    },

    handleAdd() {
      this.dialogTitle = '新增API客户端'
      this.dialogType = 'add'
      this.expireTimeType = 'never'
      this.apiClientForm = {
        enabled: true
      }
      this.dialogVisible = true
    },

    handleEdit(row) {
      this.dialogTitle = '编辑API客户端'
      this.dialogType = 'edit'

      // 判断过期时间类型
      if (!row.expireTime) {
        this.expireTimeType = 'never'
      } else if (row.expireTime.startsWith('2999')) {
        this.expireTimeType = 'never'
      } else {
        this.expireTimeType = 'custom'
      }

      this.apiClientForm = {
        id: row.id,
        clientName: row.clientName,
        expireTime: row.expireTime,
        remark: row.remark
      }
      this.dialogVisible = true
    },

    async handleDelete(row) {
      await this.$confirm('确认要删除该API客户端吗？删除后将无法恢复。', '提示', {
        type: 'warning'
      })
      await remove(row.id)
      this.$message.success('删除成功')
      this.getList()
    },

    async handleResetSecret(row) {
      try {
        await this.$confirm('确认要重置该客户端的密钥吗？重置后原密钥将立即失效。', '提示', {
          type: 'warning'
        })
        const data = await secretReset(row.id)
        // 更新列表中的数据
        const index = this.apiClientList.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.$set(this.apiClientList[index], 'apiSecret', data.apiSecret)
          this.$set(this.apiClientList[index], 'showSecret', true) // 重置后默认显示密钥
          this.$message.success('密钥重置成功')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('重置失败')
        }
      }
    },

    handleConfigDialogClose(done) {
      this.$confirm('确认关闭配置窗口？未保存的修改将会丢失', '提示', {
        type: 'warning'
      }).then(() => {
        this.currentClient = null
        this.selectedApis = []
        done()
      }).catch(() => { })
    },

    async submitForm(valid = true) {
      if (valid) {
        await this.$refs.apiClientForm.validate()
      }

      this.submitLoading = true
      try {
        var copy = JSON.parse(JSON.stringify(this.apiClientForm))
        delete copy.permissions
        if (!this.apiClientForm.id) {
          await save(copy)
          this.$message.success('新增成功')
          this.getList()

          this.dialogVisible = false
        } else {
          await update(copy)
          this.$message.success('更新成功')
          this.dialogVisible = false
          this.getList()
        }
      } finally {
        this.submitLoading = false
      }
    },

    toggleSecretVisibility(row) {
      // 如果是显示密钥，先确保有密钥值
      if (!row.showSecret && !row.apiSecret) {
        // 如果没有密钥值，可能需要从后端获取
        this.$message.warning('密钥信息已被隐藏，请使用重置功能获取新的密钥')
        return
      }
      this.$set(row, 'showSecret', !row.showSecret)
    },

    onCopySuccess() {
      this.$message.success('复制成功')
    },

    handleSizeChange(val) {
      this.queryParams.size = val
      this.getList()
    },

    handleCurrentChange(val) {
      this.queryParams.current = val
      this.getList()
    },

    refreshList() {
      this.getList()
    },

    handleSearch() {
      this.queryParams.current = 1
      this.getList()
    },

    handleApiSearch(value) {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.apiSearchKeyword = value
      }, 300)
    },

    async handleStatusChange(row, val) {
      await this.$confirm(`确认要${val ? '启用' : '禁用'}该客户端吗？`, '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })

      await update(row)
      this.$message.success(`${val ? '启用' : '禁用'}成功`)
    },

    // 处理过期时间类型变更
    handleExpireTypeChange(type) {
      if (type === 'never') {
        // 设置为2999年12月31日
        this.apiClientForm.expireTime = '2999-12-31 23:59:59'
      } else if (type === 'custom') {
        this.apiClientForm.expireTime = ''
      } else {
        // 计算N天后的时间
        const days = parseInt(type)
        const date = new Date()
        date.setDate(date.getDate() + days)
        this.apiClientForm.expireTime = this.formatDateTime(date)
      }
    },

    // 获取过期时间显示文本
    getExpireTimeText(expireTime) {
      if (!expireTime) return '永不过期'

      // 如果是永不过期（2999年）
      if (expireTime.startsWith('2999')) {
        return '永不过期'
      }

      const expireDate = new Date(expireTime)
      const now = new Date()

      // 如果已过期
      if (expireDate < now) {
        return '已过期'
      }

      // 计算剩余天数
      const days = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24))
      if (days <= 7) {
        return `剩余 ${days} 天`
      }

      return expireTime
    },

    // 获取过期标签类型
    getExpireTagType(expireTime) {
      if (!expireTime) return 'success'

      // 如果是永不过期
      if (expireTime.startsWith('2999')) {
        return 'success'
      }

      const expireDate = new Date(expireTime)
      const now = new Date()

      // 如果已过期
      if (expireDate < now) {
        return 'danger'
      }

      // 如果剩余不到7天
      const days = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24))
      if (days <= 7) {
        return 'warning'
      }

      return 'primary'
    },

    // 格式化日期时间
    formatDateTime(date) {
      const pad = num => String(num).padStart(2, '0')
      return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      const actions = {
        detail: () => this.handleDetail(row),
        edit: () => this.handleEdit(row),
        reset: () => this.handleResetSecret(row),
        delete: () => this.handleDelete(row)
      }
      if (actions[command]) {
        actions[command]()
      }
    },

    // 查看详情
    handleDetail(row) {
      this.apiClientForm = { ...row, showSecret: false }
      this.detailDialogVisible = true
    },
  }
}
</script>

<style lang="scss" scoped>
.api-client-container{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 24px;
  height: 95vh;
}
.api-client-right { 
  width: 63vw;
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
}
.api-client-left {
  width: 20vw;
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    padding-bottom: 20px;
    flex-shrink: 0;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .subtitle {
        color: #606266;
        font-size: 14px;
        margin-left: 15px;
      }
    }
  }

  .table-wrapper {
    padding: 5px 0;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;

        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;

          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        td {
          padding: 8px 0;

          .cell {
            line-height: 1.5;
          }
        }
      }

      tr {
        transition: all 0.3s;

        &:hover {
          background: #f7f9fc !important;
        }

        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }

      .client-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #409EFF;
          font-size: 16px;
        }
      }

      .key-cell,
      .secret-cell {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-tag {
          flex: 1;
          font-family: 'Roboto Mono', monospace;
          padding: 8px 12px;
          border-radius: 6px;
          background: #f8faff;
          color: #1a1f36;
          border: 1px solid #e0e5ee;
          font-size: 13px;
          letter-spacing: 0.5px;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          line-height: 1.4;
          min-height: 32px;

          &:hover {
            background: #f0f5ff;
            border-color: #409EFF;
          }
        }

        .secret-mask {
          flex: 1;
          font-family: 'Roboto Mono', monospace;
          color: #606266;
          background: #f8faff;
          padding: 8px 12px;
          border-radius: 6px;
          border: 1px solid #e0e5ee;
          font-size: 13px;
          letter-spacing: 1px;
        }

        .el-button {
          padding: 6px 12px;
          font-size: 13px;
          border-radius: 4px;
          transition: all 0.3s ease;

          &:hover {
            color: #409EFF;
            background: #ecf5ff;
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;

      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;

        .btn-prev,
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;

          &:not(.disabled):hover {
            border-color: #409EFF;
          }

          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }

        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }

        .el-pagination__jump {
          margin-left: 16px;
        }

        .el-select .el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }

        .el-pagination__editor.el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }
}

::v-deep .el-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 5vh !important;
  max-height: 90vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    flex-shrink: 0;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .api-config-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .config-header {
        padding: 5px 24px;
        border-bottom: 1px solid #ebeef5;
        background: linear-gradient(to right, #fcfcfd, #f9fafc);
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 24px;
        flex-shrink: 0;
      }

      .api-permission-wrapper {
        flex: 1;
        overflow: auto;
        padding: 24px;
        background: #f8f9fb;
        min-height: 0;
        display: flex;
        flex-direction: column;

        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    flex-shrink: 0;
  }
}

.api-client-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    .key-secret-preview {
      margin-top: 20px;
      padding: 16px;
      background: rgba(64, 158, 255, 0.05);
      border-radius: 8px;
      border: 1px dashed #409EFF;

      .preview-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          width: 100px;
          color: #606266;
          font-weight: 500;
        }

        .preview-value {
          color: #909399;
          font-style: italic;
        }
      }
    }

    .expire-time-wrapper {
      .el-radio-group {
        margin-bottom: 15px;
        width: 100%;
        display: flex;

        .el-radio-button {
          flex: 1;

          ::v-deep .el-radio-button__inner {
            width: 100%;
          }
        }
      }

      .el-date-editor {
        width: 100%;
      }
    }

    .el-textarea__inner {
      min-height: 80px;
    }

    ::v-deep {
      .el-form-item {

        .el-input,
        .el-select,
        .el-cascader {
          width: 100%;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          background: #fff;
          border: 1px solid #e0e5ee;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }

          &:hover {
            border-color: #c0d0e9;
          }
        }
      }
    }
  }
}

.expire-time-wrapper {
  .el-radio-group {
    margin-bottom: 15px;
    width: 100%;
    display: flex;

    .el-radio-button {
      flex: 1;

      ::v-deep .el-radio-button__inner {
        width: 100%;
      }
    }
  }

  .el-date-editor {
    width: 100%;
  }
}

.el-tag {
  &.el-tag--warning {
    color: #e6a23c;
    background: #fdf6ec;
    border-color: #f5dab1;
  }

  &.el-tag--danger {
    color: #f56c6c;
    background: #fef0f0;
    border-color: #fbc4c4;
  }
}

.secret-cell {
  display: flex;
  align-items: center;
  gap: 12px;

  .secret-text,
  .secret-mask {
    flex: 1;
    font-family: 'Roboto Mono', monospace;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e0e5ee;
    font-size: 13px;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    background: #f8faff;
    min-height: 32px;
    display: flex;
    align-items: center;
  }

  .secret-text {
    color: #1a1f36;
    letter-spacing: 1px;

    &:hover {
      background: #f0f5ff;
      border-color: #409EFF;
    }
  }

  .secret-mask {
    color: #606266;
    letter-spacing: 2px;
  }

  .el-button {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      color: #409EFF;
      background: #ecf5ff;
    }

    i {
      margin-right: 4px;
    }
  }
}

.el-tag {
  padding: 0 12px;
  height: 24px;
  line-height: 22px;
  border-radius: 12px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

  &.el-tag--success {
    background-color: rgba(103, 194, 58, 0.1);
    border-color: rgba(103, 194, 58, 0.2);
    color: #67c23a;
  }

  &.el-tag--warning {
    background-color: rgba(230, 162, 60, 0.1);
    border-color: rgba(230, 162, 60, 0.2);
    color: #e6a23c;
  }

  &.el-tag--danger {
    background-color: rgba(245, 108, 108, 0.1);
    border-color: rgba(245, 108, 108, 0.2);
    color: #f56c6c;
  }
}

.client-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 12px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    border-color: #c6e2ff;
    
    .client-actions {
      opacity: 1;
    }
  }
  
  &.active {
    background-color: #ecf5ff;
    border-color: #c6e2ff;
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.1);
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 4px;
      background: linear-gradient(to bottom, #409EFF, #a0cfff);
      border-radius: 4px 0 0 4px;
    }
  }
  
  .client-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .client-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #409eff, #64b5f6);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      flex-shrink: 0;
      
      i {
        font-size: 20px;
        color: white;
      }
    }
    
    .client-basic {
      flex: 1;
      min-width: 0;
      
      .client-name {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .client-key {
        display: flex;
        align-items: center;
        
        .key-label {
          font-size: 12px;
          color: #909399;
          margin-right: 4px;
          flex-shrink: 0;
        }
        
        .key-value {
          font-size: 12px;
          color: #606266;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    
    .client-status {
      margin-left: 12px;
      flex-shrink: 0;
      cursor: pointer;
      
      .el-tag {
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
  
  .client-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .client-time {
      font-size: 12px;
      color: #909399;
    }
    
    .client-actions {
      opacity: 0;
      transition: opacity 0.3s;
      display: flex;
      align-items: center;
      
      .el-link {
        padding: 4px;
        font-size: 12px;
        margin-left: 4px;
      }
    }
  }
}

// 确保在卡片悬停时也显示操作按钮
.client-item:hover .client-actions {
  opacity: 1;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #909399;
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  
  i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #c0c4cc;
  }
  
  p {
    margin: 0 0 20px;
    font-size: 14px;
  }
}

.detail-container {
  padding: 10px 20px;

  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    .detail-list {
      .detail-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        min-height: 32px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          width: 100px;
          color: #606266;
          font-weight: 500;
          line-height: 32px;
          flex-shrink: 0;
        }

        .detail-content {
          flex: 1;
          min-height: 32px;
          display: flex;
          align-items: center;

          .key-value {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;

            .key-tag {
              flex: 1;
              padding: 8px 12px;
              font-family: 'Roboto Mono', monospace;
              background: #f8faff;
              border: 1px solid #e0e5ee;
              color: #1a1f36;
              height: 32px;
              line-height: 16px;
              display: flex;
              align-items: center;
              letter-spacing: 0.5px;
              font-size: 13px;

              &:hover {
                background: #f0f5ff;
                border-color: #409EFF;
              }
            }

            .el-button {
              padding: 6px 12px;
              height: 32px;
              font-size: 13px;

              i {
                margin-right: 4px;
                font-size: 14px;
              }

              &:hover {
                color: #409EFF;
                background: #ecf5ff;
              }
            }
          }

          .el-tag {
            padding: 0 12px;
            height: 24px;
            line-height: 22px;
            border-radius: 12px;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            &.el-tag--success {
              background-color: rgba(103, 194, 58, 0.1);
              border-color: rgba(103, 194, 58, 0.2);
              color: #67c23a;
            }

            &.el-tag--warning {
              background-color: rgba(230, 162, 60, 0.1);
              border-color: rgba(230, 162, 60, 0.2);
              color: #e6a23c;
            }

            &.el-tag--danger {
              background-color: rgba(245, 108, 108, 0.1);
              border-color: rgba(245, 108, 108, 0.2);
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
}

.action-button {
  font-size: 14px;
  color: #409EFF;
  padding: 4px 12px;
  border-radius: 6px;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }

  i {
    transition: transform 0.3s ease;
  }

  &:hover i {
    transform: rotate(180deg);
  }
}

.el-dropdown-menu {
  .el-dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;

    i {
      margin-right: 8px;
      font-size: 16px;
    }

    &.danger {
      color: #F56C6C;
    }
  }
}
</style>