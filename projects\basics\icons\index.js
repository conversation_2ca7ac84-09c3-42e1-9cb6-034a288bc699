import Vue from 'vue'
import SvgIcon from '@/components/SvgIcon'// svg component

// register globally
Vue.component('svg-icon', SvgIcon)

const req = require.context('./svg', false, /\.svg$/)
const req2 = require.context('../../../projects', true, /\.svg$/)

const dirs = process.env.PROJECTS_DIR
const requireAll = arr => arr.forEach(requireContext => {
    requireContext.keys().filter(f => {
        let dir = dirs.find(d => f.indexOf(d) > 0)
        if (dir) {
            return f.indexOf(`${dir}/icons/`) > 0
        } else {
            return true
        }
    }).map(requireContext)
})

requireAll([req, req2])
