import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
import permission from './modules/permission'
import user from './modules/user'
import tagsView from './modules/tagsView'
import dict from './modules/dict'
import loading from './modules/loading.js'
import version from './modules/version.js'

Vue.use(Vuex)

let requireContext = require.context('../../../projects', true, /\/store\/module\.store\.js$/)

const globals = requireContext.keys()
  .filter(f => plugin_filefilter(f))
  .map(requireContext).map(g => g.default)

const store = new Vuex.Store({
  modules: {
    ...Object.assign({}, ...globals),
    app,
    dict,
    settings,
    user,
    permission,
    tagsView,
    loading,
    version
  },
  getters
})

export default store
