<template>
  <el-row class="scheduled-main">
    <el-col :span="6" class="scheduled-col">
      <task-tree class="scheduled-item" :selected.sync="task" :types="types" :statuses="statuses" />
    </el-col>
    <el-col :span="18" class="scheduled-col">
      <task-detail v-model="task" :statuses="statuses" />
      <record-list class="scheduled-item" v-model="task.id" />
    </el-col>
  </el-row>
</template>

<script>
import taskTree from "./task/taskTree";
import taskDetail from "./task/taskDetail";
import recordList from "./task/recordList";
import api from "@system/api/scheduled";
export default {
  name: "scheduled",
  components: {taskTree, taskDetail, recordList},
  data() {
    return {
      groups: [],
      task: {},
      types: [],
      statuses: []
    }
  },
  mounted() {
    api.task.types().then(res => this.types = res)
    api.task.statuses().then(res => this.statuses = res)
  },
  methods: {
  }
}
</script>

<style scoped>
.scheduled-main {
  padding: 0 10px;
  height: 95vh;
}
.scheduled-col{
  height: 100%;
  display: flex;
  flex-direction: column;
}
.scheduled-item{
  flex: 1;
}
</style>