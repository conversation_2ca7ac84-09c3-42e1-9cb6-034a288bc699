<template>
  <el-form :model="formData" :rules="formRules" ref="connectorForm" class="connector-base-form">
    <div class="form-row">
      <el-form-item label="连接器名称" prop="name" required class="form-item-half">
        <el-input v-model="formData.name" placeholder="请输入连接器名称" maxlength="50" show-word-limit />
      </el-form-item>

      <el-form-item label="连接器编码" prop="code" required class="form-item-half">
        <el-input v-model="formData.code" placeholder="请输入连接器编码" maxlength="30" show-word-limit />
      </el-form-item>
    </div>

    <el-form-item label="连接器描述" prop="description">
      <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入连接器描述（可选）"
        maxlength="200" show-word-limit />
    </el-form-item>
    <!-- 连接信息 -->
    <div class="connection-section">
      <div class="form-row">
        <el-form-item label="协议" prop="protocol" required class="form-item-quarter">
          <el-select v-model="formData.protocol" placeholder="选择或输入协议" filterable allow-create
            @change="handleProtocolChange" style="width: 100%">
            <el-option v-for="protocol in getProtocolOptions()" :key="protocol.value" :label="protocol.label"
              :value="protocol.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="主机名" prop="host" required class="form-item-half">
          <el-input v-model="formData.host" placeholder="如: api.example.com" maxlength="100" />
        </el-form-item>

        <el-form-item label="端口号" prop="port" required class="form-item-quarter">
          <el-input-number v-model="formData.port" :min="1" :max="65535" placeholder="端口号"
            controls-position="right" style="width: 100%" />
        </el-form-item>
      </div>

      <el-form-item label="URL附加字符串" prop="urlAppend" v-show="!['ftp'].includes(formData.protocol)">
        <el-input v-model="formData.urlAppend" :placeholder="getUrlAppendPlaceholder()" maxlength="200" />
      </el-form-item>

      <!-- URL预览和测试连接 -->
      <div class="url-preview-section" v-if="urlPreview">
        <div class="url-preview">
          <div class="preview-header">
            <label>URL预览：</label>
            <div class="header-actions" v-if="showTestConnection">
              <el-button type="text" size="mini" :loading="testingConnection" @click="testConnection"
                class="test-btn-inline">
                <i class="el-icon-connection" v-if="!testingConnection"></i>
                {{ testingConnection ? '测试中...' : '测试连接' }}
              </el-button>
              <div v-if="testResult !== null" class="test-result-tag"
                :class="{ success: testResult, error: !testResult }">
                <i :class="testResult ? 'el-icon-success' : 'el-icon-error'"></i>
              </div>
            </div>
          </div>
          <div class="preview-url">{{ urlPreview }}</div>
        </div>
      </div>
    </div>

    <!-- 认证配置 -->
    <div class="auth-section">
      <el-form-item label="认证执行器" prop="authActuator">
        <el-select v-model="formData.authActuator" placeholder="请选择认证执行器" clearable style="width: 100%"
          :loading="loadingAuthActuators" @change="handleAuthActuatorChange">
          <el-option v-for="actuator in authActuators" :key="actuator.code" :label="actuator.name"
            :value="actuator.code">
            <div class="auth-option">
              <span class="auth-name">{{ actuator.name }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 用户名密码认证 -->
      <div v-if="formData.authActuator === 'USER_PASSWORD'" class="auth-config">
        <div class="form-row">
          <el-form-item label="用户名" prop="username" required class="form-item-half">
            <el-input v-model="formData.username" placeholder="请输入用户名" maxlength="50" />
          </el-form-item>

          <el-form-item label="密码" prop="password" required class="form-item-half">
            <el-input v-model="formData.password" type="password" placeholder="请输入密码" maxlength="100"
              show-password />
          </el-form-item>
        </div>
      </div>

      <!-- 链式认证提示 -->
      <div v-else-if="formData.authActuator === 'CHAIN_AUTH'" class="chain-auth-tip">
        <el-alert title="链式认证配置复杂" description="链式认证需要配置多个步骤，建议在连接器详情页面进行配置。" type="info" :closable="false"
          show-icon />
      </div>
    </div>
  </el-form>
</template>

<script>
import { getAuthActuatorsByType, testIntegrationConnectorConnection } from '@system/api/integration/integration-connetor'

export default {
  name: 'ConnectorBaseForm',
  props: {
    // 表单数据
    value: {
      type: Object,
      default: () => ({})
    },
    // 是否显示测试连接功能
    showTestConnection: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loadingAuthActuators: false,
      authActuators: [],
      testingConnection: false,
      testResult: null,
      formData: {
        name: '',
        code: '',
        type: '',
        description: '',
        protocol: '',
        host: '',
        port: null,
        urlAppend: '',
        authActuator: '',
        username: '',
        password: ''
      },
      formRules: {
        name: [
          { required: true, message: '请输入连接器名称', trigger: 'blur' },
          { min: 2, max: 50, message: '连接器名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入连接器编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '连接器编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
          { min: 2, max: 30, message: '连接器编码长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        protocol: [
          { required: true, message: '请输入协议', trigger: 'blur' }
        ],
        host: [
          { required: true, message: '请输入主机名', trigger: 'blur' },
          { min: 2, max: 100, message: '主机名长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' },
          { type: 'number', min: 1, max: 65535, message: '端口号必须在 1 到 65535 之间', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 1, max: 50, message: '用户名长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 1, max: 100, message: '密码长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // URL预览
    urlPreview() {
      if (!this.formData.protocol || !this.formData.host || !this.formData.port) {
        return ''
      }
      let url = `${this.formData.protocol}://${this.formData.host}:${this.formData.port}`
      if (this.formData.urlAppend) {
        const separator = this.formData.urlAppend.startsWith('/') ? '' :
          (this.formData.protocol.includes('jdbc') ? '?' : '/')
        url += separator + this.formData.urlAppend
      }
      return url
    }
  },
  watch: {
    // 监听外部数据变化
    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          // 暂时禁用emit，避免循环
          this._isInitialized = false
          // 完全替换formData，确保编辑模式下数据正确填充
          this.formData = {
            name: newVal.name || '',
            code: newVal.code || '',
            type: newVal.type || '',
            description: newVal.description || '',
            protocol: newVal.protocol || '',
            host: newVal.host || '',
            port: newVal.port || null,
            urlAppend: newVal.urlAppend || '',
            authActuator: newVal.authActuator || '',
            username: newVal.username || '',
            password: newVal.password || ''
          }
          // 重新启用emit
          this.$nextTick(() => {
            this._isInitialized = true
          })
        }
      },
      immediate: true,
      deep: true
    },
    // 监听内部数据变化，向外发送
    formData: {
      handler(newVal) {
        // 避免在初始化时触发
        if (this._isInitialized) {
          this.$emit('input', newVal)
          this.$emit('change', newVal)
        }
      },
      deep: true
    },
    // 监听连接器类型变化，加载认证执行器
    'formData.type'(newType, oldType) {
      console.log('连接器类型变化:', oldType, '->', newType)
      if (newType) {
        this.loadAuthActuators()
        // 设置默认协议和端口（仅在新建时）
        if (newType === 'HTTP' && !this.formData.protocol) {
          this.formData.protocol = 'http'
          this.formData.port = 80
        } else if (newType === 'JDBC' && !this.formData.protocol) {
          this.formData.protocol = 'jdbc:mysql'
          this.formData.port = 3306
        }
      } else {
        // 清空认证执行器列表
        this.authActuators = []
      }
    },
    // 监听协议变化，自动设置端口
    'formData.protocol'(newProtocol, old) {
      if (!old && newProtocol) {
        const protocolPortMap = {
          'http': 80,
          'https': 443,
          'ftp': 21,
          'ftps': 990,
          'jdbc:mysql': 3306,
          'jdbc:postgresql': 5432,
          'jdbc:oracle:thin': 1521,
          'jdbc:sqlserver': 1433,
          'jdbc:sqlite': null
        }
        if (protocolPortMap.hasOwnProperty(newProtocol)) {
          this.formData.port = protocolPortMap[newProtocol]
        }
      }
    }
  },
  methods: {
    // 验证表单
    validate() {
      return new Promise((resolve) => {
        this.$refs.connectorForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.connectorForm.resetFields()
      this.testResult = null
      this.authActuators = []
    },

    // 手动刷新认证执行器（供外部调用）
    refreshAuthActuators() {
      if (this.formData.type) {
        this.loadAuthActuators()
      }
    },

    // 获取协议选项
    getProtocolOptions() {
      const httpProtocols = [
        { label: 'HTTP', value: 'http' },
        { label: 'HTTPS', value: 'https' }
      ]

      const jdbcProtocols = [
        { label: 'MySQL', value: 'jdbc:mysql' },
        { label: 'PostgreSQL', value: 'jdbc:postgresql' },
        { label: 'Oracle', value: 'jdbc:oracle:thin' },
        { label: 'SQL Server', value: 'jdbc:sqlserver' },
        { label: 'SQLite', value: 'jdbc:sqlite' }
      ]

      const ftpProtocols = [
        { label: 'FTP', value: 'ftp' },
        { label: 'FTPS', value: 'ftps' }
      ]

      if (this.formData.type === 'HTTP') {
        return httpProtocols
      } else if (this.formData.type === 'JDBC') {
        return jdbcProtocols
      } else if (this.formData.type === 'FTP') {
        return ftpProtocols
      }

      return [...httpProtocols, ...jdbcProtocols, ...ftpProtocols]
    },

    // 获取URL附加字符串占位符
    getUrlAppendPlaceholder() {
      if (this.formData.protocol && this.formData.protocol.includes('jdbc')) {
        return '如: useSSL=false&serverTimezone=UTC'
      } else if (this.formData.protocol?.includes('http')) {
        return '如: /api/v1'
      }
      return ''
    },

    // 处理协议变化
    handleProtocolChange() {
      // 协议变化时清除测试结果
      this.testResult = null
      // 端口设置由watch处理
      this.$emit('protocol-change', this.formData.protocol)
    },

    // 处理认证执行器变化
    async handleAuthActuatorChange() {
      this.testResult = null
      // 清空用户名密码，让用户重新输入
      if (this.formData.authActuator !== 'USER_PASSWORD') {
        this.formData.username = ''
        this.formData.password = ''
      }
      this.$emit('auth-actuator-change', this.formData.authActuator)
    },

    // 测试连接
    async testConnection() {
      // 验证必填字段
      if (!this.formData.type) {
        this.$message.warning('请先选择连接器类型')
        return
      }

      if (!this.formData.protocol || !this.formData.host || !this.formData.port) {
        this.$message.warning('请完善连接信息（协议、主机名、端口）')
        return
      }

      // 如果选择了用户名密码认证，需要验证用户名密码
      if (this.formData.authActuator === 'USER_PASSWORD') {
        if (!this.formData.username || !this.formData.password) {
          this.$message.warning('请填写用户名和密码')
          return
        }
      }

      // 如果没有名称和编码，使用默认值进行测试
      const testName = this.formData.name || '测试连接器'
      const testCode = this.formData.code || 'test_connector'

      this.testingConnection = true
      this.testResult = null

      try {
        // 构建完整的测试连接数据，与新增/修改表单保持一致
        const testData = {
          // 基础信息
          code: testCode,
          name: testName,
          type: this.formData.type, // 重要：连接器类型
          description: this.formData.description || '',
          enabled: true,

          // 连接信息
          protocol: this.formData.protocol,
          host: this.formData.host,
          port: this.formData.port,
          urlAppend: this.formData.urlAppend || '',

          // 认证信息
          authActuator: this.formData.authActuator || null,
          username: this.formData.username || '',
          password: this.formData.password || '',

          // 配置信息
          config: {
            type: this.formData.type
          },

          // 其他字段
          tokenCacheSeconds: 86400,
          remark: this.formData.description || ''
        }

        // 根据认证类型添加特殊配置
        if (this.formData.authActuator === 'USER_PASSWORD') {
          // 用户名密码认证已经在上面设置了username和password
        } else if (this.formData.authActuator === 'CHAIN_AUTH') {
          testData.config.authExpression = '${login.data.accessToken}'
          testData.config.authLocation = 'HEADER'
          testData.config.authParamName = 'Authorization'
        }

        // 根据连接器类型添加特定配置
        if (this.formData.type === 'HTTP') {
          // HTTP连接器特定配置
          testData.config.baseUrl = this.urlPreview
        } else if (this.formData.type === 'JDBC') {
          // JDBC连接器特定配置
          testData.config.jdbcUrl = this.urlPreview
        }

        console.log('测试连接数据:', testData)

        const response = await testIntegrationConnectorConnection(testData)
        console.log('测试连接响应:', response)

        // 处理响应 - 支持多种响应格式
        let isSuccess = false
        let errorMessage = ''

        if (response === true) {
          // 直接返回true
          isSuccess = true
        } else if (response && typeof response === 'object') {
          if (response.success === true || response.code === 200 || response.status === 'success') {
            isSuccess = true
          } else {
            errorMessage = response.message || response.msg || response.error || '连接测试失败'
          }
        } else if (response === false) {
          errorMessage = '连接测试失败'
        } else {
          errorMessage = '未知的响应格式'
        }

        this.testResult = isSuccess
        if (isSuccess) {
          this.$message.success('连接测试成功')
        } else {
          this.$message.error(`连接测试失败: ${errorMessage}`)
        }
      } catch (error) {
        this.testResult = false
        console.error('连接测试错误:', error)
        this.$message.error(`连接测试失败: ${error.message || '网络错误'}`)
      } finally {
        this.testingConnection = false
      }
    },

    // 加载认证执行器
    async loadAuthActuators() {
      if (!this.formData.type) {
        console.log('loadAuthActuators: 连接器类型为空，跳过加载')
        return
      }

      console.log('开始加载认证执行器，连接器类型:', this.formData.type)
      this.loadingAuthActuators = true

      try {
        const response = await getAuthActuatorsByType(this.formData.type)
        console.log('API响应:', response)

        // 根据API响应格式解析数据
        if (response && response.data) {
          this.authActuators = response.data
        } else if (Array.isArray(response)) {
          // 如果直接返回数组
          this.authActuators = response
        } else {
          this.authActuators = []
        }

        if (this.authActuators.length) {
          this.$set(this.formData, 'authActuator', this.authActuators[0].code)
        }

        console.log('加载认证执行器成功，数量:', this.authActuators.length, '数据:', this.authActuators)
      } catch (error) {
        console.error('加载认证执行器失败:', error)
        this.authActuators = []
        // 显示错误提示
        this.$message.error(`加载认证执行器失败: ${error.message || '网络错误'}`)
      } finally {
        this.loadingAuthActuators = false
      }
    }
  },
  mounted() {
    this._isInitialized = true
    console.log('ConnectorBaseForm mounted, formData.type:', this.formData.type)
    if (this.formData.type) {
      this.loadAuthActuators()
    }
  }
}
</script>

<style lang="scss" scoped>
.connector-base-form {
  .form-row {
    display: flex;
    gap: 16px;

    .form-item-half {
      flex: 1;
    }

    .form-item-quarter {
      flex: 0 0 120px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0;

      .form-item-quarter {
        flex: 1;
      }
    }
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #262626;
    font-size: 14px;
  }

  .el-input,
  .el-select,
  .el-textarea {
    .el-input__inner,
    .el-textarea__inner {
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      transition: all 0.2s ease;
      font-size: 14px;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }
  }

  // 连接信息和认证配置样式
  .connection-section,
  .auth-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
  }

  .url-preview-section {
    margin-top: 12px;
  }

  .url-preview {
    padding: 8px 12px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      label {
        font-size: 13px;
        font-weight: 500;
        color: #595959;
        margin: 0;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .test-btn-inline {
          padding: 2px 8px;
          font-size: 12px;
          color: #1890ff;

          &:hover {
            background: rgba(24, 144, 255, 0.1);
          }

          i {
            margin-right: 4px;
          }
        }

        .test-result-tag {
          display: flex;
          align-items: center;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;

          &.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
          }

          &.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
          }

          i {
            font-size: 12px;
          }
        }
      }
    }

    .preview-url {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: #096dd9;
      background: transparent;
      padding: 0;
      border: none;
      word-break: break-all;
      line-height: 1.4;
    }
  }

  .auth-config {
    margin-top: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
  }

  .chain-auth-tip {
    margin-top: 16px;

    ::v-deep .el-alert {
      border-radius: 8px;

      .el-alert__title {
        font-size: 14px;
        font-weight: 500;
      }

      .el-alert__description {
        font-size: 13px;
        margin-top: 4px;
      }
    }
  }

  .auth-option {
    display: flex;
    align-items: center;

    .auth-name {
      font-size: 14px;
      color: #262626;
    }
  }
}
</style>
