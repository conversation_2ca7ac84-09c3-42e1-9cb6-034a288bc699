<template>
    <div>
        <h3>表单信息</h3>
        <form-create v-model="fApi" :rule="objRule" :option="formOption"></form-create>
    </div>
</template>

<script>
    export default {
        props: {
            objRule: Object
        },
        data() {
            return {
                fApi: {},
                formOption: {
                    // 显示重置表单按扭
                    resetBtn: false,
                    submitBtn: false,

                    // 表单提交按扭事件
                    onSubmit: formData => {
                        this.getFormModeValue(formData)
                    }
                },
            }
        }
    }
</script>

<style lang="sass" scoped>

</style>