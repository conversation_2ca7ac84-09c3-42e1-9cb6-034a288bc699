export default {
  api:'sys/sensitive',
  search: {
    isShow: true,
    showReset: true
  },
  table: {},
  formRule:[
    {
      type: "input",
      field: "word",
      className: "word-dom",
      title: "敏感词",
      isSearch:true,
      isSearchCol:{md:{span:10}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 18}},
      props:{
        placeholder:"请输入敏感词",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"敏感词不能为空"
        }
      ]
    },
    {
      type: "select",
      field: "type",
      className: "type-dom",
      title: "类型",
      isSearch: true,
      isSearchValidate: [],
      isSearchCol: {md: {span: 10}},
      options: [
        {value: 1, label: "类型1", disabled: false},
        {value: 2, label: "类型2", disabled: false}
      ],
      isTable: true,
      col: {md: {span: 18}},
      props: {
        multiple: false,
        placeholder: "请选择类型",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change',
          required: true,
          message: "请选择类型"
        }
      ],
    },
    {
      type: "input",
      field: "remark",
      className: "remark-dom",
      title: "备注",
      isSearch:false,
      isTable:false,
      col:{md:{span: 18}},
      props:{
        type: "textarea",
        placeholder:"请输入备注",
        readonly: false,
        clearable: true,
        disabled: false
      },
    },

  ]
}
