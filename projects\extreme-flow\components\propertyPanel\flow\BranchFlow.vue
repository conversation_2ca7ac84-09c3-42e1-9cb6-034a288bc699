<template>
  <el-form :model="form" size="small" ref="form" label-position="top">
    <el-form-item label="超时时间" prop="timeout">
      <el-input v-model.number="timeout">
        <template slot="append">秒</template>
      </el-input>
    </el-form-item>
    <el-form-item label="是否顺序执行" prop="applySequence">
      <el-switch v-model="form.applySequence"></el-switch>
    </el-form-item>
    <el-form-item label="是否忽略发送失败" prop="ignoreSendFailures">
      <el-switch v-model="form.ignoreSendFailures"></el-switch>
    </el-form-item>
  </el-form>
</template>

<script>
import { isEqual } from 'element-ui/src/utils/util';
export default {
  name: 'BranchFlow',
  data() {
    return {
      form: {
        applySequence: true,
        ignoreSendFailures: true,
        timeout: -1
      },
    }
  },
  computed: {
    timeout: {
      get() {
        let timeout = this.form.timeout
        return timeout >= 0 ? timeout / 1000 : -1
      },
      set(value) {
        this.$set(this.form, 'timeout', value * 1000)
      }
    }
  },
  methods: {
    getProperties() {
      return this.form
    },
    async validate() {
      return Promise.resolve(true)
    }
  },
  props: {
    properties: {
      type: Object,
      default: () => ({})
    },
    node: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    properties: {
      handler(newVal) {
        if (!isEqual(newVal, this.form)) {
          this.form = { ...this.form, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
  },
}
</script>