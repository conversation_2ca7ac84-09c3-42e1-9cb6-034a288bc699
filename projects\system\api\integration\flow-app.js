import request from '@/utils/request'

const flowAppApi = CONSTANT.INTEGRATION + '/integration/flow/app'

// 获取集成板列表
export function getFlowCanvasList(params) {
  return request({
    url: `${flowAppApi}/canvas/list`,
    method: 'get',
    params
  })
}

// 获取单个集成板详情
export function getFlowCanvasDetail(id) {
  return request({
    url: `${flowAppApi}/canvas`,
    method: 'get',
    params: { id }
  })
}

// 创建集成板
export function createFlowCanvas(data) {
  return request({
    url: `${flowAppApi}/canvas`,
    method: 'post',
    data
  })
}

// 更新集成板
export function updateFlowCanvas(data) {
    return request({
        url: `${flowAppApi}/canvas`,
        method: 'put',
        data: { ...data, enabled: data.published }
    })
}

// 删除集成板
export function deleteFlowCanvas(id) {
  return request({
    url: `${flowAppApi}/canvas/${id}`,
    method: 'delete'
  })
}



//测试执行
export function testExec(data) {
    return request({
        url: `${flowAppApi}/test/exec`,
        method: 'post',
        data
    })
}

//执行
export function flowExec(integrationFlowConfigId) {
    return request({
        url: `${flowAppApi}/exec/${integrationFlowConfigId}`,
        method: 'post',
    })
}

// 注册集成板到注册中心
export function registerFlowCanvas(id) {
  return request({
    url: CONSTANT.INTEGRATION + `/integration/registry/flows/app/canvas/register/${id}`,
    method: 'post'
  })
}

// 从注册中心卸载集成板
export function unregisterFlowCanvas(id) {
  return request({
    url: CONSTANT.INTEGRATION + `/integration/registry/flows/${id}`,
    method: 'delete'
  })
}