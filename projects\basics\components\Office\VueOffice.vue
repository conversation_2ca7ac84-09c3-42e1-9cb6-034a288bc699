<template>
    <vue-office-excel v-if="['slsx', 'xlsx'].includes(suffix)" :src="src" @rendered="rendered" />
    <vue-office-docx v-else-if="['doc', 'docx'].includes(suffix)" :src="src" @rendered="rendered" />
    <vue-office-pdf v-else-if="['pdf'].includes(suffix)" :src="src" @rendered="rendered" />
</template>
<script>
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'
import VueOfficeDocx from '@vue-office/docx'
import '@vue-office/docx/lib/index.css'
import VueOfficePdf from '@vue-office/pdf'

export default {
    components: {
        VueOfficeExcel,
        VueOfficeDocx,
        VueOfficePdf
    },
    props: ['src', 'fileType'],

    // data() {
    //     return {
    //         //src: 'https://mp-c67b3557-aed5-4742-bba8-538822214484.cdn.bspapp.com/page/wKgB8WU_V4WATHGLAABPeTGoaJA00.xlsx'
    //         //src: 'http://static.shanhuxueyuan.com/test6.docx'
    //     }
    // },
    computed: {
        suffix() {
            return this.fileType || this.src.split('.').pop()
        }
    },
    methods: {
        rendered() {
            // console.log("渲染完成")
            this.$emit('rendered')
        }
    }
}
</script>