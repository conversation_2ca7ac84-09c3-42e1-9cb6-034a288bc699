<template>
  <div class="http-connector-api-management">
    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <div class="search-left">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索接口名称、编码或描述"
          size="small"
          clearable
          @keyup.enter.native="handleSearch"
          class="search-input"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-select
          v-model="searchForm.type"
          placeholder="接口类型"
          size="small"
          clearable
          @change="handleSearch"
          class="type-select"
        >
          <el-option label="认证接口" value="AUTH" />
          <el-option label="业务接口" value="API" />
        </el-select>
        <el-select
          v-model="searchForm.enabled"
          placeholder="状态"
          size="small"
          clearable
          @change="handleSearch"
          class="status-select"
        >
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </div>
      <div class="search-right">
        <el-button
          v-if="selectedApis.length > 0"
          size="small"
          icon="el-icon-delete"
          @click="handleBatchDelete"
          class="mac-style-btn batch-delete-btn"
        >
          批量删除 ({{ selectedApis.length }})
        </el-button>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleCreate"
          class="mac-style-btn"
        >
          新建接口
        </el-button>
      </div>
    </div>

    <!-- 接口列表 -->
    <div class="api-list">
      <el-table
        v-loading="loading"
        :data="apiList"
        stripe
        size="small"
        border
        fit
        style="width: 100%"
        class="mac-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" :width=rpx(50) align="center" />
      <el-table-column prop="code" label="接口编码" :width=rpx(140)>
          <template slot-scope="scope">
            <code class="api-code">{{ scope.row.code }}</code>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="接口名称" min-width="180">
          <template slot-scope="scope">
            <div class="api-name-cell">
              <span class="api-name">{{ scope.row.name }}</span>
              <el-tag
                :type="scope.row.type === 'AUTH' ? 'warning' : 'primary'"
                size="mini"
                class="api-type-tag"
              >
                {{ scope.row.type === 'AUTH' ? '认证' : '业务' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="method" label="请求方法" width="100" align="center">
          <template slot-scope="scope">
            <el-tag
              :type="getMethodTagType(scope.row.method)"
              size="mini"
              class="method-tag"
            >
              {{ scope.row.method }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="url" label="请求路径" min-width="200">
          <template slot-scope="scope">
            <code class="api-url">{{ scope.row.url }}</code>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column prop="enabled" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabled"
              @change="handleToggleEnabled(scope.row)"
              size="mini"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" :width=rpx(250) fixed="right" align="center">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-view"
                @click="handleView(scope.row)"
                class="action-btn"
              >
                查看
              </el-button>
              <el-button
                type="text"
                size="mini"
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
                class="action-btn"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="mini"
                icon="el-icon-video-play"
                @click="handleTest(scope.row)"
                class="action-btn test-btn"
              >
                测试
              </el-button>
              <el-button
                type="text"
                size="mini"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                class="action-btn delete-btn"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          class="mac-pagination"
        />
      </div>
    </div>



    <!-- 接口详情/编辑对话框 -->
    <http-api-dialog
      :visible.sync="dialogVisible"
      :mode="dialogMode"
      :api-data="currentApi"
      :connector-id="connectorId"
      @success="handleDialogSuccess"
    />

    <!-- 接口测试结果对话框 -->
    <el-dialog
      title="🚀 接口测试结果"
      :visible.sync="testResultDialogVisible"
      width="75%"
      top="5vh"
      :append-to-body="true"
      :close-on-click-modal="false"
      custom-class="modern-test-result-dialog"
    >
      <div v-loading="testLoading" class="modern-test-result-content">
        <div v-if="testResult" class="result-container">
          <!-- 请求信息卡片 -->
          <div class="info-card request-info-card">
            <div class="card-header">
              <div class="header-left">
                <i class="el-icon-info card-icon"></i>
                <h4 class="card-title">请求信息</h4>
              </div>
              <div class="header-right">
                <el-tag
                  :type="testResult.success ? 'success' : 'danger'"
                  size="small"
                  class="status-tag"
                >
                  <i :class="testResult.success ? 'el-icon-check' : 'el-icon-close'"></i>
                  {{ testResult.success ? '测试成功' : '测试失败' }}
                </el-tag>
              </div>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-document"></i>
                    接口名称
                  </div>
                  <div class="info-value">{{ testResult.apiName }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-connection"></i>
                    请求方法
                  </div>
                  <div class="info-value">
                    <el-tag :type="getMethodTagType(testResult.method)" size="mini" class="method-tag">
                      {{ testResult.method }}
                    </el-tag>
                  </div>
                </div>
                <div class="info-item full-width">
                  <div class="info-label">
                    <i class="el-icon-link"></i>
                    请求URL
                  </div>
                  <div class="info-value">
                    <code class="url-code">{{ testResult.url }}</code>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-time"></i>
                    响应时间
                  </div>
                  <div class="info-value">
                    <span class="response-time">{{ testResult.responseTime }}ms</span>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="el-icon-data-analysis"></i>
                    文件大小
                  </div>
                  <div class="info-value">
                    <span class="file-size">{{ formatFileSize(testResult.fileSize || testResult.blob?.size || 0) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 响应内容卡片 -->
          <div class="info-card response-content-card">
            <div class="card-header">
              <div class="header-left">
                <i class="el-icon-view card-icon"></i>
                <h4 class="card-title">响应内容</h4>
              </div>
              <div class="header-right">
                <el-tag size="mini" class="content-type-tag">
                  {{ getContentTypeLabel(testResult.contentType) }}
                </el-tag>
              </div>
            </div>
            <div class="card-content">
              <!-- JSON响应 -->
              <div v-if="testResult.contentType === 'json'" class="json-response">
                <div class="response-header">
                  <i class="el-icon-document-copy"></i>
                  <span>JSON 数据</span>
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-copy-document"
                    @click="copyToClipboard(testResult.data)"
                    class="copy-btn"
                  >
                    复制
                  </el-button>
                </div>
                <div class="json-container">
                  <pre class="json-code">{{ formatJson(testResult.data) }}</pre>
                </div>
              </div>

              <!-- 图片响应 -->
              <div v-else-if="testResult.contentType === 'image'" class="image-response">
                <div class="response-header">
                  <i class="el-icon-picture"></i>
                  <span>图片预览</span>
                </div>
                <div class="image-container">
                  <img :src="testResult.imageUrl" alt="响应图片" class="response-image" />
                </div>
                <div class="image-actions">
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-download"
                    @click="downloadFile(testResult.blob, 'response.png')"
                    class="download-btn"
                  >
                    下载图片
                  </el-button>
                </div>
              </div>

              <!-- 文件响应 -->
              <div v-else-if="testResult.contentType === 'file'" class="file-response">
                <div class="response-header">
                  <i class="el-icon-folder"></i>
                  <span>文件下载</span>
                </div>
                <div class="file-info-card">
                  <div class="file-icon">
                    <i class="el-icon-document-copy"></i>
                  </div>
                  <div class="file-details">
                    <div class="file-name">{{ testResult.fileName }}</div>
                    <div class="file-meta">
                      <span class="file-type">{{ testResult.fileType }}</span>
                      <span class="file-size">{{ formatFileSize(testResult.fileSize) }}</span>
                    </div>
                  </div>
                  <div class="file-actions">
                    <el-button
                      type="primary"
                      size="small"
                      icon="el-icon-download"
                      @click="downloadFile(testResult.blob, testResult.fileName)"
                      class="download-btn"
                    >
                      下载文件
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 文本响应 -->
              <div v-else class="text-response">
                <div class="response-header">
                  <i class="el-icon-document"></i>
                  <span>文本内容</span>
                  <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-copy-document"
                    @click="copyToClipboard(testResult.data)"
                    class="copy-btn"
                  >
                    复制
                  </el-button>
                </div>
                <div class="text-container">
                  <pre class="text-code">{{ testResult.data }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 错误信息卡片 -->
        <div v-if="testError" class="info-card error-card">
          <div class="card-header">
            <div class="header-left">
              <i class="el-icon-warning card-icon error-icon"></i>
              <h4 class="card-title error-title">测试失败</h4>
            </div>
          </div>
          <div class="card-content">
            <div class="error-content">
              <div class="error-message">
                <i class="el-icon-close-notification"></i>
                <span>{{ testError }}</span>
              </div>
              <div class="error-suggestions">
                <h5>可能的解决方案：</h5>
                <ul>
                  <li>检查网络连接是否正常</li>
                  <li>确认接口URL和参数配置正确</li>
                  <li>验证服务器是否正常运行</li>
                  <li>检查请求头和认证信息</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="modern-dialog-footer">
        <div class="footer-left">
          <span v-if="testResult" class="test-info">
            <i class="el-icon-time"></i>
            测试完成于 {{ new Date().toLocaleTimeString() }}
          </span>
        </div>
        <div class="footer-right">
          <el-button
            @click="testResultDialogVisible = false"
            class="close-btn"
            size="medium"
          >
            <i class="el-icon-close"></i>
            关闭
          </el-button>
          <el-button
            v-if="testResult"
            type="primary"
            @click="retestApi"
            class="retest-btn"
            size="medium"
            :loading="testLoading"
          >
            <i class="el-icon-refresh"></i>
            重新测试
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getHttpRequestList,
  deleteHttpRequest,
  batchDeleteHttpRequest,
  updateHttpRequest,
  executeHttpRequestTest
} from '@system/api/integration/http-connector'
import HttpApiDialog from './HttpApiDialog'

export default {
  name: 'HttpConnectorApiManagement',
  components: {
    HttpApiDialog
  },
  props: {
    connectorId: {
      type: String,
      required: true
    },
    connectorDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      apiList: [],
      selectedApis: [],
      searchForm: {
        keyword: '',
        type: '',
        enabled: null
      },
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      dialogVisible: false,
      dialogMode: 'create', // create, edit, view
      currentApi: null,
      // 测试相关
      testResultDialogVisible: false,
      testLoading: false,
      testResult: null,
      testError: null,
      currentTestApi: null
    }
  },
  created() {
    this.loadApiList()
  },
  methods: {
    // 加载接口列表
    async loadApiList() {
      this.loading = true
      try {
        const params = {
          connectorId: this.connectorId,
          current: this.pagination.current,
          size: this.pagination.size,
          ...this.searchForm
        }
        
        const response = await getHttpRequestList(params)
        this.apiList = response.records || []
        this.pagination.total = response.total || 0
      } catch (error) {
        console.error('加载接口列表失败:', error)
        this.$message.error('加载接口列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadApiList()
    },

    // 刷新
    handleRefresh() {
      this.searchForm = {
        keyword: '',
        type: '',
        enabled: null
      }
      this.pagination.current = 1
      this.loadApiList()
    },

    // 创建接口
    handleCreate() {
      this.dialogMode = 'create'
      this.currentApi = null
      this.dialogVisible = true
    },

    // 查看接口
    handleView(row) {
      this.dialogMode = 'view'
      this.currentApi = { ...row }
      this.dialogVisible = true
    },

    // 编辑接口
    handleEdit(row) {
      this.dialogMode = 'edit'
      this.currentApi = { ...row }
      this.dialogVisible = true
    },

    // 测试接口
    async handleTest(row) {
      this.currentTestApi = row
      this.testResult = null
      this.testError = null
      this.testResultDialogVisible = true
      await this.executeTest()
    },

    // 执行测试
    async executeTest() {
      if (!this.currentTestApi) return

      this.testLoading = true
      const startTime = Date.now()

      try {
        const response = await executeHttpRequestTest(this.currentTestApi)
        const endTime = Date.now()
        const responseTime = endTime - startTime

        // 处理响应
        const contentType = response.headers['content-type'] || response.headers['Content-Type'] || ''
        const blob = response.data

        let result = {
          apiName: this.currentTestApi.name,
          method: this.currentTestApi.method,
          url: this.currentTestApi.url,
          responseTime,
          success: true,
          blob
        }

        // 判断响应类型
        if (contentType.includes('application/json')) {
          // JSON响应
          const text = await blob.text()
          result.contentType = 'json'
          result.data = text
        } else if (contentType.includes('image/')) {
          // 图片响应
          result.contentType = 'image'
          result.imageUrl = URL.createObjectURL(blob)
          result.fileSize = blob.size
        } else if (contentType.includes('text/')) {
          // 文本响应
          const text = await blob.text()
          result.contentType = 'text'
          result.data = text
        } else {
          // 其他文件响应
          result.contentType = 'file'
          result.fileType = contentType
          result.fileSize = blob.size
          result.fileName = this.getFileNameFromContentType(contentType) || 'response_file'
        }

        this.testResult = result
        this.testError = null

      } catch (error) {
        console.error('接口测试失败:', error)
        this.testError = error.message || '接口测试失败，请检查网络连接和接口配置'
        this.testResult = null
      } finally {
        this.testLoading = false
      }
    },

    // 重新测试
    async retestApi() {
      await this.executeTest()
    },

    // 删除接口
    async handleDelete(row) {
      try {
        await this.$confirm(`确定要删除接口"${row.name}"吗？`, '确认删除', {
          type: 'warning'
        })
        
        await deleteHttpRequest(row.id)
        this.$message.success('删除成功')
        this.loadApiList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除接口失败:', error)
          this.$message.error('删除接口失败')
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedApis.length} 个接口吗？`, '确认批量删除', {
          type: 'warning'
        })
        
        const ids = this.selectedApis.map(api => api.id)
        await batchDeleteHttpRequest(ids)
        this.$message.success('批量删除成功')
        this.selectedApis = []
        this.loadApiList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error('批量删除失败')
        }
      }
    },

    // 切换启用状态
    async handleToggleEnabled(row) {
      try {
        await updateHttpRequest({
          ...row,
          enabled: row.enabled
        })
        this.$message.success(`${row.enabled ? '启用' : '禁用'}成功`)
      } catch (error) {
        // 回滚状态
        row.enabled = !row.enabled
        console.error('更新状态失败:', error)
        this.$message.error('更新状态失败')
      }
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedApis = selection
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadApiList()
    },

    // 当前页变化
    handleCurrentChange(current) {
      this.pagination.current = current
      this.loadApiList()
    },

    // 对话框成功回调
    handleDialogSuccess() {
      this.loadApiList()
    },

    // 获取请求方法标签类型
    getMethodTagType(method) {
      const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      }
      return typeMap[method] || 'info'
    },

    // 格式化JSON
    formatJson(jsonString) {
      try {
        const obj = JSON.parse(jsonString)
        return JSON.stringify(obj, null, 2)
      } catch (error) {
        return jsonString
      }
    },

    // 下载文件
    downloadFile(blob, fileName) {
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 根据Content-Type获取文件名
    getFileNameFromContentType(contentType) {
      const typeMap = {
        'application/pdf': 'response.pdf',
        'application/zip': 'response.zip',
        'application/xml': 'response.xml',
        'text/csv': 'response.csv',
        'text/html': 'response.html',
        'application/vnd.ms-excel': 'response.xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'response.xlsx',
        'application/msword': 'response.doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'response.docx'
      }
      return typeMap[contentType] || 'response_file'
    },

    // 获取内容类型标签
    getContentTypeLabel(contentType) {
      const labelMap = {
        'json': 'JSON',
        'image': '图片',
        'file': '文件',
        'text': '文本'
      }
      return labelMap[contentType] || '未知'
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        try {
          document.execCommand('copy')
          this.$message.success('已复制到剪贴板')
        } catch (err) {
          this.$message.error('复制失败')
        }
        document.body.removeChild(textArea)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.http-connector-api-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.1);

    .search-left {
      display: flex;
      gap: 12px;
      align-items: center;

      .search-input {
        width: 280px;
      }

      .type-select,
      .status-select {
        width: 120px;
      }
    }

    .search-right {
      display: flex;
      gap: 8px;
    }
  }

  .api-list {
    flex: 1;
    display: flex;
    flex-direction: column;

    .mac-table {
      flex: 1;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .api-name-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .api-name {
          font-weight: 500;
          color: #1e293b;
        }

        .api-type-tag {
          font-size: 10px;
          padding: 2px 6px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          line-height: 1;
        }
      }

      .api-code {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #0ea5e9;
        border-radius: 4px;
        padding: 2px 6px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 11px;
        color: #0c4a6e;
      }

      .method-tag {
        font-weight: 600;
        font-size: 10px;
        padding: 2px 6px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        line-height: 1;
      }

      .api-url {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #d1d5db;
        border-radius: 4px;
        padding: 2px 6px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 11px;
        color: #374151;
        word-break: break-all;
      }

      .action-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4px;
        white-space: nowrap;
      }

      .action-btn {
        font-size: 12px;
        padding: 4px 6px;
        margin: 0;

        &.test-btn {
          color: #f59e0b;

          &:hover {
            color: #d97706;
          }
        }

        &.delete-btn {
          color: #ef4444;

          &:hover {
            color: #dc2626;
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: 16px;
      display: flex;
      justify-content: center;

      .mac-pagination {
        ::v-deep .el-pagination__total,
        ::v-deep .el-pagination__jump {
          color: #64748b;
          font-size: 13px;
        }

        ::v-deep .el-pager li {
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 6px;
          margin: 0 2px;
          transition: all 0.2s ease;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
          }

          &.active {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
          }
        }

        ::v-deep .btn-prev,
        ::v-deep .btn-next {
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 6px;
          transition: all 0.2s ease;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
          }
        }
      }
    }
  }



  // MAC风格按钮
  .mac-style-btn {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

    &:hover {
      background: linear-gradient(135deg, #40a9ff, #69c0ff);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    &.batch-delete-btn {
      background: linear-gradient(135deg, #ff4d4f, #ff7875);
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);

      &:hover {
        background: linear-gradient(135deg, #ff7875, #ffa39e);
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
      }
    }
  }
}

// 全局表格样式
::v-deep .mac-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-bottom: 1px solid #e2e8f0;
        color: #374151;
        font-weight: 600;
        font-size: 13px;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        &:hover {
          background: rgba(59, 130, 246, 0.02);
        }

        td {
          border-bottom: 1px solid #f1f5f9;
          font-size: 13px;
          color: #374151;
        }
      }
    }
  }

  .el-table__empty-block {
    background: white;
  }
}

// 输入框样式
::v-deep .el-input {
  .el-input__inner {
    border-radius: 8px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    transition: all 0.2s ease;
    font-size: 13px;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

// 选择器样式
::v-deep .el-select {
  .el-input__inner {
    border-radius: 8px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    font-size: 13px;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

// 开关样式
::v-deep .el-switch {
  .el-switch__core {
    border-radius: 12px;
    transition: all 0.2s ease;
  }

  &.is-checked {
    .el-switch__core {
      background-color: #3b82f6;
    }
  }
}

// 现代化测试结果对话框样式
::v-deep .modern-test-result-dialog {
  border-radius: 20px !important;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
  animation: dialogSlideIn 0.3s ease-out;

  .el-dialog {
    border-radius: 20px !important;
    margin: 0 !important;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 24px 32px 20px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .el-dialog__title {
      font-size: 20px;
      font-weight: 700;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .el-dialog__headerbtn {
      top: 20px !important;
      right: 24px !important;
      width: 32px !important;
      height: 32px !important;
      background: rgba(255, 255, 255, 0.2) !important;
      border-radius: 50% !important;
      transition: all 0.3s ease !important;

      &:hover {
        background: rgba(255, 255, 255, 0.3) !important;
        transform: scale(1.1) !important;
      }

      .el-dialog__close {
        color: white !important;
        font-size: 16px !important;
        font-weight: bold !important;
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }

  .el-dialog__footer {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-top: 2px solid #f1f5f9;
    padding: 20px 32px 24px;
  }

  .modern-test-result-content {
    min-height: 400px;
    max-height: 70vh;
    overflow-y: auto;
    padding: 24px;

    .result-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .info-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(148, 163, 184, 0.1);
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }

      .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 16px 24px;
        border-bottom: 2px solid #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 24px;
          width: 60px;
          height: 2px;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          border-radius: 1px;
        }

        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;

          .card-icon {
            font-size: 18px;
            color: #3b82f6;
            padding: 8px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 8px;
          }

          .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
          }
        }

        .header-right {
          .status-tag {
            font-weight: 600;
            padding: 6px 12px;
            border-radius: 20px;
            border: none;

            i {
              margin-right: 4px;
            }
          }

          .content-type-tag {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            font-weight: 600;
            padding: 4px 12px;
            border-radius: 12px;
          }
        }
      }

      .card-content {
        padding: 24px;
      }

      &.error-card {
        border-color: rgba(239, 68, 68, 0.2);

        .card-header {
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);

          &::after {
            background: linear-gradient(135deg, #ef4444, #dc2626);
          }

          .error-icon {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
          }

          .error-title {
            color: #dc2626;
          }
        }
      }
    }

    // 信息网格布局
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;

      .info-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &.full-width {
          grid-column: 1 / -1;
        }

        .info-label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 13px;
          color: #64748b;
          font-weight: 500;

          i {
            font-size: 14px;
            color: #3b82f6;
          }
        }

        .info-value {
          font-size: 14px;
          color: #1e293b;
          font-weight: 500;

          .method-tag {
            font-weight: 600;
          }

          .url-code {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border: 1px solid #cbd5e1;
            border-radius: 8px;
            padding: 8px 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: #475569;
            word-break: break-all;
            display: block;
            margin-top: 4px;
          }

          .response-time {
            color: #059669;
            font-weight: 600;
            background: rgba(5, 150, 105, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }

          .file-size {
            color: #7c3aed;
            font-weight: 600;
            background: rgba(124, 58, 237, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }
        }
      }
    }

    // 响应内容样式
    .response-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f1f5f9;

      i {
        font-size: 16px;
        color: #3b82f6;
        margin-right: 8px;
      }

      span {
        font-size: 14px;
        font-weight: 600;
        color: #1e293b;
      }

      .copy-btn {
        color: #3b82f6;
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(59, 130, 246, 0.1);
        }
      }
    }

    .json-response {
      .json-container {
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
      }

      .json-code {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: #e2e8f0;
        padding: 20px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.6;
        overflow-x: auto;
        max-height: 400px;
        overflow-y: auto;
        margin: 0;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }

    .image-response {
      .image-container {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        border: 2px dashed #cbd5e1;
        margin-bottom: 16px;

        .response-image {
          max-width: 100%;
          max-height: 400px;
          border-radius: 12px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.02);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
          }
        }
      }

      .image-actions {
        text-align: center;
      }
    }

    .file-response {
      .file-info-card {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        margin-bottom: 16px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        .file-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 24px;
            color: white;
          }
        }

        .file-details {
          flex: 1;

          .file-name {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
          }

          .file-meta {
            display: flex;
            gap: 12px;

            .file-type {
              font-size: 12px;
              color: #64748b;
              background: rgba(100, 116, 139, 0.1);
              padding: 2px 8px;
              border-radius: 12px;
            }

            .file-size {
              font-size: 12px;
              color: #7c3aed;
              background: rgba(124, 58, 237, 0.1);
              padding: 2px 8px;
              border-radius: 12px;
            }
          }
        }

        .file-actions {
          flex-shrink: 0;
        }
      }
    }

    .text-response {
      .text-container {
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
      }

      .text-code {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 20px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.6;
        color: #475569;
        overflow-x: auto;
        max-height: 400px;
        overflow-y: auto;
        margin: 0;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }

    // 错误信息样式
    .error-content {
      .error-message {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border-radius: 12px;
        border: 1px solid #fecaca;
        margin-bottom: 20px;

        i {
          font-size: 18px;
          color: #ef4444;
        }

        span {
          font-size: 14px;
          color: #dc2626;
          font-weight: 500;
        }
      }

      .error-suggestions {
        h5 {
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          margin: 0 0 12px 0;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            font-size: 13px;
            color: #64748b;
            margin-bottom: 6px;
            line-height: 1.5;
          }
        }
      }
    }

    // 按钮样式
    .download-btn {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border: none;
      color: white;
      font-weight: 600;
      border-radius: 10px;
      padding: 8px 16px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      &:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
      }

      i {
        margin-right: 6px;
      }
    }

    // 对话框底部样式
    .modern-dialog-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .footer-left {
        .test-info {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #64748b;

          i {
            color: #3b82f6;
          }
        }
      }

      .footer-right {
        display: flex;
        gap: 12px;

        .el-button {
          min-width: 100px;
          height: 40px;
          border-radius: 10px;
          font-weight: 600;
          transition: all 0.3s ease;

          i {
            margin-right: 6px;
          }
        }

        .close-btn {
          background: linear-gradient(135deg, #ffffff, #f8fafc);
          border: 2px solid #e2e8f0;
          color: #64748b;

          &:hover {
            border-color: #3b82f6;
            color: #3b82f6;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15);
          }
        }

        .retest-btn {
          background: linear-gradient(135deg, #10b981, #059669);
          border: none;
          color: white;
          box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);

          &:hover:not(:disabled) {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
          }

          &:disabled {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            color: #94a3b8;
            box-shadow: none;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }
  }
}
</style>
