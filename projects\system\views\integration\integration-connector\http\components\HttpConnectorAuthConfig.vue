<template>
  <div class="http-connector-auth-config">
    <div class="config-section">
      <div class="section-header" @click="toggleCollapse">
        <div class="header-left">
          <h4 class="section-title">基础认证配置</h4>
          <el-tag
            :type="getConfigStatusType()"
            size="mini"
            effect="plain"
            class="status-tag"
          >
            {{ getConfigStatus() }}
          </el-tag>
        </div>
        <div class="header-actions">
          <i :class="['collapse-icon', isCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></i>
        </div>
      </div>

      <el-collapse-transition>
        <div v-show="!isCollapsed" class="form-container">
        <el-form
          ref="authForm"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="small"
          class="auth-form"
        >


        <!-- 认证表达式 -->
        <el-form-item label="认证表达式" prop="authExpression">
          <div class="expression-input-group">
            <el-input
              v-model="formData.authExpression"
              type="textarea"
              :rows="3"
              placeholder="认证表达式，如: ${getAccessToken.access_token}"
              clearable
              @input="handleFieldChange"
            />
            <el-button
              size="small"
              icon="el-icon-video-play"
              :loading="testTokenLoading"
              :disabled="!canTestToken"
              @click="testTokenExtraction"
              class="test-token-btn mac-style-btn"
            >
              测试提取
            </el-button>
          </div>
          <div class="field-help">
            使用 ${requestCode.fieldName} 格式引用认证链中的响应数据，也可以直接使用字符串和加密函数
          </div>
        </el-form-item>

        <!-- 认证位置 -->
        <el-form-item label="认证位置" prop="authLocation">
          <el-select
            v-model="formData.authLocation"
            placeholder="请选择认证位置"
            @change="handleFieldChange"
          >
            <el-option
              v-for="item in authLocationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span>{{ item.label }}</span>
              <span class="option-desc">{{ item.desc }}</span>
            </el-option>
          </el-select>
          <div class="field-help">
            认证信息在请求中的添加位置
          </div>
        </el-form-item>

        <!-- 认证参数名 -->
        <el-form-item label="认证参数名" prop="authParamName">
          <el-input
            v-model="formData.authParamName"
            placeholder="如Authorization、token、access_token等"
            clearable
            @input="(value) => { formData.authParamName = value.trim(); handleFieldChange(); }"
          />
          <div class="field-help">
            认证信息的参数名称，常见如Authorization、X-API-Key等
          </div>
        </el-form-item>
        <!-- 基础URL（只读显示） -->
        <el-form-item label="基础URL">
          <div class="readonly-base-url">
            <el-input
              :value="config.baseUrl || '未配置'"
              readonly
              disabled
              placeholder="未配置基础URL"
            />
          </div>
          <div class="field-help readonly-help">
            <i class="el-icon-info"></i>
            如果需要修改基础URL，请切换到'基础配置'标签页进行修改
          </div>
        </el-form-item>





        <!-- 缓存秒数 -->
        <el-form-item label="缓存秒数" prop="tokenCacheSeconds">
          <div class="cache-seconds-input">
            <input
              type="number"
              v-model.number="formData.tokenCacheSeconds"
              :min="0"
              :max="86400"
              placeholder="0"
              @input="handleCacheSecondsChange"
              @blur="validateCacheSeconds"
              class="cache-input"
            />
            <span class="input-unit">秒</span>
            <div class="quick-options">
              <button
                type="button"
                v-for="option in cacheQuickOptions"
                :key="option.value"
                @click="setCacheSeconds(option.value)"
                :class="['quick-btn', { active: formData.tokenCacheSeconds === option.value }]"
              >
                {{ option.label }}
              </button>
            </div>
          </div>
          <div class="field-help">
            认证信息的缓存时间，0表示不缓存，最大86400秒（24小时）
          </div>
        </el-form-item>
        </el-form>
        </div>
      </el-collapse-transition>
    </div>

    <!-- Token测试结果对话框 -->
    <el-dialog
      title="Token提取测试结果"
      :visible.sync="tokenTestDialogVisible"
      width="90vw"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
      custom-class="connector-test-dialog"
    >
      <div v-if="tokenTestResult" class="token-test-result mac-content">
        <div class="result-summary mac-summary">
          <div v-if="tokenTestResult.success" class="success-indicator">
            <div class="success-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="success-text">
              <h3>Token提取测试成功</h3>
              <p>认证链路执行成功，Token提取表达式正确解析</p>
              <!-- 提取结果信息 -->
              <div class="extraction-info">
                <div class="info-item">
                  <label>提取表达式:</label>
                  <code class="expression-code">{{ tokenTestResult.authExpression }}</code>
                </div>
                <div class="info-item">
                  <label>提取的Token:</label>
                  <code class="extracted-token mac-token">{{ tokenTestResult.extractedToken }}</code>
                </div>
                <div class="info-item">
                  <label>执行耗时:</label>
                  <span class="duration">{{ tokenTestResult.executionDurationMs }}ms</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="error-indicator">
            <div class="error-icon">
              <i class="el-icon-close"></i>
            </div>
            <div class="error-text">
              <h3>Token提取失败</h3>
              <p class="error-message">{{ tokenTestResult.errorMessage }}</p>
            </div>
          </div>
        </div>

        <div v-if="tokenTestResult.success" class="test-details mac-details">
          <h4 class="section-title">链路执行结果</h4>
          <div class="chain-execution-results mac-cards">
            <div
              v-for="(result, stepCode) in tokenTestResult.chainExecutionResults"
              :key="stepCode"
              class="step-result-card mac-card"
            >
              <div class="step-result-header mac-card-header">
                <div class="step-info">
                  <h5>{{ getStepName(stepCode) }}</h5>
                  <span class="step-code">{{ stepCode }}</span>
                </div>
                <div class="success-badge">
                  <i class="el-icon-check"></i>
                  <span>成功</span>
                </div>
              </div>
              <div class="step-result-body mac-card-body">
                <pre class="result-json mac-json">{{ JSON.stringify(result, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <!-- 表达式建议区域 -->
          <div class="expression-suggestions mac-suggestions">
            <h4 class="section-title">表达式建议</h4>
            <div class="suggestions-list mac-tags">
              <div
                v-for="expr in generateExpressionSuggestions()"
                :key="expr"
                class="suggestion-tag mac-tag"
                @click="copyExpression(expr)"
              >
                <i class="el-icon-document-copy"></i>
                <span>{{ expr }}</span>
              </div>
            </div>
            <p class="suggestions-tip mac-tip">
              <i class="el-icon-info"></i>
              点击表达式可复制到剪贴板，用于认证表达式配置
            </p>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer mac-footer">
        <el-button
          @click="tokenTestDialogVisible = false"
          class="mac-close-btn"
          size="medium"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { testHttpConnectorTokenExtraction } from '@system/api/integration/http-connector'

export default {
  name: 'HttpConnectorAuthConfig',
  props: {
    connectorId: {
      type: String,
      default: ''
    },
    config: {
      type: Object,
      default: () => ({})
    },
    authRequests: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isCollapsed: false, // 折叠状态
      formData: {
        authExpression: '',
        authLocation: 'HEADER',
        authParamName: 'Authorization',
        tokenCacheSeconds: null
      },

      authLocationOptions: [
        {
          value: 'HEADER',
          label: '请求头 (HEADER)',
          desc: '添加到HTTP请求头中'
        },
        {
          value: 'URL_PARAM',
          label: 'URL参数 (URL_PARAM)',
          desc: '添加到URL查询参数中'
        },
        {
          value: 'BODY',
          label: '请求体 (BODY)',
          desc: '添加到请求体中'
        }
      ],
      formRules: {
        authExpression: [
          { required: true, message: '请输入认证表达式', trigger: 'blur' }
        ],
        authLocation: [
          { required: true, message: '请选择认证位置', trigger: 'change' }
        ],
        authParamName: [
          { required: true, message: '请输入认证参数名', trigger: 'blur' }
        ],
        tokenCacheSeconds: [
          { type: 'number', message: '缓存秒数必须为数字', trigger: 'change' }
        ]
      },

      cacheQuickOptions: [
        { label: '不缓存', value: 0 },
        { label: '5分钟', value: 300 },
        { label: '30分钟', value: 1800 },
        { label: '1小时', value: 3600 },
        { label: '6小时', value: 21600 },
        { label: '24小时', value: 86400 }
      ],
      // 测试相关数据
      testTokenLoading: false,
      tokenTestResult: null,
      tokenTestDialogVisible: false,

    }
  },
  computed: {
    // 判断是否可以测试Token提取
    canTestToken() {
      return this.config.baseUrl &&
             this.formData.authExpression &&
             this.authRequests &&
             this.authRequests.length > 0 &&
             this.authRequests.some(req => req.enabled)
    }
  },

  watch: {
    config: {
      handler(newConfig) {
        if (newConfig) {
          // 使用 Object.assign 来避免触发 formData 的 watch
          Object.assign(this.formData, newConfig)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {


    // 切换折叠状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },

    // 处理字段变化
    handleFieldChange() {
      // 使用 nextTick 确保数据更新后再触发事件
      this.$nextTick(() => {
        this.$emit('config-change', this.formData)
      })
    },





    // 处理缓存秒数变化
    handleCacheSecondsChange(event) {
      let value = parseInt(event.target.value)
      if (isNaN(value) || value < 0) {
        value = 0
      } else if (value > 86400) {
        value = 86400
      }
      this.formData.tokenCacheSeconds = value
      this.handleFieldChange()
    },

    // 验证缓存秒数
    validateCacheSeconds(event) {
      const value = parseInt(event.target.value)
      if (isNaN(value) || value < 0) {
        this.formData.tokenCacheSeconds = 0
        event.target.value = 0
      } else if (value > 86400) {
        this.formData.tokenCacheSeconds = 86400
        event.target.value = 86400
      }
      this.handleFieldChange()
    },

    // 设置缓存秒数快捷值
    setCacheSeconds(value) {
      this.formData.tokenCacheSeconds = value
      this.handleFieldChange()
    },

    // 获取配置状态
    getConfigStatus() {
      const requiredFields = ['authExpression', 'authLocation', 'authParamName']
      const filledFields = requiredFields.filter(field => this.formData[field] && this.formData[field].trim())

      if (filledFields.length === requiredFields.length) {
        return '配置完整'
      } else if (filledFields.length > 0) {
        return `${filledFields.length}/${requiredFields.length} 已配置`
      } else {
        return '未配置'
      }
    },

    // 获取配置状态类型
    getConfigStatusType() {
      const requiredFields = ['authExpression', 'authLocation', 'authParamName']
      const filledFields = requiredFields.filter(field => this.formData[field] && this.formData[field].trim())

      if (filledFields.length === requiredFields.length) {
        return 'success'
      } else if (filledFields.length > 0) {
        return 'warning'
      } else {
        return 'info'
      }
    },

    // 测试Token提取
    async testTokenExtraction() {
      if (!this.canTestToken) {
        this.$message.error('请先完善基础配置和认证链路')
        return
      }

      this.testTokenLoading = true
      this.$message.info('开始测试Token提取...')

      try {
        // 准备测试数据，按照prompt_word.md中的格式
        const testData = {
          connectorConfig: {
            type: 'HTTP',
            baseUrl: this.config.baseUrl || '',
            headers: this.formData.headers || {},
            authExpression: this.formData.authExpression,
            authLocation: this.formData.authLocation,
            authParamName: this.formData.authParamName,
            // 包含所有config中的其他属性
            ...this.config
          },
          authRequests: this.authRequests.filter(req => req.enabled)
        }

        // 调用测试API
        const response = await testHttpConnectorTokenExtraction(testData)

        // 测试提取返回完整的测试结果，直接使用返回的数据
        // 由于全局响应拦截器已经提取了 data，response 就是完整的测试结果
        this.tokenTestResult = response

        this.tokenTestDialogVisible = true
        this.$message.success('Token提取测试成功')
      } catch (error) {
        this.$message.error(`Token提取测试失败: ${error.message || '未知错误'}`)
      } finally {
        // 确保无论成功还是失败都重置loading状态
        this.testTokenLoading = false
      }
    },



    // 重置表单
    resetForm() {
      this.$refs.authForm.resetFields()
      this.formData = {
        authExpression: '',
        authLocation: 'HEADER',
        authParamName: 'Authorization',
        tokenCacheSeconds: null
      }
    },

    // 获取步骤名称
    getStepName(stepCode) {
      const step = this.authRequests.find(req => req.code === stepCode)
      return step ? (step.name || step.code) : stepCode
    },

    // 从执行结果中提取 token
    extractTokenFromResults(results, authExpression) {
      if (!results || !authExpression) return null

      try {
        // 解析表达式，如 ${getAccessToken.access_token}
        const match = authExpression.match(/\$\{([^.]+)\.([^}]+)\}/)
        if (match) {
          const [, requestCode, fieldName] = match
          const requestResult = results[requestCode]
          if (requestResult && requestResult[fieldName]) {
            return requestResult[fieldName]
          }
        }
        return null
      } catch (error) {
        return null
      }
    },

    // 生成表达式建议
    generateExpressionSuggestions() {
      if (!this.tokenTestResult || !this.tokenTestResult.chainExecutionResults) return []

      const suggestions = []
      Object.keys(this.tokenTestResult.chainExecutionResults).forEach(requestCode => {
        const responseData = this.tokenTestResult.chainExecutionResults[requestCode]
        if (typeof responseData === 'object' && responseData !== null) {
          this.extractFieldPaths(responseData, requestCode, '').forEach(path => {
            suggestions.push(`\${${path}}`)
          })
        }
      })
      return suggestions.slice(0, 20) // 限制建议数量
    },

    // 递归提取字段路径
    extractFieldPaths(obj, prefix, currentPath) {
      const paths = []

      if (typeof obj !== 'object' || obj === null) {
        return paths
      }

      Object.keys(obj).forEach(key => {
        const fullPath = currentPath ? `${prefix}.${currentPath}.${key}` : `${prefix}.${key}`
        const value = obj[key]

        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // 递归处理对象
          paths.push(...this.extractFieldPaths(value, prefix, currentPath ? `${currentPath}.${key}` : key))
        } else {
          // 叶子节点
          paths.push(fullPath)
        }
      })

      return paths
    },

    // 复制表达式到剪贴板
    async copyExpression(expression) {
      try {
        await navigator.clipboard.writeText(expression)
        this.$message.success(`已复制: ${expression}`)
      } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = expression
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success(`已复制: ${expression}`)
      }
    },


  }
}
</script>

<style lang="scss" scoped>
.http-connector-auth-config {
  margin-bottom: 20px;

  .config-section {
    background: white;
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 20px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid rgba(148, 163, 184, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-title {
          font-size: 15px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }

        .status-tag {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;

        .collapse-icon {
          font-size: 14px;
          color: #64748b;
          transition: all 0.2s ease;
        }
      }
    }

    .form-container {
      padding: 20px;

      .auth-form {
        .cache-seconds-input {
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;

          .cache-input {
            width: 120px;
            height: 32px;
            padding: 0 12px;
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 8px;
            font-size: 13px;
            transition: all 0.2s ease;
            outline: none;

            &:focus {
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }

            &[type=number] {
              -moz-appearance: textfield;
            }
          }

          .input-unit {
            font-size: 13px;
            color: #64748b;
            font-weight: 500;
          }

          .quick-options {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;

            .quick-btn {
              padding: 4px 8px;
              font-size: 11px;
              border: 1px solid rgba(148, 163, 184, 0.2);
              border-radius: 4px;
              background: #f8fafc;
              color: #64748b;
              cursor: pointer;
              transition: all 0.2s ease;
              outline: none;

              &:hover {
                border-color: #3b82f6;
                color: #3b82f6;
                background: rgba(59, 130, 246, 0.05);
              }

              &.active {
                border-color: #3b82f6;
                background: #3b82f6;
                color: white;
                box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
              }
            }
          }
        }



        ::v-deep .el-form-item {
          margin-bottom: 20px;

          .el-form-item__label {
            color: #374151;
            font-weight: 500;
            font-size: 14px;
          }

          .el-input {
            .el-input__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);
              transition: all 0.2s ease;

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .el-select {
            .el-input__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .el-textarea {
            .el-textarea__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .el-input-number {
            .el-input__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }
        }
      }
    }

    .field-help {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 4px;
      line-height: 1.4;

      &.readonly-help {
        color: #409eff;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;

        i {
          font-size: 14px;
        }
      }
    }

    // 只读基础URL样式
    .readonly-base-url {
      .el-input {
        .el-input__inner {
          background-color: #f5f7fa;
          border-color: #e4e7ed;
          color: #909399;
          cursor: not-allowed;

          &:hover {
            border-color: #e4e7ed;
          }
        }
      }
    }

    .option-desc {
      float: right;
      font-size: 12px;
      color: #8c8c8c;
    }

    .empty-text {
      text-align: center;
      color: #8c8c8c;
      padding: 10px 0;
    }

    // 表达式输入组样式
    .expression-input-group {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .el-textarea {
        flex: 1;
      }

      .test-token-btn {
        flex-shrink: 0;
        margin-top: 2px;
      }
    }



    // MAC风格按钮
    .mac-style-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      padding: 8px 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        background: #e0e6ed;
        color: #8c8c8c;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;

        &:hover {
          background: #e0e6ed;
          transform: none;
          box-shadow: none;
        }
      }
    }

  }

  // MAC风格Token测试结果 - 样式已移至非scoped部分
}

// MAC风格对话框底部
.mac-footer {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  text-align: right;

  .mac-close-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 10px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);

    &:hover {
      background: linear-gradient(135deg, #5b6470 0%, #3f4651 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

::v-deep .el-form-item {
  margin-bottom: 20px;
}

::v-deep .el-form-item__label {
  font-size: 13px;
  color: #262626;
  font-weight: 500;
}

::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  font-size: 13px;
}

::v-deep .el-select .el-input__inner {
  font-size: 13px;
}

// 修改蒙版颜色
::v-deep .el-dialog__wrapper {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(4px);
}

// 连接器测试对话框样式
::v-deep .connector-test-dialog {
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  margin-bottom: 2.5vh !important;

  .el-dialog {
    border-radius: 16px !important;
    margin: 0 !important;
  }

  .el-dialog__header {
    padding: 24px 24px 0;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: white;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}
</style>

<!-- 非scoped样式，用于对话框内容 -->
<style lang="scss">
// Token测试结果对话框样式（非scoped，因为对话框append-to-body）
.connector-test-dialog {
  .token-test-result.mac-content {
    .mac-summary {
      margin-bottom: 24px;

      .success-indicator {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
        border: 1px solid #b3f5d1;
        border-radius: 12px;

        .success-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

          i {
            font-size: 24px;
            color: white;
            font-weight: bold;
          }
        }

        .success-text {
          flex: 1;

          h3 {
            margin: 0 0 8px 0;
            color: #065f46;
            font-size: 18px;
            font-weight: 600;
          }

          p {
            margin: 0 0 12px 0;
            color: #047857;
            font-size: 14px;
            line-height: 1.5;
          }

          .extraction-info {
            .info-item {
              margin-bottom: 12px;

              &:last-child {
                margin-bottom: 0;
              }

              label {
                display: inline-block;
                font-size: 13px;
                color: #047857;
                font-weight: 500;
                margin-right: 8px;
                min-width: 80px;
              }

              .expression-code {
                background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                border: 1px solid #0ea5e9;
                border-radius: 6px;
                padding: 4px 8px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 12px;
                color: #0c4a6e;
              }

              .mac-token {
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: 4px 8px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 12px;
                color: #374151;
                word-break: break-all;
                max-width: 300px;
                display: inline-block;
              }

              .duration {
                background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                border: 1px solid #f59e0b;
                border-radius: 6px;
                padding: 4px 8px;
                font-size: 12px;
                color: #92400e;
                font-weight: 500;
              }
            }
          }
        }
      }

      .error-indicator {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #fca5a5;
        border-radius: 12px;

        .error-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);

          i {
            font-size: 24px;
            color: white;
            font-weight: bold;
          }
        }

        .error-text {
          flex: 1;

          h3 {
            margin: 0 0 8px 0;
            color: #991b1b;
            font-size: 18px;
            font-weight: 600;
          }

          .error-message {
            margin: 0;
            color: #dc2626;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }

    // MAC风格详情区域样式
    .mac-details {
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e5e7eb;
      }

      .stats-grid,
      .mac-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 24px;

        .stat-item,
        .mac-stat-card {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 1px solid #e2e8f0;
          border-radius: 10px;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .stat-label {
            font-weight: 500;
            color: #64748b;
            font-size: 13px;
          }

          .stat-value {
            color: #1e293b;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            font-weight: 500;
            background: white;
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
          }
        }
      }

      .chain-execution-results,
      .mac-cards {
        .step-result-card,
        .mac-card {
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 12px;
          margin-bottom: 16px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
          }

          .step-result-header,
          .mac-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .step-info {
              h5 {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
              }

              .step-code {
                font-size: 12px;
                color: #64748b;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                background: #f1f5f9;
                padding: 2px 6px;
                border-radius: 4px;
              }
            }

            h5 {
              margin: 0 0 4px 0;
              font-size: 14px;
              font-weight: 600;
              color: #1e293b;
            }

            .success-badge {
              display: flex;
              align-items: center;
              gap: 6px;
              background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
              color: #166534;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;

              i {
                font-size: 12px;
              }
            }

            .el-tag {
              background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
              color: #166534;
              border: none;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;
            }
          }

          .step-result-body,
          .mac-card-body {
            padding: 20px;

            .result-json,
            .mac-json {
              background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              padding: 16px;
              font-size: 12px;
              line-height: 1.6;
              color: #374151;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              max-height: 300px;
              overflow-y: auto;
              margin: 0;
              white-space: pre-wrap;
            }
          }
        }
      }
    }

    // 表达式建议区域样式
    .mac-suggestions {
      margin-top: 24px;
      padding: 20px;
      background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
      border: 1px solid #fbbf24;
      border-radius: 12px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #92400e;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '💡';
          font-size: 18px;
        }
      }

      .mac-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 12px;

        .mac-tag {
          background: white;
          border: 1px solid #d97706;
          color: #92400e;
          padding: 8px 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 6px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          font-weight: 500;

          &:hover {
            background: #f59e0b;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
          }

          i {
            font-size: 12px;
          }
        }
      }

      .mac-tip {
        margin: 0;
        font-size: 12px;
        color: #92400e;
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
