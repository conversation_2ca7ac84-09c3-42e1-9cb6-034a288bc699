<template>
  <div class="login-container" id="loginContainer">
    <img :src="pageConfigJson.background" class="login_bgc" alt="" srcset="">

    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on"
      label-position="left" :style="pageConfigJson.main.style">
      <div class="title-container flex" v-if="pageConfigJson.label" :style="pageConfigJson.label.style"
        style="align-items: center; justify-content: center;">
        <img :src="pageConfigJson.label.logo.url" :style="pageConfigJson.label.logo.style" />
        <span class="title" :style="pageConfigJson.label.content.style">{{ pageConfigJson.label.content.text }}</span>
      </div>

      <el-form-item prop="username" class="form_line">
        <el-input ref="username" :style="pageConfigJson.main.input.normal" v-model="loginForm.username"
          placeholder="用户名" name="username" type="text" tabindex="1" auto-complete="on">
          <i class="el-icon-user" slot="prefix" />
          <!-- <img src="@/assets/icon_user.svg" class="input_prefix" slot="prefix" alt="" srcset=""> -->
        </el-input>
      </el-form-item>

      <el-form-item prop="password" class="form_line">
        <el-input :style="pageConfigJson.main.input.normal" :key="passwordType" ref="password"
          v-model="loginForm.password" :type="passwordType" placeholder="密码" show-password name="password" tabindex="2"
          auto-complete="on" @keyup.enter.native="handleLogin">
          <i class="el-icon-key" slot="prefix" />
          <!-- <img src="@/assets/icon_pass.svg" class="input_prefix" slot="prefix" alt="" srcset=""> -->
        </el-input>
        <!-- <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" /> -->
        <!-- </span> -->
      </el-form-item>
      <el-form-item prop="captcha" class="form_line">
        <div class="flex" style="align-items: flex-end; justify-content: space-between;">
          <el-input type="text" id="code" tabindex="2" ref="captcha" :style="pageConfigJson.main.input.normal"
            v-model="loginForm.code" placeholder="验证码" @keyup.enter.native="handleLogin">
            <i class="el-icon-lock" slot="prefix" />
            <!-- <img src="@/assets/icon_captch.svg" class="input_prefix" slot="prefix" alt="" srcset=""> -->
          </el-input>
          <img :src="base64ToImage" style="height: 42px; border-radius: 3px;" @click="clickPic" />
        </div>
      </el-form-item>
      <!-- <el-checkbox v-model="isChecked" class="form_checkbox">记住密码</el-checkbox> -->
      <el-form-item>
        <el-button class="login_btn" :style="pageConfigJson.main.button.style" :loading="loading" type="primary"
          @click.native.prevent="handleLogin">登录</el-button>
      </el-form-item>
    </el-form>

  </div>

</template>

<script>
import { code, getConfigJson } from "@/api/login/login";

export default {
  name: "Login",
  data() {
    return {
      loginForm: {
        username: "",
        password: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: '请输入用户名' }
        ],
        password: [
          { required: true, trigger: "blur", message: '密码不能为空' }
        ],
        code: [
          { required: true, trigger: "blur", message: '验证码不能为空' }
        ]
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
      isChecked: true,
      img: null,
      pageConfigJson: {
        "label": {
          "style": {},
          "logo": {
            "url": "",
            "style": {}
          },
          "content": {
            "text": "智慧园区管理平台",
            "style": {}
          }
        },
        "background": "",
        "main": {
          "style": {},
          "input": {
            "normal": {},
            "normalInput": {},
            "focus": {},
            "focusinput": {},
            "prefix": {}
          },
          "button": {
            "content": "登录",
            "style": {}
          }
        }
      }
    };
  },
  computed: {
    base64ToImage() {
      return `data:image/png;base64,${this.img}`;
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    // 页面布局配置
    this.getConfigJson()
    //调用生成验证码接口
    this.createCode();

  },
  mounted() {
    // 页面加载调用获取Cookie值
    this.$nextTick(() => {
      this.getCookie();
    });
    this.refreshStyle()
  },

  methods: {
    getConfigJson() {
      getConfigJson().then(res => {
        this.pageConfigJson = res.login
        this.refreshStyle()
      })
    },
    refreshStyle() {
      try {
        let is = document.querySelectorAll('.el-input__prefix i')
        var prefixStyle = this.pageConfigJson.main.input.prefix
        is.forEach(item => {
          Object.assign(item.style, prefixStyle)
        })
      } catch (error) { console.log(error) }
      try {
        let is = document.querySelectorAll('.el-input input')
        var { normalInput, focusInput, focus, normal } = this.pageConfigJson.main.input
        is.forEach(item => {
          Object.assign(item.style, normalInput)
          item.addEventListener('focus', (e) => {
            Object.assign(item.style, focusInput)
            Object.assign(e.target.parentElement.style, focus)
          })
          item.addEventListener('blur', (e) => {
            Object.assign(item.style, normalInput)
            Object.assign(e.target.parentElement.style, normal)
          })
        })
      } catch (error) { console.log(error) }

    },
    createCode() {
      code().then(res => {
        console.log(';lllllllfllflf', res)
        this.img = res.base64
        this.loginForm.uuid = res.uuid
      })
    },
    clickPic(data) {
      this.createCode();
    },
    randomNum(min, max) {
      return Math.floor(Math.random() * (max - min) + min);
    },
    makeCode(o, l) {
      for (let i = 0; i < l; i++) {
        this.identifyCode += this.identifyCodes[
          this.randomNum(0, this.identifyCodes.length)
        ];
      }
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          let passwordValue = this.loginForm.password;
          let data = JSON.parse(JSON.stringify(this.loginForm));
          data.password = this.$encruption(passwordValue);
          this.$store
            .dispatch("user/login", {data})
            .then(() => {
              if (this.redirect?.startsWith('http://') || this.redirect?.startsWith('https://')) {
                window.location.href = this.redirect
              } else {
                this.$router.push({ path: this.redirect || "/" });
              }
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
              this.createCode();
            });
        } else {
          console.log("error submit!!");

          return false;
        }
      });
    },

    //设置cookie
    setCookie(c_name, c_pwd, exdays, isChecked) {
      let exdate = new Date(); //获取时间
      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays); //保存的天数
      //字符串拼接cookie
      window.document.cookie =
        "userName" + "=" + c_name + ";path=/;expires=" + exdate.toGMTString();
      window.document.cookie =
        "userPwd" + "=" + c_pwd + ";path=/;expires=" + exdate.toGMTString();
      window.document.cookie =
        "isChecked" +
        "=" +
        isChecked +
        ";path=/;expires=" +
        exdate.toGMTString();
    },

    // 清除Cookie
    clearCookie() {
      this.setCookie("", "", -1, false); // 修改2值都为空，天数为负1天就好了
    },

    // 读取Cookie
    getCookie() {
      console.log('cookie', document.cookie);
      if (document.cookie.length > 0) {
        let arr = document.cookie.split("; "); // 这里显示的格式需要切割一下自己可输出看下
        for (let i = 0; i < arr.length; i++) {
          let arr2 = arr[i].split("="); // 再次切割
          // 判断查找相对应的值
          if (arr2[0] == "userName") {
            this.loginForm.username = arr2[1]; // 保存到保存数据的地方
          } else if (arr2[0] == "userPwd") {
            this.loginForm.password = arr2[1];
          } else if (arr2[0] == "isChecked") {
            this.isChecked = arr2[1];
          }
        }
      } else {
        //
      }
    }
  }
};
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #fff;
$light_gray: #303133;
$cursor: #303133;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
#loginContainer {
  .loginContainerImg {
    width: 100%;
    height: 80vh;
    margin-top: 10vh;
  }

  .el-tooltip__popper {
    background-color: #fff;
  }

  .el-input {
    display: inline-block;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px #f0f0f0 inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  // .el-form-item {
  //   // border: 1px solid #d9d9d9;
  //   background: #f0f0f0;
  //   border-radius: 25px;
  //   color: #666666;
  //   height: 50px;
  // }

  .margin-lr-vw {
    margin-left: 2vw;
    margin-right: 2vw;
  }
}

.login-container .el-form-item__content {
  width: 100%;
}

.login-container .el-form-item .el-input__inner {
  padding: 0 !important;
  padding-left: 40px !important;

  height: 35px !important;
  line-height: 35px !important;
}

.el-checkbox__label {
  font-size: 14px;
  font-weight: 400;
  line-height: 14px;
  color: rgba(94, 107, 130, 1);
}
</style>

<style lang="scss" scoped>
.login_btn {
  border-radius: 23px;
  background: linear-gradient(109.28deg, rgba(9, 173, 249, 1) 0%, rgba(70, 80, 251, 1) 100%);

  margin-top: 20px;
  width: 100%;
}

$bg: #0c0c0c;
$dark_gray: #303133;
$light_gray: #303133;

.topRightImg {
  width: 100vw;
  position: absolute;
  right: 0px;
  top: 0px;
}

.topRightImg img {
  width: 100%;
  height: 100%;
}

.bottomImg {
  width: 100vw;
  position: absolute;
  left: 0px;
  bottom: 0px;
}

.bottomImg img {
  width: 100%;
  height: 100%;
}

.bottomLeftImg {
  width: 15vw;
  position: absolute;
  left: 0px;
  bottom: 0px;
}

.bottomLeftImg img {
  width: 100%;
  height: 100%;
}

.cRightImg {
  width: 38vw;
  // height: 40vw;
  position: absolute;
  right: 0vw;
  top: 0px;
  z-index: 10000;
}

.cRightImg img {
  width: 100%;
  height: 100%;
}

.login-container {
  width: 100vw;
  height: 100vh;
  background-color: $bg;
  overflow: hidden;
  position: relative;

  .login_bgc {
    width: 1920px;
    height: 1080px;
    position: absolute;
    left: 0px;
    top: 0px;
  }

  .title-divider {
    width: 34%;
    padding: 15px;
    text-align: center;
    margin: 40px 0px 30px 33%;
    border-bottom: 3px solid #1890ff;
    color: #1890ff;
  }

  .login-form {
    position: absolute;
    // top: 321px;
    // left: 1176px;
    width: 405px;
    height: 437px;
    border-radius: 20px;
    background: rgba(255, 255, 255, .88);
    padding: 0 60px;

    .form_line {
      border-radius: 0 !important;
      background: none !important;
    }

    .input_prefix {
      width: 20px;
      height: 20px;
      margin-left: 5px;
      position: relative;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: rgba(0, 0, 0, 0.25);
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    width: 100%;

    font-size: 26px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 34px;
    color: rgba(0, 0, 0, 1);

    text-align: center;
    padding-top: 43px;
    padding-bottom: 4px;
    font-family: PangMenZhengDaoBiaoTi;

    .Logo {
      width: 2vw;
      height: 2vw;
      margin-right: 0.5vw;
    }

    .title {
      font-size: 1.4em;
      color: dark_gray;
      text-align: center;
      font-weight: 900;
      line-height: 1vh;
    }

    .title-bottom {
      display: block;
      padding: 10px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.25);
    cursor: pointer;
    user-select: none;
  }

  .lizi {
    width: 100vw;
    height: 100vh;
    // position: absolute;
    // top:0;
    // background-color: red;
  }

  .login-bottom {
    width: 100vw;
    position: fixed;
    bottom: 0vh;
    text-align: center;
    padding: 10px 0px;
    color: rgba(0, 0, 0, 0.45);

    .leftRow {
      padding: 15px;
    }
  }

  .captchaClass {
    background: #f0f0f0;
    border-radius: 25px;
    color: #666666;
    height: 50px;
    line-height: 50px;
    padding-left: 25px;
  }

  .captcha>>>.el-form-item__error {
    padding-left: 10px;
  }

  .captcha>>>.el-input input {
    margin-left: 14px;
  }
}
</style>
