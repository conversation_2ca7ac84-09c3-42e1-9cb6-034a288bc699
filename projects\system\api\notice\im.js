import request from '@/utils/request'

const api = '/notice'

// 获取消息列表
export function getMessageList(params) {
  const { chatType, current, size, ...queryCondition } = params
  return request({
    url: `${api}/message/admin/history`,
    method: 'get',
    params: {
      chatType,
      current,
      size,
      ...queryCondition,
    }
  })
}

// 撤回消息
export function recallMessage(id) {
  return request({
    url: `${api}/message/recall/${id}`,
    method: 'put'
  })
}

// 批量撤回消息
export function batchRecallMessages(ids) {
  return request({
    url: `${api}/message/recall/batch`,
    method: 'put',
    data: ids
  })
}

// 更新消息
export function updateMessage(data) {
  return request({
    url: `${api}/message`,
    method: 'put',
    data
  })
}

//  ====================== 管理员管理各类群、私聊、系统消息 =====================================

