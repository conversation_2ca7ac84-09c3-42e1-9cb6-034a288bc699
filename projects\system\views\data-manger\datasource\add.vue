<template>
  <div class="datasource-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>添加数据源</h2>
        <div class="back-button" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          <span>返回数据源列表</span>
        </div>
      </div>
    </div>

    <!-- 步骤条 -->
    <el-steps :active="currentStep" finish-status="success" align-center class="steps">
      <el-step title="选择数据源" description="选择数据库类型"></el-step>
      <el-step title="信息配置" description="配置连接信息"></el-step>
    </el-steps>

    <!-- 步骤内容区域 -->
    <div class="step-content">
      <!-- 第一步：选择数据源类型 -->
      <div v-show="currentStep === 0" class="db-type-selection">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" v-for="db in dbTypes" :key="db.value">
            <div 
              class="db-card" 
              :class="{ active: type === db.value }"
              @click="selectDbType(db.value)"
            >
              <div class="card-header">
                <div class="db-icon-wrapper" :style="{ color: db.color }">
                  <i :class="getDbIcon(db.value)" class="db-icon"></i>
                </div>
                <div class="header-right">
                  <h3>{{ db.label }}</h3>
                  <div class="db-badges">
                    <span class="badge-item" :style="{background: db.color + '1A', color: db.color}">
                      {{ db.version || '支持多版本' }}
                    </span>
                    <span class="badge-item">
                      <i class="el-icon-check"></i>
                      官方认证
                    </span>
                  </div>
                </div>
              </div>
              <p>{{ db.description }}</p>
              <div class="card-footer">
                <div class="db-features">
                  <span class="feature-tag">
                    <i class="el-icon-connection"></i>
                    支持JDBC
                  </span>
                  <span class="feature-tag">
                    <i class="el-icon-data-line"></i>
                    标准SQL
                  </span>
                </div>
                <el-button 
                  type="text" 
                  class="select-button"
                  :class="{ 'is-selected': type === db.value }"
                >
                  {{ type === db.value ? '已选择' : '选择' }}
                </el-button>
              </div>
              <div class="selected-indicator" v-if="type === db.value">
                <i class="el-icon-check"></i>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 第二步：配置连接信息 -->
      <div v-show="currentStep === 1" class="connection-config">
        <el-form 
          ref="connectionForm" 
          :model="connectionForm" 
          :rules="connectionRules"
          label-width="120px"
          class="connection-form"
        >
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="数据源类型">
                  <el-input 
                    v-model="typeName" 
                    disabled
                    class="readonly-input"
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="数据源名称" prop="name">
                  <el-input 
                    v-model="connectionForm.name"
                    placeholder="请输入数据源名称"
                  ></el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="数据源编码" prop="code">
                  <el-input 
                    v-model="connectionForm.code"
                    placeholder="请输入数据源编码，只能包含字母、数字和下划线"
                  >
                    <template slot="append">
                      <el-tooltip content="数据源编码需要唯一，只能包含字母、数字和下划线" placement="top">
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="描述" prop="description">
                  <el-input 
                    type="textarea"
                    v-model="connectionForm.description"
                    placeholder="请输入数据源描述"
                    :rows="3"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <div class="section-title">连接信息</div>
            <el-row :gutter="20">
              <!-- 主机名 -->
              <el-col :span="12">
                <el-form-item label="主机名" prop="host">
                  <el-input 
                    v-model="connectionForm.host"
                    placeholder="请输入数据库主机名"
                  ></el-input>
                </el-form-item>
              </el-col>

              <!-- 端口号 -->
              <el-col :span="12">
                <el-form-item label="端口号" prop="port">
                  <el-input 
                    v-model.number="connectionForm.port"
                    placeholder="请输入数据库端口号"
                    type="number"
                  ></el-input>
                </el-form-item>
              </el-col>

              <!-- 服务名(仅Oracle显示) -->
              <el-col :span="12" v-if="isOracleType">
                <el-form-item label="服务名" prop="serverName">
                  <el-input 
                    v-model="connectionForm.serverName"
                    placeholder="请输入数据库服务名"
                  >
                    <template slot="append">
                      <el-tooltip content="Oracle数据库必填(SID或服务名)" placement="top">
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>

              <!-- 默认模式(非Oracle显示) -->
              <el-col :span="12" v-if="!isOracleType">
                <el-form-item label="默认模式" prop="defaultSchema">
                  <el-input 
                    v-model="connectionForm.defaultSchema"
                    placeholder="请输入默认模式(选填)"
                  >
                    <template slot="append">
                      <el-tooltip content="数据库默认模式/默认schema" placement="top">
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>

              <!-- 额外参数 -->
              <el-col :span="12">
                <el-form-item label="连接参数" prop="arg">
                  <el-input 
                    v-model="connectionForm.arg"
                    placeholder="示例: useUnicode=true&characterEncoding=utf8"
                  >
                    <template slot="append">
                      <el-tooltip content="多个参数使用&连接" placement="top">
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>

              <!-- 数据库驱动 -->
              <el-col :span="24">
                <el-form-item label="数据库驱动" prop="driverClassName">
                  <el-input 
                    v-model="connectionForm.driverClassName"
                    placeholder="未填写时将根据数据库类型自动选择"
                  >
                    <template slot="append">
                      <el-tooltip :content="getDefaultDriver(type)" placement="top">
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </template>
                  </el-input>
                  <div class="driver-tip" v-if="!connectionForm.driverClassName">
                    <i class="el-icon-info"></i>
                    <span>默认驱动：{{ getDefaultDriver(type) }}</span>
                  </div>
                </el-form-item>
              </el-col>

              <!-- 用户名 -->
              <el-col :span="12">
                <el-form-item label="用户名" prop="username">
                  <el-input 
                    v-model="connectionForm.username"
                    placeholder="请输入数据库用户名"
                  ></el-input>
                </el-form-item>
              </el-col>

              <!-- 密码 -->
              <el-col :span="12">
                <el-form-item label="密码" prop="password">
                  <el-input 
                    v-model="connectionForm.password"
                    type="password"
                    placeholder="请输入数据库密码"
                    show-password
                  ></el-input>
                </el-form-item>
              </el-col>

              <!-- 预览生成的JDBC URL -->
              <el-col :span="24">
                <el-form-item label="JDBC URL预览">
                  <div class="jdbc-preview">
                    <span class="preview-content">{{ generatedJdbcUrl }}</span>
                    <div class="preview-actions">
                      <el-button 
                        type="text" 
                        class="refresh-btn"
                        @click="refreshJdbcUrlPreview"
                        icon="el-icon-refresh"
                      >
                        刷新预览
                      </el-button>
                      <el-button 
                        type="text" 
                        class="copy-btn"
                        @click="copyGeneratedUrl"
                      >
                        <i class="el-icon-document-copy"></i>
                        复制
                      </el-button>
                    </div>
                  </div>
                </el-form-item>
              </el-col>

              <!-- 数据源分组 -->
              <el-col :span="24">
                <el-form-item label="数据源分组" prop="groupName">
                  <el-select 
                    v-model="connectionForm.groupName" 
                    placeholder="请选择或输入数据源分组"
                    filterable
                    allow-create
                    default-first-option
                    clearable
                    class="full-width-select"
                  >
                    <el-option
                      v-for="group in groupList"
                      :key="group"
                      :label="group"
                      :value="group"
                    />
                  </el-select>
                  <div class="form-tip">
                    <i class="el-icon-info"></i>
                    <span>可以选择已有分组或输入新分组名称</span>
                  </div>
                </el-form-item>
              </el-col>

              <!-- 测试连接按钮 -->
              <el-col :span="24">
                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="testConnection"
                    :loading="testing"
                    icon="el-icon-connection"
                  >测试连接</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <div class="step-actions">
      <el-button @click="prevStep" v-if="currentStep > 0">上一步</el-button>
      <el-button 
        type="primary" 
        @click="nextStep"
        v-if="currentStep < 1"
        :disabled="!canProceed"
      >下一步</el-button>
      <el-button 
        type="primary" 
        @click="saveDataSource"
        v-if="currentStep === 1"
        :loading="saving"
        icon="el-icon-check"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import DbIcon from './DbIcon.vue'
import { createDataSource, testDataSourceConnection, getDataSourceGroups, getSupportedDataSourceTypes, previewJdbcUrl } from '@system/api/data-manger/datasource'

export default {
  name: 'DatasourceAdd',
  components: {
    DbIcon
  },
  data() {
    // 服务名验证规则
    const validateServerName = (rule, value, callback) => {
      if (this.type && this.type.toLowerCase() === 'oracle' && !value) {
        callback(new Error('Oracle数据库必须填写服务名'));
      } else {
        callback();
      }
    };

    return {
      currentStep: 0,
      type: '',
      testing: false,
      saving: false,
      dbTypes: [],
      previewJdbcUrl: '',
      connectionForm: {
        name: '',
        code: '',
        description: '',
        host: '',
        port: '',
        serverName: '',
        defaultSchema: '',
        arg: '',
        username: '',
        password: '',
        driverClassName: '',
        groupName: '默认分组'
      },
      connectionRules: {
        name: [
          { required: true, message: '请输入数据源名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入数据源编码', trigger: 'blur' },
          { pattern: /^[A-Za-z0-9_]+$/, message: '编码只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        host: [
          { required: true, message: '请输入数据库主机名', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入数据库端口号', trigger: 'blur' }
        ],
        serverName: [
          { validator: validateServerName, trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      groupList: ['默认分组'],
      supportedTypesMap: {}
    }
  },
  computed: {
    typeName() {
      const selected = this.dbTypes.find(db => db.value === this.type)
      return selected ? selected.label : ''
    },
    isOracleType() {
      return this.type && this.type.toLowerCase() === 'oracle'
    },
    canProceed() {
      if (this.currentStep === 0) {
        return !!this.type
      }
      return true
    },
    generatedJdbcUrl() {
      return this.previewJdbcUrl || ''
    }
  },
  created() {
    this.fetchSupportedTypes();
    this.fetchGroupList();
  },
  methods: {
    // 返回列表页
    goBack() {
      this.$router.push('/data/datasource')
    },
     
    selectDbType(type) {
      this.type = type;
      
      // 自动填充驱动类名
      if (this.supportedTypesMap[type]) {
        this.connectionForm.driverClassName = this.supportedTypesMap[type].driverClassName || '';
      }
    },

    async fetchGroupList() {
          try {
            const data = await getDataSourceGroups("groupName")
            this.groupList = data?.length ? data : ['默认分组']
          } catch (error) {
            console.error('获取分组列表失败:', error)
            this.groupList = ['默认分组']
          }
    },
    
    async fetchSupportedTypes() {
      try {
        const res = await getSupportedDataSourceTypes();
        if (res && Array.isArray(res)) {
          // 创建一个map用于快速查找
          this.supportedTypesMap = res.reduce((acc, type) => {
            acc[type.code] = type;
            return acc;
          }, {});
          
          // 构建dbTypes用于UI显示
          this.dbTypes = res.map(type => {
            // 为每种类型分配一个颜色
            let color = '#409EFF'; // 默认蓝色
            let version = '';
            
            if (type.code.includes('mysql')) {
              color = '#00758F';
              version = '5.x/8.x';
            } else if (type.code.includes('oracle')) {
              color = '#FF0000';
              version = '11g/12c/19c';
            } else if (type.code.includes('postgresql')) {
              color = '#336791';
              version = '9.x/10.x/12.x';
            } else if (type.code.includes('sqlserver')) {
              color = '#0078D4';
              version = '2016/2019/2022';
            } else if (type.code.includes('transwarp') || type.code.includes('hive')) {
              color = '#F29111';
              version = 'Hadoop';
            }
            
            return {
              label: type.name,
              value: type.code,
              color: color,
              version: version,
              description: type.description || `${type.name}数据库`
            };
          });
        }
      } catch (error) {
        console.error('获取支持的数据源类型失败:', error);
        // 使用默认值
        this.dbTypes = [
          {
            label: 'MySQL',
            value: 'mysql',
            color: '#00758F',
            version: '5.x/8.x',
            description: '流行的开源关系型数据库'
          },
          {
            label: 'Oracle',
            value: 'oracle',
            color: '#FF0000',
            version: '11g/12c/19c',
            description: '企业级关系型数据库'
          },
          {
            label: 'PostgreSQL',
            value: 'postgresql',
            color: '#336791',
            version: '9.x/10.x/12.x',
            description: '功能强大的开源数据库'
          }
        ];
      }
    },

    // 获取表单数据
    getFormData() {
      return {
        type: this.type.toUpperCase(),
        name: this.connectionForm.name,
        code: this.connectionForm.code,
        description: this.connectionForm.description,
        host: this.connectionForm.host,
        port: this.connectionForm.port,
        serverName: this.connectionForm.serverName,
        defaultSchema: this.connectionForm.defaultSchema,
        arg: this.connectionForm.arg,
        username: this.connectionForm.username,
        password: this.connectionForm.password,
        driverClassName: this.connectionForm.driverClassName || this.getDefaultDriver(this.type),
        groupName: this.connectionForm.groupName
      }
    },

    // 刷新JDBC URL预览
    async refreshJdbcUrlPreview() {
      const data = await previewJdbcUrl(this.getFormData());
      this.previewJdbcUrl = data;
    },

    // 测试连接
    async testConnection() {
      try {
        await this.$refs.connectionForm.validate()
      } catch (error) {
        return
      }

      this.testing = true
      try {
        const res = await testDataSourceConnection(this.getFormData())
        this.$message.success('连接成功')
      } finally {
        this.testing = false
      }
    },

    // 保存数据源
    async saveDataSource() {
      try {
        await this.$refs.connectionForm.validate()
      } catch (error) {
        return
      }

      this.saving = true
      try {
        await createDataSource(this.getFormData())
        this.$message.success('保存成功')
        this.$router.push('/data/datasource')
      } finally {
        this.saving = false
      }
    },

    copyExample() {
      const text = this.getFullExample
      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('示例已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },
    copyGeneratedUrl() {
      const text = this.generatedJdbcUrl
      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('JDBC URL已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },
    getDefaultDriver(type) {
      if (!type || !this.supportedTypesMap[type]) return '';
      return this.supportedTypesMap[type].driverClassName || '';
    },
    getDbIcon(type) {
      const iconMap = {
        mysql: 'el-icon-coin',
        oracle: 'el-icon-platform-eleme',
        postgresql: 'el-icon-data-analysis',
        sqlserver: 'el-icon-office-building',
        hive: 'el-icon-monitor',
        transwarp: 'el-icon-cpu',
        default: 'el-icon-folder-opened'
      }
      return iconMap[type] || iconMap.default
    },
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    async nextStep() {
      if (this.currentStep === 1) {
        try {
          await this.$refs.connectionForm.validate()
        } catch (error) {
          return
        }
      }
      if (this.currentStep < 1) {
        this.currentStep++
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.datasource-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  min-height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eef1f7;

    .header-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .back-button {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #409EFF;
        transition: all 0.3s;
        font-size: 14px;
        font-weight: 500;

        i {
          margin-right: 6px;
          font-size: 14px;
        }

        &:hover {
          color: #66b1ff;
          transform: translateX(-3px);
        }
      }
    }
  }

  .steps {
    margin-bottom: 32px;
    padding: 0 20px;

    ::v-deep .el-step__head {
      .el-step__icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
        background: #f5f7fa;
        border-color: #c0c4cc;
      }

      &.is-process .el-step__icon {
        background: #409EFF;
        border-color: #409EFF;
        
        .el-step__icon-inner {
          color: #fff;
          font-weight: 600;
        }
      }
      
      &.is-finish .el-step__icon {
        background: #67C23A;
        border-color: #67C23A;
        color: #fff;
      }
    }
    
    ::v-deep .el-step__main {
      .el-step__title {
        font-size: 14px;
        font-weight: 500;
        
        &.is-process {
          color: #409EFF;
          font-weight: 600;
        }
        
        &.is-finish {
          color: #67C23A;
        }
      }
      
      .el-step__description {
        font-size: 12px;
        margin-top: 5px;
      }
    }
  }

  .step-content {
    padding: 0 20px 20px;
    flex: 1;
    
    .db-type-selection {
      .el-row {
        margin-bottom: 20px;
      }
    }

    .connection-form {
      width: 100%;
      margin: 0 auto;
    }
  }

  .db-card {
    height: 100%;
    padding: 20px;
    border: 1px solid #e6e6e6;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: #fff;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.09);
    }

    &.active {
      border-color: #409EFF;
      background: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);

      .db-icon-wrapper {
        &::before {
          opacity: 0.12;
        }
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .header-right {
        flex: 1;
        margin-left: 16px;
      }
    }

    .db-icon-wrapper {
      width: 50px;
      height: 50px;
      min-width: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: currentColor;
        opacity: 0.08;
      }

      .db-icon {
        font-size: 24px;
        z-index: 1;
      }
    }

    h3 {
      margin: 0 0 8px;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
    
    .db-badges {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      
      .badge-item {
        padding: 2px 8px;
        font-size: 12px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        background: #f5f7fa;
        color: #606266;
        
        i {
          margin-right: 4px;
          font-size: 12px;
        }
      }
    }

    p {
      margin: 0 0 16px;
      font-size: 13px;
      color: #606266;
      line-height: 1.6;
      flex: 1;
    }
    
    .card-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: auto;
    }
    
    .db-features {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .feature-tag {
        padding: 2px 8px;
        background: #f0f2f5;
        color: #606266;
        font-size: 12px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        
        i {
          margin-right: 4px;
          font-size: 12px;
          color: #67C23A;
        }
      }
    }
    
    .select-button {
      font-size: 13px;
      color: #409EFF;
      padding: 0;
      
      &.is-selected {
        color: #67C23A;
      }
    }

    .selected-indicator {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 22px;
      height: 22px;
      border-radius: 50%;
      background: #409EFF;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.35);
      animation: scale-in 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      z-index: 2;
    }
    
    @keyframes scale-in {
      from { transform: scale(0); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
  }

  .form-section {
    width: 100%;
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 22px;
        
        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          line-height: 1.4;
          padding-bottom: 8px;
          color: #1a1f36;
          font-weight: 500;
          display: flex;
          align-items: center;
          height: 38px;
          justify-content: flex-end;
          text-align: right;
        }

        .el-input,
        .el-select,
        .el-cascader {
          width: 100%;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-input-group__prepend,
        .el-input-group__append {
          background-color: #f5f7fa;
          border-color: #e0e5ee;
          color: #909399;
          padding: 0 12px;
        }
        
        .el-input-group__prepend {
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
        }
        
        .el-input-group__append {
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
        }

        .el-textarea__inner {
          border-radius: 10px;
          border-color: #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-select,
        .el-cascader {
          .el-input {
            width: 100%;
          }
        }
      }
    }
  }

  .jdbc-prefix {
    color: #909399;
    user-select: none;
  }

  .url-example,
  .driver-tip,
  .form-tip {
    margin-top: 8px;
    font-size: 13px;
    color: #909399;
    line-height: 1.5;
  }

  .url-example {
    padding: 12px;
    background: #f8f9fb;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      background: #ecf5ff;
    }

    .example-header {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 8px;

      i {
        color: #409EFF;
      }
    }

    .example-content {
      display: flex;
      align-items: center;
      font-family: Monaco, Menlo, Consolas, monospace;
      font-size: 13px;
      word-break: break-all;
      
      .prefix {
        color: #909399;
        flex-shrink: 0;
      }
      
      .content {
        color: #409EFF;
        margin: 0 8px;
        flex: 1;
      }

      .copy-btn {
        flex-shrink: 0;
        padding: 4px 8px;
        
        i {
          margin-right: 4px;
        }

        &:hover {
          background: rgba(64, 158, 255, 0.1);
          border-radius: 4px;
        }
      }
    }
  }

  .driver-tip,
  .form-tip {
    display: flex;
    align-items: center;
    gap: 6px;

    i {
      color: #409EFF;
    }
  }

  .full-width-select {
    width: 100%;
  }

  .step-actions {
    margin-top: 24px;
    padding: 24px 0 0;
    text-align: center;
    border-top: 1px solid #eef1f7;

    .el-button {
      min-width: 120px;
      padding: 10px 20px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      border-radius: 10px;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      & + .el-button {
        margin-left: 16px;
      }
      
      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
        
        &[disabled] {
          color: #FFF;
          background-color: #a0cfff;
          border-color: #a0cfff;
          box-shadow: none;
        }
      }
    }
  }

  .type-display {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 15px;
    height: 38px;
    line-height: 38px;
    border: 1px solid #e0e5ee;
    border-radius: 8px;
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;

    i {
      color: #409EFF;
      font-size: 18px;
    }
  }

  .jdbc-preview {
    padding: 12px 16px;
    background: #f8f9fb;
    border: 1px solid #e0e5ee;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s;
    font-family: Monaco, Menlo, Consolas, monospace;

    &:hover {
      background: #ecf5ff;
      border-color: #409EFF;
    }

    .preview-content {
      color: #606266;
      flex: 1;
      margin-right: 16px;
      word-break: break-all;
    }

    .preview-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
      
      .refresh-btn,
      .copy-btn {
        padding: 4px 8px;
        
        i {
          margin-right: 4px;
        }

        &:hover {
          background: rgba(64, 158, 255, 0.1);
          border-radius: 4px;
        }
      }
    }
  }
}
</style> 