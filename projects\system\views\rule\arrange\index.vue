<template>
  <div class="api-arrange-container">
    <div class="page-header">
      <div class="header-title">
        <h2>可视化编排</h2>
      </div>

      <!-- 编排链信息区域 -->
      <div class="chain-info">
        <div class="info-item">
          <span class="label">编排唯一标识:</span>
          <el-input
              v-model="flow.name"
              placeholder="请输入编排链唯一标识"
              size="small"
              class="chain-input"
              @input="handleChainNameInput"
          ></el-input>
        </div>
        <div class="info-item">
          <span class="label">描述:</span>
          <el-input
              v-model="flow.description"
              placeholder="请输入编排链描述"
              size="small"
              class="chain-input"
          ></el-input>
        </div>
      </div>

      <div class="header-tools">
        <div class="tool-buttons">
          <el-tooltip content="删除选中" placement="bottom">
            <el-button
                size="small"
                icon="el-icon-delete"
                circle
                :disabled="!currentNode && !currentConnection"
                @click="handleDelete"
            ></el-button>
          </el-tooltip>
          <el-tooltip content="清空画布" placement="bottom">
            <el-button size="small" icon="el-icon-delete-solid" circle @click="handleClear"></el-button>
          </el-tooltip>
        </div>

        <div class="tool-buttons">
          <el-tooltip content="缩小" placement="bottom">
            <el-button size="small" icon="el-icon-zoom-out" circle @click="zoomOut"></el-button>
          </el-tooltip>
          <div class="zoom-display">{{ Math.round(scale * 100) }}%</div>
          <el-tooltip content="放大" placement="bottom">
            <el-button size="small" icon="el-icon-zoom-in" circle @click="zoomIn"></el-button>
          </el-tooltip>
          <el-tooltip content="重置缩放" placement="bottom">
            <el-button size="small" icon="el-icon-refresh" circle @click="resetZoom"></el-button>
          </el-tooltip>
        </div>

        <div class="tool-buttons">
          <el-tooltip :content="configPanelVisible ? '隐藏配置面板' : '显示配置面板'" placement="bottom">
            <el-button
                size="small"
                :icon="configPanelVisible ? 'el-icon-close' : 'el-icon-setting'"
                :type="configPanelVisible ? 'info' : 'primary'"
                circle
                @click="toggleConfigPanel"
            ></el-button>
          </el-tooltip>
        </div>

        <el-divider direction="vertical"></el-divider>

        <div class="main-buttons">
          <el-button type="primary" icon="el-icon-document-add" @click="handleSave">保存</el-button>
          <el-button type="success" icon="el-icon-view" @click="handlePreview">执行</el-button>
          <el-button type="warning" icon="el-icon-upload" @click="handleDeploy">发布</el-button>
        </div>
      </div>
    </div>

    <div class="api-arrange">
      <!-- 左侧节点面板 - 可收起 -->
      <div class="node-panel" :class="{'collapsed': nodePanelCollapsed}">
        <div class="panel-title">
          <span>节点类型</span>
          <el-button
              type="text"
              :icon="nodePanelCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"
              @click="toggleNodePanel"
          ></el-button>
        </div>
        <div class="node-list" v-show="!nodePanelCollapsed">
          <!-- 分组显示节点类型 (简化版) -->
          <div v-for="group in nodeGroups" :key="group.id" class="node-group">
            <div class="group-title">
              <span>{{ group.name }}</span>
              <el-divider></el-divider>
            </div>

            <!-- 普通节点列表 -->
            <template v-if="group.nodes && group.nodes.length > 0">
              <div class="node-grid">
                <div
                    v-for="node in group.nodes"
                    :key="`${group.id}_${node.type}`"
                    class="node-item"
                    draggable="true"
                    @dragstart="handleDragStart($event, node)"
                    :title="node.label"
                >
                  <i :class="node.icon"></i>
                  <span>{{ node.label }}</span>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <div
            class="canvas"
            ref="canvas"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @click="deselectAll"
            @wheel.prevent="handleWheel"
            @mousedown="startCanvasDrag"
            :class="{ 'canvas-dragging': isCanvasDragging }"
        >
          <div
              class="canvas-content"
              :style="{
              transform: `scale(${scale})`,
              transformOrigin: '0 0'
            }"
          >
            <!-- 连线 -->
            <flow-connection
                v-for="connection in flow.connections"
                :key="connection.id"
                :source="getNodeById(connection.sourceId)"
                :target="getNodeById(connection.targetId)"
                :selected="currentConnection === connection"
                @click="selectConnection(connection, $event)"
                @dblclick="openConnectionConfig(connection, $event)"
                @contextmenu="showContextMenu($event, connection, 'connection')"
            />

            <!-- 节点 -->
            <div
                v-for="node in flow.nodes"
                :key="node.id"
                :data-node-id="node.id"
                class="node"
                :class="{
                'node-selected': currentNode && currentNode.id === node.id,
                'node-start': node.type === 'START_NODE',
                'node-end': node.type === 'END_NODE'
              }"
                :style="{
                left: `${node.position.x}px`,
                top: `${node.position.y}px`
              }"
                @click.stop="selectNode(node, $event)"
                @dblclick.stop="openNodeConfig(node, $event)"
                @mousedown="startDrag($event, node)"
                @contextmenu.prevent="showContextMenu($event, node, 'node')"
            >
              <!-- 节点配置按钮 -->
              <div class="node-actions">
                <el-button
                    class="node-config-btn"
                    type="primary"
                    size="mini"
                    icon="el-icon-setting"
                    circle
                    @click.stop="openNodeConfig(node, $event)"
                    title="配置节点"
                ></el-button>
                <!-- 添加专家模式按钮 -->
                <el-button
                    v-if="node.type !== 'START_NODE' && node.type !== 'END_NODE'"
                    class="node-expert-btn"
                    type="warning"
                    size="mini"
                    icon="el-icon-s-tools"
                    circle
                    @click.stop="openExpertMode(node, $event)"
                    title="专家模式"
                ></el-button>
              </div>

              <!-- 快捷添加按钮 (非结束节点显示) -->
              <div class="node-quick-add" v-if="node.type !== 'END_NODE'">
                <el-button
                    class="quick-add-btn"
                    type="success"
                    size="mini"
                    icon="el-icon-plus"
                    circle
                    @click.stop="showQuickAddMenu(node, $event)"
                    title="快速添加后续节点"
                ></el-button>
              </div>

              <!-- 特殊节点样式 (开始/结束节点) -->
              <template v-if="node.type === 'START_NODE' || node.type === 'END_NODE'">
                <div class="node-header" :class="`node-type-${node.type}`">
                  <i :class="getNodeIcon(node.type)"></i>
                  <span>{{ node.name || node.id }}</span>
                </div>
                <div class="node-body">
                  <div class="node-type">{{ getNodeTypeName(node.type) }}</div>
                </div>
              </template>

              <!-- 普通节点样式 -->
              <template v-else>
                <div class="node-header" :class="`node-type-${node.type}`">
                  <i :class="getNodeIcon(node.type)"></i>
                  <span>{{ node.name || node.id }}</span>
                </div>

                <!-- 已移除应用信息标签 -->

                <div class="node-body">
                  <div class="node-type">{{ getNodeTypeName(node.type) }}</div>
                  <div class="node-config" v-if="node.type === 'HTTP_NODE'">
                    {{ node.config.method || 'GET' }} {{ truncateUrl(node.config.url) }}
                  </div>
                </div>
              </template>

              <div class="node-ports">
                <div
                    class="port port-in"
                    @mousedown.stop="startConnection($event, node, 'target')"
                ></div>
                <div
                    class="port port-out"
                    @mousedown.stop="startConnection($event, node, 'source')"
                ></div>
              </div>
            </div>

            <!-- 连线创建时的临时线 -->
            <svg v-if="isCreatingConnection" class="temp-connection">
              <path
                  :d="tempConnectionPath"
                  stroke="#409eff"
                  stroke-width="2"
                  stroke-dasharray="5,5"
                  fill="none"
              ></path>
            </svg>
          </div>

          <!-- 迷你地图 - 固定在右下角 -->
          <div class="minimap fixed-position" v-if="showMinimap && flow.nodes.length > 0">
            <div class="minimap-title">
              <span>导航地图</span>
              <el-button
                  type="text"
                  icon="el-icon-close"
                  size="mini"
                  @click="toggleMinimap"
              ></el-button>
            </div>
            <div class="minimap-content">
              <div
                  class="minimap-node"
                  v-for="node in flow.nodes"
                  :key="`mini_${node.id}`"
                  :style="{
                  left: `${node.position.x * minimapScale}px`,
                  top: `${node.position.y * minimapScale}px`,
                  backgroundColor: getNodeColor(node.type)
                }"
                  @click="navigateToNode(node)"
              ></div>
              <div
                  class="minimap-viewport"
                  :style="minimapViewportStyle"
              ></div>
            </div>
          </div>

          <!-- 迷你地图切换按钮 - 固定在右下角 -->
          <div class="minimap-toggle fixed-position" v-if="!showMinimap && flow.nodes.length > 0">
            <el-button
                type="primary"
                icon="el-icon-map-location"
                circle
                size="mini"
                @click="toggleMinimap"
            ></el-button>
          </div>

          <!-- 右键菜单 -->
          <div
              v-show="contextMenu.visible"
              class="context-menu"
              :style="{
              left: `${contextMenu.x}px`,
              top: `${contextMenu.y}px`
            }"
          >
            <div class="menu-item" @click="openConfigFromMenu">
              <i class="el-icon-setting"></i>
              <span>配置{{ contextMenu.type === 'node' ? '节点' : '连线' }}</span>
            </div>
            <div class="menu-item" @click="deleteFromMenu">
              <i class="el-icon-delete"></i>
              <span>删除{{ contextMenu.type === 'node' ? '节点' : '连线' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 配置面板按钮 -->
      <div class="config-panel-button" v-if="currentNode || currentConnection">
        <el-tooltip :content="configPanelVisible ? '隐藏配置面板' : '显示配置面板'" placement="left">
          <el-button
              type="primary"
              circle
              :icon="configPanelVisible ? 'el-icon-close' : 'el-icon-setting'"
              @click="toggleConfigPanel"
          ></el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 右侧配置面板 - 抽屉式 -->
    <el-drawer
        :visible.sync="configPanelVisible"
        direction="rtl"
        :size="'400px'"
        :show-close="false"
        :modal="false"
        custom-class="config-drawer"
    >
      <div slot="title" class="drawer-title">{{ getPanelTitle() }}</div>
      <div class="config-content">
        <template v-if="currentNode">
          <node-config :node="currentNode" @update="handleNodeUpdate" />
        </template>
        <template v-else-if="currentConnection">
          <connection-config
              :connection="currentConnection"
              :sourceNode="getNodeById(currentConnection.sourceId)"
              :targetNode="getNodeById(currentConnection.targetId)"
              @update="handleConnectionUpdate"
              @delete="handleConnectionDelete"
          />
        </template>
        <template v-else>
          <flow-config :flow="flow" @update="handleFlowUpdate" />
        </template>
      </div>
    </el-drawer>

    <!-- 快捷添加菜单 -->
    <div
        v-show="showQuickAdd"
        class="quick-add-menu"
        :style="{
        left: `${quickAddPosition.x}px`,
        top: `${quickAddPosition.y}px`
      }"
    >
      <div class="menu-title">添加节点</div>
      <div class="menu-items">
        <div
            v-for="(group, groupIndex) in nodeGroups.slice(0, 2)"
            :key="`quick_${group.id}`"
            class="menu-group"
        >
          <div class="group-title">{{ group.name }}</div>
          <div class="group-items">
            <div
                v-for="node in group.nodes.filter(n => n.type !== 'startNode')"
                :key="`quick_${group.id}_${node.type}`"
                class="menu-item"
                @click.stop="quickAddNode(node.type)"
            >
              <i :class="node.icon"></i>
              <span>{{ node.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 执行结果抽屉 -->
    <el-drawer
        :visible.sync="previewVisible"
        direction="rtl"
        :size="'50%'"
        :show-close="true"
        :modal="true"
        custom-class="preview-drawer"
    >
      <div slot="title" class="drawer-title">流程执行结果</div>
      <preview-result
          v-if="previewResult"
          :result="previewResult"
          @close="previewVisible = false"
      />
    </el-drawer>
  </div>
</template>

<script>
import NodeConfig from './components/NodeConfig.vue'
import FlowConfig from './components/FlowConfig.vue'
import ParamTable from './components/ParamTable.vue'
import MonacoEditor from './components/MonacoEditor.vue'
import FlowConnection from './components/FlowConnection.vue'
import ConnectionConfig from './components/ConnectionConfig.vue'
import apiArrange from '@system/api/rule/arrange'
import { getRuleScriptByScriptId } from '@system/api/rule/script'
import PreviewResult from './components/PreviewResult.vue'

export default {
  name: 'ApiArrange',

  components: {
    NodeConfig,
    FlowConfig,
    ParamTable,
    MonacoEditor,
    FlowConnection,
    ConnectionConfig,
    PreviewResult
  },

  // 挂载API
  beforeCreate() {
    this.$apiArrange = apiArrange
  },

  data() {
    return {
      // 节点类型定义（分组）- 与后端保持一致
      nodeGroups: [
        {
          id: 'flow',
          name: '编排组件',
          nodes: [
            { type: 'START_NODE', label: '开始节点', icon: 'el-icon-video-play' },
            { type: 'END_NODE', label: '结束节点', icon: 'el-icon-video-pause' },
            { type: 'SWITCH_NODE', label: '选择节点', icon: 'el-icon-sort' },
            { type: 'CONDITION_NODE', label: '条件节点', icon: 'el-icon-finished' },
            { type: 'FOR_NODE', label: 'For循环节点', icon: 'el-icon-refresh' },
            { type: 'WHILE_NODE', label: 'While循环节点', icon: 'el-icon-refresh-right' },
            { type: 'LOOP_NODE', label: '循环节点', icon: 'el-icon-refresh-left' },
            { type: 'PARALLEL_NODE', label: '并行节点', icon: 'el-icon-s-operation' },
            { type: 'COMMON_NODE', label: '普通节点', icon: 'el-icon-s-unfold' }
          ]
        },
        {
          id: 'request',
          name: '通用请求',
          nodes: [
            { type: 'HTTP_NODE', label: 'HTTP节点', icon: 'el-icon-connection' },
            { type: 'SCRIPT_NODE', label: '脚本节点', icon: 'el-icon-document' },
            { type: 'DATA_PROCESS_NODE', label: '数据处理节点', icon: 'el-icon-data-line' }
          ]
        },
        {
          id: 'logic',
          name: '逻辑节点',
          nodes: [
            { type: 'AND_NODE', label: '与逻辑节点', icon: 'el-icon-link' },
            { type: 'OR_NODE', label: '或逻辑节点', icon: 'el-icon-share' },
            { type: 'NOT_NODE', label: '非逻辑节点', icon: 'el-icon-remove' }
          ]
        },
        // 移除集成应用分组，简化代码
        {
          id: 'integration',
          name: '集成应用',
          expanded: {},
          subGroups: []
        }
      ],

      // 节点类型定义（扁平化，用于查找）
      nodeTypes: [],

      // 当前流程数据
      flow: {
        chain_id: '',
        name: '',
        description: '',
        nodes: [],
        connections: [],
        flow: ''
      },

      // 当前选中的节点
      currentNode: null,

      // 当前选中的连线
      currentConnection: null,

      // 拖拽相关
      isDragging: false,
      dragNode: null,
      dragOffset: { x: 0, y: 0 },

      // 连线创建相关
      isCreatingConnection: false,
      connectionSource: null,
      connectionTarget: null,
      connectionType: null,
      mousePosition: { x: 0, y: 0 },

      // 缩放相关
      scale: 1,
      minScale: 0.2,
      maxScale: 2,
      scaleStep: 0.1,

      // 迷你地图相关
      showMinimap: true,
      minimapScale: 0.1, // 迷你地图的缩放比例

      // 面板控制
      nodePanelCollapsed: false,
      configPanelVisible: false,

      // 画布拖动相关
      isCanvasDragging: false,
      canvasDragStart: { x: 0, y: 0 },
      canvasScrollPosition: { x: 0, y: 0 },

      // 右键菜单
      contextMenu: {
        visible: false,
        x: 0,
        y: 0,
        target: null,
        type: null // 'node' 或 'connection'
      },

      // 快捷添加节点相关
      showQuickAdd: false,
      quickAddSourceNode: null,
      quickAddPosition: { x: 0, y: 0 },

      // 执行结果相关
      previewVisible: false,
      previewResult: null,

      // 历史记录相关
      history: [],
      historyIndex: -1,
      maxHistoryLength: 50,
      isUndoRedoing: false,
    }
  },

  created() {
    // 扁平化节点类型，方便查找
    this.nodeTypes = this.nodeGroups.reduce((acc, group) => {
      if (group.nodes) {
        acc = acc.concat(group.nodes);
      }
      if (group.subGroups) {
        group.subGroups.forEach(subGroup => {
          if (subGroup.apis) {
            acc = acc.concat(subGroup.apis);
          }
        });

        // 初始化展开状态
        group.subGroups.forEach(subGroup => {
          this.$set(group.expanded, subGroup.id, false);
        });
      }
      return acc;
    }, []);

    // 检查URL参数中是否包含chainId
    const chainId = this.$route.query.chainId;
    console.log('Route query params:', this.$route.query);
    console.log('编排链唯一标识 from URL:', chainId);

    if (chainId && chainId !== 'undefined' && chainId !== 'null') {
      // 如果有chainId，加载对应的编排链数据
      this.loadChainData(chainId);
    } else {
      // 加载示例数据或创建新流程
      this.loadExampleData();

      // 如果流程为空，创建一个新流程
      if (!this.flow.nodes || this.flow.nodes.length === 0) {
        this.createNewFlow();
      }
    }
  },

  computed: {
    // 临时连线路径
    tempConnectionPath() {
      if (!this.isCreatingConnection) return ''

      let startX, startY, endX, endY

      if (this.connectionType === 'source') {
        startX = this.connectionSource.position.x + 180 // 节点宽度
        startY = this.connectionSource.position.y + 40  // 节点高度的一半
        endX = this.mousePosition.x
        endY = this.mousePosition.y
      } else {
        startX = this.mousePosition.x
        startY = this.mousePosition.y
        endX = this.connectionSource.position.x
        endY = this.connectionSource.position.y + 40    // 节点高度的一半
      }

      // 计算控制点距离，确保曲线平滑
      // 使用节点间距离的比例而不是固定值，以适应不同分辨率
      const distance = Math.abs(endX - startX)
      const dx = Math.min(distance * 0.5, 150) // 限制最大控制点距离

      const controlPoint1 = { x: startX + dx, y: startY }
      const controlPoint2 = { x: endX - dx, y: endY }

      return `M ${startX} ${startY} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${endX} ${endY}`
    },

    // 计算画布所需的最小尺寸
    canvasMinSize() {
      if (!this.flow.nodes || this.flow.nodes.length === 0) {
        return { width: '100%', height: '100%' }
      }

      // 找出所有节点中最右边和最下边的位置
      let maxX = 0
      let maxY = 0

      this.flow.nodes.forEach(node => {
        const rightEdge = node.position.x + 200 // 节点宽度 + 边距
        const bottomEdge = node.position.y + 100 // 节点高度 + 边距

        maxX = Math.max(maxX, rightEdge)
        maxY = Math.max(maxY, bottomEdge)
      })

      // 确保画布至少有足够的空间容纳所有节点,并且有额外的空间用于拖拽
      return {
        width: `${Math.max(2000, maxX + 400)}px`, // 至少2000px宽,或者足够容纳所有节点加400px边距
        height: `${Math.max(1500, maxY + 400)}px`  // 至少1500px高,或者足够容纳所有节点加400px边距
      }
    },

    // 迷你地图视口样式
    minimapViewportStyle() {
      const canvasEl = this.$refs.canvas
      if (!canvasEl) return {}

      const canvasRect = canvasEl.getBoundingClientRect()
      const contentWidth = parseFloat(this.canvasMinSize.width)
      const contentHeight = parseFloat(this.canvasMinSize.height)

      // 计算视口在迷你地图中的位置和大小
      const viewportWidth = Math.min(canvasRect.width / (contentWidth * this.scale), 1) * 100
      const viewportHeight = Math.min(canvasRect.height / (contentHeight * this.scale), 1) * 100

      const scrollLeftRatio = canvasEl.scrollLeft / (contentWidth * this.scale - canvasRect.width)
      const scrollTopRatio = canvasEl.scrollTop / (contentHeight * this.scale - canvasRect.height)

      const viewportLeft = scrollLeftRatio * (100 - viewportWidth)
      const viewportTop = scrollTopRatio * (100 - viewportHeight)

      return {
        width: `${viewportWidth}%`,
        height: `${viewportHeight}%`,
        left: `${viewportLeft}%`,
        top: `${viewportTop}%`
      }
    }
  },

  mounted() {
    // 监听全局鼠标事件
    document.addEventListener('mousemove', this.handleMouseMove)
    document.addEventListener('mouseup', this.handleMouseUp)

    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown)

    // 初始化历史记录
    this.saveToHistory();
  },

  beforeDestroy() {
    // 移除全局事件监听
    document.removeEventListener('mousemove', this.handleMouseMove)
    document.removeEventListener('mouseup', this.handleMouseUp)
    document.removeEventListener('keydown', this.handleKeyDown)
  },

  methods: {
    // 创建新流程
    createNewFlow() {
      // 获取当前时间用于生成默认名称和描述
      const now = new Date();
      const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
      const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

      const flowName = `flow_${dateStr.replace(/-/g, '')}_${timeStr.replace(':', '')}`;
      const flowDesc = `新建流程 (${dateStr} ${timeStr})`;

      // 创建一个带有开始和结束节点的新流程
      const startNodeId = `node_start_${Date.now()}`;
      const endNodeId = `node_end_${Date.now()}`;
      
      const startNode = {
        id: startNodeId,
        name: '开始',
        type: 'START_NODE',
        position: { x: 100, y: 250 }
      };

      const endNode = {
        id: endNodeId,
        name: '结束',
        type: 'END_NODE',
        position: { x: 700, y: 250 }
      };

      // 创建兼容后端格式的原始定义
      const originalDefinition = {
        metadata: {
          application: 'extreme-flow',
          chainName: flowName,
          chainDesc: flowDesc
        },
        nodes: [
          {
            nodeId: startNodeId,
            type: 'START_NODE',
            title: '开始',
            description: '流程的开始节点',
            position: { x: 100, y: 250 }
          },
          {
            nodeId: endNodeId,
            type: 'END_NODE',
            title: '结束',
            description: '流程的结束节点',
            position: { x: 700, y: 250 }
          }
        ],
        edges: [
          {
            sourceNodeId: startNodeId,
            targetNodeId: endNodeId
          }
        ]
      };

      this.flow = {
        chain_id: '',  // 新流程不设置ID
        name: flowName,
        description: flowDesc,
        nodes: [startNode, endNode],
        connections: [
          {
            id: `conn_1`,
            sourceId: startNodeId,
            targetId: endNodeId,
            type: 'default',
            priority: 1
          }
        ],
        flow: `THEN(${startNodeId}, ${endNodeId})`,
        // 保存原始定义
        originalDefinition: originalDefinition
      };
    },

    // 加载空白流程
    loadExampleData() {
      // 直接创建一个新的流程，不使用示例数据
      this.createNewFlow();
    },

    // 拖拽开始 (简化版)
    handleDragStart(event, node) {
      // 如果是开始或结束节点，检查是否已存在
      if ((node.type === 'START_NODE' && this.hasStartNode()) ||
          (node.type === 'END_NODE' && this.hasEndNode())) {
        // 如果已经存在相应类型的节点，则阻止拖拽
        event.preventDefault();
        this.$message.warning(`流程中只能有一个${node.type === 'START_NODE' ? '开始' : '结束'}节点`);
        return;
      }

      event.dataTransfer.setData('nodeType', node.type);
      event.dataTransfer.effectAllowed = 'copy';
    },

    // 拖拽放置 (简化版)
    handleDrop(event) {
      event.preventDefault();
      const nodeType = event.dataTransfer.getData('nodeType');
      if (nodeType) {
        // 检查开始/结束节点限制
        if ((nodeType === 'START_NODE' && this.hasStartNode()) ||
            (nodeType === 'END_NODE' && this.hasEndNode())) {
          this.$message.warning(`流程中只能有一个${nodeType === 'START_NODE' ? '开始' : '结束'}节点`);
          return;
        }

        const rect = this.$refs.canvas.getBoundingClientRect();
        const position = {
          x: (event.clientX - rect.left) / this.scale,
          y: (event.clientY - rect.top) / this.scale
        };

        this.addNode(nodeType, position);
      }
    },

    // 添加节点 (简化版)
    addNode(type, position, appInfo = null) {
      const node = {
        id: `node_${Date.now()}`,
        name: this.getNodeTypeName(type),
        type,
        config: this.getDefaultConfig(type),
        position,
        post_extract: {}
      };

      this.flow.nodes.push(node);

      // 如果是通过快捷添加按钮添加的，自动创建连线
      if (this.quickAddSourceNode) {
        this.createConnection(this.quickAddSourceNode.id, node.id);
        this.hideQuickAdd();
      }

      this.selectNode(node, new Event('click'));

      // 如果是首次添加开始节点，自动添加结束节点
      if (type === 'START_NODE' && this.flow.nodes.length === 1) {
        this.addNode('END_NODE', {
          x: position.x + 600,
          y: position.y
        });
      }

      // 保存历史记录
      this.saveToHistory();

      return node;
    },

    // 检查是否已存在开始节点
    hasStartNode() {
      return this.flow.nodes.some(node => node.type === 'START_NODE');
    },

    // 检查是否已存在结束节点
    hasEndNode() {
      return this.flow.nodes.some(node => node.type === 'END_NODE');
    },

    // 获取应用信息 (简化版)
    getAppInfoFromType(type) {
      // 应用信息已简化，直接返回null
      return null;
    },

    // 显示快捷添加菜单
    showQuickAddMenu(node, event) {
      // 如果是结束节点，不显示快捷添加菜单
      if (node.type === 'END_NODE') return;

      event.stopPropagation();

      // 获取加号按钮的DOM元素
      const buttonElement = event.target.closest('.quick-add-btn');
      if (!buttonElement) return;

      // 获取画布元素和其位置信息
      const canvas = this.$refs.canvas;
      const canvasRect = canvas.getBoundingClientRect();

      // 获取按钮在视口中的位置
      const buttonRect = buttonElement.getBoundingClientRect();

      // 计算按钮在画布中的相对位置（考虑缩放和滚动）
      const buttonPositionInCanvas = {
        x: (buttonRect.right - canvasRect.left) / this.scale + canvas.scrollLeft / this.scale,
        y: (buttonRect.top + buttonRect.height/2 - canvasRect.top) / this.scale + canvas.scrollTop / this.scale
      };

      this.quickAddSourceNode = node;

      // 菜单定位在按钮右侧，垂直居中
      this.quickAddPosition = {
        x: buttonPositionInCanvas.x + 10, // 按钮右侧加10px的间距
        y: buttonPositionInCanvas.y - 150  // 垂直居中偏上，避免遮挡节点
      };

      this.showQuickAdd = true;

      // 添加一次性事件监听器，点击其他地方关闭菜单
      setTimeout(() => {
        document.addEventListener('click', this.hideQuickAdd, { once: true });
      }, 0);
    },

    // 隐藏快捷添加菜单
    hideQuickAdd() {
      this.showQuickAdd = false;
      this.quickAddSourceNode = null;
    },

    // 快速添加节点
    quickAddNode(type) {
      if (!this.quickAddSourceNode) return;

      // 检查节点类型限制 (复用拖拽校验逻辑)
      if ((type === 'START_NODE' && this.hasStartNode()) ||
          (type === 'END_NODE' && this.hasEndNode())) {
        this.$message.warning(`流程中只能有一个${type === 'START_NODE' ? '开始' : '结束'}节点`);
        return;
      }

      // 计算新节点位置
      const newPosition = {
        x: this.quickAddPosition.x + 20,
        y: this.quickAddSourceNode.position.y
      };

      // 添加新节点并创建连线
      const newNode = this.addNode(type, newPosition);

      // 隐藏快捷添加菜单
      this.hideQuickAdd();
    },

    // 清空画布
    handleClear() {
      this.$confirm('确认清空画布？所有节点和连线将被删除。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清空后创建新流程（包含开始和结束节点）
        this.createNewFlow();
      }).catch(() => {});
    },

    // 拖拽悬停
    handleDragOver(event) {
      event.preventDefault()
    },

    // 开始拖动节点
    startDrag(event, node) {
      if (event.target.closest('.node-ports')) return

      this.isDragging = true
      this.dragNode = node

      const canvas = this.$refs.canvas
      const nodeRect = event.currentTarget.getBoundingClientRect()

      // 计算鼠标相对于节点的偏移，考虑缩放因素
      this.dragOffset = {
        x: (event.clientX - nodeRect.left) / this.scale,
        y: (event.clientY - nodeRect.top) / this.scale
      }

      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)
    },

    // 拖动中
    onDrag(event) {
      if (!this.isDragging || !this.dragNode) return

      const canvas = this.$refs.canvas
      const rect = canvas.getBoundingClientRect()

      // 考虑画布的滚动位置和缩放比例
      const x = Math.max(0, ((event.clientX - rect.left) / this.scale + canvas.scrollLeft / this.scale) - this.dragOffset.x)
      const y = Math.max(0, ((event.clientY - rect.top) / this.scale + canvas.scrollTop / this.scale) - this.dragOffset.y)

      this.dragNode.position = { x, y }
    },

    // 停止拖动
    stopDrag() {
      if (this.isDragging) {
        this.isDragging = false
        this.dragNode = null

        document.removeEventListener('mousemove', this.onDrag)
        document.removeEventListener('mouseup', this.stopDrag)

        // 保存历史记录
        this.saveToHistory();
      }
    },

    // 选择节点
    selectNode(node, event) {
      event.stopPropagation()
      this.currentNode = node
      this.currentConnection = null
    },

    // 双击节点打开配置
    openNodeConfig(node, event) {
      event.stopPropagation()
      this.currentNode = node
      this.currentConnection = null
      this.configPanelVisible = true
    },

    // 获取节点默认配置 (简化版)
    getDefaultConfig(type) {
      // 返回基于类型的默认配置
      switch (type) {
        case 'HTTP_NODE':
          return { method: 'GET', url: '', params: {}, body: {}, headers: {} };
        case 'SCRIPT_NODE':
          return { scriptType: 'js', script: '// 在此编写脚本\n' };
        case 'CONDITION_NODE':
          return { condition: '' };
        case 'SWITCH_NODE':
          return { condition: '' };
        case 'FOR_NODE':
          return { times: 3 };
        case 'WHILE_NODE':
          return { condition: '' };
        case 'DATA_PROCESS_NODE':
          return { type: 'process', params: {} };
        case 'LOOP_NODE':
          return { loopType: 'count', count: 3 };
        case 'AND_NODE':
        case 'OR_NODE':
        case 'NOT_NODE':
          return { inputs: [] };
        case 'START_NODE':
        case 'END_NODE':
          return {};
        default:
          return {};
      }
    },

    // 获取节点类型名称
    getNodeTypeName(type) {
      const nodeType = this.nodeTypes.find(n => n.type === type)
      return nodeType ? nodeType.label : type
    },

    // 获取节点图标
    getNodeIcon(type) {
      const nodeType = this.nodeTypes.find(n => n.type === type)
      return nodeType ? nodeType.icon : 'el-icon-question'
    },

    // 截断URL显示
    truncateUrl(url) {
      if (!url) return ''
      return url.length > 20 ? url.substring(0, 20) + '...' : url
    },

    // 节点配置更新
    handleNodeUpdate(node) {
      const index = this.flow.nodes.findIndex(n => n.id === node.id)
      if (index > -1) {
        this.flow.nodes.splice(index, 1, node)

        // 保存历史记录
        this.saveToHistory();
      }
    },

    // 流程配置更新
    handleFlowUpdate(flow) {
      this.flow = {
        ...flow,
        nodes: this.flow.nodes,
        connections: this.flow.connections
      }
    },

    // 保存流程
    async handleSave() {
      // 转换流程为后端需要的格式
      const definition = this.convertFlowToDefinition();
        let chainId = this.flow.chain_id;
        let isUpdate = !!chainId;

        // 准备canvas数据
        let canvasData;
        
        // 检查是否有原始流程定义数据
        if (this.flow.originalDefinition) {
          // 如果有原始后端格式，则更新它并保留
          const updatedDefinition = this.updateOriginalDefinition(this.flow.originalDefinition);
          
          // 创建用于保存的数据：保留原始结构，但添加前端专用的_frontendData字段
          const saveData = JSON.parse(JSON.stringify(updatedDefinition));
          
          // 添加前端渲染数据
          saveData._frontendData = {
            nodes: this.flow.nodes.map(node => {
              // 深拷贝并清理前端数据
              const cleanNode = JSON.parse(JSON.stringify(node));
              if (cleanNode.originalNode) delete cleanNode.originalNode;
              return cleanNode;
            }),
            connections: this.flow.connections.map(conn => {
              // 深拷贝并清理前端数据
              const cleanConn = JSON.parse(JSON.stringify(conn));
              if (cleanConn.originalEdge) delete cleanConn.originalEdge;
              return cleanConn;
            }),
            scale: this.scale
          };
          
          canvasData = JSON.stringify(saveData);
          console.log('使用原始定义格式保存canvas数据');
        } else {
          // 创建一个符合后端期望的结构
          const backendFormat = {
            metadata: {
              application: 'extreme-flow',
              chainName: this.flow.name || '未命名流程',
              chainDesc: this.flow.description || '无描述'
            },
            nodes: this.flow.nodes.map(node => ({
              nodeId: node.id,
              type: node.type,
              title: node.name || node.id,
              description: node.description || '',
              position: node.position ? {
                x: Math.round(node.position.x),
                y: Math.round(node.position.y)
              } : undefined,
              config: node.config || {},
              outputMapping: node.post_extract || {}
            })),
            edges: this.flow.connections.map(conn => ({
              sourceNodeId: conn.sourceId,
              targetNodeId: conn.targetId,
              condition: conn.condition || undefined
            })),
            // 添加前端渲染数据
            _frontendData: {
              nodes: JSON.parse(JSON.stringify(this.flow.nodes)),
              connections: JSON.parse(JSON.stringify(this.flow.connections)),
              scale: this.scale
            }
          };
          
          canvasData = JSON.stringify(backendFormat);
          console.log('创建新的后端格式保存canvas数据');
        }

        console.log('准备保存的canvas数据:', canvasData);

        // 准备统一的请求参数格式，无论是新增还是更新
        const chainData = {
          chainName: this.flow.name || '未命名流程',
          chainDesc: this.flow.description || '无描述',
          canvas: canvasData
        };

        if (isUpdate) {
          // 获取当前的编排链数据，以保留其他字段
          const currentChain = await this.$apiArrange.getChainDetail(chainId);
          
          // 更新已有编排链，只有更新时才需要id
          await this.$apiArrange.updateChain({
            ...currentChain,
            ...chainData,
            id: chainId
          });

          this.$message.success('更新成功');
        } else {
          // 创建新编排链，不传id参数
          chainId = await this.$apiArrange.addChain({...chainData,
            application: 'extreme-flow',
            enabled: true
          });
          
          // 保存返回的chainId
          if (chainId) {
            this.flow.chain_id = chainId;
          }
          
          this.$message.success('保存成功');
        }
     
    },
    
    // 更新原始流程定义
    updateOriginalDefinition(originalDefinition) {
      // 创建原始定义的深拷贝，避免直接修改引用
      const updatedDefinition = JSON.parse(JSON.stringify(originalDefinition));
      
      // 创建节点映射，方便快速查找
      const nodeMap = {};
      this.flow.nodes.forEach(node => {
        nodeMap[node.id] = node;
      });
      
      // 更新节点信息，严格保持原有结构
      updatedDefinition.nodes = updatedDefinition.nodes.map(originalNode => {
        const frontendNode = nodeMap[originalNode.nodeId];
        if (frontendNode) {
          // 只更新可能变化的字段，保持原有结构
          originalNode.title = frontendNode.name || originalNode.title;
          originalNode.description = frontendNode.description || originalNode.description;
          
          // 更新配置 - 如果前端有配置
          if (frontendNode.config) {
            originalNode.config = originalNode.config || {};
            // 合并配置，确保保留原有的所有字段
            Object.keys(frontendNode.config).forEach(key => {
              originalNode.config[key] = frontendNode.config[key];
            });
          }
          
          // 更新输出映射 - 如果前端有变更
          if (frontendNode.post_extract) {
            originalNode.outputMapping = originalNode.outputMapping || {};
            // 合并映射，确保保留原有的所有字段
            Object.keys(frontendNode.post_extract).forEach(key => {
              originalNode.outputMapping[key] = frontendNode.post_extract[key];
            });
          }
          
          // 添加或更新位置信息
          originalNode.position = originalNode.position || {};
          originalNode.position.x = Math.round(frontendNode.position.x);
          originalNode.position.y = Math.round(frontendNode.position.y);
        }
        
        return originalNode;
      });
      
      // 元数据更新
      updatedDefinition.metadata = updatedDefinition.metadata || {};
      updatedDefinition.metadata.chainName = this.flow.name || updatedDefinition.metadata.chainName;
      updatedDefinition.metadata.chainDesc = this.flow.description || updatedDefinition.metadata.chainDesc;
      
      // 前端渲染数据 - 存储在特殊字段中，不干扰原始结构
      updatedDefinition._frontendData = {
        nodes: this.flow.nodes.map(node => {
          // 创建节点的深拷贝，移除不需要的字段
          const cleanNode = JSON.parse(JSON.stringify(node));
          if (cleanNode.originalNode) delete cleanNode.originalNode;
          return cleanNode;
        }),
        connections: this.flow.connections.map(conn => {
          // 创建连接的深拷贝，移除不需要的字段
          const cleanConn = JSON.parse(JSON.stringify(conn));
          if (cleanConn.originalEdge) delete cleanConn.originalEdge;
          return cleanConn;
        }),
        scale: this.scale
      };
      
      return updatedDefinition;
    },

    // 处理执行
    async handlePreview() {
      try {
        this.$loading('正在执行执行...')
        if (!this.flow.name) {
          this.$message.warning('编排链唯一标识不能为空')
          return
        }

        const response = await this.$apiArrange.executeFlow(this.flow.name, {})
        this.previewResult = response
        this.previewVisible = true
      } catch (error) {
        this.$message.error('执行执行失败：' + (error.message || '未知错误'))
      } finally {
        this.$loading().close()
      }
    },

    // 发布流程
    async handleDeploy() {
      try {
        // 先保存
        await this.handleSave();

        // 确保编排链已启用
        if (this.flow.chain_id) {
          await this.$apiArrange.updateChain({
            id: this.flow.chain_id,
            enabled: true
          });

          this.$message.success('发布成功，编排链已启用');
        } else {
          this.$message.warning('发布失败，请先保存流程');
        }
      } catch (error) {
        this.$message.error('发布失败: ' + (error.message || '未知错误'));
      }
    },

    // 将前端流程数据转换为后端需要的格式
    convertFlowToDefinition() {
      // 检查是否已有chain_id
      const isUpdate = !!this.flow.chain_id;

      // 元数据信息 - 保持原有字段名
      const metadata = {
        application: 'extreme-flow', // 使用统一的应用名称
        chainName: this.flow.name || '未命名流程',
        chainDesc: this.flow.description || '无描述',
        chainId: isUpdate ? this.flow.chain_id : null
      }

      // 转换节点 - 使用原有字段名(nodeId, title等)
      const nodes = this.flow.nodes.map(node => {
        // 创建基础节点数据，保持原有字段名
        const nodeData = {
          nodeId: node.id,
          type: node.type,
          title: node.name || node.id,
          description: node.description || ''
        };

        // 如果有配置，添加配置
        if (node.config && Object.keys(node.config).length > 0) {
          nodeData.config = JSON.parse(JSON.stringify(node.config));
        }

        // 如果有输出映射，添加输出映射（使用原有字段名outputMapping）
        if (node.post_extract && Object.keys(node.post_extract).length > 0) {
          nodeData.outputMapping = JSON.parse(JSON.stringify(node.post_extract));
        }

        // 添加位置信息
        nodeData.position = {
          x: Math.round(node.position.x),
          y: Math.round(node.position.y)
        };
        
        nodeData.dimension = {
          width: 180,
          height: 80
        };

        return nodeData;
      });

      // 转换连线 - 保持原有字段名(sourceNodeId，targetNodeId)
      const edges = this.flow.connections.map(conn => {
        const edge = {
          sourceNodeId: conn.sourceId,
          targetNodeId: conn.targetId
        };

        // 如果有条件，添加条件
        if (conn.condition) {
          edge.condition = conn.condition;
        }
        
        // 如果有ID，添加ID
        if (conn.id) {
          edge.edgeId = conn.id;
        }

        return edge;
      });

      // 视口信息
      const viewport = {
        x: 0,
        y: 0,
        zoom: this.scale
      }

      // 准备canvas数据 - 以原始结构为基础，只添加前端渲染所需的数据
      const backendData = {
        metadata,
        nodes,
        edges
      };
      
      // 前端渲染数据 - 存储在特殊字段中
      const frontendData = {
        nodes: this.flow.nodes.map(node => {
          // 创建节点的深拷贝，移除不需要的字段
          const cleanNode = JSON.parse(JSON.stringify(node));
          if (cleanNode.originalNode) delete cleanNode.originalNode;
          return cleanNode;
        }),
        connections: this.flow.connections.map(conn => {
          // 创建连接的深拷贝，移除不需要的字段
          const cleanConn = JSON.parse(JSON.stringify(conn));
          if (cleanConn.originalEdge) delete cleanConn.originalEdge;
          return cleanConn;
        }),
        scale: this.scale
      };

      // 将前端数据添加到一个特殊字段，不干扰原有结构
      const canvasData = {
        ...backendData,
        _frontendData: frontendData
      };

      return {
        metadata,
        nodes,
        edges,
        viewport,
        canvas: JSON.stringify(canvasData)
      }
    },

    // 获取节点通过ID
    getNodeById(id) {
      return this.flow.nodes.find(node => node.id === id)
    },

    // 开始创建连线
    startConnection(event, node, type) {
      this.isCreatingConnection = true
      this.connectionSource = node
      this.connectionType = type

      const rect = this.$refs.canvas.getBoundingClientRect()
      this.mousePosition = {
        x: (event.clientX - rect.left) / this.scale,
        y: (event.clientY - rect.top) / this.scale
      }
    },

    // 处理鼠标移动
    handleMouseMove(event) {
      // 处理节点拖拽
      if (this.isDragging && this.dragNode) {
        event.preventDefault()
        const rect = this.$refs.canvas.getBoundingClientRect()
        const x = Math.max(0, (event.clientX - rect.left) / this.scale - this.dragOffset.x)
        const y = Math.max(0, (event.clientY - rect.top) / this.scale - this.dragOffset.y)

        // 使用Vue.set确保响应式更新而不触发布局重排
        this.$set(this.dragNode.position, 'x', x)
        this.$set(this.dragNode.position, 'y', y)
      }

      // 处理连线创建
      if (this.isCreatingConnection) {
        event.preventDefault()
        const rect = this.$refs.canvas.getBoundingClientRect()
        this.mousePosition = {
          x: (event.clientX - rect.left) / this.scale,
          y: (event.clientY - rect.top) / this.scale
        }
      }
    },

    // 处理鼠标抬起
    handleMouseUp(event) {
      // 结束节点拖拽
      if (this.isDragging) {
        this.isDragging = false
        this.dragNode = null
      }

      // 结束连线创建
      if (this.isCreatingConnection) {
        // 检查是否在节点端口上释放
        const targetElement = document.elementFromPoint(event.clientX, event.clientY)
        const portElement = targetElement?.closest('.port')

        if (portElement) {
          const nodeElement = portElement.closest('.node')
          if (nodeElement) {
            const nodeId = nodeElement.getAttribute('data-node-id')
            const targetNode = this.getNodeById(nodeId)

            if (targetNode && targetNode !== this.connectionSource) {
              // 创建连线
              if (
                  (this.connectionType === 'source' && portElement.classList.contains('port-in')) ||
                  (this.connectionType === 'target' && portElement.classList.contains('port-out'))
              ) {
                this.createConnection(
                    this.connectionType === 'source' ? this.connectionSource.id : targetNode.id,
                    this.connectionType === 'source' ? targetNode.id : this.connectionSource.id
                )
              }
            }
          }
        }

        this.isCreatingConnection = false
        this.connectionSource = null
        this.connectionType = null
      }
    },

    // 创建连线
    createConnection(sourceId, targetId) {
      // 检查是否已存在相同的连线
      const exists = this.flow.connections.some(
          conn => conn.sourceId === sourceId && conn.targetId === targetId
      )

      if (!exists) {
        const connection = {
          id: `conn_${Date.now()}`,
          sourceId,
          targetId
        }

        this.flow.connections.push(connection)
        this.selectConnection(connection)

        // 更新流程表达式
        this.updateFlowExpression()

        // 保存历史记录
        this.saveToHistory();
      }
    },

    // 选择连线
    selectConnection(connection, event) {
      event.stopPropagation()
      this.currentConnection = connection
      this.currentNode = null
    },

    // 双击连线打开配置
    openConnectionConfig(connection, event) {
      event.stopPropagation()
      this.currentConnection = connection
      this.currentNode = null
      this.configPanelVisible = true
    },

    // 取消所有选择
    deselectAll() {
      this.currentNode = null
      this.currentConnection = null
      this.currentConnectionNode = null
      this.hideContextMenu();
    },

    // 获取面板标题
    getPanelTitle() {
      if (this.currentNode) {
        return '节点配置'
      } else if (this.currentConnection) {
        return '连线配置'
      } else {
        return '流程配置'
      }
    },

    // 连线配置更新
    handleConnectionUpdate(connection) {
      const index = this.flow.connections.findIndex(c => c.id === connection.id)
      if (index > -1) {
        this.flow.connections.splice(index, 1, connection)
        this.updateFlowExpression()

        // 保存历史记录
        this.saveToHistory();
      }
    },

    // 删除连线
    handleConnectionDelete() {
      if (!this.currentConnection) return

      const index = this.flow.connections.findIndex(c => c.id === this.currentConnection.id)
      if (index > -1) {
        this.flow.connections.splice(index, 1)
        this.currentConnection = null
        this.updateFlowExpression()

        // 保存历史记录
        this.saveToHistory();
      }
    },

    // 更新流程表达式
    updateFlowExpression() {
      // 构建节点依赖图
      const graph = {}
      this.flow.nodes.forEach(node => {
        graph[node.id] = []
      })

      // 按优先级排序连线
      const sortedConnections = [...this.flow.connections].sort((a, b) => {
        return (b.priority || 1) - (a.priority || 1)
      })

      sortedConnections.forEach(conn => {
        if (graph[conn.sourceId]) {
          graph[conn.sourceId].push({
            id: conn.targetId,
            type: conn.type || 'default',
            condition: conn.condition,
            loopTimes: conn.loopTimes
          })
        }
      })

      // 查找起始节点(入度为0的节点)
      const startNodes = this.flow.nodes.filter(node => {
        return !this.flow.connections.some(conn => conn.targetId === node.id)
      })

      // 生成表达式
      if (startNodes.length > 0) {
        const expression = this.generateExpression(startNodes[0].id, graph)
        this.flow.flow = expression
      }
    },

    // 递归生成表达式
    generateExpression(nodeId, graph) {
      const children = graph[nodeId] || []

      if (children.length === 0) {
        return nodeId
      } else if (children.length === 1) {
        const child = children[0]

        // 根据连线类型生成不同的表达式
        if (child.type === 'condition') {
          return `IF(${child.condition || 'true'}, THEN(${nodeId}, ${this.generateExpression(child.id, graph)}))`
        } else if (child.type === 'loop') {
          return `THEN(${nodeId}, FOR(${child.loopTimes || 1}).DO(${this.generateExpression(child.id, graph)}))`
        } else {
          return `THEN(${nodeId}, ${this.generateExpression(child.id, graph)})`
        }
      } else {
        // 多个子节点,检查是否有条件连线
        const conditionNodes = children.filter(child => child.type === 'CONDITION_NODE')

        if (conditionNodes.length > 0) {
          // 如果有条件连线,构建SWITCH表达式
          const switchCases = conditionNodes.map(child => {
            return `${child.condition || 'true'}, ${this.generateExpression(child.id, graph)}`
          }).join(', ')

          return `THEN(${nodeId}, SWITCH(${switchCases}))`
        } else {
          // 否则构建并行表达式
          const childExpressions = children.map(child => this.generateExpression(child.id, graph))
          return `THEN(${nodeId}, WHEN(${childExpressions.join(', ')}))`
        }
      }
    },

    // 删除选中元素
    handleDelete() {
      if (this.currentNode) {
        // 检查是否是开始或结束节点
        if (this.currentNode.type === 'START_NODE' || this.currentNode.type === 'END_NODE') {
          this.$message.warning(`不能删除${this.currentNode.type === 'START_NODE' ? '开始' : '结束'}节点`);
          return;
        }

        // 删除节点
        const nodeIndex = this.flow.nodes.findIndex(n => n.id === this.currentNode.id)
        if (nodeIndex > -1) {
          // 删除与该节点相关的连线
          this.flow.connections = this.flow.connections.filter(conn =>
              conn.sourceId !== this.currentNode.id && conn.targetId !== this.currentNode.id
          )

          this.flow.nodes.splice(nodeIndex, 1)
          this.currentNode = null
          this.updateFlowExpression()

          // 保存历史记录
          this.saveToHistory();
        }
      } else if (this.currentConnection) {
        // 删除连线
        this.handleConnectionDelete()
      }
    },

    // 处理鼠标滚轮缩放
    handleWheel(event) {
      // 按住Ctrl键时才进行缩放
      if (event.ctrlKey || event.metaKey) {
        const delta = event.deltaY > 0 ? -this.scaleStep : this.scaleStep
        this.adjustScale(delta)
      } else {
        // 正常滚动画布
        this.$refs.canvas.scrollTop += event.deltaY
        this.$refs.canvas.scrollLeft += event.deltaX
      }
    },

    // 放大
    zoomIn() {
      this.adjustScale(this.scaleStep)
    },

    // 缩小
    zoomOut() {
      this.adjustScale(-this.scaleStep)
    },

    // 重置缩放
    resetZoom() {
      this.scale = 1
    },

    // 调整缩放比例
    adjustScale(delta) {
      const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.scale + delta))
      this.scale = newScale
    },

    // 切换迷你地图显示
    toggleMinimap() {
      this.showMinimap = !this.showMinimap
    },

    // 导航到指定节点
    navigateToNode(node) {
      const canvasEl = this.$refs.canvas
      if (!canvasEl) return

      // 计算节点在当前缩放比例下的位置
      const nodeX = node.position.x * this.scale
      const nodeY = node.position.y * this.scale

      // 计算画布的可视区域大小
      const canvasRect = canvasEl.getBoundingClientRect()

      // 计算滚动位置,使节点居中显示
      const scrollLeft = nodeX - canvasRect.width / 2 + 90 // 90是节点宽度的一半
      const scrollTop = nodeY - canvasRect.height / 2 + 40 // 40是节点高度的一半

      // 平滑滚动到节点位置
      canvasEl.scrollTo({
        left: Math.max(0, scrollLeft),
        top: Math.max(0, scrollTop),
        behavior: 'smooth'
      })
    },

    // 获取节点颜色
    getNodeColor(type) {
      const colors = {
        'http': '#409eff',
        'script': '#67c23a',
        'switch': '#e6a23c',
        'if': '#e6a23c',
        'for': '#909399',
        'while': '#909399',
        'break': '#f56c6c'
      }
      return colors[type] || '#409eff'
    },

    // 切换节点面板
    toggleNodePanel() {
      this.nodePanelCollapsed = !this.nodePanelCollapsed
    },

    // 切换配置面板
    toggleConfigPanel() {
      this.configPanelVisible = !this.configPanelVisible
    },

    // 开始画布拖动
    startCanvasDrag(event) {
      // 如果点击的是节点或端口等元素，不启动画布拖动
      if (event.target !== event.currentTarget &&
          !event.target.classList.contains('canvas-content')) {
        return;
      }

      // 设置鼠标样式
      document.body.style.cursor = 'grabbing';

      this.isCanvasDragging = true;
      this.canvasDragStart = {
        x: event.clientX,
        y: event.clientY
      };

      const canvas = this.$refs.canvas;
      this.canvasScrollPosition = {
        x: canvas.scrollLeft,
        y: canvas.scrollTop
      };

      // 添加鼠标移动和松开事件监听
      document.addEventListener('mousemove', this.dragCanvas);
      document.addEventListener('mouseup', this.stopCanvasDrag);
    },

    // 拖动画布
    dragCanvas(event) {
      if (!this.isCanvasDragging) return;

      event.preventDefault();

      const deltaX = this.canvasDragStart.x - event.clientX;
      const deltaY = this.canvasDragStart.y - event.clientY;

      const canvas = this.$refs.canvas;
      canvas.scrollLeft = this.canvasScrollPosition.x + deltaX;
      canvas.scrollTop = this.canvasScrollPosition.y + deltaY;
    },

    // 停止画布拖动
    stopCanvasDrag() {
      this.isCanvasDragging = false;
      document.body.style.cursor = '';

      // 移除事件监听
      document.removeEventListener('mousemove', this.dragCanvas);
      document.removeEventListener('mouseup', this.stopCanvasDrag);
    },

    // 切换子分组展开状态
    toggleSubGroup(groupId, subGroupId) {
      const group = this.nodeGroups.find(g => g.id === groupId);
      if (group && group.expanded) {
        this.$set(group.expanded, subGroupId, !group.expanded[subGroupId]);
      }
    },

    // 已删除未使用的getApiDescription方法

    // 显示右键菜单
    showContextMenu(event, target, type) {
      event.preventDefault()
      event.stopPropagation()

      // 设置右键菜单位置和目标
      this.contextMenu.x = event.clientX
      this.contextMenu.y = event.clientY
      this.contextMenu.visible = true
      this.contextMenu.target = target
      this.contextMenu.type = type

      // 选中当前目标
      if (type === 'node') {
        this.selectNode(target, event)
      } else if (type === 'connection') {
        this.selectConnection(target, event)
      }

      // 添加一次性事件监听器，点击其他地方关闭菜单
      setTimeout(() => {
        document.addEventListener('click', this.hideContextMenu, { once: true })
      }, 0)
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenu.visible = false
    },

    // 从右键菜单打开配置
    openConfigFromMenu() {
      if (this.contextMenu.type === 'node') {
        this.openNodeConfig(this.contextMenu.target, new Event('click'))
      } else if (this.contextMenu.type === 'connection') {
        this.openConnectionConfig(this.contextMenu.target, new Event('click'))
      }
      this.hideContextMenu()
    },

    // 从右键菜单删除元素
    deleteFromMenu() {
      if (this.contextMenu.type === 'node') {
        this.currentNode = this.contextMenu.target;
        // 检查是否是开始或结束节点
        if (this.currentNode.type === 'START_NODE' || this.currentNode.type === 'END_NODE') {
          this.$message.warning(`不能删除${this.currentNode.type === 'START_NODE' ? '开始' : '结束'}节点`);
        } else {
          this.handleDelete();
        }
      } else if (this.contextMenu.type === 'connection') {
        this.currentConnection = this.contextMenu.target;
        this.handleConnectionDelete();
      }
      this.hideContextMenu();
    },

    // 处理键盘事件
    handleKeyDown(event) {
      // 如果按下Backspace或Delete键，且当前有选中的节点或连线
      if ((event.key === 'Backspace' || event.key === 'Delete') &&
          (this.currentNode || this.currentConnection)) {
        // 如果是输入框、文本区域等元素，不处理删除
        if (event.target.tagName === 'INPUT' ||
            event.target.tagName === 'TEXTAREA' ||
            event.target.isContentEditable) {
          return;
        }

        // 阻止浏览器默认行为（如返回上一页）
        event.preventDefault();

        // 检查是否是开始或结束节点
        if (this.currentNode && (this.currentNode.type === 'START_NODE' || this.currentNode.type === 'END_NODE')) {
          this.$message.warning(`不能删除${this.currentNode.type === 'START_NODE' ? '开始' : '结束'}节点`);
          return;
        }

        // 执行删除操作
        this.handleDelete();
      }

      // 撤销操作 (Ctrl+Z)
      if (event.ctrlKey && event.key.toLowerCase() === 'z' && !event.shiftKey) {
        event.preventDefault();
        this.undo();
      }

      // 重做操作 (Ctrl+Y 或 Ctrl+Shift+Z)
      if ((event.ctrlKey && event.key.toLowerCase() === 'y') ||
          (event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'z')) {
        event.preventDefault();
        this.redo();
      }
    },

    // 保存当前状态到历史记录
    saveToHistory() {
      // 如果是撤销/重做操作中，不保存历史
      if (this.isUndoRedoing) {
        this.isUndoRedoing = false;
        return;
      }

      // 创建当前状态的深拷贝
      const currentState = {
        nodes: JSON.parse(JSON.stringify(this.flow.nodes)),
        connections: JSON.parse(JSON.stringify(this.flow.connections)),
        flow: this.flow.flow
      };

      // 如果当前不在历史记录的最后，删除当前位置之后的历史
      if (this.historyIndex < this.history.length - 1) {
        this.history = this.history.slice(0, this.historyIndex + 1);
      }

      // 添加新的历史记录
      this.history.push(currentState);

      // 如果历史记录超过最大长度，删除最早的记录
      if (this.history.length > this.maxHistoryLength) {
        this.history.shift();
      }

      // 更新历史索引
      this.historyIndex = this.history.length - 1;
    },

    // 撤销操作
    undo() {
      if (this.historyIndex > 0) {
        this.historyIndex--;
        this.restoreFromHistory();
      } else {
        this.$message.info('已经是最早的操作记录');
      }
    },

    // 重做操作
    redo() {
      if (this.historyIndex < this.history.length - 1) {
        this.historyIndex++;
        this.restoreFromHistory();
      } else {
        this.$message.info('已经是最新的操作记录');
      }
    },

    // 从历史记录恢复状态
    restoreFromHistory() {
      this.isUndoRedoing = true;
      const state = this.history[this.historyIndex];

      if (state) {
        this.flow.nodes = JSON.parse(JSON.stringify(state.nodes));
        this.flow.connections = JSON.parse(JSON.stringify(state.connections));
        this.flow.flow = state.flow;

        // 重置选中状态
        this.currentNode = null;
        this.currentConnection = null;
      }
    },

    // 加载编排链数据
    async loadChainData(chainId) {
      try {
        if (!chainId || chainId === 'undefined' || chainId === 'null') {
          this.$message.error('编排链唯一标识不能为空');
          this.createNewFlow();
          return;
        }

        this.$message.info('正在加载编排链数据...');
        console.log('开始加载编排链数据，chainId:', chainId);
        
        // 调用API获取编排链详情
        const chainData = await this.$apiArrange.getChainDetail(chainId);
        console.log('获取到的编排链数据:', chainData);

        if (!chainData) {
          this.$message.error('获取编排链数据失败');
          this.createNewFlow();
          return;
        }

        // 更新流程基本信息
        this.flow.chain_id = chainData.id;
        this.flow.name = chainData.chainName || '';
        this.flow.description = chainData.chainDesc || '';
        this.flow.flow = chainData.elData || '';

        // 如果有canvas数据，解析并使用
        if (chainData.canvas) {
          try {
            console.log('解析canvas数据:', chainData.canvas);
            let canvasData = JSON.parse(chainData.canvas);
            
            // 检查前端数据是否存在
            if (canvasData._frontendData) {
              console.log('检测到前端渲染数据，直接使用...');
              this.flow.nodes = canvasData._frontendData.nodes;
              this.flow.connections = canvasData._frontendData.connections;
              
              // 设置缩放比例
              if (canvasData._frontendData.scale) {
                this.scale = canvasData._frontendData.scale;
              }
              
              // 保存原始定义，供后续保存时使用
              // 移除前端渲染数据，避免循环引用和数据污染
              delete canvasData._frontendData;
              this.flow.originalDefinition = canvasData;
              
              // 成功加载提示
              this.$message.success('成功加载编排链数据');
              return;
            }
            // 检查是否为后端格式（包含metadata、nodes和edges）
            else if (canvasData.metadata && canvasData.nodes && canvasData.edges) {
              console.log('检测到后端流程定义格式，进行转换...');
              // 保存原始定义，供后续保存时使用
              this.flow.originalDefinition = JSON.parse(JSON.stringify(canvasData));
              
              // 转换为前端格式
              const frontendData = this.convertFlowDefinitionToCanvas(canvasData);
              this.flow.nodes = frontendData.nodes;
              this.flow.connections = frontendData.connections;
              this.scale = frontendData.scale || 1;
              
              // 成功加载提示
              this.$message.success('成功加载编排链数据');
              return;
            }
            
            if (canvasData.nodes && canvasData.nodes.length > 0) {
              this.flow.nodes = canvasData.nodes;
              this.flow.connections = canvasData.connections || [];

              // 设置缩放比例(如果有保存)
              if (canvasData.scale) {
                this.scale = canvasData.scale;
              }
              
              // 保存原始数据格式以供后续使用
              this.flow.originalDefinition = {
                metadata: {
                  application: "extreme-flow",
                  chainName: this.flow.name,
                  chainDesc: this.flow.description
                },
                nodes: [],
                edges: []
              };

              // 成功加载提示
              this.$message.success('成功加载编排链数据');
              return;
            } else {
              console.warn('canvas数据中没有节点信息');
            }
          } catch (e) {
            console.error('解析canvas数据出错', e);
          }
        } else {
          console.warn('chainData中没有canvas数据');
        }

        // 如果没有canvas数据或解析失败，但有elData，可以尝试自动生成布局
        // 这里暂时创建一个空流程，后续可以增加从elData自动生成布局的功能
        this.$message.info('编排链没有可视化数据，创建新布局');

        // 保留现有的chain_id、name和description
        const existingChainId = this.flow.chain_id;
        const name = this.flow.name;
        const description = this.flow.description;

        this.createNewFlow();

        // 恢复之前的chain_id、name和description
        this.flow.chain_id = existingChainId;
        this.flow.name = name;
        this.flow.description = description;

      } catch (error) {
        console.error('加载编排链数据失败:', error);
        this.$message.error('加载编排链数据失败: ' + (error.message || '未知错误'));
        this.createNewFlow();
      }
    },

    // 将后端流程定义格式转换为前端画布格式
    convertFlowDefinitionToCanvas(flowDefinition) {
      console.log('开始转换流程定义为画布格式:', flowDefinition);
      
      // 创建原始定义的深拷贝，避免修改原始数据
      const flowDefinitionCopy = JSON.parse(JSON.stringify(flowDefinition));
      
      // 默认画布尺寸和节点间距
      const nodeSpacingX = 200;
      const nodeSpacingY = 150;
      const startX = 100;
      const startY = 100;
      
      // 简化节点类型映射，不再需要复杂映射
      const typeMapping = {
        // 只保留基本兼容
        'start': 'START_NODE',
        'end': 'END_NODE'
      };
      
      // 创建节点位置布局
      // 首先构建节点依赖关系图（用于计算层级）
      const dependencyGraph = {};
      const inDegree = {}; // 入度
      
      // 初始化
      flowDefinitionCopy.nodes.forEach(node => {
        dependencyGraph[node.nodeId] = [];
        inDegree[node.nodeId] = 0;
      });
      
      // 建立依赖关系
      flowDefinitionCopy.edges.forEach(edge => {
        if (dependencyGraph[edge.sourceNodeId]) {
          dependencyGraph[edge.sourceNodeId].push(edge.targetNodeId);
        }
        inDegree[edge.targetNodeId] = (inDegree[edge.targetNodeId] || 0) + 1;
      });
      
      // 拓扑排序计算节点层级
      const levels = {};
      const queue = [];
      
      // 找出入度为0的节点（起始节点）
      Object.keys(inDegree).forEach(nodeId => {
        if (inDegree[nodeId] === 0) {
          queue.push(nodeId);
          levels[nodeId] = 0;
        }
      });
      
      // 广度优先遍历计算层级
      while (queue.length > 0) {
        const nodeId = queue.shift();
        const level = levels[nodeId];
        
        if (dependencyGraph[nodeId]) {
          dependencyGraph[nodeId].forEach(nextNodeId => {
            inDegree[nextNodeId]--;
            
            if (inDegree[nextNodeId] === 0) {
              queue.push(nextNodeId);
              levels[nextNodeId] = level + 1;
            }
          });
        }
      }
      
      // 计算每个层级的节点数量
      const levelCounts = {};
      Object.values(levels).forEach(level => {
        levelCounts[level] = (levelCounts[level] || 0) + 1;
      });
      
      // 计算每个层级当前放置的节点索引
      const levelCurrentIndex = {};
      
      // 转换节点，加入位置信息
      const nodes = flowDefinitionCopy.nodes.map(node => {
        const level = levels[node.nodeId] || 0;
        const levelNodeCount = levelCounts[level] || 1;
        
        // 计算当前节点在该层的索引
        levelCurrentIndex[level] = levelCurrentIndex[level] || 0;
        const indexInLevel = levelCurrentIndex[level]++;
        
        // 确定位置信息
        let positionX, positionY;
        if (node.position) {
          // 使用原有的位置信息（如果存在）
          positionX = node.position.x;
          positionY = node.position.y;
        } else {
          // 计算 x, y 坐标（水平布局，同层次节点垂直分布）
          positionX = startX + level * nodeSpacingX;
          positionY = startY + (indexInLevel * nodeSpacingY * 2 / levelNodeCount);
        }
        
        // 确定节点类型
        const type = typeMapping[node.type] || node.type;
        
        // 处理配置，根据节点类型设置合适的默认值
        const config = {};
        if (node.config) {
          // 深拷贝配置，避免修改原始数据
          Object.assign(config, node.config);
        }
        
        // 处理特定类型节点的配置
        if (node.type === 'HTTP_NODE' && (!node.config || Object.keys(node.config).length === 0)) {
          config.method = 'GET';
          config.url = '';
          config.params = {};
          config.headers = {};
          config.body = {};
        }
        
        // 提取输出映射
        const post_extract = {};
        if (node.outputMapping) {
          Object.assign(post_extract, node.outputMapping);
        }
        
        // 转换成前端需要的节点格式
        return {
          id: node.nodeId,
          name: node.title || node.nodeId,
          type: type,
          description: node.description || '',
          position: { x: positionX, y: positionY },
          config: config,
          post_extract: post_extract
        };
      });
      
      // 转换连线
      const connections = flowDefinitionCopy.edges.map((edge, index) => {
        // 使用原始的edgeId（如果存在）或者生成新的ID
        const id = edge.edgeId || `conn_${index + 1}`;
        
        return {
          id: id,
          sourceId: edge.sourceNodeId,
          targetId: edge.targetNodeId,
          type: edge.condition ? 'condition' : 'default',
          condition: edge.condition || '',
          priority: edge.priority || 1
        };
      });
      
      console.log('转换后的画布数据:', { nodes, connections });
      
      // 返回转换后的画布数据，不包含originalDefinition，在后续存储时再添加
      return {
        nodes,
        connections,
        scale: 1
      };
    },
    
    // 更新所有连接节点的位置
    updateConnectionNodesPositions() {
      // 遍历所有连接节点
      this.flow.connectionNodes.forEach(connNode => {
        const sourceNode = this.getNodeById(connNode.sourceId);
        const targetNode = this.getNodeById(connNode.targetId);

        if (sourceNode && targetNode) {
          // 获取画布中心点
          const canvasEl = this.$refs.canvas;
          const canvasWidth = canvasEl ? canvasEl.clientWidth : 1000;
          const centerX = canvasWidth / 2;

          // 更新位置
          this.$set(connNode.position, 'x', centerX);
          this.$set(connNode.position, 'y', (sourceNode.position.y + targetNode.position.y) / 2);
        }
      });
    },

    // 处理编排链唯一标识输入
    handleChainNameInput(value) {
      // 移除所有空格
      const newValue = value.replace(/\s/g, '');
      if (newValue !== value) {
        this.$message.warning('编排链唯一标识不能包含空格');
        this.flow.name = newValue;
      }
    },

    // 打开专家模式
    async openExpertMode(node, event) {
      event.stopPropagation();
      
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在获取脚本信息...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      });
      
      try {
        // 使用节点ID作为scriptId获取脚本详情
        const scriptId = node.id;
        const scriptInfo = await getRuleScriptByScriptId(scriptId);
        
        if (!scriptInfo || !scriptInfo.id) {
          this.$message.warning('未找到对应的脚本信息，请确认节点配置正确');
          loading.close();
          return;
        }
        
        // 跳转到脚本编辑页面，使用获取到的id
        this.$router.push({
          path: `/rule/script/edit/${scriptInfo.id}`,
          query: { fromArrange: 'true' }
        });
      } catch (error) {
        console.error('获取脚本信息失败:', error);
        this.$message.error('获取脚本信息失败: ' + (error.message || '未知错误'));
      } finally {
        loading.close();
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.api-arrange-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }

    .chain-info {
      display: flex;
      align-items: center;
      gap: 20px;
      background-color: #f8faff;
      padding: 10px 16px;
      border-radius: 8px;
      border: 1px solid #e6effd;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
      margin: 0 20px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #1a1f36;
          font-size: 14px;
          font-weight: 500;
          margin-right: 8px;
          white-space: nowrap;
        }

        .chain-input {
          width: 220px;

          ::v-deep .el-input__inner {
            height: 32px;
            line-height: 32px;
            border-radius: 4px;
            background-color: #fff;
            border: 1px solid #e0e5ee;
            transition: all 0.2s;

            &:hover, &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }

            &:disabled {
              background-color: #f5f7fa;
              color: #606266;
              cursor: not-allowed;
            }
          }
        }
      }
    }

    .header-tools {
      display: flex;
      align-items: center;

      .tool-buttons {
        display: flex;
        align-items: center;
        gap: 4px; // 减小小按钮之间的间距
        margin-right: 12px; // 按钮组之间的间距

        &:last-of-type {
          margin-right: 0;
        }
      }

      .zoom-display {
        min-width: 40px;
        text-align: center;
        font-size: 14px;
        color: #606266;
        font-weight: 500;
        margin: 0 2px;
      }

      .el-button {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
      }

      .el-divider {
        height: 20px;
        margin: 0 16px; // 增加分隔线两侧的间距
      }

      .main-buttons {
        margin-left: 8px; // 主按钮组左侧间距
        display: flex;
        gap: 10px; // 主按钮之间的间距

        .el-button {
          padding: 8px 20px; // 增加主按钮的内边距
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 8px;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          height: 36px;
          font-size: 14px;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .api-arrange {
    display: flex;
    height: calc(100% - 60px);
    background-color: #f8f9fa;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);

    .node-panel {
      width: 220px;
      background-color: #fff;
      border-right: 1px solid #e6e9ef;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      transition: width 0.3s ease;

      &.collapsed {
        width: 50px;

        .panel-title {
          justify-content: center;
          padding: 16px 0;

          span {
            display: none;
          }
        }
      }

      .panel-title {
        padding: 16px;
        font-weight: 600;
        font-size: 16px;
        border-bottom: 1px solid #e6e9ef;
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .node-list {
        padding: 16px;
        flex: 1;
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        .node-group {
          margin-bottom: 20px;

          .group-title {
            margin-bottom: 12px;

            span {
              font-size: 14px;
              font-weight: 600;
              color: #606266;
            }

            .el-divider {
              margin: 8px 0;
            }
          }

          // 节点网格布局
          .node-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
          }

          // 二级分组样式
          .sub-group {
            margin-bottom: 10px;

            .sub-group-header {
              display: flex;
              align-items: center;
              padding: 8px 10px;
              border-radius: 4px;
              background-color: #f5f7fa;
              cursor: pointer;
              transition: all 0.2s;

              &:hover {
                background-color: #ecf5ff;
              }

              i {
                margin-right: 8px;
                font-size: 16px;
                color: #606266;

                &.toggle-icon {
                  margin-left: auto;
                  margin-right: 0;
                  font-size: 12px;
                }
              }
            }

            .sub-group-apis {
              padding-left: 12px;
              margin-top: 6px;

              .api-item {
                display: flex;
                align-items: center;
                padding: 6px 10px;
                margin-bottom: 6px;
                border-radius: 4px;
                background-color: #fff;
                border: 1px dashed #dcdfe6;
                cursor: move;
                transition: all 0.2s;
                font-size: 12px;

                &:hover {
                  border-color: #c6e2ff;
                  background-color: #f0f9ff;
                  transform: translateY(-2px);
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }

                &:before {
                  content: '';
                  display: inline-block;
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  background-color: #409eff;
                  margin-right: 8px;
                }
              }
            }
          }
        }

        .node-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: calc(50% - 4px); // 一排两个，减去间隙的一半
          padding: 8px 6px;
          border: 1px solid #e6e9ef;
          border-radius: 6px;
          cursor: move;
          transition: all 0.2s;
          text-align: center;

          i {
            font-size: 18px;
            color: #606266;
            margin-bottom: 4px;
          }

          span {
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
          }

          &:hover {
            background-color: #f5f7fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .canvas-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;

      .canvas {
        flex: 1;
        position: relative;
        background-color: #f8f9fa;
        background-image:
            linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
        background-size: 20px 20px;
        overflow: auto;
        min-height: 0;
        cursor: grab;

        &.canvas-dragging {
          cursor: grabbing;
        }

        .canvas-content {
          position: absolute;
          transition: transform 0.1s ease;
          width: v-bind('canvasMinSize.width');
          height: v-bind('canvasMinSize.height');
          transform-origin: 0 0;
        }

        // 迷你地图 - 固定位置
        .minimap {
          position: absolute;
          bottom: 20px;
          right: 20px;
          width: 200px;
          height: 150px;
          background-color: rgba(255, 255, 255, 0.9);
          border: 1px solid #e6e9ef;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 100;
          overflow: hidden;
          display: flex;
          flex-direction: column;

          &.fixed-position {
            position: fixed;
            bottom: 60px;
            right: 60px;
            top: auto;
          }

          .minimap-title {
            padding: 5px 10px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #e6e9ef;
            display: flex;
            justify-content: space-between;
            align-items: center;

            span {
              font-size: 12px;
              font-weight: 500;
              color: #606266;
            }
          }

          .minimap-content {
            flex: 1;
            position: relative;
            overflow: hidden;

            .minimap-node {
              position: absolute;
              width: 18px;
              height: 8px;
              border-radius: 2px;
              cursor: pointer;
              transition: transform 0.2s;

              &:hover {
                transform: scale(1.5);
                z-index: 2;
              }
            }

            .minimap-viewport {
              position: absolute;
              border: 2px solid #409eff;
              background-color: rgba(64, 158, 255, 0.1);
              pointer-events: none;
            }
          }
        }

        // 迷你地图切换按钮 - 固定位置
        .minimap-toggle {
          position: absolute;
          bottom: 20px;
          right: 20px;
          z-index: 100;

          &.fixed-position {
            position: fixed;
            bottom: 40px;
            right: 40px;
            top: auto;
          }

          .el-button {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
          }
        }

        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        .temp-connection {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          z-index: 1;
        }

        .node {
          position: absolute;
          width: 180px;
          background-color: #fff;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          cursor: move;
          user-select: none;
          transition: box-shadow 0.2s;
          z-index: 2;
          // 移除溢出隐藏，避免遮挡设置按钮和连接点
          // overflow: hidden;

          &[data-node-id] {
            // 确保节点ID属性存在
          }

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

            .node-actions,
            .node-quick-add {
              opacity: 1;
            }
          }

          &.node-selected {
            box-shadow: 0 0 0 2px #409eff, 0 4px 12px rgba(0, 0, 0, 0.15);

            .node-actions,
            .node-quick-add {
              opacity: 1;
            }

            &:after {
              content: '';
              position: absolute;
              top: -5px;
              right: -5px;
              width: 10px;
              height: 10px;
              border-radius: 50%;
              background-color: #409eff;
              border: 2px solid #fff;
            }
          }

          // 开始和结束节点特殊样式 - 只保留颜色差异
          &.node-start .node-header {
            background: linear-gradient(45deg, #13CE66, #36D57D);
          }

          &.node-end .node-header {
            background: linear-gradient(45deg, #FF4949, #FF7849);
          }
          
          // 使用后端节点类型的样式
          &.node-START_NODE .node-header {
            background: linear-gradient(45deg, #13CE66, #36D57D);
          }

          &.node-END_NODE .node-header {
            background: linear-gradient(45deg, #FF4949, #FF7849);
          }

          // 内部元素圆角
          .node-header {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            color: #fff;
            font-weight: 500;

            i {
              margin-right: 8px;
            }

            // 旧版类型样式保留向后兼容
            &.node-type-http {
              background: linear-gradient(45deg, #409eff, #53a8ff);
            }

            &.node-type-script {
              background: linear-gradient(45deg, #67c23a, #85ce61);
            }

            // 后端节点类型样式
            &.node-type-HTTP_NODE {
              background: linear-gradient(45deg, #409eff, #53a8ff);
            }

            &.node-type-SCRIPT_NODE {
              background: linear-gradient(45deg, #67c23a, #85ce61);
            }

            &.node-type-SWITCH_NODE,
            &.node-type-CONDITION_NODE {
              background: linear-gradient(45deg, #e6a23c, #f3b760);
            }

            &.node-type-FOR_NODE,
            &.node-type-WHILE_NODE,
            &.node-type-LOOP_NODE {
              background: linear-gradient(45deg, #909399, #a6a9ad);
            }

            &.node-type-START_NODE {
              background: linear-gradient(45deg, #13CE66, #36D57D);
            }

            &.node-type-END_NODE {
              background: linear-gradient(45deg, #FF4949, #FF7849);
            }

            &.node-type-PARALLEL_NODE {
              background: linear-gradient(45deg, #9B59B6, #B07CC6);
            }

            &.node-type-COMMON_NODE {
              background: linear-gradient(45deg, #34495E, #587593);
            }

            &.node-type-DATA_PROCESS_NODE {
              background: linear-gradient(45deg, #1ABC9C, #48CFAD);
            }
            
            &.node-type-AND_NODE {
              background: linear-gradient(45deg, #3498DB, #5DADE2);
            }
            
            &.node-type-OR_NODE {
              background: linear-gradient(45deg, #F39C12, #F7B32B);
            }
            
            &.node-type-NOT_NODE {
              background: linear-gradient(45deg, #D35400, #E67E22);
            }

            /* 已简化集成应用API节点样式 */
          }

          .node-body {
            background-color: #fff; // 统一背景色
            border-bottom-left-radius: 6px;
            border-bottom-right-radius: 6px;
            padding: 12px;

            .node-type {
              font-size: 12px;
              color: #909399;
              margin-bottom: 4px;
            }

            .node-config {
              font-size: 13px;
              word-break: break-all;
            }
          }

          // 节点操作按钮
          .node-actions {
            position: absolute;
            top: -10px;
            right: -10px;
            z-index: 3;
            opacity: 0;
            transition: opacity 0.2s;
            display: flex;
            gap: 5px;

            .node-config-btn,
            .node-expert-btn {
              width: 24px;
              height: 24px;
              padding: 0;
              border: 2px solid #fff;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);

              i {
                font-size: 12px;
              }

              &:hover {
                transform: scale(1.1);
              }
            }
            
            .node-expert-btn {
              background-color: #e6a23c;
              border-color: #e6a23c;
              
              &:hover {
                background-color: #ebb563;
                border-color: #ebb563;
              }
            }
          }

          // 快捷添加按钮
          .node-quick-add {
            position: absolute;
            top: 50%;
            right: -30px;
            transform: translateY(-50%);
            z-index: 3;
            opacity: 0;
            transition: opacity 0.2s;

            .quick-add-btn {
              width: 24px;
              height: 24px;
              padding: 0;
              border: 2px solid #fff;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);

              i {
                font-size: 12px;
              }

              &:hover {
                transform: scale(1.1);
              }
            }
          }

          // 删除提示，不再需要
          &:after {
            display: none;
          }

          .node-ports {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;

            .port {
              position: absolute;
              width: 12px;
              height: 12px;
              background-color: #fff;
              border: 2px solid #409eff;
              border-radius: 50%;
              pointer-events: auto;
              cursor: crosshair;
              z-index: 3;
              transition: transform 0.2s, box-shadow 0.2s;

              &:hover {
                transform: scale(1.2);
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
              }

              &.port-in {
                top: 50%;
                left: -6px;
                transform: translateY(-50%);
              }

              &.port-out {
                top: 50%;
                right: -6px;
                transform: translateY(-50%);

                &:hover {
                  transform: translateY(-50%) scale(1.2);
                }
              }
            }
          }
        }
      }
    }
  }
}

// 抽屉式配置面板样式
::v-deep .config-drawer {
  background-color: #fff;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);

  .el-drawer__header {
    padding: 16px;
    margin-bottom: 0;
    font-weight: 600;
    font-size: 16px;
    color: #1a1f36;
    border-bottom: 1px solid #e6e9ef;

    .drawer-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .el-drawer__body {
    padding: 0;
    overflow-y: auto;

    .config-content {
      padding: 16px;
    }
  }
}

// 右键菜单样式
.context-menu {
  position: fixed;
  z-index: 1000;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-width: 120px;
  padding: 5px 0;

  .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;

    &:hover {
      background-color: #f5f7fa;
    }

    i {
      margin-right: 8px;
      font-size: 14px;
      color: #606266;
    }

    span {
      font-size: 14px;
      color: #303133;
    }
  }
}

// 配置面板按钮
.config-panel-button {
  position: fixed;
  right: 20px;
  bottom: 120px;
  z-index: 100;

  .el-button {
    width: 48px;
    height: 48px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.15);

    i {
      font-size: 20px;
    }

    &:hover {
      transform: scale(1.1);
    }
  }
}

// 快捷添加菜单样式
.quick-add-menu {
  position: absolute;
  z-index: 1000;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 300px;
  max-height: 400px;
  overflow-y: auto;
  transform-origin: left center;
  transform: translateY(-50%); // 垂直居中

  .menu-title {
    padding: 10px 16px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
  }

  .menu-items {
    padding: 8px;
  }

  .menu-group {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .group-title {
      padding: 4px 8px;
      font-size: 12px;
      color: #909399;
      font-weight: 600;
    }

    .group-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    width: calc(50% - 4px);
    background-color: #f5f7fa;

    &:hover {
      background-color: #ecf5ff;
      transform: translateY(-2px);
    }

    i {
      margin-right: 8px;
      font-size: 14px;
      color: #409eff;
    }

    span {
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 已删除应用信息标签样式

// 预览抽屉样式
::v-deep .preview-drawer {
  .el-drawer__header {
    padding: 16px 20px;
    margin-bottom: 0;
    background-color: #f8faff;
    border-bottom: 1px solid #e6effd;
    
    .drawer-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;
      
      &:before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 2px;
        margin-right: 10px;
      }
    }
  }
  
  .el-drawer__body {
    padding: 0;
    height: calc(100% - 57px); // 减去header高度
    overflow: hidden;
  }
}
</style>
