<template>
    <div v-if="connectors?.length > 0">
        <div class="divider">
            <span>快捷登录</span>
        </div>
        <div class="quick-btns">
            <el-tooltip v-for="(connector, index) in connectors" :key="index" :content="connector.name" placement="top">
                <el-button circle :icon="connector.icon" :style="{ color: connector.iconColor }"
                    @click="handleConnectorLogin(connector)">
                </el-button>
            </el-tooltip>
        </div>
    </div>
</template>
<script>
import { getPublicEnabledConnectors, doAuth } from '../../api/auth'
import LoginMixin from "../LoginMixin.js";

export default {
    mixins: [LoginMixin],
    data() {
        return {
            connectors: []
        }
    },
    mounted() {
        getPublicEnabledConnectors().then((res) => {
            this.connectors = res?.records?.filter(connector =>
                connector.enabled === true && connector.displayOnLoginPage === true
            ) || []
        }).catch(() => {
            this.connectors = []
        })
    },
    methods: {
        async handleConnectorLogin(connector) {
            if (!this.checkAgreement()) {
                return
            }
            if (connector.qrCodeEnabled) {
                this.$emit('goQrCodeUI', connector)
            } else {
                // 执行第三方认证跳转
                try {
                    const data = await doAuth(connector.id, `${window.location.origin}${process.env.BASE_URL}`);
                    if (data) {
                        // 如果返回URL，则进行重定向
                        window.location.href = data;
                    } else {
                        this.$message.error('获取认证地址失败');
                    }
                } catch (error) {
                    this.$message.error('认证失败：' + error);
                }
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.divider {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: 14px;

    &::before,
    &::after {
        content: '';
        flex: 1;
        height: 1px;
        background: #dcdfe6;
    }

    span {
        padding: 0 16px;
    }
}

.quick-btns {
    margin-top: 20px;
    padding-bottom: 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    gap: 10px;

    .el-button {
        width: 44px;
        height: 44px;
        border: none;
        margin-left: 0px;
        transition: all 0.3s;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;

        ::v-deep .iconfont {
            font-size: 44px !important;
        }

        &:hover {
            transform: scale(1.1);
        }
    }
}
</style>