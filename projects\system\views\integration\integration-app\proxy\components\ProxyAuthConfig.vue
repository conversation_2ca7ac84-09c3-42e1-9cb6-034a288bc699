<template>
  <div class="proxy-auth-config">
    <div class="config-section">
      <div class="section-header" @click="toggleCollapse">
        <div class="header-left">
          <h4 class="section-title">基础认证配置</h4>
          <el-tag
            :type="getConfigStatusType()"
            size="mini"
            effect="plain"
            class="status-tag"
          >
            {{ getConfigStatus() }}
          </el-tag>
        </div>
        <div class="header-actions">
          <i :class="['collapse-icon', isCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></i>
        </div>
      </div>

      <el-collapse-transition>
        <div v-show="!isCollapsed" class="form-container">
        <el-form
          ref="authForm"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="small"
          class="auth-form"
        >
        <!-- 认证执行器 -->
        <el-form-item label="认证执行器" prop="authActuator">
          <el-select
            v-model="formData.authActuator"
            placeholder="请选择认证执行器"
            clearable
            @change="handleFieldChange"
            :loading="loadingActuators"
          >
            <el-option
              v-for="item in authActuatorOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
            <template v-if="loadingActuators && authActuatorOptions.length === 0" slot="empty">
              <div class="empty-text">正在加载...</div>
            </template>
          </el-select>
          <div class="field-help">
            选择用于处理认证逻辑的执行器
          </div>
        </el-form-item>

        <!-- 认证表达式 -->
        <el-form-item label="认证表达式" prop="authExpression">
          <div class="expression-input-group">
            <el-input
              v-model="formData.authExpression"
              type="textarea"
              :rows="3"
              placeholder="认证表达式，如: ${getAccessToken.access_token}"
              clearable
              @input="handleFieldChange"
            />
            <el-button
              size="small"
              icon="el-icon-video-play"
              :loading="testTokenLoading"
              :disabled="!canTestToken"
              @click="testTokenExtraction"
              class="test-token-btn mac-style-btn"
            >
              测试提取
            </el-button>
          </div>
          <div class="field-help">
            使用 ${requestCode.fieldName} 格式引用认证链中的响应数据，也可以直接使用字符串和加密函数
          </div>
        </el-form-item>

        <!-- 认证位置 -->
        <el-form-item label="认证位置" prop="authLocation">
          <el-select
            v-model="formData.authLocation"
            placeholder="请选择认证位置"
            @change="handleFieldChange"
          >
            <el-option
              v-for="item in authLocationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span>{{ item.label }}</span>
              <span class="option-desc">{{ item.desc }}</span>
            </el-option>
          </el-select>
          <div class="field-help">
            认证信息在请求中的添加位置
          </div>
        </el-form-item>

        <!-- 认证参数名 -->
        <el-form-item label="认证参数名" prop="authParamName">
          <el-input
            v-model="formData.authParamName"
            placeholder="如Authorization、token、access_token等"
            clearable
            @input="handleFieldChange"
          />
          <div class="field-help">
            认证信息的参数名称，常见如Authorization、X-API-Key等
          </div>
        </el-form-item>

        <!-- 目标服务基础URL -->
        <el-form-item label="基础URL" prop="baseUrl">
          <div class="base-url-input-group">
            <el-input
              v-model="formData.baseUrl"
              placeholder="https://api.example.com 或 http://api.example.com"
              clearable
              @input="handleFieldChange"
            />
            <el-button
              size="small"
              icon="el-icon-connection"
              :loading="testConnectivityLoading"
              :disabled="!formData.baseUrl || !isValidUrl(formData.baseUrl)"
              @click="testConnectivity"
              class="test-connectivity-btn mac-style-btn"
            >
              测试连接
            </el-button>
          </div>
          <div class="field-help">
            第三方服务的基础URL地址，请包含完整的协议（http://或https://）
          </div>
        </el-form-item>

        <!-- 缓存秒数 -->
        <el-form-item label="缓存秒数" prop="tokenCacheSeconds">
          <div class="cache-seconds-input">
            <input
              type="number"
              v-model.number="formData.tokenCacheSeconds"
              :min="0"
              :max="86400"
              placeholder="0"
              @input="handleCacheSecondsChange"
              @blur="validateCacheSeconds"
              class="cache-input"
            />
            <span class="input-unit">秒</span>
            <div class="quick-options">
              <button
                type="button"
                v-for="option in cacheQuickOptions"
                :key="option.value"
                @click="setCacheSeconds(option.value)"
                :class="['quick-btn', { active: formData.tokenCacheSeconds === option.value }]"
              >
                {{ option.label }}
              </button>
            </div>
          </div>
          <div class="field-help">
            认证信息的缓存时间，0表示不缓存，最大86400秒（24小时）
          </div>
        </el-form-item>
        </el-form>
        </div>
      </el-collapse-transition>
    </div>

    <!-- Token测试结果对话框 -->
    <el-dialog
      title="Token提取测试结果"
      :visible.sync="tokenTestDialogVisible"
      width="85%"
      append-to-body
      :close-on-click-modal="false"
      custom-class="mac-style-dialog"
    >
      <div v-if="tokenTestResult" class="token-test-result mac-content">
        <div class="result-summary mac-summary">
          <div v-if="tokenTestResult.success" class="success-indicator">
            <div class="success-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="success-text">
              <h3>Token提取成功</h3>
              <p>认证链路执行成功，Token提取表达式正确解析</p>
              <div class="token-display">
                <label>提取的Token:</label>
                <code class="extracted-token mac-token">{{ tokenTestResult.extractedToken }}</code>
              </div>
            </div>
          </div>
          <div v-else class="error-indicator">
            <div class="error-icon">
              <i class="el-icon-close"></i>
            </div>
            <div class="error-text">
              <h3>Token提取失败</h3>
              <p class="error-message">{{ tokenTestResult.errorMessage }}</p>
            </div>
          </div>
        </div>

        <div v-if="tokenTestResult.success" class="test-details mac-details">
          <h4 class="section-title">执行统计</h4>
          <div class="stats-grid mac-stats">
            <div class="stat-item mac-stat-card">
              <span class="stat-label">执行时间:</span>
              <span class="stat-value">{{ tokenTestResult.executionTime }}</span>
            </div>
            <div class="stat-item mac-stat-card">
              <span class="stat-label">耗时:</span>
              <span class="stat-value">{{ tokenTestResult.executionDurationMs }}ms</span>
            </div>
            <div class="stat-item mac-stat-card">
              <span class="stat-label">执行步骤数:</span>
              <span class="stat-value">{{ tokenTestResult.executedRequestCount }}</span>
            </div>
            <div class="stat-item mac-stat-card">
              <span class="stat-label">表达式:</span>
              <span class="stat-value">{{ tokenTestResult.authExpression }}</span>
            </div>
          </div>

          <h4 class="section-title">链路执行结果</h4>
          <div class="chain-execution-results mac-cards">
            <div
              v-for="(result, stepCode) in tokenTestResult.chainExecutionResults"
              :key="stepCode"
              class="step-result-card mac-card"
            >
              <div class="step-result-header mac-card-header">
                <div class="step-info">
                  <h5>{{ getStepName(stepCode) }}</h5>
                  <span class="step-code">{{ stepCode }}</span>
                </div>
                <div class="success-badge">
                  <i class="el-icon-check"></i>
                  <span>成功</span>
                </div>
              </div>
              <div class="step-result-body mac-card-body">
                <pre class="result-json mac-json">{{ JSON.stringify(result, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <!-- 表达式建议区域 -->
          <div class="expression-suggestions mac-suggestions">
            <h4 class="section-title">表达式建议</h4>
            <div class="suggestions-list mac-tags">
              <div
                v-for="expr in generateExpressionSuggestions()"
                :key="expr"
                class="suggestion-tag mac-tag"
                @click="copyExpression(expr)"
              >
                <i class="el-icon-document-copy"></i>
                <span>{{ expr }}</span>
              </div>
            </div>
            <p class="suggestions-tip mac-tip">
              <i class="el-icon-info"></i>
              点击表达式可复制到剪贴板，用于认证表达式配置
            </p>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer mac-footer">
        <el-button
          @click="tokenTestDialogVisible = false"
          class="mac-close-btn"
          size="medium"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAuthActuators, testTokenExtraction, testNetworkConnectivity } from '@system/api/integration/proxy-app'

export default {
  name: 'ProxyAuthConfig',
  props: {
    appId: {
      type: String,
      default: ''
    },
    config: {
      type: Object,
      default: () => ({})
    },
    authRequests: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isCollapsed: false, // 折叠状态
      formData: {
        authActuator: 'default',
        authExpression: '',
        authLocation: 'HEADER',
        authParamName: 'Authorization',
        baseUrl: '',
        tokenCacheSeconds: null
      },
      authActuatorOptions: [],
      authLocationOptions: [
        {
          value: 'HEADER',
          label: '请求头 (HEADER)',
          desc: '添加到HTTP请求头中'
        },
        {
          value: 'URL_PARAM',
          label: 'URL参数 (URL_PARAM)',
          desc: '添加到URL查询参数中'
        },
        {
          value: 'BODY',
          label: '请求体 (BODY)',
          desc: '添加到请求体中'
        }
      ],
      formRules: {
        authActuator: [
          { required: true, message: '请选择认证执行器', trigger: 'change' }
        ],
        authExpression: [
          { required: true, message: '请输入认证表达式', trigger: 'blur' }
        ],
        authLocation: [
          { required: true, message: '请选择认证位置', trigger: 'change' }
        ],
        authParamName: [
          { required: true, message: '请输入认证参数名', trigger: 'blur' }
        ],
        baseUrl: [
          { required: true, message: '请输入目标服务基础URL', trigger: 'blur' },
          {
            pattern: /^https?:\/\/.+/,
            message: '请输入有效的URL地址（需包含http://或https://）',
            trigger: 'blur'
          }
        ],
        tokenCacheSeconds: [
          { type: 'number', message: '缓存秒数必须为数字', trigger: 'change' }
        ]
      },
      loadingActuators: false,
      cacheQuickOptions: [
        { label: '不缓存', value: 0 },
        { label: '5分钟', value: 300 },
        { label: '30分钟', value: 1800 },
        { label: '1小时', value: 3600 },
        { label: '6小时', value: 21600 },
        { label: '24小时', value: 86400 }
      ],
      // 测试相关数据
      testTokenLoading: false,
      tokenTestResult: null,
      tokenTestDialogVisible: false,
      // 网络连接测试相关数据
      testConnectivityLoading: false
    }
  },
  computed: {
    // 获取当前执行器的显示名称
    currentActuatorName() {
      if (!this.formData.authActuator || this.authActuatorOptions.length === 0) {
        return ''
      }

      const found = this.authActuatorOptions.find(item => item.code === this.formData.authActuator)
      return found ? found.name : this.formData.authActuator
    },

    // 判断是否可以测试Token提取
    canTestToken() {
      return this.formData.baseUrl &&
             this.formData.authExpression &&
             this.authRequests &&
             this.authRequests.length > 0 &&
             this.authRequests.some(req => req.enabled)
    }
  },
  created() {
    this.fetchAuthActuators()
  },
  watch: {
    config: {
      handler(newConfig) {
        if (newConfig) {
          console.log('收到config属性:', JSON.stringify(newConfig))
          // 使用 Object.assign 来避免触发 formData 的 watch
          Object.assign(this.formData, newConfig)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 获取认证执行器列表
    fetchAuthActuators() {
      this.loadingActuators = true
      getAuthActuators().then(res => {
        console.log('获取认证执行器列表:', JSON.stringify(res))
        this.authActuatorOptions = res
        this.loadingActuators = false
        
        // 如果没有从父组件接收到config，或者config中没有设置authActuator，则设置默认值
        if ((!this.config || !this.config.authActuator) && res && res.length > 0) {
          this.formData.authActuator = res[0].code
        }
      }).catch(err => {
        console.error('获取认证执行器失败:', err)
        this.loadingActuators = false
      })
    },

    // 切换折叠状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },

    // 处理字段变化
    handleFieldChange() {
      // 使用 nextTick 确保数据更新后再触发事件
      this.$nextTick(() => {
        this.$emit('config-change', this.formData)
      })
    },

    // 处理缓存秒数变化
    handleCacheSecondsChange(event) {
      let value = parseInt(event.target.value)
      if (isNaN(value) || value < 0) {
        value = 0
      } else if (value > 86400) {
        value = 86400
      }
      this.formData.tokenCacheSeconds = value
      this.handleFieldChange()
    },

    // 验证缓存秒数
    validateCacheSeconds(event) {
      const value = parseInt(event.target.value)
      if (isNaN(value) || value < 0) {
        this.formData.tokenCacheSeconds = 0
        event.target.value = 0
      } else if (value > 86400) {
        this.formData.tokenCacheSeconds = 86400
        event.target.value = 86400
      }
      this.handleFieldChange()
    },

    // 设置缓存秒数快捷值
    setCacheSeconds(value) {
      this.formData.tokenCacheSeconds = value
      this.handleFieldChange()
    },

    // 获取配置状态
    getConfigStatus() {
      const requiredFields = ['authActuator', 'authExpression', 'authLocation', 'authParamName', 'baseUrl']
      const filledFields = requiredFields.filter(field => this.formData[field] && this.formData[field].trim())

      if (filledFields.length === requiredFields.length) {
        return '配置完整'
      } else if (filledFields.length > 0) {
        return `${filledFields.length}/${requiredFields.length} 已配置`
      } else {
        return '未配置'
      }
    },

    // 获取配置状态类型
    getConfigStatusType() {
      const requiredFields = ['authActuator', 'authExpression', 'authLocation', 'authParamName', 'baseUrl']
      const filledFields = requiredFields.filter(field => this.formData[field] && this.formData[field].trim())

      if (filledFields.length === requiredFields.length) {
        return 'success'
      } else if (filledFields.length > 0) {
        return 'warning'
      } else {
        return 'info'
      }
    },

    // 测试Token提取
    async testTokenExtraction() {
      if (!this.canTestToken) {
        this.$message.error('请先完善基础配置和认证链路')
        return
      }

      this.testTokenLoading = true
      try {
        this.$message.info('开始测试Token提取...')

        // 准备测试数据
        const testData = {
          baseUrl: this.formData.baseUrl,
          authRequests: this.authRequests.filter(req => req.enabled),
          authExpression: this.formData.authExpression
        }

        // 调用测试API
        const response = await testTokenExtraction(testData)

        this.tokenTestResult = response
        this.tokenTestDialogVisible = true

        if (response.success) {
          this.$message.success('Token提取测试成功')
        } else {
          this.$message.error(response.errorMessage || 'Token提取测试失败')
        }
      } catch (error) {
        console.error('Token提取测试失败:', error)
        this.$message.error(error.message || 'Token提取测试失败')
      } finally {
        this.testTokenLoading = false
      }
    },

    // 重置表单
    resetForm() {
      this.$refs.authForm.resetFields()
      this.formData = {
        authActuator: 'default',
        authExpression: '',
        authLocation: 'HEADER',
        authParamName: 'Authorization',
        baseUrl: '',
        tokenCacheSeconds: null
      }
    },

    // 获取步骤名称
    getStepName(stepCode) {
      const step = this.authRequests.find(req => req.code === stepCode)
      return step ? (step.name || step.code) : stepCode
    },

    // 生成表达式建议
    generateExpressionSuggestions() {
      if (!this.tokenTestResult || !this.tokenTestResult.chainExecutionResults) return []

      const suggestions = []
      Object.keys(this.tokenTestResult.chainExecutionResults).forEach(requestCode => {
        const responseData = this.tokenTestResult.chainExecutionResults[requestCode]
        if (typeof responseData === 'object' && responseData !== null) {
          this.extractFieldPaths(responseData, requestCode, '').forEach(path => {
            suggestions.push(`\${${path}}`)
          })
        }
      })
      return suggestions.slice(0, 20) // 限制建议数量
    },

    // 递归提取字段路径
    extractFieldPaths(obj, prefix, currentPath) {
      const paths = []

      if (typeof obj !== 'object' || obj === null) {
        return paths
      }

      Object.keys(obj).forEach(key => {
        const fullPath = currentPath ? `${prefix}.${currentPath}.${key}` : `${prefix}.${key}`
        const value = obj[key]

        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // 递归处理对象
          paths.push(...this.extractFieldPaths(value, prefix, currentPath ? `${currentPath}.${key}` : key))
        } else {
          // 叶子节点
          paths.push(fullPath)
        }
      })

      return paths
    },

    // 复制表达式到剪贴板
    async copyExpression(expression) {
      try {
        await navigator.clipboard.writeText(expression)
        this.$message.success(`已复制: ${expression}`)
      } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = expression
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success(`已复制: ${expression}`)
      }
    },

    // 验证URL格式
    isValidUrl(url) {
      if (!url) return false
      return /^https?:\/\/.+/.test(url.trim())
    },

    // 测试网络连接
    async testConnectivity() {
      if (!this.formData.baseUrl || !this.isValidUrl(this.formData.baseUrl)) {
        this.$message.error('请输入有效的基础URL地址')
        return
      }

      this.testConnectivityLoading = true
      try {
        const data = await testNetworkConnectivity(this.formData.baseUrl.trim())

        if (data === true) {
          this.$message.success('网络连接测试成功！')
        } else {
          this.$message.error('网络连接测试失败，请检查URL地址是否正确或网络是否可达')
        }
      } catch (error) {
        console.error('网络连接测试失败:', error)
        this.$message.error(error.message || '网络连接测试失败')
      } finally {
        this.testConnectivityLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.proxy-auth-config {
  .config-section {
    background: white;
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 20px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid rgba(148, 163, 184, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-title {
          font-size: 15px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }

        .status-tag {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;

        .collapse-icon {
          font-size: 14px;
          color: #64748b;
          transition: all 0.2s ease;
        }
      }
    }

    .form-container {
      padding: 20px;

      .auth-form {
        .cache-seconds-input {
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;

          .cache-input {
            width: 120px;
            height: 32px;
            padding: 0 12px;
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 8px;
            font-size: 13px;
            transition: all 0.2s ease;
            outline: none;

            &:focus {
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }

            &[type=number] {
              -moz-appearance: textfield;
            }
          }

          .input-unit {
            font-size: 13px;
            color: #64748b;
            font-weight: 500;
          }

          .quick-options {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;

            .quick-btn {
              padding: 4px 8px;
              font-size: 11px;
              border: 1px solid rgba(148, 163, 184, 0.2);
              border-radius: 4px;
              background: #f8fafc;
              color: #64748b;
              cursor: pointer;
              transition: all 0.2s ease;
              outline: none;

              &:hover {
                border-color: #3b82f6;
                color: #3b82f6;
                background: rgba(59, 130, 246, 0.05);
              }

              &.active {
                border-color: #3b82f6;
                background: #3b82f6;
                color: white;
                box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
              }
            }
          }
        }

        ::v-deep .el-form-item {
          margin-bottom: 20px;

          .el-form-item__label {
            color: #374151;
            font-weight: 500;
            font-size: 14px;
          }

          .el-input {
            .el-input__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);
              transition: all 0.2s ease;

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .el-select {
            .el-input__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .el-textarea {
            .el-textarea__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .el-input-number {
            .el-input__inner {
              border-radius: 8px;
              border: 1px solid rgba(148, 163, 184, 0.2);

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }
        }
      }
    }

    .field-help {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 4px;
      line-height: 1.4;
    }

    .option-desc {
      float: right;
      font-size: 12px;
      color: #8c8c8c;
    }

    .empty-text {
      text-align: center;
      color: #8c8c8c;
      padding: 10px 0;
    }

    // 表达式输入组样式
    .expression-input-group {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .el-textarea {
        flex: 1;
      }

      .test-token-btn {
        flex-shrink: 0;
        margin-top: 2px;
      }
    }

    // 基础URL输入组样式
    .base-url-input-group {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .el-input {
        flex: 1;
      }

      .test-connectivity-btn {
        flex-shrink: 0;
        margin-top: 2px;
      }
    }

    // MAC风格按钮
    .mac-style-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      padding: 8px 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        background: #e0e6ed;
        color: #8c8c8c;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;

        &:hover {
          background: #e0e6ed;
          transform: none;
          box-shadow: none;
        }
      }
    }

  }

  // MAC风格Token测试结果 - 样式已移至非scoped部分
}

// MAC风格对话框底部
.mac-footer {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  text-align: right;

  .mac-close-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 10px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);

    &:hover {
      background: linear-gradient(135deg, #5b6470 0%, #3f4651 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

::v-deep .el-form-item {
  margin-bottom: 20px;
}

::v-deep .el-form-item__label {
  font-size: 13px;
  color: #262626;
  font-weight: 500;
}

::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  font-size: 13px;
}

::v-deep .el-select .el-input__inner {
  font-size: 13px;
}

// 全局MAC风格对话框样式
::v-deep .mac-style-dialog {
  .el-dialog {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 16px 16px 0 0;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }

    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      width: 32px;
      height: 32px;
      background: #f3f4f6;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background: #e5e7eb;
        transform: scale(1.1);
      }

      .el-dialog__close {
        color: #6b7280;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: white;
  }

  .el-dialog__footer {
    padding: 0;
    background: transparent;
  }
}
</style>

<!-- 非scoped样式，用于对话框内容 -->
<style lang="scss">
// Token测试结果对话框样式（非scoped，因为对话框append-to-body）
.mac-style-dialog {
  .token-test-result.mac-content {
    .mac-summary {
      margin-bottom: 24px;

      .success-indicator {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
        border: 1px solid #b3f5d1;
        border-radius: 12px;

        .success-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

          i {
            font-size: 24px;
            color: white;
            font-weight: bold;
          }
        }

        .success-text {
          flex: 1;

          h3 {
            margin: 0 0 8px 0;
            color: #065f46;
            font-size: 18px;
            font-weight: 600;
          }

          p {
            margin: 0 0 12px 0;
            color: #047857;
            font-size: 14px;
            line-height: 1.5;
          }

          .token-display {
            label {
              display: block;
              font-size: 14px;
              color: #047857;
              font-weight: 500;
              margin-bottom: 8px;
            }

            .mac-token {
              display: block;
              background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
              border: 1px solid #d1d5db;
              border-radius: 8px;
              padding: 12px 16px;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
              color: #374151;
              word-break: break-all;
              line-height: 1.5;
              max-height: 120px;
              overflow-y: auto;
            }
          }
        }
      }

      .error-indicator {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #fca5a5;
        border-radius: 12px;

        .error-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);

          i {
            font-size: 24px;
            color: white;
            font-weight: bold;
          }
        }

        .error-text {
          flex: 1;

          h3 {
            margin: 0 0 8px 0;
            color: #991b1b;
            font-size: 18px;
            font-weight: 600;
          }

          .error-message {
            margin: 0;
            color: #dc2626;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }

    // MAC风格详情区域样式
    .mac-details {
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e5e7eb;
      }

      .stats-grid,
      .mac-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 24px;

        .stat-item,
        .mac-stat-card {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 1px solid #e2e8f0;
          border-radius: 10px;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .stat-label {
            font-weight: 500;
            color: #64748b;
            font-size: 13px;
          }

          .stat-value {
            color: #1e293b;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            font-weight: 500;
            background: white;
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
          }
        }
      }

      .chain-execution-results,
      .mac-cards {
        .step-result-card,
        .mac-card {
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 12px;
          margin-bottom: 16px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
          }

          .step-result-header,
          .mac-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .step-info {
              h5 {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
              }

              .step-code {
                font-size: 12px;
                color: #64748b;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                background: #f1f5f9;
                padding: 2px 6px;
                border-radius: 4px;
              }
            }

            h5 {
              margin: 0 0 4px 0;
              font-size: 14px;
              font-weight: 600;
              color: #1e293b;
            }

            .success-badge {
              display: flex;
              align-items: center;
              gap: 6px;
              background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
              color: #166534;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;

              i {
                font-size: 12px;
              }
            }

            .el-tag {
              background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
              color: #166534;
              border: none;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;
            }
          }

          .step-result-body,
          .mac-card-body {
            padding: 20px;

            .result-json,
            .mac-json {
              background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              padding: 16px;
              font-size: 12px;
              line-height: 1.6;
              color: #374151;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              max-height: 300px;
              overflow-y: auto;
              margin: 0;
              white-space: pre-wrap;
            }
          }
        }
      }
    }

    // 表达式建议区域样式
    .mac-suggestions {
      margin-top: 24px;
      padding: 20px;
      background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
      border: 1px solid #fbbf24;
      border-radius: 12px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #92400e;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '💡';
          font-size: 18px;
        }
      }

      .mac-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 12px;

        .mac-tag {
          background: white;
          border: 1px solid #d97706;
          color: #92400e;
          padding: 8px 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 6px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          font-weight: 500;

          &:hover {
            background: #f59e0b;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
          }

          i {
            font-size: 12px;
          }
        }
      }

      .mac-tip {
        margin: 0;
        font-size: 12px;
        color: #92400e;
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
