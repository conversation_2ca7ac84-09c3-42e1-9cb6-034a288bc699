<template>
  <el-form :model="form" size="small" :rules="rules" ref="form" style="height: 100%;">
    <component :is="editor.component" v-model="form.content" style="height: 100%;" />
  </el-form>
</template>
<script> 
import { isEqual } from 'element-ui/src/utils/util';
import scriptEditor from './index.js'

export default {
  name: '<PERSON>rip<PERSON>',
  components: {
  },
  data() {
    return {
      rules: {},
      form: {},
    }
  },
  props: {
    properties: {
      type: Object,
      default: () => ({})
    },
    node: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() { 
  },
  methods: {
    getProperties() {
      return this.form
    },
    async validate() {
      if (!this.form.content?.trim()) {
        this.$message.error('请填写脚本内容')
        return Promise.resolve(false)
      }
      return Promise.resolve(true)
    },
  },
  computed: { 
    editor() {
      return scriptEditor[this.node.type.replace("_SCRIPT", '')]
    }
  },
  watch: {
    properties: {
        handler(newVal) {
            if (!isEqual(newVal, this.form)) {
                this.form = { ...this.form, ...newVal }
            }
        },
        immediate: true,
        deep: true
    }
  },
}
</script>