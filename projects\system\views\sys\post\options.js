export default {
    api: 'sys/post',
    search: {
        isShow: true,
        showReset: true
    },
    table: {
        isHasChildren: true
    },
    formRule: [
        {
            type: "input",
            field: "name",
            title: "职位名称",
            col: {md: {span: 18}},
            isSearch: true,
            isSearchValidate: [],
            isSearchCol:{md:{span:20}},
            props:{
                placeholder:"请输入职位名称",
                readonly: false,
                clearable: true,
                disabled: false
            },
            validate:[
                {
                    trigger:"blur",
                    required:true,
                    message:"请输入"
                }
            ]
        },
        {
            type: "input",
            field: "code",
            title: "职位标识",
            col: {md: {span: 18}},
            props: {
                disabled: false,
                readonly: false,
                placeholder: "请输入职位标识",
                clearable: true
            },
            validate:[
                {
                    trigger:"blur",
                    required:true,
                    message:"请输入"
                }
            ]
        },
        {
            type: "treeSelect",
            field: "parentId",
            title: "上级职位",
            isTable: false,
            col: {md: {span: 18}},
            options:[],
            props: {
                multiple: false,
                disabled: false,
                readonly: false,
                placeholder: "请选择上级",
                clearable: true
            }
        },
        {
            type: "input",
            field: "sort",
            title: "排序",
            col: {md: {span: 18}},
            props: {
                disabled: false,
                readonly: false,
                placeholder: "请输入排序",
                clearable: true
            },
            validate:[
                {
                    trigger:"blur",
                    required:true,
                    message:"请输入"
                }
            ]
        },
        {
            type: "radio",
            field: "state",
            title: "状态",
            col: {md: {span: 18}},
            props: {
                disabled: false,
                readonly: false,
            },
            options: [
                {
                    value: true,
                    label: "正常",
                    disabled: false
                },
                {
                    value: false,
                    label: "停用",
                    disabled: false
                }
            ],
            value: null,
        },
        {
            type: "DatePicker",
            field: "createTime",
            title: "创建时间",
            isTable: true,
            isHidden: true
        },
        {
            type: "input",
            field: "remark",
            className: "remark-dom",
            title: "备注",
            isSearch: false,
            isTable: false,
            col: {md: {span: 18}},
            props: {
                type: "textarea",
                placeholder: "请输入备注",
                readonly: false,
                clearable: true,
                disabled: false
            },
        },

    ]
}
