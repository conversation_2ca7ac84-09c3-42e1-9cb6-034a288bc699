<template>
  <div class="image-uploader">
    <!-- 上传区域 -->
    <div class="upload-area">
      <!-- <div class="upload-header">
        <div class="upload-stats" v-if="fileList.length > 0">
          <el-tag size="small" effect="light">{{ fileList.length }} 个文件</el-tag>
          <el-tag size="small" type="success" effect="light" v-if="successCount > 0">
            {{ successCount }} 个成功
          </el-tag>
        </div>
      </div> -->

      <!-- 简化的上传按钮区域 -->
      <div class="upload-buttons">
        <el-button type="primary" size="small" @click="triggerFileInput">
          <i class="el-icon-plus"></i> 选择图片
        </el-button>
        <div class="action-buttons" v-if="fileList.length > 0">
          <el-button type="success" size="small" @click="startUpload" :disabled="isUploading || !hasWaitingFiles"
            :loading="isUploading">
            {{ isUploading ? '上传中...' : '开始上传' }}
          </el-button>
          <el-button size="small" @click="clearFiles" :disabled="isUploading">
            清空
          </el-button>
        </div>
        <input type="file" ref="fileInput" multiple :accept="acceptString" @change="onFileChange" style="display: none">
        <span class="upload-hint">支持 {{ acceptTypes.join('、') }} 格式，单文件不超过 {{ formatSize(maxSize) }}</span>

      </div>

      <!-- 文件上传列表 - 使用图片网格布局 -->
      <div class="upload-file-grid" v-if="fileList.length > 0">
        <div v-for="(file, index) in fileList" :key="file.uid" class="file-card"
          :class="{ 'uploading': file.status === 'uploading' }">

          <!-- 操作按钮 - 移到最外层 -->
          <div class="file-actions">
            <el-button v-if="file.status !== 'uploading'" type="text" icon="el-icon-delete"
              @click="removeFile(index)"></el-button>
            <el-button v-else type="text" icon="el-icon-close" @click="cancelUpload(file)"></el-button>
          </div>

          <!-- 图片预览 -->
          <div class="image-wrapper" :class="{ 'with-progress': file.status === 'uploading' }">

            <!-- 彩色旋转边框层 -->
            <div class="border-layer" v-if="file.status === 'uploading'"
              :style="{ animationPlayState: file.progress < 100 ? 'running' : 'paused' }"></div>

            <!-- 图片内容区 -->
            <div class="box-content">
              <!-- 状态指示器 -->
              <div class="status-indicator" v-if="file.status === 'success'">
                <i class="el-icon-check"></i>
              </div>
              <div class="status-indicator error" v-if="file.status === 'error'">
                <i class="el-icon-close"></i>
              </div>

              <!-- 图片预览 -->
              <template v-if="file.status === 'success' && file.url">
                <img :src="getImageUrl(file.url)" alt="预览" style="padding: 10px;border-radius: 10px;">
                <div class="preview-action">
                  <el-button type="text" icon="el-icon-zoom-in" @click="previewImage(file.url)"></el-button>
                </div>
              </template>
              <template v-else>
                <img v-if="file.preview" :src="file.preview" alt="预览" style="padding: 10px;border-radius: 10px;">
                <div v-else class="no-preview">
                  <i class="el-icon-picture"></i>
                </div>
              </template>

              <!-- 上传进度文本 -->
              <div class="progress-text" v-if="file.status === 'uploading'">{{ file.progress }}%</div>
            </div>
          </div>

          <!-- 文件信息 -->
          <div class="file-info">
            <div class="file-name" :title="file.name">{{ file.name }}</div>
            <div class="file-size">{{ formatSize(file.size) }}</div>
          </div>

          <!-- 操作按钮 -->
          <div class="file-actions">
            <el-button v-if="file.status !== 'uploading'" type="text" icon="el-icon-delete"
              @click="removeFile(index)"></el-button>
            <el-button v-else type="text" icon="el-icon-close" @click="cancelUpload(file)"></el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="previewVisible" width="70%" center append-to-body custom-class="image-preview-dialog">
      <div class="preview-container"
        style="display: flex; justify-content: center; align-items: center; min-height: 70vh;">
        <img :src="previewImageUrl" alt="预览" style="max-width: 100%; max-height: 70vh; object-fit: contain;">
      </div>
    </el-dialog>
  </div>
</template>

<script>
import UploadHelper from "./uploadHelper.js";

export default {
  props: {
    acceptTypes: {
      type: Array,
      default: () => ['jpg', 'jpeg', 'png', 'gif']
    },
    maxSize: {
      type: Number,
      default: 50 * 1024 * 1024 // 50MB
    },
    maxConcurrent: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      fileList: [],
      isUploading: false,
      isDragging: false,
      uploader: null,
      previewVisible: false,
      previewImageUrl: '',
      uploadUrl: process.env.VUE_APP_FILE_URL || '',
      borderProgress: 0
    };
  },
  computed: {
    acceptString() {
      return this.acceptTypes.map(type => `.${type}`).join(',');
    },
    successCount() {
      return this.fileList.filter(file => file.status === 'success').length;
    },
    hasWaitingFiles() {
      return this.fileList.some(file => file.status === 'waiting');
    }
  },
  mounted() {
    // 初始化上传助手
    this.uploader = new UploadHelper({
      chunkSize: 2 * 1024 * 1024, // 2MB
      allowedTypes: this.acceptTypes,
      maxSize: this.maxSize,
    });
  },
  methods: {
    // 触发文件选择
    triggerFileInput() {
      this.$refs.fileInput.click();
    },

    // 文件选择处理
    onFileChange(e) {
      const files = Array.from(e.target.files);
      this.addFiles(files);
      e.target.value = ''; // 重置 input，确保可以重复选择相同文件
    },

    // 添加文件到列表
    addFiles(files) {
      const imageFiles = files.filter(file => {
        // 检查文件类型
        const fileExt = file.name.split('.').pop().toLowerCase();
        if (!this.acceptTypes.includes(fileExt)) {
          this.$message.error(`不支持的文件类型: ${file.name}`);
          return false;
        }

        // 检查文件大小
        if (file.size > this.maxSize) {
          this.$message.error(`文件过大: ${file.name}`);
          return false;
        }

        return true;
      });

      // 为每个文件创建预览
      imageFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.fileList.push({
            uid: Date.now() + Math.random().toString(36).substring(2),
            file: file,
            name: file.name,
            size: file.size,
            preview: e.target.result,
            status: 'waiting',
            progress: 0,
            error: null,
            url: ''
          });
        };
        reader.readAsDataURL(file);
      });
    },

    // 开始上传
    startUpload() {
      if (this.isUploading) return;
      this.isUploading = true;

      // 获取等待上传的文件
      const waitingFiles = this.fileList.filter(item => item.status === 'waiting');
      if (waitingFiles.length === 0) {
        this.isUploading = false;
        return;
      }

      // 创建上传任务
      const uploadTasks = waitingFiles.map(item => {
        return () => new Promise((resolve) => {
          // 更新状态为上传中
          item.status = 'uploading';

          this.uploader.upload(
            item.file,
            (progress) => {
              // 进度回调
              item.progress = Math.floor(progress);
              this.borderProgress = Math.floor(progress);
            },
            (url) => {
              // 成功回调
              item.status = 'success';
              item.url = url;
              this.$emit('upload-success', url);
              resolve();
            },
            (error) => {
              // 错误回调
              item.status = 'error';
              item.error = error.message || '上传失败';
              resolve();
            }
          );
        });
      });

      // 控制并发上传
      this.runConcurrent(uploadTasks, this.maxConcurrent)
        .then(() => {
          this.isUploading = false;
        })
        .catch(err => {
          console.error('上传出错:', err);
          this.isUploading = false;
        });
    },

    // 并发控制
    async runConcurrent(tasks, limit) {
      const results = [];
      const executing = new Set();

      for (const task of tasks) {
        const p = Promise.resolve().then(() => task());
        results.push(p);

        if (limit <= tasks.length) {
          const e = p.then(() => executing.delete(p));
          executing.add(e);
          if (executing.size >= limit) {
            await Promise.race(executing);
          }
        }
      }

      return Promise.all(results);
    },

    // 移除文件
    removeFile(index) {
      this.fileList.splice(index, 1);
    },

    // 取消上传
    cancelUpload(file) {
      // 这里可以添加取消上传的逻辑，如果 UploadHelper 支持的话
      file.status = 'error';
      file.error = '已取消';
    },

    // 清空文件列表
    clearFiles() {
      if (this.isUploading) return;
      this.fileList = [];
    },

    // 预览图片
    previewImage(url) {
      this.previewImageUrl = this.getImageUrl(url);
      this.previewVisible = true;
    },

    // 获取完整图片URL
    getImageUrl(url) {
      if (url.startsWith('http')) {
        return url;
      }
      return this.uploadUrl + url;
    },

    // 格式化文件大小
    formatSize(size) {
      if (!size) return '0 B';

      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }

      return `${size.toFixed(2)} ${units[index]}`;
    }
  }
};
</script>

<style lang="scss" scoped>
.image-uploader {
  width: 100%;
  padding: 1px;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

  // 上传区域样式
  .upload-area {
    background-color: #fff;
    border-radius: 6px;
    padding: 0px;
    // margin-bottom: 15px;

    .upload-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }

      .upload-stats {
        display: flex;
        gap: 6px;
      }
    }

    // 简化的上传按钮区域
    .upload-buttons {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 1px;

      .upload-hint {
        font-size: 12px;
        color: #909399;
        margin-left: 1px;
      }

      .action-buttons {
        display: flex;
        gap: 8px;
        margin-left: auto;
      }
    }

    // 文件上传网格
    .upload-file-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      // gap: 5px;
      // margin-top: 0px;

      .file-card {
        position: relative;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
        }

        // 文件操作
        .file-actions {
          position: absolute;
          top: 5px;
          right: 5px;
          padding: 4px;
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          opacity: 0;
          transition: opacity 0.2s;
          z-index: 10; // 确保在最上层
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;

          .el-button {
            padding: 0;
            font-size: 14px;
            color: #fff;
            margin: 0;

            i {
              font-size: 14px;
            }
          }
        }

        &:hover .file-actions {
          opacity: 1;
        }

        // 图片包装器
        .image-wrapper {
          position: relative;
          width: 100%;
          padding-top: 100%; // 1:1 宽高比
          overflow: hidden;
          border-radius: 4px;

          .border-layer {
            background-image:
              conic-gradient(from 45deg,
                #ffbe44 0deg 90deg,
                #5DADE2 90deg 180deg,
                #ee5732 180deg 270deg,
                #2ECC71 270deg 360deg);
            position: absolute;
            left: 50%;
            top: 50%;
            width: 370px;
            height: 370px;
            margin-left: -185px;
            margin-top: -185px;
            animation: 6s infinite linear rotate;
          }

          /* 旋转动画 */
          @keyframes rotate {
            from {
              transform: rotate(0deg);
            }

            to {
              transform: rotate(360deg);
            }
          }

          // 内容层
          .box-content {
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            background-color: #fff;
            border-radius: 4px;
            overflow: hidden;
            z-index: 2;

            img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            // 预览操作按钮
            .preview-action {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              opacity: 0;
              transition: opacity 0.2s;
              z-index: 3;

              .el-button {
                background-color: rgba(0, 0, 0, 0.7);
                color: #ffffff;
                width: 36px;
                height: 36px;
                font-size: 18px;
                padding: 0;
                border: none;

                i {
                  margin: 0;
                }
              }
            }

            &:hover .preview-action {
              opacity: 1;
            }

            // 进度文本
            .progress-text {
              position: absolute;
              bottom: 8px;
              right: 8px;
              background-color: rgba(0, 0, 0, 0.6);
              color: #fff;
              font-size: 12px;
              font-weight: bold;
              border-radius: 10px;
              z-index: 3;
            }

            .no-preview {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                font-size: 24px;
                color: #c0c4cc;
              }
            }
          }

          // 状态指示器
          .status-indicator {
            position: absolute;
            top: 5px;
            left: 5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #67c23a;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;

            i {
              font-size: 12px;
              color: #fff;
            }

            &.error {
              background-color: #f56c6c;
            }
          }


        }

        // 文件信息
        .file-info {
          padding: 0px 8px;

          .file-name {
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            color: #303133;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 0px;
          }

          .file-size {
            height: 16px;
            display: flex;
            align-items: center;
            font-size: 11px;
            color: #909399;
            margin-bottom: 5px;
          }
        }

        // 文件操作
        .file-actions {
          position: absolute;
          top: 0;
          right: 0;
          padding: 1px;
          background-color: rgba(0, 0, 0, 0.3);
          border-bottom-left-radius: 4px;
          opacity: 0;
          transition: opacity 0.2s;

          .el-button {
            padding: 1px;
            font-size: 14px;
            color: #fff;
          }
        }

        &:hover .file-actions {
          opacity: 1;
        }
      }
    }
  }

  // 图片预览弹窗
  :deep(.preview-dialog) {
    .el-dialog__body {
      padding: 0;
      text-align: center;
    }

    .preview-container {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      min-height: 300px;
    }

    .preview-image {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
    }
  }
}
</style>