<template>
    <div class="hello">
        <div style="border: 1px solid #ccc;">
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig"
                :mode="mode" />
            <Editor style="height: 300px; overflow-y: hidden;" class="Editor" v-model="editorContent"
                :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
        </div>
    </div>
</template>
<style src="./style.css"></style>
<script>
import Vue from 'vue'
import { Editor, Toolbar } from '@wangeditor-next/editor-for-vue2'
import { getToken } from '@/utils/auth'
import { isEqual } from 'element-ui/src/utils/util';

export default Vue.extend({
    name: 'wangEditor5',
    components: { Editor, Toolbar },
    props: {
        value: {
            type: String,
            required: true,
            default: '',
        },
        disable: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        let self = this
        return {
            editorContent: "",
            url: process.env.VUE_APP_FILE_URL,
            editor: null,
            html: '',
            fileUrl: process.env.VUE_APP_BASE_API + '/file/minio/uploadFile',
            toolbarConfig: {
                excludeKeys: ['group-video', 'fullScreen'],
                // toolbarKeys: [
                //     'bold',
                //     'underline',
                //     'italic',
                //     'clearStyle',
                //     'color',
                //     'bgColor',
                //     'fontSize',
                //     'fontFamily',
                //     'uploadImage',
                //     'justifyLeft',
                //     'justifyRight',
                //     'justifyCenter',
                //     'justifyJustify',
                //     '|', // 分割线
                //     "lineHeight", // 行间距 
                //     // "insertImage", // 插入图片 
                //     // "deleteImage", // 删除图片 
                //     // "editImage", // 编辑图片 
                //     // "viewImageLink", // 查看图片链接 
                //     "imageWidth30", // 图片宽度30% 
                //     "imageWidth50", // 图片宽度50% 
                //     "imageWidth100", // 图片宽度100% 
                //     "divider", // 分隔线 
                //     "emotion", // 表情 
                //     "insertLink", // 插入链接 
                //     "editLink", // 编辑链接 
                //     "unLink", // 取消链接 
                //     // "viewLink", // 查看链接 
                //     // "codeBlock", // 代码块 
                //     // "blockquote", // 引用块 
                //     // '|',
                //     // "headerSelect", // 头部类型选择 
                //     // "header1", // 头部1 
                //     // "header2", // 头部2 
                //     // "header3", // 头部3 
                //     // "header4", // 头部4 
                //     // "header5", // 头部5 
                //     // "todo", // 待办事项 
                //     // "redo", // 重做 
                //     // "undo", // 撤销 
                //     // "fullScreen", // 全屏 
                //     // "enter", // 换行 
                // ],
            },
            mode: 'default',
            editorConfig: {
                MENU_CONF: {
                    uploadImage: {
                        // 用户自定义上传图片
                        customUpload(file, insertFn) {
                            const axios = require('axios')
                            const FormData = require('form-data')
                            const data = new FormData()
                            data.append('file', file) // file 即选中的文件
                            const config = {
                                method: 'post',
                                url: self.fileUrl, //上传图片地址
                                headers: {
                                    'Content-Type': 'multipart/form-data',
                                    'authorization': getToken(),
                                },
                                data: data,
                            }
                            axios(config)
                                .then(function (res) {
                                    let url = self.url + res.data.data //拼接成可浏览的图片地址
                                    insertFn(url, url.substring(url.lastIndexOf('/') + 1), url) //插入图片
                                })
                                .catch(function (error) {
                                    console.log(error)
                                })
                        },
                    }
                }
            },
        }
    },
    watch: {
        disable: {
            handler: function (n) {
                if (n) {
                    this.editor && this.editor.disable() // 禁用编辑器
                } else {
                    this.editor && this.editor.enable() // 启用编辑器
                }
            },
            immediate: true,
            deep: true,
        },
        value: {
            handler(n) {
                if (!isEqual(n, this.editorContent)) {
                    this.editorContent = n
                }
            },
            immediate: true
        },
        editorContent(n) {
            if (!isEqual(n, this.value)) {
                this.$emit('input', n)
                this.$emit('onContentChange', n)
            }
        },
    },
    methods: {
        onCreated(editor) {
            this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
            if (this.disable) {
                this.editor.disable() // 禁用编辑器
            } else {
                this.editor.enable() // 启用编辑器
            }
        },
        clear() {
            this.editor && this.editor.clear()
        },
        isEmpty() {
            this.editor && this.editor.isEmpty()
        },
    },
    mounted() { },
    beforeDestroy() {
        this.editor && this.editor.destroy()
    },
})
</script>
