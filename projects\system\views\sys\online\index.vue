<template>
  <div class="auto_page">
    <el-row>
      <el-form :model="form" label-width="auto">
        <el-col :span="4">
          <el-form-item label="用户名">
            <el-input v-model="form.username" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-row>
      <el-table :data="list">
        <el-table-column label="用户名" prop="username" align="center" />
        <el-table-column label="名称" prop="name"  align="center" />
        <el-table-column label="token" prop="token"  width="400" align="center" />
        <el-table-column label="host" prop="host"  align="center" />
        <el-table-column label="登录时间" prop="loginTime"  align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-bottom" @click="offlineFun(scope.row.token)">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </div>
</template>

<script>
  import options from './options'
  import autoPage from "@/components/auto-page/AutoPage";
  import {getOnlines,offline} from "@system/api/sys/resources"
  export default {
    components:{autoPage},
    data(){
      return{
        options:null,
        form:{},
        dataList:[]
      }
    },
    computed:{
      list(){
        return this ? this.dataList.filter(i => !this.form.username || i.username.includes(this.form.username)) : []
      }
    },
    created(){
      this.getList();
    },
    methods:{
      getList(){
        getOnlines().then(res=>{
          this.dataList=res;
        })
      },
      offlineFun(token){
        console.log("点击操作")
        offline(token).then(res=>{
          if (res){
            this.$message.success("下线成功")
            localStorage.removeItem('cachedData');
          }
          this.getList();
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.auto_page {
  background: #fff;
  border-top: 1px solid #ccc;
  padding-left: 20px;
  padding: 20px 18px 20px 25px;
}
</style>