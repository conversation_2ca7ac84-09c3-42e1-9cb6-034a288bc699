<template>
  <div class="sso-apps">
    <!-- 顶部搜索和筛选栏 -->
    <div class="header-section">
      <div class="search-filter-bar">
        <div class="custom-search-input">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索应用名称、编码或描述..."
            class="search-input-field"
            @keyup.enter="handleSearch"
          />
          <button
            v-if="searchKeyword"
            class="clear-button"
            @click="clearSearch"
            title="清除"
          >
            <i class="el-icon-close"></i>
          </button>
          <button
            class="search-button"
            @click="handleSearch"
            title="搜索"
          >
            <i class="el-icon-search"></i>
            <span>搜索</span>
          </button>
        </div>

        <el-select
          v-model="authType"
          placeholder="认证类型"
          clearable
          class="filter-select"
          @change="handleSearch"
        >
          <el-option
            v-for="type in authTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          >
            <i :class="type.icon"></i>
            <span style="margin-left: 8px;">{{ type.label }}</span>
          </el-option>
        </el-select>
      </div>
    </div>

    <!-- 应用列表 -->
    <div
      class="app-container"
      v-loading="loading"
      v-infinite-scroll="loadMore"
      :infinite-scroll-disabled="noMore"
      :infinite-scroll-distance="200"
      :infinite-scroll-immediate="false"
      style="overflow:auto"
    >
      <!-- 网格视图 -->
      <div class="grid-view">
        <div class="app-grid">
          <!-- 新增应用卡片 -->
          <div class="app-card create-card" @click="$router.push('/integration/sso-apps/add')">
            <div class="create-card-content">
              <div class="create-icon">
                <i class="el-icon-plus"></i>
              </div>
              <div class="create-text">
                <h3>接入新应用</h3>
                <div class="create-options">
                  <div class="create-option" @click.stop="$router.push('/integration/sso-apps/add')">
                    <i class="el-icon-document"></i>
                    <span>创建OAuth应用</span>
                  </div>
                  <div class="create-option" @click.stop="$router.push('/integration/sso-apps/add')">
                    <i class="el-icon-key"></i>
                    <span>创建SAML应用</span>
                  </div>
                  <div class="create-option" @click.stop="$router.push('/integration/sso-apps/add')">
                    <i class="el-icon-office-building"></i>
                    <span>创建LDAP应用</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 应用卡片 -->
          <div
            v-for="app in filteredApps"
            :key="app.id"
            class="app-card"
            @click="handleEdit(app)"
          >
            <div class="app-card-header">
              <div class="app-icon" :style="getAppStyle(app)">
                <span class="app-letter">{{ app.name.charAt(0) }}</span>
              </div>

              <div class="app-info">
                <div class="app-title">
                  <h3 class="app-name" :title="app.name">{{ app.name }}</h3>
                </div>
              </div>

              <div class="app-type-badge" :class="getTypeBadgeClass(app.authType)">
                <i :class="getAuthTypeIcon(app.authType)" style="margin-right: 4px;"></i>
                <span class="type-text">{{ app.authType }}</span>
              </div>
            </div>

            <div class="app-card-body">
              <p class="app-description" :title="app.description">
                {{ app.description || '暂无描述' }}
              </p>

              <div class="app-tags">
                <div class="tags-left">
                  <el-tag
                    size="mini"
                    class="app-tag"
                  >
                    {{ app.authType }}
                  </el-tag>
                </div>
                <div class="app-status-badge status-enabled">
                  <i class="el-icon-check"></i>
                  <span class="status-text">启用</span>
                </div>
              </div>
            </div>

            <div class="app-card-footer">
              <span class="create-time">
                {{ formatDate(app.createTime) }}
              </span>
              <div class="app-actions">
                <el-button
                  type="text"
                  icon="el-icon-view"
                  size="mini"
                  @click.stop="handleEdit(app)"
                  title="查看详情"
                />
                <el-dropdown
                  @command="(command) => handleDropdownCommand(command, app)"
                  trigger="click"
                  @click.stop
                  placement="bottom-end"
                  popper-class="connector-dropdown-menu"
                >
                  <el-button
                    type="text"
                    icon="el-icon-more"
                    size="mini"
                    title="更多操作"
                    @click.stop
                    class="more-btn"
                  />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit" class="dropdown-item-custom">
                      <i class="el-icon-edit-outline"></i>
                      <span>编辑基础信息</span>
                    </el-dropdown-item>
                    <el-dropdown-item command="auth" class="dropdown-item-custom">
                      <i class="el-icon-key"></i>
                      <span>授权管理</span>
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided class="dropdown-item-custom delete-item">
                      <i class="el-icon-delete"></i>
                      <span>删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredApps.length === 0 && !loading" class="empty-state">
        <i class="el-icon-box"></i>
        <p>暂无应用数据</p>
        <p class="empty-tip">{{ searchKeyword ? '尝试调整搜索条件' : '暂时没有单点登录应用' }}</p>
      </div>

      <!-- 加载更多提示 -->
      <div v-if="noMore && filteredApps.length > 0" class="no-more">
        <span>已加载全部应用</span>
      </div>
    </div>

    <!-- 统计数据弹窗 -->
    <stats-dialog :visible.sync="statsDialogVisible" :app-name="currentApp.name" />

    <!-- 授权管理对话框 -->
    <el-dialog :title="`${currentApp.name} - 授权管理`" :visible.sync="authDialogVisible" width="1200px" class="auth-dialog"
      :close-on-click-modal="false" append-to-body>
      <div class="auth-content">
        <!-- 授权表单 -->
        <div class="auth-form">
          <div class="form-header">
            <div class="search-area">
              <el-input v-model="accountSearchKeyword" placeholder="搜索账号/用户名" prefix-icon="el-icon-search" clearable
                class="search-input" @input="debounceSearch" />
              <el-button type="primary" @click="handleBatchAuth" disabled>
                <i class="el-icon-plus"></i>
                添加授权账号
              </el-button>
            </div>
          </div>

          <!-- 已授权列表 -->
          <el-table :data="accountList" v-loading="loading" style="width: 100%">
            <el-table-column prop="accountNo" label="账号" min-width="120" />
            <el-table-column prop="accountName" label="用户名" min-width="120" />
            <el-table-column label="授权状态" width="120">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.enabled" @change="(val) => handleStatusChange(scope.row, val)"
                  active-color="#13ce66" inactive-color="#ff4949" />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template slot-scope="scope">
                {{ formatDate(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="lastLoginTime" label="最后登录" width="160">
              <template slot-scope="scope">
                {{ scope.row.lastLoginTime ? formatDate(scope.row.lastLoginTime) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" class="danger" @click="handleRevokeAuth(scope.row)">
                  <i class="el-icon-delete"></i>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination background layout="prev, pager, next" :total="total" :page-size="pageSize"
              :current-page.sync="currentPage" @current-change="handlePageChange" />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 授权用户对话框 -->
    <grant-auth-dialog :visible.sync="grantAuthVisible" :title="grantAuthTitle" :permission-type="currentPermissionType"
      @confirm="handleGrantAuth" />
  </div>
</template>

<script>
import StatsDialog from './components/stats-dialog.vue'
import GrantAuthDialog from './components/grant-auth-dialog.vue'
import {
  getAppList,
  getAppAccountList,
  updateAccountStatus,
  deleteAccount,
  deleteApp
} from '@system/api/integration/sso-app'

export default {
  name: 'SSOApps',
  components: {
    StatsDialog,
    GrantAuthDialog
  },
  data() {
    return {
      appList: [],
      statsDialogVisible: false,
      currentApp: {
        name: '',
        id: null
      },
      authDialogVisible: false,
      currentPermissionType: 'login',
      searchKeyword: '',
      accountSearchKeyword: '',
      authType: 'all',
      loading: false,
      noMore: false,
      loadingMore: false,
      authList: [
        {
          id: 1,
          name: '张三',
          type: 'user',
          permissions: ['login', 'notify'],
          grantTime: '2024-03-15 14:30:00',
          color: '#1890FF'
        },
        {
          id: 2,
          name: '管理员角色',
          type: 'role',
          permissions: ['login', 'notify', 'api'],
          grantTime: '2024-03-14 10:00:00',
          color: '#722ED1'
        },
        {
          id: 3,
          name: '李四',
          type: 'user',
          permissions: ['login'],
          grantTime: '2024-03-13 16:45:00',
          color: '#13C2C2'
        },
        {
          id: 4,
          name: '开发人员角色',
          type: 'role',
          permissions: ['login', 'api'],
          grantTime: '2024-03-12 09:15:00',
          color: '#52C41A'
        },
        {
          id: 5,
          name: '王五',
          type: 'user',
          permissions: ['login', 'notify'],
          grantTime: '2024-03-11 11:20:00',
          color: '#F5222D'
        },
        {
          id: 6,
          name: '运维角色',
          type: 'role',
          permissions: ['login', 'api', 'notify'],
          grantTime: '2024-03-10 15:30:00',
          color: '#FA8C16'
        },
        {
          id: 7,
          name: '赵六',
          type: 'user',
          permissions: ['login'],
          grantTime: '2024-03-09 11:20:00',
          color: '#EB2F96'
        },
        {
          id: 8,
          name: '测试人员角色',
          type: 'role',
          permissions: ['login', 'api'],
          grantTime: '2024-03-08 16:40:00',
          color: '#722ED1'
        },
        {
          id: 9,
          name: '钱七',
          type: 'user',
          permissions: ['login', 'notify'],
          grantTime: '2024-03-07 09:30:00',
          color: '#52C41A'
        },
        {
          id: 10,
          name: '孙八',
          type: 'user',
          permissions: ['login'],
          grantTime: '2024-03-06 14:20:00',
          color: '#1890FF'
        },
        {
          id: 11,
          name: '产品经理角色',
          type: 'role',
          permissions: ['login', 'notify'],
          grantTime: '2024-03-05 10:15:00',
          color: '#13C2C2'
        },
        {
          id: 12,
          name: '周九',
          type: 'user',
          permissions: ['login', 'api'],
          grantTime: '2024-03-04 16:50:00',
          color: '#F5222D'
        },
        {
          id: 13,
          name: '吴十',
          type: 'user',
          permissions: ['login', 'notify'],
          grantTime: '2024-03-03 11:40:00',
          color: '#722ED1'
        },
        {
          id: 14,
          name: '市场部角色',
          type: 'role',
          permissions: ['login'],
          grantTime: '2024-03-02 09:20:00',
          color: '#52C41A'
        },
        {
          id: 15,
          name: '郑十一',
          type: 'user',
          permissions: ['login', 'api', 'notify'],
          grantTime: '2024-03-01 15:10:00',
          color: '#1890FF'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      grantAuthVisible: false,
      grantAuthTitle: '',
      accountList: [],
      total: 0,
      searchTimer: null,
      authTypes: [
        { value: 'OAuth', label: 'OAuth认证', icon: 'el-icon-connection' },
        { value: 'SAML', label: 'SAML认证', icon: 'el-icon-key' },
        { value: 'LDAP', label: 'LDAP认证', icon: 'el-icon-office-building' }
      ]
    }
  },
  created() {
    this.fetchAppList()
  },

  computed: {
    filteredAuthList() {
      // 首先按权限类型过滤
      const permissionFiltered = this.authList.filter(item => {
        return item.permissions.includes(this.currentPermissionType)
      })

      // 后按搜索条件过滤
      const searchFiltered = permissionFiltered.filter(item => {
        const matchKeyword = item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
        const matchType = this.authType === 'all' || item.type === this.authType
        return matchKeyword && matchType
      })

      // 最后处理分页
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return searchFiltered.slice(start, end)
    },
    filteredApps() {
      if (!this.searchKeyword && this.authType === 'all') {
        return this.appList;
      }
      
      const keyword = this.searchKeyword.toLowerCase();
      return this.appList.filter(app => {
        const matchKeyword = !keyword || 
          app.name.toLowerCase().includes(keyword) || 
          (app.description && app.description.toLowerCase().includes(keyword));
        
        const matchType = this.authType === 'all' || app.authType === this.authType;
        
        return matchKeyword && matchType;
      });
    },
    filteredTotal() {
      return this.authList.filter(item => {
        const matchPermission = item.permissions.includes(this.currentPermissionType)
        const matchKeyword = item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
        const matchType = this.authType === 'all' || item.type === this.authType
        return matchPermission && matchKeyword && matchType
      }).length
    }
  },
  methods: {
    // 获取应用列表
    async fetchAppList() {
      try {
        this.loading = true
        const data = await getAppList({ enabled: true })
        this.appList = data?.records?.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          authType: item.protocol, // 认证类型使用 protocol 字段
          createTime: item.createTime,
          accessCount: item.accessCount,
          accountCount: item.accountCount,
          color: this.getRandomColor(), // 随机生成颜色
          icon: item.icon
        })) || []
        
        // 初始化状态
        this.noMore = true;
      } catch (error) {
        this.appList = []
        console.error('获取应用列表失败:', error)
        this.$message.error('获取应用列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      this.fetchAppList()
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.fetchAppList()
    },

    // 处理加载更多 (暂时保持不变，实际上目前是一次性加载全部)
    loadMore() {
      // 在实际应用中，这里应该实现分页加载
      console.log('加载更多数据')
    },

    // 处理下拉菜单命令
    handleDropdownCommand(command, app) {
      switch (command) {
        case 'edit':
          this.handleEdit(app)
          break
        case 'auth':
          this.handleAuth(app)
          break
        case 'delete':
          this.handleDelete(app)
          break
        default:
          console.log('未知命令:', command)
      }
    },

    // 获取应用类型样式
    getTypeBadgeClass(type) {
      const classMap = {
        'OAuth': 'badge-proxy',
        'SAML': 'badge-flow',
        'LDAP': 'badge-ldap'
      }
      return classMap[type] || 'badge-default'
    },

    // 生成随机颜色
    getRandomColor() {
      const colors = [
        '#1890FF', '#722ED1', '#13C2C2', '#52C41A',
        '#F5222D', '#FA8C16', '#EB2F96', '#722ED1'
      ]
      return colors[Math.floor(Math.random() * colors.length)]
    },

    getAppStyle(app) {
      return {
        background: `linear-gradient(135deg, ${app.color} 0%, ${this.adjustColor(app.color, -20)} 100%)`
      }
    },
    adjustColor(color, amount) {
      return color // 简化版本，实际应该实现颜色调整逻辑
    },
    getAuthTypeIcon(type) {
      const icons = {
        'OAuth': 'el-icon-connection',
        'SAML': 'el-icon-key',
        'LDAP': 'el-icon-office-building'
      }
      return icons[type] || 'el-icon-share'
    },
    formatDate(date) {
      if (!date) return '-'
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      const seconds = String(d.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    formatNumber(num) {
      if (!num) return '0'
      if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    },
    handleEdit(app) {
      this.$router.push(`/integration/sso-apps/edit/${app.id}`)
    },
    handleViewStats(app) {
      if (!app) return
      this.currentApp = { ...app }
      this.statsDialogVisible = true
    },
    handleDelete(app) {
      this.$confirm(
        `确定要删除应用"${app.name}"吗？此操作将断开所有用户的单点登录连接。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deleteApp(app.id)
          this.$message.success('删除成功')
          this.fetchAppList()
        } catch (e) {
          this.$message.success('删除失败')
        }
      }).catch(() => { })
    },
    handleAuth(app) {
      this.currentApp = app
      this.authDialogVisible = true
      this.fetchAccountList()
    },
    handleBatchAuth() {
      this.$message.info('功能开发中...')
    },
    handleRevokeAuth(auth) {
      this.$confirm(`确定要撤销 ${auth.name} 的授权吗？`, '提示', {
        type: 'warning'
      }).then(() => {
        this.authList = this.authList.filter(item => item.id !== auth.id)
        this.$message.success('已撤销授权')
      }).catch(() => { })
    },
    handlePageChange(page) {
      this.currentPage = page
      this.fetchAccountList()
    },
    handlePermissionTypeChange(type) {
      this.currentPermissionType = type
      this.currentPage = 1 // 切换权限类型时重置页码
    },
    debounceSearch(val) {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.handleSearch()
      }, 300)
    },
    handleSearch() {
      this.currentPage = 1
      this.fetchAppList()
    },
    handleAuthTypeChange() {
      this.currentPage = 1 // 切换授权对象类型时重置页码
    },
    getPermissionLabel(perm) {
      const labels = {
        login: '登录权限',
        notify: '通知权限',
        api: '接口权限'
      }
      return labels[perm] || perm
    },
    getPermissionIcon(perm) {
      const icons = {
        login: 'el-icon-key',
        notify: 'el-icon-bell',
        api: 'el-icon-connection'
      }
      return icons[perm]
    },
    getPermissionTagType(perm) {
      const types = {
        login: '',
        notify: 'success',
        api: 'warning'
      }
      return types[perm]
    },
    handleGrantAuth({ items, permissionType }) {
      // 构造新的授权记录
      const newAuthList = items.map(item => ({
        id: Date.now() + Math.random(),
        name: item.name,
        type: item.type,
        permissions: [permissionType],
        grantTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        color: item.color
      }))

      // 添加到授权列表
      this.authList.unshift(...newAuthList)
      this.$message.success(`已成功授权 ${items.length} 个对象`)
    },
    // 修改获取授权账号列表方法
    async fetchAccountList() {
      try {
        this.loading = true
        const params = {
          appId: this.currentApp.id,
          page: this.currentPage,
          size: this.pageSize
        }

        // 使用 accountSearchKeyword 替代 searchKeyword
        if (this.accountSearchKeyword && this.accountSearchKeyword.trim()) {
          params.keyword = this.accountSearchKeyword.trim()
        }

        const data = await getAppAccountList(params)

        this.accountList = data?.records || []
        this.total = data?.total || 0

      } catch (error) {
        console.error('获取授权账号列表失败:', error)
        this.$message.error('获取授权账号列表失败')
      } finally {
        this.loading = false
      }
    },

    // 修改状态变更方法
    async handleStatusChange(row, status) {
      try {
        await updateAccountStatus({
          id: row.id,
          enabled: status
        })
        this.$message.success(`${status ? '启用' : '停用'}成功`)
      } catch (error) {
        row.enabled = !status // 恢复状态
        this.$message.error(`${status ? '启用' : '停用'}失败`)
      }
    },

    // 修改删除授权方法
    handleRevokeAuth(row) {
      this.$confirm('确定要删除该授权账号吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          await deleteAccount(row.id)
          this.$message.success('删除成功')
          this.fetchAccountList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => { })
    }
  }
}
</script>

<style lang="scss" scoped>
.sso-apps {
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #f0f2f5 100%);
  min-height: calc(100vh - 120px);

  .header-section {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    margin-bottom: 24px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      pointer-events: none;
    }

    .search-filter-bar {
      display: flex;
      align-items: center;
      gap: 32px;
      flex-wrap: wrap;
      position: relative;
      z-index: 1;

      .custom-search-input {
        flex: 1;
        min-width: 280px;
        max-width: 380px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        overflow: hidden;

        &:hover {
          transform: translateY(-1px);
          border-color: rgba(24, 144, 255, 0.3);
          background: rgba(255, 255, 255, 0.95);
        }

        &:focus-within {
          transform: translateY(-1px);
          border-color: #1890ff;
          background: white;
          box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .search-input-field {
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          padding: 0 16px;
          height: 40px;
          font-size: 14px;
          color: #262626;
          caret-color: #1890ff;

          &::placeholder {
            color: #8c8c8c;
            font-size: 13px;
          }
        }

        .clear-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          border: none;
          background: transparent;
          color: #8c8c8c;
          cursor: pointer;
          border-radius: 50%;
          margin-right: 8px;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.06);
            color: #262626;
          }

          i {
            font-size: 14px;
          }
        }

        .search-button {
          display: flex;
          align-items: center;
          gap: 6px;
          border: none;
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          color: white;
          padding: 0 16px;
          height: 40px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
          font-size: 14px;
          font-weight: 500;

          &:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          }

          &:active {
            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
          }

          i {
            font-size: 16px;
          }

          span {
            font-size: 14px;
          }
        }
      }

      .filter-select {
        min-width: 150px;

        ::v-deep .el-input__inner {
          background: rgba(255, 255, 255, 0.9);
          border: 1px solid rgba(0, 0, 0, 0.08);
          border-radius: 12px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          height: 40px;
          font-size: 14px;
          backdrop-filter: blur(8px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          cursor: pointer;
          caret-color: transparent;

          &:hover {
            border-color: rgba(24, 144, 255, 0.3);
            background: rgba(255, 255, 255, 0.95);
          }

          &:focus {
            border-color: #1890ff;
            background: white;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
            caret-color: transparent;
          }
        }

        ::v-deep .el-input__suffix {
          color: #8c8c8c;
          transition: color 0.3s ease;
          pointer-events: none;
        }

        &:hover {
          ::v-deep .el-input__suffix {
            color: #1890ff;
          }
        }
      }
    }
  }

  .app-container {
    min-height: 400px;

    // 网格视图样式
    .grid-view {
      .app-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
        padding: 4px;
      }

      .app-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        height: 200px;
        display: flex;
        flex-direction: column;
        border: 1px solid rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;

        // 新增卡片样式
        &.create-card {
          border: 2px dashed #d9d9d9;
          background: #fafafa;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            border-color: #1890ff;
            background: #f6f8ff;
            transform: translateY(-2px);

            .create-icon {
              color: #1890ff;
              transform: scale(1.1);
            }
          }

          .create-card-content {
            text-align: center;
            width: 100%;

            .create-icon {
              font-size: 32px;
              color: #bfbfbf;
              margin-bottom: 16px;
              transition: all 0.3s ease;
            }

            .create-text {
              h3 {
                font-size: 16px;
                color: #262626;
                margin: 0 0 16px 0;
                font-weight: 600;
              }

              .create-options {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .create-option {
                  display: flex;
                  align-items: center;
                  padding: 8px 12px;
                  border-radius: 6px;
                  background: white;
                  border: 1px solid #e8e8e8;
                  transition: all 0.2s ease;
                  cursor: pointer;

                  &:hover {
                    border-color: #1890ff;
                    background: #f6f8ff;
                    transform: translateX(4px);
                  }

                  i {
                    font-size: 14px;
                    color: #666;
                    margin-right: 8px;
                    width: 16px;
                  }

                  span {
                    font-size: 12px;
                    color: #666;
                    font-weight: 500;
                  }
                }
              }
            }
          }
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          transform: translateY(-4px);
          border-color: rgba(24, 144, 255, 0.2);

          &::before {
            opacity: 1;
          }
        }

        .app-card-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 10px;
          position: relative;

          .app-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f2f5;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            flex-shrink: 0;

            .app-letter {
              font-size: 20px;
              font-weight: 500;
              color: #fff;
              text-transform: uppercase;
            }
          }

          .app-info {
            flex: 1;
            min-width: 0;

            .app-title {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .app-name {
                font-size: 14px;
                font-weight: 600;
                color: #262626;
                margin: 0;
                flex: 1;
                min-width: 0;
                line-height: 1.3;
                word-break: break-word;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }
          }

          .app-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            flex-shrink: 0;
            align-self: flex-start;

            &.badge-proxy {
              background: #e6f7ff;
              color: #1890ff;
            }

            &.badge-flow {
              background: #fff7e6;
              color: #fa8c16;
            }

            &.badge-ldap {
              background: #f0f5ff;
              color: #722ed1;
            }

            &.badge-default {
              background: #f0f0f0;
              color: #666;
            }

            .type-text {
              font-size: 11px;
            }
          }
        }

        .app-card-body {
          flex: 1;
          display: flex;
          flex-direction: column;

          .app-description {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            margin: 0 0 12px 0;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .app-tags {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;

            .tags-left {
              display: flex;
              flex-wrap: wrap;
              gap: 6px;
              flex: 1;
              min-width: 0;

              .app-tag {
                font-size: 10px;
                height: 20px;
                line-height: 18px;
                padding: 0 6px;
                border: none;
                background: linear-gradient(135deg, #f6f8fa, #e8e8e8);
                color: #586069;
                border-radius: 10px;
                font-weight: 500;
                transition: all 0.2s ease;

                &:hover {
                  background: linear-gradient(135deg, #e8e8e8, #d9d9d9);
                  transform: translateY(-1px);
                }
              }
            }

            .app-status-badge {
              display: inline-flex;
              align-items: center;
              gap: 4px;
              padding: 3px 8px;
              border-radius: 12px;
              font-size: 10px;
              font-weight: 600;
              transition: all 0.3s ease;
              margin-left: 8px;
              flex-shrink: 0;

              i {
                font-size: 10px;
              }

              .status-text {
                font-weight: 600;
              }

              &.status-enabled {
                background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
                color: #ffffff;
                box-shadow: 0 1px 4px rgba(82, 196, 26, 0.3);

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.4);
                }
              }

              &.status-disabled {
                background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
                color: #ffffff;
                box-shadow: 0 1px 4px rgba(255, 77, 79, 0.3);

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.4);
                }
              }
            }
          }
        }

        .app-card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12px;
          padding-top: 8px;
          border-top: 1px solid #f0f0f0;

          .create-time {
            font-size: 12px;
            color: #999;
            font-weight: 500;
          }

          .app-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 6px 10px;
              color: #8c8c8c;
              border-radius: 6px;
              transition: all 0.2s ease;

              &:hover {
                color: #1890ff;
                background: rgba(24, 144, 255, 0.1);
                transform: scale(1.05);
              }
            }

            .more-btn {
              padding: 4px;
              border-radius: 4px;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(24, 144, 255, 0.1);
                color: #1890ff;
              }

              i {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    // 加载状态样式
    .loading-more,
    .no-more {
      text-align: center;
      padding: 20px;
      color: #999;
      font-size: 14px;

      i {
        margin-right: 8px;
      }
    }

    .loading-more {
      color: #1890ff;
    }

    // 空状态样式
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #8c8c8c;

      i {
        font-size: 64px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }

      p {
        margin: 8px 0;
        font-size: 14px;

        &.empty-tip {
          font-size: 12px;
          color: #bfbfbf;
        }
      }
    }
  }
}

// 授权对话框样式
.auth-dialog {
  ::v-deep .el-dialog {
    border-radius: 8px;
    overflow: hidden;
    margin-top: 15vh !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  ::v-deep .el-dialog__body {
    padding: 0;
  }

  .auth-content {
    padding: 24px;
    min-height: 400px;

    .auth-form {
      .form-header {
        margin-bottom: 24px;

        .search-area {
          display: flex;
          gap: 16px;
          align-items: center;

          .search-input {
            width: 360px;
          }
        }
      }

      .pagination-container {
        margin-top: 24px;
        text-align: right;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sso-apps {
    padding: 12px;

    .header-section {
      padding: 20px 16px;
      margin-bottom: 20px;

      .search-filter-bar {
        flex-direction: column;
        gap: 24px;
        align-items: stretch;

        .custom-search-input {
          min-width: auto;
          max-width: none;
        }

        .filter-select {
          min-width: auto;
        }
      }
    }

    .grid-view {
      .app-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .app-card {
        height: auto;
        min-height: 180px;
        padding: 14px;
      }
    }
  }
}

// 全局下拉菜单样式
::v-deep .el-dropdown-menu {
  border: none;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin-top: 4px;

  .el-dropdown-menu__item {
    padding: 12px 16px;
    font-size: 14px;
    color: #262626;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 8px;

    &:hover {
      background: rgba(24, 144, 255, 0.08);
      color: #1890ff;
      transform: translateX(2px);
    }

    &.is-disabled {
      color: #c0c4cc;
    }

    i {
      margin-right: 8px;
      font-size: 14px;
    }
  }
}

// 全局下拉选择器样式
::v-deep .el-select-dropdown {
  border: none;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin-top: 4px;

  .el-select-dropdown__item {
    padding: 12px 16px;
    font-size: 14px;
    color: #262626;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 8px;

    &:hover {
      background: rgba(24, 144, 255, 0.08);
      color: #1890ff;
      transform: translateX(2px);
    }

    &.selected {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      font-weight: 500;

      &::after {
        color: #1890ff;
      }
    }

    i {
      margin-right: 8px;
      font-size: 14px;
    }
  }

  .el-scrollbar__view {
    padding: 0;
  }
}

// 多选标签样式
::v-deep .el-tag {
  &.el-tag--info {
    background: rgba(24, 144, 255, 0.1);
    border-color: rgba(24, 144, 255, 0.2);
    color: #1890ff;
    border-radius: 6px;
    font-size: 12px;
    height: 24px;
    line-height: 22px;
    padding: 0 8px;

    .el-tag__close {
      color: #1890ff;

      &:hover {
        background: rgba(24, 144, 255, 0.2);
        color: white;
      }
    }
  }
}

// 下拉菜单美化样式
::v-deep .connector-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  padding: 4px 0;
  min-width: 140px;

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.4;
    transition: all 0.2s ease;

    &.dropdown-item-custom {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 14px;
        width: 14px;
        text-align: center;
      }

      span {
        flex: 1;
      }
    }

    &:hover {
      background: #f6f8fa;
      color: #1890ff;
    }

    &.delete-item:hover {
      background: #fff2f0;
      color: #ff4d4f;
    }

    &:not(:last-child) {
      margin-bottom: 2px;
    }
  }

  .popper__arrow {
    border-bottom-color: #e8e8e8;
  }

  .popper__arrow::after {
    border-bottom-color: #fff;
  }
}
</style>

