import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

Vue.config.productionTip = false
// 全局注册 Element UI
Vue.use(ElementUI)

import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [


  {
    path: '/chat',
    name: 'Chat',
    component: () => import('./views/chat/index.vue'),
  },
  {
    path: '/',
    redirect: '/chat',
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
