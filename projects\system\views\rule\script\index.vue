<template>
  <div class="script-container">
    <div class="page-header">
      <div class="header-title">
        <h2>组件管理</h2>
        <el-button 
          v-if="fromChain" 
          type="text" 
          icon="el-icon-back" 
          class="back-btn"
          @click="goBackToChain"
        >
          返回编排链
        </el-button>
      </div>
      
      <div class="header-tools">
        <div class="unified-search">
          <el-input v-model="listQuery.keyword" placeholder="请输入组件名称或组件ID" prefix-icon="el-icon-search" clearable
            class="search-input" @keyup.enter.native="getList" @clear="getList">
            <el-button slot="append" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-input>

          <el-select v-model="listQuery.scriptType" placeholder="组件类型" clearable class="status-select"
            @change="getList">
            <el-option label="switch_script" value="switch_script" />
            <el-option label="boolean_script" value="boolean_script" />
            <el-option label="for_script" value="for_script" />
          </el-select>

          <el-select v-model="listQuery.enabled" placeholder="状态" clearable class="status-select" @change="getList">
            <el-option label="启用" :value="true">
              <i class="el-icon-check" style="color: #67C23A" />
              <span style="margin-left: 8px">启用</span>
            </el-option>
            <el-option label="禁用" :value="false">
              <i class="el-icon-close" style="color: #F56C6C" />
              <span style="margin-left: 8px">禁用</span>
            </el-option>
          </el-select>
        </div>

        <div class="button-group">
          <el-button type="primary" icon="el-icon-refresh" @click="getList">
            刷新
          </el-button>
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
            新增组件
          </el-button>
          <el-button type="warning" icon="el-icon-check" @click="handleBatchValidate" :loading="batchValidating">
            批量校验
          </el-button>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <div v-if="fromChain && $route.query['enums[scriptId]']" class="filter-info">
        <i class="el-icon-info"></i>
        <span>当前仅显示规则链中引用的 {{ scriptList.length }} 个组件节点</span>
      </div>
      
      <el-table v-loading="loading" :data="scriptList" border stripe fit style="width: 100%" highlight-current-row>
        <el-table-column prop="scriptId" label="组件ID" width="250">
          <template slot-scope="scope">
            <div class="script-cell" :class="{ 'invalid-script': scope.row.validateStatus === false }">
              <i class="el-icon-document-checked" />
              <span>{{ scope.row.scriptId }}</span>
              <el-tag v-if="scope.row.validateStatus === false" type="danger" size="mini">校验失败</el-tag>
              <el-tag v-if="scope.row.validateStatus === true" type="success" size="mini">校验通过</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="scriptName" label="组件名称" min-width="120" />
        <el-table-column prop="scriptType" label="组件类型" width="130" align="center">
          <template slot-scope="scope">
            <el-tag>{{ scope.row.scriptType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scriptLanguage" label="组件语言" width="120" align="center">
          <template slot-scope="scope">
            <el-tag type="success" effect="plain">
              {{ scope.row.scriptLanguage }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" show-overflow-tooltip min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
            <span v-else class="no-desc">暂无备注</span>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.enabled" @change="handleStatusChange(scope.row)" active-color="#13ce66"
              inactive-color="#ff4949" />
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-check" class="validate-btn"
              :loading="scope.row.validating" @click="handleValidate(scope.row)">
              校验
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" class="delete-btn"
              @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination :current-page="listQuery.current" :page-sizes="[10, 20, 30, 50]" :page-size="listQuery.size"
          :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getRuleScriptList,
  updateRuleScript,
  deleteRuleScript,
  validateScript,
  getApplicationList
} from '@system/api/rule/script'

export default {
  name: 'RuleScript',
  data() {
    return {
      loading: false,
      batchValidating: false,
      scriptList: [],
      total: 0,
      listQuery: {
        current: 1,
        size: 10,
        keyword: '',
        scriptType: '',
        enabled: '',
        application: 'extreme-flow'
      },
      applicationList: [],
      fromChain: false
    }
  },
  created() {
    // 检查是否有来自规则链页面的查询参数
    if (this.$route.query.keyword) {
      this.listQuery.keyword = this.$route.query.keyword
      console.log('从规则链页面接收到关键词参数:', this.listQuery.keyword)
    }
    
    // 处理节点列表参数 enums[scriptId]
    if (this.$route.query['enums[scriptId]']) {
      const scriptIds = this.$route.query['enums[scriptId]']
      console.log('从规则链页面接收到节点列表:', scriptIds)
      
      // 设置scriptId枚举参数
      this.listQuery['enums[scriptId]'] = scriptIds
      
      // 清除关键词，使用枚举替代
      delete this.listQuery.keyword
    }
    
    if (this.$route.query.from === 'chain') {
      this.fromChain = true
      console.log('来源标记为规则链页面')
    }
    
    this.getList()
  },
  watch: {
    // 监听路由变化，处理可能的参数更新
    '$route.query': {
      handler(newQuery) {
        let needRefresh = false
        
        if (newQuery.keyword && newQuery.keyword !== this.listQuery.keyword) {
          console.log('检测到URL参数变化，更新关键词:', newQuery.keyword)
          this.listQuery.keyword = newQuery.keyword
          needRefresh = true
        }
        
        if (newQuery['enums[scriptId]'] && newQuery['enums[scriptId]'] !== this.listQuery['enums[scriptId]']) {
          console.log('检测到节点列表参数变化:', newQuery['enums[scriptId]'])
          this.listQuery['enums[scriptId]'] = newQuery['enums[scriptId]']
          delete this.listQuery.keyword
          needRefresh = true
        }
        
        this.fromChain = newQuery.from === 'chain'
        
        if (needRefresh) {
          this.getList()
        }
      },
      deep: true
    }
  },
  methods: {
    goBackToChain() {
      this.$router.push('/rule/chain')
    },
    async getList() {
      try {
        console.log('正在获取组件列表，参数:', this.listQuery)
        this.loading = true
        const params = { ...this.listQuery }
        
        // 始终使用 extreme-flow 作为应用服务
        params.application = 'extreme-flow'
        
        // 清除空字符串参数
        Object.keys(params).forEach(key => {
          if (params[key] === '') {
            delete params[key]
          }
        })

        const data = await getRuleScriptList(params)
        this.scriptList = data.records
        this.total = data.total
        
        if (this.fromChain && this.scriptList.length > 0) {
          this.$nextTick(() => {
            // 滚动到列表顶部
            const tableBody = document.querySelector('.el-table__body-wrapper')
            if (tableBody) {
              tableBody.scrollTop = 0
            }
            
            // 添加高亮效果
            if (this.scriptList.length === 1) {
              // 如果只有一个结果，高亮显示
              const firstRow = document.querySelector('.el-table__row')
              if (firstRow) {
                firstRow.classList.add('highlight-row')
              }
            }
          })
        }
      } catch (error) {
        console.error('获取组件列表失败:', error)
        this.$message.error('获取组件列表失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    handleAdd() {
      this.$router.push('/rule/script/add')
    },
    handleEdit(row) {
      this.$router.push(`/rule/script/edit/${row.id}`)
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该组件吗？删除后不可恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteRuleScript(row.id)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除组件失败:', error)
          this.$message.error('删除组件失败：' + (error.message || '未知错误'))
        }
      }
    },
    async handleStatusChange(row) {
      try {
        await updateRuleScript({
          ...row,
          enabled: row.enabled
        })
        this.$message.success(`${row.enabled ? '启用' : '禁用'}成功`)
      } catch (error) {
        console.error('更新状态失败:', error)
        row.enabled = !row.enabled // 恢复状态
        this.$message.error(`${row.enabled ? '启用' : '禁用'}失败：` + (error.message || '未知错误'))
      }
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.listQuery.current = 1 // 修复分页BUG，改变每页显示数量时重置为第一页
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.current = val
      this.getList()
    },
    async handleValidate(row) {
      if (!row.scriptData) {
        this.$message.warning('组件内容为空')
        return
      }

      this.$set(row, 'validating', true)
      try {
        await validateScript({
          script: row.scriptData,
          type: row.scriptLanguage.toLowerCase()
        })
        this.$message.success('组件验证通过')
        this.$set(row, 'validateStatus', true)
      } catch (error) {
        this.$set(row, 'validateStatus', false)
      } finally {
        this.$set(row, 'validating', false)
      }
    },
    // 批量校验所有组件
    async handleBatchValidate() {
      if (this.batchValidating) return
      if (this.scriptList.length === 0) {
        this.$message.warning('没有可校验的组件')
        return
      }

      this.batchValidating = true

      try {
        const promises = this.scriptList.map(async (row) => {
          if (!row.scriptData) {
            // 如果组件内容为空，跳过校验
            return { id: row.id, success: false, skipped: true }
          }

          try {
            this.$set(row, 'validating', true)
            await validateScript({
              script: row.scriptData,
              type: row.scriptLanguage.toLowerCase()
            })
            this.$set(row, 'validateStatus', true)
            return { id: row.id, success: true }
          } catch (error) {
            this.$set(row, 'validateStatus', false)
            return { id: row.id, success: false, error: error.message || '未知错误' }
          } finally {
            this.$set(row, 'validating', false)
          }
        })

        const results = await Promise.all(promises)
        const failCount = results.filter(r => !r.success && !r.skipped).length
        const skipCount = results.filter(r => r.skipped).length

        if (failCount === 0 && skipCount === 0) {
          this.$message.success('所有组件校验通过')
        } else if (failCount === 0 && skipCount > 0) {
          this.$message.warning(`校验完成，${skipCount} 个组件因内容为空而被跳过`)
        } else {
          this.$message.warning(`校验完成，${failCount} 个组件校验失败，${skipCount} 个组件被跳过`)
        }
      } catch (error) {
        this.$message.error('批量校验过程中发生错误：' + (error.message || '未知错误'))
      } finally {
        this.batchValidating = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.script-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
      
      .back-btn {
        margin-left: 10px;
        font-size: 14px;
        color: #409EFF;
        
        &:hover {
          color: #66b1ff;
        }
        
        i {
          margin-right: 4px;
        }
      }
    }
    
    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .unified-search {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .search-input {
          width: 250px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;
              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;

              .el-button {
                margin: 0;
                height: 34px;
                border: none;
                padding: 0 16px;
                border-radius: 0 8px 8px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
          }
        }

        .status-select {
          width: 120px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              border-radius: 8px;
              background: #f9fafc;
              border: 1px solid #e0e5ee;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }
          }
        }
      }
      
      .button-group {
        display: flex;
        gap: 12px;
        
        .el-button {
          padding: 8px 16px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 8px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          height: 36px;
          font-size: 14px;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    .filter-info {
      background-color: #ecf8ff;
      padding: 12px 16px;
      margin-bottom: 16px;
      border-radius: 8px;
      border-left: 4px solid #409EFF;
      font-size: 14px;
      color: #1a1f36;
      display: flex;
      align-items: center;
      
      i {
        color: #409EFF;
        font-size: 16px;
        margin-right: 8px;
      }
    }

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;

        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;

          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        td {
          padding: 8px 0;

          .cell {
            line-height: 1.5;
          }
        }
      }

      tr {
        transition: all 0.3s;

        &:hover {
          background: #f7f9fc !important;
        }

        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
        
        &.highlight-row {
          td {
            background: #ecf5ff !important;
            animation: highlight-pulse 2s infinite;
          }
        }
      }

      .script-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #409EFF;
          font-size: 16px;
        }

        span {
          color: #1a1f36;
          font-weight: 500;
        }

        &.invalid-script {

          i,
          span {
            color: #F56C6C;
          }
        }

        .el-tag--mini {
          margin-left: 6px;
          height: 20px;
          line-height: 18px;
          padding: 0 6px;
        }
      }

      .no-desc {
        color: #909399;
        font-style: italic;
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;

      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;

        .btn-prev,
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;

          &:not(.disabled):hover {
            border-color: #409EFF;
          }

          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }

        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }

        .el-pagination__jump {
          margin-left: 16px;
        }

        .el-select .el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }

        .el-pagination__editor.el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }

  .editor-container {
    height: 200px;
    border: 1px solid #dcdfe6;
    margin: 5px 0;
    border-radius: 4px;
  }

  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    .el-dialog__header {
      padding: 16px 20px;
      background: linear-gradient(to right, #f7f9fc, #f3f6fa);
      border-bottom: 1px solid #eef1f7;

      .el-dialog__title {
        font-weight: 600;
        color: #1a1f36;
        font-size: 18px;
      }
    }

    .el-dialog__body {
      padding: 20px;
      padding-top: 20px;
    }

    .el-dialog__footer {
      padding: 12px 20px;
      border-top: 1px solid #eef1f7;

      .el-button {
        padding: 8px 20px;
        border-radius: 8px;

        &--primary {
          background-color: #409EFF;
          border-color: #409EFF;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);

          &:hover {
            background-color: #5aacff;
            border-color: #5aacff;
          }
        }
      }
    }
  }

  ::v-deep .el-tag {
    text-transform: capitalize;

    &.el-tag--primary {
      background-color: #409EFF;
      border-color: #409EFF;
    }

    &.el-tag--success {
      background-color: #67C23A;
      border-color: #67C23A;
      background-color: transparent;
      color: #67C23A !important;

      &.el-tag--plain {
        color: #67C23A;
        background-color: #f0f9eb;
      }
    }

    &.el-tag--warning {
      background-color: #E6A23C;
      border-color: #E6A23C;
    }

    &.el-tag--danger {
      color: #F56C6C;
      border-color: #F56C6C;
      background-color: transparent;
    }
  }

  .delete-btn {
    color: #F56C6C;

    &:hover {
      color: #f78989;
    }
  }

  .validate-btn {
    color: #E6A23C;
    margin: 0 10px;

    &:hover {
      color: #ebb563;
    }
  }
}

@keyframes highlight-pulse {
  0% {
    background-color: #ecf5ff !important;
  }
  50% {
    background-color: #d0e6ff !important;
  }
  100% {
    background-color: #ecf5ff !important;
  }
}
</style>
