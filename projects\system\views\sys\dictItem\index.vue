<template>
  <div>
    <div class="container">
      <div class="top_div">
        <div>
          <div class="back_list" @click="backList">{{ `< 返回列表` }}</div>
              <div class="row_title">
                <span class="option_title">数据字典</span>
                <el-select class="div_option" v-model="valueDictId" filterable>
                  <el-option v-for="item in optionsDict" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </div>
          </div>
        </div>
        <div class="center_div">
          <div class="row_btn_left">
            <el-row :gutter="10" style="flex: 1">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                  :disabled="isEditState">新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="!multiple"
                  @click="handleDelete">批量删除</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-check" size="mini" :disabled="!isSaveAll"
                  @click="handleSavaAll">批量保存</el-button>
              </el-col>
            </el-row>
          </div>
          <div class="row_btn_right">
            <el-row :gutter="10">
              <el-col :span="1.5" v-show="!isEditState">
                <el-button type="primary" plain icon="el-icon-edit" size="mini"
                  @click="handleEditState">编辑模式</el-button>
              </el-col>
              <el-col :span="1.5" v-show="isEditState">
                <el-button type="success" plain icon="el-icon-check" size="mini"
                  @click="saveChanges(true)">保存修改</el-button>
              </el-col>
              <el-col :span="1.5" v-show="isEditState">
                <el-button type="info" plain icon="el-icon-close" size="mini" @click="saveChanges(false)">取消</el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="bottom_div">
          <el-table :data="tableData" ref="mTable" @selection-change="handleSelectionChange">
            <el-table-column type="selection" align="center" />
            <el-table-column prop="key" label="字典值" align="center">
              <template slot-scope="scope">
                <EditableInput :isEditing="isEditState" :isNew="scope && scope.row.isNew" :value="scope.row.value"
                  :placeholder="`请输入字典值`" :width="`150px`" @change="
                    (value) => {
                      scope.row.value = value;
                    }
                  " @tempChange="
                    (value) => {
                      scope.row.tempValue = value;
                    }
                  "></EditableInput>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="标签" align="center">
              <template slot-scope="scope">
                <EditableInput :isEditing="isEditState" :isNew="scope && scope.row.isNew" :value="scope.row.name"
                  :placeholder="`请输入标签`" @change="
                    (value) => {
                      scope.row.name = value;
                    }
                  " @tempChange="
                    (value) => {
                      scope.row.tempName = value;
                    }
                  "></EditableInput>
              </template>
            </el-table-column>
            <el-table-column prop="sort" label="排序" align="center">
              <template slot-scope="scope">
                <EditableInput :isEditing="isEditState" :value="scope.row.sort" :width="`150px`" :placeholder="`请输入标签`"
                  @change="
                    (value) => {
                      scope.row.sort = value;
                    }
                  " @tempChange="
                    (value) => {
                      scope.row.tempSort = value;
                    }
                  "></EditableInput>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button v-show="scope.row.isNew" size="mini" type="text" icon="el-icon-folder-checked"
                  @click="handleSaveRow(scope.row)">保存</el-button>
                <el-button v-show="!scope.row.isNew" size="mini" type="text" icon="el-icon-edit-outline"
                  @click="handleEditRow(scope.row)">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete"
                  @click="handleDeleteRow(scope.row, scope.$index)" style="color: #f56d6d">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <el-drawer :visible.sync="showDrawer" append-to-body :with-header="true" :direction="'rtl'" :size="'30%'"
        :wrapperClosable="false">
        <form-create v-model="fApi" :rule="formRuleDrawerNes" :option="formOption"></form-create>
      </el-drawer>

      <el-dialog :visible.sync="showConfirm" title="提示" width="30%" center>
        <span>{{ dialogCon }}</span>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showConfirm = false">取消</el-button>
            <el-button type="primary" @click="(showConfirm = false), deleteRows()">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
</template>
<script>
// import autoPage from "@/components/auto-page/AutoPage";
import EditableInput from "../../../components/EditableInput.vue";
import options from "./options";
import {
  list,
  getInfoById,
  addDict,
  delDict,
  delDictList,
  update,
} from "../../../api/sys/dictItem";

import { deepCopy } from "@form-create/utils/lib/deepextend";

export default {
  components: {
    // autoPage,
    EditableInput,
  },
  data() {
    return {
      queryParams: {
        size: -1,
        current: 1,
      },
      queryItemParams: {
        dictId: -1,
        "orders[sort]": "asc",
        size: -1,
        current: 1,
      },
      optionsDict: [],
      valueDictId: "",
      tableData: [],
      options: null,
      tableCheckedRows: [],
      multiple: false,
      isSaveAll: false,
      isEditState: false,
      deleRows: [],
      showConfirm: false,
      dialogCon: "",
      showDrawer: false,
      currRow: {},
      fApi: {},
      formRuleDrawerNes: options.formRule,
      formOption: {
        // 显示重置表单按扭
        resetBtn: false,
        // 表单提交按扭事件
        onSubmit: (formData) => {
          formData.id = this.currRow.id;
          update(formData)
            .then((res) => {
              if (res == null || res == undefined) {
                this.$message.success("操作成功！");
                let currData = this.tableData.find((item) => item.id == formData.id);
                Object.assign(currData, formData);
                this.showDrawer = false;
              } else {
                this.$message.success("操作失败！");
              }
            })
            .catch((err) => {
              this.$message.success("操作异常！");
            });
        },
      },
      from: this.$route.query.from
    };
  },
  mounted() {
    this.getDictList();
  },
  methods: {
    backList() {
      if (this.from == 'treeDict') {
        this.$router.replace("/sys/treeDict");
      } else {
        this.$router.replace("/sys/dict");
      }
    },
    getDictList() {
      if (this.from == 'treeDict' && this.$route.query.level != 'root') {
        getInfoById({
          dictId: this.$route.query.pId,
          size: -1,
          current: 1,
          "orders[sort]": "asc"
        }).then((res) => {
          this.optionsDict = res.records?.filter(f => f.parentId != null)?.map((item) => {
            return {
              ...item,
              id: item.parentId.toString(),
            };
          }) || []
          this.$set(this, "valueDictId", this.$route.query.parentId);
        });
      } else {
        list(this.queryParams).then((res) => {
          this.optionsDict = res.records;
          if (this.optionsDict) {
            this.optionsDict = this.optionsDict.map((item) => {
              return {
                ...item,
                id: item.id.toString(),
              };
            });
            this.$set(this, "valueDictId", this.$route.query.id);
          }
        });
      }

    },
    loadDictItem(id) {
      this.queryItemParams.dictId = id;
      this.tableData = [];
      getInfoById(this.queryItemParams).then((res) => {
        let records = res.records || [];
        this.tableData = records
        // this.tableData = records.filter(f => f.parentId == null) || [];
      });
    },
    saveChanges(isSave) {
      if (isSave) {
        let updateList = [];
        this.tableData.forEach((item) => {
          if (
            item.tempName != item.name ||
            item.tempValue != item.value ||
            item.tempSort != item.sort
          ) {
            updateList.push(item);
          } else {
            delete item.tempValue;
            delete item.tempName;
            delete item.tempSort;
          }
        });
        this.saveAll(updateList);
      } else {
        this.tableData.forEach((item) => {
          delete item.tempValue;
          delete item.tempName;
          delete item.tempSort;
        });
        this.$forceUpdate();
      }
      this.isEditState = !this.isEditState;
    },
    saveData(data) {
      return new Promise((resolve, reject) => {
        update(data)
          .then((res) => {
            if (res == null || res == undefined) {
              resolve();
            } else {
              reject();
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    saveAll(updateList) {
      if (updateList.length === 0) {
        return;
      }
      const savePromises = updateList.map((item) => {
        item.value = item.tempValue;
        item.name = item.tempName;
        item.sort = item.tempSort;
        delete item.tempValue;
        delete item.tempName;
        delete item.tempSort;
        return this.saveData(item);
      });
      Promise.all(savePromises)
        .then((res) => {
          this.$message.success("修改成功");
        })
        .catch((error) => {
          this.$message.error("修改异常");
        });
    },
    handleSavaAll() {
      // 验证方法
      const validateRow = (row) => {
        if (!row.value) return "字典值不能为空！";
        // if (!/^[0-9]+$/.test(row.value)) return "字典值只能是数字！";
        if (!row.name) return "标签不能为空！";
        return null; // 没有错误，返回 null
      };
      for (let row of this.tableCheckedRows) {
        const errorMessage = validateRow(row);
        if (errorMessage) {
          this.$message.error(errorMessage);
          return;
        }
        // 验证字典值是否重复
        if (this.tableData.some((item) => item.value === row.value && item !== row)) {
          this.$message.error("字典值不能相同！");
          return;
        }
        // 验证标签是否重复
        if (this.tableData.some((item) => item.name === row.name && item !== row)) {
          this.$message.error("标签不能相同！");
          return;
        }
      }
      this.saveAllAdd(this.tableCheckedRows);
    },
    saveDataAdd(data) {
      return new Promise((resolve, reject) => {
        addDict(data)
          .then((res) => {
            if (res && res[0] && res[0].id) {
              let currData = this.tableData.find(
                (item) =>
                  item.name == res[0].name &&
                  item.value == res[0].value &&
                  item.sort == res[0].sort
              );
              if (currData) {
                data.id = res[0].id;
                data.isNew = false;
                setTimeout(() => {
                  Object.assign(currData, data);
                }, 200);
              }
              resolve();
            } else {
              reject();
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    saveAllAdd(addList) {
      if (addList.length === 0) {
        return;
      }
      const saveAddPromises = addList.map((item) => {
        let newRow = {
          dictId: this.valueDictId,
          name: item.name,
          orders: { sort: "asc" },
          remark: null,
          sort: item.sort,
          value: item.value,
        };
        return this.saveDataAdd(newRow);
      });
      Promise.all(saveAddPromises)
        .then((res) => {
          this.$message.success("批量保存成功");
        })
        .catch((error) => {
          this.$message.error("批量保存异常", error);
        });
    },
    handleSelectionChange(e) {
      this.tableCheckedRows = e;
    },
    handleAdd() {
      let currSort =
        this.tableData.length > 0
          ? Math.max(...this.tableData.map((q) => Number(q.sort))) + 1
          : 1;
      let row = { value: "", name: "", sort: currSort, isNew: true };
      this.tableData.push(row);
      this.$refs.mTable.toggleRowSelection(row);
    },
    handleDelete() {
      this.dialogCon = "是否删除所选数据项？";
      this.showConfirm = true;
      this.deleRows = this.tableCheckedRows;
    },
    handleDeleteRow(e, i) {
      if (e.isNew) {
        this.tableData.splice(i, 1);
      } else {
        this.dialogCon = `是否删除数据  '字典值:${e.value} 标签:${e.name}'  的数据项`;
        this.deleRows = e;
        this.showConfirm = true;
      }
    },
    handleSaveRow(row) {
      if (row && !row.value) {
        return this.$message.error(`字典值不能为空！`);
      }
      if (this.tableData.find((item) => item.value === row.value && !item.isNew)) {
        return this.$message.error(`字典值不能相同！`);
      }
      if (row && !row.name) {
        return this.$message.error(`标签不能为空！`);
      }
      if (this.tableData.find((item) => item.name === row.name && !item.isNew)) {
        return this.$message.error(`标签不能相同！`);
      }
      if (!/^[0-9]+$/.test(row.value)) {
        return this.$message.error(`字典值只能是数字！`);
      }
      if (row && !row.sort) {
        return this.$message.error(`排序不能为空！`);
      }
      //单条保存
      let newRow = {
        dictId: this.valueDictId,
        name: row.name,
        orders: { sort: "asc" },
        remark: null,
        sort: row.sort,
        value: row.value,
      };
      addDict(newRow).then((res) => {
        if (res && res[0] && res[0].id) {
          this.$message.success("保存成功！");
          let currData = this.tableData.find(
            (item) =>
              item.name == res[0].name &&
              item.value == res[0].value &&
              item.sort == res[0].sort
          );
          if (currData) {
            newRow.id = res[0].id;
            newRow.isNew = false;
            Object.assign(currData, newRow);
          }
        }
      });
    },
    handleEditRow(row) {
      this.showDrawer = true;
      this.currRow = row;
      let fins = deepCopy(this.formRuleDrawerNes);
      // let fins = JSON.parse(JSON.stringify(this.formRuleDrawerNes));
      Object.entries(row).forEach((ens) => {
        let rule = fins.find((f) => f.field == ens[0]);
        if (rule) {
          rule.value = ens[1];
        }
      });
      this.formRuleDrawerNes = fins;
    },
    handleEditState() {
      this.tableData = this.tableData.filter((item) => !item.isNew);
      this.isEditState = !this.isEditState;
    },
    handleSave() {
      this.isEditState = !this.isEditState;
    },
    //删除
    deleteRows() {
      if (!this.multiple) {
        this.deleteData(this.deleRows);
      } else {
        let ids = this.tableCheckedRows.map((item) => item.id.toString()).join(",");
        this.deleteDataList(ids);
      }
    },
    deleteData(data) {
      delDict(data.id).then((res) => {
        this.$message.success("删除成功！");
        this.tableData = this.tableData.filter((item) => item.id != this.deleRows.id);
      });
    },
    deleteDataList(ids) {
      delDictList(ids).then((res) => {
        this.$message.success("删除成功！");
        this.tableCheckedRows = [];
        this.tableData = this.tableData.filter((item) => !ids.includes(item.id));
      });
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始，需要加 1
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
  },
  watch: {
    tableCheckedRows: {
      handler(val) {
        if (val.length > 0) {
          let mulNum = 0;
          let saveNum = 0;
          val.forEach((item) => {
            mulNum += item.id ? 1 : 0;
            saveNum += item.id ? 0 : 1;
          });
          this.multiple = val.length == mulNum;
          this.isSaveAll = val.length == saveNum;
        } else {
          this.multiple = false;
          this.isSaveAll = false;
        }
      },
      deep: true,
      immediate: true,
    },
    valueDictId(val) {
      if (val) {
        this.loadDictItem(val);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  margin: 20px;
  border-radius: 10px;
  padding: 10px;
  background: white;

  .top_div {
    margin: 20px;

    .back_list {
      width: 100px;
      height: 30px;
      margin: 0 10px 10px;
      font-size: 16px;
      color: black;
      cursor: pointer;

      &:hover {
        color: #0089ff;
      }
    }

    .row_title {
      width: 370px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: 5px;
    }

    .option_title {
      font-size: 14px;
      color: rgb(85, 84, 84);
    }

    .div_option {
      margin-left: 10px;
      margin-right: 40px;
      width: 250px;
    }
  }

  .center_div {
    margin-left: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .row_btn_left {
      width: 600px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .row_btn_right {
      margin-left: 20px;
    }
  }

  .bottom_div {
    margin-top: 20px;
    margin-left: 20px;
    margin-bottom: 20px;

    .inputValue {
      width: 150px;
    }
  }

  ::v-deep .focus-input .el-input__inner {
    border: 1px solid transparent;
  }
}
</style>

<style lang="scss" scoped>
// .el-dialog {
//   margin: 0 auto !important;
// }

// .el-dialog__wrapper {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100%;
// }

// .dialog-footer {
//   width: 100%;
//   display: flex;
//   justify-content: space-evenly;
// }

.editable-input:not(.is-editing):hover :deep(.el-input__inner) {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
}

::v-deep .el-drawer__body .form-create .el-form-item {
  display: flex;
  flex-direction: column;
  padding-left: 40px;

  .el-form-item__label {
    width: 100% !important;
    text-align: left;
  }

  .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
