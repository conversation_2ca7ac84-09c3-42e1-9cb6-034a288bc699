import request from '@/utils/request'

const api = CONSTANT.SYSTEM + '/sys/resources'

// 从环境变量或全局配置中获取API版本
import config from '@/root.config.js'
/**
* 获取路由配置
* @param {string} path - 项目路径，默认使用环境变量中的项目名
* @returns {Promise} 处理后的路由配置
*/
export function getRouters(path = process.env.PROJECT_NAME) {
  // v1版本的原有逻辑
  if (config.API_VERSION === 'v1') {
    return request({
      url: `${api}/getRouters`,
      method: 'get'
    })
  }

  // v2版本的处理逻辑
  if (path === 'system') {
    let pathParts = window.location.pathname.split('/')
    let searchParams = new URLSearchParams(window.location.search)
    let redirect = searchParams.get('redirect')


    if (!redirect?.startsWith('http://') && !redirect?.startsWith('https://') && redirect) {
      pathParts = redirect.split('/')
      if (pathParts.length > 2) {
        path = pathParts[1]
      }
    } else {
      if (pathParts.length > 2 && pathParts[1] === 'system' && pathParts[2] !== 'login') {
        path = pathParts[2]
      } else {
        path = pathParts[1]
      }
    }
  }

  return request({
    url: `${api}/getRouters`,
    method: 'get',
    params: {
      path
    }
  })
}


export function getApp(path = process.env.PROJECT_NAME) {
  if (path === 'system') {
    let pathParts = window.location.pathname.split('/')
    if (pathParts.length > 2 && pathParts[1] === 'system') {
      path = pathParts[2]
    } else {
      path = pathParts[1]
    }
  }
  if (path.length < 1) {
    return Promise.resolve({})
  }

  return request({
    url: `${api}`,
    method: 'get',
    params: {
      type: 'APP',
      path
    }
  })
}
