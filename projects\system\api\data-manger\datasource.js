import request from '@/utils/request'

const api = CONSTANT.META

// 获取数据源列表
export function getDataSourceList(params) {
  return request({
    url: api + '/data/source/config/list',
    method: 'get',
    params
  })
}

// 获取数据源详情
export function getDataSourceDetail(id) {
  return request({
    url: api + '/data/source/config',
    method: 'get',
    params: { id }
  })
}

// 新增数据源
export function createDataSource(data) {
  return request({
    url: api + '/data/source/config',
    method: 'post',
    data
  })
}

// 修改数据源
export function updateDataSource(data) {
  return request({
    url: api + '/data/source/config',
    method: 'put',
    data
  })
}

// 添加测试连接的API方法
export function testDataSourceConnection(data) {
  return request({
    url: api + '/data/source/config/testConnection',
    method: 'post',
    data
  })
}

// 删除数据源
export function deleteDataSource(id) {
  return request({
    url: api + `/data/source/config/${id}`,
    method: 'delete'
  })
}

// 添加获取数据源分组列表的接口
export function getDataSourceGroups(groupName) {
  return request({
    url: api + '/data/source/config/groupNames',
    method: 'get',
    params: { groupName }
  })
}

// 获取支持的数据源类型列表
export function getSupportedDataSourceTypes() {
  return request({
    url: api + '/data/source/config/supportedTypes',
    method: 'get'
  })
}

// 预览JDBC URL
export function previewJdbcUrl(data) {
  return request({
    url: api + '/data/source/config/previewJdbcUrl',
    method: 'post',
    data
  })
} 