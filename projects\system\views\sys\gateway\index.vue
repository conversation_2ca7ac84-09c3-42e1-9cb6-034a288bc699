<template>
  <div class="gateway-security-container">
    <el-row :gutter="24">
      <!-- 左侧流量链路 -->
      <el-col :span="6">
        <div class="card-container flow-panel">
          <div class="card-body flow-list">
            <FlowVisualizer 
              ref="flowVisualizer"
              :predicates="allPredicates"
              :filters="allFilters"
              :securities="allSecurities"
              :selectedRoute="currentRoute"
              :editable="true"
              @route-selected="selectRoute"
              @edit-route="handleEditRoute"
              @delete-route="handleDeleteRoute"
              @add-route="handleAddRoute"
              @enableChange="routeEnableChange"
              @apply-config="handleApplyConfig"
            />
          </div>
        </div>
      </el-col>

      <!-- 右侧配置面板 -->
      <el-col :span="18">
        <div class="card-container config-panel">
          <div class="card-header compact-header">
            <div class="header-title-row">
              <div class="header-title">
                <h2 v-if="currentRoute">
                  <span class="title-text">{{ currentRoute.name }} (路由ID: {{ currentRoute.id }})</span>
                  <div class="config-type-tabs">
                    <el-radio-group v-model="activeTab" size="mini">
                      <el-radio-button label="predicates" >
                        <i class="el-icon-fork-spoon" style="color: #67C23A;"></i> 断言条件  {{ predicates.length }}
                      </el-radio-button>
                      <el-radio-button label="filters">
                        <i class="el-icon-magic-stick" style="color: #E6A23C;"></i> 过滤器  {{ filters.length }}
                      </el-radio-button>
                      <el-radio-button label="securities">
                        <i class="el-icon-lock" style="color: #F56C6C;"></i> 安全配置  {{ securities.length }}
                      </el-radio-button>
                    </el-radio-group>
                  </div>
                </h2>
                <h2 v-else>
                  <span class="title-text">网关配置</span>
                </h2>
              </div>
              
              <!-- <div class="action-bar" v-if="currentRoute">
                <el-input
                  placeholder="搜索配置项"
                  prefix-icon="el-icon-search"
                  clearable
                  size="small"
                  style="width: 220px; margin-right: 12px;"
                />
                
                <el-button-group>
                  <el-button size="small" type="primary" @click="handleAddConfigItem" :disabled="!currentRoute">
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                  <el-button size="small" type="warning" @click="handleEditRoute(currentRoute)" :disabled="!currentRoute">
                    <i class="el-icon-edit"></i> 编辑路由
                  </el-button>
                </el-button-group>
              </div> -->
            </div>
          </div>
          
          <div class="card-body config-container">
            <div v-if="!currentRoute" class="empty-selection">
              <i class="el-icon-warning-outline"></i>
              <p>请先选择左侧的路由或创建新路由</p>
            </div>
            
            <template v-else>
              <!-- 路由详情 -->
              <!-- <div class="route-detail" v-if="currentRoute">
                <div class="detail-card">
                  <div class="detail-item">
                    <span class="label">URI</span>
                    <span class="value">{{ currentRoute.uri }}</span>
                  </div>
                  <div class="detail-item" v-if="currentRoute.pattern">
                    <span class="label">匹配路径</span>
                    <span class="value">{{ currentRoute.pattern }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">元数据</span>
                    <div class="value">
                      <div v-if="currentRoute.metadata && Object.keys(currentRoute.metadata).length" class="metadata-list">
                        <el-tag v-for="(value, key) in currentRoute.metadata" :key="key" size="small" class="metadata-item">
                          {{ key }}: {{ value }}
                        </el-tag>
                      </div>
                      <span v-else class="no-data">无元数据</span>
                    </div>
                  </div>
                </div>
              </div> -->

              <!-- 断言条件配置 -->
              <div v-if="activeTab === 'predicates'" class="config-node-container">
                <div class="node-header">
                  <span class="node-title">
                    <i class="el-icon-fork-spoon"></i>
                    断言条件
                    <el-tag size="mini" type="success">{{ predicates.length }}</el-tag>
                  </span>
                  <el-button size="small" type="success" icon="el-icon-plus" @click="handleAddPredicate">添加断言</el-button>
                </div>
                <div class="node-list">
                  <template v-if="predicates.length">
                    <PredicateNode 
                      v-for="predicate in predicates" 
                      :key="predicate.id" 
                      :predicate="predicate"
                      :is-active="currentPredicate && currentPredicate.id === predicate.id"
                      @click="selectPredicate"
                      @edit="handleEditPredicate(predicate)"
                      @delete="handleDeletePredicate(predicate)"
                      @enableChange="predicateEnableChange(predicate)"
                    />
                  </template>
                  <el-empty v-else description="暂无断言条件" :image-size="80"></el-empty>
                </div>
              </div>

              <!-- 过滤器配置 -->
              <div v-if="activeTab === 'filters'" class="config-node-container">
                <div class="node-header">
                  <span class="node-title">
                    <i class="el-icon-magic-stick"></i>
                    过滤器
                    <el-tag size="mini" type="warning">{{ filters.length }}</el-tag>
                  </span>
                  <el-button size="small" type="warning" icon="el-icon-plus" @click="handleAddFilter">添加过滤器</el-button>
                </div>
                <div class="node-list">
                  <template v-if="filters.length">
                    <FilterNode 
                      v-for="filter in filters" 
                      :key="filter.id" 
                      :filter="filter"
                      :is-active="currentFilter && currentFilter.id === filter.id"
                      @click="selectFilter"
                      @edit="handleEditFilter(filter)"
                      @delete="handleDeleteFilter(filter)"
                      @enableChange="filterEnableChange(filter)"
                    />
                  </template>
                  <el-empty v-else description="暂无过滤器" :image-size="80"></el-empty>
                </div>
              </div>

              <!-- 安全配置 -->
              <div v-if="activeTab === 'securities'" class="config-node-container">
                <div class="node-header">
                  <span class="node-title">
                    <i class="el-icon-lock"></i>
                    安全配置
                    <el-tag size="mini" type="danger">{{ securities.length }}</el-tag>
                  </span>
                  <el-button size="small" type="danger" icon="el-icon-plus" @click="handleAddSecurity">添加安全配置</el-button>
                </div>
                <div class="node-list">
                  <template v-if="securities.length">
                    <SecurityNode 
                      v-for="security in securities" 
                      :key="security.id" 
                      :security="security"
                      :is-active="currentSecurity && currentSecurity.id === security.id"
                      @click="selectSecurity"
                      @edit="handleEditSecurity(security)"
                      @delete="handleDeleteSecurity(security)"
                      @enableChange="securityEnableChange(security)"
                    />
                  </template>
                  <el-empty v-else description="暂无安全配置" :image-size="80"></el-empty>
                </div>
              </div>
            </template>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 路由表单对话框 -->
    <el-dialog 
      :title="dialogTitle('路由')" 
      :visible.sync="routeDialogVisible" 
      width="600px"
      @closed="resetRouteForm"
      custom-class="gateway-dialog"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form ref="routeForm" :model="routeForm" :rules="routeRules" label-width="100px">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="路由名称" prop="name">
                <el-input v-model="routeForm.name" placeholder="请输入路由名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="匹配路径" prop="pattern">
                <el-input v-model="routeForm.pattern" placeholder="例如：/api/**"></el-input>
                <div class="form-tip">默认断言路径，等价于 Predicate Path=/api/**</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="代理URL" prop="uri">
                <el-input v-model="routeForm.uri" placeholder="例如：lb://service-name">
                </el-input>
                <div class="form-tip">支持协议 lb://负载均衡 或 http:// 格式等</div>
                <div class="form-tip">以"/"结束代理路径会清除第一个路径，等价于Filter StripPrefix=1</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否启用" prop="enable">
                <el-switch class="value" v-model="routeForm.enable" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="排序" prop="sort">
                <el-input-number v-model="routeForm.sort" :min="0" :max="1000" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <div class="form-section">
          <div class="section-title" style="display: flex; align-items: center; justify-content: space-between;">
            <div>元数据信息</div>
            <el-button size="small" type="primary" plain @click="$refs.metadataForm.addKey()">
              <i class="el-icon-plus"></i>
            </el-button>
          </div>
          <el-form-item class="metadata-form">
            <DynamicData v-model="routeForm.metadata" ref="metadataForm" />
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="routeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitRouteForm" :loading="submitLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 断言表单对话框 -->
    <el-dialog 
      :title="dialogTitle('断言')" 
      :visible.sync="predicateDialogVisible" 
      width="600px"
      @closed="resetPredicateForm"
      custom-class="gateway-dialog"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form ref="predicateForm" :model="predicateForm" :rules="predicateRules" label-width="100px">
        <div class="form-section">
          <div class="section-title">断言配置</div>
          <el-row :gutter="20">
            <el-col :span="24">
        <el-form-item label="断言类型" prop="name">
          <el-select v-model="predicateForm.name" placeholder="请选择断言类型" @change="handlePredicateTypeChange" allow-create filterable>
            <el-option 
              v-for="item in predicateTypes" 
              :key="item.value" 
              :label="`${item.label}     ${item.value}`" 
              :value="item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
            </el-option>
          </el-select>
        </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否启用" prop="enable">
                <el-switch class="value" v-model="predicateForm.enable"  />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="排序" prop="sort">
                <el-input-number v-model="predicateForm.sort" :min="0" :max="1000" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
        
        <!-- 动态参数表单 -->
        <template v-if="predicateForm.name">
          <!-- Path断言 -->
              <el-col :span="24" v-if="predicateForm.name === 'Path'">
            <el-form-item label="路径模式" prop="pattern">
              <el-input v-model="predicateForm.arg.pattern" placeholder="例如：/api/**"></el-input>
              <span class="form-tip">支持 Ant 风格的路径匹配模式</span>
            </el-form-item>
              </el-col>
          
          <!-- Method断言 -->
              <el-col :span="24" v-else-if="predicateForm.name === 'Method'">
            <el-form-item label="HTTP方法" prop="method">
              <el-select v-model="predicateForm.arg.method" placeholder="请选择HTTP方法">
                <el-option label="GET" value="GET"></el-option>
                <el-option label="POST" value="POST"></el-option>
                <el-option label="PUT" value="PUT"></el-option>
                <el-option label="DELETE" value="DELETE"></el-option>
                <el-option label="PATCH" value="PATCH"></el-option>
                <el-option label="HEAD" value="HEAD"></el-option>
                <el-option label="OPTIONS" value="OPTIONS"></el-option>
              </el-select>
            </el-form-item>
              </el-col>
          
          <!-- Host断言 -->
              <el-col :span="24" v-else-if="predicateForm.name === 'Host'">
            <el-form-item label="主机模式" prop="pattern">
              <el-input v-model="predicateForm.arg.pattern" placeholder="例如：**.example.org"></el-input>
              <span class="form-tip">支持通配符，如 **.example.org, *.example.org</span>
            </el-form-item>
              </el-col>
          
          <!-- Header断言 -->
          <template v-else-if="predicateForm.name === 'Header'">
                <el-col :span="24">
            <el-form-item label="请求头名称" prop="header">
              <el-input v-model="predicateForm.arg.header" placeholder="例如：X-Request-Id"></el-input>
            </el-form-item>
                </el-col>
                <el-col :span="24">
            <el-form-item label="请求头值匹配" prop="regexp">
              <el-input v-model="predicateForm.arg.regexp" placeholder="正则表达式"></el-input>
              <span class="form-tip">支持正则表达式</span>
            </el-form-item>
                </el-col>
          </template>
          
          <!-- 其他类型断言的动态表单 -->
          <template v-else-if="predicateTypes.map(type => type.value).includes(predicateTypes.name)">
                <el-col :span="24" v-for="(key, index) in getPredicateArgKeys()" :key="index">
                  <el-form-item :label="getArgLabel(key)" :prop="key">
              <el-input v-model="predicateForm.arg[key]" :placeholder="getArgPlaceholder(key)"></el-input>
            </el-form-item>
                </el-col>
          </template>
          
          <template v-else>
            <div class="section-title" style="display: flex; align-items: center; justify-content: space-between;">
              <div>参数</div>
              <el-button size="small" type="primary" plain @click="$refs.predicateDynamic.addKey()">
                <i class="el-icon-plus"></i>
              </el-button>
            </div>
            <el-col :span="24">
              <DynamicData v-model="predicateForm.arg" ref="predicateDynamic" />
            </el-col>
          </template>
        </template>
          </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="predicateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPredicateForm" :loading="submitLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 过滤器表单对话框 -->
    <el-dialog 
      :title="dialogTitle('过滤器')" 
      :visible.sync="filterDialogVisible" 
      width="600px"
      @closed="resetFilterForm"
      custom-class="gateway-dialog"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form ref="filterForm" :model="filterForm" :rules="filterRules" label-width="100px">
        <div class="form-section">
          <div class="section-title">过滤器配置</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="过滤器类型" prop="name">
                <el-select v-model="filterForm.name" placeholder="请选择过滤器类型" @change="handleFilterTypeChange" allow-create filterable>
                  <el-option 
                    v-for="item in filterTypes" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value">
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="24">
              <el-form-item label="是否启用" prop="enable">
                <el-switch class="value" v-model="filterForm.enable"  />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="排序" prop="sort">
                <el-input-number v-model="filterForm.sort" :min="0" :max="1000" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
        
        <!-- 动态参数表单 -->
        <template v-if="filterForm.name">
          <!-- AddRequestHeader过滤器 -->
          <template v-if="filterForm.name === 'AddRequestHeader'">
                <el-col :span="24">
            <el-form-item label="请求头名称" prop="name">
              <el-input v-model="filterForm.arg.name" placeholder="例如：X-Custom-Header"></el-input>
            </el-form-item>
                </el-col>
                <el-col :span="24">
            <el-form-item label="请求头值" prop="value">
              <el-input v-model="filterForm.arg.value" placeholder="请求头的值"></el-input>
            </el-form-item>
                </el-col>
          </template>
          
          <!-- StripPrefix过滤器 -->
              <el-col :span="24" v-else-if="filterForm.name === 'StripPrefix'">
            <el-form-item label="前缀数量" prop="parts">
              <el-input-number v-model="filterForm.arg.parts" :min="1" :max="10"></el-input-number>
              <span class="form-tip">去除URI中的前缀部分数量，如'/api/users'去除1个前缀后变为'/users'</span>
            </el-form-item>
              </el-col>
          
          <!-- 通用参数输入 -->
          <template v-else-if="filterTypes.map(type => type.value).includes(filterForm.name)">
                <el-col :span="24" v-for="(key, index) in getFilterArgKeys()" :key="index">
                  <el-form-item :label="getArgLabel(key)" :prop="key">
              <el-input v-model="filterForm.arg[key]" :placeholder="getArgPlaceholder(key)"></el-input>
            </el-form-item>
                </el-col>
          </template>
          <template v-else>
            <div class="section-title" style="display: flex; align-items: center; justify-content: space-between;">
              <div>参数</div>
              <el-button size="small" type="primary" plain @click="$refs.filterDynamic.addKey()">
                <i class="el-icon-plus"></i>
              </el-button>
            </div>
            <el-col :span="24">
              <DynamicData v-model="filterForm.arg" ref="filterDynamic" />
            </el-col>
          </template>
        </template>
          </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="filterDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitFilterForm" :loading="submitLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 安全配置表单对话框 -->
    <el-dialog 
      :title="dialogTitle('安全配置')" 
      :visible.sync="securityDialogVisible" 
      width="600px"
      @closed="resetSecurityForm"
      custom-class="gateway-dialog"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form ref="securityForm" :model="securityForm" :rules="securityRules" label-width="100px">
        <div class="form-section">
          <div class="section-title">安全规则配置</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="HTTP方法" prop="method">
                <el-select v-model="securityForm.method" placeholder="请选择HTTP方法" clearable>
                  <el-option label="不限" :value="undefined"></el-option>
                  <el-option label="GET" value="GET"></el-option>
                  <el-option label="POST" value="POST"></el-option>
                  <el-option label="PUT" value="PUT"></el-option>
                  <el-option label="DELETE" value="DELETE"></el-option>
                  <el-option label="PATCH" value="PATCH"></el-option>
                  <el-option label="OPTIONS" value="OPTIONS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="匹配规则" prop="matcher">
                <el-select v-model="securityForm.matcher" placeholder="请选择匹配规则">
                  <el-option v-for="matcher in matchers" :label="`${matcher.label}      ${matcher.value}`" :value="matcher.value">
                    <div style="display: flex; justify-content: space-between;">
                      <span>{{ matcher.label }}</span>
                      <span>{{ matcher.value }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否启用" prop="enable">
                <el-switch class="value" v-model="securityForm.enable" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="排序" prop="sort">
                <el-input-number v-model="securityForm.sort" :min="0" :max="1000" controls-position="right"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <div>
                <div class="section-title" style="display: flex; align-items: center; justify-content: space-between;">
                  <div>匹配参数</div>
                  <el-button size="small" type="primary" plain @click="$refs.securityDynamic.addKey()">
                    <i class="el-icon-plus"></i>
                  </el-button>
                </div>
                <DynamicData v-model="securityForm.arg" ref="securityDynamic" />
              </div>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="securityDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSecurityForm" :loading="submitLoading">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 应用配置确认对话框 -->
    <el-dialog
      title="应用配置确认"
      :visible.sync="applyDialogVisible"
      width="450px"
      custom-class="gateway-dialog confirm-dialog"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <div class="apply-dialog-content">
        <i class="el-icon-warning-outline warning-icon"></i>
        <div class="apply-message">
          <p class="apply-title">确定要应用当前网关配置吗？</p>
          <p class="apply-desc">此操作将使配置在网关服务中生效，可能会影响正在运行的服务。</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="applyDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmApplyConfig" :loading="applyLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  createRoute,
  updateRoute,
  deleteRoute,
  getPredicateList,
  createPredicate,
  updatePredicate,
  deletePredicate,
  getFilterList,
  createFilter,
  updateFilter,
  deleteFilter,
  getSecurityList,
  createSecurity,
  updateSecurity,
  deleteSecurity,
  applyGatewayConfig,
  matchers
} from '@system/api/sys/gateway'

// 导入组件
import RouteNode from './components/RouteNode'
import PredicateNode from './components/PredicateNode'
import FilterNode from './components/FilterNode'
import SecurityNode from './components/SecurityNode'
import FlowVisualizer from './components/FlowVisualizer'
import DynamicData from '@system/components/DynamicData'

export default {
  name: 'GatewaySecurity',
  components: {
    RouteNode,
    PredicateNode,
    FilterNode,
    SecurityNode,
    FlowVisualizer,
    DynamicData
  },
  data() {
    return {
      total: 0,
      matchers:[],
      // 列表数据
      routes: [],
      allPredicates: [],
      allFilters: [],
      allSecurities: [],
      
      // 当前选中项
      currentRoute: null,
      currentPredicate: null,
      currentFilter: null,
      currentSecurity: null,
      
      // 当前选中路由的配置
      predicates: [],
      filters: [],
      securities: [],
      
      // 当前激活的标签页
      activeTab: 'predicates',
      
      // 对话框显示状态
      routeDialogVisible: false,
      predicateDialogVisible: false,
      filterDialogVisible: false,
      securityDialogVisible: false,
      applyDialogVisible: false,
      
      // 编辑状态
      editMode: false,
      
      // 加载状态
      loading: false,
      submitLoading: false,
      applyLoading: false,
      
      // 路由表单
      routeForm: {
        name: '',
        uri: '',
        pattern: '',
        metadata: {},
        enable: true,
        sort: 0
      },
      routeParams: {
        current: 1,
        size: -1
      },
      metadataItems: [],
      
      // 断言表单
      predicateForm: {
        routeId: '',
        name: '',
        arg: {}
      },
      
      // 过滤器表单
      filterForm: {
        routeId: '',
        name: '',
        arg: {}
      },
      
      // 安全配置表单
      securityForm: {
        routeId: '',
        matcher: '',
        arg: [],
        enable: true,
        sort: 0
      },
      
      // 安全配置参数输入
      inputArgVisible: false,
      inputArgValue: '',
      
      // 表单校验规则
      routeRules: {
        name: [
          { required: true, message: '请输入路由名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        pattern: [
          { required: true, message: '请输入匹配路径', trigger: 'blur' }
        ],
        uri: [
          { required: true, message: '请输入URI', trigger: 'blur' }
        ]
      },
      predicateRules: {
        name: [
          { required: true, message: '请选择断言类型', trigger: 'change' }
        ]
      },
      filterRules: {
        name: [
          { required: true, message: '请选择过滤器类型', trigger: 'change' }
        ]
      },
      securityRules: {
        uri: [
          { required: true, message: '请输入拦截路径', trigger: 'blur' }
        ],
        matcher: [
          { required: true, message: '请选择匹配规则', trigger: 'change' }
        ]
      },
      
      // 预设断言类型
      predicateTypes: [
        { label: '路径匹配', value: 'Path' },
        { label: 'HTTP方法匹配', value: 'Method' },
        { label: '主机名匹配', value: 'Host' },
        { label: '请求头匹配', value: 'Header' },
        { label: 'Cookie匹配', value: 'Cookie' },
        { label: '查询参数匹配', value: 'Query' },
        { label: '时间匹配-之后', value: 'After' },
        { label: '时间匹配-之前', value: 'Before' },
        { label: '时间匹配-之间', value: 'Between' },
        { label: '远程地址匹配', value: 'RemoteAddr' },
        { label: '权重路由', value: 'Weight' }
      ],
      
      // 预设过滤器类型
      filterTypes: [
        { label: '添加请求头', value: 'AddRequestHeader' },
        { label: '添加响应头', value: 'AddResponseHeader' },
        { label: '添加请求参数', value: 'AddRequestParameter' },
        { label: '路径前缀', value: 'PrefixPath' },
        { label: '移除请求头', value: 'RemoveRequestHeader' },
        { label: '移除响应头', value: 'RemoveResponseHeader' },
        { label: '移除请求参数', value: 'RemoveRequestParameter' },
        { label: '重写路径', value: 'RewritePath' },
        { label: '设置路径', value: 'SetPath' },
        { label: '设置响应头', value: 'SetResponseHeader' },
        { label: '设置状态码', value: 'SetStatus' },
        { label: '去除前缀', value: 'StripPrefix' },
        { label: '请求大小限制', value: 'RequestSize' },
        { label: '请求限流', value: 'RequestRateLimiter' },
        { label: '断路器', value: 'CircuitBreaker' },
        { label: '重试机制', value: 'Retry' },
        { label: '重定向', value: 'RedirectTo' }
      ],
      
      // 模拟模式开关
      mockMode: false,
    }
  },
  created() {
    matchers().then(data => {
      this.matchers = data
    })
  },
  methods: {
    // 获取所有路由列表
    async getRouteList() {
      return this.$refs.flowVisualizer.fetchRoutes()
    },
    
    // 获取所有配置信息 - 该方法不再主动调用，改为按需加载
    async fetchAllConfigs() {
      try {
        // 初始化存储所有配置的数组
        this.allPredicates = []
        this.allFilters = []
        this.allSecurities = []
        
        // 为每个路由获取配置
        for (const route of this.routes) {
          // 获取断言
          const predicates = await getPredicateList({routeId: route.id, size: -1})
          if (predicates && predicates.records) {
            this.allPredicates = this.allPredicates.concat(predicates.records)
          }
          
          // 获取过滤器
          const filters = await getFilterList({routeId: route.id, size: -1})
          if (filters && filters.records) {
            this.allFilters = this.allFilters.concat(filters.records)
          }
          
          // 获取安全配置
          const securities = await getSecurityList({routeId: route.id, size: -1})
          if (securities && securities.records) {
            this.allSecurities = this.allSecurities.concat(securities.records)
          }
        }
      } catch (error) {
        this.$message.error('获取配置信息失败')
        console.error(error)
      }
    },
    
    // 选择路由
    async selectRoute(route) {
      if (this.currentRoute?.id !== route.id) {
        this.currentRoute = route
        
        // 重置当前选中的配置项
        this.currentPredicate = null
        this.currentFilter = null
        this.currentSecurity = null
        
        // 获取该路由的断言、过滤器和安全配置
        await this.getRouteConfigs(route.id)
      }
    },
    
    // 获取路由的配置
    async getRouteConfigs(routeId) {
      this.loading = true
      try {
        if (this.mockMode) {
          // 使用模拟数据替代API调用
          // 获取断言
          this.predicates = mockPredicates.filter(p => p.routeId === routeId) || []
          
          // 获取过滤器
          this.filters = mockFilters.filter(f => f.routeId === routeId) || []
          
          // 获取安全配置
          this.securities = mockSecurities.filter(s => s.routeId === routeId) || []
        } else {
          // 原API调用方式
          // 获取断言
          const predicates = await getPredicateList({routeId, size: -1})
          this.predicates = predicates.records || []
          
          // 获取过滤器
          const filters = await getFilterList({routeId, size: -1})
          this.filters = filters.records || []
          
          // 获取安全配置
          const securities = await getSecurityList({routeId, size: -1})
          this.securities = securities.records || []
          
          // 更新所有配置的缓存（用于FlowVisualizer组件）
          // 先检查缓存中是否已存在该路由的配置
          const existingPredicates = this.allPredicates.filter(p => p.routeId !== routeId);
          const existingFilters = this.allFilters.filter(f => f.routeId !== routeId);
          const existingSecurities = this.allSecurities.filter(s => s.routeId !== routeId);
          
          // 将当前路由的配置添加到缓存中
          this.allPredicates = [...existingPredicates, ...this.predicates];
          this.allFilters = [...existingFilters, ...this.filters];
          this.allSecurities = [...existingSecurities, ...this.securities];
        }
      } catch (error) {
        this.$message.error('获取路由配置失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    
    // 选择断言
    selectPredicate(predicate) {
      this.currentPredicate = predicate
    },
    
    // 选择过滤器
    selectFilter(filter) {
      this.currentFilter = filter
    },
    
    // 选择安全配置
    selectSecurity(security) {
      this.currentSecurity = security
    },
    
    // 添加配置项（根据当前激活的标签页）
    handleAddConfigItem() {
      if (!this.currentRoute) return
      
      switch (this.activeTab) {
        case 'predicates':
          this.handleAddPredicate()
          break
        case 'filters':
          this.handleAddFilter()
          break
        case 'securities':
          this.handleAddSecurity()
          break
      }
    },
    
    // 对话框标题
    dialogTitle(type) {
      return this.editMode ? `编辑${type}` : `添加${type}`
    },
    
    // 添加路由
    handleAddRoute() {
      this.editMode = false
      this.routeForm = {
        name: '',
        uri: '',
        pattern: '',
        metadata: {},
        enable: true,
        sort: 0
      }
      this.metadataItems = []
      this.routeDialogVisible = true
    },
    
    // 编辑路由
    handleEditRoute(route) {
      this.editMode = true
      this.routeForm = JSON.parse(JSON.stringify(route))
      
      // 转换元数据为表单项
      this.metadataItems = []
      if (route.metadata) {
        for (const [key, value] of Object.entries(route.metadata)) {
          this.metadataItems.push({ key, value })
        }
      }
      
      this.routeDialogVisible = true
    },
    
    // 删除路由
    handleDeleteRoute(route) {
      this.$confirm('确认删除该路由吗？相关的断言、过滤器和安全配置也将被删除', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, '删除成功')
          } else {
            // 实际API调用
            await deleteRoute(route.id)
            this.$message.success('删除成功')
          }
          this.getRouteList()
          if (this.currentRoute && this.currentRoute.id === route.id) {
            this.currentRoute = null
          }
        } catch (error) {
          this.$message.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    },
    
    // 添加元数据
    addMetadata() {
      this.$refs.metadataForm.addKey()
      // this.metadataItems.push({ key: '', value: '' })
    },
    
    // 移除元数据
    removeMetadata(index) {
      this.metadataItems.splice(index, 1)
    },
    
    // 重置路由表单
    resetRouteForm() {
      if (this.$refs.routeForm) {
        this.$refs.routeForm.resetFields()
      }
      this.metadataItems = []
    },
    
    // 模拟API操作（开发环境使用）
    async mockApiOperation(operation, successMsg) {
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 800))
      this.$message.success(successMsg || '操作成功')
      return { success: true }
    },

    routeEnableChange(route) {
      var {id, enable} = route
      this.submitLoading = true
      updateRoute({id, enable}).then(res => {
        this.$message.success('更新成功')
        this.submitLoading = false
      }).catch(e => {
        enable = !enable
        this.submitLoading = false
      })
    },
    
    // 提交路由表单
    async submitRouteForm() {
      this.$refs.routeForm.validate(async valid => {
        if (!valid) return
        
        this.submitLoading = true
        
        try {
          const routeData = this.routeForm
          
          // 创建或更新路由
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, this.editMode ? '更新成功' : '创建成功')
          } else {
            // 实际API调用
            if (this.editMode) {
              await updateRoute(routeData)
              this.$message.success('更新成功')
            } else {
              await createRoute(routeData)
              this.$message.success('创建成功')
            }
          }
          
          this.routeDialogVisible = false
          this.getRouteList()
        } catch (error) {
          this.$message.error(this.editMode ? '更新失败' : '创建失败')
          console.error(error)
        } finally {
          this.submitLoading = false
        }
      })
    },
    
    // 添加断言
    handleAddPredicate() {
      this.editMode = false
      this.predicateForm = {
        routeId: this.currentRoute.id,
        name: '',
        arg: {},
        sort: 0,
        enable: true
      }
      this.predicateDialogVisible = true
    },
    
    // 编辑断言
    handleEditPredicate(predicate) {
      this.editMode = true
      this.predicateForm = JSON.parse(JSON.stringify(predicate))
      this.predicateDialogVisible = true
    },
    
    // 删除断言
    handleDeletePredicate(predicate) {
      this.$confirm('确认删除该断言吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, '删除成功')
          } else {
            // 实际API调用
            await deletePredicate(predicate.id)
            this.$message.success('删除成功')
          }
          await this.getRouteConfigs(this.currentRoute.id)
          await this.fetchAllConfigs()
        } catch (error) {
          this.$message.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    },
    
    // 断言类型变更
    handlePredicateTypeChange(value) {
      // 清空参数
      this.predicateForm.arg = {}
      
      // 根据不同断言类型设置不同的默认参数
      switch (value) {
        case 'Path':
          this.predicateForm.arg.pattern = ''
          break
        case 'Method':
          this.predicateForm.arg.method = ''
          break
        case 'Host':
          this.predicateForm.arg.pattern = ''
          break
        case 'Header':
          this.predicateForm.arg.header = ''
          this.predicateForm.arg.regexp = ''
          break
        case 'Cookie':
          this.predicateForm.arg.name = ''
          this.predicateForm.arg.regexp = ''
          break
        case 'Query':
          this.predicateForm.arg.param = ''
          this.predicateForm.arg.regexp = ''
          break
        default:
          // 其他类型断言的默认处理
          break
      }
    },
    
    // 获取断言参数键
    getPredicateArgKeys() {
      // 根据断言类型返回参数键
      const name = this.predicateForm.name
      
      switch (name) {
        case 'Cookie':
          return ['name', 'regexp']
        case 'Query':
          return ['param', 'regexp']
        case 'RemoteAddr':
          return ['sources']
        case 'After':
        case 'Before':
          return ['datetime']
        case 'Between':
          return ['datetime1', 'datetime2']
        case 'Weight':
          return ['group', 'weight']
        default:
          return Object.keys(this.predicateForm.arg || {})
      }
    },
    
    // 重置断言表单
    resetPredicateForm() {
      if (this.$refs.predicateForm) {
        this.$refs.predicateForm.resetFields()
      }
      this.predicateForm = {
        routeId: this.currentRoute ? this.currentRoute.id : '',
        name: '',
        arg: {},
        sort: 0,
        enable: true
      }
    },

    predicateEnableChange(predicate) {
      var {id, enable} = predicate
      this.submitLoading = true
      updatePredicate({id, enable}).then(res => {
        this.$message.success('更新成功')
        this.submitLoading = false
      }).catch(e => {
        enable = !enable
        this.submitLoading = false
      })
    },
    
    // 提交断言表单
    async submitPredicateForm() {
      this.$refs.predicateForm.validate(async valid => {
        if (!valid) return
        
        this.submitLoading = true
        
        try {
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, this.editMode ? '更新成功' : '创建成功')
          } else {
            // 实际API调用
            if (this.editMode) {
              await updatePredicate(this.predicateForm)
              this.$message.success('更新成功')
            } else {
              await createPredicate(this.predicateForm)
              this.$message.success('创建成功')
            }
          }
          
          this.predicateDialogVisible = false
          await this.getRouteConfigs(this.currentRoute.id)
          await this.fetchAllConfigs()
        } catch (error) {
          this.$message.error(this.editMode ? '更新失败' : '创建失败')
          console.error(error)
        } finally {
          this.submitLoading = false
        }
      })
    },
    
    // 添加过滤器
    handleAddFilter() {
      this.editMode = false
      this.filterForm = {
        routeId: this.currentRoute.id,
        name: '',
        arg: {},
        enable: true,
        sort:0
      }
      this.filterDialogVisible = true
    },
    
    // 编辑过滤器
    handleEditFilter(filter) {
      this.editMode = true
      this.filterForm = JSON.parse(JSON.stringify(filter))
      this.filterDialogVisible = true
    },
    
    // 删除过滤器
    handleDeleteFilter(filter) {
      this.$confirm('确认删除该过滤器吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, '删除成功')
          } else {
            // 实际API调用
            await deleteFilter(filter.id)
            this.$message.success('删除成功')
          }
          await this.getRouteConfigs(this.currentRoute.id)
          await this.fetchAllConfigs()
        } catch (error) {
          this.$message.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    },
    
    // 过滤器类型变更
    handleFilterTypeChange(value) {
      // 清空参数
      this.filterForm.arg = {}
      
      // 根据不同过滤器类型设置不同的默认参数
      switch (value) {
        case 'AddRequestHeader':
        case 'AddResponseHeader':
        case 'SetResponseHeader':
          this.filterForm.arg.name = ''
          this.filterForm.arg.value = ''
          break
        case 'StripPrefix':
          this.filterForm.arg.parts = 1
          break
        case 'RewritePath':
          this.filterForm.arg.regexp = ''
          this.filterForm.arg.replacement = ''
          break
        case 'SetPath':
          this.filterForm.arg.template = ''
          break
        case 'SetStatus':
          this.filterForm.arg.status = 200
          break
        default:
          // 其他类型过滤器的默认处理
          break
      }
    },
    
    // 获取过滤器参数键
    getFilterArgKeys() {
      // 根据过滤器类型返回参数键
      const name = this.filterForm.name
      
      switch (name) {
        case 'AddRequestParameter':
          return ['name', 'value']
        case 'PrefixPath':
          return ['prefix']
        case 'RemoveRequestHeader':
        case 'RemoveResponseHeader':
          return ['name']
        case 'RemoveRequestParameter':
          return ['name']
        case 'RewritePath':
          return ['regexp', 'replacement']
        case 'SetPath':
          return ['template']
        case 'SetStatus':
          return ['status']
        case 'RequestSize':
          return ['maxSize']
        default:
          return Object.keys(this.filterForm.arg || {})
      }
    },
    
    // 获取参数标签
    getArgLabel(key) {
      const labelMap = {
        name: '名称',
        value: '值',
        regexp: '正则表达式',
        replacement: '替换值',
        template: '模板',
        prefix: '前缀',
        status: '状态码',
        maxSize: '最大大小',
        parts: '前缀数量',
        pattern: '模式',
        method: 'HTTP方法',
        header: '请求头',
        param: '参数名',
        sources: '源地址',
        datetime: '日期时间',
        datetime1: '开始时间',
        datetime2: '结束时间',
        group: '分组',
        weight: '权重'
      }
      
      return labelMap[key] || key
    },
    
    // 获取参数占位符
    getArgPlaceholder(key) {
      const placeholderMap = {
        name: '请输入名称',
        value: '请输入值',
        regexp: '请输入正则表达式',
        replacement: '请输入替换值',
        template: '请输入模板',
        prefix: '请输入前缀',
        status: '请输入状态码',
        maxSize: '请输入最大大小',
        parts: '请输入前缀数量',
        pattern: '请输入模式',
        method: '请选择HTTP方法',
        header: '请输入请求头',
        param: '请输入参数名',
        sources: '请输入源地址',
        datetime: '请输入日期时间',
        datetime1: '请输入开始时间',
        datetime2: '请输入结束时间',
        group: '请输入分组',
        weight: '请输入权重'
      }
      
      return placeholderMap[key] || `请输入${key}`
    },
    
    // 重置过滤器表单
    resetFilterForm() {
      if (this.$refs.filterForm) {
        this.$refs.filterForm.resetFields()
      }
      this.filterForm = {
        routeId: this.currentRoute ? this.currentRoute.id : '',
        name: '',
        arg: {}
      }
    },

    filterEnableChange(filter) {
      var {id, enable} = filter
      this.submitLoading = true
      updateFilter({id, enable}).then(res => {
        this.$message.success('更新成功')
        this.submitLoading = false
      }).catch(e => {
        enable = !enable
        this.submitLoading = false
      })
    },
    
    // 提交过滤器表单
    async submitFilterForm() {
      this.$refs.filterForm.validate(async valid => {
        if (!valid) return
        
        this.submitLoading = true
        
        try {
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, this.editMode ? '更新成功' : '创建成功')
          } else {
            // 实际API调用
            if (this.editMode) {
              await updateFilter(this.filterForm)
              this.$message.success('更新成功')
            } else {
              await createFilter(this.filterForm)
              this.$message.success('创建成功')
            }
          }
          
          this.filterDialogVisible = false
          await this.getRouteConfigs(this.currentRoute.id)
          await this.fetchAllConfigs()
        } catch (error) {
          this.$message.error(this.editMode ? '更新失败' : '创建失败')
          console.error(error)
        } finally {
          this.submitLoading = false
        }
      })
    },
    
    // 添加安全配置
    handleAddSecurity() {
      this.editMode = false
      this.securityForm = {
        routeId: this.currentRoute.id,
        matcher: '',
        arg: [],
        enable: true,
        sort: 0
      }
      this.securityDialogVisible = true
    },
    
    // 编辑安全配置
    handleEditSecurity(security) {
      this.editMode = true
      this.securityForm = JSON.parse(JSON.stringify(security))
      this.securityDialogVisible = true
    },
    
    // 删除安全配置
    handleDeleteSecurity(security) {
      this.$confirm('确认删除该安全配置吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, '删除成功')
          } else {
            // 实际API调用
            await deleteSecurity(security.id)
            this.$message.success('删除成功')
          }
          await this.getRouteConfigs(this.currentRoute.id)
          await this.fetchAllConfigs()
        } catch (error) {
          this.$message.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    },
    
    // 显示安全参数输入框
    showSecurityArgInput() {
      this.inputArgVisible = true
      this.$nextTick(_ => {
        this.$refs.saveArgInput.$refs.input.focus()
      })
    },
    
    // 关闭安全参数标签
    handleSecurityArgClose(index) {
      this.securityForm.arg.splice(index, 1)
    },
    
    // 确认添加安全参数
    handleSecurityArgConfirm() {
      const inputValue = this.inputArgValue
      
      this.securityForm.arg.push(inputValue)
      this.inputArgVisible = false
      this.inputArgValue = ''
    },
    
    // 重置安全配置表单
    resetSecurityForm() {
      if (this.$refs.securityForm) {
        this.$refs.securityForm.resetFields()
      }
      this.securityForm = {
        routeId: this.currentRoute ? this.currentRoute.id : '',
        matcher: '',
        arg: []
      }
      this.inputArgVisible = false
      this.inputArgValue = ''
    },

    securityEnableChange(security) {
      var {id, enable} = security
      this.submitLoading = true
      updateSecurity({id, enable}).then(res => {
        this.$message.success('更新成功')
        this.submitLoading = false
      }).catch(err => {
        security.enable = !enable
        this.submitLoading = false
      })
    },
    
    // 提交安全配置表单
    async submitSecurityForm() {
      this.$refs.securityForm.validate(async valid => {
        if (!valid) return
        
        // 如果选择的是需要参数的匹配规则，但没有参数
        if (['hasRole', 'hasAuthority', 'hasIpAddress'].includes(this.securityForm.matcher) && 
            (!this.securityForm.arg || this.securityForm.arg.length === 0)) {
          this.$message.warning('请添加匹配参数')
          return
        }
        
        this.submitLoading = true
        
        try {
          if (this.mockMode) {
            // 使用模拟数据
            await this.mockApiOperation(null, this.editMode ? '更新成功' : '创建成功')
          } else {
            // 实际API调用
            if (this.editMode) {
              await updateSecurity(this.securityForm)
              this.$message.success('更新成功')
            } else {
              await createSecurity(this.securityForm)
              this.$message.success('创建成功')
            }
          }
          
          this.securityDialogVisible = false
          await this.getRouteConfigs(this.currentRoute.id)
          await this.fetchAllConfigs()
        } catch (error) {
          this.$message.error(this.editMode ? '更新失败' : '创建失败')
          console.error(error)
        } finally {
          this.submitLoading = false
        }
      })
    },
    
    // 应用配置
    handleApplyConfig() {
      this.applyDialogVisible = true
    },
    
    // 确认应用配置
    async confirmApplyConfig() {
      this.applyLoading = true
      try {
        if (this.mockMode) {
          // 使用模拟数据
          await this.mockApiOperation(null, '配置已成功应用')
        } else {
          // 实际API调用
          await applyGatewayConfig()
          this.$message.success('配置已成功应用')
        }
        this.applyDialogVisible = false
      } catch (error) {
        this.$message.error('应用配置失败')
        console.error(error)
      } finally {
        this.applyLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.gateway-security-container {
  height: calc(100vh - 75px);
  padding: 12px;
  margin: 12px;
  background: inherit;
  overflow: hidden;

  .el-row {
    height: 100%;
    width: 100%;
    margin: 0 !important;
    
    .el-col {
      height: 100%;
    }
  }

  .card-container {
    height: 100%;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(31, 45, 61, 0.1);
    }
  }

  .card-header {
    padding: 15px;
    border-bottom: 1px solid #eef1f7;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    
    &.compact-header {
      padding: 16px 20px;
      
      .header-title-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .header-title {
          h2 {
            margin: 0;
          }
        }
        
        .action-bar {
          margin-top: 0 !important;
        }
      }
    }
    
    .header-title {
      margin: 0 0 12px 0;
      h2 {
        margin: 0 0 0 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        display: flex;
        align-items: center;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
        
        .title-text {
          margin-right: 16px;
        }
      }
    }
    
    .config-tip {
      
      ::v-deep .el-alert {
        background-color: #fdf6ec;
        border: 1px solid rgba(230, 162, 60, 0.2);
        border-radius: 8px;
        padding: 8px 16px;
        
        .el-alert__icon {
          font-size: 16px;
          color: #e6a23c;
        }
        
        .el-alert__title {
          font-size: 13px;
          line-height: 1.4;
          color: #b88230;
        }
      }
    }
  }

  .card-body {
    flex: 1;
    overflow: hidden;
    padding: 20px;
    height: 0;
    min-height: 0;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    
    &.config-container {
      padding-top: 0;
      overflow-y: auto;
    }
  }

  .flow-panel {
    .flow-list {
      padding: 10px;
      height: 100%;
      overflow-y: auto;
      margin-right: 2px;
      
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 4px;
        border: 2px solid #fff;
        background-clip: padding-box;
        
        &:hover {
          background: rgba(144, 147, 153, 0.5);
          border: 2px solid #fff;
          background-clip: padding-box;
        }
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
      }
    }
  }

  .empty-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #606266;
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    margin-top: 20px;
    
    i {
      font-size: 60px;
      margin-bottom: 20px;
      color: #e6a23c;
      opacity: 0.8;
    }
    
    p {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .route-detail {
    margin-bottom: 20px;
    
    .detail-card {
      background-color: #f8f9fc;
      border-radius: 12px;
      padding: 16px;
      margin-top: 16px;
      border: 1px solid #ebeef5;
      
      .detail-item {
        margin-bottom: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          display: block;
          color: #909399;
          font-size: 13px;
          margin-bottom: 5px;
        }
        
        .value {
          color: #303133;
          font-size: 14px;
          word-break: break-all;
        }
        
        .metadata-list {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
          margin-top: 5px;
          
          .metadata-item {
            border-radius: 4px;
            background-color: #f0f9eb;
            border-color: #e1f3d8;
            color: #67c23a;
          }
        }
        
        .no-data {
          color: #c0c4cc;
          font-style: italic;
        }
      }
    }
  }

  .config-node-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .node-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 16px 12px;
      border-bottom: 1px solid #ebeef5;
      
      .node-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        
        i {
          margin-right: 8px;
          font-size: 18px;
        }
        
        .el-tag {
          margin-left: 8px;
        }
      }
    }
    
    .node-list {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #fafbfc;
      min-height: 200px;
    }
  }

  .metadata-form-items {
    margin-top: 10px;
    
    .metadata-form-item {
      margin-bottom: 15px;
      background-color: #f8f9fc;
      border-radius: 10px;
      padding: 12px;
      border: 1px solid #ebeef5;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
        border-color: #e0e5ee;
      }
      
      .el-row {
        margin: 0 !important;
      }
      
      .metadata-key {
        ::v-deep .el-input__inner {
          border-radius: 6px;
        }
      }
      
      .metadata-value {
        ::v-deep .el-input__inner {
          border-radius: 6px;
        }
      }
      
      .el-button {
        transition: all 0.3s ease;
        margin-top: 0;
        
        &:hover {
          transform: rotate(90deg);
          color: #F56C6C;
          background-color: #fef0f0;
        }
      }
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
    margin-top: 4px;
  }
  
  .security-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    .el-tag {
      display: flex;
      align-items: center;
      padding: 0 10px;
      height: 28px;
      border-radius: 6px;
      background-color: #ecf5ff;
      border-color: #d9ecff;
      color: #409EFF;
      font-size: 13px;
      
      .el-tag__close {
        font-size: 12px;
        color: #409EFF;
        background-color: transparent;
        right: -2px;
        
        &:hover {
          background-color: #409EFF;
          color: #fff;
        }
      }
    }
  }
  
  .input-new-arg {
    width: 120px;
    vertical-align: middle;
    height: 28px;
    
    ::v-deep .el-input__inner {
      height: 28px;
      line-height: 28px;
      border-radius: 4px;
    }
  }
  
  .button-new-arg {
    height: 28px;
    line-height: 26px;
    padding: 0 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 4px;
      font-size: 12px;
    }
  }
  
  .apply-dialog-content {
    display: flex;
    align-items: center;
    padding: 10px;
    
    .warning-icon {
      font-size: 48px;
      color: #E6A23C;
      margin-right: 20px;
      background-color: rgba(230, 162, 60, 0.1);
      border-radius: 50%;
      padding: 14px;
      height: 48px;
      width: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .apply-message {
      .apply-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 10px;
      }
      
      .apply-desc {
        color: #606266;
        margin: 0;
        line-height: 1.6;
      }
    }
  }
}

// 对话框样式美化
::v-deep .gateway-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
    
    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      
      .el-dialog__close {
        font-size: 18px;
        color: #909399;
        font-weight: bold;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    background: #f8f9fb;

    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .el-form {
      .el-form-item {
        margin-bottom: 22px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-form-item__label {
          font-weight: 500;
          color: #1a1f36;
          padding-right: 15px;
        }
        
        .el-form-item__content {
          .el-input__inner, 
          .el-textarea__inner {
            border-radius: 10px;
            border: 1px solid #e0e5ee;
            background-color: #fff;
            transition: all 0.3s ease;
            
            &:hover {
              border-color: #c0d0e9;
            }
            
            &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
          }
          
          .el-input__inner {
            height: 38px;
            line-height: 38px;
          }
          
          .el-select {
            width: 100%;
          }
          
          .el-input-number {
            .el-input__inner {
              height: 38px;
              line-height: 38px;
              text-align: left;
            }
          }
        }
      }
      
      .form-section {
        background-color: #f8f9fb;
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #f5f7fa;
        }
        
        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          margin-bottom: 20px;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;
        }

      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    
    .el-button {
      padding: 9px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      & + .el-button {
        margin-left: 12px;
      }

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);
        
        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

// 应用配置确认对话框样式
::v-deep .confirm-dialog {
  .el-dialog__body {
    padding: 30px;
    background: #fff;
  }
  
  .apply-dialog-content {
    background-color: #fdf6ec;
    border-radius: 16px;
    padding: 20px;
  }
}
.metadata-form {
  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
