<template>
  <div>
    <auto-page :options="options" :form-rule="options.formRule">
      <template #tablecreateBy="rows">
        <span> {{getNameFun(rows.row.createBy)}} </span>
      </template>
    </auto-page>
  </div>
</template>

<script>
  // import options from "./options";
  import autoPage from "@/components/auto-page/AutoPage";
  import {list} from "@system/api/sys/account"
  export default {
    components:{autoPage},
    data(){
      return{
        options: null,
        accountList: []
      }
    },
    created(){
      // this.options = options
      this.getAccountList()
    },
    methods:{
      getAccountList(){
        list({size:-1,valid:true}).then(res=>{
          this.accountList = res.records
        })
      },
      getNameFun(val) {
        let n ='--';
        let account = this.accountList.find(i => i.id == val);
        if (account != null){
          n = account.name
        }
        return n;
      }
    }
  }
</script>
