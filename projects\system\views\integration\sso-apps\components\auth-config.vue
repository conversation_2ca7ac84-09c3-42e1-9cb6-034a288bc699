<template>
  <div class="auth-config">
    <el-form 
      ref="authForm" 
      :model="authConfig"
      :rules="rules"
      label-width="180px"
      class="auth-form"
    >
      <el-form-item label="配置唯一标识" prop="ssoConfigUniqueId">
        <el-input 
          v-model="authConfig.ssoConfigUniqueId"
          placeholder="请输入配置唯一标识"
        />
      </el-form-item>

      <!-- 认证方式选择 -->
      <el-form-item label="认证方式" prop="authType">
        <el-select 
          v-model="authConfig.authType" 
          placeholder="请选择认证方式"
          @change="handleAuthTypeChange"
        >
          <el-option
            v-for="item in authTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- OAuth 配置项 -->
      <template v-if="authConfig.authType === 'OAuth2'">
        <el-form-item label="Client ID" prop="clientId">
          <el-input 
            v-model="authConfig.clientId"
            placeholder="请输入Client ID"
          >
            <el-button 
              slot="append" 
              @click="generateCredentials"
            >
              生成
            </el-button>
          </el-input>
        </el-form-item>

        <el-form-item label="Client Secret" prop="clientSecret">
          <el-input 
            v-model="authConfig.clientSecret"
            placeholder="请输入Client Secret"
            type="password"
            show-password
          >
            <el-button 
              slot="append" 
              @click="generateCredentials"
            >
              生成
            </el-button>
          </el-input>
        </el-form-item>

        <el-form-item label="回调地址" prop="redirectUri">
          <el-input 
            v-model="authConfig.redirectUri"
            placeholder="请输入回调地址"
          />
        </el-form-item>

        <el-form-item label="验证模式" prop="verifyMode">
          <div class="verify-mode-tip">
            <p>验证模式说明：</p>
            <p>- 宽松模式：只校验回调 URI 的域名是否相同</p>
            <p>- 严格模式：回调 URI 必须完全匹配</p>
          </div>
          <el-select v-model="authConfig.verifyMode" placeholder="请选择验证模式">
            <el-option label="宽松" value="LOOSE" />
            <el-option label="严格" value="STRICT" />
          </el-select>
        </el-form-item>
      </template>

      <!-- SAML 配置项 -->
      <template v-if="authConfig.authType === 'SAML'">
        <el-form-item label="Entity ID" prop="entityId">
          <el-input 
            v-model="authConfig.entityId"
            placeholder="请输入Entity ID"
          />
        </el-form-item>

        <el-form-item label="ACS URL" prop="acsUrl">
          <el-input 
            v-model="authConfig.acsUrl"
            placeholder="请输入ACS URL"
          />
        </el-form-item>

        <el-form-item label="证书" prop="certificate">
          <el-input 
            type="textarea"
            v-model="authConfig.certificate"
            placeholder="请输入证书内容"
            :rows="4"
          />
        </el-form-item>
      </template>

      <!-- LDAP 配置项 -->
      <template v-if="authConfig.authType === 'LDAP'">
        <el-form-item label="LDAP 服务器地址" prop="serverUrl">
          <el-input 
            v-model="authConfig.serverUrl"
            placeholder="请输入LDAP服务器地址"
          />
        </el-form-item>

        <el-form-item label="Base DN" prop="baseDn">
          <el-input 
            v-model="authConfig.baseDn"
            placeholder="请输入Base DN"
          />
        </el-form-item>

        <el-form-item label="绑定DN" prop="bindDn">
          <el-input 
            v-model="authConfig.bindDn"
            placeholder="请输入绑定DN"
          />
        </el-form-item>

        <el-form-item label="绑定密码" prop="bindPassword">
          <el-input 
            v-model="authConfig.bindPassword"
            type="password"
            show-password
            placeholder="请输入绑定密码"
          />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'

export default {
  name: 'AuthConfig',
  data() {
    return {
      authTypes: [
        { label: 'OAuth 2.0', value: 'OAuth2' },
        { label: 'SAML 2.0', value: 'SAML' },
        { label: 'LDAP', value: 'LDAP' }
      ],
      authConfig: {
        ssoConfigUniqueId: '',
        authType: '',
        // OAuth配置
        clientId: '',
        clientSecret: '',
        redirectUri: '',
        verifyMode: 'LOOSE',
        // SAML配置
        entityId: '',
        acsUrl: '',
        certificate: '',
        // LDAP配置
        serverUrl: '',
        baseDn: '',
        bindDn: '',
        bindPassword: ''
      },
      rules: {
        ssoConfigUniqueId: [
          { required: true, message: '请输入配置唯一标识', trigger: 'blur' }
        ],
        authType: [
          { required: true, message: '请选择认证方式', trigger: 'change' }
        ],
        // OAuth验证规则
        clientId: [
          { required: true, message: '请输入Client ID', trigger: 'blur' }
        ],
        clientSecret: [
          { required: true, message: '请输入Client Secret', trigger: 'blur' }
        ],
        redirectUri: [
          { required: true, message: '请输入回调地址', trigger: 'blur' },
          { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
        ],
        verifyMode: [
          { required: true, message: '请选择验证模式', trigger: 'change' }
        ],
        // SAML验证规则
        entityId: [
          { required: true, message: '请输入Entity ID', trigger: 'blur' }
        ],
        acsUrl: [
          { required: true, message: '请输入ACS URL', trigger: 'blur' },
          { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
        ],
        certificate: [
          { required: true, message: '请输入证书内容', trigger: 'blur' }
        ],
        // LDAP验证规则
        serverUrl: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' }
        ],
        baseDn: [
          { required: true, message: '请输入Base DN', trigger: 'blur' }
        ],
        bindDn: [
          { required: true, message: '请输入绑定DN', trigger: 'blur' }
        ],
        bindPassword: [
          { required: true, message: '请输入绑定密码', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleAuthTypeChange(value) {
      // 切换认证方式时重置表单
      this.$refs.authForm.clearValidate()
    },
    handleRefreshChange(value) {
      if (!value) {
        this.authConfig.refreshInterval = 300
      }
    },
    generateCredentials() {
      // 生成随机的Client ID和Secret
      this.authConfig.clientId = `${uuidv4().replace(/-/g, '')}`
      this.authConfig.clientSecret = uuidv4().replace(/-/g, '')
    },
    setConfig(config) {
      // 根据传入的配置更新组件的数据
      if (config) {
        this.authConfig = {
          ...this.authConfig, // 保留默认值
          ...config // 使用传入的配置覆盖
        }
      }
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.authForm.validate((valid) => {
          if (valid) {
            resolve(this.authConfig)
          } else {
            reject(new Error('认证配置验证失败'))
          }
        })
      })
    },
    getAuthConfig() {
      return this.authConfig
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-config {
  padding: 20px;

  .auth-form {
    max-width: 680px;
    margin: 0 auto;

    .verify-mode-tip {
      margin-bottom: 10px;
      padding: 12px 16px;
      background: #f8f9fb;
      border-radius: 4px;
      
      p {
        margin: 0;
        line-height: 1.8;
        color: #606266;
        font-size: 13px;
        
        &:first-child {
          color: #303133;
          margin-bottom: 4px;
        }
      }
    }
  }

  ::v-deep .el-input-number {
    width: 100%;
  }

  ::v-deep .el-select {
    width: 100%;
  }
}
</style> 