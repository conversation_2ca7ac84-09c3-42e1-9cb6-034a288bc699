<template>
    <div>
        <div class="audio-permission">
            <button @click="requestAudioPermission" :class="{ 'has-permission': hasPermission }">
                {{ hasPermission ? '已获取录音权限' : '获取录音权限' }}
            </button>
            <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>

            <voiceprint :volumes="volumes" :barCount="barCount"></voiceprint>
        </div>
    </div>
</template>

<script>
import { checkPermission, getUserMedia, newAudioContext, createMediaStreamSource, createScriptProcessor, mediaClose } from './audiolib/AudioHelper'
import voiceprint from './voiceprint.vue'
export default {
    components: {
        voiceprint
    },
    data() {
        return {
            hasPermission: false,
            errorMessage: '',
            volumes: [],
            barCount: 32
        }
    },
    methods: {

        close() {
            this.volumes = []
            this.hasPermission = false
            this.errorMessage = ''
            mediaClose(this.mediaStreamSource, this.scriptProcessorNode, this.audioContext)
        },
        async requestAudioPermission() {
            if (this.hasPermission) {
                this.close()
                return
            }
            let obj = await checkPermission()
            this.hasPermission = obj.hasPermission
            this.errorMessage = obj.errorMessage || ''
            if (this.hasPermission) {
                getUserMedia()
                    .then((stream) => {
                        let audioContext = newAudioContext()
                        this.audioContext = audioContext
                        let mediaStreamSource = createMediaStreamSource(audioContext, stream)
                        this.mediaStreamSource = mediaStreamSource
                        let scriptProcessorNode = createScriptProcessor(audioContext)
                        this.scriptProcessorNode = scriptProcessorNode
                        mediaStreamSource.connect(scriptProcessorNode)
                        scriptProcessorNode.connect(audioContext.destination)
                        scriptProcessorNode.onaudioprocess = this.onaudioprocess
                    })
                    .catch(err => {

                    })
            }
        },
        onaudioprocess(e) {
            const audioData = e.inputBuffer.getChannelData(0);
            let barCount = this.barCount;
            // 计算音量用于可视化
            let volumes = [];
            const segmentSize = Math.floor(audioData.length / barCount);

            for (let i = 0; i < barCount; i++) {
                let sum = 0;
                const start = i * segmentSize;
                const end = start + segmentSize;

                for (let j = start; j < end; j++) {
                    sum += Math.abs(audioData[j]);
                }

                const avg = sum / segmentSize;
                volumes.push(avg);
            }

            this.$set(this, 'volumes', volumes.map(vol => {
                return {
                    height: `${Math.min(100, vol * 300)}%`
                }
            }))

            // console.log('=====--00', this.volumes);

        }
    }
}
</script>

<style scoped>
.audio-permission {
    margin-top: 20px;
}

button {
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

button:hover {
    background-color: #f0f0f0;
}

button.has-permission {
    background-color: #4CAF50;
    color: white;
    border-color: #45a049;
}

.error-message {
    color: #f44336;
    margin-top: 10px;
}
</style>