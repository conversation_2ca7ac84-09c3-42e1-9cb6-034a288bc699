<template>
    <div class="visualizer-container">
        <div class="visualizer">
            <div v-for="index in barCount" :key="index" class="visualizer-bar"
                :style="`width:${100.0 / barCount}%;height:${volumes[index]?.height || '1px'}`"></div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        volumes: {
            type: Array,
            default: () => [{ height: 0 }]
        },
        barCount: {
            type: Number,
            default: 32
        }
    },
    mounted() {

    }
}
</script>
<style lang="scss" scoped>
.visualizer-container {
    height: 60px;
}

.visualizer {
    height: 100%;
    display: flex;
    align-items: flex-end;
    gap: 3px;
}

.visualizer-bar {
    --primary-color: #6e8efb;
    --secondary-color: #a777e3;
    background: linear-gradient(to top, var(--secondary-color), var(--primary-color));
    border-radius: 3px 3px 0 0;
    width: 6px;
    min-height: 1px;
    transition: height 0.1s ease-out;
}
</style>
