# 连接器API

现在来完成对接连接器的接口


const integrationAppApi = CONSTANT.INTEGRATION+ '/integration/app'

// 获取集成应用列表
export function getIntegrationAppList(params) {
return request({
url: `${integrationAppApi}/list`,
method: 'get',
params
})
}

// 获取集成应用分组统计
export function getIntegrationAppCount(params) {
return request({
url: `${integrationAppApi}/count`,
method: 'get',
params
})
}

// 获取单个集成应用详情
export function getIntegrationAppDetail(id) {
return request({
url: `${integrationAppApi}`,
method: 'get',
params: { id }
})
}

// 创建集成应用
export function createIntegrationApp(data) {
return request({
url: `${integrationAppApi}`,
method: 'post',
data
})
}

// 更新集成应用
export function updateIntegrationApp(data) {
return request({
url: `${integrationAppApi}`,
method: 'put',
data
})
}

// 删除集成应用
export function deleteIntegrationApp(id) {
return request({
url: `${integrationAppApi}/${id}`,
method: 'delete'
})
}


## 分页接口如下

``` bash 
curl --location --request GET 'http://localhost:8007/integration/http/request/list?connectorId=2&type=API' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive'
```

``` json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "records": [
            {
                "createBy": 1002,
                "updateBy": 1002,
                "createTime": "2025-08-01 14:25:00",
                "updateTime": "2025-08-01 14:25:00",
                "id": 6,
                "remark": "钉钉用户详情查询API",
                "code": "getUserDetail",
                "name": "获取钉钉用户详情",
                "description": "根据用户ID获取钉钉用户的详细信息",
                "contentType": "application/json",
                "url": "/topapi/v2/user/get",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "userid": "${userId}"
                },
                "connectorId": 2,
                "type": "API",
                "enabled": true
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    },
    "success": true
}
```

