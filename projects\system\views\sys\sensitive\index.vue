<template>
  <div>
    <auto-page :options="options" :form-rule="options.formRule" :defaultQuery="{orders:{'createTime':'desc'}}" ref="rules">
    </auto-page>
  </div>
</template>

<script>
  import options from "./options";
  import autoPage from "@/components/auto-page/AutoPage";
  export default {
    components:{autoPage},
    data(){
      return{
        options: null,
        accountList: []
      }
    },
    created(){
      this.options = options
    },
    methods:{
    }
  }
</script>
