<template>
  <div class="api-permission-container" v-loading="apiLoading">
    <div class="header-actions">
      <div class="left-section">
        <div class="module-selection">
          <span class="module-title">微服务模块：</span>
          <el-select 
            v-model="selectedModule" 
            placeholder="请选择微服务模块" 
            class="module-selector"
            @change="handleModuleChange"
          >
            <el-option
              v-for="module in moduleList"
              :key="module.name"
              :label="module.name"
              :value="module.name"
              :disabled="!module.supportPermission"
            />
          </el-select>
        </div>
        
        <div class="tip-box">
          <el-tooltip effect="dark" placement="bottom" max-width="300">
            <div slot="content">
              <p>部分内部监测接口不对外开放，必须角色大于等于ops权限</p>
              <p>例如：网关、外部认证、权限配置等</p>
            </div>
            <el-tag type="warning" size="medium">
              <i class="el-icon-warning-outline"></i> 权限提示
            </el-tag>
          </el-tooltip>
        </div>
      </div>
      
      <div class="view-controls">
        <div class="view-mode">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="all">全部接口</el-radio-button>
            <el-radio-button label="bound">已绑定</el-radio-button>
            <el-radio-button label="unbound">未绑定</el-radio-button>
          </el-radio-group>
        </div>

        <div class="group-selection">
          <span class="group-title">分组方式：</span>
          <el-select 
            v-model="groupBy" 
            placeholder="请选择分组方式" 
            class="group-selector"
            @change="handleGroupByChange"
          >
            <el-option
              label="不分组"
              value=""
            />
            <el-option
              label="按控制器分组"
              value="className"
            />
          </el-select>
        </div>
        
        <el-button type="primary" size="small" class="refresh-btn" @click="getModuleApiList(selectedModule)">
          <i class="el-icon-refresh"></i> 刷新接口列表
        </el-button>
      </div>
    </div>
    
    <!-- 搜索框和搜索模式选择 -->
    <div class="search-box">
      <div class="search-input-container">
        <div class="input-wrapper">
          <el-input
            v-model="searchKeyword"
            placeholder="输入关键字或Ant风格路径模式匹配 (例如: /api/**)"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
            class="search-input"
          />
        </div>
        <div class="selector-wrapper">
          <el-select v-model="searchMode" size="small" class="search-mode-selector">
            <el-option label="普通搜索" value="normal" />
            <el-option label="Ant模式匹配" value="ant" />
          </el-select>
        </div>
        <el-button type="primary" class="search-btn" @click="handleSearch">
          <i class="el-icon-search"></i> 搜索
        </el-button>
      </div>
      <div class="search-tips" v-if="searchMode === 'ant'">
        <i class="el-icon-info"></i>
        <span>支持通配符: ? (单字符), * (多字符), ** (多路径段)，针对URL路径进行匹配</span>
      </div>
    </div>
    
    <!-- 缺省图 -->
    <div class="empty-placeholder" v-if="showEmptyPlaceholder">
      <svg class="empty-image" viewBox="0 0 184 152" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="linearGradient-1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop stop-color="#EAEDF1" offset="0%"></stop>
            <stop stop-color="#DCDFE6" offset="100%"></stop>
          </linearGradient>
        </defs>
        <g fill="none" fill-rule="evenodd">
          <g transform="translate(24 31.67)">
            <ellipse fill-opacity=".8" fill="#F5F5F7" cx="67.797" cy="106.89" rx="67.797" ry="12.668"/>
            <path d="M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z" fill="#AEB8C2"/>
            <path d="M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z" fill="url(#linearGradient-1)" transform="translate(13.56)"/>
            <path d="M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z" fill="#F5F5F7"/>
            <path d="M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z" fill="#DCE0E6"/>
          </g>
          <path d="M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z" fill="#DCE0E6"/>
          <g transform="translate(149.65 15.383)" fill="#FFF">
            <ellipse cx="20.654" cy="3.167" rx="2.849" ry="2.815"/>
            <path d="M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"/>
          </g>
        </g>
      </svg>
      <p class="empty-text">未找到接口数据</p>
    </div>
    
    <!-- 控制器分组视图 -->
    <template v-else-if="groupBy === 'className' && !apiDataLoading">
      <div class="controller-groups">
        <el-collapse v-model="activeGroupNames">
          <el-collapse-item  
            v-for="(group, className) in groupedApis" 
            :key="className" 
            :name="className"
          >
            <template slot="title">
              <div class="group-title-container">
                <span>{{ getControllerDisplayName(className) }}</span>
                <span v-if="getControllerDescription(className)" class="controller-description">{{ getControllerDescription(className) }}</span>
                <el-tag size="small" type="info" class="api-count-tag">{{ group.length }} 个接口</el-tag>
              </div>
            </template>
            <el-table
              :ref="`groupTable_${className}`"
              :data="group"
              style="width: 100%"
              @selection-change="(selection) => handleGroupSelectionChange(selection, className)"
              border
              stripe
              fit
              highlight-current-row
            >
              <el-table-column type="selection" :width=rpx(50) align="center" 
                :selectable="row => selectedRole && selectedRole.code !== 'admin'">
              </el-table-column>
              <el-table-column prop="permission" label="权限标识" min-width="200">
                <template slot-scope="scope">
                  <el-tooltip :content="scope.row.permission" placement="top" :disabled="scope.row.permission.length < 30">
                    <div class="permission-cell">
                      <i class="el-icon-key"></i>
                      <span>{{ scope.row.permission }}</span>
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="接口名称" min-width="150">
                <template slot-scope="scope">
                  <span class="api-name">{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="url" label="接口路径" min-width="150">
                <template slot-scope="scope">
                  <span class="api-url">{{ scope.row.url }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="chineseName" label="描述" min-width="120">
                <template slot-scope="scope">
                  <template v-if="scope.row.chineseName">
                    <span>{{ scope.row.chineseName }}</span>
                  </template>
                  <template v-else>
                    <el-tag size="small" type="warning">需添加@ApiOperation注解</el-tag>
                  </template>
                </template>
              </el-table-column>
              <el-table-column prop="method" label="请求方式" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getMethodTag(scope.row.method)" effect="dark">{{ scope.row.method }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="groupName" label="所属模块" min-width="120"></el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </template>
    
    <!-- 默认视图 -->
    <div class="permission-list" v-else>
      <el-table
        ref="apiTable"
        :data="filteredApiList"
        style="width: 100%"
        v-loading="apiDataLoading"
        @selection-change="handleApiSelectionChange"
        border
        stripe
        fit
        highlight-current-row
      >
        <el-table-column type="selection" :width=rpx(50) align="center" 
          :selectable="row => selectedRole && selectedRole.code !== 'admin'">
        </el-table-column>
        <el-table-column prop="permission" label="权限标识" min-width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.permission" placement="top" :disabled="scope.row.permission.length < 30">
              <div class="permission-cell">
                <i class="el-icon-key"></i>
                <span>{{ scope.row.permission }}</span>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="接口名称" min-width="150">
          <template slot-scope="scope">
            <span class="api-name">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="chineseName" label="中文名称" min-width="180">
          <template slot-scope="scope">
            <template v-if="scope.row.chineseName">
              <span>{{ scope.row.chineseName }}</span>
            </template>
            <template v-else>
              <el-tag size="small" type="warning">需@ApiOperation注解</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="method" label="请求方式" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getMethodTag(scope.row.method)" effect="dark">{{ scope.row.method }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="className" label="控制器类" min-width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.className" placement="top" :disabled="scope.row.className.length < 30">
              <span class="controller-name">{{ getControllerDisplayName(scope.row.className) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="groupName" label="所属模块" min-width="120"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getModulePermissions, getGatewayRoutes } from '@system/api/sys/resourcePermission'

export default {
  name: 'ApiPermission',
  props: {
    selectedRole: {
      type: Object,
      default: null
    },
    keyword: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      apiLoading: false,
      apiDataLoading: false,
      selectedModule: '',
      moduleList: [],
      apiList: [],
      filteredApiList: [],
      selectedApiList: [],
      groupBy: 'className', // 分组方式：'' 或 'className'
      groupedApis: {}, // 按控制器分组的API
      activeGroupNames: [], // 当前展开的分组
      groupSelections: {}, // 每个分组中选中的数据
      dataLoadFailed: false, // 数据加载失败标记
      viewMode: 'all', // 新增的视图模式
      lastSelectedPermissions: null, // 用于记录上一次的权限数据
      searchMode: 'normal', // 搜索模式：normal 或 ant
      searchKeyword: '' // 搜索关键词
    }
  },
  computed: {
    showEmptyPlaceholder() {
      return (this.dataLoadFailed || (!this.apiDataLoading && this.filteredApiList.length === 0))
    },
    filteredByViewMode() {
      if (!this.selectedRole || !this.apiList) return []
      
      const permissions = Array.isArray(this.selectedRole.permissions) ? 
        this.selectedRole.permissions : []

      switch (this.viewMode) {
        case 'bound':
          return this.apiList.filter(api => permissions.includes(api.permission))
        case 'unbound':
          return this.apiList.filter(api => !permissions.includes(api.permission))
        default:
          return this.apiList
      }
    }
  },
  watch: {
    keyword(val) {
      this.searchKeyword = val;
      this.filterApiList();
    },
    'selectedRole.permissions': {
      handler(newPermissions) {
        // 只有当权限数据真正发生变化时才更新
        if (JSON.stringify(newPermissions) !== JSON.stringify(this.lastSelectedPermissions)) {
          this.lastSelectedPermissions = newPermissions
          this.$nextTick(() => {
            this.updateApiSelection()
          })
        }
      },
      deep: true
    },
    selectedRole(newVal, oldVal) {
      if (newVal && newVal.id !== (oldVal && oldVal.id)) {
        this.updateApiSelection()
      }
    },
    viewMode() {
      this.filterApiList()
    },
    searchMode() {
      // 当搜索模式改变时，重新过滤
      this.filterApiList();
    }
  },
  created() {
    this.getModuleList()
  },
  methods: {
    // 处理搜索输入
    handleSearch() {
      this.filterApiList();
    },

    // Ant风格路径匹配
    isAntMatch(pattern, text) {
      // 如果没有通配符，直接进行普通匹配
      if (!pattern.includes('*') && !pattern.includes('?')) {
        return text.includes(pattern);
      }

      // 转换ant风格的模式为正则表达式
      let regexPattern = pattern
        .replace(/\./g, '\\.') // 转义点号
        .replace(/\?/g, '.')   // ? 匹配单个字符
        .replace(/\*\*/g, '###PLACEHOLDER###') // 暂时替换 ** 为占位符
        .replace(/\*/g, '[^/]*') // * 匹配一个路径段内的任意字符
        .replace(/###PLACEHOLDER###/g, '.*'); // ** 匹配任意字符包括路径分隔符
      
      // 创建正则表达式
      const regex = new RegExp('^' + regexPattern + '$');
      
      // 测试是否匹配
      return regex.test(text);
    },

    // 获取模块列表
    async getModuleList() {
      this.apiLoading = true
      this.dataLoadFailed = false
      try {
        const data = await getGatewayRoutes()
        // 转换网关路由数据为模块列表格式
        const routes = data?.records || []
        this.moduleList = routes.map(route => ({
          name: route.name || route.id,
          supportPermission: route.enable !== false,
          id: route.id,
          uri: route.uri,
          pattern: route.pattern,
          sort: route.sort
        }))

        // 自动选择第一个支持权限的模块
        if (this.moduleList.length > 0) {
          const supportedModule = this.moduleList.find(module => module.supportPermission)
          if (supportedModule) {
            this.selectedModule = supportedModule.name
            this.getModuleApiList(this.selectedModule)
          }
        }
      } catch (error) {
        console.error('获取网关路由列表失败:', error)
        this.$message.error('获取网关路由列表失败')
        this.dataLoadFailed = true
      } finally {
        this.apiLoading = false
      }
    },

    // 获取模块的API权限列表
    async getModuleApiList(moduleName) {
      if (!moduleName) return
      
      this.apiDataLoading = true
      this.apiList = [] // 清空之前的数据
      this.dataLoadFailed = false // 重置失败标记
      
      try {
        // 创建一个Promise.race来处理超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 10000) // 10秒超时
        })
        
        const fetchPromise = getModulePermissions(moduleName, {
          current: 1,
          size: -1
        })
        
        const result = await Promise.race([fetchPromise, timeoutPromise])
        this.apiList = result?.records || []
        this.filterApiList()
        
        // 如果使用分组，需要重新构建分组数据
        if (this.groupBy) {
          this.buildGroupedApis()
        }
        
        this.$nextTick(() => {
          this.updateApiSelection()
        })
      } catch (error) {
        console.log('获取接口权限列表问题:', error.message || '未找到数据')
        this.apiList = []
        this.filteredApiList = []
        this.groupedApis = {}
        this.dataLoadFailed = true // 标记数据加载失败
        // 不向用户显示错误，仅清空数据
      } finally {
        this.apiDataLoading = false
      }
    },

    // 处理模块选择变更
    handleModuleChange(moduleName) {
      this.getModuleApiList(moduleName)
    },

    // 处理分组方式变更
    handleGroupByChange(groupBy) {
      if (groupBy === 'className') {
        this.buildGroupedApis()
        // 默认展开第一个分组
        if (Object.keys(this.groupedApis).length > 0) {
          this.activeGroupNames = [Object.keys(this.groupedApis)[0]]
        }
      } else {
        // 切换回不分组模式，需要更新选中状态
        this.$nextTick(() => {
          this.updateApiSelection()
        })
      }
    },

    // 构建按控制器分组的数据
    buildGroupedApis() {
      this.groupedApis = {}
      this.filteredApiList.forEach(api => {
        if (!this.groupedApis[api.className]) {
          this.groupedApis[api.className] = []
        }
        this.groupedApis[api.className].push(api)
      })
      
      // 初始化分组选择状态
      this.groupSelections = {}
      Object.keys(this.groupedApis).forEach(className => {
        this.groupSelections[className] = []
      })
    },

    // 过滤API列表
    filterApiList() {
      // 首先根据视图模式过滤
      let filtered = this.filteredByViewMode;
      
      // 然后根据关键字过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        
        if (this.searchMode === 'ant') {
          // 使用Ant风格模式匹配，只针对URL字段进行匹配
          filtered = filtered.filter(api => {
            return this.isAntMatch(this.searchKeyword, api.url);
          });
        } else {
          // 普通搜索模式
          filtered = filtered.filter(api => 
            api.permission.toLowerCase().includes(keyword) || 
            api.name.toLowerCase().includes(keyword) ||
            (api.chineseName && api.chineseName.toLowerCase().includes(keyword)) ||
            (api.className && api.className.toLowerCase().includes(keyword))
          );
        }
      }
      
      this.filteredApiList = filtered;
      
      // 如果使用分组，需要重新构建分组数据
      if (this.groupBy) {
        this.buildGroupedApis();
      }
      
      this.$nextTick(this.updateApiSelection);
    },

    // 更新API选择状态
    updateApiSelection() {
      if (this.groupBy === 'className') {
        // 更新分组视图的选择状态
        this.updateGroupedApiSelection()
      } else {
        // 更新普通视图的选择状态
        this.updateFlatApiSelection()
      }
    },

    // 更新普通视图的选择状态
    updateFlatApiSelection() {
      // 如果表格还未渲染完成，跳过
      if (!this.$refs.apiTable) return
      
      // 清除当前选择
      this.$refs.apiTable.clearSelection()
      
      // 根据当前角色的permissions预选API
      if (this.selectedRole && this.selectedRole.permissions) {
        const permissions = Array.isArray(this.selectedRole.permissions) ? 
          this.selectedRole.permissions : 
          [];
          
        this.filteredApiList.forEach(api => {
          if (permissions.includes(api.permission)) {
            this.$refs.apiTable.toggleRowSelection(api, true)
          }
        })
      }
    },
    
    // 更新分组视图的选择状态
    updateGroupedApiSelection() {
      // 遍历每个分组
      Object.keys(this.groupedApis).forEach(className => {
        const tableRef = this.$refs[`groupTable_${className}`]
        if (!tableRef || tableRef.length === 0) return
        
        // 清除当前分组的选择
        tableRef[0].clearSelection()
        
        // 根据当前角色的permissions预选API
        if (this.selectedRole && this.selectedRole.permissions) {
          const permissions = Array.isArray(this.selectedRole.permissions) ? 
            this.selectedRole.permissions : 
            [];
            
          this.groupedApis[className].forEach(api => {
            if (permissions.includes(api.permission)) {
              tableRef[0].toggleRowSelection(api, true)
            }
          })
        }
      })
    },

    // 处理接口选择变更（普通视图）
    handleApiSelectionChange(selectedRows) {
      this.selectedApiList = selectedRows.map(row => row.permission)
      this.$emit('selection-change', this.selectedApiList)
    },
    
    // 处理分组内接口选择变更
    handleGroupSelectionChange(selectedRows, className) {
      this.groupSelections[className] = selectedRows.map(row => row.permission)
      
      // 合并所有分组的选择，更新总选择
      this.selectedApiList = Object.values(this.groupSelections).flat()
      this.$emit('selection-change', this.selectedApiList)
    },

    // 全选接口（普通视图）
    handleSelectAllApi() {
      if (this.$refs.apiTable) {
        this.filteredApiList.forEach(row => {
          this.$refs.apiTable.toggleRowSelection(row, true)
        })
      }
    },

    // 取消全选接口（普通视图）
    handleUnselectAllApi() {
      if (this.$refs.apiTable) {
        this.$refs.apiTable.clearSelection()
      }
    },
    
    // 全选分组内的接口
    selectAllInGroup(className) {
      const tableRef = this.$refs[`groupTable_${className}`]
      if (tableRef && tableRef.length > 0) {
        this.groupedApis[className].forEach(row => {
          if (this.selectedRole && this.selectedRole.code !== 'admin') {
            tableRef[0].toggleRowSelection(row, true)
          }
        })
      }
    },
    
    // 取消全选分组内的接口
    unselectAllInGroup(className) {
      const tableRef = this.$refs[`groupTable_${className}`]
      if (tableRef && tableRef.length > 0) {
        tableRef[0].clearSelection()
      }
    },

    // 获取控制器显示名称（取最后一段）
    getControllerDisplayName(className) {
      if (!className) return '-'
      const parts = className.split('.')
      return parts[parts.length - 1] 
    },
    
    // 获取控制器描述信息
    getControllerDescription(className) {
      if (!className || !this.apiList || this.apiList.length === 0) return '';
      
      // 找到此控制器的一个API项，获取其classDescription
      const apiItem = this.apiList.find(api => api.className === className);
      return apiItem && apiItem.classDescription ? apiItem.classDescription : '';
    },

    // 获取方法标签样式
    getMethodTag(method) {
      const methods = {
        GET: 'success',
        POST: 'primary',
        PUT: 'warning',
        DELETE: 'danger'
      }
      return methods[method] || 'info'
    },

    // 获取当前选中的权限列表
    getSelectedPermissions() {
      return this.selectedApiList
    }
  }
}
</script>

<style lang="scss" scoped>
.api-permission-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
  padding-top: 12px;

  .header-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .left-section {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .module-selection {
      display: flex;
      align-items: center;
      
      .module-title {
        font-weight: 600;
        margin-right: 12px;
        white-space: nowrap;
      }
      
      .module-selector {
        width: 200px;
        
        ::v-deep .el-input__inner {
          border-radius: 8px;
          height: 36px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }
      }
    }
    
    .tip-box {
      ::v-deep .el-tag {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 0 12px;
        height: 32px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.3s;
        border: 1px solid #f3d19e;
        background-color: #fdf6ec;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(230, 162, 60, 0.2);
        }
        
        i {
          font-size: 16px;
          color: #E6A23C;
        }
      }
      
      ::v-deep .el-tooltip__popper {
        max-width: 300px;
        line-height: 1.5;
        
        p {
          margin: 6px 0;
          
          &:first-child {
            margin-top: 0;
          }
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .view-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .view-mode {
        display: flex;
        align-items: center;
        
        ::v-deep .el-radio-group {
          background: #f5f7fa;
          padding: 2px;
          border-radius: 8px;
          border: 1px solid #e0e5ee;
          
          .el-radio-button {
            margin: 0;
            
            &:first-child .el-radio-button__inner {
              border-radius: 6px 0 0 6px;
            }
            
            &:last-child .el-radio-button__inner {
              border-radius: 0 6px 6px 0;
            }
            
            .el-radio-button__inner {
              border: none;
              background: transparent;
              padding: 8px 16px;
              height: 32px;
              line-height: 16px;
              font-size: 13px;
              font-weight: 500;
              transition: all 0.3s ease;
              
              &:hover {
                color: #409EFF;
              }
            }
            
            &.is-active .el-radio-button__inner {
              background: #fff;
              color: #409EFF;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            }
          }
        }
      }
      
      .group-selection {
        display: flex;
        align-items: center;
        
        .group-title {
          font-weight: 600;
          margin-right: 12px;
          white-space: nowrap;
        }
        
        .group-selector {
          width: 150px;
          
          ::v-deep .el-input__inner {
            border-radius: 8px;
            height: 36px;
            background: #fff;
            border: 1px solid #e0e5ee;
            
            &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
        }
      }
    }
    
    .refresh-btn {
      padding: 8px 16px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);
      }
      
      i {
        margin-right: 4px;
      }
    }
  }

  /* 搜索框样式 */
  .search-box {
    margin-bottom: 16px;
    
    .search-input-container {
      display: flex;
      align-items: center;
      gap: 0;
      
      .input-wrapper {
        flex: 1;
        
        .search-input {
          width: 100%;
          
          ::v-deep .el-input__inner {
            border-radius: 8px 0 0 8px;
            height: 40px;
            background: #fff;
            border: 1px solid #e0e5ee;
            border-right: none;
            
            &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
        }
      }
      
      .selector-wrapper {
        .search-mode-selector {
          width: 120px;
          
          ::v-deep .el-input__inner {
            border-radius: 0;
            height: 40px;
            background-color: #f5f7fa;
            border: 1px solid #e0e5ee;
            border-left: none;
            border-right: none;
          }
        }
      }
      
      .search-btn {
        height: 40px;
        border-radius: 0 8px 8px 0;
        margin-left: 0;
        padding: 0 20px;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
        
        i {
          margin-right: 4px;
        }
      }
    }
    
    .search-tips {
      display: flex;
      align-items: center;
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
      
      i {
        margin-right: 4px;
        color: #E6A23C;
      }
    }
  }

  // 添加缺省图样式
  .empty-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    background-color: #f9fafc;
    border-radius: 8px;
    margin-top: 12px;
    
    .empty-image {
      width: 160px;
      height: 160px;
      margin-bottom: 16px;
      opacity: 0.8;
    }
    
    .empty-text {
      font-size: 16px;
      color: #909399;
      margin: 0;
      text-align: center;
    }
  }

  .permission-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      
      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }
      
      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        
        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }
        
        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);
          
          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }
        
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }
      
      .permission-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #409EFF;
          font-size: 16px;
        }
      }
      
      .api-name {
        font-weight: 500;
        color: #1a1f36;
      }
      
      .controller-name {
        font-family: monospace;
        color: #333;
        background: #f5f7fa;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 13px;
      }
      
      // 添加el-tag样式
      .el-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22px;
        line-height: 1;
        padding: 0 8px;
      }
    }
    
    .table-actions {
      display: flex;
      justify-content: flex-end;
      padding: 16px 0 0 0;
      gap: 16px;
      border-top: 1px solid #ebeef5;
      margin-top: 16px;
      
      .el-button {
        color: #409EFF;
        font-weight: 500;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
  
  .controller-groups {
    flex: 1;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
      height: 0;
    }
    
    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(144, 147, 153, 0.3);
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    ::v-deep .el-collapse {
      border: none;
      
      .el-collapse-item {
        margin-bottom: 10px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        overflow: hidden;
        
        &__header {
          padding: 12px 16px;
          background: #f7f9fd;
          font-weight: 600;
          color: #1a1f36;
          font-size: 15px;
          position: relative;
          
          &:hover {
            background: #edf2fc;
          }
          
          .group-title-container {
            display: flex;
            align-items: center;
            
            .controller-description {
              margin-left: 8px;
              color: #606266;
              font-size: 13px;
              font-style: italic;
            }
            
            .api-count-tag {
              margin-left: 10px;
              font-size: 12px;
              padding: 0 8px;
              height: 22px;
              line-height: 20px;
              border-radius: 11px;
              font-weight: normal;
              background-color: #f0f2f5;
              border-color: #e6e8eb;
              color: #606266;
            }
          }
        }
        
        &__content {
          padding: 0;
          
          &-inner {
            padding: 16px;
          }
        }
      }
    }
    
    .group-count {
      margin-bottom: 8px;
      color: #909399;
      font-size: 14px;
    }
    
    // 添加分组表格样式，与角色管理保持一致
    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;
      
      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      
      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }
      
      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        
        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }
        
        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);
          
          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }
        
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }
      
      .permission-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #409EFF;
          font-size: 16px;
        }
      }
      
      .api-name {
        font-weight: 500;
        color: #1a1f36;
      }
      
      .controller-name {
        font-family: monospace;
        color: #333;
        background: #f5f7fa;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 13px;
      }
      
      // el-tag样式
      .el-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22px;
        line-height: 1;
        padding: 0 8px;
      }
    }
  }
}
</style>
