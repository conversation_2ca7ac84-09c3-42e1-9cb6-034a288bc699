import {getConfigBy<PERSON><PERSON>} from '@/api/sys/config'
const VERSION_KEY = 'system.version'
const NULL_VERSION = '1.0.0'

const regex = /^v?(\d+\.\d+\.\d+)(?:-([A-Z]+))?$/i;

var state = {
  version: '',
  publish: ''
}

const mutations = {
  SET_VERSION(state, version) {
    state.version = version
  },
  SET_PUBLISH(state, publish) {
    state.publish = publish
  }
}

const actions = {
  refresh({ commit }) {
    return getConfigByKey(VERSION_KEY).then(version => {
      const match = version ? version.match(regex) : false;
      if (match) {
        version = match[1] ? match[1] : NULL_VERSION
        commit('SET_VERSION', version)
        if (match[2]) {
          commit('SET_PUBLISH', match[2])
        }
        return version
      } else {
        commit('SET_VERSION', NULL_VERSION)
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}