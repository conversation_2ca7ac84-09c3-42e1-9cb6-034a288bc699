<template>
  <div class="main">
    <el-popover placement="top" width="390" v-model="visible">
        <el-button
          size="small"
          type="success"
          :loading="upload.isUploading"
          @click="submitUpload"
          >{{startButtonName}}</el-button
        >
        <el-upload
          :multiple="false"
          drag
          ref="upload"
          :limit="1"
          :accept="accept"
          :action="upload.url"
          :headers="upload.headers"
          :file-list="upload.fileList"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :on-change="handleChange"
          :auto-upload="false"
          :on-error="handleError"
        >
          <el-button
            style="margin-top: 70px"
            slot="trigger"
            size="small"
            type="primary"
            >选取文件</el-button
          >
          <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
        </el-upload>
    </el-popover>
  </div>
</template>
<script>
import { getToken } from "@/utils/auth";
export default {
  data() {
    return {
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: "",
        // 上传的文件列表
        fileList: [],
      },
      visible: false,
    };
  },
  props: {
    accept: {
      type: String,
      default: ".jpg, .png",
    },
    url: {
      type: String,
      default: "",
    },
    startButtonName:{
      type: String,
      default: "开始导入",
    },
    needDownload:{
      type: Boolean,
      default: true,
    }
  },
  mounted() {
    this.upload.url = this.url;
  },
  watchs: {
    url(val) {
      this.upload.url = val;
    },
  },
  methods: {
    // 文件提交处理
    submitUpload() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      if(this.needDownload){
        if (response.data) {
          this.msgSuccess(response.data);
        } else {
          this.download(response.msg);
          this.msgError("部分数据导入失败");
        }
      }else{
        if(200 == response.code)
          this.msgSuccess("上传成功")
        else
          this.msgError("上传失败");
      }
      this.close();
      this.$refs.upload.clearFiles()
    },
    handleChange(file, fileList) {
    },
    open() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    switch () {
      this.visible = !this.visible
    },
    handleError(){

    }
  },
};
</script>
<style scoped>
.main {
  text-align: center;
}
</style>
