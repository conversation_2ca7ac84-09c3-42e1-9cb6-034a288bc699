
import formCreate, { maker } from "@form-create/element-ui"
import JsonEditer from './JsonEditer'
import Uploader from './fileUpload/FileView.vue'
import ImgUploader from './fileUpload/ImgUploader.vue'
import GUploader from './fileUpload/FileViewName.vue'
import GList from './GList.vue'
import TreeSelect from './TreeSelect'
import Casader from './Casader'
import SvgIcon from './SvgIcon/index.vue'
import Map from './Map.vue'


import UserSelect from "./UserSelect/index.vue"
import DeptSelect from "./UserSelect/deptSelect.vue"

import FcEditor from '@form-create/component-wangeditor'
import FcDesigner from './Designer/index.es.js'
import DesignerEditor from "./Designer/Designer.vue"
import userDeptCascader from './UserSelect/userDeptCascader.vue'
import Crontab from './Crontab/input.vue'


import Editor from './Editor'

const install = function (Vue) {
    Vue.use(formCreate)

    Vue.use(FcDesigner)
    Vue.use(FcDesigner.formCreate)
    Vue.component('designerEditor', DesignerEditor)
    Vue.component('fcEditor2', Editor)

    Vue.component('editor', Editor)
    Vue.component('editor2', FcEditor)

    Vue.component('json', JsonEditer)
    Vue.component('uploader', Uploader)
    Vue.component('guploader', GUploader)
    Vue.component('glist', GList)
    Vue.component('treeSelect', TreeSelect)
    Vue.component('cascader2', Casader)
    Vue.component('svgIcon', SvgIcon)
    Vue.component('cronInput', Crontab) //cron表达式

    Vue.component('gmap', Map)
    Vue.component('userSelect', UserSelect)
    Vue.component('deptSelect', DeptSelect)
    Vue.component('userDeptCascader', userDeptCascader)

}



export { maker }
export default install
