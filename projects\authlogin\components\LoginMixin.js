export default {

    methods: {
        checkAgreement() {
            if (this.$attrs.configKeys['builtin.user.agreement'] && !this.$attrs.isAgree) {
                this.$message.info('请输入勾选同意用户协议和隐私政策')
                return false
            }
            return true
        },
        loginComplete(res) {
            this.$emit('loginComplete', res)
        },
        loginError(err) {
            this.$emit('loginError', err)
        },
    }
}