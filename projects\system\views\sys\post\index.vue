<template>
  <div>
    <auto-page :options="options" :form-rule="options.formRule" :defaultQuery="{parentId:0,orders:{'sort':'asc'}}" ref="rules"
               @addRow="addRow">
    </auto-page>
  </div>
</template>

<script>
  import options from './options'
  import AutoPage from "@/components/auto-page/AutoPage";
  import {treeList} from "@system/api/sys/post";
  export default {
    components:{AutoPage},
    data(){
      return{
        options:null
      }
    },
    created(){
      this.options = JSON.parse(JSON.stringify(options))
      this.assemblyTree();
    },
    methods:{
      addRow(){
        this.$refs.rules.formRuleDrawerNes[4].value = true
      },
      async getTreeList() {
        return new Promise(((resolve, reject) => {
          treeList().then(res => {
            resolve(res)
          })
        }))
      },
      async assemblyTree(){
        let array = await this.getTreeList();
        this.recursion(array)
        this.options.formRule = this.options.formRule.map(is => {
          if (is.field === "parentId") {
            is.options = array
          }
          return is
        })
      },
      recursion(array){
        for (let i = 0; i < array.length; i++) {
          array[i].label = array[i].name;
          delete array[i].name;
          if (array[i].children.length > 0){
            this.recursion(array[i].children)
          }
        }
      }
    }
  }
</script>
