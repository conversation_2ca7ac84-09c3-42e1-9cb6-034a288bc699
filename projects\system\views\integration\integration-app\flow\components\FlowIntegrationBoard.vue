<template>
  <div class="integration-board">
    <!-- 顶部操作栏 -->
    <div class="board-header">
      <div class="header-info">
        <div class="title-section">
          <h4 class="section-title">集成板</h4>
          <div class="status-info">
            <el-tag
              :type="getStatusType()"
              size="small"
              effect="plain"
              class="status-tag"
            >
              {{ getStatusText() }}
            </el-tag>
          </div>
        </div>
        <div class="usage-tip">
          <i class="el-icon-info"></i>
          <span>管理和编排集成流程，支持可视化流程设计</span>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          icon="el-icon-refresh"
          size="small"
          :loading="loading"
          @click="loadFlowList"
          type="primary"
        >
          刷新
        </el-button>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleCreate"
          class="create-btn"
        >
          创建集成板
        </el-button>
      </div>
    </div>

    <!-- 集成板列表 -->
    <div class="board-content" v-loading="loading">
      <div v-if="flowList.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无集成板">
          <el-button type="primary" @click="handleCreate">创建集成板</el-button>
        </el-empty>
      </div>

      <div v-else class="flow-grid">
        <!-- 集成板卡片 -->
        <div
          v-for="flow in flowList"
          :key="flow.id"
          :class="['flow-card', {
            'published': flow.published
          }]"
          @click="handleCanvasEdit(flow)"
        >
          <div class="card-header">
            <div class="flow-info">
              <div class="flow-icon">
                <i class="el-icon-connection"></i>
              </div>
              <div class="flow-meta">
                <h5 class="flow-name">{{ flow.name || `集成板 ${flow.id}` }}</h5>
                <span class="flow-code">{{ flow.code }}</span>
              </div>
            </div>
            <div class="flow-status">
              <el-tag
                size="mini"
                :type="flow.published ? 'success' : 'warning'"
                effect="plain"
              >
                {{ flow.published ? '已发布' : '草稿' }}
              </el-tag>
            </div>
          </div>

          <div class="card-body">
            <div class="flow-description" v-if="flow.description">
              {{ flow.description }}
            </div>

            <div class="flow-config">
              <div class="config-item">
                <span class="config-label">更新时间:</span>
                <span class="config-value">{{ formatDate(flow.updateTime) }}</span>
              </div>
              <div class="config-item">
                <span class="config-label">发布状态:</span>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <div class="footer-left">
              <span class="update-time">{{ formatDate(flow.updateTime) }}</span>
            </div>
            <div class="footer-right">
              <div class="publish-switch" @click.stop>
                <el-switch
                  v-model="flow.published"
                  active-text=""
                  inactive-text=""
                  :loading="flow.publishLoading"
                  @change="handlePublishChange(flow, $event)"
                  size="small"
                />
                <span class="publish-label">{{ flow.published ? '已发布' : '草稿' }}</span>
              </div>
              <el-button
                type="text"
                icon="el-icon-edit"
                size="mini"
                @click.stop="handleEdit(flow)"
                title="编辑集成板"
              />
              <!-- <el-button
                type="text"
                icon="el-icon-connection"
                size="mini"
                @click.stop="handleCanvasEdit(flow)"
                title="画布编辑"
              /> -->
              <el-dropdown
                @command="(command) => handleFlowAction(command, flow)"
                trigger="click"
                @click.stop
              >
                <el-button
                  type="text"
                  icon="el-icon-more"
                  size="mini"
                  title="更多操作"
                  @click.stop
                />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="copy" icon="el-icon-document-copy">
                    复制集成板
                  </el-dropdown-item>
                  <el-dropdown-item command="export" icon="el-icon-download">
                    导出集成板
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" icon="el-icon-delete">
                    删除集成板
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 集成板编辑对话框 -->
    <el-dialog
      :visible.sync="showEditDialog"
      :title="isEditMode ? '编辑集成板' : '创建集成板'"
      width="95vw"
      top="2.5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :append-to-body="true"
      custom-class="edit-flow-dialog"
      @close="resetEditForm"
    >
      <div class="edit-flow-container">
        <!-- 左侧：表单填写 -->
        <div class="form-section">
          <div class="section-header">
            <h2>{{ isEditMode ? '编辑集成板' : '创建新的集成板' }}</h2>
            <p>{{ isEditMode ? '修改集成板基本信息' : '填写集成板基本信息' }}</p>
          </div>

          <el-form :model="editForm" :rules="editRules" ref="editForm" class="flow-form">
            <el-form-item label="名称" prop="name" required>
              <el-input
                v-model="editForm.name"
                placeholder="请输入集成板名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="编码" prop="code" required>
              <el-input
                v-model="editForm.code"
                placeholder="请输入集成板编码（用于唯一标识）"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="描述" prop="description">
              <el-input
                v-model="editForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入集成板描述，说明该集成板的用途和功能"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="editForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息，可以包含版本信息、注意事项等（可选）"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>


            <el-form-item label="更新时间" v-if="isEditMode && editForm.updateTime">
              <span class="form-readonly">{{ editForm.updateTime }}</span>
            </el-form-item>
          </el-form>
        </div>

        <!-- 右侧：XML编辑器 -->
        <div class="xml-editor-section">
          <div class="editor-header">
            <h3>XML配置</h3>
            <p>Spring Integration XML配置内容</p>
          </div>

          <div class="editor-content">
            <div id="xml-editor-container" class="monaco-editor-container"></div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button
          size="large"
          class="cancel-btn"
          @click="showEditDialog = false"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="large"
          class="save-btn"
          :disabled="!canSave"
          @click="handleSave"
          :loading="saving"
        >
          <i class="el-icon-check" v-if="!saving"></i>
          {{ saving ? (isEditMode ? '保存中...' : '创建中...') : (isEditMode ? '保存' : '创建集成板') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as monaco from 'monaco-editor'
import { getFlowCanvasList, createFlowCanvas, updateFlowCanvas, deleteFlowCanvas, registerFlowCanvas, unregisterFlowCanvas } from '@system/api/integration/flow-app'

export default {
  name: 'FlowIntegrationBoard',
  props: {
    appId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      flowList: [],
      showEditDialog: false,
      saving: false,
      isEditMode: false,
      xmlEditor: null, // Monaco 编辑器实例
      editForm: {
        id: '',
        integrationAppId: '',
        name: '',
        code: '',
        description: '',
        remark: '',
        xmlContent: '',
        createTime: '',
        updateTime: ''
      },
      editRules: {
        name: [
          { required: true, message: '请输入集成板名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入集成板编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_-]+$/, message: '编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    getStatusType() {
      return () => {
        if (this.flowList.length === 0) return 'warning'
        return 'success'
      }
    },
    getStatusText() {
      return () => {
        if (this.flowList.length === 0) return '暂无集成板'
        return `共 ${this.flowList.length} 个集成板`
      }
    },
    canSave() {
      return this.editForm.name && this.editForm.code
    }
  },
  created() {
    this.loadFlowList()
  },
  beforeDestroy() {
    // 销毁 Monaco 编辑器
    if (this.xmlEditor) {
      this.xmlEditor.dispose()
    }
  },
  methods: {
    // 初始化 Monaco 编辑器
    initXmlEditor() {
      this.$nextTick(() => {
        const container = document.getElementById('xml-editor-container')
        if (!container) return

        // 销毁已存在的编辑器
        if (this.xmlEditor) {
          this.xmlEditor.dispose()
        }

        // 创建新的编辑器实例
        this.xmlEditor = monaco.editor.create(container, {
          value: this.editForm.xmlContent || '',
          language: 'xml',
          theme: 'vs', // 使用白色主题
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 13,
          tabSize: 2,
          wordWrap: 'on',
          folding: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3
        })

        // 监听内容变化
        this.xmlEditor.onDidChangeModelContent(() => {
          this.editForm.xmlContent = this.xmlEditor.getValue()
        })

        // 添加快捷键
        this.xmlEditor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
          this.handleSave()
          return false
        })
      })
    },

    // 加载集成板列表
    async loadFlowList() {
      this.loading = true
      try {
        const response = await getFlowCanvasList({
          current: 1,
          size: -1, // 获取全部
          integrationAppId: this.appId // 只查询当前应用的集成板
        })
        this.flowList = (response.records || []).map(item => ({
          ...item,
          publishLoading: false
        }))
      } catch (error) {
        console.error('加载集成板列表失败:', error)
        this.$message.error('加载集成板列表失败')
      } finally {
        this.loading = false
      }
    },

    // 创建集成板
    handleCreate() {
      this.isEditMode = false
      this.editForm = {
        id: '',
        integrationAppId: this.appId,
        name: '',
        code: '',
        description: '',
        remark: '',
        xmlContent: '',
        createTime: '',
        updateTime: ''
      }
      this.showEditDialog = true
      // 初始化编辑器
      this.$nextTick(() => {
        this.initXmlEditor()
      })
    },

    // 编辑集成板
    handleEdit(flow) {
      this.isEditMode = true
      this.editForm = {
        id: flow.id,
        integrationAppId: flow.integrationAppId,
        name: flow.name || '',
        code: flow.code || '',
        description: flow.description || '',
        remark: flow.remark || '',
        xmlContent: flow.xmlContent || '',
        createTime: flow.createTime || '',
        updateTime: flow.updateTime || ''
      }
      this.showEditDialog = true
      // 初始化编辑器
      this.$nextTick(() => {
        this.initXmlEditor()
      })
    },

    // 画布编辑
    handleCanvasEdit(flow) {
      this.$emit('edit-flow', { ...flow, editMode: 'canvas' })
    },

    // 保存
    async handleSave() {
      try {
        await this.$refs.editForm.validate()
        this.saving = true

        const saveData = {
          integrationAppId: this.editForm.integrationAppId,
          name: this.editForm.name,
          code: this.editForm.code,
          description: this.editForm.description,
          remark: this.editForm.remark,
          xmlContent: this.editForm.xmlContent || '',
          published: false
        }

        if (this.isEditMode) {
          saveData.id = this.editForm.id
          await updateFlowCanvas(saveData)
          this.$message.success('集成板保存成功')
        } else {
          saveData.canvas = JSON.stringify({ nodes: [], edges: [] })
          await createFlowCanvas(saveData)
          this.$message.success('集成板创建成功')
        }

        this.showEditDialog = false
        this.loadFlowList()

      } catch (error) {
    
      } finally {
        this.saving = false
      }
    },

    // 重置表单
    resetEditForm() {
      // 销毁编辑器
      if (this.xmlEditor) {
        this.xmlEditor.dispose()
        this.xmlEditor = null
      }

      this.editForm = {
        id: '',
        integrationAppId: '',
        name: '',
        code: '',
        description: '',
        remark: '',
        xmlContent: '',
        createTime: '',
        updateTime: ''
      }
      this.$refs.editForm?.resetFields()
    },

    // 发布状态切换 - 先保存再发布
    async handlePublishChange(flow, newValue) {
      const originalValue = !newValue
      flow.publishLoading = true

      try {
        if (newValue) {
          // 发布流程：1. 先保存当前流程配置（如果有画布数据）
          // if (flow.canvas) {
          //   await updateFlowCanvas({
          //     ...flow,
          //     published: false // 先保存为草稿状态
          //   })
          // }

          // 2. 然后调用注册接口发布
          await registerFlowCanvas(flow.id)
          this.$message.success(`集成板"${flow.name}"发布成功`)
        } else {
          // 取消发布：调用卸载接口
          await unregisterFlowCanvas(flow.id)
          this.$message.success(`集成板"${flow.name}"已取消发布`)
        }

        this.loadFlowList()

      } catch (error) {
        console.error('发布状态切换失败:', error)
        // 恢复原始状态
        flow.published = originalValue
        this.$message.error('发布状态切换失败')
      } finally {
        flow.publishLoading = false
      }
    },

    // 处理流程操作
    async handleFlowAction(command, flow) {
      switch (command) {
        case 'copy':
          await this.copyFlow(flow)
          break
        case 'export':
          await this.exportFlow(flow)
          break
        case 'delete':
          await this.deleteFlow(flow)
          break
      }
    },

    // 复制集成板
    async copyFlow(flow) {
      try {
        const copyData = {
          integrationAppId: flow.integrationAppId,
          name: `${flow.name} - 副本`,
          code: `${flow.code}_copy_${Date.now()}`,
          description: flow.description,
          remark: `${flow.remark || ''} - 副本`,
          canvas: flow.canvas,
          xmlContent: flow.xmlContent,
          published: false
        }
        await createFlowCanvas(copyData)
        this.$message.success('集成板复制成功')
        this.loadFlowList()
      } catch (error) {
        console.error('复制集成板失败:', error)
        this.$message.error('复制集成板失败')
      }
    },

    // 导出集成板
    async exportFlow(flow) {
      try {
        const flowName = flow.name || `集成板_${flow.id}`
        const dataStr = JSON.stringify(flow, null, 2)
        const dataBlob = new Blob([dataStr], { type: 'application/json' })
        const url = URL.createObjectURL(dataBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${flowName}_${flow.code}.json`
        link.click()
        URL.revokeObjectURL(url)
        this.$message.success(`集成板"${flowName}"导出成功`)
      } catch (error) {
        console.error('导出集成板失败:', error)
        this.$message.error('导出集成板失败')
      }
    },

    // 删除集成板
    async deleteFlow(flow) {
      try {
        const flowName = flow.name || `集成板_${flow.id}`
        await this.$confirm(
          `确定要删除集成板"${flowName}"吗？此操作不可撤销。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )

        await deleteFlowCanvas(flow.id)
        this.$message.success(`集成板"${flowName}"删除成功`)
        this.loadFlowList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除集成板失败:', error)
          this.$message.error('删除集成板失败')
        }
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知'
      if (dateString.includes(' ')) {
        return dateString.split(' ')[0]
      }
      return dateString
    }
  }
}
</script>

<style lang="scss" scoped>
// 对话框样式
::v-deep .el-dialog__wrapper {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(4px);
}

::v-deep .edit-flow-dialog {
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;

  .el-dialog {
    border-radius: 16px !important;
    margin: 0 !important;
  }

  .el-dialog__header {
    padding: 24px 24px 0;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}

.integration-board {
  // 顶部操作栏
  .board-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 0 16px 0;
    margin-bottom: 20px;

    .header-info {
      flex: 1;

      .title-section {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .section-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          line-height: 1.2;
        }

        .status-info {
          .status-tag {
            font-size: 12px;
            padding: 4px 12px;
            border-radius: 16px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 24px;
            line-height: 1;
          }
        }
      }

      .usage-tip {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        color: #64748b;
        line-height: 1.4;

        i {
          font-size: 14px;
          color: #94a3b8;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .create-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border: none;
        color: white;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
          background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        &:disabled {
          background: #e0e6ed;
          color: #8c8c8c;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  // 集成板列表
  .board-content {
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }

    .flow-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 16px;

      .flow-card {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
          transform: translateY(-2px);
        }



        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .flow-info {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            flex: 1;

            .flow-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 16px;
              flex-shrink: 0;
              transition: all 0.3s ease;
            }

            .flow-meta {
              flex: 1;
              min-width: 0;

              .flow-name {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
                line-height: 1.3;
              }

              .flow-code {
                font-size: 12px;
                color: #64748b;
                font-family: 'Monaco', 'Menlo', monospace;
              }
            }
          }

          .flow-status {
            flex-shrink: 0;
          }
        }

        .card-body {
          .flow-description {
            font-size: 12px;
            color: #64748b;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .flow-config {
            .config-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 11px;
              margin-bottom: 4px;

              .config-label {
                color: #64748b;
                font-weight: 500;
              }

              .config-value {
                color: #1e293b;
                font-family: 'Monaco', 'Menlo', monospace;
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;

          .footer-left {
            .update-time {
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .footer-right {
            display: flex;
            align-items: center;
            gap: 8px;

            .publish-switch {
              display: flex;
              align-items: center;
              gap: 6px;
              margin-right: 12px;

              ::v-deep .el-switch {
                &.el-switch--small {
                  .el-switch__core {
                    width: 32px;
                    height: 18px;
                    border-radius: 9px;

                    &:after {
                      width: 14px;
                      height: 14px;
                      border-radius: 7px;
                    }
                  }

                  &.is-checked .el-switch__core:after {
                    margin-left: -15px;
                  }
                }

                .el-switch__core {
                  background: #dcdfe6;
                  border: none;

                  &:after {
                    background: white;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
                  }
                }

                &.is-checked .el-switch__core {
                  background: #10b981;
                }
              }

              .publish-label {
                font-size: 11px;
                color: #64748b;
                font-weight: 500;
                min-width: 32px;
              }
            }
          }
        }
      }
    }
  }
}

// 编辑对话框样式
.edit-flow-container {
  display: flex;
  height: 85vh;
  min-height: 600px;
}

.form-section {
  flex: 1;
  padding: 32px;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
  background: #fafbfc;

  .section-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #8c8c8c;
      margin: 0;
    }
  }

  .flow-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .el-form-item {
      margin-bottom: 20px;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #262626;
      font-size: 14px;
    }

    .el-input, .el-textarea {
      .el-input__inner, .el-textarea__inner {
        border-radius: 8px;
        border: 1px solid #e8e8e8;
        transition: all 0.2s ease;
        font-size: 14px;

        &:focus {
          border-color: #10b981;
          box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
        }
      }
    }

    .form-readonly {
      color: #8c8c8c;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.xml-editor-section {
  flex: 1;
  padding: 24px;
  background: #fafafa;

  .editor-header {
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 4px 0;
    }

    p {
      font-size: 13px;
      color: #8c8c8c;
      margin: 0;
      line-height: 1.4;
    }
  }

  .editor-content {
    height: calc(100% - 60px);

    .monaco-editor-container {
      height: 100%;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      overflow: hidden;

      ::v-deep .monaco-editor {
        .margin {
          background-color: #f8f9fa;
        }

        .monaco-editor-background {
          background-color: #ffffff;
        }

        .current-line {
          background-color: rgba(16, 185, 129, 0.05);
        }

        .line-numbers {
          color: #94a3b8;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    min-width: 100px;
    height: 40px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.cancel-btn {
      background: #fff;
      border: 1px solid #d9d9d9;
      color: #595959;

      &:hover {
        border-color: #10b981;
        color: #10b981;
      }
    }

    &.save-btn {
      background: linear-gradient(135deg, #10b981, #059669);
      border: none;
      color: white;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #059669, #047857);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
        transform: translateY(-1px);
      }

      &:disabled {
        background: #f5f5f5;
        color: #bfbfbf;
        box-shadow: none;
        cursor: not-allowed;
      }

      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
