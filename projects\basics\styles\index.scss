@import "./variables.module.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./font.scss";

//通过更换 样式文件达到换肤效果
@import "./utils.scss"; //默认主题
// @import "./darklyTheme.scss"; //暗黑主题

// 页面上 默认边距写入  关于颜色则切换样式文件

//阿里字体图标
@import './ali-iconfont/index.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

// .el-checkbox:last-of-type {
//   margin-right: -5px !important;
// }

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.el-button {
  padding: 10px 15px;
}

.el-button--text {
  padding: 0px;
}

.bg-success {
  background-color: #67C23A;
}

.bg-Warning {
  background-color: #E6A23C;
}

.bg-Danger {
  background-color: #F56C6C;
}

.bg-Info {
  background-color: #909399;
}


/*-组件-*/
.app-container {
  padding: 15px;
}

.el-dialog__body {
  padding: 20px 25px;
}

/* 设计图dialog样式 */
.el-dialog__header {
  padding: 20px 30px !important;
  text-align: left;
  height: 60px;
  line-height: 20px;
}

/* dialog 中 树结构 */
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

// 添加单张图片样式
.avatar-uploader .el-upload {
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}


.avatar-uploader-icon {
  font-size: 28px;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

/* 表格样式 */
.el-table {
  line-height: 45px;
}

.el-table th {
  background-color: #f8f8f9 !important;

  .cell {
    color: #515a6e;
    font-weight: bolder;
  }
}

// 布局样式
.el-header {
  padding-top: 12px !important;
}

.el-footer {
  // background-color: #ffffff;
  // color: #333;
  position: absolute;
  bottom: 0;
}

.pagination-container {
  position: relative;
  height: 25px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
}

.pagination-container .el-pagination {
  right: 0;
  position: absolute;
}

.el-aside {
  // background-color: #ffffff;
  // color: #333;
}

.el-main {
  // background-color: #ffffff;
  // color: #333;
}

//表格显示 全部字段 tip 控制宽度
.el-tooltip__popper {
  max-width: 40%;
}

//表单label
.el-form-item__label {
  font-weight: bolder;
}

.el-form-item {
  margin-bottom: 18px !important;
}

.el-form-item__label {
  padding: 0 12px 0 0 !important;
}


/*  -- 浅色边框 -- */
.solid::after {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.solid-top::after {
  border-top: 1px solid solid rgba(0, 0, 0, 0.3);
}

.solid-right::after {
  border-right: 1px solid rgba(0, 0, 0, 0.2);
}

.solid-bottom::after {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}

.solid-left::after {
  border-left: 1px solid rgba(0, 0, 0, 0.2);
}

/*  -- flex弹性布局 -- */
.flex {
  display: flex;
}

.basis-xs {
  flex-basis: 20%;
}

.basis-sm {
  flex-basis: 40%;
}

.basis-df {
  flex-basis: 50%;
}

.basis-lg {
  flex-basis: 60%;
}

.basis-xl {
  flex-basis: 80%;
}

.flex-sub {
  flex: 1;
}

.flex-twice {
  flex: 2;
}

.flex-treble {
  flex: 3;
}

.flex-direction {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.align-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

/*  -- 内外边距 -- */

.margin-0 {
  margin: 0;
}

.margin-xs {
  margin: 10px;
}

.margin-sm {
  margin: 20px;
}

// .margin {
//   margin: 30px;
// }

.margin-lg {
  margin: 40px;
}

.margin-xl {
  margin: 50px;
}

.margin-top-xs {
  margin-top: 10px;
}

.margin-top-sm {
  margin-top: 20px;
}

.margin-top {
  margin-top: 30px;
}

.margin-top-lg {
  margin-top: 40px;
}

.margin-top-xl {
  margin-top: 50px;
}

.margin-right-xs {
  margin-right: 10px;
}

.margin-right-sm {
  margin-right: 20px;
}

.margin-right {
  margin-right: 30px;
}

.margin-right-lg {
  margin-right: 40px;
}

.margin-right-xl {
  margin-right: 50px;
}

.margin-bottom-xs {
  margin-bottom: 10px;
}

.margin-bottom-sm {
  margin-bottom: 20px;
}

.margin-bottom {
  margin-bottom: 30px;
}

.margin-bottom-lg {
  margin-bottom: 40px;
}

.margin-bottom-xl {
  margin-bottom: 50px;
}

.margin-left-xs {
  margin-left: 10px;
}

.margin-left-sm {
  margin-left: 20px;
}

.margin-left {
  margin-left: 30px;
}

.margin-left-lg {
  margin-left: 40px;
}

.margin-left-xl {
  margin-left: 50px;
}

.margin-lr-xs {
  margin-left: 10px;
  margin-right: 10px;
}

.margin-lr-sm {
  margin-left: 20px;
  margin-right: 20px;
}

.margin-lr {
  margin-left: 30px;
  margin-right: 30px;
}

.margin-lr-lg {
  margin-left: 40px;
  margin-right: 40px;
}

.margin-lr-xl {
  margin-left: 50px;
  margin-right: 50px;
}

.margin-tb-xs {
  margin-top: 10px;
  margin-bottom: 10px;
}

.margin-tb-sm {
  margin-top: 20px;
  margin-bottom: 20px;
}

.margin-tb {
  margin-top: 30px;
  margin-bottom: 30px;
}

.margin-tb-lg {
  margin-top: 40px;
  margin-bottom: 40px;
}

.margin-tb-xl {
  margin-top: 50px;
  margin-bottom: 50px;
}

.padding-0 {
  padding: 0;
}

.padding-xs {
  padding: 10px;
}

.padding-sm {
  padding: 20px;
}

.padding {
  padding: 30px;
}

.padding-lg {
  padding: 40px;
}

.padding-xl {
  padding: 50px;
}

.padding-top-xs {
  padding-top: 10px;
}

.padding-top-sm {
  padding-top: 20px;
}

.padding-top {
  padding-top: 30px;
}

.padding-top-lg {
  padding-top: 40px;
}

.padding-top-xl {
  padding-top: 50px;
}

.padding-right-xs {
  padding-right: 10px;
}

.padding-right-sm {
  padding-right: 20px;
}

.padding-right {
  padding-right: 30px;
}

.padding-right-lg {
  padding-right: 40px;
}

.padding-right-xl {
  padding-right: 50px;
}

.padding-bottom-xs {
  padding-bottom: 10px;
}

.padding-bottom-sm {
  padding-bottom: 20px;
}

.padding-bottom {
  padding-bottom: 30px;
}

.padding-bottom-lg {
  padding-bottom: 40px;
}

.padding-bottom-xl {
  padding-bottom: 50px;
}

.padding-left-xs {
  padding-left: 10px;
}

.padding-left-sm {
  padding-left: 20px;
}

.padding-left {
  padding-left: 30px;
}

.padding-left-lg {
  padding-left: 40px;
}

.padding-left-xl {
  padding-left: 50px;
}

.padding-lr-xs {
  padding-left: 10px;
  padding-right: 10px;
}

.padding-lr-sm {
  padding-left: 20px;
  padding-right: 20px;
}

.padding-lr {
  padding-left: 30px;
  padding-right: 30px;
}

.padding-lr-lg {
  padding-left: 40px;
  padding-right: 40px;
}

.padding-lr-xl {
  padding-left: 50px;
  padding-right: 50px;
}

.padding-tb-xs {
  padding-top: 10px;
  padding-bottom: 10px;
}

.padding-tb-sm {
  padding-top: 20px;
  padding-bottom: 20px;
}

.padding-tb {
  padding-top: 30px;
  padding-bottom: 30px;
}

.padding-tb-lg {
  padding-top: 40px;
  padding-bottom: 40px;
}

.padding-tb-xl {
  padding-top: 50px;
  padding-bottom: 50px;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.el-drawer__wrapper {
  .el-drawer__body {
    position: relative;
  }
}

.pointer {
  cursor: pointer;
}

.mb8 {
  margin-bottom: 8px;
}