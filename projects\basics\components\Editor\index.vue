<template>
    <component :is="dynamicComponent" v-bind="$attrs" v-on="$listeners"></component>
</template>
<script>

export default {

    data() {
        return {
            dynamicComponent: null,
            ie: 'ActiveXObject' in window && true
        }
    },
    created() {
        this.loadComponent()
    },
    methods: {
        async loadComponent() {
            if (this.ie) {
                this.dynamicComponent = () => import('./wangEditor4.vue')
            } else {
                this.dynamicComponent = () => import('./wangEditor5.vue')
            }
        }
    },
}
</script>