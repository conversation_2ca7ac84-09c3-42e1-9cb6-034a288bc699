<template>
  <div class="message-push-container">
    <div class="push-card">
      <!-- 卡片头部 -->
      <div class="card-header">
        <div class="header-title">
          <span>{{ pageTitle }}</span>
        </div>
      </div>

      <!-- 表单区域 -->
      <div class="form-wrapper">
        <el-form 
          ref="pushForm" 
          :model="pushForm" 
          :rules="pushRules" 
          label-width="100px"
          class="push-form"
        >
          <template v-if="showBasicInfo">
            <div class="form-section">
              <div class="section-title">基本信息</div>
              <el-form-item label="任务名称" prop="name" :required="isPresetMode">
                <el-input v-model="pushForm.name" placeholder="请输入任务名称"></el-input>
              </el-form-item>
              
              <el-form-item label="任务代码" prop="code" :required="isPresetMode">
                <el-input v-model="pushForm.code" placeholder="请输入任务代码"></el-input>
              </el-form-item>

              <el-form-item label="分组" prop="groupName">
                <el-select 
                  v-model="pushForm.groupName" 
                  filterable 
                  allow-create 
                  default-first-option
                  placeholder="请选择或输入分组名称" 
                  @focus="loadGroupNames"
                >
                  <el-option 
                    v-for="item in groupOptions" 
                    :key="item" 
                    :label="item" 
                    :value="item"
                  >
                    <i class="el-icon-folder"></i>
                    <span style="margin-left: 8px">{{ item }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="备注说明" prop="remark">
                <el-input 
                  type="textarea" 
                  v-model="pushForm.remark" 
                  :rows="3"
                  placeholder="请输入备注说明"
                ></el-input>
              </el-form-item>
            </div>
          </template>

          <div class="form-section">
            <div class="section-title">消息供应商</div>
            <el-form-item label="供应商" prop="providerId">
              <el-select 
                v-model="pushForm.providerId" 
                filterable 
                placeholder="请选择消息供应商"
                style="width: 100%"
                popper-class="message-push-provider-dropdown"
              >
                <el-option
                  v-for="item in providerList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <div class="provider-option">
                    <i :class="getPluginIcon(item.pluginBeanName)" :style="{color: getPluginColor(item.pluginBeanName)}"></i>
                    <span>{{ item.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <div class="form-section">
            <div class="section-title">接收人设置</div>
            <el-form-item label="接收人类型" prop="receiverType">
              <el-radio-group v-model="pushForm.receiverType" @change="handleReceiverTypeChange">
                <el-radio label="USER">用户</el-radio>
                <el-radio label="ALL">全员通知</el-radio>
                <el-radio label="DEPT_ONLY">部门</el-radio>
                <el-radio label="DEPT">部门及以下</el-radio>
                <el-radio label="ROLE">角色</el-radio>
                <el-radio label="DEPT_AND_ROLE">部门和角色</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 用户选择 -->
            <template v-if="pushForm.receiverType === 'USER'">
              <el-form-item 
                label="选择用户" 
                prop="userIds"
              >
                <el-select
                  v-model="pushForm.userIds"
                  multiple
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入用户名/姓名搜索"
                  :remote-method="remoteSearchUsers"
                  :loading="userSearchLoading"
                  style="width: 100%"
                  :collapse-tags="false"
                  popper-class="message-push-user-dropdown"
                >
                  <el-option
                    v-for="item in userOptions"
                    :key="item.id"
                    :label="`${item.nickName || item.name || ''}(${item.username})`"
                    :value="item.id"
                  >
                    <div class="user-option">
                      <el-avatar :size="28" :src="item.picUrl">
                        {{ (item.nickName || item.name || '').substring(0, 1) || 'U' }}
                      </el-avatar>
                      <div class="user-info">
                        <div class="info-row">
                          <span class="name">{{ item.nickName || item.name || item.username }}</span>
                          <span class="account">
                            <i class="el-icon-user"></i>{{ item.username }}
                          </span>
                        </div>
                        <div class="info-row" v-if="item.deptName">
                          <span class="dept-info">
                            <i class="el-icon-office-building"></i>{{ item.deptName }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </template>

            <!-- 部门选择(不包含子部门) -->
            <template v-if="pushForm.receiverType === 'DEPT_ONLY'">
              <el-form-item label="选择部门" prop="deptIds">
                <div class="dept-tree-container">
                  <!-- 部门搜索框 -->
                  <div class="tree-search">
                    <el-input
                      v-model="deptSearchKeyword"
                      placeholder="搜索部门名称"
                      prefix-icon="el-icon-search"
                      clearable
                      size="small"
                      @clear="handleDeptSearchClear"
                    />
                  </div>
                  
                  <!-- 部门树 -->
                  <div class="tree-wrapper thin-scrollbar">
                    <el-tree
                      ref="departmentOnlyTree"
                      :data="treeData"
                      :props="defaultProps"
                      :filter-node-method="filterNode"
                      node-key="id"
                      highlight-current
                      show-checkbox
                      :check-strictly="true"
                      :default-checked-keys="selectedDeptIds"
                      @check="handleDeptOnlyCheck"
                    >
                      <span slot-scope="{ node }" class="custom-tree-node">
                        <div class="node-content">
                          <i class="el-icon-office-building"></i>
                          <span :class="{ 'highlight': isHighlighted(node, deptSearchKeyword) }">
                            {{ node.label }}
                          </span>
                        </div>
                      </span>
                    </el-tree>
                  </div>

                  <!-- 已选择部门提示 -->
                  <div class="selected-dept-info" v-if="selectedDeptIds.length">
                    <el-tag type="success" size="small">
                      已选择 {{ selectedDeptIds.length }} 个部门
                    </el-tag>
                    <el-button type="text" size="small" @click="clearSelectedDepts">清空选择</el-button>
                  </div>
                </div>
              </el-form-item>
            </template>

            <!-- 部门树选择(包含子部门) -->
            <template v-if="pushForm.receiverType === 'DEPT'">
              <el-form-item label="选择部门" prop="deptIds">
                <div class="dept-tree-container">
                  <!-- 部门搜索框 -->
                  <div class="tree-search">
                    <el-input
                      v-model="deptSearchKeyword"
                      placeholder="搜索部门名称"
                      prefix-icon="el-icon-search"
                      clearable
                      size="small"
                      @clear="handleDeptSearchClear"
                    />
                  </div>
                  
                  <!-- 部门树 -->
                  <div class="tree-wrapper thin-scrollbar">
                    <el-tree
                      ref="departmentTree"
                      :data="treeData"
                      :props="defaultProps"
                      :filter-node-method="filterNode"
                      node-key="id"
                      highlight-current
                      show-checkbox
                      :default-checked-keys="selectedDeptIds"
                      @check="handleDeptCheck"
                    >
                      <span slot-scope="{ node }" class="custom-tree-node">
                        <div class="node-content">
                          <i class="el-icon-office-building"></i>
                          <span :class="{ 'highlight': isHighlighted(node, deptSearchKeyword) }">
                            {{ node.label }}
                          </span>
                        </div>
                      </span>
                    </el-tree>
                  </div>

                  <!-- 已选择部门提示 -->
                  <div class="selected-dept-info" v-if="selectedDeptIds.length">
                    <el-tag type="success" size="small">
                      已选择 {{ selectedDeptIds.length }} 个部门
                    </el-tag>
                    <el-button type="text" size="small" @click="clearSelectedDepts">清空选择</el-button>
                  </div>
                </div>
              </el-form-item>
            </template>

            <!-- 角色选择 -->
            <template v-if="pushForm.receiverType === 'ROLE'">
              <el-form-item label="选择角色" prop="roleIds">
                <el-select
                  v-model="pushForm.roleIds"
                  multiple
                  filterable
                  placeholder="请选择角色"
                  style="width: 100%"
                  :loading="roleLoading"
                  popper-class="message-push-role-dropdown"
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                    <div class="role-option">
                      <i class="el-icon-s-custom"></i>
                      <span>{{ item.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </template>

            <!-- 部门和角色二次过滤 -->
            <template v-if="pushForm.receiverType === 'DEPT_AND_ROLE'">
              <el-form-item label="选择部门" prop="deptIds">
                <div class="dept-tree-container">
                  <!-- 部门搜索框 -->
                  <div class="tree-search">
                    <el-input
                      v-model="deptSearchKeyword"
                      placeholder="搜索部门名称"
                      prefix-icon="el-icon-search"
                      clearable
                      size="small"
                      @clear="handleDeptSearchClear"
                    />
                  </div>
                  
                  <!-- 部门树 -->
                  <div class="tree-wrapper thin-scrollbar">
                    <el-tree
                      ref="departmentAndRoleTree"
                      :data="treeData"
                      :props="defaultProps"
                      :filter-node-method="filterNode"
                      node-key="id"
                      highlight-current
                      show-checkbox
                      :check-strictly="true"
                      :default-checked-keys="selectedDeptIds"
                      @check="handleDeptOnlyCheck"
                    >
                      <span slot-scope="{ node }" class="custom-tree-node">
                        <div class="node-content">
                          <i class="el-icon-office-building"></i>
                          <span :class="{ 'highlight': isHighlighted(node, deptSearchKeyword) }">
                            {{ node.label }}
                          </span>
                        </div>
                      </span>
                    </el-tree>
                  </div>

                  <!-- 已选择部门提示 -->
                  <div class="selected-dept-info" v-if="selectedDeptIds.length">
                    <el-tag type="success" size="small">
                      已选择 {{ selectedDeptIds.length }} 个部门
                    </el-tag>
                    <el-button type="text" size="small" @click="clearSelectedDepts">清空选择</el-button>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="选择角色" prop="roleIds">
                <el-select
                  v-model="pushForm.roleIds"
                  multiple
                  filterable
                  placeholder="请选择角色"
                  style="width: 100%"
                  :loading="roleLoading"
                  popper-class="message-push-role-dropdown"
                >
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                    <div class="role-option">
                      <i class="el-icon-s-custom"></i>
                      <span>{{ item.name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </div>

          <div class="form-section">
            <div class="section-title">消息内容</div>
            
            <el-form-item label="消息类型" prop="messageType">
              <el-radio-group v-model="messageType">
                <el-radio label="CUSTOM">指定消息</el-radio>
                <el-radio label="TEMPLATE">模板消息</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <!-- 模板消息选择 -->
            <template v-if="messageType === 'TEMPLATE'">
              <el-form-item label="选择模板" prop="templateId">
                <el-select 
                  v-model="pushForm.templateId" 
                  filterable 
                  placeholder="请选择消息模板"
                  style="width: 100%"
                  :loading="templateLoading"
                  popper-class="message-push-template-dropdown"
                >
                  <el-option
                    v-for="item in templateList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                    <div class="template-option">
                      <i class="el-icon-document-copy"></i>
                      <span>{{ item.name }}</span>
                      <!-- <el-tag size="mini" type="info">{{ item.providerId ? '自定义模板' : '系统模板' }}</el-tag> -->
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              
              <!-- 模板预览 -->
              <div v-if="templateDetail" class="template-preview">
                <div class="preview-header">
                  <span>模板预览</span>
                </div>
                <div class="preview-content">
                  <div class="preview-title">{{ templateDetail.name }}</div>
                  <div class="preview-body">
                    <div>模板代码：{{ templateDetail.code }}</div>
                    <div v-if="templateDetail.thirdTemplateCode" style="margin-top: 10px">三方模板代码：{{ templateDetail.thirdTemplateCode.trim() }}</div>
                    <div style="margin-top: 10px">模板内容：{{ templateDetail.templateContent }}</div>
                  </div>
                </div>
              </div>
            </template>
            
            <!-- 指定消息编辑 -->
            <template v-else>
              <el-form-item label="标题" prop="title" v-if="isSmtpProvider">
                <el-input
                  v-model="pushForm.title"
                  placeholder="请输入消息标题"
                ></el-input>
              </el-form-item>
              <el-form-item label="内容" prop="content">
                <!-- SMTP供应商使用富文本编辑器 -->
                <div class="editor-container" v-if="isSmtpProvider">
                  <Editor
                    v-model="pushForm.content"
                    :value="pushForm.content"
                    :disable="false"
                    @onContentChange="onContentChange"
                  />
                </div>
                <!-- 其他供应商使用普通文本框 -->
                <el-input
                  v-else
                  v-model="pushForm.content"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入消息内容"
                ></el-input>
              </el-form-item>
              <!-- 站内信链接输入 -->
              <el-form-item label="链接" prop="link" v-if="isImProvider">
                <el-input
                  v-model="pushForm.link"
                  placeholder="请输入消息跳转链接（选填）"
                ></el-input>
              </el-form-item>
            </template>
          </div>

          <div class="form-section">
            <div class="section-title">发送设置</div>
            <el-form-item label="发送时间" prop="sendType">
              <el-radio-group v-model="pushForm.sendType" @change="handleSendTypeChange">
                <el-radio label="NOW">立即发送</el-radio>
                <el-radio label="SCHEDULE">定时发送</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item 
              v-if="pushForm.sendType === 'SCHEDULE'" 
              label="定时时间" 
              prop="scheduledTime"
            >
              <el-date-picker
                v-model="pushForm.scheduledTime"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                :picker-options="{
                  disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                  }
                }"
              ></el-date-picker>
            </el-form-item>
          </div>
        </el-form>

        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <template v-if="isPresetMode">
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">保存</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">发送</el-button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDeptTreeList } from '@system/api/sys/treeAccount'
import { getProviderList, getTemplateList, getTemplateDetail, commitMessagePush, getPresetTaskDetail, updatePresetTask, createPresetTask, getPresetTaskGroupNames } from '@system/api/notice/manager'
import { getUserList } from '@system/api/sys/treeAccount'
import { getRoleList } from '@system/api/sys/role' // 导入角色API
import Editor from '@/components/auto-page/Editor/index.vue'

export default {
  name: 'MessagePush',
  components: {
    Editor
  },
  data() {
    return {
      // 部门树相关
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      deptSearchKeyword: '',
      selectedDeptIds: [],

      // 消息供应商列表
      providerList: [],
      
      // 用户搜索相关
      userSearchLoading: false,
      userOptions: [],
      
      // 消息模板相关
      messageType: 'CUSTOM', // TEMPLATE: 模板消息, CUSTOM: 指定消息
      templateList: [],
      templateDetail: null,
      templateLoading: false,
      
      // 角色相关
      roleOptions: [],
      roleLoading: false,
      
      // 表单相关
      pushForm: {
        name: '',
        code: '',
        providerId: '',
        receiverType: 'USER',
        userIds: [],
        deptIds: [],
        roleIds: [], // 新增角色ID数组
        title: '',
        content: '',
        link: '',
        sendType: 'NOW',
        scheduledTime: '',
        templateId: '',
        groupName: '',
        remark: ''
      },
      pushRules: {
        ...this.isPresetMode ? {
          name: [
            { required: true, message: '请输入任务名称', trigger: 'blur' },
            { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
          ],
          code: [
            { required: true, message: '请输入任务代码', trigger: 'blur' },
            { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
          ]
        } : {},
        providerId: [
          { required: true, message: '请选择消息供应商', trigger: 'change' }
        ],
        userIds: [
          { required: true, message: '请选择接收用户', trigger: 'change' }
        ],
        deptIds: [
          { required: true, message: '请选择部门', trigger: 'change' },
          { type: 'array', min: 1, message: '请至少选择一个部门', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入消息标题', trigger: 'blur' },
          { max: 50, message: '标题长度不能超过50个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入消息内容', trigger: 'blur' }
        ],
        scheduledTime: [
          { required: true, message: '请选择定时发送时间', trigger: 'change' }
        ],
        templateId: [
          { required: true, message: '请选择消息模板', trigger: 'change' }
        ],
        roleIds: [
          { required: true, message: '请选择角色', trigger: 'change' },
          { type: 'array', min: 1, message: '请至少选择一个角色', trigger: 'change' }
        ]
      },
      submitLoading: false,
      isPresetMode: false,
      presetMode: 'create', // create 或 edit
      presetTaskId: null,
      groupOptions: [],
    }
  },
  computed: {
    // 当前选择的供应商信息
    currentProvider() {
      return this.providerList.find(provider => provider.id === this.pushForm.providerId) || {}
    },
    // 是否是SMTP供应商
    isSmtpProvider() {
      return this.currentProvider.pluginBeanName === 'NoticeSmtpPlugin'
    },
    // 是否是阿里云短信供应商
    isAliSmsProvider() {
      return this.currentProvider.pluginBeanName === 'NoticeAliYunSmsPlugin'
    },
    // 是否是站内信供应商
    isImProvider() {
      return this.currentProvider.pluginBeanName === 'NoticeImPlugin'
    },
    // 页面标题
    pageTitle() {
      if (this.isPresetMode) {
        return this.presetMode === 'create' ? '新建预设任务' : '编辑预设任务'
      }
      return '消息推送'
    },
    // 是否显示基本信息区域
    showBasicInfo() {
      return this.isPresetMode
    }
  },
  watch: {
    deptSearchKeyword(val) {
      this.$refs.departmentTree && this.$refs.departmentTree.filter(val)
      this.$refs.departmentOnlyTree && this.$refs.departmentOnlyTree.filter(val)
      this.$refs.departmentAndRoleTree && this.$refs.departmentAndRoleTree.filter(val)
    },
    'pushForm.receiverType': {
      handler(newVal) {
        // 根据接收人类型修改表单校验规则
        if (newVal === 'USER') {
          this.pushRules.userIds = [
            { required: true, message: '请选择接收用户', trigger: 'change' },
            { type: 'array', min: 1, message: '请至少选择一个用户', trigger: 'change' }
          ]
          this.pushRules.roleIds = []
          this.pushRules.deptIds = []
        } else if (newVal === 'DEPT' || newVal === 'DEPT_ONLY' || newVal === 'DEPT_AND_ROLE') {
          // 加载部门树数据
          if (this.treeData.length === 0) {
            this.getTreeData()
          }
          // 部门选择校验
          this.pushRules.userIds = []
          this.pushRules.deptIds = [
            { required: true, message: '请选择部门', trigger: 'change' },
            { type: 'array', min: 1, message: '请至少选择一个部门', trigger: 'change' }
          ]
          
          // 如果是部门和角色二次过滤，需要加载角色列表
          if (newVal === 'DEPT_AND_ROLE') {
            this.loadRoleList()
            this.pushRules.roleIds = [
              { required: true, message: '请选择角色', trigger: 'change' },
              { type: 'array', min: 1, message: '请至少选择一个角色', trigger: 'change' }
            ]
          } else {
            this.pushRules.roleIds = []
          }
        } else if (newVal === 'ROLE') {
          // 加载角色列表
          this.loadRoleList()
          this.pushRules.roleIds = [
            { required: true, message: '请选择角色', trigger: 'change' },
            { type: 'array', min: 1, message: '请至少选择一个角色', trigger: 'change' }
          ]
          this.pushRules.userIds = []
          this.pushRules.deptIds = []
        } else {
          // ALL 类型不需要校验接收人
          this.pushRules.userIds = []
          this.pushRules.roleIds = []
          this.pushRules.deptIds = []
        }
      },
      immediate: true
    },
    'pushForm.templateId': {
      handler(val) {
        if (val) {
          // 选择模板后，获取模板详情
          this.getTemplateDetail(val)
        } else {
          this.templateDetail = null
        }
      }
    },
    messageType(val) {
      if (val === 'TEMPLATE') {
        // 切换到模板消息时，需要先判断是否已选择供应商
        if (this.pushForm.providerId) {
          this.getTemplateList({providerId: this.pushForm.providerId, size: -1})
        } else {
          this.$message.warning('请先选择消息供应商')
          this.messageType = 'CUSTOM' // 如果未选择供应商，自动切回指定消息
        }
      } else {
        // 切换到指定消息时，清空模板相关数据
        this.pushForm.templateId = ''
        this.templateDetail = null
      }
    },
    'pushForm.providerId': {
      handler(val) {
        // 根据供应商类型动态设置表单验证规则
        if (this.isSmtpProvider) {
          // SMTP供应商需要标题和内容
          this.pushRules.title = [
            { required: true, message: '请输入消息标题', trigger: 'blur' },
            { max: 50, message: '标题长度不能超过50个字符', trigger: 'blur' }
          ]
        } else {
          // 其他供应商只需要内容
          this.pushRules.title = []
        }
        
        // 清空表单内容
        this.pushForm.title = ''
        this.pushForm.content = ''

        // 如果当前是模板消息模式，重新获取模板列表
        if (this.messageType === 'TEMPLATE') {
          if (val) {
            this.getTemplateList({providerId: val, size: -1})
          } else {
            this.templateList = []
            this.pushForm.templateId = ''
            this.templateDetail = null
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // 检查是否是预设任务模式
    const { type, mode, taskId, groupName } = this.$route.query
    this.isPresetMode = type === 'preset'
    this.presetMode = mode || 'create'
    this.presetTaskId = taskId
    
    // 如果是新建模式且传入了分组，设置默认分组
    if (this.isPresetMode && this.presetMode === 'create' && groupName) {
      this.pushForm.groupName = groupName
    }

    // 如果是编辑模式，获取预设任务详情
    if (this.isPresetMode && this.presetMode === 'edit' && this.presetTaskId) {
      this.loadPresetTaskDetail()
    }

    // 获取供应商列表
    this.getProviderList()
  },
  methods: {
    // 获取部门树数据
    async getTreeData() {
      try {
        const data = await getDeptTreeList()
        this.treeData = data || []
      } catch (error) {
        this.$message.error('获取组织架构失败')
      }
    },

    // 获取消息供应商列表
    async getProviderList() {
      try {
        const data = await getProviderList({ size: -1 })
        this.providerList = data?.records || []
      } catch (error) {
        this.$message.error('获取消息供应商列表失败')
      }
    },

    // 获取消息模板列表
    async getTemplateList(params) {
      this.templateLoading = true
      try {
        const data = await getTemplateList(params)
        this.templateList = data?.records || []
      } catch (error) {
        this.$message.error('获取消息模板列表失败')
      } finally {
        this.templateLoading = false
      }
    },

    // 获取模板详情
    async getTemplateDetail(id) {
      this.templateLoading = true
      try {
        const data = await getTemplateDetail(id)
        this.templateDetail = data
        
        // 自动填充模板标题和内容
        if (this.templateDetail) {
          this.pushForm.title = this.templateDetail.name || ''
          this.pushForm.content = this.templateDetail.templateContent || ''
        }
      } catch (error) {
        this.$message.error('获取模板详情失败')
      } finally {
        this.templateLoading = false
      }
    },

    // 部门树节点过滤
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase())
    },

    // 清除部门搜索
    handleDeptSearchClear() {
      this.deptSearchKeyword = ''
      this.$refs.departmentTree && this.$refs.departmentTree.filter('')
      this.$refs.departmentOnlyTree && this.$refs.departmentOnlyTree.filter('')
      this.$refs.departmentAndRoleTree && this.$refs.departmentAndRoleTree.filter('')
    },

    // 判断节点是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false
      return node.label.toLowerCase().includes(keyword.toLowerCase())
    },

    // 处理部门选择
    handleDeptCheck(data, checked) {
      this.selectedDeptIds = checked.checkedKeys
      this.pushForm.deptIds = checked.checkedKeys
    },

    // 处理部门选择(仅选择部门,不包含子部门)
    handleDeptOnlyCheck(data, checked) {
      this.selectedDeptIds = checked.checkedKeys
      this.pushForm.deptIds = checked.checkedKeys
    },

    // 清空已选择的部门
    clearSelectedDepts() {
      this.selectedDeptIds = []
      this.pushForm.deptIds = []
      if (this.pushForm.receiverType === 'DEPT' && this.$refs.departmentTree) {
        this.$refs.departmentTree.setCheckedKeys([])
      } else if (this.pushForm.receiverType === 'DEPT_ONLY' && this.$refs.departmentOnlyTree) {
        this.$refs.departmentOnlyTree.setCheckedKeys([])
      } else if (this.pushForm.receiverType === 'DEPT_AND_ROLE' && this.$refs.departmentAndRoleTree) {
        this.$refs.departmentAndRoleTree.setCheckedKeys([])
      }
    },

    // 获取插件图标
    getPluginIcon(pluginBeanName) {
      const iconMap = {
        'NoticeSmtpPlugin': 'el-icon-message',
        'NoticeAliYunSmsPlugin': 'el-icon-mobile',
        'NoticeTencentSmsPlugin': 'el-icon-mobile',
        'NoticeWebhookPlugin': 'el-icon-connection'
      }
      return iconMap[pluginBeanName] || 'el-icon-bell'
    },

    // 获取插件颜色
    getPluginColor(pluginBeanName) {
      const colorMap = {
        'NoticeSmtpPlugin': '#409EFF',
        'NoticeAliYunSmsPlugin': '#67C23A',
        'NoticeTencentSmsPlugin': '#E6A23C',
        'NoticeWebhookPlugin': '#909399'
      }
      return colorMap[pluginBeanName] || '#409EFF'
    },

    // 获取插件标签类型
    getPluginTagType(pluginBeanName) {
      const typeMap = {
        'NoticeSmtpPlugin': 'primary',
        'NoticeAliYunSmsPlugin': 'success',
        'NoticeTencentSmsPlugin': 'warning',
        'NoticeWebhookPlugin': 'info'
      }
      return typeMap[pluginBeanName] || 'primary'
    },

    // 远程搜索用户
    async remoteSearchUsers(query) {
      if (query.trim() === '') {
        this.userOptions = []
        return
      }

      this.userSearchLoading = true
      try {
        const { records } = await getUserList({
          keyword: query,
          size: 20
        })
        // 处理用户头像URL和组织信息
        this.userOptions = (records || []).map(user => ({
          ...user,
          picUrl: user.picUrl ? process.env.VUE_APP_FILE_URL + '/' + user.picUrl : '',
          deptName: user.deptName || '未分配部门' // 添加部门名称
        }))
      } catch (error) {
        console.error('搜索用户失败', error)
      } finally {
        this.userSearchLoading = false
      }
    },

    // 处理接收人类型变更
    handleReceiverTypeChange(type) {
      // 重置所有选择
      this.pushForm.userIds = []
      this.selectedDeptIds = []
      this.pushForm.deptIds = []
      this.pushForm.roleIds = []
      
      // 根据类型加载不同数据
      if (type === 'DEPT' || type === 'DEPT_ONLY' || type === 'DEPT_AND_ROLE') {
        // 加载部门树数据
        if (this.treeData.length === 0) {
          this.getTreeData()
        }
        
        // 确保之前的选择被清空
        if (this.$refs.departmentTree) {
          this.$refs.departmentTree.setCheckedKeys([])
        }
        if (this.$refs.departmentOnlyTree) {
          this.$refs.departmentOnlyTree.setCheckedKeys([])
        }
        if (this.$refs.departmentAndRoleTree) {
          this.$refs.departmentAndRoleTree.setCheckedKeys([])
        }
      }
      
      if (type === 'ROLE' || type === 'DEPT_AND_ROLE') {
        // 加载角色列表
        this.loadRoleList()
      }
    },

    // 处理发送类型变更
    handleSendTypeChange(type) {
      if (type === 'NOW') {
        this.pushForm.scheduledTime = ''
      } else {
        // 设置默认时间为明天此刻
        const tomorrow = new Date()
        tomorrow.setDate(tomorrow.getDate() + 1)
        this.pushForm.scheduledTime = tomorrow.toISOString().substring(0, 19).replace('T', ' ')
      }
    },

    // 内容变更
    onContentChange(content) {
      console.log('content changed:', content)
      this.pushForm.content = content
    },

    // 重置表单
    resetForm() {
      this.$refs.pushForm.resetFields()
      this.pushForm = {
        name: '',
        code: '',
        providerId: '',
        receiverType: 'USER',
        userIds: [],
        deptIds: [],
        roleIds: [], // 新增角色ID数组
        title: '',
        content: '',
        link: '',
        sendType: 'NOW',
        scheduledTime: '',
        templateId: '',
        groupName: '',
        remark: ''
      }
      
      // 重置消息类型
      this.messageType = 'CUSTOM'
      this.selectedDeptIds = []
      if (this.$refs.departmentTree) {
        this.$refs.departmentTree.setCheckedKeys([])
      }
      if (this.$refs.departmentOnlyTree) {
        this.$refs.departmentOnlyTree.setCheckedKeys([])
      }
      if (this.$refs.departmentAndRoleTree) {
        this.$refs.departmentAndRoleTree.setCheckedKeys([])
      }
    },

    // 加载预设任务详情
    async loadPresetTaskDetail() {
      try {
        const data = await getPresetTaskDetail(this.presetTaskId)
        if (data) {
          // 等待供应商列表加载完成
          if (this.providerList.length === 0) {
            await this.getProviderList()
          }

          // 根据providerId找到对应的供应商
          const provider = this.providerList.find(p => p.id === data.providerId)
          
          // 设置消息类型
          this.messageType = data.messageType || 'CUSTOM'
          
          // 解析content JSON
          let parsedContent = ''
          let parsedLink = ''
          if (data.content) {
            try {
              const contentObj = JSON.parse(data.content)
              // 确保解析出的内容不是undefined或null
              parsedContent = contentObj.content ?? ''
              parsedLink = contentObj.link ?? ''
              console.log('解析后的content:', parsedContent)
              console.log('解析后的link:', parsedLink)
            } catch (e) {
              console.error('解析content失败:', e)
              parsedContent = data.content
            }
          }

          // 填充表单数据
          this.pushForm = {
            name: data.name,
            code: data.code,
            remark: data.remark,
            providerId: data.providerId,
            receiverType: data.receiverType,
            title: data.title || '',
            content: parsedContent,
            link: parsedLink,
            sendType: 'NOW',
            scheduledTime: '',
            templateId: data.templateId,
            groupName: data.groupName,
            userIds: [],
            deptIds: [],
            roleIds: data.roleIds || [] // 添加角色ID数组
          }

          // 确保在DOM更新后设置内容
          this.$nextTick(() => {
            // 如果是富文本编辑器，需要手动触发内容更新
            if (this.isSmtpProvider) {
              this.onContentChange(parsedContent)
            }
            // 对于站内信，确保内容和链接都正确设置
            if (this.isImProvider) {
              this.pushForm.content = parsedContent
              this.pushForm.link = parsedLink
            }
          })
          
          // 处理接收人数据
          if (data.receiverType === 'USER' && data.accountIds) {
            this.pushForm.userIds = data.accountIds
            // 加载用户信息
            if (data.accountIds.length > 0) {
              const { records } = await getUserList({
                ids: data.accountIds.join(','),
                size: -1
              })
              this.userOptions = (records || []).map(user => ({
                ...user,
                picUrl: user.picUrl ? process.env.VUE_APP_FILE_URL + '/' + user.picUrl : '',
                deptName: user.deptName || '未分配部门'
              }))
            }
          } else if (['DEPT', 'DEPT_ONLY', 'DEPT_AND_ROLE'].includes(data.receiverType)) {
            this.pushForm.deptIds = data.deptIds || []
            this.selectedDeptIds = data.deptIds || []
            // 加载部门树
            if (this.treeData.length === 0) {
              await this.getTreeData()
            }
            // 设置选中的部门
            if (this.$refs.departmentTree) {
              this.$refs.departmentTree.setCheckedKeys(this.selectedDeptIds)
            }
            if (this.$refs.departmentOnlyTree) {
              this.$refs.departmentOnlyTree.setCheckedKeys(this.selectedDeptIds)
            }
            if (this.$refs.departmentAndRoleTree) {
              this.$refs.departmentAndRoleTree.setCheckedKeys(this.selectedDeptIds)
            }
          }
          
          // 处理角色数据
          if (['ROLE', 'DEPT_AND_ROLE'].includes(data.receiverType) && data.roleIds) {
            this.pushForm.roleIds = data.roleIds
            // 加载角色列表
            await this.loadRoleList()
          }

          // 如果是模板消息，需要加载模板详情和列表
          if (this.messageType === 'TEMPLATE') {
            if (provider) {
              await this.getTemplateList({providerId: provider.id, size: -1})
            }
            if (data.templateId) {
              await this.getTemplateDetail(data.templateId)
            }
          }
        }
      } catch (error) {
        console.error('获取预设任务详情失败:', error)
        this.$message.error('获取预设任务详情失败')
      }
    },

    // 重写提交方法
    async handleSubmit() {
      this.$refs.pushForm.validate(async valid => {
        if (!valid) return

        // 模板消息校验
        if (this.messageType === 'TEMPLATE' && !this.pushForm.templateId) {
          this.$message.warning('请选择消息模板')
          return
        }

        this.submitLoading = true
        try {
          // 构建提交数据
          const messageData = {
            providerId: this.pushForm.providerId,
            receiverType: this.pushForm.receiverType,
            messageType: this.messageType,
            content: this.getFormattedContent(),
            templateId: this.messageType === 'TEMPLATE' ? 
              this.pushForm.templateId : undefined,
            sendType: this.pushForm.sendType,
            scheduledTime: this.pushForm.sendType === 'SCHEDULE' ? 
              this.pushForm.scheduledTime : undefined
          }

          // 根据接收人类型设置不同的字段
          if (this.pushForm.receiverType === 'USER') {
            messageData.accountIds = this.pushForm.userIds
          } else if (['DEPT', 'DEPT_ONLY', 'DEPT_AND_ROLE'].includes(this.pushForm.receiverType)) {
            messageData.deptIds = this.pushForm.deptIds
          }
          
          // 设置角色IDs
          if (['ROLE', 'DEPT_AND_ROLE'].includes(this.pushForm.receiverType)) {
            messageData.roleIds = this.pushForm.roleIds
          }

          if (this.isPresetMode) {
            // 预设任务模式
            const taskData = {
              ...messageData,
              name: this.pushForm.name,
              code: this.pushForm.code,
              remark: this.pushForm.remark,
              enabled: true,
              groupName: this.pushForm.groupName
            }
            
            if (this.presetMode === 'edit') {
              taskData.id = this.presetTaskId
              await updatePresetTask(taskData)
              this.$message.success('更新成功')
            } else {
              await createPresetTask(taskData)
              this.$message.success('创建成功')
            }
            
            // 返回预设任务列表页
            this.$router.push('/notice/preset/task')
          } else {
            // 普通消息推送模式
            await commitMessagePush(messageData)
            this.$message.success('消息发送成功')
          }
        } catch (error) {
          console.error('操作失败', error)
          this.$message.error('操作失败: ' + (error.message || '未知错误'))
        } finally {
          this.submitLoading = false
        }
      })
    },

    // 根据供应商类型格式化内容
    getFormattedContent() {
      if (this.messageType === 'TEMPLATE') {
        return undefined
      }

      // 站内信格式化
      if (this.isImProvider) {
        const content = {
          content: this.pushForm.content || '',
          link: this.pushForm.link || ''
        }
        console.log('提交的站内信内容:', content)
        return JSON.stringify(content)
      }
      
      // SMTP供应商格式化
      if (this.isSmtpProvider) {
        return JSON.stringify({
          title: this.pushForm.title || '',
          content: this.pushForm.content || ''
        })
      }
      
      // 其他供应商直接返回内容
      return this.pushForm.content || ''
    },

    // 处理取消操作
    handleCancel() {
      this.$router.push('/notice/preset/task')
    },

    // 加载分组名称列表
    async loadGroupNames() {
      try {
        const  data = await getPresetTaskGroupNames()
        this.groupOptions = data || []
      } catch (error) {
        console.error('获取分组列表失败:', error)
      }
    },

    // 加载角色列表
    async loadRoleList() {
      this.roleLoading = true
      try {
        const { records } = await getRoleList({ size: -1 })
        this.roleOptions = records || []
      } catch (error) {
        console.error('获取角色列表失败', error)
        this.$message.error('获取角色列表失败')
      } finally {
        this.roleLoading = false
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.message-push-container {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  overflow: hidden;

  .push-card {
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
    
    &:hover {
      box-shadow: 0 10px 30px rgba(31, 45, 61, 0.1);
    }

    .card-header {
      padding: 20px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);
      flex-shrink: 0;

      .header-title {
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        display: flex;
        align-items: center;
        gap: 8px;
        letter-spacing: 0.5px;
        padding-left: 12px;
        border-left: 3px solid #409EFF;
      }
    }

    .form-wrapper {
      flex: 1;
      overflow-y: auto;
      padding: 20px;

      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .push-form {
        .form-section {
          background-color: #f8f9fb;
          border-radius: 16px;
          padding: 24px;
          margin-bottom: 24px;
          box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

          &:hover {
            background-color: #f5f7fa;
          }

          .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a1f36;
            margin-bottom: 24px;
            padding-left: 12px;
            border-left: 3px solid #409EFF;
            letter-spacing: 0.5px;
          }
        }

        .provider-option {
          display: flex;
          align-items: center;
          gap: 10px;
          
          i {
            font-size: 18px;
            margin-right: 8px;
          }
          
          span {
            margin-right: 8px;
          }
          
          .el-tag {
            margin-left: auto;
          }
        }

        .dept-tree-container {
          border: 1px solid #e0e5ee;
          border-radius: 8px;
          overflow: hidden;
          
          .tree-search {
            padding: 10px;
            background-color: #f8f9fb;
            border-bottom: 1px solid #e0e5ee;
          }
          
          .tree-wrapper {
            height: 300px;
            overflow: auto;
            padding: 10px;
          }
          
          .thin-scrollbar {
            &::-webkit-scrollbar {
              width: 4px;
            }
            
            &::-webkit-scrollbar-thumb {
              background: rgba(144, 147, 153, 0.3);
              border-radius: 2px;
            }
            
            &::-webkit-scrollbar-track {
              background: transparent;
            }
          }
          
          .selected-dept-info {
            padding: 10px;
            background-color: #f8f9fb;
            border-top: 1px solid #e0e5ee;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        }

        .editor-container {
          border-radius: 4px;
          overflow: hidden;
        }
      }

      .form-actions {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 30px;
        margin-bottom: 20px;

        .el-button {
          padding: 12px 30px;
          font-size: 16px;
          border-radius: 8px;
        }
      }
    }
  }
}

.highlight {
  color: #409EFF;
  font-weight: 600;
  position: relative;
  z-index: 1;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: rgba(64, 158, 255, 0.2);
    z-index: -1;
    border-radius: 3px;
  }
}

::v-deep {
  .el-form-item {
    margin-bottom: 22px;

    .el-form-item__label {
      font-weight: 500;
    }

    .el-input, .el-select, .el-radio-group {
      width: 100%;
    }

    .el-input__inner {
      border-radius: 8px;
      height: 40px;
      border-color: #e0e5ee;
      
      &:focus {
        border-color: #409EFF;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
      }
      
      &:hover {
        border-color: #c0d0e9;
      }
    }

    .el-radio {
      margin-right: 20px;
      line-height: 40px;
    }
  }
  
  .el-select-dropdown__wrap {
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
}

.template-option {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 8px 0;

  i {
    font-size: 18px;
    color: #409EFF;
  }
}

.template-preview {
  border: 1px solid #e0e5ee;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
  
  .preview-header {
    padding: 12px 15px;
    background-color: #f8f9fb;
    border-bottom: 1px solid #e0e5ee;
    font-weight: 500;
    color: #303133;
    
    span {
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #409EFF;
        border-radius: 3px;
      }
    }
  }
  
  .preview-content {
    padding: 15px;
    background-color: #fff;
    
    .preview-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #e0e5ee;
    }
    
    .preview-body {
      color: #606266;
      line-height: 1.6;
      max-height: 300px;
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }
  }
}

.el-select .el-tag {
  background-color: #ecf5ff;
  color: #409EFF;
  border-color: #d9ecff;
  margin: 2px 4px 2px 0;
  max-width: 100%;
  box-sizing: border-box;
}

.el-select__tags {
  flex-wrap: wrap;
  max-width: 100%;
  
  .el-tag {
    display: flex;
    align-items: center;
    max-width: calc(100% - 8px);
    overflow: hidden;
    
    .el-select__tags-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 20px);
    }
  }
}

.el-select .el-input {
  .el-input__inner {
    height: auto;
    min-height: 40px;
    height: fit-content;
  }
}

.el-tree-node__content {
  height: 32px;
  border-radius: 8px;
  margin: 2px 0;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f5f7fa;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    
    .node-content {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 14px;
      }
    }
  }
  
  &.is-current {
    background-color: #ecf5ff !important;
    color: #409EFF;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
    
    .custom-tree-node .node-content {
      span {
        color: #409EFF;
      }
    }
  }
}
</style>

<style lang="scss">
/* 使用特定class选择器限制样式只应用于指定的下拉菜单 */
.message-push-user-dropdown {
  .el-select-dropdown__item {
    padding: 0 !important;
    height: 20px !important;
    min-height: 62px !important;  /* 增加高度以适应更多内容 */
    line-height: normal !important;
  }

  .user-option {
    display: flex;
    align-items: flex-start;
    padding: 8px 10px;
    gap: 8px;
    width: 100%;
    height: auto !important;
    box-sizing: border-box;
    min-height: 62px;
    overflow: visible;

    .el-avatar {
      flex: 0 0 28px;
      width: 28px !important;
      height: 28px !important;
      border: 1px solid rgba(0, 0, 0, 0.1);
      background: #e1f0ff;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #2b85e4;
      font-weight: 600;
    }

    .user-info {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .info-row {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .name {
          font-size: 13px;
          color: #303133;
          font-weight: 500;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .account {
          font-size: 12px;
          color: #909399;
          display: flex;
          align-items: center;
          gap: 4px;
          white-space: nowrap;

          i {
            font-size: 12px;
          }
        }
        
        .dept-info {
          font-size: 12px;
          color: #606266;
          display: flex;
          align-items: center;
          gap: 4px;
          
          i {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  .el-select-dropdown__item.selected .user-option {
    background-color: #f5f7fa;
  }

  .el-select-dropdown__item.selected .user-option .name {
    color: #077cf1;
    font-weight: 600;
  }

  .el-select-dropdown__item:hover {
    background-color: transparent;
  }

  .el-select-dropdown__item:hover .user-option {
    background-color: #f5f7fa;
  }

  &.el-select-dropdown {
    background-color: #fff;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .el-select-dropdown__list {
    background-color: #fff;
  }
}

/* 供应商下拉菜单样式 */
.message-push-provider-dropdown {
  .el-select-dropdown__item {
    padding: 0 !important;
    height: auto !important;
    min-height: 42px !important;
    line-height: normal !important;
  }
  
  .provider-option {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    min-height: 42px;
    
    i {
      font-size: 18px;
      margin-right: 10px;
      flex-shrink: 0;
    }
    
    span {
      flex: 1;
      margin-right: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .el-tag {
      margin-left: auto;
      flex-shrink: 0;
    }
  }
  
  .el-select-dropdown__item.selected .provider-option {
    background-color: #f5f7fa;
  }
  
  .el-select-dropdown__item:hover {
    background-color: transparent;
  }
  
  .el-select-dropdown__item:hover .provider-option {
    background-color: #f5f7fa;
  }
}

/* 模板下拉菜单样式 */
.message-push-template-dropdown {
  .el-select-dropdown__item {
    padding: 0 !important;
    height: auto !important;
    min-height: 42px !important;
    line-height: normal !important;
  }
  
  .template-option {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    gap: 10px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    min-height: 42px;
    
    i {
      font-size: 18px;
      color: #409EFF;
      flex-shrink: 0;
    }
    
    span {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  
  .el-select-dropdown__item.selected .template-option {
    background-color: #f5f7fa;
  }
  
  .el-select-dropdown__item:hover {
    background-color: transparent;
  }
  
  .el-select-dropdown__item:hover .template-option {
    background-color: #f5f7fa;
  }
}

/* 角色选择样式 */
::v-deep {
  .message-push-role-dropdown {
    .el-select-dropdown__item {
      padding: 0 !important;
      height: auto !important;
      min-height: 42px !important;
      line-height: normal !important;
    }
    
    .role-option {
      display: flex;
      align-items: center;
      padding: 6px 10px;
      gap: 10px;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      min-height: 42px;
      
      i {
        font-size: 18px;
        color: #67C23A;
        flex-shrink: 0;
      }
      
      span {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .el-select-dropdown__item.selected .role-option {
      background-color: #f5f7fa;
    }
    
    .el-select-dropdown__item:hover {
      background-color: transparent;
    }
    
    .el-select-dropdown__item:hover .role-option {
      background-color: #f5f7fa;
    }
  }
}
</style>
