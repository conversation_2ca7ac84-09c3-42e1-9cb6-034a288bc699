<template>
  <div class="config-container">
    <div class="page-header">
      <div class="header-title">
        <h2>系统配置</h2>
      </div>
      
      <div class="header-tools">
        <div class="unified-search">
          <el-input v-model="queryParams.keyword" placeholder="请输入配置键/分组/描述" clearable class="search-input"
            prefix-icon="el-icon-search" @keyup.enter.native="handleSearch" @clear="handleSearch">
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>

          <el-select v-model="queryParams.groupName" placeholder="分组" clearable class="status-select"
            @change="handleSearch">
            <el-option v-for="item in groupOptions" :key="item" :label="item" :value="item">
              <i class="el-icon-folder-opened" style="color: #409EFF" />
              <span style="margin-left: 8px">{{ item }}</span>
            </el-option>
          </el-select>

          <el-select v-model="queryParams.enabled" placeholder="状态" clearable class="status-select" @change="handleSearch">
            <el-option label="启用" :value="true">
              <i class="el-icon-check" style="color: #67C23A" />
              <span style="margin-left: 8px">启用</span>
            </el-option>
            <el-option label="禁用" :value="false">
              <i class="el-icon-close" style="color: #F56C6C" />
              <span style="margin-left: 8px">禁用</span>
            </el-option>
          </el-select>
        </div>

        <div class="button-group">
          <el-button type="primary" icon="el-icon-refresh" @click="refreshList">
            刷新列表
          </el-button>
          <el-button type="primary" @click="handleAdd">
            <i class="el-icon-plus"></i>
            新增配置
          </el-button>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <el-table v-loading="loading" :data="configList" border stripe fit
        style="width: 100%" highlight-current-row>
        <el-table-column prop="configKey" label="配置键" min-width="180">
          <template slot-scope="scope">
            <div class="config-key-cell">
              <el-tag size="medium">{{ scope.row.configKey }}</el-tag>
              <i class="el-icon-document-copy copy-icon" @click="handleCopy(scope.row)" />
            </div>
          </template>
        </el-table-column>


        <el-table-column prop="configValue" label="配置值" min-width="100">
          <template slot-scope="scope">
            <span>{{ decodeValue(scope.row.configValue, scope.row.type) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="groupName" label="分组" width="300">
          <template slot-scope="scope">
            <el-tag type="success" size="medium">{{ scope.row.groupName }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="140" />

        <el-table-column prop="type" label="类型" width="150">
          <template slot-scope="scope">
            <el-tag :type="typeColorMap[scope.row.type]" size="medium">
              {{ typeMap[scope.row.type] }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="isPublic" label="公开" width="100">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.isPublic" @change="handleStatusPublicChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column prop="enabled" label="状态" width="100">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.enabled" @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="120" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" icon="el-icon-edit" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" style="color: #F56C6C" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination :current-page="page.current" :page-sizes="[10, 20, 50, 100]" :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper" :total="page.total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 配置表单对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false"
      append-to-body custom-class="config-dialog">
      <el-form ref="configForm" :model="formData" :rules="rules" label-width="100px" class="config-form">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="配置键" prop="configKey">
                <el-input v-model="formData.configKey" placeholder="请输入配置键">
                  <template slot="prepend">
                    <i class="el-icon-key"></i>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="描述" prop="description">
                <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入配置描述" />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="分组" prop="groupName">
                <el-select v-model="formData.groupName" filterable allow-create default-first-option
                  placeholder="请选择或输入分组名称" @focus="loadGroupNames">
                  <el-option v-for="item in groupOptions" :key="item" :label="item" :value="item">
                    <i class="el-icon-folder"></i>
                    <span style="margin-left: 8px">{{ item }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">配置内容</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="类型" prop="type">
                <el-select v-model="formData.type" placeholder="请选择配置类型">
                  <el-option label="字符串" value="java.lang.String">
                    <i class="el-icon-edit-outline"></i>
                    <span style="margin-left: 8px">字符串</span>
                  </el-option>
                  <el-option label="整数" value="java.lang.Integer">
                    <i class="el-icon-tickets"></i>
                    <span style="margin-left: 8px">整数</span>
                  </el-option>
                  <el-option label="布尔值" value="java.lang.Boolean">
                    <i class="el-icon-switch-button"></i>
                    <span style="margin-left: 8px">布尔值</span>
                  </el-option>
                  <el-option label="浮点数" value="java.lang.Double">
                    <i class="el-icon-odometer"></i>
                    <span style="margin-left: 8px">浮点数</span>
                  </el-option>
                  <el-option label="长整数" value="java.lang.Long">
                    <i class="el-icon-tickets"></i>
                    <span style="margin-left: 8px">长整数</span>
                  </el-option>
                  <el-option label="列表" value="java.util.List">
                    <i class="el-icon-collection"></i>
                    <span style="margin-left: 8px">列表</span>
                  </el-option>
                  <el-option label="JSON" value="java.lang.Object">
                    <i class="el-icon-document"></i>
                    <span style="margin-left: 8px">JSON</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="配置值" prop="configValue">
                <template v-if="formData.type === 'java.lang.Object'">
                  <div class="json-editor">
                    <div class="panel-header">
                      <div class="header-title">
                        <span class="title">JSON配置</span>
                        <el-tooltip content="编辑JSON内容" placement="top">
                          <i class="el-icon-question"></i>
                        </el-tooltip>
                      </div>
                      <div class="header-actions">
                        <el-button type="primary" size="small" plain @click="handleDesignerEditor">
                          布局
                        </el-button>
                        <el-button type="primary" size="small" plain @click="handleEditJson">
                          <i class="el-icon-edit"></i>
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div class="panel-content">
                      <vue-json-pretty v-if="!isEditingJson" :data="jsonData" :deep="2" :showDoubleQuotes="true"
                        :showLength="true" :showLine="true" :highlightMouseoverNode="true" />
                      <el-input v-else v-model="formData.configValue" type="textarea" :rows="10" placeholder="请输入JSON格式的配置值"
                        @blur="handleJsonBlur" />
                    </div>
                  </div>
                </template>
                <el-input v-else-if="formData.type === 'java.lang.String'" v-model="formData.configValue"
                  placeholder="请输入配置值" />
                <el-input-number v-else-if="formData.type === 'java.lang.Integer'" v-model="formData.configValue"
                  :controls="true" placeholder="请输入整数值" style="width: 100%" />
                <el-switch v-else-if="formData.type === 'java.lang.Boolean'" v-model="formData.configValue"
                  active-color="#409EFF" />
                <el-input-number v-else-if="formData.type === 'java.lang.Double'" v-model="formData.configValue"
                  :precision="2" :step="0.1" placeholder="请输入浮点数" style="width: 100%" />
                <el-input-number v-else-if="formData.type === 'java.lang.Long'" v-model="formData.configValue"
                  :controls="true" placeholder="请输入长整数" style="width: 100%" />
                <template v-else-if="formData.type === 'java.util.List'">
                  <div class="list-value-container">
                    <div v-for="(item, index) in listValues" :key="index" class="list-item">
                      <el-input v-model="listValues[index]" placeholder="请输入列表项" class="list-item-input">
                        <template slot="append">
                          <el-button type="text" icon="el-icon-delete" class="delete-item-btn"
                            @click="removeListItem(index)" />
                        </template>
                      </el-input>
                    </div>
                    <div class="list-actions">
                      <el-button type="text" @click="addListItem">
                        <i class="el-icon-plus"></i>
                        添加列表项
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="公开" prop="isPublic">
                <el-switch v-model="formData.isPublic" active-color="#409EFF" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="状态" prop="enabled">
                <el-switch v-model="formData.enabled" active-color="#409EFF" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="designerEditorVisible" title="编辑布局" width="80%" center top="8vh">
      <transition name="fade">
        <designerEditor v-if="designerEditorVisible" style="height: 75vh;" :jsonConfig="defaultData" :noCache="true"
          ref="monaco"></designerEditor>
      </transition>
      <div slot="footer" class="dialog-footer">
        <el-button @click="designerEditorVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveDesignerEditor">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getConfigList, createConfig, updateConfig, deleteConfig, getGroupNames } from '@system/api/sys/config'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'

export default {
  name: 'SystemConfig',
  components: {
    VueJsonPretty  // 注册组件
  },
  data() {
    // 添加自定义验证规则
    const validateConfigValue = (rule, value, callback) => {
      if (this.formData.type === 'java.lang.Object') {
        try {
          if (!value) {
            callback(new Error('请输入配置值'))
            return
          }
          JSON.parse(value)
          callback()
        } catch (error) {
          callback(new Error('请输入有效的JSON格式'))
        }
      } else if (this.formData.type === 'java.util.List') {
        // 检查是否至少有一个非空的列表项
        if (this.listValues.some(item => item.trim() !== '')) {
          callback()
        } else {
          callback(new Error('请至少输入一个列表项'))
        }
      } else if (!value && value !== false && value !== 0) {
        callback(new Error('请输入配置值'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      designerEditorVisible: false,
      defaultData: '',
      configList: [],
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: '',
      formData: {
        configKey: '',
        groupName: '',
        description: '',
        type: 'java.lang.String',
        configValue: '',
        enabled: true
      },
      rules: {
        configKey: [
          { required: true, message: '请输入配置键', trigger: 'blur' }
        ],
        groupName: [
          { required: true, message: '请输入分组名称', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入配置描述', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择配置类型', trigger: 'change' }
        ],
        configValue: [
          { required: true, validator: validateConfigValue, trigger: 'blur' }
        ]
      },
      groupOptions: [],
      queryParams: {
        keyword: '',
        enabled: null,
        groupName: '',
        current: 1,
        size: 10
      },
      typeMap: {
        'java.lang.String': '字符串',
        'java.lang.Integer': '整数',
        'java.lang.Boolean': '布尔值',
        'java.lang.Double': '浮点数',
        'java.lang.Long': '长整数',
        'java.util.List': '列表',
        'java.lang.Object': 'JSON'
      },
      listValues: [''],
      typeColorMap: {
        'java.lang.String': '',          // 默认蓝色
        'java.lang.Integer': 'success',  // 绿色
        'java.lang.Boolean': 'warning',  // 黄色
        'java.lang.Double': 'info',      // 灰色
        'java.lang.Long': 'success',     // 绿色
        'java.util.List': 'danger',       // 红色
        'java.lang.Object': 'primary'
      },
      isEditingJson: false,
      jsonData: null
    }
  },
  created() {
    this.fetchData()
    this.loadGroupNames()
  },
  watch: {
    'formData.type'(newType) {
      if (newType === 'java.util.List') {
        this.initListValues()
      } else if (newType === 'java.lang.Boolean') {
        // 只有在新增配置（没有id）时才设置默认值
        if (!this.formData.id) {
          this.formData.configValue = false
        }
      }
    }
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true

        // 构建查询参数，过滤掉空值
        const params = {}
        Object.keys(this.queryParams).forEach(key => {
          if (this.queryParams[key] !== '' &&
            this.queryParams[key] !== null &&
            this.queryParams[key] !== undefined) {
            params[key] = this.queryParams[key]
          }
        })

        const data = await getConfigList(params)
        this.configList = data.records
        this.page.total = data.total
      } catch (error) {
        console.error('获取配置列表失败:', error)
        this.$message.error('获取配置列表失败')
      } finally {
        this.loading = false
      }
    },
    decodeValue(value, type) {
      if (value === null || value === undefined) return '';
      
      // 处理布尔值
      if (type === 'java.lang.Boolean') {
        return value.toString();
      }

      // 处理长文本截断
      const truncate = (text, length = 50) => {
        if (typeof text === 'string' && text.length > length) {
          return text.substring(0, length) + '...'
        }
        return text
      }

      try {
        switch (type) {
          case 'java.lang.Object':
            // JSON类型，格式化后截断
            const formatted = typeof value === 'string' ? JSON.parse(value) : value
            return truncate(JSON.stringify(formatted))
          case 'java.util.List':
            // 列表类型，转换后截断
            const list = Array.isArray(value) ? value : JSON.parse(value)
            return truncate(JSON.stringify(list))
          default:
            // 其他类型直接截断
            return truncate(String(value))
        }
      } catch (error) {
        console.warn('解析值失败:', error)
        return truncate(String(value))
      }
    },
    handleAdd() {
      this.dialogTitle = '新增配置'
      this.formData = {
        configKey: '',
        groupName: '',
        description: '',
        type: 'java.lang.String',
        configValue: '',
        enabled: true
      }
      // 如果初始类型就是布尔值，确保设置默认值
      if (this.formData.type === 'java.lang.Boolean') {
        this.formData.configValue = false
      }
      this.loadGroupNames()
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑配置'
      this.formData = {
        ...row,
        configValue: row.configValue
      }

      // 如果是 JSON 类型，初始化 JSON 预览
      if (row.type === 'java.lang.Object') {
        try {
          const parsed = typeof row.configValue === 'string'
            ? JSON.parse(row.configValue)
            : row.configValue
          this.jsonData = parsed
          this.formData.configValue = JSON.stringify(parsed, null, 2)
          this.isEditingJson = false
        } catch (error) {
          console.warn('JSON 解析失败:', error)
        }
      }

      if (row.type === 'java.util.List') {
        this.initListValues()
      }
      
      // 处理布尔类型值，确保正确转换为布尔值
      if (row.type === 'java.lang.Boolean') {
        // 根据实际值类型进行转换
        if (typeof row.configValue === 'string') {
          // 如果是字符串，转换为布尔值
          this.formData.configValue = row.configValue.toLowerCase() === 'true'
        } else {
          // 如果已经是布尔值或其他类型，确保为布尔值
          this.formData.configValue = Boolean(row.configValue)
        }
      }
      
      this.loadGroupNames()
      this.dialogVisible = true
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该配置项吗？', '提示', {
          type: 'warning'
        })
        await deleteConfig(row.id)
        this.$message.success('删除成功')
        this.fetchData()
      } catch (error) {
        console.error('删除失败:', error)
      }
    },
    // 转换配置值为对应的类型
    convertConfigValue(value, type) {
      if (value === null || value === undefined) {
        return value;
      }

      try {
        switch (type) {
          case 'java.lang.String':
            return String(value);
          case 'java.lang.Integer':
            return parseInt(value, 10);
          case 'java.lang.Long':
            return parseInt(value, 10); // JavaScript不区分Int和Long，都使用Number
          case 'java.lang.Double':
            return parseFloat(value);
          case 'java.lang.Boolean':
            // 增强的布尔值转换，处理字符串"true"和"false"
            if (typeof value === 'string') {
              return value.toLowerCase() === 'true';
            }
            return Boolean(value);
          case 'java.util.List':
            return Array.isArray(value) ? value : JSON.parse(value);
          case 'java.lang.Object':
            return typeof value === 'string' ? JSON.parse(value) : value;
          default:
            return value;
        }
      } catch (error) {
        console.error('配置值转换失败:', error);
        return value;
      }
    },
    async handleSubmit() {
      try {
        await this.$refs.configForm.validate()
        const method = this.formData.id ? updateConfig : createConfig

        // 根据类型转换配置值
        let configValue = this.formData.configValue
        if (this.formData.type === 'java.util.List') {
          // 处理列表类型
          configValue = this.listValues.filter(item => item.trim() !== '')
        }
        
        // 转换配置值为对应的类型
        configValue = this.convertConfigValue(configValue, this.formData.type)

        const data = {
          ...this.formData,
          configValue
        }

        await method(data)
        this.$message.success('保存成功')
        this.dialogVisible = false
        this.fetchData()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      }
    },
    async handleStatusPublicChange(row) {
      try {
        await updateConfig({
          id: row.id,
          isPublic: row.isPublic
        })
        this.$message.success('权限更新成功')
      } catch (error) {
        row.enabled = !row.isPublic
        console.error('权限更新失败:', error)
        this.$message.error('权限更新失败')
      }
    },
    async handleStatusChange(row) {
      try {
        await updateConfig({
          id: row.id,
          enabled: row.enabled
        })
        this.$message.success('状态更新成功')
      } catch (error) {
        row.enabled = !row.enabled
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败')
      }
    },
    handleSizeChange(val) {
      this.queryParams.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.queryParams.current = val
      this.fetchData()
    },
    async loadGroupNames() {
      try {
        const data = await getGroupNames()
        this.groupOptions = data
      } catch (error) {
        console.error('获取分组列表失败:', error)
      }
    },
    handleSearch() {
      this.queryParams.current = 1
      this.fetchData()
    },
    initListValues() {
      try {
        if (this.formData.configValue) {
          // 如果是字符串,尝试解析JSON
          if (typeof this.formData.configValue === 'string') {
            const parsed = JSON.parse(this.formData.configValue)
            // 确保解析结果是数组
            this.listValues = Array.isArray(parsed) ? parsed : ['']
          }
          // 如果直接是数组
          else if (Array.isArray(this.formData.configValue)) {
            this.listValues = this.formData.configValue
          }

          // 其他情况使用默认值
          else {
            this.listValues = ['']
          }
        } else {
          this.listValues = ['']
        }
      } catch (error) {
        console.warn('解析列表值失败:', error)
        this.listValues = ['']
      }
    },
    addListItem() {
      this.listValues.push('')
    },
    removeListItem(index) {
      if (this.listValues.length > 1) {
        this.listValues.splice(index, 1)
      } else {
        this.$message.warning('至少保留一个列表项')
      }
    },
    handleCopy(row) {
      // 创建临时输入框
      const input = document.createElement('input')
      input.value = row.configKey
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)

      // 使用 Element UI 的消息提示
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: 2000
      })
    },
    handleEditJson() {
      this.isEditingJson = true
    },
    handleJsonBlur() {
      try {
        const parsed = JSON.parse(this.formData.configValue)
        this.jsonData = parsed
        this.formData.configValue = JSON.stringify(parsed, null, 2)
        this.isEditingJson = false
      } catch (error) {
        this.$message.error('JSON格式不正确，请检查')
      }
    },
    handleDesignerEditor() {
      this.defaultData = JSON.stringify({
        formRule: JSON.parse(this.formData.configValue)
      })
      this.designerEditorVisible = true
    },
    saveDesignerEditor() {
      try {
        this.isEditingJson = true
        this.designerEditorVisible = false
        let moStr = this.$refs.monaco.getConfig()
        this.formData.configValue = JSON.stringify(moStr.formRule, null, 2)
      } catch (error) {
        console.log('------------------->error', error)
      }
    },
    refreshList() {
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .header-subtitle {
        font-size: 14px;
        color: #909399;
        margin-left: 12px;
      }
    }
    
    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .unified-search {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .search-input {
          width: 250px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;
              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;

              .el-button {
                margin: 0;
                height: 34px;
                border: none;
                padding: 0 16px;
                border-radius: 0 8px 8px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
          }
        }

        .status-select {
          width: 120px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              border-radius: 8px;
              background: #f9fafc;
              border: 1px solid #e0e5ee;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }
          }
        }
      }
      
      .button-group {
        display: flex;
        gap: 12px;
        
        .el-button {
          padding: 8px 16px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 8px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          height: 36px;
          font-size: 14px;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }

      .el-button--text {
        padding: 4px 8px;
        font-size: 13px;
        font-weight: 500;
        border-radius: 4px;
        
        &.el-button--mini {
          padding: 3px 6px;
        }
        
        &:hover {
          background-color: rgba(64, 158, 255, 0.1);
          color: #5aacff;
        }
        
        &:active {
          color: #3a8ee6;
        }
        
        i {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;
      
      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;
        
        .btn-prev, 
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;
          
          &:not(.disabled):hover {
            border-color: #409EFF;
          }
          
          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }
        
        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }
        
        .el-pagination__jump {
          margin-left: 16px;
        }
        
        .el-select .el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }
        
        .el-pagination__editor.el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }
}

.config-dialog {
  // 对话框的特定样式已移至全局el-dialog样式，此处无需重复定义
}

.config-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }
  }

  ::v-deep {
    .el-form-item {
      margin-bottom: 22px;

      &:last-child {
        margin-bottom: 0;
      }

      .el-input,
      .el-select,
      .el-cascader {
        width: 100%;
      }

      .el-input__inner {
        border-radius: 10px;
        height: 38px;
        background: #fff;
        border: 1px solid #e0e5ee;
        
        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
        }
        
        &:hover {
          border-color: #c0d0e9;
        }
      }

      .el-textarea__inner {
        border-radius: 10px;
        background: #fff;
        border: 1px solid #e0e5ee;
        
        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
        }
        
        &:hover {
          border-color: #c0d0e9;
        }
      }

      .el-select,
      .el-cascader {
        .el-input {
          width: 100%;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.list-value-container {
  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .list-item-input {
      flex: 1;

      ::v-deep {
        .el-input__inner {
          border-radius: 10px;
        }

        .el-input-group__append {
          padding: 0;
          border: none;
          background: none;
          position: absolute;
          right: -40px;
          top: 0;
          height: 100%;
          z-index: 1;
          display: flex;
          align-items: center;
        }

        .delete-item-btn {
          padding: 0 12px;
          height: 100%;
          color: #F56C6C;
          display: flex;
          align-items: center;

          &:hover {
            color: #ff7875;
          }
        }

        .el-input__inner {
          padding-right: 15px;
        }
      }
    }

    padding-right: 40px;
    position: relative;
  }

  .list-actions {
    margin-top: 8px;

    .el-button {
      padding-left: 0;

      i {
        margin-right: 4px;
      }
    }
  }
}

.config-key-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-tag {
    background-color: #ecf5ff;
    color: #409EFF;
    border-color: #d9ecff;
    font-weight: 500;
  }

  .copy-icon {
    color: #909399;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.2s;

    &:hover {
      color: #409EFF;
    }
  }
}

.json-editor {
  border: 1px solid #EBEEF5;
  border-radius: 10px;
  overflow: hidden;

  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #EBEEF5;
    background-color: #f5f7fa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }

      .el-icon-question {
        color: #909399;
        cursor: help;
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .panel-content {
    padding: 16px;
    min-height: 200px;
    max-height: 400px;
    overflow: auto;
    background: #fff;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(144, 147, 153, 0.3);

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .el-textarea {
      ::v-deep .el-textarea__inner {
        font-family: Monaco, Menlo, Consolas, monospace;
        font-size: 13px;
        line-height: 1.6;
        padding: 12px;
        border: none;
        background: none;

        &:focus {
          box-shadow: none;
        }
      }
    }
  }
}

::v-deep .el-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
    
    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      
      .el-dialog__close {
        font-size: 18px;
        color: #909399;
        font-weight: bold;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    background: #f8f9fb;
    flex: 1;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    
    .el-button {
      padding: 9px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      & + .el-button {
        margin-left: 12px;
      }

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);
        
        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

::v-deep .vjs-tree {
  font-family: Monaco, Menlo, Consolas, monospace;
  font-size: 13px;
  line-height: 1.5;

  .vjs-key {
    color: #881391;
  }

  .vjs-value {
    &.vjs-value-string {
      color: #c41a16;
    }

    &.vjs-value-number {
      color: #1c00cf;
    }

    &.vjs-value-boolean {
      color: #0b7500;
    }

    &.vjs-value-null {
      color: #808080;
    }
  }
}
</style>