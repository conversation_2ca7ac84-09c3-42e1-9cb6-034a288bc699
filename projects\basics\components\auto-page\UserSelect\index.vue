<template>
    <div>
        <el-button type="primary" icon="el-icon-circle-plus-outline" @click="dialogVisible = true">{{
            componentName
        }}</el-button>

        <div ref="tags" class="tags" style="line-height: 35px;" v-if="items.length" :class="{ 'expand': isExpand }">
            <el-tag style="margin-left: 5px;;margin-right: 5px;" size="small" closable v-for="(item, index) in items"
                :key="index" @close="tagClose(index)">{{ item.name }}</el-tag>
        </div>
        <div v-if="showEx" class="ex" v-text="isExpand ? '收起' : '展开'" @click="isExpand = !isExpand">
        </div>
        <el-dialog :title="componentName" :visible.sync="dialogVisible" width="80%" top="25vh" :append-to-body="true">

            <transition name="fade">
                <cascaderPanel v-if="dialogVisible" :type="componentType" @change="change" :values="items">
                </cascaderPanel>
            </transition>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="items = tempItems; emitValue(); dialogVisible = false">确
                    定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import cascaderPanel from './cascaderPanel.vue';
export default {
    components: { cascaderPanel },
    props: {
        componentType: {
            type: String,
            default: 'account'
        },
        componentName: {
            type: String,
            default: '人员选择'
        }
    },
    data() {
        return {
            isExpand: false,
            showEx: false,
            items: [],
            tempItems: [],
            dialogVisible: false,
        }
    },
    mounted() {
        setTimeout(() => {
            this.showExpand()
        }, 200);
    },
    methods: {
        tagClose(index) {
            this.items.splice(index, 1)
            this.emitValue()
        },
        emitValue() {
            this.$emit('value', this.items)
        },
        showExpand() {
            let tags = this.$refs.tags

            if (tags) {
                let scrollHeight = tags.scrollHeight
                let clientHeight = tags.clientHeight
                this.showEx = scrollHeight > clientHeight
            } else {
                this.showEx = false
            }
        },

        change(value) {
            this.tempItems = value
        }

    }
}
</script>
<style lang="scss" scoped>
.tags {
    border-radius: 0.20833vw;
    border: 1px solid #ddd;
    width: 100%;
    margin-top: 5px;
    padding-bottom: 5px;
    max-height: 160px;
    overflow-y: scroll;

    &:hover {
        border-color: #409EFF;
    }
}

.ex {
    color: rgb(76, 92, 130);
    font-size: 14px;
    line-height: 20px;
    text-align: right;
    cursor: pointer;
}

.tags.expand {
    max-height: max-content;
}
</style>