<template>
  <div class="connector-container">
    <!-- 顶部操作栏 -->
    <div class="action-bar">
      <div class="left">
        <el-input v-model="searchKeyword" placeholder="搜索连接器名称" prefix-icon="el-icon-search" clearable
          class="search-input" />
      </div>
      <div class="right">
        <el-button type="primary" icon="el-icon-plus" @click="$router.push('/sys/connector/add')">
          添加连接器
        </el-button>
      </div>
    </div>

    <!-- 连接器卡片列表 -->
    <el-row :gutter="24">
      <el-col :span="6" v-for="connector in filteredConnectors" :key="connector.id">
        <el-card class="connector-card" shadow="hover">
          <div class="connector-header">
            <div class="connector-info">
              <div class="connector-icon" :style="{ backgroundColor: connector.iconColor }">
                <i :class="connector.icon || 'el-icon-connection'"></i>
              </div>
              <div class="connector-meta">
                <h3>{{ connector.name }}</h3>
                <div class="connector-tags">
                  <el-tag :type="getPluginTagType(connector.pluginBeanName)" size="small" effect="dark">
                    {{ getPluginName(connector.pluginBeanName) }}
                  </el-tag>
                  <el-tag :type="connector.enabled ? 'success' : 'info'" size="small" class="status-tag">
                    {{ connector.enabled ? '已启用' : '已禁用' }}
                  </el-tag>
                </div>
              </div>
            </div>
            <el-dropdown trigger="click" @command="handleCommand($event, connector)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">
                  <i class="el-icon-edit"></i> 编辑配置
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <i class="el-icon-delete"></i> 删除连接器
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>

          <div class="connector-content">
            <p class="description">{{ connector.description || '暂无描述' }}</p>
            <div class="connector-details">
              <div class="detail-item">
                <i class="el-icon-time"></i>
                <span>{{ formatTime(connector.updateTime) }}</span>
              </div>
              <div class="detail-item">
                <i class="el-icon-connection"></i>
                <span>{{ connector.code }}</span>
              </div>
            </div>
          </div>

          <div class="connector-footer">
            <el-switch v-model="connector.enabled" @change="handleToggleStatus(connector)" active-text="启用"
              inactive-text="禁用"></el-switch>
            <div class="footer-actions">
              <el-button type="text" @click="handleEditConnector(connector)">
                配置
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getConnectorList, updateConnectorStatus, deleteConnector } from '@system/api/sys/connector'
import { getPlugins } from '@system/api/sys/auth'

export default {
  name: 'ConnectorManagement',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      connectorList: [],
      plugins: []
    }
  },
  computed: {
    filteredConnectors() {
      return this.connectorList.filter(connector => {
        return connector.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      })
    }
  },
  created() {
    this.loadPlugins()
    this.getConnectorList()
  },
  methods: {
    async getConnectorList() {
      this.loading = true
      try {
        const data = await getConnectorList()
        this.connectorList = data?.records || []
      } catch (error) {
        this.$message.error('获取连接器列表失败')
      }
      this.loading = false
    },
    async loadPlugins() {
      try {
        const data = await getPlugins()
        this.plugins = data
      } catch (error) {
        this.$message.error('获取插件列表失败')
      }
    },
    getPluginName(pluginBeanName) {
      const plugin = this.plugins.find(p => p.pluginBeanName === pluginBeanName)
      return plugin ? plugin.name : '未知插件'
    },
    getPluginTagType(pluginBeanName) {
      const typeMap = {
        'OAuthPlugin': 'primary',
        'WeChatOfficialAuthPlugin': 'success',
        'WeChatMiniAuthPlugin': 'success',
        'DingTalkAuthPlugin': 'warning'
      }
      return typeMap[pluginBeanName] || 'primary'
    },
    formatTime(time) {
      return time || '-'
    },
    handleCommand(command, connector) {
      switch (command) {
        case 'edit':
          this.handleEditConnector(connector)
          break
        case 'delete':
          this.handleDeleteConnector(connector)
          break
      }
    },
    handleEditConnector(connector) {
      this.$router.push(`/sys/connector/edit/${connector.id}`)
    },
    async handleDeleteConnector(connector) {
      try {
        await this.$confirm('确认删除该连接器吗？', '提示', {
          type: 'warning'
        })
        await deleteConnector(connector.id)
        this.$message.success('删除成功')
        await this.getConnectorList()
      } catch (err) {
        if (err !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    async handleToggleStatus(connector) {
      try {
        await updateConnectorStatus(connector.id, connector.enabled)
        this.$message.success(`${connector.enabled ? '启用' : '禁用'}成功`)
      } catch (err) {
        connector.enabled = !connector.enabled
        this.$message.error('操作失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.connector-container {
  padding: 24px;
  background: inherit;
  .action-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 4px;

    .left {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .search-input {
        width: 280px;

        ::v-deep .el-input__inner {
          border-radius: 20px;
          padding-left: 40px;
        }

        ::v-deep .el-input__prefix {
          left: 15px;
        }
      }
    }

    .right {
      .el-button {
        border-radius: 20px;
        padding: 10px 24px;

        i {
          margin-right: 6px;
        }
      }
    }
  }

  .connector-card {
    margin-bottom: 20px;
    border-radius: 8px;
    transition: all 0.3s;
    border: none;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    }

    .connector-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .connector-info {
        display: flex;
        gap: 12px;

        .connector-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
          background-color: #409EFF;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          .iconfont {
            font-size: 28px !important;
          }
        }

        .connector-meta {
          h3 {
            margin: 0 0 6px;
            font-size: 14px;
            font-weight: normal;
            color: #303133;
          }

          .connector-tags {
            display: flex;
            gap: 6px;

            .el-tag {
              border-radius: 10px;
              padding: 0 8px;
              font-size: 11px;
            }
          }
        }
      }

      .el-icon-more {
        padding: 6px;
        cursor: pointer;
        color: #909399;
        border-radius: 4px;
      }
    }

    .connector-content {
      margin-bottom: 12px;

      .description {
        height: 36px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #909399;
        line-height: 1.5;
        margin: 0 0 12px;
      }

      .connector-details {
        display: flex;
        gap: 16px;

        .detail-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #909399;

          i {
            font-size: 12px;
          }
        }
      }
    }

    .connector-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;

      .footer-actions {
        display: flex;
        gap: 8px;

        .el-button {
          font-size: 12px;
          padding: 6px 12px;
        }
      }
    }
  }
}
</style>