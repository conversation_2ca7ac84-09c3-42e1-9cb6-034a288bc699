import axios from 'axios'

// const baseURL = 'http://192.168.1.189'
const baseURL = '/prod-chat'

export function stopTask(task_id, appkey) {
    return axios.post(`${baseURL}/v1/chat-messages/${task_id}/stop`, {
        "user": "abc-123",
    }, {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + appkey
            // Authorization: 'Bearer ' + 'app-AtpJPZMXO13OrlNagu2dNclH'
        }
    })
}


export function sendAppMessage(query, appId, appkey, inputs, { onNext, onComplete, onError }) {
    return axios.post(`${baseURL}/console/api/installed-apps/${appId}/chat-messages`, {
        query: query,
        "inputs": {
            ...inputs
        },
        "response_mode": "streaming",
        "conversation_id": "",
    }, {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + appkey
        },
        onDownloadProgress: (res) => {
            const responseText = res.event.target?.responseText || ''
            const lines = responseText.split('\n')
            let content = ''

            for (const line of lines) {
                if (!line.trim()) continue
                if (line.startsWith('data: ')) {
                    let data = line.slice(6).trim()

                    data = JSON.parse(data)

                    if (data.event == 'message') {
                        content += data.answer
                        onNext && onNext(content, { task_id: data.task_id })
                    } else if (data.event == 'message_end') {
                        onComplete && onComplete(content, data?.metadata?.retriever_resources)
                    }
                }
            }
        }
    }).catch(err => {
        onError && onError(err)
        // console.log('pppdddddoop====', err);
    })
}


export function sendMessage(query, appkey, inputs, { onNext, onComplete, onError }) {
    return axios.post(`${baseURL}/v1/chat-messages`, {
        query: query,
        "inputs": {
            ...inputs
        },
        "response_mode": "streaming",
        "conversation_id": "",
        "user": "abc-123",
    }, {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + appkey
            // Authorization: 'Bearer ' + 'app-AtpJPZMXO13OrlNagu2dNclH'
        },
        onDownloadProgress: (res) => {
            const responseText = res.event.target?.responseText || ''
            const lines = responseText.split('\n')
            let content = ''

            for (const line of lines) {
                if (!line.trim()) continue
                if (line.startsWith('data: ')) {
                    let data = line.slice(6).trim()

                    data = JSON.parse(data)

                    if (data.event == 'message') {
                        content += data.answer
                        onNext && onNext(content, { task_id: data.task_id })
                    } else if (data.event == 'message_end') {
                        onComplete && onComplete(content)
                    }
                }
            }
        }
    }).catch(err => {
        onError && onError(err)
        // console.log('pppdddddoop====', err);
    })
}