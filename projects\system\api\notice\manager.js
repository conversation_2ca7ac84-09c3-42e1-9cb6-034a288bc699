import request from '@/utils/request'

const api = '/notice'
/**
 * 获取消息日志列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getMessageLogList(params) {
  // 如果未设置排序，则默认按发送时间降序排序
  if (!params.orders) {
    params['orders[sendDateTime]'] = 'desc'
  }
  return request({
    url: `${api}/notice/message/log/list`,
    method: 'get',
    params
  })
}

/**
 * 获取消息提供者列表
 * @returns {Promise}
 */
export function getProviderList(params) {
  return request({
    url: `${api}/notice/provider/list`,
    method: 'get',
    params
  })
}

/**
 * 删除消息提供者
 * @param {String|Number} id 提供者ID
 * @returns {Promise}
 */
export function deleteProvider(id) {
  return request({
    url: `${api}/notice/provider/${id}`,
    method: 'delete'
  })
}

/**
 * 获取消息提供者详情
 * @param {String|Number} id 提供者ID
 * @returns {Promise}
 */
export function getProviderDetail(id) {
  return request({
    url: `${api}/notice/provider`,
    method: 'get',
    params: { id }
  })
}

/**
 * 创建消息提供者
 * @param {Object} data 提供者数据
 * @returns {Promise}
 */
export function createProvider(data) {
  return request({
    url: `${api}/notice/provider`,
    method: 'post',
    data
  })
}

/**
 * 更新消息提供者
 * @param {Object} data 提供者数据
 * @returns {Promise}
 */
export function updateProvider(data) {
  return request({
    url: `${api}/notice/provider`,
    method: 'put',
    data
  })
}

/**
 * 更新提供者状态
 * @param {String|Number} id 提供者ID
 * @param {Boolean} enabled 是否启用
 * @returns {Promise}
 */
export function updateProviderStatus(id, enabled) {
  return request({
    url: `${api}/notice/provider/status`,
    method: 'put',
    data: { id, enabled }
  })
}

/**
 * 获取插件列表
 * @returns {Promise}
 */
export function getPlugins() {
  return request({
    url: `${api}/actuator/plugin`,
    method: 'get'
  })
}

/**
 * 获取消息模板列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getTemplateList(params) {
  return request({
    url: `${api}/notice/template/list`,
    method: 'get',
    params
  })
}

/**
 * 创建消息模板
 * @param {Object} data 模板数据
 * @returns {Promise}
 */
export function createTemplate(data) {
  return request({
    url: `${api}/notice/template`,
    method: 'post',
    data
  })
}

/**
 * 更新消息模板
 * @param {Object} data 模板数据
 * @returns {Promise}
 */
export function updateTemplate(data) {
  return request({
    url: `${api}/notice/template`,
    method: 'put',
    data
  })
}

/**
 * 删除消息模板
 * @param {String|Number} id 模板ID
 * @returns {Promise}
 */
export function deleteTemplate(id) {
  return request({
    url: `${api}/notice/template/${id}`,
    method: 'delete'
  })
}

/**
 * 获取消息模板详情
 * @param {String|Number} id 模板ID
 * @returns {Promise}
 */
export function getTemplateDetail(id) {
  return request({
    url: `${api}/notice/template`,
    method: 'get',
    params: { id }
  })
}

/**
 * 提交消息推送任务
 * @param {Object} data 消息推送数据
 * @returns {Promise}
 */
export function commitMessagePush(data) {
  return request({
    url: `${api}/notice/task/push/commit`,
    method: 'post',
    data
  })
}

/**
 * 获取预设任务列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getPresetTaskList(params) {
  return request({
    url: `${api}/notice/preset/task/list`,
    method: 'get',
    params
  })
}

/**
 * 创建预设任务
 * @param {Object} data 任务数据
 * @returns {Promise}
 */
export function createPresetTask(data) {
  return request({
    url: `${api}/notice/preset/task`,
    method: 'post',
    data
  })
}

/**
 * 更新预设任务
 * @param {Object} data 任务数据
 * @returns {Promise}
 */
export function updatePresetTask(data) {
  return request({
    url: `${api}/notice/preset/task`,
    method: 'put',
    data
  })
}

/**
 * 删除预设任务
 * @param {String|Number} id 任务ID
 * @returns {Promise}
 */
export function deletePresetTask(id) {
  return request({
    url: `${api}/notice/preset/task/${id}`,
    method: 'delete'
  })
}

/**
 * 获取预设任务详情
 * @param {String|Number} id 任务ID
 * @returns {Promise}
 */
export function getPresetTaskDetail(id) {
  return request({
    url: `${api}/notice/preset/task`,
    method: 'get',
    params: { id }
  })
}

/**
 * 执行预设任务
 * @param {String} taskCode 任务代码
 * @returns {Promise}
 */
export function executePresetTask(taskCode) {
  return request({
    url: `${api}/notice/preset/task/preset/execute/${taskCode}`,
    method: 'post'
  })
}

/**
 * 获取预设任务分组名称列表
 * @returns {Promise}
 */
export function getPresetTaskGroupNames() {
  return request({
    url: `${api}/notice/preset/task/groupNames?groupName=groupName`,
    method: 'get',
  })
}

/**
 * 获取模板分组名称列表
 * @returns {Promise}
 */
export function getTemplateGroupNames() {
  return request({
    url: `${api}/notice/template/groupNames`,
    method: 'get',
    params: {
      groupName: 'groupName'
    }
  })
}
