export default {
  api: 'sys/resources',
  search: {
    isShow: true,
    showReset: true
  },

  table: {
    isHasChildren: true
  },
  formRule: [
    {
      type: "treeSelect",
      field: "parentId",
      title: "上级菜单",
      //初始化设置默认值，查询parentId为0的列表
      isSearch: false,
      isTable: false,
      isScope: false,
      options: [],
      value: "",
      props: {
        multiple: false,
        placeholder: "请选择上级菜单",
        readonly: false,
        clearable: true
      },
      // validate: [
      //   {trigger: "change",required: true,message: "请选择"}
      // ],
      col: { md: { span: 15 } },
    },
    {
      type: "radio",
      field: "type",
      title: "菜单类型",
      isSearch: false,
      options: [
        { value: "APP", label: "应用", disabled: false },
        { value: "DIR", label: "目录", disabled: false },
        { value: "MENU", label: "菜单", disabled: false },
        { value: "BUTTON", label: "按钮", disabled: false },
      ],
      isTable: true,
      col: { md: { span: 15 } },
      props: {
        placeholder: "请选择权限标识",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change', required: true, message: '请选择'
        }
      ],
    },
    {
      type: "input",
      field: "name",
      className: "name-dom",
      title: "菜单名称",
      //如果要做搜索条件，置null否则就空字符串查询，数字0传值value:"0"
      value: null,
      isSearch: true,
      isSearchCol: { md: { span: 20 } },
      isSearchValidate: [],
      isTable: true,
      isScope: false,
      props: {
        placeholder: "请输入菜单名称",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'blur', required: true, message: '请输入'
        }
      ],
      col: { md: { span: 15 } },
    },
    {
      type: "svgIcon",
      field: "icon",
      title: "菜单图标",
      value: '',
      isSearch: false,
      isTable: true,
      isScope: false,
      isSvgIcon: true,
      props: {
        placeholder: "请输入图标名称",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change', required: false, message: '请选择图标'
        }
      ],
      col: { md: { span: 15 } }
    },
    {
      type: "input",
      field: "sort",
      title: "显示排序",
      value: '',
      isSearch: false,
      isTable: true,
      isScope: false,
      props: {
        placeholder: "请输入排序",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'blur', required: true, message: '请输入'
        }
      ],
      col: { md: { span: 15 } }
    },
    {
      type: "radio",
      field: "mode",
      title: "模式",
      isHidden: false,
      isTable: true,
      isSearch: true,
      isSearchCol: {
        md: {
          span: 12
        }
      },
      col: {
        md: {
          span: 18
        }
      },
      props: {
        disabled: false,
        readonly: false,
        multiple: false,
        placeholder: "请选择",
        notFoundText: "无匹配数据",
        placement: "bottom"
      },
      validate: [
        {
          trigger: 'blur',
          required: true,
          message: "状态不能为空"
        }
      ],
      options: [
        {
          value: 0,
          label: "普通",
          disabled: false
        },
        {
          value: 1,
          label: "外链",
          disabled: false
        },
        {
          value: 2,
          label: "表单",
          disabled: false
        }
      ]
    },
    {
      type: "input",
      field: "path",
      title: "路由地址",
      value: null,
      isSearch: false,
      isTable: true,
      isScope: false,
      isHidden: false,
      props: {
        placeholder: "请输入路由地址",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'blur', required: true, message: '请输入'
        }
      ],
      col: { md: { span: 15 } }
    },
    {
      type: "input",
      field: "component",
      title: "组件路径",
      value: null,
      isSearch: false,
      isTable: true,
      isScope: false,
      isHidden: false,
      props: {
        placeholder: "请输入组件路径",
        disabled: false,
        readonly: false,
        clearable: true
      },
      // validate:[
      //   {
      //     trigger:'blur',required: true,message:'请输入'
      //   }
      // ],
      col: { md: { span: 15 } }
    },
    {
      type: "input",
      field: "activeMenu",
      title: "激活菜单地址",
      value: null,
      isSearch: false,
      isTable: false,
      isScope: false,
      isHidden: false,
      props: {
        placeholder: "",
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: { md: { span: 15 } }
    },
    {
      type: "input",
      field: "perms",
      title: "权限标识",
      value: '',
      isSearch: false,
      isTable: true,
      isScope: false,
      isHidden: false,
      props: {
        placeholder: "请输入权限标识",
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: { md: { span: 15 } }
    },
    {
      type: "switch",
      field: "windowOpen",
      title: "新页签打开",
      isSearch: false,
      isTable: false,
      col: { md: { span: 15 } },
      value: null,
      props: {
        activeValue: true,
        inactiveValue: false,
        activeColor: "#13ce66",
        inactiveColor: "#ff4949",
        placeholder: "请选择显示状态",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change', required: true, message: '请选择'
        }
      ]
    },
    {
      type: "switch",
      field: "visible",
      title: "显示状态",
      isSearch: false,
      isTable: false,
      col: { md: { span: 15 } },
      value: null,
      props: {
        activeValue: true,
        inactiveValue: false,
        activeColor: "#13ce66",
        inactiveColor: "#ff4949",
        placeholder: "",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change', required: true, message: '请选择'
        }
      ]
    },
    {
      type: "switch",
      field: "status",
      title: "状态",
      isSearch: false,
      options: [
        { value: true, label: "启用", disabled: false },
        { value: false, label: "停用", disabled: false },
      ],
      isTable: true,
      col: { md: { span: 15 } },
      props: {
        activeValue: true,
        inactiveValue: false,
        activeColor: "#13ce66",
        inactiveColor: "#ff4949",
        placeholder: "请选择状态",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change', required: true, message: '请选择'
        }
      ]
    },
    {
      type: "radio",
      field: "cacheStatus",
      title: "是否缓存",
      isSearch: false,
      value: null,
      // isHidden: true,
      options: [
        { value: false, label: "否", disabled: false },
        { value: true, label: "是", disabled: false },
      ],
      isTable: false,
      col: { md: { span: 15 } },
      props: {
        placeholder: "请选择是否缓存",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change', required: true, message: '请选择'
        }
      ],
    },
    {
      type: "hidden",
      field: "json",
      title: "页面json",
      value: null,
      isSearch: false,
      isTable: false,
      isScope: true,
      props: {
        type: "textarea",
        placeholder: "请输入页面json",
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: { md: { span: 18 } }
    },
    {
      type: "input",
      field: "remark",
      title: "备注",
      value: '',
      isSearch: false,
      isTable: false,
      isScope: false,
      props: {
        type: "textarea",
        placeholder: "请输入备注",
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: { md: { span: 15 } }
    }
  ]
}
