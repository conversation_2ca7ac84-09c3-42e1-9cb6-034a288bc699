<template>
  <div class="security-node" :class="{ 'is-active': isActive,disabled: security.enable < 1 }" @click="$emit('click', security)">
    <div class="security-header">
      <div class="security-name">
        <i class="el-icon-lock"></i>
        <span class="method" :class="getMethodClass()">{{ security.method || 'ALL' }}</span>
        <span class="uri">{{ security.uri }}</span>
      </div>
      <div class="security-actions">
        <el-switch v-model="security.enable" style="transform: scale(0.7)" @change="$emit('enableChange')"></el-switch>
        <el-tooltip content="编辑安全配置" placement="top">
          <el-button type="text" icon="el-icon-edit" @click.stop="$emit('edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除安全配置" placement="top">
          <el-button type="text" icon="el-icon-delete" @click.stop="$emit('delete')"></el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="security-content">
      <div class="matcher-info">
        <span class="matcher-label">匹配规则:</span>
        <span class="matcher-value">{{ security.matcher || '无' }}</span>
      </div>
      <div v-if="security.arg && security.arg.length" class="arg-list">
        <div class="arg-label">匹配参数:</div>
        <div v-for="(arg, index) in security.arg" :key="index" class="arg-item">
          <span class="arg-value">{{ arg }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SecurityNode',
  props: {
    security: {
      type: Object,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getMethodClass() {
      if (!this.security.method) return '';
      
      const method = this.security.method.toUpperCase();
      switch (method) {
        case 'GET':
          return 'method-get';
        case 'POST':
          return 'method-post';
        case 'PUT':
          return 'method-put';
        case 'DELETE':
          return 'method-delete';
        case 'PATCH':
          return 'method-patch';
        default:
          return '';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.disabled {
  opacity: 0.4;
}
.security-node {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px;
  margin-bottom: 12px;
  border-left: 3px solid #F56C6C;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  &.is-active {
    background-color: #FEF0F0;
    
    &:before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      border-width: 0 18px 18px 0;
      border-style: solid;
      border-color: #F56C6C #fff;
    }
  }
  
  .security-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .security-name {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      flex-wrap: wrap;
      
      i {
        margin-right: 6px;
        color: #F56C6C;
        font-size: 16px;
      }
      
      .method {
        padding: 2px 6px;
        margin-right: 8px;
        border-radius: 4px;
        font-size: 12px;
        color: #fff;
        background-color: #909399;
        
        &.method-get {
          background-color: #409EFF;
        }
        
        &.method-post {
          background-color: #67C23A;
        }
        
        &.method-put {
          background-color: #E6A23C;
        }
        
        &.method-delete {
          background-color: #F56C6C;
        }
        
        &.method-patch {
          background-color: #9C27B0;
        }
      }
      
      .uri {
        flex: 1;
        word-break: break-all;
      }
    }
    
    .security-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .el-button {
        font-size: 16px;
        padding: 4px;
        
        &:hover {
          color: #409EFF;
        }
        
        &:last-child:hover {
          color: #F56C6C;
        }
      }
    }
  }
  
  .security-content {
    margin-bottom: 4px;
    
    .matcher-info {
      display: flex;
      margin-bottom: 6px;
      font-size: 13px;
      
      .matcher-label {
        color: #909399;
        min-width: 70px;
        margin-right: 4px;
      }
      
      .matcher-value {
        color: #303133;
        word-break: break-all;
        flex: 1;
      }
    }
    
    .arg-list {
      .arg-label {
        color: #909399;
        font-size: 13px;
        margin-bottom: 4px;
      }
      
      .arg-item {
        margin-left: 12px;
        margin-bottom: 2px;
        
        .arg-value {
          color: #303133;
          font-size: 13px;
          padding: 1px 6px;
          background-color: #F5F7FA;
          border-radius: 3px;
          display: inline-block;
        }
      }
    }
  }
}
</style> 