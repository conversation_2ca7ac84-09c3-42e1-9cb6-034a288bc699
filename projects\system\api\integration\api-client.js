import request from '@/utils/request'

const api = '/integration'

// 分页查询API客户端列表
export function getApiClientList(params) {
  return request({
    url: `${api}/api/client/list`,
    method: 'get',
    params
  })
}

// 创建API客户端
export function createApiClient(data) {
  return request({
    url: `${api}/api/client/create`,
    method: 'post',
    data
  })
}

// 更新API客户端状态
export function updateApiClientStatus(id, enabled) {
  return request({
    url: `${api}/api/client/${id}`,
    method: 'put',
    data: { enabled }
  })
}

// 重新生成客户端密钥
export function regenerateSecret(id) {
  return request({
    url: `${api}/api/client/${id}/regenerate`,
    method: 'put'
  })
}

// 删除API客户端
export function deleteApiClient(id) {
  return request({
    url: `${api}/api/client/${id}`,
    method: 'delete'
  })
}

// 获取客户端权限列表
export function getClientPermissions(clientId) {
  return request({
    url: `${api}/api/client/${clientId}/permissions`,
    method: 'get'
  })
}

// 绑定API客户端权限
export function bindClientPermissions(data) {
  return request({
    url: `${api}/api/client/bind`,
    method: 'post',
    data
  })
}

// 更新API客户端
export function updateApiClient(data) {
  return request({
    url: `${api}/api/client`,
    method: 'put',
    data: [data]  // 包装成数组，因为后端接收的是数组
  })
}

