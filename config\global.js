const dev = {
  SYSTEM: 'system',
  FILE: 'file',
  FORM: 'form',

}

const test = {
  SYSTEM: 'system',
  FILE: 'file',
  FORM: 'form',
}

const pro = {
  SYSTEM: 'system',
  FILE: 'file',
  FORM: 'form',
}


function getApi() {
  if (process.env.VUE_APP_ENV === 'development') {
    return dev
  } else if (process.env.VUE_APP_ENV === 'stage') {
    return test
  } else {
    return pro
  }
}

const api = getApi()

let requireContext = require.context('../projects', true, /\/config\/global\.js$/)
let globals = requireContext.keys()
  .filter(f => plugin_filefilter(f))
  .map(requireContext).map(g => g.default)

Object.assign(api, ...globals)

/** 全局变量在此处配置 */
export default api



