<template>
  <div class="notice-settings-view">
    <div class="settings-header">
      <h3>消息通知设置</h3>
      <p class="settings-desc">设置您想要接收的通知类型和接收方式</p>
    </div>

    <el-divider content-position="left">通知类型</el-divider>

    <div class="settings-section">
      <el-form ref="settingsForm" label-width="120px">
        <el-form-item label="系统通知">
          <el-switch v-model="settings.system.enabled"></el-switch>
          <div class="setting-desc">系统重要更新、安全提醒等</div>
          <div class="notification-methods" v-if="settings.system.enabled">
            <span class="method-label">接收方式：</span>
            <el-checkbox v-model="settings.system.web">站内</el-checkbox>
            <el-checkbox v-model="settings.system.email">邮件</el-checkbox>
            <el-checkbox v-model="settings.system.sms">短信</el-checkbox>
          </div>
        </el-form-item>

        <el-form-item label="回复通知">
          <el-switch v-model="settings.reply.enabled"></el-switch>
          <div class="setting-desc">其他用户回复您的内容时通知您</div>
          <div class="notification-methods" v-if="settings.reply.enabled">
            <span class="method-label">接收方式：</span>
            <el-checkbox v-model="settings.reply.web">站内</el-checkbox>
            <el-checkbox v-model="settings.reply.email">邮件</el-checkbox>
            <el-checkbox v-model="settings.reply.sms">短信</el-checkbox>
          </div>
        </el-form-item>

        <el-form-item label="@我的通知">
          <el-switch v-model="settings.mention.enabled"></el-switch>
          <div class="setting-desc">其他用户在内容中@提到您时通知您</div>
          <div class="notification-methods" v-if="settings.mention.enabled">
            <span class="method-label">接收方式：</span>
            <el-checkbox v-model="settings.mention.web">站内</el-checkbox>
            <el-checkbox v-model="settings.mention.email">邮件</el-checkbox>
            <el-checkbox v-model="settings.mention.sms">短信</el-checkbox>
          </div>
        </el-form-item>

        <el-form-item label="点赞通知">
          <el-switch v-model="settings.like.enabled"></el-switch>
          <div class="setting-desc">其他用户点赞您的内容时通知您</div>
          <div class="notification-methods" v-if="settings.like.enabled">
            <span class="method-label">接收方式：</span>
            <el-checkbox v-model="settings.like.web">站内</el-checkbox>
            <el-checkbox v-model="settings.like.email">邮件</el-checkbox>
            <el-checkbox v-model="settings.like.sms">短信</el-checkbox>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <el-divider content-position="left">接收时间</el-divider>

    <div class="settings-section">
      <el-form label-width="120px">
        <el-form-item label="接收时间段">
          <el-radio-group v-model="settings.receiveTime">
            <el-radio :label="'all'">全天接收</el-radio>
            <el-radio :label="'custom'">自定义时间段</el-radio>
          </el-radio-group>
          
          <div class="time-range" v-if="settings.receiveTime === 'custom'">
            <el-time-picker
              v-model="settings.startTime"
              placeholder="开始时间"
              format="HH:mm"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:59'
              }"
            ></el-time-picker>
            <span class="time-separator">至</span>
            <el-time-picker
              v-model="settings.endTime"
              placeholder="结束时间"
              format="HH:mm"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:59'
              }"
            ></el-time-picker>
          </div>
        </el-form-item>

        <el-form-item label="免打扰模式">
          <el-switch 
            v-model="settings.doNotDisturb"
            @change="handleDoNotDisturbChange"
          ></el-switch>
          <div class="setting-desc">开启后将暂停所有通知推送</div>
        </el-form-item>
      </el-form>
    </div>

    <el-divider content-position="left">联系方式</el-divider>

    <div class="settings-section">
      <el-form label-width="120px">
        <el-form-item label="邮箱地址">
          <el-input 
            v-model="settings.email" 
            placeholder="请输入邮箱地址"
            style="width: 300px;"
          ></el-input>
          <el-button 
            type="text" 
            size="small" 
            class="verify-btn"
            @click="handleVerifyEmail"
          >
            验证邮箱
          </el-button>
        </el-form-item>

        <el-form-item label="手机号码">
          <el-input 
            v-model="settings.phone" 
            placeholder="请输入手机号码"
            style="width: 300px;"
          ></el-input>
          <el-button 
            type="text" 
            size="small" 
            class="verify-btn"
            @click="handleVerifyPhone"
          >
            验证手机
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
      <el-button @click="resetSettings">重置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoticeSettingsView',
  data() {
    return {
      settings: {
        system: {
          enabled: true,
          web: true,
          email: false,
          sms: false
        },
        reply: {
          enabled: true,
          web: true,
          email: true,
          sms: false
        },
        mention: {
          enabled: true,
          web: true,
          email: true,
          sms: false
        },
        like: {
          enabled: true,
          web: true,
          email: false,
          sms: false
        },
        receiveTime: 'all',
        startTime: new Date(2024, 0, 1, 9, 0),
        endTime: new Date(2024, 0, 1, 22, 0),
        doNotDisturb: false,
        email: '<EMAIL>',
        phone: '13800138000'
      },
      originalSettings: null
    }
  },
  created() {
    // 保存初始设置，用于重置
    this.originalSettings = JSON.parse(JSON.stringify(this.settings))
  },
  methods: {
    handleDoNotDisturbChange(value) {
      if (value) {
        this.$confirm('开启免打扰模式后，您将不会收到任何通知，确定要开启吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 用户确认开启
        }).catch(() => {
          // 用户取消，恢复开关状态
          this.settings.doNotDisturb = false
        })
      }
    },
    
    handleVerifyEmail() {
      if (!this.settings.email) {
        this.$message.warning('请先填写邮箱地址')
        return
      }
      
      // 模拟发送验证邮件
      this.$message.success('验证邮件已发送到您的邮箱，请查收')
    },
    
    handleVerifyPhone() {
      if (!this.settings.phone) {
        this.$message.warning('请先填写手机号码')
        return
      }
      
      // 模拟发送验证短信
      this.$message.success('验证码已发送到您的手机，请查收')
    },
    
    saveSettings() {
      // 模拟保存设置
      this.$message.success('设置已保存')
      
      // 更新初始设置
      this.originalSettings = JSON.parse(JSON.stringify(this.settings))
    },
    
    resetSettings() {
      this.$confirm('确定要重置所有设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置为初始值
        this.settings = JSON.parse(JSON.stringify(this.originalSettings))
        this.$message.success('设置已重置')
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-settings-view {
  flex: 1;
  overflow: auto;
  padding: 20px;
  scrollbar-width: thin; /* Firefox */
  
  /* WebKit (Chrome/Safari/Edge) */
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 2px;
  }
  
  .settings-header {
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      color: #303133;
      margin: 0 0 8px 0;
    }
    
    .settings-desc {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .settings-section {
    margin-bottom: 20px;
    
    .setting-desc {
      margin-top: 5px;
      color: #909399;
      font-size: 12px;
    }
    
    .notification-methods {
      margin-top: 10px;
      display: flex;
      align-items: center;
      
      .method-label {
        margin-right: 10px;
        color: #606266;
        font-size: 13px;
      }
      
      .el-checkbox {
        margin-right: 15px;
      }
    }
    
    .time-range {
      margin-top: 10px;
      display: flex;
      align-items: center;
      
      .time-separator {
        margin: 0 10px;
        color: #606266;
      }
    }
    
    .verify-btn {
      margin-left: 10px;
    }
  }
  
  .settings-actions {
    margin-top: 30px;
    border-top: 1px solid #ebeef5;
    padding-top: 20px;
  }
}
</style> 