<template>
  <el-form :model="form" size="small" :rules="rules" ref="form" label-position="top">
    <el-form-item label="触发方式"> 
      <el-radio-group v-model="form.triggerType">
        <el-radio label="fixRate" >间隔触发</el-radio>
        <el-radio label="cron">cron</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="间隔时间" v-if="form.triggerType === 'fixRate'" :rules="{ required: true, message: '请输入cron', trigger: 'blur' }"> 
      <el-input v-model.number="fixRate" placeholder="输入间隔时间" type="number" >
        <template slot="append">秒</template>
      </el-input>
    </el-form-item>
    <el-form-item label="cron" v-if="form.triggerType === 'cron'" :rules="{ required: true, message: '请输入cron', trigger: 'blur' }"> 
      <el-input v-model="form.cron" placeholder="输入cron" />
    </el-form-item>
  </el-form>
</template>
<script>
import { isEqual } from 'element-ui/src/utils/util';
export default {
  name: 'ScheduledTrigger',
  data() {
    return {
      rules: {},
      form: {
        triggerType: 'fixRate',
        fixRate: 5000
      }
    }
  },
  methods: {
    
  },
  props: {
    properties: {
        type: Object,
        default: () => ({})
    },
    node: {
        type: Object,
        default: () => ({})
    }
  },
  computed: { 
    fixRate: {
      get() {
        return this.form.fixRate / 1000
      },
      set(val) {
        this.$set(this.form, 'fixRate', val * 1000)
      }
    }
  },
  created() {
    
  },
  watch: {
    properties: {
        handler(newVal) {
            if (!isEqual(newVal, this.form)) {
                this.form = { ...this.form, ...newVal }
            }
        },
        immediate: true,
        deep: true
    },
  },
  methods: {
    getProperties() {
      if (this.form.triggerType === 'fixRate') {
        delete this.form.cron
      } else {
        delete this.form.fixRate
      }
      return this.form
    },
    async validate() {
        return new Promise(resolve => {
            this.$refs.form.validate(valid => {
                resolve(valid)
            })
        })
    }
  }
}
</script>