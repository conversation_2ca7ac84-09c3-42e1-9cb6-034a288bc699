const requireContext = require.context('./projects', true, /loadview.config\.js$/)
const globals = requireContext.keys()
  .filter(f => plugin_filefilter(f))
  .map(requireContext).map(g => g.default)
export default function (view) {
  try {



    for (let index = 0; index < globals.length; index++) {
      const fun = globals[index]
      const importFun = view && fun(view)

      if (importFun != null) {
        return importFun
      }

    }

    throw `${view}未找到loadview路由配置`
  } catch (error) {
    throw error;
  }
}
