<template>
  <el-card shadow="always">
    <div class="label" slot="header">
      <label>执行记录</label>
      <div v-show="value">
        <el-date-picker
          v-model="requestParams.ranges.createTime"
          type="datetimerange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          clearable
          value-format="yyyy-MM-dd HH:mm:ss"
          class="search-item"
          @change="refreshList"
        >
        </el-date-picker>
        <el-select v-model="requestParams.success" class="search-item" clearable placeholder="执行结果" @change="refreshList">
          <el-option :value="true" label="成功"></el-option>
          <el-option :value="false" label="失败"></el-option>
        </el-select>
        <el-button class="search-item" type="primary" size="mini" @click="toggleView" icon="el-icon-refresh-left" circle></el-button>
      </div>
    </div>
    <div v-show="!showChart">
      <!-- 表格视图 -->
      <el-table :data="records" border style="height: 57vh; overflow-y: auto;" v-loading="loading">
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-descriptions :column="1">
              <el-descriptions-item label="参数">{{props.row.parameter}}</el-descriptions-item>
              <el-descriptions-item v-if="props.row.success && props.row.result" label="响应">{{props.row.result}}</el-descriptions-item>
              <el-descriptions-item v-if="!props.row.success" label="错误">{{props.row.error}}</el-descriptions-item>
          </el-descriptions>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="运行时间" />
        <el-table-column prop="duration" label="耗时(ms)" />
        <el-table-column prop="success" label="执行结果">
          <template slot-scope="scope">
            <el-tag size="medium" :type="scope.row.success ? 'success' : 'danger'">{{ scope.row.success ? '成功' : '失败' }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <!-- 图表视图 -->
      <div style="display: flex; justify-content: flex-end; margin-top: 10px;">
        <el-pagination :current-page.sync="requestParams.current" :page-sizes="[10, 20, 50, 100]" :page-size.sync="requestParams.size" @size-change="refreshList" @current-change="refreshList"
          layout="total, sizes, prev, pager, next, jumper" :total="total" />
      </div>
    </div>
    <div v-show="showChart" ref="chartContainer" style="height: 57vh; width: 100%"></div>
  </el-card>
</template>
<script>
import api from "@system/api/scheduled";
import * as echarts from 'echarts';

export default {
  name: 'recordList',
  props: {
    value: {
      type: [String, Number]
    }
  },
  data() {
    return {
      records: [],
      loading: false,
      total: 0,
      requestParams: {
        current: 1,
        size: 10,
        ranges: {},
        orders: {
          createTime: 'desc'
        }
      },
      showChart: false,
      chart: null
    }
  },
  watch: {
    value: {
      handler(val) {
        this.refreshList()
      },
      immediate: true
    }
  },
  methods: {
    refreshList() {
      if (this.value) {
        this.loading = true
        this.requestParams.taskId = this.value
        api.record.list(this.requestParams).then(res => {
          this.total = res.total
          this.records = res.records
          this.loading = false
          if (this.showChart) {
            this.$nextTick(() => {
              this.initChart()
            })
          }
        }).catch(e => {
          this.loading = false
        })
      } else {
        this.records = []
        this.total = 0
      }
    },
    toggleView() {
      this.showChart = !this.showChart
      if (this.showChart) {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    },
    initChart() {
      if (this.chart) {
        this.chart.dispose()
      }
      
      this.chart = echarts.init(this.$refs.chartContainer)
      
      const data = this.records.sort((a, b) => new Date(a.createTime) - new Date(b.createTime))

      const xAxisData = data.map(item => item.createTime)
      .map(item => {
        const date = new Date(item)
        return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}`
      })

      const option = {
        title: {
          text: '任务执行时长趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const {createTime, value, success} = params[0].data
            return `时间：${createTime}<br/>
                    耗时：${value/1000} s<br/>
                    状态：${success ? '成功' : '失败'}`
          }
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '耗时(ms)'
        },
        series: [{
          data: data.map(item => ({
            value: item.duration,
            itemStyle: {
              color: item.success ? '#67C23A' : '#F56C6C'
            },
            createTime: item.createTime,
            success: item.success
          })),
          type: 'line',
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 2
          }
        }]
      }
      
      this.chart.setOption(option)
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  }
}
</script>

<style lang="scss" scoped>
.label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-item {
  margin-left: 20px;
}
</style>