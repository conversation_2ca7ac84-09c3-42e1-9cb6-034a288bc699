// 定义 Worklet 脚本
export default `class MyAudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super()
    this.port.onmessage = (event) => {
      console.warn(event.data);
    }
  }
  process(inputs, outputs, parameters) {
    this.port.postMessage({
      type: 'AudioProcessor',
      process: { inputs, outputs, parameters }
    })
    return true
  }
}
registerProcessor('my-audio-processor', MyAudioProcessor)`
