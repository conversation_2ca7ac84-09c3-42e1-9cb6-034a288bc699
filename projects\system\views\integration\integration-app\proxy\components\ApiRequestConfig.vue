<template>
  <div class="api-request-config">
    <div class="section-header">
        <h4 class="section-title">API访问控制</h4>
        <div class="header-controls">
          <!-- 默认策略选择 -->
          <div class="default-policy-section">
<!--            <span class="policy-label">默认访问策略：</span>-->
            <el-radio-group v-model="defaultPolicy" size="mini" @change="handleDefaultPolicyChange">
              <el-radio-button label="ALLOW_ALL">
                <i class="el-icon-unlock"></i>
                允许所有（宽松模式）
              </el-radio-button>
              <el-radio-button label="DENY_ALL">
                <i class="el-icon-lock"></i>
                拒绝所有（严格模式）
              </el-radio-button>
            </el-radio-group>
          </div>
          <div class="header-actions">
            <el-button
              v-if="defaultPolicy === 'DENY_ALL'"
              type="success"
              size="mini"
              icon="el-icon-check"
              @click="addAllowRule"
              class="mac-style-btn allow-rule-btn"
            >
              添加允许规则
            </el-button>
            <el-button
              v-if="defaultPolicy === 'ALLOW_ALL'"
              type="danger"
              size="mini"
              icon="el-icon-close"
              @click="addDenyRule"
              class="mac-style-btn deny-rule-btn"
            >
              添加拒绝规则
            </el-button>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="addCustomRule"
              class="mac-style-btn add-rule-btn"
            >
              自定义规则
            </el-button>
            <el-button
              type="success"
              size="mini"
              icon="el-icon-refresh"
              @click="refreshApiList"
              :loading="loading"
              class="mac-style-btn refresh-btn"
            >
              刷新
            </el-button>
          </div>
        </div>
      </div>

      <!-- 策略说明 -->
      <div class="policy-explanation">
        <div class="explanation-content">
          <div class="current-policy">
            <i :class="defaultPolicy === 'ALLOW_ALL' ? 'el-icon-unlock' : 'el-icon-lock'"></i>
            <span class="policy-text">
              当前策略：{{ defaultPolicy === 'ALLOW_ALL' ? '允许所有请求访问' : '拒绝所有请求访问' }}
            </span>
          </div>
          <div class="exception-note">
            <i class="el-icon-info"></i>
            <span>
              {{ defaultPolicy === 'ALLOW_ALL' ? '下方例外规则中的请求将被拒绝访问' : '下方例外规则中的请求将被允许访问' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 例外规则列表 -->
      <div class="exception-rules-section">
        <div class="section-title">
          <h5>例外规则列表</h5>
          <span class="rule-count">（{{ apiEndpoints.length }} 条规则）</span>
        </div>

        <div class="api-list" v-if="apiEndpoints.length > 0">
          <div class="list-header">
            <div class="header-item">规则信息</div>
            <div class="header-item">请求方法</div>
            <div class="header-item">例外动作</div>
            <div class="header-item">状态</div>
            <div class="header-item">操作</div>
          </div>

        <div class="api-items">
          <div
            v-for="api in apiEndpoints"
            :key="api.id"
            class="api-item"
            :class="{ 'active': selectedApi === api.id }"
            @click="selectApi(api.id)"
          >
            <div class="api-info">
              <div class="api-path">
                <span class="path-text">{{ api.url }}</span>
                <el-tag size="mini" type="info" v-if="api.name">{{ api.name }}</el-tag>
                <el-tag size="mini" :type="api.isAntPath ? 'warning' : 'primary'" v-if="api.isAntPath">
                  Ant路径
                </el-tag>
              </div>
              <div class="api-description">{{ api.description || '暂无描述' }}</div>
              <div class="api-remark" v-if="api.remark">
                <span class="remark-label">备注：</span>
                <span class="remark-text">{{ api.remark }}</span>
              </div>
            </div>

            <div class="api-method">
              <el-tag
                size="small"
                :type="getMethodType(api.method)"
                class="method-tag"
              >
                {{ api.method }}
              </el-tag>
            </div>

            <div class="api-type">
              <el-tag
                size="mini"
                :type="getExceptionActionType(api)"
              >
                {{ getExceptionActionText(api) }}
              </el-tag>
            </div>

            <div class="api-status">
              <el-tag
                size="mini"
                :type="api.enabled ? 'success' : 'danger'"
              >
                {{ api.enabled ? '启用' : '禁用' }}
              </el-tag>
            </div>

            <div class="api-actions">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-view"
                @click.stop="viewApiDetail(api)"
                title="查看详情"
              />
              <el-button
                type="text"
                size="mini"
                icon="el-icon-edit"
                @click.stop="editApi(api)"
                title="编辑"
              />
              <el-dropdown @command="handleApiCommand" trigger="click">
                <el-button type="text" size="mini" icon="el-icon-more" @click.stop />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{action: 'toggle', api}">
                    {{ api.enabled ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'copy', api}">
                    复制规则
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', api}" divided>
                    删除规则
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <i class="el-icon-document-remove"></i>
          <p>暂无例外规则</p>
          <p class="empty-desc">
            {{ defaultPolicy === 'ALLOW_ALL' ? '所有请求默认允许访问，可添加拒绝规则进行限制' : '所有请求默认拒绝访问，可添加允许规则进行开放' }}
          </p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>

      <!-- 访问控制说明 -->
      <div class="access-control-info">
        <h5>访问控制机制说明</h5>
        <div class="info-content">
          <div class="info-item">
            <span class="label">工作原理：</span>
            <span>系统首先应用默认策略，然后检查例外规则进行覆盖</span>
          </div>
          <div class="info-item">
            <span class="label">规则优先级：</span>
            <span>例外规则 > 默认策略，匹配到例外规则时忽略默认策略</span>
          </div>
          <div class="info-item">
            <span class="label">路径匹配：</span>
            <span>支持精确匹配和 Ant 风格通配符（如 /api/users/* 匹配 /api/users/123）</span>
          </div>
          <div class="info-note">
            <i class="el-icon-warning-outline"></i>
            <span>建议：生产环境使用严格模式（拒绝所有），开发环境可使用宽松模式（允许所有）</span>
          </div>
        </div>
      </div>

    <!-- 例外规则对话框 -->
    <el-dialog
      :title="getDialogTitle()"
      :visible.sync="apiDialogVisible"
      width="700px"
      append-to-body
      :close-on-click-modal="false"
      custom-class="mac-style-dialog"
    >
      <el-form
        ref="apiForm"
        :model="apiForm"
        :rules="apiRules"
        label-width="120px"
        size="small"
        class="mac-form"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input
            v-model="apiForm.name"
            placeholder="请输入规则名称"
            clearable
          />
          <div class="field-help">
            为此API请求规则设置一个易于识别的名称
          </div>
        </el-form-item>

        <el-form-item label="请求路径" prop="url">
          <el-input
            v-model="apiForm.url"
            placeholder="/api/users 或 /api/users/* (Ant风格)"
            clearable
          />
          <div class="field-help">
            支持 Ant 风格路径匹配，如：/api/users/* 匹配 /api/users/123
          </div>
        </el-form-item>

        <el-form-item label="请求方法" prop="method">
          <el-select v-model="apiForm.method" placeholder="请选择请求方法" style="width: 100%">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
            <el-option label="PATCH" value="PATCH"></el-option>
            <el-option label="HEAD" value="HEAD"></el-option>
            <el-option label="OPTIONS" value="OPTIONS"></el-option>
            <el-option label="ALL" value="ALL">所有方法</el-option>
          </el-select>
          <div class="field-help">
            选择此规则要匹配的HTTP请求方法，选择"所有方法"将匹配任意HTTP方法
          </div>
        </el-form-item>

        <el-form-item label="例外动作" prop="exceptionAction">
          <el-radio-group v-model="apiForm.exceptionAction">
            <el-radio label="ALLOW">
              <i class="el-icon-check"></i>
              允许访问
            </el-radio>
            <el-radio label="DENY">
              <i class="el-icon-close"></i>
              拒绝访问
            </el-radio>
          </el-radio-group>
          <div class="field-help">
            {{ getExceptionActionHelp() }}
          </div>
        </el-form-item>

        <el-form-item label="路径类型">
          <el-switch
            v-model="apiForm.isAntPath"
            active-text="Ant风格路径"
            inactive-text="精确匹配"
          />
          <div class="field-help">
            Ant风格路径支持通配符匹配，如 * 和 **
          </div>
        </el-form-item>

        <el-form-item label="Content-Type">
          <el-input
            v-model="apiForm.contentType"
            placeholder="application/json"
            clearable
          />
          <div class="field-help">
            指定请求的内容类型，可选填
          </div>
        </el-form-item>

        <el-form-item label="请求头">
          <el-input
            v-model="apiForm.headers"
            type="textarea"
            :rows="3"
            placeholder='{"Authorization": "Bearer token"}'
          />
          <div class="field-help">
            JSON格式的请求头信息，用于匹配特定的请求头
          </div>
        </el-form-item>

        <el-form-item label="请求体">
          <el-input
            v-model="apiForm.body"
            type="textarea"
            :rows="3"
            placeholder='{"key": "value"}'
          />
          <div class="field-help">
            JSON格式的请求体内容，用于匹配特定的请求体
          </div>
        </el-form-item>

        <el-form-item label="规则描述">
          <el-input
            v-model="apiForm.description"
            type="textarea"
            :rows="2"
            placeholder="请输入规则描述"
          />
          <div class="field-help">
            详细描述此规则的用途和作用
          </div>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="apiForm.remark"
            placeholder="请输入备注信息"
            clearable
          />
          <div class="field-help">
            可添加额外的备注信息，如创建原因、注意事项等
          </div>
        </el-form-item>

        <el-form-item label="是否启用">
          <el-switch v-model="apiForm.enabled" />
          <div class="field-help">
            启用后此规则将生效，禁用后将被忽略
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="mac-footer">
        <el-button @click="apiDialogVisible = false" class="mac-cancel-btn">取消</el-button>
        <el-button type="primary" @click="confirmApiSave" :loading="saving" class="mac-confirm-btn">确定</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {
  getApiRequestList,
  createApiRequests,
  updateApiRequests,
  deleteApiRequests
} from '@system/api/integration/proxy-app'

export default {
  name: 'ApiRequestConfig',
  props: {
    appId: {
      type: [String, Number],
      required: true
    },
    baseUrl: {
      type: String,
      default: ''
    },
    authConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      saving: false,
      selectedApi: '',
      apiDialogVisible: false,
      currentApi: null,
      defaultPolicy: 'DENY_ALL', // 默认策略：ALLOW_ALL(允许所有) 或 DENY_ALL(拒绝所有)
      apiEndpoints: [], // 例外规则列表
      pagination: {
        current: 1,
        size: 10
      },
      total: 0,
      apiForm: {
        name: '',
        url: '',
        method: 'GET',
        exceptionAction: 'ALLOW', // 例外动作：ALLOW(允许) 或 DENY(拒绝)
        contentType: '',
        headers: '',
        body: '',
        description: '',
        remark: '',
        enabled: true,
        isAntPath: false,
        integrationAppId: null
      },
      apiRules: {
        name: [
          { required: true, message: '请输入规则名称', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入请求路径', trigger: 'blur' },
          { pattern: /^\//, message: '请求路径必须以/开头', trigger: 'blur' }
        ],
        method: [
          { required: true, message: '请选择请求方法', trigger: 'change' }
        ],
        exceptionAction: [
          { required: true, message: '请选择例外动作', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.fetchApiList()
  },
  methods: {
    // 获取例外规则列表和默认策略
    async fetchApiList() {
      try {
        this.loading = true
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          integrationAppId: this.appId
        }

        const response = await getApiRequestList(params)
        this.apiEndpoints = response.records || []
        this.total = response.total || 0

        // 如果有配置信息，获取默认策略
        if (response.defaultPolicy) {
          this.defaultPolicy = response.defaultPolicy
        }
      } catch (error) {
        console.error('获取例外规则列表失败:', error)
        this.$message.error('获取例外规则列表失败')
        this.apiEndpoints = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 切换默认策略
    async handleDefaultPolicyChange(policy) {
      try {
        // 这里应该调用API保存默认策略
        // await updateDefaultPolicy({ integrationAppId: this.appId, defaultPolicy: policy })
        this.defaultPolicy = policy
        this.$message.success(`默认策略已切换为${policy === 'ALLOW_ALL' ? '允许所有' : '拒绝所有'}`)
        this.emitChange()
      } catch (error) {
        console.error('切换默认策略失败:', error)
        this.$message.error('切换默认策略失败')
      }
    },

    // 添加允许规则（严格模式下的快捷操作）
    addAllowRule() {
      this.currentApi = null
      this.apiForm = {
        name: '允许访问规则',
        url: '/',
        method: 'GET',
        exceptionAction: 'ALLOW',
        contentType: '',
        headers: '',
        body: '',
        description: '允许特定请求访问',
        remark: '严格模式下的允许规则',
        enabled: true,
        isAntPath: false,
        integrationAppId: this.appId
      }
      this.apiDialogVisible = true
    },

    // 添加拒绝规则（宽松模式下的快捷操作）
    addDenyRule() {
      this.currentApi = null
      this.apiForm = {
        name: '拒绝访问规则',
        url: '/',
        method: 'GET',
        exceptionAction: 'DENY',
        contentType: '',
        headers: '',
        body: '',
        description: '拒绝特定请求访问',
        remark: '宽松模式下的拒绝规则',
        enabled: true,
        isAntPath: false,
        integrationAppId: this.appId
      }
      this.apiDialogVisible = true
    },

    // 添加自定义规则
    addCustomRule() {
      this.currentApi = null
      this.apiForm = {
        name: '',
        url: '',
        method: 'GET',
        exceptionAction: this.defaultPolicy === 'ALLOW_ALL' ? 'DENY' : 'ALLOW',
        contentType: '',
        headers: '',
        body: '',
        description: '',
        remark: '',
        enabled: true,
        isAntPath: false,
        integrationAppId: this.appId
      }
      this.apiDialogVisible = true
    },

    // 编辑API规则
    editApi(api) {
      this.currentApi = { ...api }
      this.apiForm = { ...api }
      this.apiDialogVisible = true
    },

    // 查看API详情
    viewApiDetail(api) {
      this.$alert(
        `<div style="text-align: left;">
          <p><strong>规则名称：</strong>${api.name}</p>
          <p><strong>请求路径：</strong>${api.url}</p>
          <p><strong>请求方法：</strong>${api.method}</p>
          <p><strong>访问类型：</strong>${api.accessType === 'WHITELIST' ? '白名单' : '黑名单'}</p>
          <p><strong>路径类型：</strong>${api.isAntPath ? 'Ant风格路径' : '精确匹配'}</p>
          <p><strong>Content-Type：</strong>${api.contentType || '无'}</p>
          <p><strong>描述：</strong>${api.description || '无'}</p>
          <p><strong>备注：</strong>${api.remark || '无'}</p>
          <p><strong>状态：</strong>${api.enabled ? '启用' : '禁用'}</p>
        </div>`,
        '规则详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      )
    },

    // 选择API
    selectApi(apiId) {
      this.selectedApi = apiId
    },

    // 刷新API列表
    refreshApiList() {
      this.fetchApiList()
    },

    // 获取例外动作的显示文本
    getExceptionActionText(api) {
      return api.exceptionAction === 'ALLOW' ? '允许' : '拒绝'
    },

    // 获取例外动作的标签类型
    getExceptionActionType(api) {
      return api.exceptionAction === 'ALLOW' ? 'success' : 'danger'
    },

    // 获取对话框标题
    getDialogTitle() {
      if (this.currentApi) {
        return '编辑例外规则'
      }
      return '添加例外规则'
    },

    // 获取例外动作的帮助文本
    getExceptionActionHelp() {
      if (this.defaultPolicy === 'ALLOW_ALL') {
        return '选择"拒绝访问"将阻止匹配此规则的请求（当前默认允许所有）'
      } else {
        return '选择"允许访问"将放行匹配此规则的请求（当前默认拒绝所有）'
      }
    },

    // 处理API命令
    handleApiCommand({ action, api }) {
      switch (action) {
        case 'toggle':
          this.toggleApiStatus(api)
          break
        case 'copy':
          this.copyApi(api)
          break
        case 'delete':
          this.deleteApi(api)
          break
      }
    },

    // 切换API状态
    async toggleApiStatus(api) {
      try {
        const updatedApi = { ...api, enabled: !api.enabled }
        await updateApiRequests([updatedApi])

        const index = this.apiEndpoints.findIndex(item => item.id === api.id)
        if (index > -1) {
          this.$set(this.apiEndpoints, index, updatedApi)
        }

        this.$message.success(`规则已${updatedApi.enabled ? '启用' : '禁用'}`)
      } catch (error) {
        console.error('切换规则状态失败:', error)
        this.$message.error('切换规则状态失败')
      }
    },

    // 复制API规则
    copyApi(api) {
      this.currentApi = null
      this.apiForm = {
        ...api,
        id: undefined,
        name: api.name + '_副本',
        url: api.url,
        createTime: undefined,
        updateTime: undefined
      }
      this.apiDialogVisible = true
    },

    // 删除API规则
    deleteApi(api) {
      this.$confirm('确定要删除这个API规则吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteApiRequests([api.id])
          this.$message.success('规则已删除')
          this.fetchApiList()
        } catch (error) {
          console.error('删除规则失败:', error)
          this.$message.error('删除规则失败')
        }
      }).catch(() => {})
    },

    // 确认保存例外规则
    async confirmApiSave() {
      try {
        this.saving = true
        await this.$refs.apiForm.validate()

        // 确保 integrationAppId 正确设置
        this.apiForm.integrationAppId = this.appId

        if (this.currentApi) {
          // 更新现有规则
          await updateApiRequests([this.apiForm])
          this.$message.success('例外规则更新成功')
        } else {
          // 添加新规则
          await createApiRequests([this.apiForm])
          this.$message.success('例外规则添加成功')
        }

        this.apiDialogVisible = false
        this.fetchApiList()
        this.emitChange()
      } catch (error) {
        console.error('保存例外规则失败:', error)
        this.$message.error('保存例外规则失败')
      } finally {
        this.saving = false
      }
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.fetchApiList()
    },

    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.fetchApiList()
    },

    // 获取请求方法类型
    getMethodType(method) {
      const types = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info',
        'HEAD': 'info',
        'OPTIONS': 'info'
      }
      return types[method] || 'info'
    },

    // 发出变化事件
    emitChange() {
      this.$emit('api-change', {
        defaultPolicy: this.defaultPolicy,
        exceptionRules: this.apiEndpoints,
        total: this.total
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.api-request-config {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e5e7eb;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;

      .default-policy-section {
        display: flex;
        align-items: center;
        gap: 12px;

        .policy-label {
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          white-space: nowrap;
        }

        .el-radio-group {
          background: white;
          border-radius: 10px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          .el-radio-button__inner {
            padding: 12px 20px;
            font-size: 13px;
            font-weight: 500;
            border: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 6px;

            i {
              font-size: 14px;
            }
          }

          .el-radio-button:first-child .el-radio-button__inner {
            border-radius: 10px 0 0 10px;
          }

          .el-radio-button:last-child .el-radio-button__inner {
            border-radius: 0 10px 10px 0;
          }

          .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
      }
    }
  }

  // 策略说明区域
  .policy-explanation {
    margin: 16px 0;
    padding: 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);

    .explanation-content {
      display: flex;
      align-items: center;
      gap: 24px;
      flex-wrap: wrap;

      .current-policy {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: #0c4a6e;

        i {
          font-size: 16px;
          color: #0ea5e9;
        }

        .policy-text {
          font-size: 14px;
        }
      }

      .exception-note {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #0369a1;

        i {
          font-size: 14px;
          color: #3b82f6;
        }
      }
    }
  }

  // 例外规则区域
  .exception-rules-section {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;

      h5 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .rule-count {
        font-size: 13px;
        color: #6b7280;
        background: #f3f4f6;
        padding: 2px 8px;
        border-radius: 12px;
      }
    }
  }

  // MAC风格按钮
  .mac-style-btn {
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
    }

    &.allow-rule-btn {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);

      &:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
      }
    }

    &.deny-rule-btn {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

      &:hover {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      }
    }

    &.add-rule-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);

      &:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      }
    }

    &.refresh-btn {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);

      &:hover {
        background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
      }
    }

    &:disabled {
      background: #e0e6ed;
      color: #8c8c8c;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &:hover {
        background: #e0e6ed;
        transform: none;
        box-shadow: none;
      }
    }
  }

  .api-list {
    .list-header {
        display: grid;
        grid-template-columns: 2fr 120px 80px 80px 120px;
        gap: 16px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
        border-radius: 10px;
        margin-bottom: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .header-item {
        font-size: 13px;
        color: #64748b;
        font-weight: 600;
      }
    }

    .api-items {
      .api-item {
          display: grid;
          grid-template-columns: 2fr 120px 80px 80px 120px;
          gap: 16px;
          padding: 16px;
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 10px;
          margin-bottom: 12px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #c7d2fe;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
          }

          &.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
          }

          .api-info {
            .api-path {
              display: flex;
              align-items: center;
              gap: 10px;
              margin-bottom: 8px;
              flex-wrap: wrap;

              .path-text {
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;
                word-break: break-all;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
                padding: 4px 8px;
                border-radius: 6px;
                border: 1px solid #d1d5db;
              }
            }

            .api-description {
              font-size: 13px;
              color: #6b7280;
              margin-bottom: 6px;
              line-height: 1.4;
            }

            .api-remark {
              font-size: 12px;
              color: #9ca3af;

              .remark-label {
                color: #6b7280;
                font-weight: 500;
              }

              .remark-text {
                color: #3b82f6;
                font-weight: 500;
              }
            }
          }

          .api-method {
            display: flex;
            align-items: center;

            .method-tag {
              font-weight: 600;
            }
          }

          .api-type {
            display: flex;
            align-items: center;
          }

          .api-status {
            display: flex;
            align-items: center;
          }

          .api-actions {
            display: flex;
            align-items: center;
            gap: 4px;
          }
      }
    }
  }

  .empty-state {
      text-align: center;
      padding: 60px 20px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      color: #64748b;

      i {
        font-size: 64px;
        color: #cbd5e1;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        margin: 12px 0;
        font-size: 16px;
        font-weight: 500;

        &.empty-desc {
          font-size: 14px;
          color: #94a3b8;
          font-weight: 400;
        }
    }
  }

  .pagination-wrapper {
    margin-top: 24px;
    text-align: center;
    padding: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
  }

  .access-control-info {
      margin-top: 24px;
      padding: 20px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #bae6fd;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);

    h5 {
      font-size: 16px;
      font-weight: 600;
      color: #0c4a6e;
      margin: 0 0 16px 0;
      background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .info-content {
      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        font-size: 14px;
        line-height: 1.5;

        .label {
          color: #0369a1;
          min-width: 100px;
          font-weight: 500;
          flex-shrink: 0;
        }

        span:not(.label) {
          color: #0c4a6e;
        }
      }

      .info-note {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin-top: 16px;
        padding: 16px;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        border: 1px solid #93c5fd;
        border-radius: 10px;
        font-size: 13px;
        color: #1e40af;
        line-height: 1.5;

        i {
          font-size: 16px;
          color: #3b82f6;
          margin-top: 2px;
          flex-shrink: 0;
        }
      }
    }
  }

  // MAC风格表单
  .mac-form {
    .el-form-item {
      margin-bottom: 24px;

      .el-form-item__label {
        font-weight: 500;
        color: #374151;
        font-size: 14px;
      }

      .el-input__inner,
      .el-textarea__inner,
      .el-select .el-input__inner {
        border: 1px solid #d1d5db;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }

      .el-switch {
        .el-switch__core {
          border-radius: 12px;

          &:after {
            border-radius: 50%;
          }
        }
      }
    }

    .field-help {
      font-size: 12px;
      color: #6b7280;
      margin-top: 6px;
      line-height: 1.4;
      font-style: italic;
    }
  }
}

// MAC风格对话框样式 (非scoped)
::v-deep .mac-style-dialog {
  .el-dialog {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 16px 16px 0 0;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      width: 32px;
      height: 32px;
      background: #f3f4f6;
      border-radius: 50%;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: #e5e7eb;
        transform: scale(1.1);
      }

      .el-dialog__close {
        color: #6b7280;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: white;
  }

  .el-dialog__footer {
    padding: 0;
    background: transparent;
  }
}

// MAC风格对话框底部
.mac-footer {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .mac-cancel-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 10px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);

    &:hover {
      background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
    }
  }

  .mac-confirm-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 10px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      background: #e0e6ed;
      color: #8c8c8c;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &:hover {
        background: #e0e6ed;
        transform: none;
        box-shadow: none;
      }
    }
  }
}
</style>
