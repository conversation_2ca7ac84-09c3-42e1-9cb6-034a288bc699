<template>
  <div class="file-actions">
    <div class="action-item">
      <el-tooltip content="下载文件" placement="top">
        <el-button 
          type="text"
          size="mini"
          :disabled="!file.in_folder"
          @click.stop="handleDownload"
        >
          <i class="el-icon-download"></i>
        </el-button>
      </el-tooltip>
    </div>

    <div class="action-item">
      <el-tooltip :content="file.in_db ? '重新添加至向量库' : '添加至向量库'" placement="top">
        <el-button 
          type="text"
          size="mini"
          :disabled="!file.in_folder"
          @click.stop="showReloadDialog"
        >
          <i class="el-icon-refresh"></i>
        </el-button>
      </el-tooltip>
    </div>

    <div class="action-item">
      <el-dropdown 
        trigger="hover"
        @command="handleDelete"
        class="delete-dropdown"
      >
        <el-button 
          type="text"
          size="mini"
        >
          <i class="el-icon-delete"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item 
            :command="'all'"
          >
            完全删除
          </el-dropdown-item>
          <el-dropdown-item 
            :command="'vector'"
            :disabled="!file.in_db"
            divided
          >
            从向量库删除
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 重新加载对话框 -->
    <upload-dialog
      :visible.sync="reloadDialogVisible"
      :kb-name="kbName"
      :mode="'reload'"
      :reload-file="file"
      @success="$emit('reload')"
    />
  </div>
</template>

<script>
import api from '../../api'
import UploadDialog from './UploadDialog.vue'

export default {
  name: 'FileActions',
  components: {
    UploadDialog
  },
  props: {
    file: {
      type: Object,
      required: true
    },
    kbName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      reloadDialogVisible: false
    }
  },
  methods: {
    async handleDownload() {
      try {
        const response = await api.knowledge.downloadFile({
          knowledge_base_name: this.kbName,
          file_name: this.file.file_name
        });
        
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', this.file.file_name.split('/').pop());
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        this.$message.error('下载失败');
        console.error(error);
      }
    },

    showReloadDialog() {
      this.reloadDialogVisible = true;
    },

    async handleDelete(type) {
      try {
        const isVectorOnly = type === 'vector';
        const confirmMessage = isVectorOnly 
          ? '确认将该文件从向量库中删除吗？' 
          : '确认完全删除该文件吗？这将同时从知识库和向量库中删除文件';
        
        await this.$confirm(confirmMessage, '提示', {
          type: 'warning'
        });
        await api.knowledge.deleteFile({
          knowledge_base_name: this.kbName,
          file_name: this.file.file_name,
          delete_content: !isVectorOnly,
          not_refresh_vs_cache: false
        });
        this.$message.success('删除成功');
        this.$emit('reload');
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败');
          console.error(error);
        }
      }
    }
  }
}
</script>

<style scoped>
.file-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0;
}

.action-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-item .el-button {
  width: 20px;
  height: 20px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-item .el-button i {
  font-size: 16px;
  line-height: 1;
}

.delete-dropdown {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.file-actions .el-button:not(:disabled):hover {
  color: #409EFF;
}

.delete-dropdown .el-button:not(:disabled):hover {
  color: #F56C6C;
}

.file-actions .el-button[disabled] {
  color: #C0C4CC;
  cursor: not-allowed;
}

:deep(.el-dropdown-menu__item.is-disabled) {
  color: #C0C4CC;
  cursor: not-allowed;
}

:deep(.el-dropdown-menu__item--divided) {
  margin-top: 6px;
  border-top-color: #EBEEF5;
}

:deep(.el-table .cell) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 删除按钮下拉菜单样式 */
:deep(.el-dropdown-menu) {
  padding: 5px 0;
  min-width: 120px;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  font-size: 13px;
  line-height: 1.5;
}

:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  background-color: #f5f7fa;
  color: #F56C6C;
}
</style> 