import { getAccountConfig, setAccountConfig, deleteAccountConfig } from '@/api/login/login'
import store from '@/store'
export class AccountConfig {


    static get(config<PERSON><PERSON>, groupName) {
        return new Promise((resolve, reject) => {
            getAccountConfig({
                enabled: true,
                type: config<PERSON><PERSON>,
                config<PERSON>ey,
                groupName,
                current: 1,
                size: -1,
                accountId: store.state.user.id
            }).then(response => {
                resolve(response?.records || [])
            }).catch(error => {
                reject(error)
            })
        })
    }

    static set(config<PERSON>ey, groupName, data) {
        return new Promise((resolve, reject) => {
            setAccountConfig({
                ...data,
                type: config<PERSON><PERSON>,
                config<PERSON>ey,
                groupName,
            }).then(response => {
                resolve(response)
            }).catch(error => {
                reject(error)
            })
        })
    }
    static delete(ids) {
        return new Promise((resolve, reject) => {
            deleteAccountConfig(ids).then(response => {
                resolve(response)
            }).catch(error => {
                reject(error)
            })
        })
    }
}