 import request from '@/utils/request'

const api = CONSTANT.SYSTEM + '/sys/account'

export function list(params) {
  return request({
    url: `${api}/list`,
    method: 'get',
    params
  })
}

export function getInfo(params) {
  return request({
    url: `${api}`,
    method: 'get',
    params
  })
}

export function update(data) {
  return request({
    url: `${api}`,
    method: 'put',
    data
  })
}

//修改个人信息
export function updateByUser(data) {
  return request({
    url: `${api}/updateByUser`,
    method: 'put',
    data
  })
}

export function changePassword(data) {
  return request({
    url: `${api}/changePassword`,
    method: 'put',
    data
  })
}

// 获取用户列表
export function getAccountList(params) {
  return request({
    url: `${api}/list`,
    method: 'get',
    params
  })
}
