<template>
  <div class="knowledge-form-overlay">
    <div class="knowledge-form">
      <h3>{{ item ? '编辑知识条目' : '添加知识条目' }}</h3>
      <form @submit.prevent="handleSubmit">
        <div class="form-group">
          <label>标题</label>
          <input 
            type="text" 
            v-model="formData.title"
            required
            placeholder="请输入标题"
          >
        </div>
        <div class="form-group">
          <label>内容</label>
          <textarea 
            v-model="formData.content"
            required
            placeholder="请输入内容"
            rows="4"
          ></textarea>
        </div>
        <div class="form-actions">
          <button type="button" class="cancel-btn" @click="$emit('cancel')">取消</button>
          <button type="submit" class="submit-btn">保存</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KnowledgeForm',
  props: {
    item: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      formData: {
        title: '',
        content: ''
      }
    }
  },
  created() {
    if (this.item) {
      this.formData = { ...this.item }
    }
  },
  methods: {
    handleSubmit() {
      this.$emit('save', { ...this.formData })
    }
  }
}
</script>

<style scoped>
.knowledge-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.knowledge-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  color: #666;
}

input, textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

textarea {
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
}

.submit-btn {
  background: #42b983;
  color: white;
}
</style> 