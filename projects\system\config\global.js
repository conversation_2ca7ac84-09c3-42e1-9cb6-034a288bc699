const dev = {
  QUARTZ: 'quartz',
  INTEGRATION: 'integration',
  META: 'meta',
  AUTH: 'auth'
}

const test = {
  QUARTZ: 'quartz',
  INTEGRATION: 'integration',
  META: 'meta',
  AUTH: 'auth'

}

const pro = {
  QUARTZ: 'quartz',
  INTEGRATION: 'integration',
  META: 'meta',
  AUTH: 'auth'
}


function getApi() {
  if (process.env.VUE_APP_ENV === 'development') {
    return dev
  } else if (process.env.VUE_APP_ENV === 'stage') {
    return test
  } else {
    return pro
  }
}

const api = getApi()
export default api



