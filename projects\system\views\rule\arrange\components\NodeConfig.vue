<template>
  <div class="node-config">
    <el-form
        ref="form"
        :model="formData"
        label-width="80px"
        size="small"
    >
      <!-- 基础配置 -->
      <el-form-item label="节点名称">
        <el-input v-model="formData.name" placeholder="请输入节点名称"></el-input>
      </el-form-item>

      <!-- HTTP节点配置 -->
      <template v-if="node.type === 'HTTP_NODE'">
        <el-form-item label="请求URL">
          <el-input v-model="formData.config.url" placeholder="请输入请求URL"></el-input>
        </el-form-item>

        <el-form-item label="请求方法">
          <el-select v-model="formData.config.method">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="请求参数">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="Query参数" name="query">
              <param-table v-model="formData.config.params" />
            </el-tab-pane>
            <el-tab-pane label="Body参数" name="body">
              <param-table v-model="formData.config.body" />
            </el-tab-pane>
            <el-tab-pane label="Header参数" name="headers">
              <param-table v-model="formData.config.headers" />
            </el-tab-pane>
          </el-tabs>
        </el-form-item>
      </template>

      <!-- 脚本节点配置 -->
      <template v-else-if="node.type === 'SCRIPT_NODE'">
        <el-form-item label="脚本类型">
          <el-select v-model="formData.config.scriptType">
            <el-option label="JavaScript" value="js"></el-option>
            <el-option label="Groovy" value="groovy"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="脚本内容">
          <monaco-editor
              v-model="formData.config.script"
              :language="formData.config.scriptType"
              style="height: 300px"
          />
        </el-form-item>
      </template>

      <!-- 条件节点配置 -->
      <template v-else-if="node.type === 'CONDITION_NODE' || node.type === 'SWITCH_NODE'">
        <el-form-item label="条件表达式">
          <el-input
              type="textarea"
              v-model="formData.config.condition"
              placeholder="请输入条件表达式"
              :rows="3"
          ></el-input>
        </el-form-item>
      </template>

      <!-- 循环节点配置 -->
      <template v-else-if="node.type === 'FOR_NODE' || node.type === 'WHILE_NODE'">
                  <el-form-item :label="node.type === 'FOR_NODE' ? '循环次数' : '循环条件'">
          <el-input
              v-if="node.type === 'FOR_NODE'"
              v-model.number="formData.config.times"
              type="number"
              placeholder="请输入循环次数"
          ></el-input>
          <el-input
              v-else
              type="textarea"
              v-model="formData.config.condition"
              placeholder="请输入循环条件"
              :rows="3"
          ></el-input>
        </el-form-item>
      </template>

      <!-- 后置提取配置 -->
      <el-form-item label="后置提取">
        <el-button
            type="text"
            icon="el-icon-plus"
            @click="addExtractRule"
        >添加提取规则</el-button>

        <div
            v-for="(rule, field) in formData.post_extract"
            :key="field"
            class="extract-rule"
        >
          <el-input
              v-model="extractFields[field]"
              placeholder="字段名"
              @change="updateExtractField(field, $event)"
          >
            <template slot="prepend">字段</template>
          </el-input>

          <el-input
              v-model="formData.post_extract[field]"
              placeholder="JSONPath表达式"
          >
            <template slot="prepend">表达式</template>
          </el-input>

          <el-button
              type="text"
              icon="el-icon-delete"
              @click="removeExtractRule(field)"
          ></el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import ParamTable from './ParamTable.vue'
import MonacoEditor from './MonacoEditor.vue'

export default {
  name: 'NodeConfig',

  components: {
    ParamTable,
    MonacoEditor
  },

  props: {
    node: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      activeTab: 'query',
      formData: {
        name: '',
        config: {
          url: '',
          method: 'GET',
          params: [],
          body: [],
          headers: []
        },
        post_extract: {}
      },
      extractFields: {}
    }
  },

  watch: {
    node: {
      immediate: true,
      handler(node) {
        this.formData = JSON.parse(JSON.stringify(node))
        
        if (node.type === 'HTTP_NODE') {
          this.formData.config = {
            url: this.formData.config?.url || '',
            method: this.formData.config?.method || 'GET',
            params: this.formData.config?.params || [],
            body: this.formData.config?.body || [],
            headers: this.formData.config?.headers || []
          }
        }
        
        this.extractFields = Object.keys(node.post_extract || {}).reduce((acc, field) => {
          acc[field] = field
          return acc
        }, {})
      }
    },

    formData: {
      deep: true,
      handler(val) {
        this.$emit('update', val)
      }
    }
  },

  methods: {
    addExtractRule() {
      const field = `field_${Date.now()}`
      this.$set(this.formData.post_extract, field, '')
      this.$set(this.extractFields, field, field)
    },

    removeExtractRule(field) {
      this.$delete(this.formData.post_extract, field)
      this.$delete(this.extractFields, field)
    },

    updateExtractField(oldField, newField) {
      if (oldField === newField) return

      const value = this.formData.post_extract[oldField]
      this.$delete(this.formData.post_extract, oldField)
      this.$set(this.formData.post_extract, newField, value)
      this.$delete(this.extractFields, oldField)
      this.$set(this.extractFields, newField, newField)
    }
  }
}
</script>

<style lang="scss" scoped>
.node-config {
  .extract-rule {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .el-input {
      margin-right: 10px;
    }
  }
}
</style>