import request from '@/utils/request'

export function login({ type, data, headers }) {
  let url = 'login'
  type && (url = `${type}/${url}`)
  url = `${CONSTANT.SYSTEM}/${url}`
  return request({
    url,
    method: 'post',
    data,
    headers
  })
}

export function getInfo() {
  return request({
    url: CONSTANT.SYSTEM + '/info',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: CONSTANT.SYSTEM + '/logout',
    method: 'post'
  })
}

export function code(type) {
  type === 'text' && (type = '')
  return request({
    url: type ? `/code/${type}` : '/code',
  })
}

export function getConfigJson() {
  return request({
    // url: 'http://192.168.1.122:8001' + '/config/config-front.json',
    url: '/config/config-front.json'
  })
}
export function getAccountConfig(params) {
  return request({
    url: CONSTANT.SYSTEM + '/sys/account/config/scope/list',
    method: 'get',
    params
  })
}
export function setAccountConfig(data) {
  return request({
    url: CONSTANT.SYSTEM + '/sys/account/config',
    method: 'post',
    data
  })
}
export function deleteAccountConfig(ids) {
  return request({
    url: CONSTANT.SYSTEM + `/sys/account/config/${ids}`,
    method: 'delete',
    data
  })
}