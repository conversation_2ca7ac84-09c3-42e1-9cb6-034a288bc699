/**
 * 邮箱地址验证器
 * 基于 RFC 2822 标准，但做了一些实用性的权衡
 * 这个实现会验证大多数常见的邮箱格式，但不包括带双引号或注释的特殊邮箱格式
 */
export default class EmailValidator {
    static MAX_LOCAL_PART_LENGTH = 64;

    static LOCAL_PART_ATOM = '[a-z0-9!#$%&\'*+/=?^_`{|}~\\u0080-\\uffff-]';
    static LOCAL_PART_INSIDE_QUOTES_ATOM = '(?:[a-z0-9!#$%&\'*.(),<>\\[\\]:;  @+/=?^_`{|}~\\u0080-\\uffff-]|\\\\\\\\|\\\\")';

    /**
     * 邮箱本地部分（@前面的部分）的正则表达式
     */
    static LOCAL_PART_PATTERN = new RegExp(
        `(?:${EmailValidator.LOCAL_PART_ATOM}+|"${EmailValidator.LOCAL_PART_INSIDE_QUOTES_ATOM}+")` +
        `(?:\\.(?:${EmailValidator.LOCAL_PART_ATOM}+|"${EmailValidator.LOCAL_PART_INSIDE_QUOTES_ATOM}+"))*`,
        'i'
    );

    /**
     * 验证邮箱地址是否有效
     * @param {string} value - 要验证的邮箱地址
     * @returns {boolean} - 如果邮箱地址有效则返回true，否则返回false
     */
    static isValid(value) {
        if (!value || value.length === 0) {
            return true;
        }

        // 不能简单地用@分割邮箱字符串，因为@可能是引号内本地部分的一部分
        // 所以我们需要在字符串中找到最后一个@的位置
        const splitPosition = value.lastIndexOf('@');

        if (splitPosition < 0) {
            return false;
        }

        const localPart = value.substring(0, splitPosition);
        const domainPart = value.substring(splitPosition + 1);

        if (!EmailValidator.isValidEmailLocalPart(localPart)) {
            return false;
        }

        return EmailValidator.isValidEmailDomainAddress(domainPart);
    }

    /**
     * 验证邮箱的本地部分是否有效
     * @param {string} localPart - 邮箱的本地部分（@前面的部分）
     * @returns {boolean} - 如果本地部分有效则返回true，否则返回false
     */
    static isValidEmailLocalPart(localPart) {
        if (localPart.length > EmailValidator.MAX_LOCAL_PART_LENGTH) {
            return false;
        }
        return EmailValidator.LOCAL_PART_PATTERN.test(localPart);
    }

    /**
     * 验证邮箱域名部分是否有效
     * @param {string} domainPart - 邮箱的域名部分（@后面的部分）
     * @returns {boolean} - 如果域名部分有效则返回true，否则返回false
     */
    static isValidEmailDomainAddress(domainPart) {
        if (!domainPart || domainPart.length === 0) {
            return false;
        }

        // 域名最大长度为255个字符
        if (domainPart.length > 255) {
            return false;
        }

        // 检查域名是否符合DNS命名规则
        const domainRegex = /^[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/;
        if (!domainRegex.test(domainPart)) {
            return false;
        }

        // 检查每个域名段的长度是否不超过63个字符
        const labels = domainPart.split('.');
        for (const label of labels) {
            if (label.length > 63) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证邮箱格式是否正确
     * @param {string} email - 需要验证的邮箱地址
     * @returns {boolean} - 邮箱格式正确返回true，否则返回false
     * @example
     * // 返回 true
     * EmailValidator.validateEmail('<EMAIL>')
     * // 返回 false
     * EmailValidator.validateEmail('invalid.email@')
     */
    static validateEmail(email) {
        return EmailValidator.isValid(email);
    }
}