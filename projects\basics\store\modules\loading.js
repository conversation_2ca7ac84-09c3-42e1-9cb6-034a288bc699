const state = {
    loadingCount: 0,
}

const mutations = {
    SET_LOADING: (state, isShow) => { // 需要全局loading的则loadingCount+1,否则loadingCount-1
        isShow ? ++state.loadingCount : --state.loadingCount
    },
    CLEAR_LOADING: (state) => {
        state.loadingCount = 0  // 当loadingCount等于0时关闭loading效果
    },
}

const actions = {
    setLoding({ commit }, boolean) {
        commit('SET_LOADING', boolean)
    },
    clearLoading({ commit }) {
        commit('CLEAR_LOADING')
    },
}

export default {
    namespaced: true,
    state,
    mutations,
    actions,
}