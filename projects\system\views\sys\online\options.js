export default {
  api:'sys/account',
  // newRequest: "/sys/resources/onlines",
  search: {
    isShow: false,
    showReset: true
  },
  table: {},
  formRule: [
    {
      type: "input",
      field: "username",
      className: "username-dom",
      title: "用户名",
      value: null,
      isSearch: true,              //是否为search内容
      isTable: true,               //是否为table内容
      isScope: false
    },
    {
      type: "input",
      field: "name",
      className: "name-dom",
      title: "昵称",
      value: null,
      isSearch: false,
      isTable: true,
      props: {
        placeholder: '请输入',
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "input",
      field: "token",
      className: "token-dom",
      title: "token",
      value: null,
      isSearch: false,
      isTable: true,
      isScope: false
    },
    {
      type: "input",
      field: "host",
      className: "host-dom",
      title: "host",
      value: null,
      isSearch: false,
      isTable: true,
      isScope: true
    },
    {
      type: "DatePicker",
      field: "loginTime",
      className: "loginTime-dom",
      title: "登录时间",
      isSearch: false,
      isTable: true,
      isHidden:false
    },
  ]
}
