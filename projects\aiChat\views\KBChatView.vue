<template>
  <div class="kb-chat">
    <div class="chat-container">
      <!-- 左侧知识库选择 -->
      <div class="kb-selector">
        <div class="kb-header">
          <h3>选择知识库</h3>
        </div>
        <el-select 
          v-model="selectedKB" 
          placeholder="请选择知识库"
          style="width: 100%"
          @change="handleKBChange"
        >
          <el-option
            v-for="kb in knowledgeBases"
            :key="kb.kb_name"
            :label="kb.kb_name"
            :value="kb.kb_name"
          />
        </el-select>

        <!-- 对话参数设置 -->
        <div class="chat-settings">
          <h4>对话设置</h4>
          <el-form :model="chatConfig" label-position="top" size="small">
            <el-form-item label="模型">
              <el-select v-model="chatConfig.model" style="width: 100%">
                <el-option label="Qwen 2.5" value="qwen2.5:7b" />
              </el-select>
            </el-form-item>
            <el-form-item label="温度">
              <el-slider 
                v-model="chatConfig.temperature" 
                :min="0" 
                :max="1" 
                :step="0.1"
              />
            </el-form-item>
            <el-form-item label="返回数量">
              <el-input-number 
                v-model="chatConfig.top_k"
                :min="1"
                :max="10"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="相关度阈值">
              <el-input-number 
                v-model="chatConfig.score_threshold"
                :min="0"
                :max="2"
                :step="1"
                controls-position="right"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 右侧对话区域 -->
      <div class="chat-main">
        <!-- 对话历史 -->
        <div class="chat-messages" ref="messagesContainer">
          <div 
            v-for="(msg, index) in chatHistory" 
            :key="index"
            :class="['message', msg.role]"
          >
            <div class="message-content">
              <!-- 参考文档区域 -->
              <template v-if="msg.role === 'assistant' && msg.docs">
                <div class="reference-docs">
                  <div 
                    class="docs-header" 
                    @click="toggleDocs(index)"
                    :class="{ 'is-loading': msg.loading }"
                  >
                    <span class="docs-title">
                      参考文档 
                      <span class="docs-count">({{ msg.docs.length }})</span>
                    </span>
                    <i 
                      :class="[
                        msg.loading ? 'el-icon-loading' : 
                        msg.showDocs ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                      ]"
                    ></i>
                  </div>
                  <div v-show="msg.showDocs" class="docs-content">
                    <div 
                      v-for="(doc, docIndex) in msg.docs" 
                      :key="docIndex" 
                      class="doc-item"
                    >
                      {{ `[${docIndex + 1}] ${doc}` }}
                    </div>
                  </div>
                </div>
              </template>
              {{ msg.content }}
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="3"
            placeholder="请输入问题..."
            resize="none"
            @keyup.enter.native.exact="handleSend"
          />
          <el-button 
            type="primary" 
            :loading="sending"
            @click="handleSend"
          >发送</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import api from '../api/knowledge'

export default {
  name: 'KBChatView',
  
  data() {
    return {
      knowledgeBases: [],
      selectedKB: '',
      chatHistory: [],
      userInput: '',
      sending: false,
      chatConfig: {
        model: 'qwen2.5:7b',
        temperature: 0.7,
        top_k: 3,
        score_threshold: 2,
        max_tokens: 0,
        prompt_name: 'default',
        mode: 'local_kb',
        stream: true
      }
    }
  },

  methods: {
    async loadKnowledgeBases() {
      try {
        const response = await api.getList()
        this.knowledgeBases = response.data
      } catch (error) {
        this.$message.error('加载知识库列表失败')
      }
    },

    handleKBChange() {
      this.chatHistory = []
    },

    async handleSend() {
      if (!this.userInput.trim()) {
        this.$message.warning('请输入问题')
        return
      }

      if (!this.selectedKB) {
        this.$message.warning('请选择知识库')
        return
      }

      const userMessage = {
        role: 'user',
        content: this.userInput.trim()
      }
      this.chatHistory.push(userMessage)
      
      const assistantMessage = {
        role: 'assistant',
        content: '',
        docs: [],
        showDocs: false,
        loading: true
      }
      this.chatHistory.push(assistantMessage)
      
      this.sending = true
      this.userInput = ''

      try {
        const response = await fetch('/chat/kb_chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ...this.chatConfig,
            query: userMessage.content,
            kb_name: this.selectedKB,
            history: this.chatHistory.slice(0, -1)
          })
        })

        const reader = response.body.getReader()
        const decoder = new TextDecoder()

        const processStreamData = async () => {
          try {
            let streamDone = false
            while (!streamDone) {
              const { value, done } = await reader.read()
              streamDone = done
              
              if (done) break

              const chunk = decoder.decode(value)
              const lines = chunk.split('\n')

              lines.forEach(line => {
                if (line.startsWith('data: ')) {
                  try {
                    const data = JSON.parse(line.slice(5))
                    if (data.docs) {
                      assistantMessage.docs = data.docs
                      assistantMessage.loading = false
                    }
                    if (data.choices?.[0]?.delta?.content) {
                      assistantMessage.content += data.choices[0].delta.content
                    }
                  } catch (e) {
                    console.error('Parse SSE data error:', e)
                  }
                }
              })

              this.$nextTick(() => {
                this.scrollToBottom()
              })
            }
          } catch (error) {
            console.error('Stream reading error:', error)
            throw error
          }
        }

        await processStreamData()
      } catch (error) {
        this.$message.error('发送消息失败')
        this.chatHistory.pop()
      } finally {
        this.sending = false
        if (assistantMessage.loading) {
          assistantMessage.loading = false
        }
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    toggleDocs(messageIndex) {
      const message = this.chatHistory[messageIndex]
      if (message.loading) return
      
      this.$set(message, 'showDocs', !message.showDocs)
    }
  },

  created() {
    this.loadKnowledgeBases()
  }
}
</script>

<style scoped>
.kb-chat {
  height: 100%;
  padding: 20px;
}

.chat-container {
  display: flex;
  gap: 20px;
  height: calc(100vh - 100px);
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.kb-selector {
  width: 300px;
  padding: 20px;
  border-right: 1px solid #eee;
}

.kb-header {
  margin-bottom: 20px;
}

.kb-header h3 {
  margin: 0;
  color: #333;
}

.chat-settings {
  margin-top: 20px;
}

.chat-settings h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.message {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.message.user {
  align-items: flex-end;
}

.message-content {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 8px;
  white-space: pre-wrap;
  word-break: break-word;
}

.user .message-content {
  background: #e3f2fd;
}

.assistant .message-content {
  background: #f5f5f5;
}

.reference-docs {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.docs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  cursor: pointer;
  transition: background-color 0.3s;
}

.docs-header:hover {
  background: #e9ecf2;
}

.docs-header.is-loading {
  cursor: wait;
}

.docs-title {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 5px;
}

.docs-count {
  color: #909399;
  font-weight: normal;
}

.docs-content {
  padding: 8px 12px;
  background: white;
}

.doc-item {
  margin-bottom: 6px;
  line-height: 1.4;
}

.doc-item:last-child {
  margin-bottom: 0;
}

.chat-input {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 10px;
}

.chat-input .el-textarea {
  flex: 1;
}

.chat-input .el-button {
  align-self: flex-end;
}

/* 添加加载图标的动画 */
.el-icon-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 