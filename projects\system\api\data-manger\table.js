import request from '@/utils/request'

const api = CONSTANT.META

// 获取表列表
export function getTableList(params) {
  return request({
    url: api + '/table/list',
    method: 'get',
    params
  })
}

// 获取表字段列表
export function getColumnList(params) {
  return request({
    url: api+ '/column/list',
    method: 'get',
    params
  })
}

// 同步表结构
export function syncTables(datasourceId) {
  return request({
    url: api + `/table/sync`,
    method: 'post',
    params: { datasourceId }
  })
}

// 添加新的API方法
export function createTableFromDDL(data) {
  return request({
    url: api + '/table/create-from-ddl',
    method: 'post',
    data
  })
}

export function createColumn(data) {
  return request({
    url: api + '/column/create',
    method: 'post',
    data
  })
} 