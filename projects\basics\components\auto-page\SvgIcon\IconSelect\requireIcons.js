const re = /\.\/(.*)\.svg/
const req = require.context('../../../../../../projects', true, /\.svg$/)
const dirs = process.env.PROJECTS_DIR
const icons = []
const requireAll = arr => arr.forEach(requireContext => {
  requireContext.keys().filter(f => {
    let dir = dirs.find(d => f.indexOf(d) > 0)
    if (dir) {
      return f.indexOf(`${dir}/icons/`) > 0
    } else {
      return true
    }
  }).forEach(i => {
    let name = i.match(re)[1]
    let startIndex = name.lastIndexOf('/') + 1
    let svgFileName = name.substring(startIndex)
    icons.push(svgFileName)
  })
})

requireAll([req])

export default icons
