import { login, logout, getInfo } from '@/api/login/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { getRouters } from '@/api/resource/resources'
import { resetRouter } from '@/router'
import { AccountConfig } from "@/utils/AccountConfig"
import lodash from 'lodash'

const state = {
  token: getToken(),
  info: '',
  info2: '',
  username: '',
  name: '',
  nickName: '',
  id: '',
  roles: [],
  permissions: [],
  userExtraInfo: Object
}

const mutations = {
  SET_INFO: (state, info) => {
    state.info = info
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_ID: (state, id) => {
    state.id = id
  },
  SET_USERNAME: (state, username) => {
    state.username = username
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_NICK_NAME: (state, nickName) => {
    state.nickName = nickName
  },
  SET_PHONE: (state, phone) => {
    state.phone = phone
  },
  SET_EMAIL: (state, email) => {
    state.email = email
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
  SET_USER_EXTRA_INFO: (state, userExtraInfo) => {
    state.userExtraInfo = userExtraInfo
  },
}

const actions = {

  smsLogin({ commit }, form) {
    return new Promise((resolve, reject) => {
      form.type = 'sms'
      login(form).then(response => {
        commit('SET_TOKEN', response)
        setToken(response)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  emailLogin({ commit }, form) {
    form.type = 'email'
    return new Promise((resolve, reject) => {
      login(form).then(response => {
        commit('SET_TOKEN', response)
        setToken(response)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // user login
  login({ commit }, form) {
    return new Promise((resolve, reject) => {
      login(form).then(response => {
        commit('SET_TOKEN', response)
        setToken(response)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(data => {
        commit('SET_INFO', {
          ...data,
          name: data.nickName || data.name,
          nickName: data.nickName || data.name
        })
        state.info2 = data
        commit('SET_ID', data.id)
        commit('SET_NAME', data.nickName || data.name)
        commit('SET_USERNAME', data.username)
        commit('SET_NICK_NAME', data.nickName || data.name)
        commit('SET_ROLES', data.roles)
        commit('SET_PERMISSIONS', data.permissions)
        commit('SET_PHONE', data.phone)
        commit('SET_EMAIL', data.email)
        let userExtraInfo = {}
        commit('SET_USER_EXTRA_INFO', userExtraInfo)

        AccountConfig.get('accountConfig', 'accountConfig').then(response => {
          if (response && response.length > 0) {
            userExtraInfo['accountConfig'] = response[0]?.config || {}
          } else {
            userExtraInfo['accountConfig'] = {}
          }

        }).catch(error => {
          userExtraInfo['accountConfig'] = {}
        })

        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  putAccountConfig({ commit }, config = {}) {
    const aConfig = lodash.merge(state.userExtraInfo['accountConfig'] || {}, config || {})
    return new Promise((resolve, reject) => {
      AccountConfig.set('accountConfig', 'accountConfig', {
        config: aConfig
      }).then(response => {
        state.userExtraInfo['accountConfig'] = aConfig
        resolve(aConfig)
      }).catch(error => {
        reject(error)
      })
    })
  },
  //获取账号配置
  getAccountConfig({ commit, state }) {

    return new Promise((resolve, reject) => {
      AccountConfig.get('accountConfig', 'accountConfig').then(response => {
        if (response && response.length > 0) {
          resolve(response[0]?.config || {})
        } else {
          resolve({})
        }
      }).catch(error => {
        reject(error)
      })
    })
  },

  getRouters({ commit, state }) {
    return new Promise((resolve, reject) => {
      getRouters().then(data => {
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resetRouter()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      removeToken()
      resolve()
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

