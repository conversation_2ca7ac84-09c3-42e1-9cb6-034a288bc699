import { BezierEdge, BezierEdgeModel } from '@logicflow/core'
import { decorate, action, observable } from 'mobx'

export class CustomBezierEdge extends BezierEdge {

}

export class CustomBezierEdgeModel extends BezierEdgeModel {

    @observable isHighlight = false

    setAttributes() {
        const { executeStatus } = this.properties
        this.isAnimation = executeStatus === 'success'
    }

    @action
    setHighlight(isHighlight = true) {
        this.isHighlight = isHighlight
    }


    getEdgeStyle() {
        const style = super.getEdgeStyle()
        if (this.isHighlight) {
            style.stroke = '#5eb15e'
            style.strokeWidth = 3
        } else if (this.isHovered) {
            style.stroke = '#1e6fff'
            style.strokeWidth = 3
        }
        return style
    }

    getEdgeAnimationStyle() {
        const style = super.getEdgeAnimationStyle()
        style.stroke = '#84cf65'
        if (this.isHighlight) {
            style.strokeWidth = 3
        } else if (this.isHovered) {
            style.strokeWidth = 3
        }
        return style
    }
}

export default {
    // type: 'customBezier',
    type: 'bezier',
    view: CustomBezierEdge,
    model: CustomBezierEdgeModel
}