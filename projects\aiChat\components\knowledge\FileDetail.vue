<template>
  <el-dialog title="文件详情" :visible="visible" @update:visible="$emit('update:visible', $event)" width="600px">
    <div v-if="file" class="file-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="文件名" :span="2">
          {{ file.file_name }}
        </el-descriptions-item>
        <el-descriptions-item label="文件类型">
          {{ file.file_ext }}
        </el-descriptions-item>
        <el-descriptions-item label="文件大小">
          {{ formatFileSize(file.file_size) }}
        </el-descriptions-item>
        <el-descriptions-item label="文档数量">
          {{ file.docs_count }}
        </el-descriptions-item>
        <el-descriptions-item label="文件版本">
          v{{ file.file_version }}
        </el-descriptions-item>
        <el-descriptions-item label="加载器">
          {{ file.document_loader }}
        </el-descriptions-item>
        <el-descriptions-item label="分词器">
          {{ file.text_splitter }}
        </el-descriptions-item>
        <el-descriptions-item label="源文件">
          <el-tag :type="file.in_db ? 'success' : 'warning'">
            {{ file.in_db ? '√' : '×' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="向量库">
          <el-tag :type="file.in_folder ? 'success' : 'info'">
            {{ file.in_folder ? '√' : '×' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ formatDate(file.create_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后修改时间" :span="2">
          {{ formatDate(new Date(file.file_mtime * 1000)) }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<script>
import { formatDate, formatFileSize } from '../../utils/format'

export default {
  name: 'FileDetail',
  props: {
    visible: Boolean,
    file: Object
  },
  methods: {
    formatDate,
    formatFileSize
  }
}
</script>

<style scoped>
.file-detail {
  padding: 10px;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions-item__label) {
  width: 120px;
  background-color: #f5f7fa;
}
</style>