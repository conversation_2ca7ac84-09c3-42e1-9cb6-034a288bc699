<template>
    <div class="edge-style-panel" :class="{ collapsed: isCollapsed }">
        <!-- 面板头部 -->
        <div class="panel-header">
            <span class="panel-title" v-if="!isCollapsed">边样式管理</span>
            <el-button type="text" class="collapse-btn" @click="toggleCollapse" :title="isCollapsed ? '展开面板' : '收起面板'">
                <i :class="isCollapsed ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'"></i>
            </el-button>
        </div>

        <!-- 面板内容 -->
        <div class="panel-content" v-show="!isCollapsed">
            <!-- 线条类型 -->
            <div class="style-section">
                <div class="section-title">
                    <i class="el-icon-connection"></i>
                    <span>线条类型</span>
                </div>
                <div class="edge-type-grid">
                    <div v-for="type in edgeTypes" :key="type.value" class="edge-type-item"
                        :class="{ active: currentEdgeType === type.value }" @click="setEdgeType(type.value)"
                        :title="type.label">
                        <div class="edge-preview" :class="type.value">
                            <svg width="40" height="20" viewBox="0 0 40 20">
                                <path :d="type.path" :stroke="currentColor" :stroke-width="currentStrokeWidth"
                                    :stroke-dasharray="currentDashArray" fill="none" />
                            </svg>
                        </div>
                        <span class="type-label">{{ type.label }}</span>
                    </div>
                </div>
            </div>

            <!-- 线条样式 -->
            <div class="style-section">
                <div class="section-title">
                    <i class="el-icon-edit"></i>
                    <span>线条样式</span>
                </div>
                <div class="style-controls">
                    <!-- 实线/虚线切换 -->
                    <div class="control-item">
                        <label>线条样式</label>
                        <el-radio-group v-model="strokeStyle">
                            <el-radio label="solid">实线</el-radio>
                            <el-radio label="dashed">虚线</el-radio>
                        </el-radio-group>
                    </div>

                    <!-- 虚线设置 -->
                    <div class="control-item" v-if="strokeStyle === 'dashed'">
                        <label>虚线长度</label>
                        <el-slider v-model="dashLength" :min="1" :max="20" show-input :show-input-controls="false"
                            input-size="mini">
                        </el-slider>
                    </div>
                    <div class="control-item" v-if="strokeStyle === 'dashed'">
                        <label>虚线间距</label>
                        <el-slider v-model="dashGap" :min="1" :max="20" show-input :show-input-controls="false"
                            input-size="mini">
                        </el-slider>
                    </div>


                    <!-- 线条宽度 -->
                    <div class="control-item">
                        <label>线条宽度</label>
                        <el-slider v-model="strokeWidth" :min="1" :max="10" show-input :show-input-controls="false"
                            input-size="mini">
                        </el-slider>
                    </div>

                    <!-- 颜色选择 -->
                    <div class="control-item">
                        <label>线条颜色</label>
                        <div class="color-picker-wrapper">
                            <el-color-picker v-model="strokeColor" show-alpha :predefine="predefineColors">
                            </el-color-picker>
                            <span class="color-value">{{ strokeColor }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 动画控制 -->
            <div class="style-section">
                <div class="section-title">
                    <i class="el-icon-video-play"></i>
                    <span>动画效果</span>
                </div>
                <div class="animation-controls">
                    <!-- 动画开关 -->
                    <div class="control-item">
                        <label>启用动画</label>
                        <el-switch v-model="animationEnabled">
                        </el-switch>
                    </div>

                    <!-- 动画速度 -->
                    <div class="control-item" v-if="animationEnabled">
                        <label>动画速度</label>
                        <el-slider v-model="animationSpeed" :min="0.5" :max="5" :step="0.1" show-input
                            :show-input-controls="false" input-size="mini">
                        </el-slider>
                    </div>
                </div>
            </div>

            <!-- 应用按钮 -->
            <div class="action-buttons">
                <el-button size="small" @click="applyToSelected" type="primary">
                    应用到选中边
                </el-button>
                <el-button size="small" @click="applyToAll">
                    应用到所有边
                </el-button>
            </div>
        </div>
    </div>
</template>

<script>
import LogicFlow from "@logicflow/core"

export default {
    name: 'EdgeStylePanel',
    props: {
        lf: {
            type: LogicFlow,
            required: true
        }
    },
    data() {
        return {
            isCollapsed: false,
            currentEdgeType: 'polyline',
            strokeStyle: 'solid',
            strokeWidth: 2,
            strokeColor: '#409EFF',
            animationEnabled: false,
            animationSpeed: 2,

            // 虚线设置
            dashLength: 5,
            dashGap: 5,

            // 边类型定义
            edgeTypes: [
                {
                    value: 'line',
                    label: '直线',
                    path: 'M 5 10 L 35 10'
                },
                {
                    value: 'polyline',
                    label: '折线',
                    path: 'M 5 10 L 20 10 L 20 15 L 35 15'
                },
                {
                    value: 'bezier',
                    label: '曲线',
                    path: 'M 5 10 Q 20 5 35 10'
                }
            ],

            // 预定义颜色
            predefineColors: [
                '#409EFF',
                '#67C23A',
                '#E6A23C',
                '#F56C6C',
                '#909399',
                '#000000',
                '#FFFFFF'
            ]
        }
    },
    computed: {
        currentColor() {
            return this.strokeColor
        },
        currentStrokeWidth() {
            return this.strokeWidth
        },
        currentDashArray() {
            switch (this.strokeStyle) {
                case 'dashed':
                    return `${this.dashLength},${this.dashGap}`
                default:
                    return ''
            }
        }
    },
    methods: {
        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed
        },


        setEdgeType(type) {
            this.currentEdgeType = type
        },
        // 应用当前样式
        applyCurrentStyles() {
            let obj = {}
            obj[this.currentEdgeType] = {
                stroke: this.strokeColor,
                strokeWidth: this.strokeWidth,
                strokeDasharray: this.currentDashArray
            }
            this.lf.setTheme(obj)
        },

        // 应用到选中的边
        applyToSelected() {
            const selectedElements = this.lf.getSelectElements()
            const edges = selectedElements.edges || []

            if (edges.length === 0) {
                this.$message.warning('请先选择要应用样式的边')
                return
            }

            edges.forEach(edge => {
                this.lf.setProperties(edge.id, {
                    style: {
                        stroke: this.strokeColor,
                        strokeWidth: this.strokeWidth,
                        strokeDasharray: this.currentDashArray
                    }
                })

                if (this.animationEnabled) {
                    this.lf.openEdgeAnimation(edge.id)
                } else {
                    this.lf.closeEdgeAnimation(edge.id)
                }

                // 更新边类型
                this.lf.changeEdgeType(edge.id, this.currentEdgeType)
            })

            this.$message.success(`已应用样式到 ${edges.length} 条边`)
        },

        // 应用到所有边
        applyToAll() {
            const graphData = this.lf.getGraphData()
            const edges = graphData.edges || []

            if (edges.length === 0) {
                this.$message.warning('当前画布中没有边')
                return
            }

            edges.forEach(edge => {
                this.lf.setProperties(edge.id, {
                    style: {
                        stroke: this.strokeColor,
                        strokeWidth: this.strokeWidth,
                        strokeDasharray: this.currentDashArray
                    }
                })

                if (this.animationEnabled) {
                    this.lf.openEdgeAnimation(edge.id)
                } else {
                    this.lf.closeEdgeAnimation(edge.id)
                }

                // 更新边类型
                this.lf.changeEdgeType(edge.id, this.currentEdgeType)
            })

            this.$message.success(`已应用样式到所有 ${edges.length} 条边`)
        },
    }
}
</script>

<style lang="scss" scoped>
.edge-style-panel {
    position: absolute;
    top: 80px;
    right: 10px;
    width: 280px;
    max-height: calc(100% - 100px);
    background-color: #fff;
    border: 1px solid #e6e9ef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 999;
    transition: all 0.3s ease;

    &.collapsed {
        width: 40px;
        overflow: hidden;
    }

    .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid #e6e9ef;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px 8px 0 0;

        .panel-title {
            font-size: 14px;
            font-weight: 600;
            color: #606266;
            margin: 0;
        }

        .collapse-btn {
            padding: 4px;
            min-width: auto;
            height: auto;
            color: #606266;

            &:hover {
                color: #409eff;
                background-color: transparent;
            }

            i {
                font-size: 16px;
            }
        }
    }

    .panel-content {
        padding: 16px;
        max-height: calc(100vh - 120px);
        overflow-y: auto;
    }

    .style-section {
        margin-bottom: 20px;

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 13px;
            font-weight: 600;
            color: #606266;

            i {
                margin-right: 6px;
                color: #409eff;
            }
        }
    }

    .edge-type-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;

        .edge-type-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            border: 1px solid #e6e9ef;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
                border-color: #409eff;
                background-color: #f0f7ff;
            }

            &.active {
                border-color: #409eff;
                background-color: #e6f3ff;

                .type-label {
                    color: #409eff;
                    font-weight: 600;
                }
            }

            .edge-preview {
                margin-bottom: 4px;
            }

            .type-label {
                font-size: 11px;
                color: #606266;
                text-align: center;
            }
        }
    }

    .style-controls,
    .animation-controls {
        .control-item {
            margin-bottom: 16px;

            label {
                display: block;
                font-size: 12px;
                color: #606266;
                margin-bottom: 6px;
                font-weight: 500;
            }

            .color-picker-wrapper {
                display: flex;
                align-items: center;
                gap: 8px;

                .color-value {
                    font-size: 11px;
                    color: #909399;
                    font-family: monospace;
                }
            }
        }
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid #e6e9ef;

        .el-button {
            width: 100%;
            margin-left: 0;
        }
    }
}

// 深度选择器样式
::v-deep {
    .el-slider__runway {
        height: 4px;
    }

    .el-slider__button {
        width: 12px;
        height: 12px;
    }

    .el-color-picker__trigger {
        width: 32px;
        height: 24px;
        border-radius: 4px;
    }

    .el-radio {
        margin-right: 12px;

        .el-radio__label {
            font-size: 12px;
        }
    }

    .el-select {
        width: 100%;

        .el-input__inner {
            height: 28px;
            line-height: 28px;
            font-size: 12px;
        }
    }

    .el-switch {
        .el-switch__core {
            height: 16px;

            &::after {
                width: 12px;
                height: 12px;
            }
        }
    }
}
</style>