import router from '@/router'
import store from '@/store'
import { Loading } from 'element-ui'

import anime from "animejs"

import { customRef } from 'vue'
export const loadingRef = () => customRef((track, trigger) => {
    let loadingCount = 0
    return {
        get() {
            track() // 通知 Vue 跟踪此引用的读取
            return loadingCount > 0
        },
        set(n) {
            if (n) {
                loadingCount++
            } else {
                if (n <= 0) {
                    loadingCount = 0
                } else {
                    loadingCount--
                }
            }
            loadingCount = Math.max(0, loadingCount)
            trigger() // 通知 Vue 触发更新
        }
    }
})


export const loading = loadingRef()

if (router) {
    router.beforeEach(async (to, from, next) => {
        store.dispatch('loading/clearLoading')
        loading.value = 0
        next()
    })
}

const loadingInstance2 = {
    delayClose: function () {
        store.dispatch('loading/setLoding', false)
    }
}

const loadingInstance3 = {
    delayClose: function () {
        loading.value = false
    }
}

function loading3() {
    loading.value = true
    return loadingInstance3
}


function loading1() {
    store.dispatch('loading/setLoding', true)
    return loadingInstance2
}

function loading2() {
    const loadingInstance = Loading.service({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.6)',
        customClass: 'custom-loading'
    })


    loadingInstance.minDuration = 300
    loadingInstance.startTime = Date.now()
    loadingInstance.delayClose = function (delay = 100) {

        let time = Date.now() - loadingInstance.startTime
        loadingInstance.loadingTimeout && clearTimeout(loadingInstance.loadingTimeout)
        loadingInstance.finallyClose && clearTimeout(loadingInstance.finallyClose)
        loadingInstance.loadingTimeout = setTimeout(() => {
            anime({
                targets: '.el-loading-mask.custom-loading',
                opacity: 0,
                duration: 400,
                easing: 'easeInOutQuad',
                complete: function () {
                    loadingInstance.close()
                }
            })

            loadingInstance.finallyClose = setTimeout(() => {
                loadingInstance.close()
            }, 450);


        }, Math.max(loadingInstance.minDuration - time, delay));
    }

    return loadingInstance
}

window.loadingInstanceMode = 1
export function createLoadingInstance(mode) {

    if (window.loadingInstanceMode === 3) {
        return loading3()
    }

    return window.loadingInstanceMode === 1 ? loading1() : loading2()
}

