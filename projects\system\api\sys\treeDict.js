import request from "@/utils/request";


// 获取数据字典列表
export function getDictList(params) {
  return request({
    url: 'system/sys/dict/scope/list',
    method: 'get',
    params
  })
}

// 获取数据字典项列表
export function getDictItemList(params) {
  return request({
    url: 'system/sys/dict/item/list',
    method: 'get',
    params,
    // 不显示加载提示
    loadingDisabled: true
  })
}

// 新增数据字典
export function addDict(data) {
  return request({
    url: 'system/sys/dict',
    method: 'post',
    data,
    hideExceptionPrompt: true
  })
}

// 修改数据字典
export function updateDict(data) {
  return request({
    url: 'system/sys/dict',
    method: 'put',
    data,
    hideExceptionPrompt: true
  })
}

// 删除数据字典
export function deleteDict(id) {
  return request({
    url: `system/sys/dict/${id}`,
    method: 'delete'
  })
}

// 新增数据字典项 - 支持批量
export function addDictItem(...data) {
  return request({
    url: 'system/sys/dict/item',
    method: 'post',
    data,
    hideExceptionPrompt: true
  })
}

// 修改数据字典项 - 支持批量
export function updateDictItem(...data) {
  return request({
    url: 'system/sys/dict/item',
    method: 'put',
    data,
    hideExceptionPrompt: true
  })
}

// 删除数据字典项 - 支持批量
export function deleteDictItem(ids) {
  return request({
    url: `system/sys/dict/item/${Array.isArray(ids) ? ids.join(',') : ids}`,
    method: 'delete'
  })
}
