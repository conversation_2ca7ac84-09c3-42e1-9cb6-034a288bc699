import request from '@/utils/request'

const api = CONSTANT.AUTH

// 获取应用列表
export function getAppList(params) {
  return request({
    url: api + '/app/list',
    method: 'get',
    params
  })
}

// 获取单个应用详情
export function getAppDetail(id) {
  return request({
    url: api + `/app`,
    method: 'get',
    params: { id }
  })
}

// 创建新应用
export function createApp(data) {
  return request({
    url: api + '/app',
    method: 'post',
    data
  })
}

// 更新应用
export function updateApp(id, data) {
  return request({
    url: api + `/app`,
    method: 'put',
    data
  })
}
//删除应用
export function deleteApp(id) {
  return request({
    url: api + `/app/` + id,
    method: 'delete',
  })
}
// 获取应用授权账号列表
export function getAppAccountList(params) {
  return request({
    url: api + '/app/account/list',
    method: 'get',
    params
  })
}

// 更新授权账号状态
export function updateAccountStatus(data) {
  return request({
    url: api + '/app/account/status',
    method: 'put',
    data
  })
}

// 删除授权账号
export function deleteAccount(id) {
  return request({
    url: api + '/app/account',
    method: 'delete',
    params: { id }
  })
}