<template>
  <div>
    <AutoPage :options="options" :formRule="options.formRule">
      <template #tablesss="rows">
        <el-button size="mini" type="text" icon="el-icon-thumb" @click="click(rows.row)">详情</el-button>
      </template>
    </AutoPage>
  </div>
</template>
<script>
import AutoPage from "@/components/auto-page/AutoPage";
import options from "./options";
export default {
  components: { AutoPage },
  computed: {},
  data() {
    return {
      options: null,
    };
  },
  created() {
    this.options = options;
  },
  methods: {
    click(row) {
      this.$router.push({
        path: "/sys/dict/item",
        query: {
          id: row.id,
        },
      });
    },
  },
};
</script>
