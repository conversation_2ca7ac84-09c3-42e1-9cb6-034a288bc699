<template>
  <div class="app-add">
    <div class="page-header">
      <div class="header-content">
        <el-page-header @back="$router.push('/integration/sso-apps/list')">
          <template #content>
            <div class="page-title">
              <i class="el-icon-plus"></i>
              <span>新增单点应用</span>
            </div>
          </template>
        </el-page-header>
        <div class="header-desc">
          配置单点登录应用信息，支持OAuth、SAML和LDAP等多种认证方式
        </div>
      </div>
    </div>

    <div class="edit-content">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <div class="tab-content">
            <el-form ref="basicForm" :model="basicInfo" :rules="basicRules" label-width="120px" class="config-form">
              <el-form-item label="应用图标" prop="icon" class="upload-item">
                <div class="icon-upload">
                  <el-upload class="avatar-uploader" action="/api/upload" :show-file-list="false"
                    :on-success="handleIconSuccess" :before-upload="beforeIconUpload">
                    <div v-if="basicInfo.icon" class="icon-preview"
                      :style="{ backgroundColor: basicInfo.color || '#409EFF' }">
                      <img :src="basicInfo.icon" class="uploaded-icon">
                    </div>
                    <div v-else class="icon-placeholder" :style="{ backgroundColor: basicInfo.color || '#409EFF' }">
                      <i class="el-icon-plus"></i>
                      <span>上传图标</span>
                    </div>
                  </el-upload>
                  <div class="icon-tips">
                    <p>建议尺寸：128x128px</p>
                    <p>支持格式：PNG、JPG</p>
                    <p>大小不超过2M</p>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="应用颜色" prop="color">
                <el-color-picker v-model="basicInfo.color" :predefine="predefineColors" show-alpha
                  class="color-picker" />
                <span class="color-tip">选择应用主题色，将用于图标背景</span>
              </el-form-item>

              <el-form-item label="应用名称" prop="name">
                <el-input v-model="basicInfo.name" placeholder="请输入应用名称" class="custom-input" />
              </el-form-item>
              <el-form-item label="应用描述" prop="description">
                <el-input type="textarea" v-model="basicInfo.description" :rows="3" placeholder="请输入应用描述"
                  class="custom-textarea" />
              </el-form-item>

              <el-form-item label="应用编码" prop="code">
                <el-input v-model="basicInfo.code" placeholder="请输入应用编码" class="custom-input" />
              </el-form-item>

              <el-form-item label="应用地址" prop="address">
                <el-input v-model="basicInfo.address" placeholder="请输入应用访问地址" class="custom-input" />
              </el-form-item>

              <el-form-item label="启用状态" prop="enabled">
                <el-switch v-model="basicInfo.enabled" />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="认证配置" name="auth">
          <div class="tab-content">
            <auth-config ref="authConfig" />
          </div>
        </el-tab-pane>
      </el-tabs>

      <div class="form-actions">
        <el-button @click="$router.go(-1)" class="cancel-button">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving" class="save-button">
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import AuthConfig from './components/auth-config.vue'
import { createApp } from '@system/api/integration/sso-app'

export default {
  name: 'AppAdd',
  components: {
    AuthConfig
  },
  data() {
    return {
      activeTab: 'basic',
      saving: false,
      basicInfo: {
        code: '',
        name: '',
        icon: '',
        description: '',
        address: '',
        enabled: true,
        color: '#409EFF',
        ssoConfigJson: {}
      },
      basicRules: {
        code: [
          { required: true, message: '请输入应用编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入应用地址', trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '不能超过200个字符', trigger: 'blur' }
        ]
      },
      predefineColors: [
        '#409EFF',
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
        '#1890FF',
        '#722ED1',
        '#13C2C2',
        '#52C41A'
      ]
    }
  },
  methods: {
    async handleSave() {
      try {
        this.saving = true
        await this.$refs.basicForm.validate()

        if (this.$refs.authConfig) {
          const authConfig = await this.$refs.authConfig.validate()

          // 构建新应用数据
          const newApp = {
            ...this.basicInfo,
            protocol: authConfig.authType,
            ssoConfigUniqueId: authConfig.ssoConfigUniqueId,
            ssoConfigJson: {
              clientId: authConfig.clientId,
              clientSecret: authConfig.clientSecret,
              redirectUri: authConfig.redirectUri,
              verifyMode: authConfig.verifyMode
            }
          }

          await createApp(newApp)

          this.$message.success('创建成功')
          this.$router.push('/integration/sso-apps/list')
        }
      } catch (error) {
        console.error(error)
        this.$message.error('保存失败，请检查表单')
      } finally {
        this.saving = false
      }
    },
    handleIconSuccess(res, file) {
      // 处理上传成功
      this.basicInfo.icon = URL.createObjectURL(file.raw)
    },
    beforeIconUpload(file) {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('上传图标只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图标大小不能超过 2MB!')
      }
      return isImage && isLt2M
    }
  }
}
</script>

<style lang="scss" scoped>
.app-add {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 48px);

  .page-header {
    margin-bottom: 24px;
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .header-content {
      ::v-deep .el-page-header {
        .el-page-header__left {
          .el-icon-back {
            font-size: 18px;
            color: #909399;
            margin-right: 20px;
          }

          .el-page-header__title {
            font-size: 14px;
            color: #909399;
          }
        }

        .el-page-header__content {
          font-size: 16px;
          color: #303133;
        }
      }

      .header-desc {
        margin-top: 16px;
        color: #909399;
        font-size: 14px;
        padding-left: 32px;
      }
    }

    .page-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 500;
      color: #303133;

      i {
        margin-right: 8px;
        font-size: 20px;
        color: #409EFF;
      }
    }
  }

  .edit-content {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .custom-tabs {
      ::v-deep .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #f0f0f0;
      }

      ::v-deep .el-tabs__item {
        font-size: 15px;
        padding: 0 24px;
        height: 48px;
        line-height: 48px;

        &.is-active {
          font-weight: 500;
        }
      }

      ::v-deep .el-tabs__active-bar {
        height: 3px;
        border-radius: 3px;
      }
    }

    .tab-content {
      padding: 32px 0;
    }

    .config-form {
      max-width: 680px;
      margin: 0 auto;

      ::v-deep .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }

      .custom-input,
      .custom-textarea {

        ::v-deep .el-input__inner,
        ::v-deep .el-textarea__inner {
          border-radius: 8px;
          border-color: #dcdfe6;
          padding: 12px;
          font-size: 14px;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
          }
        }
      }
    }
  }

  .form-actions {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;

    .cancel-button,
    .save-button {
      min-width: 120px;
      padding: 12px 24px;
      font-size: 14px;
      border-radius: 8px;
      transition: all 0.3s;
    }

    .cancel-button {
      margin-right: 16px;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    .save-button {
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .upload-item {
    .icon-upload {
      display: flex;
      align-items: flex-start;
      gap: 24px;

      .avatar-uploader {

        .icon-preview,
        .icon-placeholder {
          width: 100px;
          height: 100px;
          border-radius: 16px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s;
          color: white;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
          }
        }

        .icon-preview {
          overflow: hidden;

          .uploaded-icon {
            width: 60%;
            height: 60%;
            object-fit: contain;
          }
        }

        .icon-placeholder {
          i {
            font-size: 24px;
            margin-bottom: 8px;
          }

          span {
            font-size: 13px;
          }
        }
      }

      .icon-tips {
        padding: 12px 16px;
        background: #f8f9fb;
        border-radius: 8px;

        p {
          margin: 0;
          font-size: 12px;
          color: #909399;
          line-height: 1.8;
        }
      }
    }
  }

  .color-picker {
    vertical-align: middle;
  }

  .color-tip {
    margin-left: 12px;
    font-size: 13px;
    color: #909399;
  }
}
</style>