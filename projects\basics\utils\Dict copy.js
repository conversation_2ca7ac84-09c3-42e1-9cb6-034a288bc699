
// import Vue from 'vue'
// import request from '@/utils/request'
// const NodeCache = require('node-cache');
// const cache = new NodeCache({ stdTTL: 60 * 3 });

// class Dict {

//     init(dicts) {
//         return Promise.all(dicts.map(m => this.getItemByDataKey(m)))
//     }

//     //数据字典
//     getItemByDataKey(dataKey) {
//         return new Promise(resolve => {
//             let url = CONSTANT.SYSTEM + '/sys/dict/item/keys?keys=' + dataKey
//             let cachedData = cache.get(url);
//             if (cachedData) {
//                 return resolve(cachedData)
//             }
//             request({
//                 url,
//                 method: 'get',
//                 loadingDisabled: true
//             }).then(response => {
//                 resolve(response)
//             }).catch(err => {
//                 resolve({})
//             })
//         })
//     }
// }

// Vue.use({
//     install(Vue, options) {
//         Vue.mixin({
//             data() {
//                 if (this.$options === undefined || this.$options.dicts === undefined || this.$options.dicts === null) {
//                     return {}
//                 }

//                 let dict = new Dict()
//                 dict.owner = this
//                 let dictArr = this.$options.dicts.join(',').split(',') || []
//                 dictArr.forEach(dk => {
//                     dict[dk] = []
//                 })
//                 return {
//                     dict
//                 }
//             },
//             created() {
//                 if (!(this.dict instanceof Dict)) {
//                     return
//                 }
//                 this.dict.init(this.$options.dicts)
//                     .then(arr => {
//                         let dict = Object.assign({}, ...arr)
//                         Object.entries(dict).forEach(ens => {
//                             let d = {}
//                             d[ens[0]] = ens[1]
//                             cache.set(`${CONSTANT.SYSTEM}/sys/dict/item/keys?keys=${ens[0]}`, d);
//                         });
//                         return dict
//                     })
//                     .then((dict) => {
//                         Object.assign(this.dict, dict)
//                         this.$nextTick(() => {
//                             if (this.$options.onDictReady instanceof Function) {
//                                 this.$options.onDictReady.call(this, this.dict)
//                             }
//                         })
//                     })
//             },
//         })
//     }
// })



