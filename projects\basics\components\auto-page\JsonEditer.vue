<template>
    <div class="flow-container">
        <div class="route-management">
            <vue-json-editor v-model="procedure" :show-btns="false" mode="code" lang="zh" :disabled="disabled"
                @has-error="JsonError" class="vue-json-editor" />
        </div>
    </div>
</template>

<script>
import VueJsonEditor from 'vue-json-editor'
import { Message } from 'element-ui'
import Emitter from 'element-ui/src/mixins/emitter';

export default {
    components: { VueJsonEditor },
    mixins: [Emitter],
    data() {
        return {
            procedure: ''
        }
    },
    model: {
        prop: 'value',
        event: 'value'
    },
    computed: {
        procedureVals: {
            get() {
                return this.value || null
            },
            set(val) {
                return val
            }
        }
    },
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        value: [String, Object],
    },
    watch: {
        procedureVals: {
            handler(val) {
                this.procedure = val
            },
            deep: true,
            immediate: true
        },
        procedure: {
            handler(val) {
                this.$emit('value', val)
                this.$emit('change', val)
                this.dispatch('ElFormItem', 'el.form.change', [val]);
            },
            deep: true,
            immediate: true
        },
    },
    methods: {
        JsonError(e) {
            Message({
                message: e,
                type: 'error',
                duration: 5 * 1000
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.flow-container {

    .route-management {

        ::v-deep .jsoneditor-poweredBy {
            display: none !important;
        }

        ::v-deep .vue-json-editor {
            height: 30vh !important;
            position: relative;
        }

        ::v-deep .jsoneditor-vue {
            height: 100% !important;
        }
    }


    .el-dialog__wrapper {
        height: 100vh !important;

        .el-dialog {
            margin-top: 2vh !important;
            height: 90vh !important;

            .route-management {
                height: 74vh !important;

                .vue-json-editor {
                    height: 74vh !important;
                }

                .jsoneditor-vue {
                    height: 100% !important;
                }
            }
        }
    }
}
</style>