
import { h } from '@logicflow/core'
import { VueNodeView, VueNodeModel } from '@logicflow/vue-node-registry'
import { action, observable } from 'mobx'


export class BaseVueNodeView extends VueNodeView {

  getAnchorShape(anchorData) {
    const { x, y, type } = anchorData

    const { model } = this.props

    let show = 'block'
    if ((!model.inAnchor && type === 'incomming')
      || (!model.outAnchor && type === 'outgoing')) {
      show = 'none'
    }

    return h('rect', {
      width: 8,
      height: 8,
      display: show,
      className: `custom-anchor ${type === 'incomming' ? 'incomming-anchor' : 'outgoing-anchor'}`,
      transform: `translate(${x - 4}, ${y - 4})`,
      onmouseenter: (e) => {
        const rect = e.target;
        rect.setAttribute('style', 'transition:0.3s')
        rect.setAttribute('width', 12)
        rect.setAttribute('height', 12)
        rect.setAttribute('transform', `translate(${x - 6}, ${y - 6})`)
      },
      onmouseout: (e) => {
        const rect = e.target;
        rect.setAttribute('width', 8)
        rect.setAttribute('height', 8)
        rect.setAttribute('transform', `translate(${x - 4}, ${y - 4})`)
      }
    })
  }
}

export class BaseVueNodeModel extends VueNodeModel {
  @observable inAnchor = true
  @observable outAnchor = true
  @observable propertyPanel = true

  setAttributes() {
    super.setAttributes()
    this.width = this.properties?.width || 160
    this.height = this.properties?.height || 50
    this.inAnchor = this.properties.inAnchor !== false
    this.outAnchor = this.properties.outAnchor !== false
    this.propertyPanel = this.properties.propertyPanel
    this.text.editable = false
    this.sourceRules.push({
      message: '只允许从右边的锚点连出',
      validate: (sourceNode, targetNode, sourceAnchor, targetAnchor) => {
        return targetAnchor.type === 'incomming' && targetNode.inAnchor
      }
    })
  }
  @action
  setInAnchor(inAnchor) {
    this.inAnchor = inAnchor !== false
  }

  @action
  setOutAnchor(outAnchor) {
    this.outAnchor = outAnchor !== false
  }
  @action
  setPropertyPanel(propertyPanel) {
    this.propertyPanel = propertyPanel
  }


  getOutlineStyle() {
    const style = super.getOutlineStyle()
    style.stroke = 'none'
    style.hover.stroke = 'none'
    return style
  }
  getDefaultAnchor() {
    const { id, x, y, width, height } = this
    const anchors = []
    anchors.push({
      x: x - width / 2,
      y,
      id: `${id}_incomming`,
      edgeAddable: false,
      type: 'incomming'
    })
    anchors.push({
      x: x + width / 2,
      y,
      id: `${id}_outgoing`,
      type: 'outgoing'
    })
    return anchors
  }
}

