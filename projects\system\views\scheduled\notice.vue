<template>
    <el-row class="scheduled-main">
      <el-col :span="6" class="scheduled-col">
        <notice-tree class="scheduled-item" :selected.sync="task" :types="types" :statuses="statuses" />
      </el-col>
      <el-col :span="18" class="scheduled-col">
        <notice-detail v-model="task" :statuses="statuses" />
        <notice-list class="scheduled-item" v-model="task.noticeId" />
      </el-col>
    </el-row>
  </template>
  
  <script>
  import noticeTree from "./notice/noticeTree";
  import noticeDetail from "./notice/noticeDetail";
  import noticeList from "./notice/noticeList";
  import api from "@system/api/scheduled";
  export default {
    name: "notice",
    components: {noticeTree, noticeDetail, noticeList},
    data() {
      return {
        groups: [],
        task: {},
        types: [],
        statuses: []
      }
    },
    mounted() {
      api.task.types().then(res => this.types = res)
      api.task.statuses().then(res => this.statuses = res)
    },
    methods: {
    }
  }
  </script>
  
  <style scoped>
  .scheduled-main {
    padding: 0 10px;
    height: 95vh;
  }
  .scheduled-col{
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .scheduled-item{
    flex: 1;
  }
  </style>