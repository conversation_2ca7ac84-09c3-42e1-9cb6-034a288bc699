import './init.root.config.js'

import { vue } from '@/main.js'
import "./root.scss"


vue.$store.dispatch('settings/changeSetting', {
    key: 'title',
    value: '后台管理系统'
})
// vue.$store.dispatch('settings/setReadOnly', true)

import { replaceImport } from "@/router"
replaceImport('/login', import('@authlogin/views/index.vue'))
replaceImport('/auth', import('@authlogin/views/auth.vue')).end()

import redirect from '@/views/redirect.vue'
import Layout from '@/layout'

vue.$router.moduleRoutes([
    {
        path: '/',
        component: () => import('./views/home/<USER>')
    },

    {
        path: '/profile/index',
        component: () => import('@system/views/sys/profile/index')
    },
    {
        path: '/notice/center',
        component: () => import('@system/views/notice/center/index.vue')
    },

    {
        path: '/integration/integration-app/proxy-app',
        name: 'ProxyApp',
        component: () => import('@system/views/integration/integration-app/proxy/proxy-app.vue'),
        hidden: true,
        meta: {
            title: '代理应用',
            noCache: true
        }
    },
     {
        path: '/integration/integration-app/flow-app',
        name: 'FlowApp',
        component: () => import('@system/views/integration/integration-app/flow/flow-app.vue'),
        hidden: true,
        meta: {
            title: '代理应用',
            noCache: true
        }
    },
    {
        path: '/integration/integration-connector/http/detail',
        name: 'HTTPConnectorDetail',
        component: () => import('@system/views/integration/integration-connector/http/http-connector-detail.vue'),
        hidden: true,
        meta: {
            title: 'http',
            noCache: true
        }
    },
    
    {
        path: '/flow',
        name: 'flowTest',
        component: () => import('@system/views/integration/integration-app/flow/FlowPanel.vue'),
        hidden: true,
        meta: {
            title: 'flow测试',
            noCache: true
        }
    },



    // {
    //     path: '/profile',
    //     component: Layout,
    //     children: [
    //         {
    //             path: 'index',
    //             name: 'index',
    //             component: () => import('@system/views/sys/profile/index'),
    //             meta: { title: '个人中心', icon: 'tree', external: true },
    //         }
    //     ],
    //     hidden: true
    // },

    {
        path: '/:path',
        component: redirect
    }
])