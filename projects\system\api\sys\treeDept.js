import request from '@/utils/request'

const sysDeptApi = CONSTANT.SYSTEM + '/sys/dept'
const sysAccountApi = CONSTANT.SYSTEM + '/sys/account'

// 获取部门树列表
export function getDeptTreeList() {
  return request({
    url: `${sysDeptApi}/treeList`,
    method: 'get'
  })
}

// 获取部门信息
export function getDeptInfo(id) {
  return request({
    url: `${sysDeptApi}`,
    method: 'get',
    params: { id }
  })
}

// 获取用户信息
export function getUserInfo(id) {
  return request({
    url: `${sysAccountApi}`,
    method: 'get',
    params: { id }
  })
}

// 批量获取用户信息
export function getUserList(ids) {
  return request({
    url: `${sysAccountApi}/list`,
    method: 'get',
    params: {
      'enums[id]': ids.join(',')
    }
  })
}

// 获取部门用户列表
export function getDeptUsers(deptId) {
  return request({
    url: `${sysAccountApi}/dept/${deptId}`,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: sysDeptApi,
    method: 'post',
    data
  })
}

// 编辑部门
export function updateDept(data) {
  return request({
    headers: {
      // 后台管理 覆盖更新 避免null值无法更新
      'X-Cover': 'true'
    },
    url: sysDeptApi,
    method: 'put',
    data
  })
}

// 删除部门，支持批量删除
export function deleteDept(ids) {
  return request({
    url: `${sysDeptApi}/${ids}`,
    method: 'delete'
  })
}

/**
 * 设置部门关联的资源
 * @param {Number} deptId 部门ID
 * @param {Array} resourceIds 资源ID集合
 * @returns {Promise}
 */
export function setDeptResources(deptId, resourceIds) {
  return request({
    url: `${sysDeptApi}/${deptId}/resources`,
    method: 'post',
    data: resourceIds
  })
}

/**
 * 设置部门关联的权限
 * @param {Number} deptId 部门ID
 * @param {Array} permissions 权限标识集合
 * @returns {Promise}
 */
export function setDeptPermissions(deptId, permissions) {
  return request({
    url: `${sysDeptApi}/${deptId}/permissions`,
    method: 'post',
    data: permissions
  })
} 