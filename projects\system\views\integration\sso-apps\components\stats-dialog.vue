<template>
  <el-dialog
    :title="`${appName} - 访问统计`"
    :visible.sync="dialogVisible"
    width="900px"
    class="stats-dialog"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="stats-content">
      <!-- 时间范围选择 -->
      <div class="filter-bar">
        <div class="filter-left">
          <span class="label">统计时间:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="handleDateChange"
            value-format="yyyy-MM-dd"
          />
        </div>
        <div class="filter-right">
          <el-button 
            size="small" 
            type="primary"
            icon="el-icon-refresh"
            :loading="refreshing"
            @click="handleRefresh"
          >
            刷新数据
          </el-button>
        </div>
      </div>

      <!-- 数据概览卡片 -->
      <el-row :gutter="20" class="stat-cards">
        <el-col :span="8" v-for="(card, index) in statCards" :key="index">
          <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <div class="stat-card-content">
              <div class="stat-info">
                <span class="label">{{ card.label }}</span>
                <span class="value">{{ card.value }}</span>
                <span class="compare">
                  较上期
                  <span :class="['trend', card.trend]">
                    {{ card.trend === 'up' ? '+' : '-' }}{{ card.percentage }}%
                  </span>
                </span>
              </div>
              <div class="stat-icon" :class="card.type">
                <i :class="card.icon"></i>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 访问趋势图表 -->
      <el-card class="chart-card trend-chart" shadow="never">
        <div slot="header">
          <div class="chart-header">
            <span class="title">访问趋势</span>
            <div class="actions">
              <el-radio-group v-model="timeUnit" size="small">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <div ref="trendChart" style="height: 300px"></div>
        </div>
      </el-card>

      <!-- 访问来源分布 -->
      <el-row :gutter="20" class="distribution-charts">
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <div slot="header">
              <div class="chart-header">
                <span class="title">设备分布</span>
                <div class="total">
                  总访问量: <span>{{ totalDeviceVisits }}</span>
                </div>
              </div>
            </div>
            <div ref="deviceChart" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <div slot="header">
              <div class="chart-header">
                <span class="title">地域分布</span>
                <div class="total">
                  覆盖地区: <span>{{ totalRegions }}</span>
                </div>
              </div>
            </div>
            <div ref="regionChart" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'StatsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    appName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data() {
    return {
      dateRange: [],
      timeUnit: 'day',
      refreshing: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      statCards: [
        {
          label: '总访问量',
          value: '125,789',
          trend: 'up',
          percentage: 15.8,
          type: 'visits',
          icon: 'el-icon-view'
        },
        {
          label: '活跃用户',
          value: '45,623',
          trend: 'up',
          percentage: 12.3,
          type: 'users',
          icon: 'el-icon-user'
        },
        {
          label: '平均响应时间',
          value: '238ms',
          trend: 'down',
          percentage: 8.5,
          type: 'performance',
          icon: 'el-icon-timer'
        }
      ],
      charts: {
        trend: null,
        device: null,
        region: null
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    totalDeviceVisits() {
      return '2,847'
    },
    totalRegions() {
      return '31个'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.initCharts()
        })
      }
    },
    timeUnit() {
      this.updateTrendChart()
    }
  },
  methods: {
    handleDateChange() {
      this.updateCharts()
    },
    async handleRefresh() {
      this.refreshing = true
      try {
        await this.updateCharts()
        this.$message.success('数据已更新')
      } catch (error) {
        this.$message.error('数据更新失败')
      } finally {
        this.refreshing = false
      }
    },
    initCharts() {
      // 只初始化趋势图表，因为其他图表使用 Highcharts
      this.charts.trend = echarts.init(this.$refs.trendChart)
      
      this.updateCharts()
      
      window.addEventListener('resize', this.handleResize)
    },
    updateCharts() {
      this.updateTrendChart()
      this.$nextTick(() => {
        // 确保 DOM 已更新后再初始化 Highcharts
        this.updateDeviceChart()
        this.updateRegionChart()
      })
    },
    updateTrendChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#eee',
          borderWidth: 1,
          textStyle: {
            color: '#666'
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '访问量',
            type: 'line',
            smooth: true,
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            areaStyle: {
              opacity: 0.1,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#409EFF'
                },
                {
                  offset: 1,
                  color: 'rgba(64,158,255,0.1)'
                }
              ])
            },
            lineStyle: {
              width: 3,
              color: '#409EFF'
            },
            itemStyle: {
              color: '#409EFF',
              borderWidth: 2
            }
          }
        ]
      }
      this.charts.trend.setOption(option)
    },
    updateDeviceChart() {
      const options = {
        chart: {
          type: 'pie',
          options3d: {
            enabled: true,
            alpha: 45,
            beta: 0
          }
        },
        title: {
          text: null
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            depth: 35,
            dataLabels: {
              enabled: true,
              format: '{point.name}'
            },
            innerSize: '60%'
          }
        },
        series: [{
          name: '设备分布',
          data: [
            {
              name: '桌面端',
              y: 1048,
              color: '#409EFF'
            },
            {
              name: '移动端',
              y: 735,
              color: '#67C23A'
            },
            {
              name: '平板',
              y: 580,
              color: '#E6A23C'
            },
            {
              name: '其他',
              y: 484,
              color: '#909399'
            }
          ]
        }]
      }

      window.Highcharts.chart(this.$refs.deviceChart, options)
    },
    updateRegionChart() {
      const options = {
        chart: {
          type: 'pie',
          options3d: {
            enabled: true,
            alpha: 45,
            beta: 0
          }
        },
        title: {
          text: null
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            depth: 35,
            dataLabels: {
              enabled: true,
              format: '{point.name}'
            },
            innerSize: '60%'
          }
        },
        series: [{
          name: '地域分布',
          data: [
            {
              name: '华东',
              y: 1048,
              color: '#409EFF'
            },
            {
              name: '华北',
              y: 735,
              color: '#67C23A'
            },
            {
              name: '华南',
              y: 580,
              color: '#E6A23C'
            },
            {
              name: '西部',
              y: 484,
              color: '#F56C6C'
            },
            {
              name: '其他',
              y: 300,
              color: '#909399'
            }
          ]
        }]
      }

      window.Highcharts.chart(this.$refs.regionChart, options)
    },
    handleResize() {
      // 只处理 ECharts 实例的 resize
      if (this.charts.trend) {
        this.charts.trend.resize()
      }
      // Highcharts 会自动处理 resize
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    // 只需要销毁 ECharts 实例
    if (this.charts.trend) {
      this.charts.trend.dispose()
    }
  }
}
</script>

<style lang="scss" scoped>
.stats-dialog {
  ::v-deep .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }

  ::v-deep .el-dialog__header {
    padding: 20px 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }
  }

  ::v-deep .el-dialog__body {
    padding: 24px;
  }

  .stats-content {
    .filter-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .filter-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .label {
          font-size: 14px;
          color: #606266;
        }
      }
    }

    .stat-cards {
      margin-bottom: 24px;

      .stat-card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .stat-info {
          .label {
            display: block;
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
          }

          .value {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            font-family: 'DIN Alternate', -apple-system, BlinkMacSystemFont, sans-serif;
            margin-bottom: 8px;
          }

          .compare {
            font-size: 13px;
            color: #909399;

            .trend {
              &.up {
                color: #67C23A;
              }
              &.down {
                color: #F56C6C;
              }
            }
          }
        }

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 24px;
            color: white;
          }

          &.visits {
            background: linear-gradient(135deg, #409EFF, #007AFF);
          }

          &.users {
            background: linear-gradient(135deg, #67C23A, #4CAF50);
          }

          &.performance {
            background: linear-gradient(135deg, #F56C6C, #E64A19);
          }
        }
      }
    }

    .chart-card {
      margin-bottom: 24px;

      &.trend-chart {
        margin-bottom: 24px;
      }

      ::v-deep .el-card__header {
        padding: 16px 20px;
        border-bottom: 1px solid #EBEEF5;
      }

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }

        .total {
          font-size: 14px;
          color: #909399;

          span {
            color: #303133;
            font-weight: 500;
            margin-left: 4px;
          }
        }
      }

      .chart-container {
        padding: 20px;
      }
    }

    .distribution-charts {
      margin-bottom: 0;
    }
  }
}
</style> 