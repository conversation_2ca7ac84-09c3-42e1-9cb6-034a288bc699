<template>
  <el-popover placement="bottom-start" title="添加流程节点" width="400" trigger="click">
    <div slot="title"></div>
    <div class="node-select">
      <div @click="addApprovalNode">
        <i class="el-icon-s-check" style="color:rgb(255, 148, 62);"></i>
        <span>审批人</span>
      </div>
      <div @click="addTaskNode">
        <i class="el-icon-s-claim" style="color:#E6B039;"></i>
        <span>办理人</span>
      </div>
      <div @click="addCcNode">
        <i class="el-icon-s-promotion" style="color:rgb(50, 150, 250);"></i>
        <span>抄送人</span>
      </div>
      <div @click="addConditionsNode">
        <i class="el-icon-share" style="color:rgb(21, 188, 131);"></i>
        <span>条件分支</span>
      </div>
      <div @click="addConcurrentsNode">
        <i class="el-icon-s-operation" style="color:#718dff;"></i>
        <span>并行分支</span>
      </div>
      <div @click="addInclusivesNode">
        <i class="el-icon-connection" style="color:#345DA2;"></i>
        <span>包容分支</span>
      </div>
      <div @click="addSubProcNode" v-if="!isSubProc">
        <i class="el-icon-money" style="color:#9274E7;"></i>
        <span>子流程</span>
      </div>
      <div @click="addDelayNode">
        <i class="el-icon-time" style="color:#f25643;"></i>
        <span>延迟等待</span>
      </div>
      <div @click="addTriggerNode">
        <i class="el-icon-set-up" style="color:#15BC83;"></i>
        <span>触发器</span>
      </div>
    </div>
    <el-button icon="el-icon-plus" slot="reference" type="primary" size="small" circle></el-button>
  </el-popover>
</template>

<script>
export default {
  name: "InsertButton",
  components: {},
  data() {
    return {}
  },
  computed:{
    selectedNode(){
       this.$store.state.bpm.selectedNode
    },
    isSubProc(){
      return  this.$store.state.bpm.design.isSubProc || false;
    },
  },
  methods: {
    addApprovalNode(){
      this.$emit('insertNode', "APPROVAL")
    },
    addCcNode(){
      this.$emit('insertNode', "CC")
    },
    addDelayNode(){
      this.$emit('insertNode', "DELAY")
    },
    addInclusivesNode(){
      this.$emit('insertNode', "INCLUSIVES")
    },
    addTaskNode(){
      this.$emit('insertNode', "TASK")
    },
    addConditionsNode(){
      this.$emit('insertNode', "CONDITIONS")
    },
    addConcurrentsNode(){
      this.$emit('insertNode', "CONCURRENTS")
    },
    addTriggerNode(){
      this.$emit('insertNode', "TRIGGER")
    },
    addSubProcNode(){
      this.$emit('insertNode', "SUBPROC")
    }
  }
}
</script>

<style lang="scss" scoped>
.node-select{
  display: flex;
  flex-wrap: wrap;
  div{
    display: flex;
    align-items: center;
    margin: 5px 5px;
    cursor: pointer;
    padding: 6px 10px;
    border: 1px solid #F8F9F9;
    background-color: #F8F9F9;
    border-radius: 10px;
    width: 100px;
    position: relative;
    span{
      margin-left: 5px;
    }
    &:hover{
      background-color: #fff;
      box-shadow: 0 0 8px 2px #d6d6d6;
    }
    i{
      font-size: 23px;
      padding: 5px;
      border: 1px solid #dedfdf;
      border-radius: 14px;
    }
  }
}
</style>
