<template>
  <div class="script-container">
    <div class="page-header">
      <div class="header-title">
        <h2>新增脚本</h2>
        <div class="back-button" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          <span>返回脚本列表</span>
        </div>
      </div>
    </div>

    <el-form 
      ref="scriptForm" 
      :model="form" 
      :rules="rules"
      label-width="120px"
      class="script-form"
    >
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="脚本ID" prop="scriptId">
              <el-input 
                v-model="form.scriptId"
                placeholder="请输入脚本ID"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="脚本名称" prop="scriptName">
              <el-input 
                v-model="form.scriptName"
                placeholder="请输入脚本名称"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="所属应用" prop="application">
              <el-select 
                v-model="form.application" 
                placeholder="请选择或输入所属应用"
                filterable
                allow-create
                default-first-option
                class="full-width-select"
              >
                <el-option
                  v-for="app in applicationList"
                  :key="app"
                  :label="app"
                  :value="app"
                >
                  <i class="el-icon-s-platform" style="color: #409EFF"></i>
                  <span style="margin-left: 8px">{{ app }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="脚本类型" prop="scriptType">
              <el-select 
                v-model="form.scriptType"
                placeholder="请选择脚本类型"
                class="full-width-select"
              >
                <el-option label="script" value="script" />
                <el-option label="switch_script" value="switch_script" />
                <el-option label="boolean_script" value="boolean_script" />
                <el-option label="for_script" value="for_script" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="脚本语言" prop="scriptLanguage">
              <el-select 
                v-model="form.scriptLanguage"
                placeholder="请选择脚本语言"
                @change="handleLanguageChange"
                class="full-width-select"
              >
                <el-option label="Groovy" value="groovy" />
                <el-option label="JavaScript" value="javascript" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态">
              <el-switch
                v-model="form.enabled"
                active-text="启用"
                inactive-text="禁用"
              ></el-switch>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input 
                type="textarea"
                v-model="form.remark"
                :rows="3"
                placeholder="请输入备注信息"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <div class="section-title">脚本内容</div>
        <el-row>
          <el-col :span="24">
            <el-form-item prop="scriptData" class="script-content-form-item">
              <div class="editor-container" id="editor-form"></div>
              <div class="editor-tip">
                <i class="el-icon-info"></i>
                <span>请编写符合 {{ form.scriptLanguage }} 语法的脚本内容</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="form-actions">
      <el-button @click="goBack">取消</el-button>
      <el-button 
        type="warning" 
        @click="validateScriptContent"
        :loading="validating"
        icon="el-icon-check-circle"
      >校验</el-button>
      <el-button 
        type="primary" 
        @click="saveScript"
        :loading="saving"
        icon="el-icon-check"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import { createRuleScript, validateScript, getApplicationList } from '@system/api/rule/script'
import * as monaco from 'monaco-editor'
import { debounce } from 'lodash'

export default {
  name: 'ScriptAdd',
  data() {
    return {
      saving: false,
      validating: false,
      form: {
        scriptId: '',
        scriptName: '',
        application: '',
        scriptType: 'script',
        scriptLanguage: 'groovy',
        scriptData: '',
        remark: '',
        enabled: true
      },
      rules: {
        scriptId: [
          { required: true, message: '请输入脚本ID', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        scriptName: [
          { required: true, message: '请输入脚本名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        application: [
          { required: true, message: '请选择所属应用', trigger: 'change' }
        ],
        scriptType: [
          { required: true, message: '请选择脚本类型', trigger: 'change' }
        ],
        scriptLanguage: [
          { required: true, message: '请选择脚本语言', trigger: 'change' }
        ],
        scriptData: [
          { required: true, message: '请输入脚本内容', trigger: 'blur' }
        ]
      },
      applicationList: [],
      editor: null
    }
  },
  created() {
    this.fetchApplicationList()
  },
  mounted() {
    this.createEditor()
  },
  methods: {
    async fetchApplicationList() {
      const data = await getApplicationList()
      if (data && Array.isArray(data)) {
        this.applicationList = data
      }
    },
    createEditor() {
      this.$nextTick(() => {
        const container = document.getElementById('editor-form')
        if (!container) return

        // 销毁已存在的编辑器
        if (this.editor) {
          this.editor.dispose()
        }

        // 创建新的编辑器实例
        this.editor = monaco.editor.create(container, {
          value: this.form.scriptData,
          language: this.form.scriptLanguage.toLowerCase(),
          theme: 'vs-dark',
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 14,
          tabSize: 2
        })

        // 监听内容变化
        this.editor.onDidChangeModelContent(
          debounce(() => {
            this.form.scriptData = this.editor.getValue()
          }, 300)
        )
      })
    },
    handleLanguageChange() {
      if (this.editor) {
        monaco.editor.setModelLanguage(
          this.editor.getModel(),
          this.form.scriptLanguage.toLowerCase()
        )
      }
    },
    goBack() {
      this.$router.push('/rule/script')
    },
    async saveScript() {
      // 编辑器内容手动赋值给表单
      if (this.editor) {
        this.form.scriptData = this.editor.getValue()
      }

      try {
        await this.$refs.scriptForm.validate()
      } catch (error) {
        return
      }

      this.saving = true
      try {
        await createRuleScript(this.form)
        this.$message.success('保存成功')
        this.goBack()
      } finally {
        this.saving = false
      }
    },
    async validateScriptContent() {
      if (this.editor) {
        this.form.scriptData = this.editor.getValue()
      }
      
      if (!this.form.scriptData) {
        this.$message.warning('请输入脚本内容')
        return
      }

      this.validating = true
      try {
        await validateScript({
          script: this.form.scriptData,
          type: this.form.scriptLanguage.toLowerCase()
        })
        this.$message.success('脚本验证通过')
      } finally {
        this.validating = false
      }
    }
  },
  beforeDestroy() {
    // 销毁编辑器实例
    if (this.editor) {
      this.editor.dispose()
    }
  }
}
</script>

<style lang="scss" scoped>
.script-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  min-height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eef1f7;

    .header-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .back-button {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #409EFF;
        transition: all 0.3s;
        font-size: 14px;
        font-weight: 500;

        i {
          margin-right: 6px;
          font-size: 14px;
        }

        &:hover {
          color: #66b1ff;
          transform: translateX(-3px);
        }
      }
    }
  }

  .script-form {
    width: 100%;
    padding: 0;
  }

  .form-section {
    width: 100%;
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 22px;
        
        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          line-height: 1.4;
          padding-bottom: 8px;
          color: #1a1f36;
          font-weight: 500;
          display: flex;
          align-items: center;
          height: 38px;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-textarea__inner {
          border-radius: 10px;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }
      }

      .script-content-form-item {
        display: flex;
        flex-direction: column;
        width: 100%;
        
        .el-form-item__content {
          margin-left: 0 !important;
          width: 100%;
        }
      }

      .el-select {
        width: 100%;
      }
    }
  }

  .editor-container {
    height: 500px;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e0e5ee;
    transition: all 0.3s;
    width: 100%;
    
    &:hover {
      border-color: #c0d0e9;
    }
    
    &:focus-within {
      border-color: #409EFF;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
    }
  }

  .editor-tip {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    color: #909399;
    font-size: 13px;

    i {
      color: #409EFF;
    }
  }

  .form-actions {
    margin-top: 24px;
    padding: 24px 0 0;
    text-align: center;
    border-top: 1px solid #eef1f7;

    .el-button {
      min-width: 120px;
      padding: 10px 20px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      border-radius: 10px;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      & + .el-button {
        margin-left: 16px;
      }
      
      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}
</style> 