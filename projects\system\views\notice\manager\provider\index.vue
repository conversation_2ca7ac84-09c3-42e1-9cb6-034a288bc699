<template>
  <div class="provider-container">
    <!-- 顶部操作栏 -->
    <div class="action-bar">
      <div class="left">
        <el-input v-model="searchKeyword" placeholder="搜索供应商名称" prefix-icon="el-icon-search" clearable
          class="search-input" />
      </div>
      <div class="right">
        <el-button type="primary" icon="el-icon-plus" @click="$router.push('/notice/provider/add')">
          添加供应商
        </el-button>
      </div>
    </div>

    <!-- 供应商卡片列表 -->
    <el-row :gutter="24">
      <el-col :span="6" v-for="provider in filteredProviders" :key="provider.id">
        <el-card class="provider-card" shadow="hover">
          <div class="provider-header">
            <div class="provider-info">
              <div class="provider-icon" :style="{ backgroundColor: getPluginColor(provider.pluginBeanName) }">
                <i :class="getPluginIcon(provider.pluginBeanName)"></i>
              </div>
              <div class="provider-meta">
                <h3>{{ provider.name }}</h3>
                <div class="provider-tags">
                  <el-tag :type="getPluginTagType(provider.pluginBeanName)" size="small" effect="dark">
                    {{ getPluginName(provider.pluginBeanName) }}
                  </el-tag>
                  <el-tag :type="provider.enabled ? 'success' : 'info'" size="small" class="status-tag">
                    {{ provider.enabled ? '已启用' : '已禁用' }}
                  </el-tag>
                </div>
              </div>
            </div>
            <el-dropdown trigger="click" @command="handleCommand($event, provider)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">
                  <i class="el-icon-edit"></i> 编辑配置
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <i class="el-icon-delete"></i> 删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>

          <div class="provider-content">
            <p class="description">{{ provider.remark || '暂无描述' }}</p>
            <div class="provider-details">
              <div class="detail-item">
                <i class="el-icon-time"></i>
                <span>{{ formatTime(provider.updateTime) }}</span>
              </div>
              <div class="detail-item">
                <i class="el-icon-connection"></i>
                <span>{{ provider.code }}</span>
              </div>
            </div>
          </div>

          <div class="provider-footer">
            <el-switch v-model="provider.enabled" @change="handleToggleStatus(provider)" active-text="启用"
              inactive-text="禁用"></el-switch>
            <div class="footer-actions">
              <el-button type="text" @click="handleEditProvider(provider)">
                配置
              </el-button>
              <el-button type="primary" size="small" @click="handleTemplateManage(provider)">
                <i class="el-icon-document"></i>
                消息模板
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getProviderList, deleteProvider, updateProviderStatus } from '@system/api/notice/manager'

export default {
  name: 'ProviderManagement',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      providerList: []
    }
  },
  computed: {
    filteredProviders() {
      return this.providerList.filter(provider => {
        return provider.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      })
    }
  },
  created() {
    this.getProviderList()
  },
  methods: {
    async getProviderList() {
      this.loading = true
      const data = await getProviderList({ size: -1 })
      this.providerList = data?.records || []
      this.loading = false
    },
    getPluginName(pluginBeanName) {
      const pluginMap = {
        'NoticeSmtpPlugin': 'SMTP邮件',
        'NoticeAliYunSmsPlugin': '阿里云短信',
        'NoticeTencentSmsPlugin': '腾讯云短信',
        'NoticeImPlugin': '即时通信'
      }
      return pluginMap[pluginBeanName] || '自定义插件'
    },
    getPluginTagType(pluginBeanName) {
      const typeMap = {
        'NoticeSmtpPlugin': 'primary',
        'NoticeAliYunSmsPlugin': 'success',
        'NoticeTencentSmsPlugin': 'warning',
        'NoticeWebhookPlugin': 'info'
      }
      return typeMap[pluginBeanName] || 'primary'
    },
    getPluginIcon(pluginBeanName) {
      const iconMap = {
        'NoticeSmtpPlugin': 'el-icon-message',
        'NoticeAliYunSmsPlugin': 'el-icon-mobile',
        'NoticeTencentSmsPlugin': 'el-icon-mobile',
        'NoticeWebhookPlugin': 'el-icon-connection'
      }
      return iconMap[pluginBeanName] || 'el-icon-bell'
    },
    getPluginColor(pluginBeanName) {
      const colorMap = {
        'NoticeSmtpPlugin': '#409EFF',
        'NoticeAliYunSmsPlugin': '#67C23A',
        'NoticeTencentSmsPlugin': '#E6A23C',
        'NoticeWebhookPlugin': '#909399'
      }
      return colorMap[pluginBeanName] || '#409EFF'
    },
    formatTime(time) {
      return time || '-'
    },
    handleCommand(command, provider) {
      switch (command) {
        case 'edit':
          this.handleEditProvider(provider)
          break
        case 'delete':
          this.handleDeleteProvider(provider)
          break
      }
    },
    handleEditProvider(provider) {
      this.$router.push(`/notice/provider/edit/${provider.id}`)
    },
    async handleDeleteProvider(provider) {
      try {
        await this.$confirm('确认删除该供应商吗？', '提示', {
          type: 'warning'
        })
        // 删除供应商的API调用
        await deleteProvider(provider.id)
        this.$message.success('删除成功')
        await this.getProviderList()
      } catch (err) {
        if (err !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    async handleToggleStatus(provider) {
      try {
        await updateProviderStatus(provider.id, provider.enabled)
        this.$message.success(`${provider.enabled ? '启用' : '禁用'}成功`)
      } catch (error) {
        provider.enabled = !provider.enabled // 恢复状态
        this.$message.error(`${provider.enabled ? '禁用' : '启用'}失败`)
      }
    },
    handleTemplateManage(provider) {
      this.$router.push(`/notice/provider/template/${provider.id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.provider-container {
  padding: 24px;
  background: inherit;

  .action-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 4px;

    .left {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .search-input {
        width: 280px;

        ::v-deep .el-input__inner {
          border-radius: 20px;
          padding-left: 40px;
        }

        ::v-deep .el-input__prefix {
          left: 15px;
        }
      }
    }

    .right {
      .el-button {
        border-radius: 20px;
        padding: 10px 24px;

        i {
          margin-right: 6px;
        }
      }
    }
  }

  .provider-card {
    margin-bottom: 20px;
    border-radius: 8px;
    transition: all 0.3s;
    border: none;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    }

    .provider-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .provider-info {
        display: flex;
        gap: 12px;

        .provider-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
          background-color: #409EFF;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .provider-meta {
          h3 {
            margin: 0 0 6px;
            font-size: 14px;
            font-weight: normal;
            color: #303133;
          }

          .provider-tags {
            display: flex;
            gap: 6px;

            .el-tag {
              border-radius: 10px;
              padding: 0 8px;
              font-size: 11px;
            }
          }
        }
      }

      .el-icon-more {
        padding: 8px;
        cursor: pointer;
        color: #909399;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background-color: #f5f7fa;
          color: #606266;
        }
      }
    }

    .provider-content {
      margin-bottom: 16px;

      .description {
        height: 36px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #909399;
        line-height: 1.5;
        margin: 0 0 12px;
      }

      .provider-details {
        display: flex;
        gap: 16px;

        .detail-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #909399;

          i {
            font-size: 12px;
          }
        }
      }
    }

    .provider-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;

      .footer-actions {
        display: flex;
        gap: 6px;
        align-items: center;

        .el-button--primary {
          padding: 6px 12px;
          border-radius: 20px;
          background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
          border: none;
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            background: linear-gradient(135deg, #66b1ff 0%, #409EFF 100%);
          }
          
          i {
            margin-right: 6px;
            font-size: 14px;
            vertical-align: middle;
          }
        }

        .el-button--text {
          font-size: 12px;
          padding: 6px 8px;
          border-radius: 15px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f5f7fa;
            color: #409EFF;
          }
        }
      }

      ::v-deep .el-switch {
        &.is-checked {
          .el-switch__core {
            background-color: #67C23A;
            border-color: #67C23A;
          }
        }

        &:not(.is-checked) {
          .el-switch__core {
            background-color: #F56C6C;
            border-color: #F56C6C;
          }
        }
      }
    }
  }
}
</style> 