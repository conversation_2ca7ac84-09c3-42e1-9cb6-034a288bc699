import request from '@/utils/request'

const sysPostApi = CONSTANT.SYSTEM + '/sys/post'

// 获取岗位树列表
export function getPostTreeList(params) {
  return request({
    url: `${sysPostApi}/treeList`,
    method: 'get',
    params
  })
}

// 获取岗位信息
export function getPostInfo(id) {
  return request({
    url: `${sysPostApi}`,
    method: 'get',
    params: { id }
  })
}

// 新增岗位
export function addPost(data) {
  return request({
    url: sysPostApi,
    method: 'post',
    data
  })
}

// 编辑岗位
export function updatePost(data) {
  return request({
    headers: {
      'X-Cover': 'true'
    },
    url: sysPostApi,
    method: 'put',
    data
  })
}

// 删除岗位
export function deletePost(ids) {
  return request({
    url: `${sysPostApi}/${ids}`,
    method: 'delete'
  })
}
