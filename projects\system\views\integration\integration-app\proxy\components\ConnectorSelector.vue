<template>
  <div class="connector-selector">
    <!-- 顶部操作栏 -->
    <div class="selector-header">
      <div class="header-info">
        <div class="title-section">
          <h4 class="section-title">选择连接</h4>
          <div class="status-info">
            <el-tag
              :type="getStatusType()"
              size="small"
              effect="plain"
              class="status-tag"
            >
              {{ getStatusText() }}
            </el-tag>
          </div>
        </div>
        <div class="usage-tip">
          <i class="el-icon-info"></i>
          <span>选择代理应用可以使用的连接器，支持多选</span>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          size="small"
          icon="el-icon-refresh"
          @click="refreshConnectors"
          :loading="loading"
          class="refresh-btn"
        >
          刷新
        </el-button>
        <el-button
          type="primary"
          size="small"
          :loading="saving"
          @click="saveConnectorConfig"
          class="save-btn"
        >
          <i class="el-icon-check"></i>
          保存配置
        </el-button>
      </div>
    </div>

    <!-- 已选择的连接器 -->
    <div class="selected-connectors" v-if="selectedConnectorIds.length > 0">
      <div class="selected-header">
        <div class="selected-summary">
          <div class="summary-main">
            <i class="el-icon-link"></i>
            <span class="summary-text">{{ getSelectionSummary() }}</span>
          </div>
          <div class="summary-details" v-if="getConfigurationStatus().hasIssues">
            <el-tag size="mini" type="warning" effect="plain">
              <i class="el-icon-warning"></i>
              {{ getConfigurationStatus().message }}
            </el-tag>
          </div>
        </div>
        <div class="header-actions">
          <el-tooltip content="查看连接器类型分布" placement="top">
            <el-button
              size="mini"
              type="text"
              @click="showTypeDistribution = !showTypeDistribution"
              class="info-btn"
            >
              <i class="el-icon-pie-chart"></i>
            </el-button>
          </el-tooltip>
          <el-button
            size="mini"
            type="text"
            @click="clearSelection"
            class="clear-all-btn"
          >
            <i class="el-icon-delete"></i>
            清空
          </el-button>
        </div>
      </div>

      <!-- 类型分布信息 -->
      <div class="type-distribution" v-if="showTypeDistribution">
        <div class="distribution-items">
          <div
            v-for="(count, type) in getTypeDistribution()"
            :key="type"
            class="distribution-item"
          >
            <i :class="getConnectorIcon(type)"></i>
            <span class="type-name">{{ getTypeDisplayName(type) }}</span>
            <el-tag size="mini" :type="getTypeTagType(type)">{{ count }}</el-tag>
          </div>
        </div>
      </div>
      <div class="selected-items">
        <div
          v-for="connectorId in selectedConnectorIds"
          :key="connectorId"
          class="selected-item"
        >
          <div class="item-info">
            <div class="item-icon">
              <i :class="getConnectorIcon(getConnectorById(connectorId)?.type)"></i>
            </div>
            <div class="item-details">
              <span class="item-name">{{ getConnectorName(connectorId) }}</span>
              <span class="item-code">{{ getConnectorById(connectorId)?.code }}</span>
            </div>
          </div>
          <el-button
            size="mini"
            type="text"
            @click="removeConnector(connectorId)"
            class="remove-btn"
          >
            <i class="el-icon-close"></i>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 连接器列表 -->
    <div class="connector-list" v-loading="loading">
      <div v-if="connectorList.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无可用连接器">
          <el-button type="primary" @click="refreshConnectors">刷新列表</el-button>
        </el-empty>
      </div>

      <div v-else class="connector-grid">
        <div
          v-for="connector in connectorList"
          :key="connector.id"
          :class="['connector-card', {
            'selected': selectedConnectorIds.includes(connector.id),
            'disabled': !connector.enabled
          }]"
          @click="toggleConnector(connector)"
        >
          <div class="card-header">
            <div class="connector-info">
              <div class="connector-icon">
                <i :class="getConnectorIcon(connector.type)"></i>
              </div>
              <div class="connector-meta">
                <h5 class="connector-name">{{ connector.name }}</h5>
                <span class="connector-code">{{ connector.code }}</span>
              </div>
            </div>
            <div class="selection-indicator">
              <el-checkbox
                :value="selectedConnectorIds.includes(connector.id)"
                @change="(checked) => handleConnectorChange(connector, checked)"
                :disabled="!connector.enabled"
              />
            </div>
          </div>

          <div class="card-body">
            <div class="connector-type">
              <el-tag size="mini" :type="getTypeTagType(connector.type)">
                {{ connector.type }}
              </el-tag>
              <el-tag
                size="mini"
                :type="connector.enabled ? 'success' : 'danger'"
                effect="plain"
              >
                {{ connector.enabled ? '启用' : '禁用' }}
              </el-tag>
            </div>

            <div class="connector-description" v-if="connector.description">
              {{ connector.description }}
            </div>

            <div class="connector-config" v-if="connector.config">
              <div class="config-item" v-if="connector.config.baseUrl">
                <span class="config-label">基础URL:</span>
                <span class="config-value">{{ connector.config.baseUrl }}</span>
              </div>
              <div class="config-item" v-if="connector.config.authActuator">
                <span class="config-label">认证执行器:</span>
                <span class="config-value">{{ connector.config.authActuator }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getConnectorList, updateAppConnectors, getAppConnectors } from '@system/api/integration/proxy-app'

export default {
  name: 'ConnectorSelector',
  props: {
    appId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      saving: false,
      connectorList: [],
      selectedConnectorIds: [],
      showTypeDistribution: false
    }
  },
  computed: {
    getStatusType() {
      return () => {
        if (this.selectedConnectorIds.length === 0) return 'warning'
        const configStatus = this.getConfigurationStatus()
        if (configStatus.hasIssues) return 'warning'
        return 'success'
      }
    },
    getStatusText() {
      return () => {
        if (this.selectedConnectorIds.length === 0) return '未配置连接器'

        const selectedConnectors = this.selectedConnectorIds.map(id => this.getConnectorById(id)).filter(Boolean)
        const enabledCount = selectedConnectors.filter(c => c.enabled).length
        const disabledCount = selectedConnectors.length - enabledCount

        if (disabledCount > 0) {
          return `${enabledCount}个活跃 / ${disabledCount}个禁用`
        }

        return `${enabledCount}个连接器已就绪`
      }
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      await Promise.all([
        this.loadConnectors(),
        this.loadAppConnectors()
      ])
    },

    // 加载连接器列表
    async loadConnectors() {
      this.loading = true
      try {
        const response = await getConnectorList({
          current: 1,
          size: -1 // 获取全部
        })
        this.connectorList = response.records || []
      } catch (error) {
        console.error('加载连接器列表失败:', error)
        this.$message.error('加载连接器列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载应用关联的连接器
    async loadAppConnectors() {
      try {
        const connectorIds = await getAppConnectors(this.appId)
        this.selectedConnectorIds = connectorIds || []
      } catch (error) {
        console.error('加载应用连接器失败:', error)
        // 不显示错误消息，可能是新应用还没有配置连接器
      }
    },

    // 刷新连接器列表
    refreshConnectors() {
      this.loadConnectors()
    },

    // 根据ID获取连接器对象
    getConnectorById(connectorId) {
      return this.connectorList.find(c => c.id === connectorId)
    },

    // 切换连接器选择状态
    toggleConnector(connector) {
      if (!connector.enabled) return
      
      const index = this.selectedConnectorIds.indexOf(connector.id)
      if (index > -1) {
        this.selectedConnectorIds.splice(index, 1)
      } else {
        this.selectedConnectorIds.push(connector.id)
      }
    },

    // 处理连接器选择变化
    handleConnectorChange(connector, checked) {
      if (checked) {
        if (!this.selectedConnectorIds.includes(connector.id)) {
          this.selectedConnectorIds.push(connector.id)
        }
      } else {
        const index = this.selectedConnectorIds.indexOf(connector.id)
        if (index > -1) {
          this.selectedConnectorIds.splice(index, 1)
        }
      }
    },

    // 移除连接器
    removeConnector(connectorId) {
      const index = this.selectedConnectorIds.indexOf(connectorId)
      if (index > -1) {
        this.selectedConnectorIds.splice(index, 1)
      }
    },

    // 清空选择
    clearSelection() {
      this.selectedConnectorIds = []
    },

    // 保存连接器配置
    async saveConnectorConfig() {
      this.saving = true
      try {
        await updateAppConnectors({
          appId: this.appId,
          connectorIds: this.selectedConnectorIds
        })
        this.$message.success('连接器配置保存成功')
        this.$emit('config-saved', this.selectedConnectorIds)
      } catch (error) {
        console.error('保存连接器配置失败:', error)
        this.$message.error('保存连接器配置失败')
      } finally {
        this.saving = false
      }
    },

    // 获取连接器名称
    getConnectorName(connectorId) {
      const connector = this.connectorList.find(c => c.id === connectorId)
      return connector ? connector.name : connectorId
    },

    // 获取连接器图标
    getConnectorIcon(type) {
      const iconMap = {
        'HTTP': 'el-icon-link',
        'DATABASE': 'el-icon-coin',
        'MESSAGE': 'el-icon-message',
        'FILE': 'el-icon-document'
      }
      return iconMap[type] || 'el-icon-connection'
    },

    // 获取类型标签类型
    getTypeTagType(type) {
      const typeMap = {
        'HTTP': 'primary',
        'DATABASE': 'success',
        'MESSAGE': 'warning',
        'FILE': 'info'
      }
      return typeMap[type] || 'default'
    },

    // 获取选择摘要信息
    getSelectionSummary() {
      const count = this.selectedConnectorIds.length
      if (count === 0) return '未选择连接器'

      const selectedConnectors = this.selectedConnectorIds.map(id => this.getConnectorById(id)).filter(Boolean)
      const typeCount = {}

      selectedConnectors.forEach(connector => {
        typeCount[connector.type] = (typeCount[connector.type] || 0) + 1
      })

      const typeNames = Object.keys(typeCount).map(type => {
        const displayName = this.getTypeDisplayName(type)
        const count = typeCount[type]
        return count > 1 ? `${displayName}(${count})` : displayName
      })

      if (typeNames.length <= 2) {
        return `已选择: ${typeNames.join('、')}`
      } else {
        return `已选择 ${count} 个连接器 (${typeNames.length} 种类型)`
      }
    },

    // 获取配置状态
    getConfigurationStatus() {
      const selectedConnectors = this.selectedConnectorIds.map(id => this.getConnectorById(id)).filter(Boolean)
      const disabledConnectors = selectedConnectors.filter(c => !c.enabled)

      if (disabledConnectors.length > 0) {
        return {
          hasIssues: true,
          message: `${disabledConnectors.length}个连接器未启用`
        }
      }

      return {
        hasIssues: false,
        message: '配置正常'
      }
    },

    // 获取类型分布
    getTypeDistribution() {
      const selectedConnectors = this.selectedConnectorIds.map(id => this.getConnectorById(id)).filter(Boolean)
      const distribution = {}

      selectedConnectors.forEach(connector => {
        distribution[connector.type] = (distribution[connector.type] || 0) + 1
      })

      return distribution
    },

    // 获取类型显示名称
    getTypeDisplayName(type) {
      const nameMap = {
        'HTTP': 'HTTP接口',
        'DATABASE': '数据库',
        'MESSAGE': '消息队列',
        'FILE': '文件系统'
      }
      return nameMap[type] || type
    }
  }
}
</script>

<style lang="scss" scoped>
.connector-selector {
  // 顶部操作栏
  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 0 16px 0;
    margin-bottom: 20px;

    .header-info {
      flex: 1;

      .title-section {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .section-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          line-height: 1.2;
        }

        .status-info {
          .status-tag {
            font-size: 12px;
            padding: 4px 12px;
            border-radius: 16px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 24px;
            line-height: 1;
          }
        }
      }

      .usage-tip {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        color: #64748b;
        line-height: 1.4;

        i {
          font-size: 14px;
          color: #94a3b8;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      .refresh-btn {
        background: #f8fafc;
        border-color: #e2e8f0;
        color: #64748b;

        &:hover {
          background: #f1f5f9;
          border-color: #cbd5e1;
          color: #475569;
        }
      }

      .save-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border: none;
        color: white;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
          background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        &:disabled {
          background: #e0e6ed;
          color: #8c8c8c;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  // 已选择的连接器
  .selected-connectors {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;

    .selected-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .selected-summary {
        flex: 1;

        .summary-main {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 6px;
          font-size: 14px;
          font-weight: 600;
          color: #0c4a6e;

          i {
            font-size: 16px;
          }

          .summary-text {
            line-height: 1.3;
          }
        }

        .summary-details {
          margin-left: 24px;

          .el-tag {
            font-size: 11px;
            padding: 2px 8px;
            border-radius: 12px;

            i {
              margin-right: 4px;
              font-size: 12px;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;

        .info-btn {
          color: #0369a1;
          font-size: 12px;
          padding: 4px 6px;

          &:hover {
            color: #0c4a6e;
            background: rgba(3, 105, 161, 0.1);
          }

          i {
            font-size: 14px;
          }
        }

        .clear-all-btn {
          color: #0369a1;
          font-size: 12px;
          padding: 4px 8px;

          &:hover {
            color: #0c4a6e;
            background: rgba(3, 105, 161, 0.1);
          }

          i {
            margin-right: 4px;
          }
        }
      }
    }

    .type-distribution {
      background: rgba(255, 255, 255, 0.7);
      border: 1px solid #bae6fd;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;

      .distribution-items {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .distribution-item {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 10px;
          background: white;
          border: 1px solid #e0f2fe;
          border-radius: 6px;
          font-size: 12px;
          color: #0c4a6e;

          i {
            font-size: 14px;
            color: #0369a1;
          }

          .type-name {
            font-weight: 500;
          }

          .el-tag {
            margin-left: 4px;
            font-size: 11px;
            padding: 1px 6px;
            min-width: 20px;
            text-align: center;
          }
        }
      }
    }

    .selected-items {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .selected-item {
        background: white;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: 200px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #0ea5e9;
          box-shadow: 0 2px 8px rgba(14, 165, 233, 0.15);
          transform: translateY(-1px);
        }

        .item-info {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1;

          .item-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
          }

          .item-details {
            flex: 1;
            min-width: 0;

            .item-name {
              display: block;
              font-size: 13px;
              font-weight: 600;
              color: #1e293b;
              line-height: 1.3;
              margin-bottom: 2px;
            }

            .item-code {
              display: block;
              font-size: 11px;
              color: #64748b;
              font-family: 'Monaco', 'Menlo', monospace;
            }
          }
        }

        .remove-btn {
          color: #ef4444;
          padding: 4px;
          margin-left: 8px;
          flex-shrink: 0;

          &:hover {
            color: #dc2626;
            background: rgba(239, 68, 68, 0.1);
          }

          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 连接器列表
  .connector-list {
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }

    .connector-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 16px;

      .connector-card {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
          transform: translateY(-2px);
        }

        &.selected {
          border-color: #10b981;
          background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
          box-shadow: 0 4px 16px rgba(16, 185, 129, 0.2);

          .connector-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
          }
        }

        &.disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:hover {
            border-color: #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transform: none;
          }
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .connector-info {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            flex: 1;

            .connector-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 16px;
              flex-shrink: 0;
              transition: all 0.3s ease;
            }

            .connector-meta {
              flex: 1;
              min-width: 0;

              .connector-name {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
                line-height: 1.3;
              }

              .connector-code {
                font-size: 12px;
                color: #64748b;
                font-family: 'Monaco', 'Menlo', monospace;
              }
            }
          }

          .selection-indicator {
            flex-shrink: 0;
          }
        }

        .card-body {
          .connector-type {
            display: flex;
            gap: 6px;
            margin-bottom: 8px;
            flex-wrap: wrap;
          }

          .connector-description {
            font-size: 12px;
            color: #64748b;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .connector-config {
            .config-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 11px;
              margin-bottom: 4px;

              .config-label {
                color: #64748b;
                font-weight: 500;
              }

              .config-value {
                color: #1e293b;
                font-family: 'Monaco', 'Menlo', monospace;
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }
}

</style>
