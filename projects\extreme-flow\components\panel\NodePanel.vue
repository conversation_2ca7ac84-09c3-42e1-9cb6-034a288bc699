<template>
    <div class="drag-panel" :class="{ collapsed: isCollapsed }" style="position: absolute;z-index:9999;">
        <!-- 收起/展开按钮 -->
        <div class="panel-header">
            <span class="panel-title" v-if="!isCollapsed">节点面板</span>
            <el-button type="text" class="collapse-btn" @click="toggleCollapse" :title="isCollapsed ? '展开面板' : '收起面板'">
                <i :class="isCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
            </el-button>
        </div>

        <!-- 面板内容 -->
        <div class="panel-content" v-show="!isCollapsed">
            <div v-for="(group, index) in componentsList" :key="index" class="node-group">
                <div class="group-title">
                    <span>{{ group.name }}</span>
                    <el-divider></el-divider>
                </div>
                <div class="node-grid" v-if="group.nodes && group.nodes.length">
                    <div v-for="(node, index2) in group.nodes" :key="`${index}_${index2}`" class="node-item"
                        @mousedown="node.type && lf.dnd.startDrag({ type: node.type, properties: { ...node.properties, inAnchor: node.inAnchor, outAnchor: node.outAnchor, propertyPanel: node.propertyPanel != null } })"
                        @mouseup="lf.dnd.stopDrag()">
                        <i :class="node.icon"></i>
                        <span>{{ node.label }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import LogicFlow from "@logicflow/core"
import config from "./config"

export default {
    props: {
        lf: {
            type: LogicFlow,
            required: true
        }
    },
    data() {
        return {
            componentsList: [],
            isCollapsed: false
        }
    },
    mounted() {
        config().then(list => {
            this.componentsList = [...list]
        })
    },
    methods: {
        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed
        }
    }
};
</script>
<style lang="scss" scoped>
.drag-panel {
    width: 200px;
    max-height: calc(100% - 20px);
    overflow-y: auto;
    margin: 10px 0px 10px 5px;
    background-color: #fff;
    border: 1px solid #e6e9ef;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 0;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    transition: all 0.3s ease;

    &.collapsed {
        width: 40px;
        overflow: hidden;
    }

    .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px;
        border-bottom: 1px solid #e6e9ef;
        background-color: #f8f9fa;
        border-radius: 6px 6px 0 0;

        .panel-title {
            font-size: 14px;
            font-weight: 600;
            color: #606266;
            margin: 0;
        }

        .collapse-btn {
            padding: 4px;
            min-width: auto;
            height: auto;
            color: #606266;

            &:hover {
                color: #409eff;
                background-color: transparent;
            }

            i {
                font-size: 16px;
            }
        }
    }

    .panel-content {
        padding: 10px;
        transition: all 0.3s ease;
    }

    .node-group {
        margin-bottom: 20px;

        .group-title {
            margin-bottom: 12px;

            span {
                font-size: 14px;
                font-weight: 600;
                color: #606266;
            }

            .el-divider {
                margin: 8px 0;
            }
        }

        .node-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .node-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: calc(50% - 4px);
            padding: 8px 6px;
            border: 1px solid #e6e9ef;
            border-radius: 6px;
            cursor: move;
            transition: all 0.2s;
            text-align: center;

            i {
                font-size: 18px;
                color: #606266;
                margin-bottom: 4px;
            }

            span {
                color: black;
                font-size: 12px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
            }

            &:hover {
                background-color: #f5f7fa;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
        }
    }

}
</style>