<template>
    <div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
            <el-form-item prop="username">
                <el-input maxlength='20' v-model="loginForm.username" placeholder="用户名" prefix-icon="el-icon-user" />
            </el-form-item>
            <el-form-item prop="password">
                <el-input maxlength='20' v-model="loginForm.password" type="password" placeholder="密码"
                    prefix-icon="el-icon-lock" show-password @keyup.enter.native="handleLogin" />
            </el-form-item>

            <el-form-item prop="code">
                <el-input v-if="version.match('^1.0')" maxlength="4" placeholder="请输入验证码" v-model="loginForm.code"
                    prefix-icon="el-icon-key" ref="captchaInput" @keyup.enter="handleLogin">
                    <div slot="suffix" class="captcha-img" @click.stop="createCode">
                        <el-image :src="base64ToImage" @click.stop="createCode" />
                    </div>
                </el-input>
                <captcha v-else-if="version.match('>=2.0')" :type="$attrs.configKeys['builtin.captcha.type'] || 'text'"
                    ref="captcha" />
            </el-form-item>

            <div class="remember-forget">
                <el-checkbox v-model="isChecked">记住密码</el-checkbox>
                <span v-if="$attrs.configKeys['builtin.forget.email'] || $attrs.configKeys['builtin.forget.phone']"
                    class="forget-pwd" @click="$parent.uiType = 'ResetPwdUI'">忘记密码?</span>
            </div>

            <el-form-item>
                <el-button v-enter="handleLogin" :loading="loading" type="primary" class="login-button"
                    @click="handleLogin">登
                    录</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import captcha from './captcha';
import { $encruption } from './consts';
import { code } from "@/api/login/login";
import LoginMixin from "./LoginMixin.js";

export default {
    mixins: [LoginMixin],
    components: { captcha },
    data() {
        return {
            loginForm: {
                username: '',
                password: '',
            },
            img: null,
            loading: false,
            loginRules: {
                username: [
                    { required: true, trigger: "blur", message: '请输入用户名' }
                ],
                password: [
                    { required: true, trigger: "blur", message: '密码不能为空' }
                ],
                code: [
                    { required: true, trigger: "blur", message: '验证码不能为空' }
                ]
            },
            redirect: undefined,
            isChecked: false
        }
    },

    computed: {
        base64ToImage() {
            return `data:image/png;base64,${this.img || ''}`;
        }
    },
    watch: {
        $route: {
            handler: function (route) {
                this.redirect = route.query && route.query.redirect
            },
            immediate: true
        },
        isChecked(n) {
            localStorage.setItem('remember-isChecked', n)
        }
    },
    mounted() {
        this.createCode()
        this.isChecked = localStorage.getItem('remember-isChecked') == 'true'
    },
    methods: {

        createCode() {
            if (this.$refs.captchaInput) {
                code().then(res => {
                    this.img = res.base64
                    this.loginForm.uuid = res.uuid
                })
            }
        },
        async handleLogin() {
            this.$refs.loginForm.validate((valid, promise) => {
                if (valid) {
                    if (!this.checkAgreement()) return
                    var form = {}
                    var captcha = this.$refs.captcha
                    captcha && (form.headers = { "X-Captcha": captcha.result })

                    let passwordValue = this.loginForm.password;
                    form.data = JSON.parse(JSON.stringify(this.loginForm));
                    form.data.password = $encruption(passwordValue);
                    this.loading = true
                    this.$store
                        .dispatch("user/login", form).then(res => {
                            this.loading = false
                            this.loginComplete(res)
                            captcha && captcha.complete(true)
                        }).catch((err) => {
                            this.loading = false
                            console.log("error submit!!", err)
                            this.createCode()
                            this.loginError(err)
                            captcha && captcha.complete(false, err)
                        })
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.login-form {
    .el-input {
        height: 44px;

        ::v-deep .el-input__inner {
            height: 44px;
            padding-left: 48px;
            font-size: 15px;
        }

        ::v-deep .el-input__prefix {
            left: 10px;
            font-size: 18px;
        }

        .el-input__inner:focus {
            border-color: #1682e6;
        }

        .captcha-img {
            width: 120px;
            height: 100%;
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;

            .el-image {
                width: 120px;
                height: 36px;
                background: #f0f2f5;
                border-radius: 4px;


                ::v-deep .el-image__error:hover {
                    background-color: #e6e8eb !important;
                }
            }
        }
    }

    .login-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        margin-top: 20px;
        background: rgb(0, 122, 255);
        border-radius: 8px;
        border: none;

        &:hover {
            background: rgb(0, 86, 179);
        }
    }


    .remember-forget {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;

        .forget-pwd {
            font-size: 14px;
            color: rgb(0, 122, 255);
            transition: all 0.3s;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;

            &:hover {
                color: rgb(0, 86, 179);
                transform: scale(1.05);
            }
        }
    }
}
</style>