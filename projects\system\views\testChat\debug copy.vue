<template>
    <div class="app-container">
        <div class="left-panel">
            <div class="control-panel">
                <div class="button-row">
                    <button @click="startRecording" :disabled="isRecording">
                        <span class="mic-icon"></span>
                        Start Recording
                    </button>
                    <button @click="stopRecording" :disabled="!isRecording">
                        <span class="stop-icon"></span>
                        Stop
                    </button>
                </div>
            </div>

            <div class="visualizer-container" v-show="isRecording">
                <voiceprint :volumes="volumes" :barCount="barCount"></voiceprint>
            </div>

            <div class="status-indicator" :class="{ error: statusType === 'error' }" v-show="showStatusIndicator">
                {{ statusMessage }}
            </div>

            <div class="transcript-container">
                <div class="status">{{ status }}</div>
                <div class="transcript">{{ currentTranscript }}</div>
            </div>
        </div>

        <div class="right-panel">
            <div class="result-box">
                <h3>Corrected Text</h3>
                <div class="result-content">{{ correctedText }}</div>
                <div class="timestamp">{{ correctedDelay }}</div>
            </div>

            <div class="result-box">
                <h3>Dialogue Format</h3>
                <div class="result-content">{{ dialogueFormat }}</div>
                <div class="timestamp">{{ dialogueDelay }}</div>
            </div>

            <div class="result-box">
                <h3>Structured Data</h3>
                <div class="result-content">{{ structuredData }}</div>
                <div class="timestamp">{{ structuredDelay }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import { checkPermission, getUserMedia, newAudioContext, createMediaStreamSource, createScriptProcessor, mediaClose } from './audiolib/AudioHelper'
import voiceprint from './voiceprint.vue'

export default {
    components: {
        voiceprint
    },
    data() {
        return {
            isRecording: false,
            volumes: [],
            barCount: 50,
            status: 'Press "Start Recording" to begin',
            currentTranscript: '',
            correctedText: '',
            dialogueFormat: '',
            structuredData: '',
            correctedDelay: '',
            dialogueDelay: '',
            structuredDelay: '',
            statusMessage: '',
            statusType: 'info',
            showStatusIndicator: false,
            socket: null,
            requestTimestamps: {
                corrected: null,
                dialogue: null,
                structured: null
            },
            sampleRate: 16000,
            chunkSize: 20000,
        }
    },

    methods: {
        formatDelayTime(delayMs) {
            if (delayMs < 1000) {
                return `${delayMs}ms`
            }
            return `${(delayMs / 1000).toFixed(1)}s`
        },
        showStatus(message, type = 'info') {
            this.statusMessage = message
            this.statusType = type
            this.showStatusIndicator = true

            setTimeout(() => {
                this.showStatusIndicator = false
            }, 5000)
        },
        async startRecording() {
            try {
                // 重置状态
                this.resetState()
                this.status = 'Connecting to server...'

                // 初始化WebSocket
                const wsUrl = `ws://192.168.1.189:10001/asr`
                this.socket = new WebSocket(wsUrl)

                this.setupWebSocketHandlers()

                let obj = await checkPermission()
                obj.errorMessage && this.showStatus(obj.errorMessage)
                if (obj.hasPermission) {
                    getUserMedia({
                        sampleRate: this.sampleRate,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }).then((stream) => {
                        let audioContext = newAudioContext({ sampleRate: this.sampleRate })
                        this.audioContext = audioContext
                        let mediaStreamSource = createMediaStreamSource(audioContext, stream)
                        this.mediaStreamSource = mediaStreamSource
                        let scriptProcessorNode = createScriptProcessor(audioContext)
                        this.scriptProcessorNode = scriptProcessorNode
                        mediaStreamSource.connect(scriptProcessorNode)
                        scriptProcessorNode.connect(audioContext.destination)
                        this.isRecording = true
                        this.onaudioprocess(scriptProcessorNode)
                    })
                        .catch(err => {
                            throw err
                        })
                }


            } catch (error) {
                this.showStatus(`Error: ${error.message}`, 'error')
                this.stopRecording()
            }
        },
        setupWebSocketHandlers() {
            if (!this.socket) return

            this.socket.onopen = () => {
                this.status = 'Connected. Start speaking...'
                this.showStatus('Ready to transcribe', 'success')
            }

            this.socket.onmessage = async (event) => {
                try {
                    const data = JSON.parse(event.data)
                    const currentTime = Date.now()

                    switch (data.type) {
                        case 'transcript':
                            this.currentTranscript += data.content
                            break
                        case 'combined_response':
                            const responseJson = JSON.parse(data.content)
                            this.correctedText = responseJson.修正内容 || ''
                            this.dialogueFormat = responseJson.对话 || ''
                            this.structuredData = JSON.stringify(responseJson.关键信息 || [], null, 2)
                            if (this.requestTimestamps.corrected) {
                                this.correctedDelay = this.formatDelayTime(currentTime - this.requestTimestamps.corrected)
                                this.requestTimestamps.corrected = null
                            }
                            break
                        case 'combined_delay':

                            this.correctedDelay = this.formatDelayTime(data.content)
                            this.dialogueDelay = this.formatDelayTime(data.content)
                            this.structuredDelay = this.formatDelayTime(data.content)
                            break
                        case 'error':
                            this.showStatus(data.content, 'error')
                            break
                    }
                } catch (e) {
                    console.error('Error parsing WebSocket message:', e)
                }
            }

            this.socket.onerror = () => {
                this.showStatus('Connection error', 'error')
            }

            this.socket.onclose = () => {
                if (this.status.includes('Connected')) {
                    this.showStatus('Connection closed', 'info')
                }
            }
        },
        onaudioprocess(scriptProcessorNode) {
            let audioBuffer = []
            scriptProcessorNode.onaudioprocess = (e) => {
                const audioData = e.inputBuffer.getChannelData(0)

                // 计算音量用于可视化
                let volumes = []
                const segmentSize = Math.floor(audioData.length / this.barCount)

                for (let i = 0; i < this.barCount; i++) {
                    let sum = 0
                    const start = i * segmentSize
                    const end = start + segmentSize

                    for (let j = start; j < end; j++) {
                        sum += Math.abs(audioData[j])
                    }

                    const avg = sum / segmentSize
                    volumes.push(avg)
                }

                // 更新可视化
                this.volumes = volumes.map(vol => ({
                    height: `${Math.min(100, vol * 300)}%`
                }))

                // 收集音频样本
                for (let i = 0; i < audioData.length; i++) {
                    audioBuffer.push(audioData[i])
                }

                // 当收集到足够样本时发送
                while (audioBuffer.length >= this.chunkSize) {
                    const chunk = audioBuffer.splice(0, this.chunkSize)
                    const pcmData = new Int16Array(this.chunkSize)
                    for (let i = 0; i < this.chunkSize; i++) {
                        pcmData[i] = Math.max(-32768, Math.min(32767, chunk[i] * 32768))
                    }

                    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                        const sendTime = Date.now()
                        if (!this.requestTimestamps.corrected) {
                            this.requestTimestamps.corrected = sendTime
                        }
                        this.socket.send(pcmData.buffer)
                    }
                }
            }
        },
        stopRecording() {

            if (this.socket) {
                this.socket.close()
            }

            mediaClose(this.mediaStreamSource, this.scriptProcessorNode, this.audioContext)

            this.isRecording = false
            this.volumes = []
        },
        resetState() {
            this.currentTranscript = ''
            this.correctedText = ''
            this.dialogueFormat = ''
            this.structuredData = ''
            this.correctedDelay = ''
            this.dialogueDelay = ''
            this.structuredDelay = ''
            this.showStatusIndicator = false
            this.volumes = []
            this.requestTimestamps = {
                corrected: null,
                dialogue: null,
                structured: null
            }
        }
    },
    beforeDestroy() {
        this.stopRecording()
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    --primary-color: #6e8efb;
    --secondary-color: #a777e3;
    --success-color: #4ade80;
    --error-color: #f87171;
    --text-color: #1a1a1a;
    --bg-color: #f5f7fa;
    --panel-bg: #ffffff;
    --border-color: #e2e8f0;
    height: calc(100% - 80px);
    display: flex;
    background: var(--panel-bg);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin: 40px;

    .left-panel {
        flex: 1;
        padding: 20px;
        border-right: 1px solid var(--border-color);
        display: flex;
        flex-direction: column;
    }

    .right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .control-panel {
        padding: 16px;
        background: #f9fafc;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .button-row {
        display: flex;
        gap: 12px;
        flex: 1;
    }

    button {
        padding: 12px 24px;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        flex: 1;
        background-color: var(--success-color);
        color: white;
    }

    button:hover {
        background-color: #3bc76f;
        transform: translateY(-1px);
    }

    button:disabled {
        background-color: #e5e7eb;
        color: #9ca3af;
        cursor: not-allowed;
        transform: none;
    }

    .status-indicator {
        padding: 12px 20px;
        background: #f0fdf4;
        border-left: 4px solid var(--success-color);
        margin-bottom: 16px;
        font-size: 14px;
        border-radius: 4px;
    }

    .status-indicator.error {
        background: #fef2f2;
        border-left-color: var(--error-color);
    }

    .transcript-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
    }

    .status {
        color: #64748b;
        font-style: italic;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .transcript {
        flex: 1;
        background: #f8fafc;
        border-radius: 8px;
        padding: 16px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        font-size: 16px;
        line-height: 1.5;
    }

    .result-box {
        flex: 1;
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        flex-direction: column;
        min-height: 0;
        position: relative;
    }

    .result-box:last-child {
        border-bottom: none;
    }

    .result-box h3 {
        margin-bottom: 12px;
        color: var(--secondary-color);
    }

    .result-content {
        flex: 1;
        overflow-y: auto;
        padding: 8px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        white-space: pre-wrap;
    }

    .timestamp {
        position: absolute;
        bottom: 8px;
        right: 8px;
        font-size: 12px;
        color: #64748b;
        background-color: rgba(248, 250, 252, 0.8);
        padding: 2px 6px;
        border-radius: 4px;
    }

    .mic-icon,
    .stop-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: currentColor;
    }

    .mic-icon {
        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z'%3E%3C/path%3E%3Cpath d='M19 10v2a7 7 0 0 1-14 0v-2'%3E%3C/path%3E%3Cline x1='12' y1='19' x2='12' y2='23'%3E%3C/line%3E%3Cline x1='8' y1='23' x2='16' y2='23'%3E%3C/line%3E%3C/svg%3E");
    }

    .stop-icon {
        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='6' y='4' width='12' height='16' rx='2'%3E%3C/rect%3E%3C/svg%3E");
    }
}



@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .left-panel {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
}
</style>