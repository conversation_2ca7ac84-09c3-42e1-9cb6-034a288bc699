<template>
  <div>
    <auto-page :options="options" :form-rule="options.formRule">
    </auto-page>
  </div>
</template>

<script>
  import options from './options'
  import autoPage from "@/components/auto-page/AutoPage";
  import {treeList} from "@system/api/sys/resources"
  export default {
    components:{autoPage},
    data(){
      return{
        options:null
      }
    },
    created(){
      this.options = options
      this.assemblyTree()
    },
    methods: {
      async getTreeList() {
        return new Promise(((resolve, reject) => {
          treeList().then(res => {
            resolve(res)
          })
        }))
      },
      async assemblyTree(){
        let array = await this.getTreeList();
        this.options.formRule[2].props.data = array;
      },
    }
  }
</script>
