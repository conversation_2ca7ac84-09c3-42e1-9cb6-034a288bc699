<template>
  <div class="app-detail-header">
    <!-- 主要内容区域 -->
    <div class="header-main">
      <!-- 返回按钮 -->
      <div class="header-back">
        <el-button
          type="text"
          icon="el-icon-arrow-left"
          @click="handleGoBack"
          class="back-btn"
        >
          返回
        </el-button>
      </div>

      <!-- 应用基本信息 - 横向布局 -->
      <div class="app-basic-info">
        <!-- 应用图标 -->
        <div class="app-icon-section">
          <div class="app-icon">
            <img
              v-if="appDetail.icon && appDetail.icon.trim()"
              :src="appDetail.icon"
              :alt="appDetail.name"
              @error="handleImageError"
            />
            <div v-else class="default-icon" :style="getDefaultIconStyle()">
              {{ getAppIconLetter() }}
            </div>
          </div>
        </div>

        <!-- 应用名称 -->
        <div class="app-name-section">
          <h1
            v-if="!editingName"
            class="app-name"
            @click="startEditName"
            :title="'点击编辑应用名称'"
          >
            {{ appDetail.name || '应用名称' }}
            <i class="el-icon-edit edit-icon"></i>
          </h1>
          <div v-else class="name-edit-section">
            <el-input
              v-model="editForm.name"
              ref="nameInput"
              size="small"
              @blur="saveAppName"
              @keyup.enter.native="saveAppName"
              @keyup.esc.native="cancelEditName"
              class="name-input"
            />
            <div class="edit-actions">
              <el-button size="mini" type="primary" @click="saveAppName" icon="el-icon-check"></el-button>
              <el-button size="mini" @click="cancelEditName" icon="el-icon-close"></el-button>
            </div>
          </div>
        </div>

        <!-- 应用编码 -->
        <div class="app-code-section">
          <el-tag
            size="small"
            type="info"
            class="code-tag"
            @click="copyCode"
            :title="'点击复制应用编码'"
          >
            <i class="el-icon-document-copy copy-icon"></i>
            {{ appDetail.code || 'APP-CODE' }}
          </el-tag>
        </div>

        <!-- 应用类型 -->
        <div class="app-type-section">
          <el-tag
            size="small"
            :type="getTypeTagType()"
            class="type-tag"
          >
            {{ getTypeLabel() }}
          </el-tag>
        </div>

        <!-- 应用分组 -->
        <div class="app-group-section">
          <div v-if="!editingGroupName" class="group-display" @click="startEditGroupName" :title="'点击编辑应用分组'">
            <el-tag
              size="small"
              type="warning"
              class="group-tag editable-tag"
            >
              {{ appDetail.groupName || '未分组' }}
              <i class="el-icon-edit edit-icon"></i>
            </el-tag>
          </div>
          <div v-else class="group-edit-section">
            <el-input
              v-model="editForm.groupName"
              ref="groupNameInput"
              size="small"
              placeholder="请输入分组名称"
              @blur="saveGroupName"
              @keyup.enter.native="saveGroupName"
              @keyup.esc.native="cancelEditGroupName"
              class="group-input"
            />
            <div class="edit-actions">
              <el-button size="mini" type="primary" @click="saveGroupName" icon="el-icon-check"></el-button>
              <el-button size="mini" @click="cancelEditGroupName" icon="el-icon-close"></el-button>
            </div>
          </div>
        </div>

        <!-- 排序字段 -->
        <div class="app-sort-section">
          <div v-if="!editingSort" class="sort-display" @click="startEditSort" :title="'点击编辑排序'">
            <el-tag
              size="small"
              type="info"
              class="sort-tag editable-tag"
            >
              排序: {{ appDetail.sort || 0 }}
              <i class="el-icon-edit edit-icon"></i>
            </el-tag>
          </div>
          <div v-else class="sort-edit-section">
            <el-input-number
              v-model="editForm.sort"
              ref="sortInput"
              size="small"
              :min="0"
              :max="9999"
              placeholder="排序值"
              @blur="saveSort"
              @keyup.enter.native="saveSort"
              @keyup.esc.native="cancelEditSort"
              class="sort-input"
            />
            <div class="edit-actions">
              <el-button size="mini" type="primary" @click="saveSort" icon="el-icon-check"></el-button>
              <el-button size="mini" @click="cancelEditSort" icon="el-icon-close"></el-button>
            </div>
          </div>
        </div>

        <!-- 应用描述 -->
        <div class="app-description-section">
          <div v-if="!editingDescription" class="description-display" @click="startEditDescription" :title="'点击编辑应用描述'">
            <span class="description-text editable-text">
              {{ appDetail.description || '暂无描述' }}
              <i class="el-icon-edit edit-icon"></i>
            </span>
          </div>
          <div v-else class="description-edit-section">
            <el-input
              v-model="editForm.description"
              ref="descriptionInput"
              size="small"
              placeholder="请输入应用描述"
              maxlength="100"
              show-word-limit
              @blur="saveDescription"
              @keyup.enter.native="saveDescription"
              @keyup.esc.native="cancelEditDescription"
              class="description-input"
            />
            <div class="edit-actions">
              <el-button size="mini" type="primary" @click="saveDescription" icon="el-icon-check"></el-button>
              <el-button size="mini" @click="cancelEditDescription" icon="el-icon-close"></el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：状态信息 -->
      <div class="app-status-info">
        <div class="status-card">
          <div class="status-main">
            <div v-if="!editingEnabled" class="status-badge editable-status" :class="getAppStatusClass()" @click="startEditEnabled" :title="'点击编辑启用状态'">
              <i :class="getAppStatusIcon()"></i>
              <span class="status-text">{{ getAppStatusText() }}</span>
              <i class="el-icon-edit edit-icon"></i>
            </div>
            <div v-else class="status-edit-section">
              <el-switch
                v-model="editForm.enabled"
                ref="enabledSwitch"
                active-text="启用"
                inactive-text="禁用"
                @change="saveEnabled"
                class="enabled-switch"
              />
              <div class="edit-actions">
                <el-button size="mini" @click="cancelEditEnabled" icon="el-icon-close"></el-button>
              </div>
            </div>
          </div>
          
          <div class="status-details">
            <div class="status-item" v-if="appDetail.createTime">
              <span class="status-label">创建时间</span>
              <span class="status-value">{{ formatDate(appDetail.createTime) }}</span>
            </div>
            <div class="status-item" v-if="appDetail.updateTime">
              <span class="status-label">更新时间</span>
              <span class="status-value">{{ formatDate(appDetail.updateTime) }}</span>
            </div>
            <div class="status-item" v-if="appDetail.remark">
              <span class="status-label">备注</span>
              <span class="status-value" :title="appDetail.remark">{{ appDetail.remark }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getDefaultIconStyle,
  getAppIconLetter,
  getAppTypeTagType,
  getAppTypeLabel,
  formatDateTime,
  copyToClipboard
} from '@system/utils/appIconUtils'

export default {
  name: 'AppDetailHeader',
  props: {
    appDetail: {
      type: Object,
      default: () => ({})
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      editingName: false,
      editingDescription: false,
      editingEnabled: false,
      editingGroupName: false,
      editingSort: false,
      editForm: {
        name: '',
        description: '',
        enabled: false,
        groupName: '',
        sort: 0
      }
    }
  },
  methods: {
    // 返回上一页
    handleGoBack() {
      this.$emit('go-back')
    },

    // 获取默认图标样式
    getDefaultIconStyle() {
      return getDefaultIconStyle(this.appDetail.name, {
        fontSize: '18px',
        fontWeight: '600',
        color: '#fff'
      })
    },

    // 获取应用图标字母
    getAppIconLetter() {
      return getAppIconLetter(this.appDetail.name)
    },

    // 处理图片错误
    handleImageError(event) {
      event.target.style.display = 'none'
    },

    // 开始编辑名称
    startEditName() {
      if (!this.editable) return
      this.editForm.name = this.appDetail.name || ''
      this.editingName = true
      this.$nextTick(() => {
        this.$refs.nameInput.focus()
      })
    },

    // 保存应用名称
    async saveAppName() {
      if (!this.editForm.name.trim()) {
        this.$message.warning('应用名称不能为空')
        return
      }
      try {
        this.$emit('update-name', this.editForm.name)
        this.editingName = false
        this.$message.success('应用名称更新成功')
      } catch (error) {
        console.error('更新应用名称失败:', error)
        this.$message.error('更新应用名称失败')
      }
    },

    // 取消编辑名称
    cancelEditName() {
      this.editingName = false
      this.editForm.name = ''
    },

    // 开始编辑描述
    startEditDescription() {
      if (!this.editable) return
      this.editForm.description = this.appDetail.description || ''
      this.editingDescription = true
      this.$nextTick(() => {
        this.$refs.descriptionInput.focus()
      })
    },

    // 保存应用描述
    async saveDescription() {
      try {
        this.$emit('update-description', this.editForm.description)
        this.editingDescription = false
        this.$message.success('应用描述更新成功')
      } catch (error) {
        console.error('更新应用描述失败:', error)
        this.$message.error('更新应用描述失败')
      }
    },

    // 取消编辑描述
    cancelEditDescription() {
      this.editingDescription = false
      this.editForm.description = ''
    },

    // 开始编辑启用状态
    startEditEnabled() {
      if (!this.editable) return
      this.editForm.enabled = this.appDetail.enabled || false
      this.editingEnabled = true
    },

    // 保存启用状态
    async saveEnabled() {
      try {
        this.$emit('update-enabled', this.editForm.enabled)
        this.editingEnabled = false
        this.$message.success('应用状态更新成功')
      } catch (error) {
        console.error('更新应用状态失败:', error)
        this.$message.error('更新应用状态失败')
      }
    },

    // 取消编辑启用状态
    cancelEditEnabled() {
      this.editingEnabled = false
      this.editForm.enabled = false
    },

    // 开始编辑分组名称
    startEditGroupName() {
      if (!this.editable) return
      this.editForm.groupName = this.appDetail.groupName || ''
      this.editingGroupName = true
      this.$nextTick(() => {
        this.$refs.groupNameInput.focus()
      })
    },

    // 保存分组名称
    async saveGroupName() {
      try {
        this.$emit('update-group-name', this.editForm.groupName)
        this.editingGroupName = false
        this.$message.success('应用分组更新成功')
      } catch (error) {
        console.error('更新应用分组失败:', error)
        this.$message.error('更新应用分组失败')
      }
    },

    // 取消编辑分组名称
    cancelEditGroupName() {
      this.editingGroupName = false
      this.editForm.groupName = ''
    },

    // 开始编辑排序
    startEditSort() {
      if (!this.editable) return
      this.editForm.sort = this.appDetail.sort || 0
      this.editingSort = true
      this.$nextTick(() => {
        this.$refs.sortInput.focus()
      })
    },

    // 保存排序
    async saveSort() {
      try {
        this.$emit('update-sort', this.editForm.sort)
        this.editingSort = false
        this.$message.success('应用排序更新成功')
      } catch (error) {
        console.error('更新应用排序失败:', error)
        this.$message.error('更新应用排序失败')
      }
    },

    // 取消编辑排序
    cancelEditSort() {
      this.editingSort = false
      this.editForm.sort = 0
    },

    // 开始编辑描述
    startEditDescription() {
      if (!this.editable) return
      this.editForm.description = this.appDetail.description || ''
      this.editingDescription = true
      this.$nextTick(() => {
        this.$refs.descriptionInput.focus()
      })
    },

    // 保存应用描述
    async saveDescription() {
      try {
        this.$emit('update-description', this.editForm.description)
        this.editingDescription = false
        this.$message.success('应用描述更新成功')
      } catch (error) {
        console.error('更新应用描述失败:', error)
        this.$message.error('更新应用描述失败')
      }
    },

    // 取消编辑描述
    cancelEditDescription() {
      this.editingDescription = false
      this.editForm.description = ''
    },

    // 复制应用编码
    copyCode() {
      const code = this.appDetail.code || 'APP-CODE'
      copyToClipboard(
        code,
        () => this.$message.success('应用编码已复制到剪贴板'),
        () => this.$message.error('复制失败，请手动复制')
      )
    },

    // 获取类型标签类型
    getTypeTagType() {
      return getAppTypeTagType(this.appDetail.type)
    },

    // 获取类型标签文本
    getTypeLabel() {
      return getAppTypeLabel(this.appDetail.type)
    },

    // 获取应用状态样式类
    getAppStatusClass() {
      return this.appDetail.enabled ? 'status-enabled' : 'status-disabled'
    },

    // 获取应用状态图标
    getAppStatusIcon() {
      return this.appDetail.enabled ? 'el-icon-check' : 'el-icon-close'
    },

    // 获取应用状态文本
    getAppStatusText() {
      return this.appDetail.enabled ? '启用' : '禁用'
    },

    // 格式化日期
    formatDate(dateString) {
      return formatDateTime(dateString)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-detail-header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  margin-bottom: 16px;

  .header-main {
    padding: 12px 16px;
    display: flex;
    gap: 16px;
    align-items: center;
    min-height: 56px;

    .header-back {
      flex-shrink: 0;

      .back-btn {
        font-size: 13px;
        color: #666;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.06);
        }
      }
    }

    .app-basic-info {
      flex: 1;
      display: flex;
      gap: 16px;
      min-width: 0;
      align-items: center;

      .app-icon-section {
        flex-shrink: 0;

        .app-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f2f5;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .default-icon {
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .app-name-section {
        flex-shrink: 0;
        min-width: 120px;

        .app-name {
          font-size: 18px;
          font-weight: 600;
          color: #262626;
          margin: 0;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 4px;
          padding: 2px 6px;
          border-radius: 4px;
          transition: all 0.2s ease;
          line-height: 1.3;
          white-space: nowrap;

          &:hover {
            background: rgba(24, 144, 255, 0.06);
            color: #1890ff;

            .edit-icon {
              opacity: 1;
            }
          }

          .edit-icon {
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
        }

        .name-edit-section {
          display: flex;
          align-items: center;
          gap: 8px;

          .name-input {
            width: 150px;
          }

          .edit-actions {
            display: flex;
            gap: 4px;
          }
        }
      }

      .app-code-section {
        flex-shrink: 0;

        .code-tag {
          cursor: pointer;
          transition: all 0.2s ease;
          display: inline-flex;
          align-items: center;
          gap: 4px;
          font-family: 'Monaco', 'Menlo', monospace;
          font-size: 12px;

          &:hover {
            background: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
            transform: translateY(-1px);
          }

          .copy-icon {
            font-size: 11px;
          }
        }
      }

      .app-type-section {
        flex-shrink: 0;

        .type-tag {
          font-weight: 600;
          font-size: 12px;
        }
      }

      .app-group-section {
        flex-shrink: 0;

        .group-display {
          cursor: pointer;

          .group-tag {
            background: #fff7e6;
            border-color: #ffd591;
            color: #d46b08;
            font-size: 12px;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;

            &.editable-tag:hover {
              background: #ffe7ba;
              border-color: #ffb366;
              transform: translateY(-1px);

              .edit-icon {
                opacity: 1;
              }
            }

            .edit-icon {
              font-size: 11px;
              opacity: 0;
              transition: opacity 0.2s ease;
            }
          }
        }

        .group-edit-section {
          display: flex;
          align-items: center;
          gap: 8px;

          .group-input {
            width: 120px;
          }

          .edit-actions {
            display: flex;
            gap: 4px;
          }
        }
      }

      .app-sort-section {
        flex-shrink: 0;

        .sort-display {
          cursor: pointer;

          .sort-tag {
            font-size: 12px;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;

            &.editable-tag:hover {
              background: #e6f7ff;
              border-color: #91d5ff;
              color: #1890ff;
              transform: translateY(-1px);

              .edit-icon {
                opacity: 1;
              }
            }

            .edit-icon {
              font-size: 11px;
              opacity: 0;
              transition: opacity 0.2s ease;
            }
          }
        }

        .sort-edit-section {
          display: flex;
          align-items: center;
          gap: 8px;

          .sort-input {
            width: 100px;
          }

          .edit-actions {
            display: flex;
            gap: 4px;
          }
        }
      }

      .app-description-section {
        flex: 1;
        min-width: 0;
        margin-left: 8px;

        .description-display {
          cursor: pointer;

          .description-text {
            font-size: 13px;
            color: #595959;
            line-height: 1.4;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 6px;
            border-radius: 4px;
            transition: all 0.2s ease;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 200px; // 限制最大宽度避免换行

            &.editable-text:hover {
              background: rgba(24, 144, 255, 0.06);
              color: #1890ff;

              .edit-icon {
                opacity: 1;
              }
            }

            .edit-icon {
              font-size: 12px;
              opacity: 0;
              transition: opacity 0.2s ease;
              flex-shrink: 0;
            }
          }
        }

        .description-edit-section {
          display: flex;
          align-items: center;
          gap: 8px;

          .description-input {
            width: 200px; // 固定宽度避免换行
          }

          .edit-actions {
            display: flex;
            gap: 4px;
            flex-shrink: 0;
          }
        }
      }
    }

    .app-status-info {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 12px;

      .status-card {
        display: flex;
        align-items: center;
        gap: 12px;
        background: #fafbfc;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        padding: 8px 12px;

        .status-main {
          .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            white-space: nowrap;

            i {
              font-size: 12px;
            }

            .status-text {
              font-weight: 600;
            }

            &.editable-status {
              cursor: pointer;

              .edit-icon {
                font-size: 11px;
                opacity: 0;
                transition: opacity 0.2s ease;
                margin-left: 4px;
              }

              &:hover .edit-icon {
                opacity: 1;
              }
            }

            &.status-enabled {
              background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
              color: #ffffff;
              box-shadow: 0 1px 6px rgba(82, 196, 26, 0.2);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
              }
            }

            &.status-disabled {
              background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
              color: #ffffff;
              box-shadow: 0 1px 6px rgba(255, 77, 79, 0.2);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
              }
            }
          }

          .status-edit-section {
            display: flex;
            align-items: center;
            gap: 8px;

            .enabled-switch {
              margin-right: 8px;
            }

            .edit-actions {
              display: flex;
              gap: 4px;
            }
          }
        }

        .status-details {
          display: flex;
          gap: 16px;

          .status-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            white-space: nowrap;

            .status-label {
              font-size: 11px;
              color: #8c8c8c;
              font-weight: 500;
            }

            .status-value {
              font-size: 12px;
              color: #262626;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .header-main {
      flex-wrap: wrap;
      gap: 12px;

      .app-basic-info {
        .app-description-section {
          display: none; // 在中等屏幕隐藏描述
        }
      }

      .app-status-info {
        .status-card .status-details {
          gap: 12px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .header-main {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .app-basic-info {
        flex-wrap: wrap;
        gap: 12px;

        .app-name-section {
          min-width: auto;
          flex: 1;
        }

        .app-description-section {
          display: block;
          flex: 1 100%;
          margin-left: 0;
          margin-top: 8px;
        }
      }

      .app-status-info {
        justify-content: center;

        .status-card {
          flex-direction: column;
          gap: 8px;
          text-align: center;

          .status-details {
            flex-direction: row;
            justify-content: center;
            gap: 16px;
          }
        }
      }
    }
  }
}
</style>
