<template>
    <div>
        <el-tree
            :default-checked-keys="eTreeValue"
            :data="options"
            :show-checkbox="showCheckbox"
            :default-expand-all="defaultExpandAll"
            ref="dept"
            node-key="id"
            :check-strictly="checkStrictly"
            :empty-text="emptyText"
            :props="props"
            :render-after-expand="renderAfterExpand"
            :highlight-current="highlightCurrent"
            :expand-on-click-node="expandOnClickNode"
            :check-on-click-node="checkOnClickNode"
            :accordion="accordion"
            :indent="indent"
            :disabled="disabled"
        ></el-tree>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                eTreeValue: null
            }
        },
        computed: {
            eTrees: {
                get() {
                    return this.value == null ? null : this.value
                },
                set(val) {
                    return val
                }
            }
        },
        watch: {
            eTrees: {
                handler(val) {
                    this.eTreeValue = val
                },
                deep: true,
                immediate: true
            },
            eTreeValue: {
                handler(val) {
                    this.$emit('value', val)
                },
                deep: true,
                immediate: true
            },
        },
        model: {
            props: 'value',
            event: 'value'
        },
        props: {
            disabled: {
                type: Boolean,
                default: false
            },
            value: {
                type: String,
                default: null
            },
            options: {
                type: Array,
                default: function name(params) {
                    return []
                }
            },
            emptyText: {
                type: String,
                default: ''
            },
            props: {
                type: Object,
                default: function name(params) {
                    return {}
                }
            },
            renderAfterExpand: {
                type: Boolean,
                default: true
            },
            highlightCurrent: {
                type: Boolean,
                default: false
            },
            defaultExpandAll: {
                type: Boolean,
                default: false
            },
            expandOnClickNode: {
                type: Boolean,
                default: true
            },
            checkOnClickNode: {
                type: Boolean,
                default: false
            },
            showCheckbox: {
                type: Boolean,
                default: false
            },
            checkStrictly: {
                type: Boolean,
                default: false
            },
            accordion: {
                type: Boolean,
                default: false
            },
            indent: {
                type: Number,
                default: 16
            },
        },
    }
</script>