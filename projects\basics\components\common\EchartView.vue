<template>
  <div class="echart-view" ref="chart" />
</template>

<script>
import { baseOption, baseSeriesOption } from "./echart-view-base-option";

import * as Echarts from "echarts/index.js";

export default {
  name: "EchartView",
  props: {
    option: {
      type: Object,
      default: () => ({
        legend: {
          show: true,
          data: [],
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110],
            type: "bar",
            showBackground: true,
            backgroundStyle: {
              color: "rgba(57, 211, 236, 1)",
            },
          },
        ],
      }),
    },
  },

  watch: {
    option: {
      handler(val) {
        const option = this.objectDeepMerge(baseOption(), this.option);
        option.series = option.series.map((s) =>
          this.objectDeepMerge(baseSeriesOption(), s)
        );
        this.chart.setOption(option);
      },
      deep: true,
    },
  },
  mounted() {
    this.chart = Echarts.init(this.$refs.chart, null, { renderer: "svg" });

    this.$nextTick(() => {
      const option = this.objectDeepMerge(baseOption(), this.option);
      option.series = option.series.map((s) =>
        this.objectDeepMerge(baseSeriesOption(), s)
      );
      this.chart.setOption(option);
      setInterval(() => {
        window.addEventListener("resize", () => {
          this.chart.resize();
        });
      }, 200);
    });
    return;

    setInterval(() => {
      option.series.forEach((s) => {
        const rate = ((new Date().getTime() % 2000) / 2000) * Math.PI;

        s.lineStyle = s.lineStyle || { color: {} };
        const color = s.lineStyle.color;
        color.x = color.x + 0.01;
        color.x2 = color.x + 0.2;
        if (color.x > 1) {
          color.x = -0.2;
          color.x2 = 0;
        }

        s.itemStyle = s.itemStyle || {};
        s.itemStyle.shadowBlur = s.itemStyle.shadowBlur || 0;
        s.itemStyle.shadowBlur = Math.sin(rate) * 10;
      });
      this.chart.setOption(option);
    }, 50);
  },
  methods: {
    objectDeepMerge(target, src) {
      Object.keys(src).forEach((key) => {
        const val = src[key];
        if (typeof target[key] !== "object" || typeof val !== "object") {
          target[key] = val;
        } else {
          this.objectDeepMerge(target[key], val);
        }
      });
      return target;
    },
  },
};
</script>

<style scoped>
.echart-view {
  width: 100%;
  height: 100%;
}

:deep(div) {
  width: 100% !important;
  height: 100% !important;
}
</style>
