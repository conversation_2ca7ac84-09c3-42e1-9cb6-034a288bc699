<template>
  <div class="chat-input">
    <div class="input-area">
      <div class="textarea-wrapper">
        <button v-if="message" class="clear-input-button" @click="clearMessage" type="button">
          <i class="el-icon-close"></i>
        </button>
        <textarea v-model="message" :placeholder="placeholder" :disabled="loading || disabled" ref="textarea" rows="3"
          @input="adjustHeight"></textarea>
      </div>
      <div class="button-group">
        <button v-if="supportsSpeech" class="voice-button" @click="toggleVoiceInput"
          :class="{ 'recording': isRecording }" :disabled="loading || disabled">
          <i class="microphone-icon"></i>
        </button>
        <button class="send-button" @click="handleSend"
          :disabled="loading || disabled || !message.trim() || !selectedModel">
          <span class="button-text">{{ loading ? '发送中...' : '发送' }}</span>
          <i class="send-icon"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatInput',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入问题...'
    },
    selectedModel: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      message: '',
      minHeight: 72,
      maxHeight: 150,
      isRecording: false,
      recognition: null,
      supportsSpeech: false
    }
  },
  methods: {
    handleSend() {
      if (!this.message.trim() || this.loading || this.disabled || !this.selectedModel) return
      this.$emit('send', {
        message: this.message,
        model: this.selectedModel
      })
      this.message = ''
      this.$nextTick(() => {
        this.adjustHeight()
      })
    },
    adjustHeight() {
      const textarea = this.$refs.textarea
      if (!textarea) return

      textarea.style.height = 'auto'
      const newHeight = Math.min(
        Math.max(textarea.scrollHeight, this.minHeight),
        this.maxHeight
      )
      textarea.style.height = `${newHeight}px`
    },
    initSpeechRecognition() {
      if ('webkitSpeechRecognition' in window) {
        this.recognition = new window.webkitSpeechRecognition()
        this.recognition.continuous = true
        this.recognition.interimResults = true
        this.recognition.lang = 'zh-CN'

        this.recognition.onresult = (event) => {
          console.log(event);

          let finalTranscript = ''
          for (let i = event.resultIndex; i < event.results.length; ++i) {
            if (event.results[i].isFinal) {
              finalTranscript += event.results[i][0].transcript
            }
          }
          if (finalTranscript) {
            this.message = this.message + finalTranscript
          }
        }

        this.recognition.onerror = (event) => {
          console.error('语音识别错误:', event.error)
          this.stopRecording()
        }

        this.recognition.onend = () => {
          this.stopRecording()
        }
      } else {
        console.warn('浏览器不支持语音识别')
      }
    },
    toggleVoiceInput() {
      if (!this.supportsSpeech) {
        console.warn('浏览器不支持语音识别');
        return;
      }

      if (this.isRecording) {
        this.stopRecording()
      } else {
        this.startRecording()
      }
    },
    startRecording() {
      if (!this.recognition) {
        this.initSpeechRecognition()
      }

      if (this.recognition) {
        try {
          this.recognition.start()
          this.isRecording = true
        } catch (error) {
          console.error('启动语音识别失败:', error)
        }
      }
    },
    stopRecording() {
      if (this.recognition) {
        this.recognition.stop()
        this.isRecording = false
      }
    },
    setMessage(message) {
      this.message = message;
      this.$nextTick(() => {
        this.adjustHeight();
        if (this.$refs.textarea) {
          this.$refs.textarea.focus();
        }
      });
    },
    clearMessage() {
      this.message = '';
      this.$nextTick(() => {
        this.adjustHeight();
        if (this.$refs.textarea) {
          this.$refs.textarea.focus();
        }
      });
    }
  },
  mounted() {
    this.adjustHeight()
    this.supportsSpeech = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    if (this.supportsSpeech) {
      this.initSpeechRecognition();
    }
  },
  beforeDestroy() {
    if (this.recognition) {
      this.recognition.stop()
    }
  }
}
</script>

<style scoped>
.chat-input {
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.input-area {
  display: flex;
  gap: 12px;
  align-items: stretch;
  height: 100%;
}

.textarea-wrapper {
  flex: 1;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
}

.textarea-wrapper:focus-within {
  border-color: #42b983;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(66, 185, 131, 0.1);
}

textarea {
  width: 100%;
  min-height: 72px;
  max-height: 150px;
  padding: 8px;
  border: none;
  background: transparent;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
  overflow-y: auto;
  margin: 0;
}

textarea:disabled {
  background: transparent;
  cursor: not-allowed;
  color: #adb5bd;
}

.send-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 0 24px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  flex-shrink: 0;
  white-space: nowrap;
  height: 100%;
  width: 120px;
}

.send-button:hover:not(:disabled) {
  background: #3aa876;
  transform: translateY(-1px);
}

.send-button:active:not(:disabled) {
  transform: translateY(0);
}

.send-button:disabled {
  background: #adb5bd;
  cursor: not-allowed;
  opacity: 0.8;
}

.button-text {
  line-height: 1;
}

.send-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M2.01 21L23 12 2.01 3 2 10l15 2-15 2z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  margin-top: -1px;
}

/* 滚动条样式 */
textarea::-webkit-scrollbar {
  width: 4px;
}

textarea::-webkit-scrollbar-track {
  background: transparent;
}

textarea::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

.button-group {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.voice-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 0 16px;
  height: 40px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.voice-button:hover:not(:disabled) {
  background: #e5e7eb;
}

.voice-button.recording {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  animation: pulse 1.5s infinite;
}

.voice-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.microphone-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@media screen and (max-width: 768px) {
  .chat-input {
    padding: 10px;
  }

  .input-area {
    flex-direction: column;
    gap: 8px;
  }

  .textarea-wrapper {
    padding: 8px;
    border-radius: 10px;
  }

  textarea {
    min-height: 45px;
    max-height: 120px;
    font-size: 14px;
    line-height: 1.4;
  }

  .button-group {
    width: 100%;
    justify-content: space-between;
    align-items: center;
    height: 40px;
  }

  .send-button {
    height: 40px;
    width: auto;
    flex: 1;
    margin-left: 8px;
    font-size: 14px;
    padding: 0 16px;
    border-radius: 10px;
  }

  .voice-button {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 10px;
  }

  .microphone-icon {
    width: 18px;
    height: 18px;
  }

  .send-icon {
    width: 14px;
    height: 14px;
  }

  /* 优化按钮点击区域 */
  .voice-button,
  .send-button {
    min-height: 40px;
    touch-action: manipulation;
  }

  /* 调整发送按钮文字和图标间距 */
  .send-button .button-text {
    margin-right: 4px;
  }
}

.clear-input-button {
  position: absolute;
  right: 10px;
  top: 10px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  opacity: 0.6;
  z-index: 1;
}

.clear-input-button:hover {
  background: #e5e7eb;
  opacity: 1;
}

.clear-input-button i {
  font-size: 16px;
  color: #4a5568;
}

@media screen and (max-width: 768px) {
  .clear-input-button {
    padding: 6px;
  }

  .clear-input-button i {
    font-size: 14px;
  }

  /* 调整输入框右侧内边距，避免文字被清空按钮遮挡 */
  textarea {
    padding-right: 32px;
  }
}
</style>