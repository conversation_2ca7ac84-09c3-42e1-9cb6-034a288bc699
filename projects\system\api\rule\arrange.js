import request from '@/utils/request'

// 后端接口前缀
const baseUrl = 'rule/flow/dsl'

// 流程DSL API接口
export default {
  /**
   * 保存流程定义
   * @param {Object} definition 流程定义对象
   * @returns {Promise<String>} 返回流程链ID
   */
  saveDefinition(definition) {
    return request({
      url: `${baseUrl}/definition`,
      method: 'post',
      data: definition
    })
  },

  /**
   * 执行流程
   * @param {String} chainId 流程链ID
   * @param {Object} inputData 输入数据
   * @returns {Promise<Object>} 流程执行结果
   */
  executeFlow(chainId, inputData) {
    return request({
      url: `${baseUrl}/execute`,
      method: 'post',
      data: {
        chainId,
        inputData
      }
    })
  },
  
  /**
   * 获取编排链详情
   * @param {Number} chainId 编排链ID
   * @returns {Promise<Object>} 编排链详情
   */
  getChainDetail(chainId) {
    console.log('请求编排链详情，ID:', chainId);
    return request({
      url: `/rule/rule/chain`,
      method: 'get',
      params: { id: chainId }
    }).then(res => {
      console.log('获取到编排链详情:', res);
      return res;
    }).catch(err => {
      console.error('获取编排链详情失败:', err);
      throw err;
    });
  },
  
  /**
   * 更新编排链
   * @param {Object} chain 编排链数据
   * @returns {Promise<Object>} 更新结果
   */
  updateChain(chain) {
    return request({
      url: `/rule/rule/chain`,
      method: 'put',
      data: chain
    })
  },

  addChain(chain) {
    return request({
      url: `/rule/rule/chain`,
      method: 'post',
      data: chain
    })
  }
}


