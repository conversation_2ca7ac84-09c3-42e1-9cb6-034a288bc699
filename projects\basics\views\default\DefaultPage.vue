<template>
    <div>
        <auto-page v-if="options" :scope="options.scope" :collectionName="collectionName" :options="options"
            :form-rule="options.formRule" :defaultQuery="defaultQuery" ref="rules"> </auto-page>
    </div>
</template>

<script>
import AutoPage from "@/components/auto-page/AutoPage";
import { formJson } from '@/api/form/index'

export default {
    components: { AutoPage },
    data() {
        return {
            collectionName: null,
            options: null,
            defaultQuery: { orders: { 'updateTime': 'desc', 'updated_time': 'desc' } }
        }
    },
    mounted() {
        let query = this.$route.query
        this.defaultQuery = Object.assign(this.defaultQuery, query)

        let result = this.findCurrentRouteInfo(this.$route.name)


        if (result[0].meta.form) {
            this.collectionName = result[0].path
            this.getFormJson(result[0].path?.replace(`${result[0].parentPath}/`, ''))
        } else {
            this.options = result[0].json
        }

    },
    methods: {
        getForm<PERSON>son(path) {
            formJson(path).then(res => {
                this.options = res
            })
        }
    }
}
</script>

<style lang="scss" scoped></style>