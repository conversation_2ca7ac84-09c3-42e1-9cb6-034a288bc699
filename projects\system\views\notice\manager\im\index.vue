<template>
  <div class="message-management">
    <div class="message-content-container">
      <div class="message-card">
        <!-- 标题和搜索栏 -->
        <div class="card-header">
          <div class="header-title">
            <span>实时消息</span>
          </div>
          
          <div class="header-tools">
            <div class="unified-search">
              <el-select v-model="searchForm.chatType" placeholder="消息类型" class="type-select">
                <el-option label="私聊消息" value="PRIVATE" />
                <el-option label="系统消息" value="GROUP" />
                <el-option label="公告消息" value="BROADCAST" />
              </el-select>
              
              <el-input v-model="searchForm.fromUsername" placeholder="请输入发送人" clearable class="search-input" />
              
              <el-input v-model="searchForm.receiver" placeholder="请输入接收人/群" clearable class="search-input" />
              
              <el-input v-model="searchForm.content" placeholder="请输入消息内容" clearable @keyup.enter.native="handleSearch" class="search-input" />
            </div>

            <div class="button-group">
              <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </div>
          </div>
        </div>

        <!-- 批量操作栏 -->
        <div class="batch-actions-bar" v-show="selectedMessages.length">
          <div class="selection-info">
            已选择 <span class="count">{{ selectedMessages.length }}</span> 项
            <el-button type="text" @click="$refs.messageTable.clearSelection()">清空选择</el-button>
          </div>
          <div class="batch-buttons">
            <el-button-group class="operation-group">
              <el-button 
                type="danger" 
                icon="el-icon-delete" 
                size="small"
                @click="handleBatchRecall">批量撤回</el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="table-wrapper">
          <el-table
            ref="messageTable"
            :data="messageList"
            v-loading="loading"
            border
            stripe
            style="width: 100%"
            height="calc(100% - 56px)"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" :width=rpx(50) align="center" />
            <el-table-column prop="chatType" label="消息类型" :width=rpx(120)>
              <template slot-scope="{ row }">
                <el-tag :type="getChatTypeTag(row.chatType)">
                  {{ getChatTypeLabel(row.chatType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="fromUsername" label="发送人" min-width="30" />
            <el-table-column label="接收人/群" min-width="30">
              <template slot-scope="{ row }">
                <span>{{ getReceiverDisplay(row) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="消息内容" min-width="200">
              <template slot-scope="{ row }">
                <div class="message-content">
                  <div v-if="row.replyMessageContent" class="reply-message">
                    <div class="reply-content">{{ getTruncatedContent(row.replyMessageContent) }}</div>
                  </div>
                  <div class="main-content">{{ getTruncatedContent(row.content) }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="sendTime" label="发送时间" width="180" />
            <el-table-column prop="recall" label="状态" width="100">
              <template slot-scope="{ row }">
                <el-tag :type="row.recall ? 'info' : 'success'" effect="dark">
                  {{ row.recall ? '已撤回' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template slot-scope="{ row }">
                <el-dropdown trigger="click" @command="(command) => handleCommand(command, row)">
                  <el-button type="text" class="action-button">
                    操作<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="view">
                      <i class="el-icon-view"></i> 查看
                    </el-dropdown-item>
                    <el-dropdown-item v-if="!row.recall" command="edit" divided>
                      <i class="el-icon-edit"></i> 编辑
                    </el-dropdown-item>
                    <el-dropdown-item v-if="!row.recall" command="recall" divided class="danger">
                      <i class="el-icon-delete"></i> 撤回
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              :current-page="page.current"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="page.size"
              :total="page.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 消息详情对话框 -->
    <el-dialog
      title="消息详情"
      :visible.sync="dialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      custom-class="message-detail-dialog"
    >
      <div v-if="currentMessage">
        <h3 class="message-title">{{ currentMessage.fromUsername }}</h3>
        <div class="message-content-container">
          <div v-if="currentMessage.replyMessageContent" class="reply-message">
            <div class="reply-content">{{ currentMessage.replyMessageContent }}</div>
          </div>
          <div class="message-content rich-content" v-html="currentMessage.content"></div>
        </div>
        <div class="message-meta">
          <span class="time">{{ currentMessage.sendTime }}</span>
          <el-tag :type="currentMessage.recall ? 'info' : 'success'" effect="dark">
            {{ currentMessage.recall ? '已撤回' : '正常' }}
          </el-tag>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑消息对话框 -->
    <el-dialog
      title="编辑消息"
      :visible.sync="editDialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      custom-class="message-detail-dialog"
    >
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="消息类型">
          <el-tag :type="getChatTypeTag(editForm.chatType)">
            {{ getChatTypeLabel(editForm.chatType) }}
          </el-tag>
        </el-form-item>
        <el-form-item label="发送人">
          <span>{{ editForm.fromUsername }}</span>
        </el-form-item>
        <el-form-item label="接收人/群">
          <span>{{ editForm.receiver }}</span>
        </el-form-item>
        <el-form-item label="消息内容" prop="content">
          <div class="edit-content-container">
            <el-input
              v-model="editForm.content"
              type="textarea"
              :rows="8"
              placeholder="请输入消息内容"
            ></el-input>
            <div class="preview-container" v-if="editForm.content">
              <div class="preview-title">预览</div>
              <div class="preview-content rich-content" v-html="editForm.content" style="min-height: 100px; max-height: 300px; overflow-y: auto;"></div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit" :loading="editLoading">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMessageList, recallMessage, batchRecallMessages, updateMessage } from '@system/api/notice/im'

export default {
  name: 'MessageManagement',
  data() {
    return {
      // 搜索相关
      searchForm: {
        chatType: 'BROADCAST',
        fromUsername: '',
        receiver: '',
        content: ''
      },

      // 消息列表相关
      loading: false,
      messageList: [],
      page: {
        current: 1,
        size: 10,
        total: 0
      },

      // 选中的消息
      selectedMessages: [],

      // 详情对话框
      dialogVisible: false,
      currentMessage: {},

      // 编辑相关
      editDialogVisible: false,
      editForm: {
        id: '',
        chatType: '',
        fromUsername: '',
        receiver: '',
        content: ''
      },
      editRules: {
        content: [
          { required: true, message: '请输入消息内容', trigger: 'blur' }
        ]
      },
      editLoading: false,

      // 消息内容最大显示长度
      contentMaxLength: 50,
    }
  },
  created() {
    // 确保页面加载时立即执行查询
    this.$nextTick(() => {
      this.getMessageList()
    })
  },
  methods: {
    // 获取消息列表
    async getMessageList() {
      // 验证消息类型是否已选择
      try {
        if (!this.searchForm.chatType) {
          this.$message.warning('请选择消息类型');
          return;
        }
      } catch (error) {
        return
      }

      this.loading = true
      const params = {
        ...this.searchForm,
        current: this.page.current,
        size: this.page.size,
        'orders[updateTime]': 'desc',
        'orders[sendTime]': 'desc',
      }
      
      const { records, total } = await getMessageList(params)
      this.messageList = records || []
      this.page.total = total || 0
      this.loading = false
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      const actions = {
        view: () => this.handleView(row),
        edit: () => this.handleEdit(row),
        recall: () => this.handleRecall(row)
      }
      if (actions[command]) {
        actions[command]()
      }
    },

    // 查看消息详情
    handleView(row) {
      this.currentMessage = { ...row }
      this.dialogVisible = true
    },

    // 编辑消息
    handleEdit(row) {
      this.editForm = {
        id: row.id,
        chatType: row.chatType,
        fromUsername: row.fromUsername,
        receiver: row.receiver,
        content: row.content
      }
      this.editDialogVisible = true
    },

    // 撤回单条消息
    async handleRecall(row) {
      try {
        await this.$confirm('确定要撤回该消息吗？', '提示', {
          type: 'warning'
        })
        await recallMessage(row.id)
        this.$message.success('撤回成功')
        this.getMessageList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('撤回失败')
        }
      }
    },

    // 批量撤回消息
    async handleBatchRecall() {
      try {
        await this.$confirm(`确定要撤回选中的 ${this.selectedMessages.length} 条消息吗？`, '提示', {
          type: 'warning'
        })
        const ids = this.selectedMessages.map(msg => msg.id)
        await batchRecallMessages(ids)
        this.$message.success('批量撤回成功')
        this.getMessageList()
        this.$refs.messageTable.clearSelection()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量撤回失败')
        }
      }
    },

    // 表格选择相关
    handleSelectionChange(selection) {
      this.selectedMessages = selection
    },

    // 搜索相关
    handleSearch() {
      if (!this.searchForm.chatType) {
        this.$message.warning('请选择消息类型');
        return;
      }
      this.page.current = 1;
      this.getMessageList();
    },

    resetSearch() {
      this.searchForm = {
        chatType: 'PRIVATE',
        fromUsername: '',
        receiver: '',
        content: ''
      }
      this.handleSearch()
    },

    // 分页相关
    handleSizeChange(val) {
      this.page.size = val
      this.getMessageList()
    },
    handleCurrentChange(val) {
      this.page.current = val
      this.getMessageList()
    },

    // 工具方法
    getChatTypeLabel(type) {
      const types = {
        PRIVATE: '私聊消息',
        GROUP: '系统消息',
        BROADCAST: '公告消息'
      }
      return types[type] || type
    },

    getChatTypeTag(type) {
      const types = {
        PRIVATE: 'success',
        GROUP: 'warning',
        BROADCAST: 'info'
      }
      return types[type] || ''
    },

    // 获取接收人显示文本
    getReceiverDisplay(row) {
      if (row.chatType === 'BROADCAST' && !row.receiver) {
        return '全体人员'
      }
      if (row.displayName) {
        return row.displayName
      }
      if (row.chatType === 'GROUP' && row.groupName) {
        return row.groupName
      }
      return row.receiver || '-'
    },

    // 获取缩略文本
    getTruncatedContent(content) {
      if (!content) return ''
      // 移除HTML标签
      const plainText = content.replace(/<[^>]+>/g, '')
      if (plainText.length <= this.contentMaxLength) return plainText
      return plainText.substring(0, this.contentMaxLength) + '...'
    },

    // 保存编辑
    async handleSaveEdit() {
      try {
        await this.$refs.editForm.validate()
        this.editLoading = true
        
        await updateMessage({
          id: this.editForm.id,
          content: this.editForm.content
        })
        
        this.$message.success('更新成功')
        this.editDialogVisible = false
        this.getMessageList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更新失败:', error)
          this.$message.error('更新失败：' + (error.message || '未知错误'))
        }
      } finally {
        this.editLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.message-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  overflow: hidden;

  .message-content-container {
    flex: 1;
    background: transparent;
    display: flex;
    overflow: hidden;
    padding: 0;
    min-width: 0;

    .message-card {
      background: #fff;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
      transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
      width: 100%;
      display: flex;
      flex-direction: column;
      height: 100%;
      
      &:hover {
        box-shadow: 0 10px 30px rgba(31, 45, 61, 0.1);
      }

      .card-header {
        padding: 20px;
        border-bottom: 1px solid #eef1f7;
        background: linear-gradient(to right, #fcfcfd, #f9fafc);
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-title {
          font-size: 20px;
          font-weight: 600;
          color: #1a1f36;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          white-space: nowrap;
        }
        
        .header-tools {
          display: flex;
          align-items: center;
          gap: 16px;
          flex: 1;
          justify-content: flex-end;
          
          .unified-search {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap;
            
            .type-select {
              width: 120px;
            }
            
            .search-input {
              width: 160px;
            }
            
            ::v-deep .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              border-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }
          }
          
          .button-group {
            display: flex;
            gap: 8px;
            
            .el-button {
              padding: 8px 16px;
              font-weight: 500;
              transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
              position: relative;
              border-radius: 8px;
              height: 36px;
              
              &[type="primary"] {
                background-color: #409EFF;
                border-color: #409EFF;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
                
                &:hover {
                  background-color: #5aacff;
                  border-color: #5aacff;
                  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
                }
              }
              
              i {
                margin-right: 6px;
                font-size: 16px;
              }
            }
          }
        }
      }

      .batch-actions-bar {
        background: linear-gradient(to right, #f5f7fa, #f9fafc);
        padding: 14px 20px;
        margin: 0;
        border-radius: 0;
        border-left: none;
        border-right: none;
        border-top: none;
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        animation: fadeIn 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        border: 1px solid #eef1f7;

        .selection-info {
          color: #606266;
          font-size: 14px;
          display: flex;
          align-items: center;

          .count {
            color: #409EFF;
            font-weight: 600;
            margin: 0 4px;
            display: inline-block;
            min-width: 24px;
            height: 24px;
            line-height: 24px;
            background: rgba(64, 158, 255, 0.1);
            border-radius: 12px;
            padding: 0 8px;
            text-align: center;
          }

          .el-button {
            margin-left: 12px;
            padding: 0;
            color: #909399;
            
            &:hover {
              color: #409EFF;
            }
          }
        }
      }

      .table-wrapper {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
        background: #fff;
        
        ::v-deep .el-table {
          flex: 1;
          
          .el-table__inner-wrapper {
            height: 100%;
          }
          
          .el-table__header-wrapper {
            th {
              background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
              font-weight: 600;
              color: #1a1f36;
            }
          }
          
          .el-table__body-wrapper {
            overflow-y: auto;
            
            &::-webkit-scrollbar {
              width: 6px;
            }
            
            &::-webkit-scrollbar-thumb {
              border-radius: 3px;
              background: rgba(144, 147, 153, 0.3);
              
              &:hover {
                background: rgba(144, 147, 153, 0.5);
              }
            }
            
            &::-webkit-scrollbar-track {
              background: transparent;
            }
          }

          .message-content {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .reply-message {
              background: #f5f7fa;
              border-left: 4px solid #409EFF;
              padding: 4px 8px;
              font-size: 13px;
              color: #606266;
              border-radius: 0 4px 4px 0;
              margin-bottom: 4px;
              
              .reply-content {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 100%;
              }
            }

            .main-content {
              line-height: 1.5;
              word-break: break-word;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 100%;

              &.rich-content {
                white-space: pre-wrap;
                max-height: 300px;
                overflow-y: auto;
              }
            }
          }

          .el-tag {
            padding: 0 12px;
            height: 24px;
            line-height: 22px;
            border-radius: 12px;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
          }

          .action-button {
            font-size: 14px;
            color: #409EFF;
            padding: 4px 12px;
            border-radius: 6px;
            transition: all 0.3s;
            
            &:hover {
              background-color: rgba(64, 158, 255, 0.1);
            }
            
            i {
              transition: transform 0.3s ease;
            }
            
            &:hover i {
              transform: rotate(180deg);
            }
          }
        }
        
        .pagination-container {
          margin: 0;
          padding: 12px 24px;
          background: #fff;
          border-top: 1px solid #eef1f7;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          flex-shrink: 0;
          height: 56px;
          box-sizing: border-box;
          
          ::v-deep .el-pagination {
            padding: 0;
            font-weight: normal;
            white-space: nowrap;
            
            .btn-prev, 
            .btn-next,
            .el-pager li {
              margin: 0 4px;
              min-width: 32px;
              border-radius: 4px;
              border: 1px solid #e0e5ee;
              
                &:not(.disabled):hover {
            border-color: #409EFF;
          }
              
              &.active {
                background-color: #409EFF;
                border-color: #409EFF;
                color: #fff;
              }
            }
            
            .el-pagination__total,
            .el-pagination__sizes {
              margin-right: 16px;
            }
            
            .el-pagination__jump {
              margin-left: 16px;
            }
            
            .el-select .el-input {
              margin: 0 8px;
              
              .el-input__inner {
                border-radius: 4px;
                height: 28px;
                line-height: 28px;
                padding-right: 25px;
              }
            }
            
            .el-pagination__editor.el-input {
              margin: 0 8px;
              
              .el-input__inner {
                border-radius: 4px;
                height: 28px;
                line-height: 28px;
              }
            }
          }
        }
      }
    }
  }
}

.message-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 30px 24px;
    background: #f8f9fb;
    max-height: 70vh;
    overflow-y: auto;
  }

  .message-title {
    font-size: 20px;
    font-weight: 600;
    color: #1a1f36;
    margin-bottom: 20px;
    padding-left: 12px;
    border-left: 3px solid #409EFF;
    letter-spacing: 0.5px;
  }

  .message-content-container {
    background-color: #fff;
    border-radius: 12px;
    padding: 24px;
    min-height: 200px;
    max-height: calc(70vh - 200px);
    overflow-y: auto;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    position: relative;
    margin-bottom: 20px;
    border: 1px solid #eef1f7;

    &:hover {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    }

    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .message-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #909399;
    font-size: 13px;
    
    .time {
      background-color: #f0f2f5;
      padding: 4px 10px;
      border-radius: 12px;
      display: inline-block;
    }
  }
}

.edit-content-container {
  .preview-container {
    margin-top: 20px;
    border-top: 1px dashed #dcdfe6;
    padding-top: 20px;

    .preview-title {
      font-size: 16px;
      color: #606266;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .preview-content {
      background-color: #f8f9fb;
      padding: 16px;
      border-radius: 8px;
      border: 1px solid #ebeef5;
      min-height: 200px;
      max-height: calc(70vh - 400px);
      overflow-y: auto;
    }
  }
}

:deep(.rich-content) {
  line-height: 1.8;
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;

  * {
    max-width: 100%;
  }
  
  img {
    max-width: 100%;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  a {
    color: #409EFF;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  p {
    margin: 8px 0;
  }
  
  ul, ol {
    padding-left: 20px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.el-dropdown-menu {
  .danger {
    color: #F56C6C;
  }
}
</style> 