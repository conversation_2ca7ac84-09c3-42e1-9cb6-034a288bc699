<template>
    <div>
        <el-popover placement="bottom-start" width="460" trigger="click" @show="$refs['iconSelect'].reset()">
            <IconSelect ref="iconSelect" @selected="selected" />
            <el-input slot="reference" v-model="cValue" placeholder="点击选择图标" clearable>
                <SvgIcon v-if="cValue" slot="prefix" :icon-class="cValue" class="el-input__icon"
                    style="height: 32px;width: 16px;margin-left: 5px;margin-top: 5px;" />
                <i v-else slot="prefix" class="el-icon-search el-input__icon" />
            </el-input>
        </el-popover>
    </div>
</template>

<script>
import SvgIcon from './SvgIcon.vue'
import IconSelect from './IconSelect/index.vue'
import { isEqual } from 'element-ui/src/utils/util';
export default {
    components: { SvgIcon, IconSelect },
    props: {
        value: {
            type: String,
            default: ''
        }
    },
    model: {
        prop: 'value',
        event: 'value'
    },
    data() {
        return {
            cValue: ""
        }
    },
    watch: {
        value: {
            handler(val) {

                if (!isEqual(val, this.cValue)) {
                    this.cValue = val
                    this.$emit('value', val)
                }
            },
            immediate: true
        },
        cValue(val) {
            if (!isEqual(val, this.value)) {
                this.$emit('value', val)
            }
        }
    },
    methods: {
        selected(name) {
            this.cValue = name
        },
    }
}
</script>

<style lang="scss" scoped></style>