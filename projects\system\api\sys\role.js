import request from '@/utils/request'

const sysRoleApi = CONSTANT.SYSTEM + '/sys/role'

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: `${sysRoleApi}/list`,
    method: 'get',
    params
  })
}

// 获取角色信息
export function getRoleInfo(id) {
  return request({
    url: `${sysRoleApi}`,
    method: 'get',
    params: { id }
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: sysRoleApi,
    method: 'post',
    data
  })
}

// 编辑角色
export function updateRole(data) {
  return request({
    url: sysRoleApi,
    method: 'put',
    data
  })
}

// 删除角色，支持批量删除
export function deleteRole(ids) {
  return request({
    url: `${sysRoleApi}/${ids}`,
    method: 'delete'
  })
}


// 获取角色下的用户列表
export function getUsersByRoleId(query) {
  const { roleId, ...params } = query
  // 确保roleId作为路径参数传递，并确保它是一个数字
  return request({
    url: `${CONSTANT.SYSTEM}/sys/account/role/${roleId}`,
    method: 'get',
    params
  })
}

// 批量赋权用户到角色
export function batchAssignRoleUsers(data) {
  return request({
    url: `${CONSTANT.SYSTEM}/sys/account/batchAssignRole`,
    method: 'post',
    data: data
  })
}
