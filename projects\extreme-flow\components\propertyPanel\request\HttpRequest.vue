<template>
  <el-form :model="form" size="small" :rules="rules" ref="form">
    <el-form-item label="请求路径" prop="url">
      <el-input v-model="form.url"></el-input>
    </el-form-item>
    <el-form-item label="请求方法" prop="httpMethod">
      <el-select v-model="form.httpMethod" style="width: 100%;" >
        <el-option label="GET" value="GET" />
        <el-option label="POST" value="POST" />
        <el-option label="PUT" value="PUT" />
        <el-option label="DELETE" value="DELETE" />
      </el-select>
    </el-form-item>
    <el-form-item label="ContentType" prop="contentType">
      <el-select v-model="form.contentType" style="width: 100%;">
        <el-option value="application/json" label="application/json"></el-option>
        <el-option value="application/x-www-form-urlencoded" label="application/x-www-form-urlencoded"></el-option>
        <el-option value="multipart/form-data" label="multipart/form-data"></el-option>
        <el-option value="text/plain" label="text/plain"></el-option>
        <el-option value="application/xml" label="application/xml"></el-option>
        <el-option value="text/xml" label="text/xml"></el-option>
        <el-option value="image/svg+xml" label="image/svg+xml"></el-option>
      </el-select>
    </el-form-item>
    <div style="text-align: center;">
      <el-link type="primary" :underline="false" @click="() => higherOption = !higherOption">高级选项<i :class="higherOption ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" /></el-link>
    </div>
    <div v-show="higherOption">
      <el-form-item label="响应超时" prop="replyTimeout">
        <el-input v-model="form.replyTimeout" type="number">
          <template slot="suffix">ms</template>
        </el-input>
      </el-form-item>
    </div>
  </el-form>
</template>
<script>
import { isEqual } from 'element-ui/src/utils/util';
export default {
  name: 'HttpRequest',
  data() {
    return {
      form: {
        contentType: 'application/json'
      },
      rules: {
        'url': [
          { required: true, message: '请输入请求路径', trigger: 'blur' }
        ],
        'httpMethod': [
          { required: true, message: '请选择请求方式', trigger: 'change' }
        ]
      },
      higherOption: false
    }
  },
  methods: {
    getProperties() {
      return this.form
    },
    async validate() {
        return new Promise(resolve => {
            this.$refs.form.validate(valid => {
                resolve(valid)
            })
        })
    }
  },
  props: {
    properties: {
        type: Object,
        default: () => ({})
    },
    node: {
        type: Object,
        default: () => ({})
    }
  },
  watch: {
    properties: {
        handler(newVal) {
            if (!isEqual(newVal, this.form)) {
                this.form = { ...this.form, ...newVal }
            }
        },
        immediate: true,
        deep: true
    },
  },
}
</script>