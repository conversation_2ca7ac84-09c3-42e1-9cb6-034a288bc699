<template>
  <div id="js-editor" :style="{ 'height': `${height}px` }"></div>
</template>

<script>
import * as monaco from 'monaco-editor'
const LANGUAGE = 'javascript'

export default {
  name: 'JsEditor',
  props: {
    height: {
      type: Number,
      default: 550
    },
    data: {
      type: String,
      default: ''
    }
  },
  model: {
    prop: 'data',
    event: 'change'
  },
  data() {
    return {
      editor: null,
    }
  },
  beforeDestroy() {
    // 销毁 Monaco 编辑器
    if (this.editor) {
      this.editor.dispose()
    }
  },
  mounted() { 
    this.$nextTick(() => {
        const container = document.getElementById('js-editor')
        if (!container) return

        // 销毁已存在的编辑器
        if (this.editor) {
          this.editor.dispose()
        }

        // 创建新的编辑器实例
        this.editor = monaco.editor.create(container, {
          value: this.data || '',
          language: LANGUAGE,
          theme: 'vs', // 使用白色主题
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 13,
          tabSize: 2,
          wordWrap: 'on',
          folding: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3
        })

        // 监听内容变化
        this.editor.onDidChangeModelContent((val) => {
          this.$emit('change', this.editor.getValue())
        })
      })
  },
}
</script>