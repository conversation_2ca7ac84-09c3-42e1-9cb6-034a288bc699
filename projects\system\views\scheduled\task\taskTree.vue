<template>
  <el-card shadow="always">
    <div class="label" slot="header">
      <label>任务列表</label>
      <div>
        <el-tooltip class="item" effect="dark" content="重置任务" placement="top-start" style="margin-right: 10px;">
          <el-button :loading="loading" type="text" @click="reloadTask" icon="el-icon-refresh-left" ></el-button>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="刷新列表" placement="top-start" style="margin-right: 10px;">
          <el-button :loading="loading" @click="refreshTree" type="text" icon="el-icon-refresh" ></el-button>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="新增任务" placement="top-start">
          <el-button :loading="loading" @click="task = {status: 'RUNNING', concurrent: true}" type="text" icon="el-icon-plus"></el-button>
        </el-tooltip>
      </div>
    </div>
    <el-tree lazy :load="loadNode" :props="props" @current-change="handleCurrentChange" highlight-current ref="taskTree" node-key="id">
      <template slot-scope="{ node, data }">
        <div>
          <div v-if="1 != node.level" style="display: flex;justify-content: space-around;">
            <div :class="'job_' + data.status" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width: 280px">{{ data.name }}</div>
            <div style="">
              <el-link class="el-icon-edit" :underline="false" style="margin-right: 10px;" @click="task = JSON.parse(JSON.stringify(data))" />
              <el-link class="el-icon-delete" :underline="false" @click="handleDelete(data)" />
            </div>
          </div>
          <div v-else style="display: flex;justify-content: space-between;">
            <div style="padding: 0;">
              <i class="el-icon-folder" /> {{ `${data.name}  (${data.count})` }}
            </div>
            <i class="el-icon-plus" @click.prevent="task = {status: 'RUNNING', concurrent: true, groupName: data.name}" />
          </div>
        </div>
      </template>
    </el-tree>
    <task-dialog v-model="task" :groups="groups" :types="types" :statuses="statuses" @after-submit="afterSubmit" />
  </el-card>

</template>
<script>
import api from "@system/api/scheduled";
import taskDialog from './taskDialog'
export default {
  name: 'taskTree',
  components: {
    taskDialog
  },
  mounted() {
  },
  data() {
    return {
      props: {
        label: 'name',
        children: 'children',
        isLeaf: 'leaf'
      },
      task: undefined,
      groups: [],
      loading: false
    }
  },
  props: {
    selected: {
      type: Object,
      default: undefined
    },
    types: {
      type: Array,
      default: () => {
        return []
      }
    },
    statuses: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  methods: {
    loadNode(node, resolve) {
      if (node.level === 0) {
        api.task.count({ groups: 'groupName' }).then(res => res.filter(r => r.groupName).map(i => {
          return { name: i.groupName, count: i.count }
        })).then(res => {
          this.groups = res
          resolve(res)
        })
      } else if (node.level === 1) {
        api.task.list({ groupName: node.label, size: -1 }).then(res => resolve(res.records.map(r => { return { ...r, leaf: true } })))
      }
    },
    handleCurrentChange(data, node) {
      if (node.level === 2) {
        this.$emit('update:selected', data)
      }
    },
    refreshTree() {
      var taskTree = this.$refs.taskTree
      var activeNodes = taskTree.root.childNodes.filter(node => node.loaded).map(node=>node.data.name)
      var nodekey = taskTree.getCurrentKey()
      var data = taskTree.getCurrentNode()
      taskTree.root.loaded=false
      taskTree.root.expand(() => {
        var childNodes = taskTree.root.childNodes.filter(node => activeNodes.includes(node.data.name))
        childNodes.forEach(node => {
          node.loaded = false
          if (data.groupName === node.data.name) {
            node.expand(() => {
              taskTree.setCurrentKey(nodekey)
              var n = node.childNodes.find(child => child.data.id = data.id)
              if (n) {
                this.handleCurrentChange(n.data, n)
              }
            })
          } else {
            node.expand()
          }
        })
      })
    },
    reloadTask() {
      this.$confirm(`此操作会停止全部任务，然后重新加载，是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        api.task.reload().then(() => { 
          this.$message({
            message: '任务已重新加载',
            type: 'success'
          })
          this.refreshTree()
          this.loading = false
        }).catch(e => {
          this.loading = false
        })
      })
    },
    handleDelete(data) {
      this.$confirm(`此操作将永久删除 ${data.name}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.task.remove(data.id).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          var taskTree = this.$refs.taskTree
          taskTree.remove(data)
          var childNode = taskTree.root.childNodes.find(node => node.data.name === data.groupName)
          if (childNode) {
            childNode.data.count = childNode.data.count > 0 ? childNode.data.count - 1 : 0
          }
          this.$emit('update:selected', {})
        })
      })
    },
    afterSubmit(task) {
      var taskTree = this.$refs.taskTree
      task.leaf = true
      let node = taskTree.getNode(task)
      var parentData
      if (node) {
        // 更新
        if (node.data.groupName === task.groupName) {
          node.data = task
        } else {
          node.parent.data.count --
          taskTree.remove(node)
          this.afterSubmit(task)
          return 
        }
      } else {
        // 新增
        var parentNode = taskTree.root.childNodes.find(node => node.data.name === task.groupName)
        if (parentNode) {
          parentData = parentNode.data
          taskTree.append(task, parentNode)
        } else {
          parentData = {name: task.groupName, count: 0}
          taskTree.append(parentData, taskTree.root)
          taskTree.append(task, parentData)
        }
        parentData.count ++
      }
      this.$emit('update:selected', task)
    }
  }
}
</script>

<style scoped>
.label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.label label {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-tree) {
  background-color: transparent;
  margin: 8px 0;
  padding-left: 0;
}

:deep(.el-tree-node) {
  padding-left: 0 !important;
  position: relative;
}

:deep(.el-tree-node__content) {
  height: 40px;
  border-radius: 4px;
  margin: 4px 0;
  transition: all 0.3s;
  padding-left: 0 !important;
  position: relative;
}

:deep(.el-tree-node__children) {
  padding-left: 0 !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: transparent;
  position: relative;
  z-index: 2;
}

:deep(.el-tree-node.is-current > .el-tree-node__content div[style*="display: flex"]) {
  background-color: #ecf5ff;
  border: 1px solid #409EFF;
  position: relative;
  z-index: 2;
}

:deep(.el-tree-node__content .el-icon-folder) {
  color: #409EFF;
  margin-right: 8px;
}

:deep(.el-tree-node__expand-icon) {
  padding: 0;
  margin-right: 8px;
}

:deep(.el-tree-node__content) div:not([style*="display: flex"]) {
  padding-left: 8px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

:deep(.is-leaf) {
  display: none;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05) !important;
}

.el-link {
  margin-left: 8px;
  transition: all 0.3s;
  position: relative;
  z-index: 3;
}

.el-link:hover {
  transform: scale(1.1);
}

.el-icon-edit {
  color: #409EFF;
}

.el-icon-delete {
  color: #F56C6C;
}

:deep(.el-tree-node__content) > div {
  width: 100%;
  position: relative;
}

:deep(.el-tree-node__content) div[style*="display: flex"] {
  background-color: #fff;
  padding: 8px 15px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
}

:deep(.el-tree-node__content) div[style*="display: flex"]:hover {
  border-color: #c6e2ff;
  background-color: #f5f7fa;
  z-index: 2;
}

:deep(.el-tree-node__expand-icon.expanded) {
  z-index: 3;
  position: relative;
}

.job_COMPLETE {
  color: #67C23A;
}
.job_FAILED {
  color: #F56C6C;
}
.job_STOPPED {
  color: #909399;
}
</style>