


module.exports = {
    "compilerOptions": {
        "target": "esnext",
        "module": "esnext",
        "moduleResolution": "node",
        "esModuleInterop": true,
        "noEmit": true, // 只进行类型检查，不输出编译文件
        "skipLibCheck": true, // 跳过库文件的类型检查
        "noImplicitAny": false, // 允许隐式的 `any` 类型
        "noImplicitThis": false, // 允许隐式的 `this` 类型
        "strict": false, // 关闭所有严格类型检查
        "strictNullChecks": false,
        "strictFunctionTypes": false,
        "strictPropertyInitialization": false,
        "noImplicitReturns": false,
        "forceConsistentCasingInFileNames": true,
        "jsx": "preserve",
        "allowJs": true,
        "sourceMap": false,
        "resolveJsonModule": true,
        "baseUrl": ".",
        "paths": {},
    },
    "include": [
    ],
    "exclude": [
        "node_modules"
    ]
}