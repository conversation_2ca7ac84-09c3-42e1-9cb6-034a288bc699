<template>
  <div class="batch-role-auth">
    <el-dialog
      :visible.sync="visible"
      width="800px"
      :close-on-click-modal="false"
      @closed="handleDialogClosed"
    >
      <template slot="title">
        <div class="dialog-title">
          <span>批量用户赋权</span>
          <el-tag v-if="role.name" type="primary" size="medium" effect="plain" class="role-tag">{{ role.name }}</el-tag>
          <el-tag v-if="role.code" type="info" size="small" effect="plain" class="code-tag">{{ role.code }}</el-tag>
        </div>
      </template>
      
      <div class="dialog-content">
        <!-- 自定义双列表代替穿梭框 -->
        <div class="custom-transfer">
          <!-- 左侧：未授权用户 -->
          <div class="list-panel unassigned-panel">
            <div class="panel-header">
              <div class="title">
                <el-checkbox
                  v-model="leftCheckedAll"
                  @change="handleLeftCheckAll"
                  :indeterminate="isLeftIndeterminate"
                >未授权用户</el-checkbox>
              </div>
              <div class="count">{{ filteredUnassignedUsers.length }}/{{ unassignedUsers.length }}</div>
            </div>
            
            <div class="panel-filter">
              <el-input
                v-model="leftFilter"
                placeholder="请输入关键字搜索"
                prefix-icon="el-icon-search"
                clearable
              />
            </div>
            
            <div class="panel-body">
              <el-checkbox-group v-model="leftCheckedUsers" @change="handleLeftCheckedChange">
                <div
                  v-for="user in filteredUnassignedUsers"
                  :key="'left-' + user.id"
                  class="user-item"
                >
                  <el-checkbox :label="user.id">
                    <div class="user-info">
                      <div class="username" :title="user.username">
                        <i class="el-icon-user"></i>
                        {{ user.username }}
                      </div>
                      <div class="nickname" v-if="user.nickName" :title="user.nickName">{{ user.nickName }}</div>
                      <div class="phone" v-if="user.phone" :title="user.phone">{{ user.phone }}</div>
                    </div>
                  </el-checkbox>
                </div>
                <div v-if="filteredUnassignedUsers.length === 0" class="empty-tip">
                  <i class="el-icon-document"></i>
                  无数据
                </div>
              </el-checkbox-group>
            </div>
            
            <div class="panel-footer">
              <span class="count">共 {{ filteredUnassignedUsers.length }} 项</span>
            </div>
          </div>
          
          <!-- 中间：操作按钮 -->
          <div class="transfer-controls">
            <el-button
              type="primary"
              circle
              icon="el-icon-arrow-right"
              :disabled="leftCheckedUsers.length === 0"
              @click="moveToRight"
            ></el-button>
            <el-button
              type="primary"
              circle
              icon="el-icon-arrow-left"
              :disabled="rightCheckedUsers.length === 0"
              @click="moveToLeft"
            ></el-button>
          </div>
          
          <!-- 右侧：已授权用户 -->
          <div class="list-panel assigned-panel">
            <div class="panel-header">
              <div class="title">
                <el-checkbox
                  v-model="rightCheckedAll"
                  @change="handleRightCheckAll"
                  :indeterminate="isRightIndeterminate"
                >已授权用户</el-checkbox>
              </div>
              <div class="count">{{ filteredAssignedUsers.length }}/{{ assignedUsers.length }}</div>
            </div>
            
            <div class="panel-filter">
              <el-input
                v-model="rightFilter"
                placeholder="请输入关键字搜索"
                prefix-icon="el-icon-search"
                clearable
              />
            </div>
            
            <div class="panel-body">
              <el-checkbox-group v-model="rightCheckedUsers" @change="handleRightCheckedChange">
                <div
                  v-for="user in filteredAssignedUsers"
                  :key="'right-' + user.id"
                  class="user-item"
                >
                  <el-checkbox :label="user.id">
                    <div class="user-info">
                      <div class="username" :title="user.username">
                        <i class="el-icon-user"></i>
                        {{ user.username }}
                      </div>
                      <div class="nickname" v-if="user.nickName" :title="user.nickName">{{ user.nickName }}</div>
                      <div class="phone" v-if="user.phone" :title="user.phone">{{ user.phone }}</div>
                    </div>
                  </el-checkbox>
                </div>
                <div v-if="filteredAssignedUsers.length === 0" class="empty-tip">
                  <i class="el-icon-document"></i>
                  无数据
                </div>
              </el-checkbox-group>
            </div>
            
            <div class="panel-footer">
              <span class="count">共 {{ filteredAssignedUsers.length }} 项</span>
            </div>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确认授权</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAccountList } from '@system/api/sys/account'
import { getUsersByRoleId, batchAssignRoleUsers } from '@system/api/sys/role'

export default {
  name: 'BatchRoleAuth',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    role: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      allUsers: [],
      // 双列表相关数据
      unassignedUsers: [], // 未授权用户
      assignedUsers: [], // 已授权用户
      leftCheckedUsers: [], // 左侧选中的用户
      rightCheckedUsers: [], // 右侧选中的用户
      leftCheckedAll: false, // 左侧全选
      rightCheckedAll: false, // 右侧全选
      leftFilter: '', // 左侧过滤
      rightFilter: '', // 右侧过滤
      
      originalAssignedUserIds: [], // 用于记录原始已授权的用户
    }
  },
  computed: {
    // 左侧部分选中状态
    isLeftIndeterminate() {
      return this.leftCheckedUsers.length > 0 && 
             this.leftCheckedUsers.length < this.filteredUnassignedUsers.length;
    },
    
    // 右侧部分选中状态
    isRightIndeterminate() {
      return this.rightCheckedUsers.length > 0 && 
             this.rightCheckedUsers.length < this.filteredAssignedUsers.length;
    },
    
    // 过滤后的未授权用户
    filteredUnassignedUsers() {
      if (!this.leftFilter) return this.unassignedUsers;
      
      const filter = this.leftFilter.toLowerCase();
      return this.unassignedUsers.filter(user => 
        (user.username && user.username.toLowerCase().includes(filter)) ||
        (user.nickName && user.nickName.toLowerCase().includes(filter)) ||
        (user.phone && user.phone.includes(filter))
      );
    },
    
    // 过滤后的已授权用户
    filteredAssignedUsers() {
      if (!this.rightFilter) return this.assignedUsers;
      
      const filter = this.rightFilter.toLowerCase();
      return this.assignedUsers.filter(user => 
        (user.username && user.username.toLowerCase().includes(filter)) ||
        (user.nickName && user.nickName.toLowerCase().includes(filter)) ||
        (user.phone && user.phone.includes(filter))
      );
    },
    
    // 当前选中的所有用户ID（用于提交）
    selectedUserIds() {
      return this.assignedUsers.map(user => user.id);
    }
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val && this.role && (this.role.id !== undefined && this.role.id !== null)) {
        console.log('对话框打开，初始化数据，角色ID:', this.role.id);
        this.fetchData();
      }
    },
    visible(val) {
      this.$emit('input', val);
    }
  },
  methods: {
    // 获取所有用户和角色用户数据
    async fetchData() {
      if (!this.role || (this.role.id === undefined || this.role.id === null)) {
        console.warn('角色对象无效或ID为空:', this.role);
        return;
      }
      
      this.loading = true;
      try {
        console.log('获取角色数据，角色ID:', this.role.id, '角色名称:', this.role.name);
        
        // 获取所有用户
        const allUsersRes = await getAccountList({
          size: 1000,
          valid: true // 只获取有效的用户
        });
        
        const allUsersList = (allUsersRes.records || []).map(user => ({
          id: user.id,
          username: user.username,
          nickName: user.nickName || user.name || '',
          phone: user.phone || '',
          email: user.email || ''
        }));
        
        console.log('所有用户数量:', allUsersList.length);
        
        // 获取角色已授权用户
        const roleUsersRes = await getUsersByRoleId({
          roleId: String(this.role.id),
          size: -1
        });
        
        const roleUserIds = (roleUsersRes.records || []).map(user => user.id);
        console.log('已授权用户数量:', roleUserIds.length);
        
        // 设置已授权和未授权用户
        this.assignedUsers = allUsersList.filter(user => roleUserIds.includes(user.id));
        this.unassignedUsers = allUsersList.filter(user => !roleUserIds.includes(user.id));
        this.originalAssignedUserIds = [...roleUserIds];
        
        // 清空选中状态
        this.leftCheckedUsers = [];
        this.rightCheckedUsers = [];
        this.leftCheckedAll = false;
        this.rightCheckedAll = false;
      } catch (error) {
        console.error('获取用户数据失败', error);
        this.$message.error('获取用户数据失败: ' + (error.message || '未知错误'));
      } finally {
        this.loading = false;
      }
    },
    
    // 处理左侧全选
    handleLeftCheckAll(val) {
      if (val) {
        this.leftCheckedUsers = this.filteredUnassignedUsers.map(user => user.id);
      } else {
        this.leftCheckedUsers = [];
      }
    },
    
    // 处理右侧全选
    handleRightCheckAll(val) {
      if (val) {
        this.rightCheckedUsers = this.filteredAssignedUsers.map(user => user.id);
      } else {
        this.rightCheckedUsers = [];
      }
    },
    
    // 处理左侧选中变化
    handleLeftCheckedChange(value) {
      const checkedCount = value.length;
      this.leftCheckedAll = checkedCount === this.filteredUnassignedUsers.length && this.filteredUnassignedUsers.length > 0;
    },
    
    // 处理右侧选中变化
    handleRightCheckedChange(value) {
      const checkedCount = value.length;
      this.rightCheckedAll = checkedCount === this.filteredAssignedUsers.length && this.filteredAssignedUsers.length > 0;
    },
    
    // 移动到右侧
    moveToRight() {
      if (this.leftCheckedUsers.length === 0) return;
      
      // 找到要移动的用户
      const usersToMove = this.unassignedUsers.filter(user => 
        this.leftCheckedUsers.includes(user.id)
      );
      
      // 更新两个列表
      this.assignedUsers = [...this.assignedUsers, ...usersToMove];
      this.unassignedUsers = this.unassignedUsers.filter(user => 
        !this.leftCheckedUsers.includes(user.id)
      );
      
      // 清空选中
      this.leftCheckedUsers = [];
      this.leftCheckedAll = false;
    },
    
    // 移动到左侧
    moveToLeft() {
      if (this.rightCheckedUsers.length === 0) return;
      
      // 找到要移动的用户
      const usersToMove = this.assignedUsers.filter(user => 
        this.rightCheckedUsers.includes(user.id)
      );
      
      // 更新两个列表
      this.unassignedUsers = [...this.unassignedUsers, ...usersToMove];
      this.assignedUsers = this.assignedUsers.filter(user => 
        !this.rightCheckedUsers.includes(user.id)
      );
      
      // 清空选中
      this.rightCheckedUsers = [];
      this.rightCheckedAll = false;
    },
    
    // 处理提交
    async handleSubmit() {
      if (!this.role || (this.role.id === undefined || this.role.id === null)) {
        this.$message.error('未选择角色或角色ID无效');
        return;
      }
      
      // 获取当前选中的用户ID
      const currentUserIds = this.selectedUserIds;
      
      // 比较是否有变化
      const added = currentUserIds.filter(id => !this.originalAssignedUserIds.includes(id));
      const removed = this.originalAssignedUserIds.filter(id => !currentUserIds.includes(id));
      
      if (added.length === 0 && removed.length === 0) {
        this.$message.info('未进行任何更改');
        this.visible = false;
        return;
      }
      
      this.loading = true;
      try {
        console.log('提交授权，角色ID:', this.role.id, '选中用户数:', currentUserIds.length);
        
        await batchAssignRoleUsers({
          roleId: String(this.role.id), // 确保roleId作为字符串提交
          accountIds: currentUserIds.map(id => String(id)) // 确保所有ID都作为字符串提交
        });
        
        this.$message.success('批量授权成功');
        this.visible = false;
        this.$emit('success');
      } catch (error) {
        console.error('批量授权失败', error);
        this.$message.error('批量授权失败：' + (error.message || '未知错误'));
      } finally {
        this.loading = false;
      }
    },
    
    // 处理对话框关闭
    handleDialogClosed() {
      this.leftFilter = '';
      this.rightFilter = '';
      this.unassignedUsers = [];
      this.assignedUsers = [];
      this.leftCheckedUsers = [];
      this.rightCheckedUsers = [];
      this.leftCheckedAll = false;
      this.rightCheckedAll = false;
      this.originalAssignedUserIds = [];
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-role-auth {
  .dialog-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    
    .role-tag {
      margin-left: 12px;
      font-weight: normal;
      font-size: 14px;
      height: 26px;
      line-height: 24px;
      padding: 0 10px;
      border-radius: 4px;
      background-color: #ecf5ff;
      border-color: #d9ecff;
    }
    
    .code-tag {
      margin-left: 8px;
      font-weight: normal;
      font-family: 'Courier New', monospace;
      letter-spacing: 0.5px;
      font-size: 12px;
      height: 22px;
      line-height: 20px;
      background-color: #f4f4f5;
      border-color: #e9e9eb;
    }
  }
  
  .dialog-content {
    .custom-transfer {
      display: flex;
      justify-content: space-between;
      align-items: stretch;
      height: 500px;
      margin-bottom: 10px;
      
      .list-panel {
        width: 46%;
        min-width: 0;
        flex-shrink: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
        border: 1px solid #e6ebf5;
        overflow: hidden;
        transition: all 0.3s;
        
        &:hover {
          box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
        }
        
        &.unassigned-panel {
          background: #f9fafc;
          
          .panel-header {
            background: linear-gradient(to right, #f5f7fa, #ebeef5);
            color: #909399;
            border-bottom: 1px solid #e6ebf5;
          }
        }
        
        &.assigned-panel {
          background: #f0f9eb;
          border: 1px solid #e1f3d8;
          
          .panel-header {
            background: linear-gradient(to right, #f0f9eb, #e1f3d8);
            color: #67c23a;
            border-bottom: 1px solid #e1f3d8;
          }
        }
        
        .panel-header {
          padding: 12px 15px;
          font-size: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 44px;
          
          .title {
            font-weight: 600;
            
            .el-checkbox {
              ::v-deep .el-checkbox__label {
                font-weight: 600;
              }
            }
          }
          
          .count {
            font-size: 13px;
            padding: 2px 8px;
            border-radius: 12px;
            background-color: rgba(0, 0, 0, 0.06);
          }
        }
        
        .panel-filter {
          padding: 12px 15px;
          border-bottom: 1px solid #ebeef5;
          
          .el-input {
            ::v-deep .el-input__inner {
              border-radius: 4px;
              height: 36px;
              transition: all 0.3s;
              
              &:focus {
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
              }
            }
            
            ::v-deep .el-input__suffix {
              right: 8px;
            }
          }
        }
        
        .panel-body {
          flex: 1;
          overflow: auto;
          padding: 0;
          background-color: #fff;
          
          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          
          &::-webkit-scrollbar-thumb {
            background: #c0c4cc;
            border-radius: 3px;
          }
          
          &::-webkit-scrollbar-track {
            background: #f0f2f5;
          }
          
          .el-checkbox-group {
            display: flex;
            flex-direction: column;
            width: 100%;
            
            .user-item {
              border-bottom: 1px solid #f0f0f0;
              transition: all 0.2s;
              
              &:last-child {
                border-bottom: none;
              }
              
              &:hover {
                background-color: #f5f7fa;
              }
              
              .el-checkbox {
                display: flex;
                align-items: center;
                padding: 10px 15px;
                margin: 0;
                transition: all 0.2s;
                width: 100%;
                
                &:hover {
                  background-color: rgba(64, 158, 255, 0.1);
                }
                
                &.is-checked {
                  background-color: rgba(103, 194, 58, 0.1);
                }
                
                ::v-deep .el-checkbox__input {
                  margin-right: 10px;
                  
                  .el-checkbox__inner {
                    transition: all 0.3s;
                    
                    &:hover {
                      border-color: #409EFF;
                    }
                  }
                  
                  &.is-checked {
                    .el-checkbox__inner {
                      background-color: #409EFF;
                      border-color: #409EFF;
                    }
                  }
                }
                
                ::v-deep .el-checkbox__label {
                  flex: 1;
                  padding-left: 0;
                }
              }
            }
            
            .empty-tip {
              padding: 40px 0;
              text-align: center;
              color: #909399;
              font-size: 14px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              
              i {
                font-size: 48px;
                margin-bottom: 16px;
                color: #dcdfe6;
              }
            }
          }
        }
        
        .panel-footer {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding: 8px 15px;
          background-color: #f5f7fa;
          border-top: 1px solid #ebeef5;
          
          .count {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .transfer-controls {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 15px;
        
        .el-button {
          margin-bottom: 15px;
          width: 40px;
          height: 40px;
          transition: all 0.3s;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &:hover:not(:disabled) {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
          
          &:disabled {
            opacity: 0.6;
          }
          
          i {
            font-size: 16px;
          }
        }
      }
    }
    
    .user-info {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      width: 100%;
      min-width: 0;
      overflow: hidden;
      
      .username {
        font-weight: 600;
        color: #303133;
        margin-right: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100px;
        display: flex;
        align-items: center;
        
        i {
          color: #409EFF;
          font-size: 14px;
          margin-right: 6px;
          flex-shrink: 0;
        }
      }
      
      .nickname {
        color: #606266;
        margin-right: 8px;
        background-color: #f0f2f5;
        padding: 0 6px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 70px;
        height: 20px;
        line-height: 20px;
      }
      
      .phone {
        color: #909399;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100px;
      }
    }
  }
  
  .dialog-footer {
    .el-button {
      min-width: 80px;
      padding: 10px 20px;
      
      &.el-button--primary {
        background-color: #409EFF;
        border-color: #409EFF;
        
        &:hover {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}
</style> 