<template>
  <div>
    <auto-page :options="options" :form-rule="options.formRule" ref="rules" @formChange="formChange"
      @updateRow="updateRow" @addRow="addRow" @subFormDrawer="subFormDrawer">
      <!--      <template #tableaccountType="scope">-->
      <!--        <span v-if="scope.row.accountType==0">系统用户</span>-->
      <!--        <span v-else-if="scope.row.accountType==1">普通用户</span>-->
      <!--        <span v-else-if="scope.row.accountType==2">大屏管理员</span>-->
      <!--        <span v-else-if="scope.row.accountType==3">机构大屏管理员</span>-->
      <!--      </template>-->
      <template #tablehost="rows">
        <span v-if="rows.row.host">{{ rows.row.host }}</span>
        <span v-else>--</span>
      </template>
    </auto-page>
  </div>
</template>

<script>
import options from './options'
import autoPage from "@/components/auto-page/AutoPage";
import { list } from "@system/api/sys/position"
import { list as deptList, treeList as deptTreeList } from "@system/api/sys/dept"
import { treeList as postTreeList } from "@system/api/sys/post";
export default {
  components: { autoPage },
  data() {
    return {
      options: null
    }
  },
  created() {
    this.roleList();
    this.assemblyPostTree();
    this.options = options
  },
  methods: {
    addRow() {
      setTimeout(() => {
        let api = this.$refs.rules.fApi
        api.hidden(true, ['deptIds'])
      }, 100)
    },
    subFormDrawer(a, resolve) {
      if (a.newData.password == null) {
        a.newData.password = "123456"
      }
      else if (a.newData.password == "******") {
        delete a.newData.password
      }
      resolve()
    },
    updateRow() {
      this.$refs.rules.formRuleDrawerNes[1].value = "******"
    },
    formChange(field, value, rule, api, setFlag) {
      if (api.form?.scope == 'CUSTOM') {
        api.hidden(false, ['deptIds'])
      } else {
        api.hidden(true, ['deptIds'])
      }
    },
    roleList() {
      list({ size: -1, valid: true }).then(res => {
        let array = []
        res.records.forEach((item) => {
          array.push({ value: item.id, label: item.name })
        })
        this.options.formRule = this.options.formRule.map(is => {
          if (is.field === "positionIds") {
            is.options = array
          }
          return is
        })
      })
    },
    async assemblyPostTree() {
      let postArray = await this.getPostTreeList();
      let deptArray = await this.getDeptTreeList();
      this.recursion(postArray)
      this.recursion(deptArray)
      this.options.formRule = this.options.formRule.map(is => {
        if (is.field === "postId") {
          is.options = postArray
        }
        else if (is.field === "deptId") {
          is.options = deptArray
        }
        return is
      })
    },
    async getPostTreeList() {
      return new Promise(((resolve, reject) => {
        postTreeList().then(res => {
          resolve(res)
        })
      }))
    },
    async getDeptTreeList() {
      return new Promise(((resolve, reject) => {
        deptTreeList().then(res => {
          resolve(res)
        })
      }))
    },
    recursion(array) {
      for (let i = 0; i < array.length; i++) {
        array[i].label = array[i].name;
        delete array[i].name;
        if (array[i].children.length > 0) {
          this.recursion(array[i].children)
        }
      }
    }
  }
}
</script>
