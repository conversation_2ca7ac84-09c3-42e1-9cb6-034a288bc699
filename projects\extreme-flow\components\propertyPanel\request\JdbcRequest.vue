<template>
  <el-form :model="form" size="small" :rules="rules" ref="form" style="height: 100%; display: flex; flex-direction: column; justify-content: space-between;">
    <div v-show="!isSqlEdit">
      <el-form-item label="数据源" prop="connector">
        <el-select v-model="form.connector" style="width: 100%">
          <el-option v-for="item in connectorList" :key="item.id" :label="item.name" :value="item.id">
            <div style="display: flex; justify-content: space-between;">
              <div>{{ item.name }}</div>
              <div v-if="item.host">{{ `${item.host}:${item.port}` }}</div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <div v-if="form.dataSource">
        <el-form-item label="数据库类型" prop="dataSource.driverClassName" :rules="{ required: true, message: '请选择数据库类型', trigger: 'change' }">
          <el-select v-model="form.dataSource.driverClassName" placeholder="请选择数据库类型" style="width: 100%">
            <el-option v-for="item in dbTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="url" prop="dataSource.url" :rules="{ required: true, message: '请输入url', trigger: 'blur' }">
          <el-input v-model="form.dataSource.url" placeholder="请输入数据库连接"></el-input>
        </el-form-item>
        <el-form-item label="用户名" prop="dataSource.username" :rules="{ required: true, message: '请输入用户名', trigger: 'blur' }">
          <el-input v-model="form.dataSource.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="dataSource.password" :rules="{ required: true, message: '请输入密码', trigger: 'blur' }">
          <el-input v-model="form.dataSource.password" placeholder="请输入密码" type="password"></el-input>
        </el-form-item>
      </div>

      <el-form-item label="最大行数" prop="maxRows">
        <el-input v-model="form.maxRows" type="number"></el-input>
      </el-form-item>
      <div>
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <label class="el-form-item__label">参数设置</label>
          <el-button @click="$refs.dynamicData.addKey()" size="small" type="primary" plain>
            <i class="el-icon-plus"></i>
          </el-button>
        </div>
        <el-row>
          <DynamicData v-model="form.parameterExpressions" ref="dynamicData" style="width: 350px;"/>
        </el-row>
      </div>
    </div>

    <div v-show="isSqlEdit" style="height: 100%;">
      <div id="sqlEditor" style="height: 100%;"></div>
    </div>

    <el-form-item>
      <el-link :underline="false" type="primary" style="float: right;" @click="isSqlEdit = !isSqlEdit">
        {{ isSqlEdit ? '基础信息' : 'SQL' }}编辑
      </el-link>
    </el-form-item>
  </el-form>
</template>

<script>
import { getIntegrationConnectorList } from '@extreme-flow/api/integration'
import { isEqual } from 'element-ui/src/utils/util'
import * as monaco from 'monaco-editor'
import DynamicData from '@system/components/DynamicData'

// 定义静态的 SQL 关键字和建议
const SQL_KEYWORDS = [
  'SELECT', 'FROM', 'WHERE', 'INSERT', 'INTO', 'VALUES', 'UPDATE', 'DELETE',
  'CREATE', 'TABLE', 'ALTER', 'DROP', 'INDEX', 'VIEW', 'JOIN', 'LEFT', 'RIGHT',
  'INNER', 'OUTER', 'ON', 'GROUP BY', 'ORDER BY', 'HAVING', 'LIMIT', 'OFFSET',
  'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS', 'NULL', 'DISTINCT', 'AS'
];

const SQL_FUNCTIONS = [
  'COUNT()', 'SUM()', 'AVG()', 'MIN()', 'MAX()',
  'UPPER()', 'LOWER()', 'SUBSTR()', 'NOW()', 'DATE()'
];

// 注册 SQL 补全提供者
monaco.languages.registerCompletionItemProvider('sql', {
  provideCompletionItems: function(model, position, context) {
    // 获取当前光标前的单词
    const word = model.getWordUntilPosition(position);
    const range = {
      startLineNumber: position.lineNumber,
      startColumn: word.startColumn,
      endLineNumber: position.lineNumber,
      endColumn: word.endColumn
    };

    let suggestions = [];

    // 添加关键字建议
    suggestions = suggestions.concat(SQL_KEYWORDS.map(keyword => {
      return {
        label: keyword,
        kind: monaco.languages.CompletionItemKind.Keyword,
        documentation: `SQL 关键字: ${keyword}`,
        insertText: keyword, // 直接插入关键字本身
        range: range
      };
    }));

    // 添加函数建议
    suggestions = suggestions.concat(SQL_FUNCTIONS.map(func => {
      return {
        label: func,
        kind: monaco.languages.CompletionItemKind.Function,
        documentation: `SQL 函数: ${func}`,
        insertText: func, // 插入带括号的函数名
        range: range
      };
    }));

    return { suggestions: suggestions };
  }
});

export default {
  components: {
    DynamicData
  },
  data() {
    return {
      form: {
        connector: 'custom',
        maxRows: 1000,
        parameterExpressions: {},
        dataSource: {
          driverClassName: '',
          url: '',
          username: '',
          password: ''
        }
      },
      rules: {
        connector: [
          { required: true, message: '请选择连接器', trigger: 'change' }
        ]
      },
      connectorList: [],
      dbTypeList: [
        { label: 'MySQL', value: 'com.mysql.cj.jdbc.Driver' },
        { label: 'Oracle', value: 'oracle.jdbc.OracleDriver' },
        { label: 'SQL Server', value: 'com.microsoft.sqlserver.jdbc.SQLServerDriver' },
        { label: 'PostgreSQL', value: 'org.postgresql.Driver' },
        { label: 'SQLite', value: 'org.sqlite.JDBC' },
        { label: 'DB2', value: 'com.ibm.db2.jcc.DB2Driver' },
        { label: 'H2', value: 'org.h2.Driver' }
      ],
      isSqlEdit: false,
      editor: null
    }
  },

  mounted() {
    this.getConnectors()
    this.initEditor()
  },

  methods: {
    initEditor() {
      this.$nextTick(() => {
        const container = document.getElementById('sqlEditor')
        if (!container) return

        if (this.editor) {
          this.editor.dispose()
        }

        this.editor = monaco.editor.create(container, {
          value: this.sql,
          language: 'sql',
          theme: 'vs',
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 13,
          tabSize: 2,
          wordWrap: 'on',
          folding: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3
        })

        this.editor.onDidChangeModelContent(() => {
          this.sqlChange(this.editor.getValue())
        })
      })
    },

    sqlChange(sql) {
      if (sql.toLowerCase().includes('select')) {
        delete this.form.update
        this.$set(this.form, 'query', sql)
      } else {
        delete this.form.query
        this.$set(this.form, 'update', sql)
      }
    },

    getConnectors() {
      this.connectorList.push({ id: 'custom', name: '自定义' })
      getIntegrationConnectorList({ size: -1, type: 'JDBC', enabled: true }).then(({ records }) => {
        this.connectorList = records
        this.connectorList.push({ id: 'custom', name: '自定义' })
      })
    },

    getProperties() {
      return this.form
    },

    async validate() {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid && !this.sql.trim()) {
            this.$message.error('请编辑SQL')
            return resolve(false)
          }
          resolve(valid)
        })
      })
    }
  },

  computed: {
    sql() {
      return this.form.query || this.form.update || ''
    }
  },

  props: {
    properties: {
      type: Object,
      default: () => ({})
    },
    node: {
      type: Object,
      default: () => ({})
    }
  },

  watch: {
    'form.connector': {
      handler(val) {
        if (val === 'custom') {
          this.$set(this.form, 'dataSource', {
            driverClassName: '',
            url: '',
            username: '',
            password: ''
          })
        } else {
          this.$delete(this.form, 'dataSource')
        }
      },
      immediate: true
    },

    properties: {
      handler(newVal) {
        if (!isEqual(newVal, this.form)) {
          this.form = { ...this.form, ...newVal }
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>
