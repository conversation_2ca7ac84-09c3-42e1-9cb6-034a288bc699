// 根据设计图 修改的 dialog / uploader图片 / 弹框中的树结构

/* 全局背景 */
.app-container {}

/* 设计图dialog样式 */
.el-dialog__header {
  border-bottom: 1px solid #e9e9e9;
}

// 添加单张图片样式
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
}

.avatar-uploader-icon {
  color: #8c939d;
}

.navbar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

/* 表格样式 */
.el-table {}

.el-table th {
  background-color: #fafafa;
}

// 布局样式
.el-header {
  background-color: #ffffff;
  color: #333;
}

.el-footer {
  background-color: #ffffff;
  color: #333;
}

.pagination-container {}

.el-aside {
  background-color: #ffffff;
  color: #333;
}

.el-main {
  background-color: #ffffff;
  color: #333;
}

//表格显示 全部字段 tip 控制宽度
.el-tooltip__popper {}

//表单label
.el-form-item__label {}

.el-form-item {}

.el-form-item__label {}