{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "esModuleInterop": true, "noEmit": true, "skipLibCheck": true, "noImplicitAny": false, "noImplicitThis": false, "strict": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "allowJs": true, "sourceMap": false, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["projects/basics/*"], "@basics/*": ["projects/basics/*"], "@authlogin/*": ["projects/authlogin/*"], "@extreme-flow/*": ["projects/extreme-flow/*"]}}, "include": ["projects/basics/**/*.ts", "projects/basics/**/*.tsx", "projects/basics/**/*.vue", "projects/authlogin/**/*.ts", "projects/authlogin/**/*.tsx", "projects/authlogin/**/*.vue", "projects/extreme-flow/**/*.ts", "projects/extreme-flow/**/*.tsx", "projects/extreme-flow/**/*.vue"], "exclude": ["node_modules"]}