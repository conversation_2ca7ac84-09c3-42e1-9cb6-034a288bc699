import request from '@/utils/request'

export function verifyPassword(password) {
    return request({
        url: 'system/verify/password',
        method: 'post',
        data: { password }
    })
}

// 验证身份（用于重置密码第一步）
export function verifyResetCode(data) {
    return request({
        url: 'system/verify/identity',
        method: 'post',
        data
    })
}
// 重置密码（用于重置密码第二步）
export function resetPassword(tempToken, data) {
    return request({
        url: 'system/resetPassword',
        method: 'put',
        headers: {
            'X-Temp-Token': tempToken
        },
        data
    })
}

// 注册接口
export function register(data) {
    return request({
        url: 'system/register',
        method: 'post',
        data
    })
}

// 根据key获取配置
export function getConfigByKey(key) {
    return request({
        url: 'system/sys/config/key',
        method: 'get',
        params: { key }
    })
}

export function getConfigByKeys(keys) {
    return request({
        url: 'system/sys/config/keys',
        method: 'get',
        params: { keys: keys, enabled: true }
    })
}

// 获取启用的连接器列表
export function getPublicEnabledConnectors() {
    return request({
        url: 'system/auth/connector/config/public/list',
        method: 'get',
        params: {
            enabled: true
        }
    })
}
// 执行授权跳转
export function doAuth(connectorId, redirectUri) {
    return request({
        url: 'system/external/auth/doAuth',
        method: 'get',
        params: { connectorId, redirectUri }
    })
}
// 获取二维码信息
export function getQrCode(connectorId) {
    return request({
        url: 'system/external/auth/qrCode',
        method: 'get',
        params: { connectorId }
    })
}

// 轮询登录二维码状态
export function pollLoginStatus(qrCodeKey) {
    return request({
        url: 'system/external/wechat/official/poll',
        method: 'get',
        params: { qrCodeKey }
    })
}