<template>
    <div class="fast-upload-container">
        <el-upload class="upload-demo" ref="upload" :action="uploadUrl" :auto-upload="false"
            :on-change="handleFileChange" :on-remove="handleRemove" :http-request="customUpload" :file-list="fileList"
            :limit="maxFileCount" :on-exceed="handleExceed" :on-success="handleUploadSuccess"
            :on-error="handleUploadError" multiple>
            <el-button type="primary">选择文件</el-button>
            <div class="el-upload__tip" slot="tip">支持的文件类型：{{ acceptTypes.join('、') }}，单个文件不超过 {{
                formatFileSize(maxFileSize) }}</div>
        </el-upload>

        <div class="upload-actions" v-if="fileList.length > 0">
            <el-button type="primary" @click="submitUpload" :loading="uploading">开始上传</el-button>
            <el-button @click="clearFiles" :disabled="uploading">清空列表</el-button>
        </div>
    </div>
</template>

<script>
import UploadHelper from "./uploadHelper.js";


export default {
    props: {
        acceptTypes: {
            type: Array,
            default: () => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
        },
        maxFileSize: {
            type: Number,
            default: 50 * 1024 * 1024 // 50MB
        },
        maxFileCount: {
            type: Number,
            default: 10
        }
    },
    data() {
        return {
            fileList: [],
            uploading: false,
            uploadUrl: process.env.VUE_APP_FILE_URL
        };
    },
    mounted() {
        // 初始化上传助手
        this.uploader = new UploadHelper({
            chunkSize: 5 * 1024 * 1024, // 2MB
            allowedTypes: this.acceptTypes,
            maxSize: this.maxFileSize
        });
    },
    methods: {
        submitUpload() {
            if (this.uploading) return;
            this.uploading = true;
            this.$refs.upload.submit();
        },

        handleFileChange(file, fileList) {
            this.fileList = fileList;
        },

        customUpload({ file, onProgress, onSuccess, onError }) {
            this.uploader.upload(
                file,
                (progress) => {
                    onProgress({ percent: progress });
                },
                (url) => {
                    onSuccess({ url }, file);
                },
                (error) => {
                    onError(error);
                }
            );
        },

        // 处理上传成功的回调
        handleUploadSuccess(response, file, fileList) {
            file.status = 'success';
            file.url = response.url;
            this.successCount++;
            this.$message.success(`文件 ${file.name} 上传成功`);
            this.uploading = false;
        },

        // 处理上传失败的回调
        handleUploadError(error, file, fileList) {
            file.status = 'error';
            this.$message.error(`文件 ${file.name} 上传失败: ${error.message || '未知错误'}`);
            this.uploading = false;
        },

        handleRemove(file, fileList) {
            this.fileList = fileList;
        },

        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 ${this.maxFileCount} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },

        clearFiles() {
            if (this.uploading) return;
            this.fileList = [];
            this.$refs.upload.clearFiles();
        },

        formatFileSize(size) {
            if (!size) return '0 B';
            const units = ['B', 'KB', 'MB', 'GB', 'TB'];
            let index = 0;
            while (size >= 1024 && index < units.length - 1) {
                size /= 1024;
                index++;
            }
            return `${size.toFixed(2)} ${units[index]}`;
        }
    }
}
</script>

<style lang="scss" scoped>
.fast-upload-container {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .upload-demo {
        width: 100%;
    }

    .upload-actions {
        margin-top: 20px;
    }
}
</style>