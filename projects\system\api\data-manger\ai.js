import { SSEClient } from './sse-client';

const sseClient = new SSEClient('/api/meta/chat/scenario', {
  headers: {
    'Authorization': localStorage.getItem('Authorization'),
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream'
  }
})

export function generateSQL(data, callbacks) {
  return sseClient.connect({
    scenario: 'SQL_ANALYSIS',
    prompt: data.prompt,
    params: {
      tableIds: data.params.tableIds
    }
  }, callbacks)
} 