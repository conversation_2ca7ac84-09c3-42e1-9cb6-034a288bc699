<template>
  <div style="text-align:center;">
    <img src="./exception.svg">
    <div>
      <p v-if="msg?.length > 0">{{ msg }}</p>

      <div class="back-to-login" @click="goLogin">
        <i class="el-icon-switch-button"></i>
        <span>返回账号密码登录</span>
      </div>
    </div>
  </div>
</template>
<script>
import { removeToken } from '@/utils/auth'

export default {
  data() {
    return {
      msg: ''
    }
  },
  created() {
    this.msg = decodeURIComponent(this.$route.query.msg)
  },
  methods: {
    goLogin() {
      removeToken()
      this.$router.replace({ path: '/login' })
    }
  }
}
</script>
<style lang="scss" scoped>
img {
  width: 500px;
  margin-top: 20px;
}

.back-to-login {
  display: inline-block;
  color: #909399;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
  margin-top: 20px;

  &:hover {
    color: #409EFF;
    background: #f5f7fa;
  }

  i {
    margin-right: 6px;
    font-size: 16px;
  }
}
</style>