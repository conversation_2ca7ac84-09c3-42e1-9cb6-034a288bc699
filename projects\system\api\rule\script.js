import request from '@/utils/request'

const api = '/rule'

// 获取脚本列表
export function getRuleScriptList(params) {
  return request({
    url: api + '/rule/script/list',
    method: 'get',
    params
  })
}

// 获取脚本详情
export function getRuleScriptByScriptId(scriptId) {
  return request({
    url: api + '/rule/script',
    method: 'get',
    params: { scriptId }
  })
}

// 获取脚本详情
export function getRuleScriptDetail(id) {
  return request({
    url: api + '/rule/script',
    method: 'get',
    params: { id }
  })
}

// 创建脚本
export function createRuleScript(data) {
  return request({
    url: api + '/rule/script',
    method: 'post',
    data
  })
}

// 更新脚本
export function updateRuleScript(data) {
  return request({
    url: api + '/rule/script',
    method: 'put',
    data
  })
}

// 删除脚本
export function deleteRuleScript(id) {
  return request({
    url: api + `/rule/script/${id}`,
    method: 'delete'
  })
}

// 校验脚本
export function validateScript(data) {
  return request({
    url: api + '/rule/script/validate',
    method: 'post',
    data
  })
}

// 添加获取应用列表的API接口
export function getApplicationList(groupName = 'application') {
  return request({
    url: api+ '/rule/script/groupNames',
    method: 'get',
    params: { groupName }
  })
}
