<template>
  <div class="integration-connector">
    <!-- 顶部搜索和筛选栏 -->
    <div class="header-section">
      <div class="search-filter-bar">
        <div class="custom-search-input">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索连接器名称、编码或描述..."
            class="search-input-field"
            @keyup.enter="handleSearch"
          />
          <button
            v-if="searchKeyword"
            class="clear-button"
            @click="clearSearch"
            title="清除"
          >
            <i class="el-icon-close"></i>
          </button>
          <button
            class="search-button"
            @click="handleSearch"
            title="搜索"
          >
            <i class="el-icon-search"></i>
            <span>搜索</span>
          </button>
        </div>

        <el-select
          v-model="selectedType"
          placeholder="连接器类型"
          clearable
          class="filter-select"
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in connectorTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          >
            <i :class="type.icon"></i>
            <span style="margin-left: 8px;">{{ type.label }}</span>
          </el-option>
        </el-select>

        <el-select
          v-model="selectedStatus"
          placeholder="连接器状态"
          clearable
          class="filter-select"
          @change="handleStatusChange"
        >
          <el-option
            v-for="status in connectorStatuses"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          >
            <i :class="status.icon" :style="{ color: status.color }"></i>
            <span style="margin-left: 8px;">{{ status.label }}</span>
          </el-option>
        </el-select>

      </div>
    </div>

    <!-- 连接器列表 -->
    <div
      class="app-container"
      v-loading="loading"
    >
      <!-- 网格视图 -->
      <div class="grid-view">
        <div class="app-grid">
          <!-- 新增连接器卡片 -->
          <div class="app-card create-card" @click="handleCreateConnector">
            <div class="create-card-content">
              <div class="create-icon">
                <i class="el-icon-plus"></i>
              </div>
              <div class="create-text">
                <h3>创建连接器</h3>
                <div class="create-options">
                  <div class="create-option" @click.stop="handleCreateFromTemplate">
                    <i class="el-icon-document"></i>
                    <span>创建空白连接器</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 连接器卡片 -->
          <div
            v-for="connector in filteredConnectors"
            :key="connector.id"
            class="app-card"
            @click="handleConnectorClick(connector)"
          >
            <div class="app-card-header">
              <div class="app-icon">
                <div class="default-icon" :style="getDefaultIconStyle(connector.name)">
                  {{ getAppIconLetter(connector.name) }}
                </div>
              </div>

              <div class="app-info">
                <div class="app-title">
                  <h3 class="app-name" :title="connector.name">{{ connector.name }}</h3>
                </div>
              </div>

              <div class="app-type-badge" :class="getTypeBadgeClass(connector.type)">
                <span class="type-text">{{ getTypeLabel(connector.type) }}</span>
              </div>


            </div>

            <div class="app-card-body">
              <p class="app-description" :title="connector.description">
                {{ connector.description || '暂无描述' }}
              </p>

              <div class="app-tags">
                <div class="tags-left">
                  <el-tag
                    size="mini"
                    class="app-tag"
                  >
                    {{ connector.code }}
                  </el-tag>
                </div>
                <div class="app-status-badge" :class="getAppStatusClass(connector)">
                  <i :class="getAppStatusIcon(connector)"></i>
                  <span class="status-text">{{ getAppStatusText(connector) }}</span>
                </div>
              </div>
            </div>

            <div class="app-card-footer">
              <span class="create-time">
                {{ formatDate(connector.createTime) }}
              </span>
              <div class="app-actions">
                <el-button
                  type="text"
                  icon="el-icon-view"
                  size="mini"
                  @click.stop="handleViewDetail(connector)"
                  title="查看详情"
                />
                <el-dropdown
                  @command="(command) => handleDropdownCommand(command, connector)"
                  trigger="click"
                  @click.stop
                  placement="bottom-end"
                  popper-class="connector-dropdown-menu"
                >
                  <el-button
                    type="text"
                    icon="el-icon-more"
                    size="mini"
                    title="更多操作"
                    @click.stop
                    class="more-btn"
                  />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit" class="dropdown-item-custom">
                      <i class="el-icon-edit-outline"></i>
                      <span>编辑基础信息</span>
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided class="dropdown-item-custom delete-item">
                      <i class="el-icon-delete"></i>
                      <span>删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredConnectors.length === 0 && !loading" class="empty-state">
        <i class="el-icon-box"></i>
        <p>暂无连接器数据</p>
        <p class="empty-tip">{{ searchKeyword ? '尝试调整搜索条件' : '暂时没有集成连接器' }}</p>
      </div>

    </div>

    <!-- 分页组件 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background
      />
    </div>

    <!-- 创建连接器对话框 -->
    <CreateConnectorDialog
      :visible.sync="showCreateDialog"
      :edit-mode="false"
      @created="handleConnectorCreated"
    />

    <!-- 编辑连接器对话框 -->
    <CreateConnectorDialog
      :visible.sync="showEditDialog"
      :edit-mode="true"
      :edit-data="editConnectorData"
      @updated="handleConnectorUpdated"
    />
  </div>
</template>

<script>
import {
  getIntegrationConnectorList,
  deleteIntegrationConnector,
  updateIntegrationConnectorStatus
} from '@system/api/integration/integration-connetor'
import { getDefaultIconStyle as getIconStyle, getAppIconLetter as getIconLetter } from '@system/utils/appIconUtils'
import CreateConnectorDialog from './components/ConnectorFormDialog.vue'

export default {
  name: 'IntegrationConnectorList',
  components: {
    CreateConnectorDialog
  },
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedType: '',
      selectedStatus: '',

      // 数据
      connectorList: [],
      connectorTypes: [
        { value: 'HTTP', label: 'HTTP', icon: 'el-icon-link' },
        { value: 'JDBC', label: 'JDBC', icon: 'el-icon-coin' }
      ],
      connectorStatuses: [
        { value: true, label: '启用', icon: 'el-icon-check', color: '#52c41a' },
        { value: false, label: '禁用', icon: 'el-icon-close', color: '#ff4d4f' }
      ],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // 状态
      loading: false,
      showCreateDialog: false,
      showEditDialog: false,
      editConnectorData: {}
    }
  },
  
  computed: {
    filteredConnectors() {
      return this.connectorList
    }
  },
  
  created() {
    this.initData()
  },
  
  methods: {
    async initData() {
      this.loading = true
      await this.loadConnectorList(true)
      this.loading = false
    },

    // 加载连接器列表
    async loadConnectorList(resetPage = false) {
      if (resetPage) {
        this.currentPage = 1
      }

      // 构建请求参数
      const params = {
        current: this.currentPage,
        size: this.pageSize,
        'orders[sort]': 'asc',
        ...(this.searchKeyword?.trim() && { keyword: this.searchKeyword.trim() }),
        ...(this.selectedType && { type: this.selectedType }),
        ...(this.selectedStatus !== '' && { enabled: this.selectedStatus })
      }

      const response = await getIntegrationConnectorList(params)
      // 全局响应已经把data取出来了，直接使用
      let newList = []
      let total = 0

      if (response && response.records) {
        // MyBatis Plus 分页结构
        newList = response.records
        total = response.total || 0
      } else if (response && response.list) {
        // 自定义分页结构
        newList = response.list
        total = response.total || 0
      } else if (Array.isArray(response)) {
        // 直接返回数组
        newList = response
        total = response.length
      }

      this.connectorList = newList
      this.total = total
    },

    // 分页事件处理
    handleSizeChange(newSize) {
      this.pageSize = newSize
      this.currentPage = 1
      this.loadConnectorList(true)
    },

    handleCurrentChange(newPage) {
      this.currentPage = newPage
      this.loadConnectorList(false)
    },

    // 搜索处理
    handleSearch() {
      this.loadConnectorList(true)
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.loadConnectorList(true)
    },

    // 类型筛选
    handleTypeChange() {
      this.loadConnectorList(true)
    },

    // 状态筛选
    handleStatusChange() {
      this.loadConnectorList(true)
    },

    // 连接器操作
    handleConnectorClick(connector) {
      const { type, id, code, name } = connector;
      console.log('点击连接器:', connector);

      this.$router.push({
        path: `/integration/integration-connector/${type.toLowerCase()}/detail`,
        query: { id, code, name }
      });
    },

    handleViewDetail(connector) {
      // 与点击卡片逻辑一致，直接进入详情页
      this.handleConnectorClick(connector)
    },

    // 处理下拉菜单命令
    handleDropdownCommand(command, connector) {
      switch (command) {
        case 'edit':
          this.handleEditConnector(connector)
          break
        case 'delete':
          this.handleDeleteConnector(connector)
          break
        default:
          console.log('未知命令:', command)
      }
    },

    // 编辑连接器
    handleEditConnector(connector) {
      this.editConnectorData = { ...connector }
      this.showEditDialog = true
    },

    // 删除连接器
    async handleDeleteConnector(connector) {
      await this.$confirm(
        `确定要删除连接器"${connector.name}"吗？此操作不可撤销。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      )

      await deleteIntegrationConnector(connector.id)
      this.$message.success('删除成功')
      this.loadConnectorList()
    },

    // 创建连接器相关方法
    handleCreateConnector() {
      this.showCreateDialog = true
    },

    handleCreateFromTemplate() {
      this.showCreateDialog = true
    },



    // 处理连接器创建成功
    handleConnectorCreated(connector) {
      this.$message.success('创建成功')
      this.loadConnectorList(true) 
    },

    // 处理连接器更新成功
    handleConnectorUpdated(connector) {
      this.$message.success('更新成功')
      this.loadConnectorList(true)
      this.showEditDialog = false
      this.editConnectorData = {}
    },

    // 工具方法
    getDefaultIconStyle(name) {
      return getIconStyle(name)
    },

    getAppIconLetter(name) {
      return getIconLetter(name)
    },

    getTypeBadgeClass(type) {
      const classMap = {
        'HTTP': 'badge-proxy',
        'JDBC': 'badge-flow',
        'FTP': 'ftp-flow',
        'MESSAGE': 'badge-default'
      }
      return classMap[type] || 'badge-default'
    },

    getTypeLabel(type) {
      const typeMap = {
        'HTTP': 'HTTP',
        'JDBC': 'JDBC',
        'MESSAGE': '消息'
      }
      return typeMap[type] || type
    },

    getAppStatusClass(connector) {
      return connector.enabled ? 'status-enabled' : 'status-disabled'
    },

    getAppStatusIcon(connector) {
      return connector.enabled ? 'el-icon-check' : 'el-icon-close'
    },

    getAppStatusText(connector) {
      return connector.enabled ? '启用' : '禁用'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.integration-connector {
  padding: 16px 16px 8px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #f0f2f5 100%);
  min-height: calc(100vh - 60px);
  height: calc(100vh - 84px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .header-section {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      pointer-events: none;
    }

    .search-filter-bar {
      display: flex;
      align-items: center;
      gap: 32px;
      flex-wrap: wrap;
      position: relative;
      z-index: 1;

      .custom-search-input {
        flex: 1;
        min-width: 280px;
        max-width: 380px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        overflow: hidden;

        &:hover {
          transform: translateY(-1px);
          border-color: rgba(24, 144, 255, 0.3);
          background: rgba(255, 255, 255, 0.95);
        }

        &:focus-within {
          transform: translateY(-1px);
          border-color: #1890ff;
          background: white;
          box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .search-input-field {
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          padding: 0 16px;
          height: 40px;
          font-size: 14px;
          color: #262626;
          caret-color: #1890ff;

          &::placeholder {
            color: #8c8c8c;
            font-size: 13px;
          }
        }

        .clear-button {
          background: none;
          border: none;
          padding: 8px;
          cursor: pointer;
          color: #8c8c8c;
          transition: all 0.2s;
          border-radius: 6px;
          margin-right: 4px;

          &:hover {
            color: #ff4d4f;
            background: rgba(255, 77, 79, 0.1);
          }
        }

        .search-button {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          border: none;
          padding: 0 16px;
          height: 40px;
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border-radius: 0 11px 11px 0;

          &:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
            transform: translateX(1px);
          }

          &:active {
            transform: translateX(0);
          }
        }
      }

      .filter-select {
        min-width: 140px;

        ::v-deep .el-input__inner {
          background: rgba(255, 255, 255, 0.9);
          border: 1px solid rgba(0, 0, 0, 0.08);
          border-radius: 12px;
          height: 40px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(8px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

          &:hover {
            transform: translateY(-1px);
            border-color: rgba(24, 144, 255, 0.3);
            background: rgba(255, 255, 255, 0.95);
          }

          &:focus {
            transform: translateY(-1px);
            border-color: #1890ff;
            background: white;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
          }
        }
      }
    }
  }

  .app-container {
    flex: 1;
    min-height: 400px;
    display: flex;
    flex-direction: column;

    // 网格视图样式
    .grid-view {
      flex: 1;

      .app-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
        padding: 4px;
      }

      .app-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        height: 200px;
        display: flex;
        flex-direction: column;
        border: 1px solid rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;

        // 新增卡片样式
        &.create-card {
          border: 2px dashed #d9d9d9;
          background: #fafafa;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            border-color: #1890ff;
            background: #f6f8ff;
            transform: translateY(-2px);

            .create-icon {
              color: #1890ff;
              transform: scale(1.1);
            }
          }

          .create-card-content {
            text-align: center;
            width: 100%;

            .create-icon {
              font-size: 32px;
              color: #bfbfbf;
              margin-bottom: 16px;
              transition: all 0.3s ease;
            }

            .create-text {
              h3 {
                font-size: 16px;
                color: #262626;
                margin: 0 0 16px 0;
                font-weight: 600;
              }

              .create-options {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .create-option {
                  display: flex;
                  align-items: center;
                  padding: 8px 12px;
                  border-radius: 6px;
                  background: white;
                  border: 1px solid #e8e8e8;
                  transition: all 0.2s ease;
                  cursor: pointer;

                  &:hover {
                    border-color: #1890ff;
                    background: #f6f8ff;
                    transform: translateX(4px);
                  }

                  i {
                    font-size: 14px;
                    color: #666;
                    margin-right: 8px;
                    width: 16px;
                  }

                  span {
                    font-size: 13px;
                    color: #595959;
                  }
                }
              }
            }
          }
        }

        // 悬停效果
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
        background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          transform: translateY(-4px);
          border-color: rgba(24, 144, 255, 0.2);

          &::before {
            opacity: 1;
          }


        }

        .app-card-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 10px;
          position: relative;

          .app-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f2f5;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            flex-shrink: 0;

            .default-icon {
              font-size: 20px;
              font-weight: 500;
              border-radius: 10px;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .app-info {
            flex: 1;
            min-width: 0;

            .app-title {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .app-name {
                font-size: 14px;
                font-weight: 600;
                color: #262626;
                margin: 0;
                flex: 1;
                min-width: 0;
                line-height: 1.3;
                word-break: break-word;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }


            }
          }

          .app-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            flex-shrink: 0;
            align-self: flex-start;

            &.badge-proxy {
              background: #e6f7ff;
              color: #1890ff;
            }

            &.badge-flow {
              background: #fff7e6;
              color: #fa8c16;
            }

            &.ftp-flow {
              background: #67C23A;
              color: #d7ffc1;
            }

            &.badge-default {
              background: #f0f0f0;
              color: #666;
            }

            .type-text {
              font-size: 11px;
            }
          }


        }

        .app-card-body {
          flex: 1;
          display: flex;
          flex-direction: column;

          .app-description {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            margin: 0 0 12px 0;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            flex: 1;
          }

          .app-tags {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;

            .tags-left {
              display: flex;
              flex-wrap: wrap;
              gap: 6px;
              flex: 1;
              min-width: 0;

              .app-tag {
                font-size: 10px;
                height: 20px;
                line-height: 18px;
                padding: 0 6px;
                border: none;
                background: linear-gradient(135deg, #f6f8fa, #e8e8e8);
                color: #586069;
                border-radius: 10px;
                font-weight: 500;
                transition: all 0.2s ease;

                &:hover {
                  background: linear-gradient(135deg, #e8e8e8, #d9d9d9);
                  transform: translateY(-1px);
                }
              }
            }

            .app-status-badge {
              display: inline-flex;
              align-items: center;
              gap: 4px;
              padding: 3px 8px;
              border-radius: 12px;
              font-size: 10px;
              font-weight: 600;
              transition: all 0.3s ease;
              margin-left: 8px;
              flex-shrink: 0;

              i {
                font-size: 10px;
              }

              .status-text {
                font-weight: 600;
              }

              &.status-enabled {
                background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
                color: #ffffff;
                box-shadow: 0 1px 4px rgba(82, 196, 26, 0.3);

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.4);
                }
              }

              &.status-disabled {
                background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
                color: #ffffff;
                box-shadow: 0 1px 4px rgba(255, 77, 79, 0.3);

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.4);
                }
              }
            }
          }
        }

        .app-card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12px;
          padding-top: 8px;
          border-top: 1px solid #f0f0f0;

          .create-time {
            font-size: 12px;
            color: #999;
            font-weight: 500;
          }

          .app-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 6px 10px;
              color: #8c8c8c;
              border-radius: 6px;
              transition: all 0.2s ease;

              &:hover {
                color: #1890ff;
                background: rgba(24, 144, 255, 0.1);
                transform: scale(1.05);
              }
            }

            .more-btn {
              padding: 4px;
              border-radius: 4px;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(24, 144, 255, 0.1);
                color: #1890ff;
              }

              i {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    // 空状态样式
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #8c8c8c;

      i {
        font-size: 64px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }

      p {
        margin: 8px 0;
        font-size: 14px;

        &.empty-tip {
          font-size: 12px;
          color: #bfbfbf;
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: auto;
    margin-bottom: 0;
    padding: 12px 20px 8px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.3);

    ::v-deep .el-pagination {
      .btn-prev,
      .btn-next,
      .el-pager li {
        background: #ffffff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin: 0 4px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        color: #606266;
        min-width: 32px;
        height: 32px;
        line-height: 30px;

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
          background: #f0f8ff;
          transform: translateY(-1px);
        }

        &.is-active {
          background: #1890ff !important;
          color: #ffffff !important;
          font-weight: 600;
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        &.disabled {
          color: #c0c4cc;
          background: #f5f7fa;
          border-color: #e4e7ed;
          cursor: not-allowed;

          &:hover {
            color: #c0c4cc;
            background: #f5f7fa;
            border-color: #e4e7ed;
            transform: none;
          }
        }
      }

      .el-pagination__sizes,
      .el-pagination__total,
      .el-pagination__jump {
        color: #606266;
        font-weight: 500;

        .el-input__inner {
          background: #ffffff;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          transition: all 0.3s ease;
          color: #606266;

          &:focus {
            border-color: #1890ff;
            background: #ffffff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }

      .el-pagination__total {
        margin-right: 16px;
      }

      .el-pagination__jump {
        margin-left: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .integration-connector {
    padding: 12px 12px 6px 12px;

    .header-section {
      padding: 20px 16px;
      margin-bottom: 20px;

      .search-filter-bar {
        flex-direction: column;
        gap: 24px;
        align-items: stretch;

        .custom-search-input {
          min-width: auto;
          max-width: none;

          .search-input-field {
            height: 44px;
            font-size: 16px; // 防止iOS缩放
          }
        }

        .filter-select {
          min-width: auto;

          ::v-deep .el-input__inner {
            height: 44px;
            font-size: 16px; // 防止iOS缩放
          }
        }
      }
    }

    .grid-view {
      .app-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .app-card {
        height: auto;
        min-height: 180px;
        padding: 14px;
      }
    }

    .pagination-wrapper {
      margin-top: 16px;
      padding: 10px 16px 6px 16px;
      border-radius: 10px;
    }
  }
}

@media (max-width: 480px) {
  .integration-connector {
    padding: 8px 8px 4px 8px;

    .header-section {
      padding: 16px 12px;
      margin-bottom: 16px;
      border-radius: 12px;

      .search-filter-bar {
        gap: 20px;

        .custom-search-input {
          .search-input-field {
            height: 42px;
            border-radius: 10px;
            font-size: 16px;
          }
        }

        .filter-select {
          ::v-deep .el-input__inner {
            height: 42px;
            border-radius: 10px;
            font-size: 16px;
          }
        }
      }
    }

    .grid-view {
      .app-grid {
        gap: 12px;
      }

      .app-card {
        padding: 12px;
        min-height: 160px;

        .app-card-header {
          margin-bottom: 12px;
          gap: 10px;

          .app-icon {
            width: 40px;
            height: 40px;
          }

          .app-info {
            .app-title {
              .app-name {
                font-size: 15px;
              }


            }
          }
        }

        .app-card-body {
          .app-description {
            font-size: 12px;
            -webkit-line-clamp: 2;
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: 12px;
      padding: 8px 12px 4px 12px;
      border-radius: 8px;
    }
  }
}

// 全局下拉菜单样式
::v-deep .el-select-dropdown {
  border: none;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin-top: 4px;

  .el-select-dropdown__item {
    padding: 12px 16px;
    font-size: 14px;
    color: #262626;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 8px;

    &:hover {
      background: rgba(24, 144, 255, 0.08);
      color: #1890ff;
      transform: translateX(2px);
    }

    &.selected {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      font-weight: 500;

      &::after {
        color: #1890ff;
      }
    }

    i {
      margin-right: 8px;
      font-size: 14px;
    }
  }

  .el-scrollbar__view {
    padding: 0;
  }
}

// 多选标签样式
::v-deep .el-tag {
  &.el-tag--info {
    background: rgba(24, 144, 255, 0.1);
    border-color: rgba(24, 144, 255, 0.2);
    color: #1890ff;
    border-radius: 6px;
    font-size: 12px;
    height: 24px;
    line-height: 22px;
    padding: 0 8px;

    .el-tag__close {
      color: #1890ff;

      &:hover {
        background: rgba(24, 144, 255, 0.2);
        color: white;
      }
    }
  }
}

// 下拉菜单美化样式
::v-deep .connector-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  padding: 4px 0;
  min-width: 140px;

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.4;
    transition: all 0.2s ease;

    &.dropdown-item-custom {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 14px;
        width: 14px;
        text-align: center;
      }

      span {
        flex: 1;
      }
    }

    &:hover {
      background: #f6f8fa;
      color: #1890ff;
    }

    &.delete-item:hover {
      background: #fff2f0;
      color: #ff4d4f;
    }

    &:not(:last-child) {
      margin-bottom: 2px;
    }
  }

  .popper__arrow {
    border-bottom-color: #e8e8e8;
  }

  .popper__arrow::after {
    border-bottom-color: #fff;
  }
}
</style>
