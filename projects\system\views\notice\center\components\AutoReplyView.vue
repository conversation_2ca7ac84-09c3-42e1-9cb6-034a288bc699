<template>
  <div class="auto-reply-view">
    <div class="auto-reply-header">
      <h3>自动回复设置</h3>
      <p class="auto-reply-desc">设置您的自动回复内容和规则，在您离开时自动回复消息</p>
    </div>

    <div class="auto-reply-container">
      <div class="settings-section">
        <div class="section-title">
          <span>自动回复状态</span>
        </div>
        <div class="section-content">
          <el-form label-width="120px">
            <el-form-item label="开启自动回复">
              <el-switch 
                v-model="settings.enabled"
                @change="handleStatusChange"
              ></el-switch>
              <div class="switch-desc">{{ settings.enabled ? '已开启' : '已关闭' }}</div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="settings-section" v-if="settings.enabled">
        <div class="section-title">
          <span>自动回复内容</span>
        </div>
        <div class="section-content">
          <el-form label-width="120px">
            <el-form-item label="回复内容">
              <el-input
                type="textarea"
                v-model="settings.content"
                placeholder="请输入自动回复的内容"
                :rows="4"
              ></el-input>
              <div class="form-tips">
                支持使用变量：{name} - 发送者姓名, {time} - 当前时间
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="settings-section" v-if="settings.enabled">
        <div class="section-title">
          <span>回复规则</span>
        </div>
        <div class="section-content">
          <el-form label-width="120px">
            <el-form-item label="回复范围">
              <el-radio-group v-model="settings.replyScope">
                <el-radio :label="'all'">所有人</el-radio>
                <el-radio :label="'contacts'">仅联系人</el-radio>
                <el-radio :label="'custom'">自定义</el-radio>
              </el-radio-group>
              
              <div v-if="settings.replyScope === 'custom'" class="custom-scope">
                <el-form-item label="自定义联系人">
                  <el-select
                    v-model="settings.customContacts"
                    multiple
                    placeholder="请选择联系人"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="contact in contactsList"
                      :key="contact.id"
                      :label="contact.name"
                      :value="contact.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-form-item>

            <el-form-item label="生效时段">
              <el-radio-group v-model="settings.effectiveTime">
                <el-radio :label="'all'">全天</el-radio>
                <el-radio :label="'custom'">自定义时间</el-radio>
              </el-radio-group>
              
              <div v-if="settings.effectiveTime === 'custom'" class="time-range">
                <el-time-picker
                  v-model="settings.startTime"
                  placeholder="开始时间"
                  format="HH:mm"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                ></el-time-picker>
                <span class="time-separator">至</span>
                <el-time-picker
                  v-model="settings.endTime"
                  placeholder="结束时间"
                  format="HH:mm"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                ></el-time-picker>
              </div>
            </el-form-item>

            <el-form-item label="生效日期">
              <el-checkbox-group v-model="settings.effectiveDays">
                <el-checkbox label="1">周一</el-checkbox>
                <el-checkbox label="2">周二</el-checkbox>
                <el-checkbox label="3">周三</el-checkbox>
                <el-checkbox label="4">周四</el-checkbox>
                <el-checkbox label="5">周五</el-checkbox>
                <el-checkbox label="6">周六</el-checkbox>
                <el-checkbox label="0">周日</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="延迟回复">
              <el-switch v-model="settings.delayEnabled"></el-switch>
              <div class="switch-desc">{{ settings.delayEnabled ? '已开启' : '已关闭' }}</div>
              
              <div v-if="settings.delayEnabled" class="delay-settings">
                <el-form-item label="延迟时间">
                  <el-slider
                    v-model="settings.delayTime"
                    :min="1"
                    :max="60"
                    :step="1"
                    :format-tooltip="formatDelayTime"
                    style="width: 300px"
                  ></el-slider>
                </el-form-item>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="settings-section" v-if="settings.enabled">
        <div class="section-title">
          <span>消息模板</span>
          <el-button 
            type="primary" 
            size="small" 
            icon="el-icon-plus" 
            plain
            @click="handleAddTemplate"
          >
            添加模板
          </el-button>
        </div>
        <div class="section-content">
          <div v-if="settings.templates.length > 0" class="templates-list">
            <el-card
              v-for="(template, index) in settings.templates"
              :key="index"
              class="template-card"
              shadow="hover"
            >
              <div slot="header" class="template-header">
                <span>{{ template.name }}</span>
                <div class="template-actions">
                  <el-button 
                    type="text" 
                    size="small"
                    @click="handleEditTemplate(index)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    type="text" 
                    size="small" 
                    class="danger-text"
                    @click="handleDeleteTemplate(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
              <div class="template-content">
                {{ template.content }}
              </div>
            </el-card>
          </div>
          <div v-else class="empty-templates">
            <i class="el-icon-document"></i>
            <p>暂无消息模板，点击"添加模板"创建</p>
          </div>
        </div>
      </div>
    </div>

    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
      <el-button @click="resetSettings">重置</el-button>
    </div>

    <!-- 模板编辑对话框 -->
    <el-dialog
      :title="editingTemplate ? '编辑模板' : '添加模板'"
      :visible.sync="templateDialogVisible"
      width="550px"
    >
      <el-form :model="currentTemplate" label-width="80px">
        <el-form-item label="模板名称">
          <el-input v-model="currentTemplate.name" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="模板内容">
          <el-input
            type="textarea"
            v-model="currentTemplate.content"
            :rows="5"
            placeholder="请输入模板内容"
          ></el-input>
          <div class="form-tips">
            支持使用变量：{name} - 发送者姓名, {time} - 当前时间
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="templateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveTemplate">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AutoReplyView',
  data() {
    return {
      settings: {
        enabled: false,
        content: '您好，我现在暂时无法回复消息，稍后会联系您。',
        replyScope: 'all',
        customContacts: [],
        effectiveTime: 'all',
        startTime: new Date(2024, 0, 1, 18, 0),
        endTime: new Date(2024, 0, 1, 9, 0),
        effectiveDays: ['0', '1', '2', '3', '4', '5', '6'],
        delayEnabled: false,
        delayTime: 5,
        templates: [
          {
            name: '工作时间',
            content: '您好，我正在工作中，可能会有些延迟回复，谢谢理解。'
          },
          {
            name: '休假',
            content: '您好，我目前正在休假中，将于{time}返回。如有紧急事务，请联系其他同事。'
          }
        ]
      },
      originalSettings: null,
      templateDialogVisible: false,
      currentTemplate: {
        name: '',
        content: ''
      },
      editingTemplateIndex: -1,
      editingTemplate: false,
      contactsList: [
        { id: 'contact_001', name: '系统管理员' },
        { id: 'contact_002', name: '技术支持' },
        { id: 'contact_003', name: '琉璃青RO' },
        { id: 'contact_004', name: '产品经理' },
        { id: 'contact_005', name: '张三' }
      ]
    }
  },
  created() {
    // 保存初始设置，用于重置
    this.originalSettings = JSON.parse(JSON.stringify(this.settings))
  },
  methods: {
    handleStatusChange(value) {
      if (value) {
        this.$message.success('自动回复已开启')
      } else {
        this.$message.info('自动回复已关闭')
      }
    },
    
    formatDelayTime(val) {
      return `${val} 秒`
    },
    
    handleAddTemplate() {
      this.editingTemplate = false
      this.editingTemplateIndex = -1
      this.currentTemplate = {
        name: '',
        content: ''
      }
      this.templateDialogVisible = true
    },
    
    handleEditTemplate(index) {
      this.editingTemplate = true
      this.editingTemplateIndex = index
      this.currentTemplate = JSON.parse(JSON.stringify(this.settings.templates[index]))
      this.templateDialogVisible = true
    },
    
    handleDeleteTemplate(index) {
      this.$confirm('确定要删除该模板吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.settings.templates.splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    
    handleSaveTemplate() {
      if (!this.currentTemplate.name.trim()) {
        this.$message.warning('请输入模板名称')
        return
      }
      
      if (!this.currentTemplate.content.trim()) {
        this.$message.warning('请输入模板内容')
        return
      }
      
      if (this.editingTemplate) {
        // 编辑现有模板
        this.settings.templates[this.editingTemplateIndex] = { ...this.currentTemplate }
      } else {
        // 添加新模板
        this.settings.templates.push({ ...this.currentTemplate })
      }
      
      this.templateDialogVisible = false
      this.$message.success(this.editingTemplate ? '修改成功' : '添加成功')
    },
    
    saveSettings() {
      // 模拟保存设置
      this.$message.success('设置已保存')
      
      // 更新初始设置
      this.originalSettings = JSON.parse(JSON.stringify(this.settings))
    },
    
    resetSettings() {
      this.$confirm('确定要重置所有设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置为初始值
        this.settings = JSON.parse(JSON.stringify(this.originalSettings))
        this.$message.success('设置已重置')
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.auto-reply-view {
  flex: 1;
  overflow: auto;
  padding: 20px;

  scrollbar-width: thin; /* Firefox */
  
  /* WebKit (Chrome/Safari/Edge) */
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 2px;
  }
  
  .auto-reply-header {
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      color: #303133;
      margin: 0 0 8px 0;
    }
    
    .auto-reply-desc {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .auto-reply-container {
    .settings-section {
      background-color: #ffffff;
      border-radius: 6px;
      margin-bottom: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      
      .section-title {
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        span {
          font-size: 15px;
          font-weight: 500;
          color: #303133;
        }
      }
      
      .section-content {
        padding: 20px;
        
        .switch-desc {
          display: inline-block;
          margin-left: 10px;
          color: #909399;
          font-size: 13px;
        }
        
        .form-tips {
          margin-top: 5px;
          color: #909399;
          font-size: 12px;
        }
        
        .custom-scope {
          margin-top: 15px;
          padding-left: 25px;
        }
        
        .time-range {
          margin-top: 15px;
          padding-left: 25px;
          display: flex;
          align-items: center;
          
          .time-separator {
            margin: 0 10px;
            color: #606266;
          }
        }
        
        .delay-settings {
          margin-top: 15px;
          padding-left: 25px;
        }
        
        .templates-list {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
          gap: 16px;
          
          .template-card {
            .template-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              span {
                font-weight: 500;
              }
              
              .template-actions {
                .danger-text {
                  color: #F56C6C;
                }
              }
            }
            
            .template-content {
              font-size: 13px;
              color: #606266;
              line-height: 1.5;
              max-height: 100px;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              -webkit-box-orient: vertical;
            }
          }
        }
        
        .empty-templates {
          text-align: center;
          padding: 40px 0;
          
          i {
            font-size: 48px;
            color: #dcdfe6;
            margin-bottom: 16px;
          }
          
          p {
            color: #909399;
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .settings-actions {
    margin-top: 30px;
    border-top: 1px solid #ebeef5;
    padding-top: 20px;
  }
}
</style> 