<template>
  <div class="auth-chain-config">
    <div class="config-section">
      <div class="section-header" @click="toggleCollapse">
        <div class="header-left">
          <h4 class="section-title">认证链路配置</h4>
          <el-tag
            :type="getChainStatusType()"
            size="mini"
            effect="plain"
            class="status-tag"
          >
            {{ getChainStatus() }}
          </el-tag>
        </div>
        <div class="header-actions">
          <el-button
            size="mini"
            icon="el-icon-video-play"
            @click.stop="testChain"
            :loading="testLoading"
            :disabled="sortedAuthRequests.length === 0"
            class="test-chain-btn mac-style-btn"
          >
            测试链路
          </el-button>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click.stop="addAuthRequest"
            class="add-step-btn mac-style-btn"
          >
            添加步骤
          </el-button>
          <i :class="['collapse-icon', isCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></i>
        </div>
      </div>

      <el-collapse-transition>
        <div v-show="!isCollapsed" class="chain-content">
          <!-- 认证步骤列表 -->
          <div class="auth-steps" v-if="sortedAuthRequests.length > 0">
            <draggable
              v-model="sortedAuthRequests"
              group="auth-steps"
              @change="handleStepOrderChange"
              class="steps-list"
              handle=".drag-handle"
              :animation="200"
              ghost-class="ghost-card"
              chosen-class="chosen-card"
            >
              <div
                v-for="(request, index) in sortedAuthRequests"
                :key="request.id || request.code"
                class="auth-step-card"
              >
                <div class="card-header" @click="toggleStepExpand(request.id || request.code)">
                  <div class="header-left">
                    <div class="step-order">
                      <span class="order-number">{{ index + 1 }}</span>
                      <i class="el-icon-sort drag-handle"></i>
                    </div>
                    <div class="step-basic-info">
                      <h5 class="step-name">{{ request.name || request.code }}</h5>
                      <div class="step-meta">
                        <span class="step-url">{{ request.url || '未配置URL' }}</span>
                        <span class="step-method">{{ request.method || 'GET' }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="header-right">
                    <el-tag size="small" :type="getStepStatusType(request)">
                      {{ getStepStatus(request) }}
                    </el-tag>
                    <i
                      :class="['expand-icon', expandedSteps.includes(request.id || request.code) ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
                    ></i>
                  </div>
                </div>

                <el-collapse-transition>
                  <div v-show="expandedSteps.includes(request.id || request.code)" class="card-content">
                    <div class="step-form">
                      <el-form
                        :ref="`stepForm_${request.code}`"
                        :model="request"
                        :rules="stepRules"
                        label-width="100px"
                        size="small"
                      >
                        <div class="form-row">
                          <el-form-item label="步骤编码" prop="code" class="form-item-half">
                            <el-input
                              v-model="request.code"
                              placeholder="如：getAccessToken, getUserInfo"
                              @input="(value) => { request.code = value.trim(); handleStepCodeChange(request); }"
                              @mousedown="handleInputMouseDown"
                              @mousemove="handleInputMouseMove"
                              @mouseup="handleInputMouseUp"
                              size="small"
                            />
                            <div class="field-help">
                              用于标识和引用的唯一编码，建议使用驼峰命名
                            </div>
                          </el-form-item>
                          <el-form-item label="步骤名称" prop="name" class="form-item-half">
                            <el-input
                              v-model="request.name"
                              placeholder="如：获取访问令牌"
                              @input="handleStepChange(request)"
                              @mousedown="handleInputMouseDown"
                              @mousemove="handleInputMouseMove"
                              @mouseup="handleInputMouseUp"
                              size="small"
                            />
                            <div class="field-help">
                              步骤的显示名称
                            </div>
                          </el-form-item>
                        </div>

                        <!-- 请求方法和URL -->
                        <el-form-item label="请求配置" prop="url">
                          <div class="url-config-row">
                            <div class="method-selector">
                              <el-select
                                v-model="request.method"
                                placeholder="方法"
                                @change="handleStepChange(request)"
                                size="small"
                                class="colored-method-select"
                                :class="`method-${(request.method || 'GET').toLowerCase()}`"
                              >
                                <el-option label="GET" value="GET"></el-option>
                                <el-option label="POST" value="POST"></el-option>
                                <el-option label="PUT" value="PUT"></el-option>
                                <el-option label="DELETE" value="DELETE"></el-option>
                                <el-option label="PATCH" value="PATCH"></el-option>
                              </el-select>
                            </div>
                            <div class="url-input">
                              <el-input
                                v-model="request.url"
                                placeholder="/oauth/token 或 https://api.example.com/oauth/token"
                                @input="(value) => { request.url = value.trim(); handleStepChange(request); }"
                                @mousedown="handleInputMouseDown"
                                @mousemove="handleInputMouseMove"
                                @mouseup="handleInputMouseUp"
                                size="small"
                              />
                            </div>
                          </div>
                          <div class="field-help">
                            支持表达式：${requestCode.fieldName}
                          </div>
                        </el-form-item>

                        <el-form-item label="描述">
                          <el-input
                            v-model="request.description"
                            type="textarea"
                            :rows="2"
                            placeholder="如：使用客户端凭据获取OAuth2访问令牌"
                            @input="handleStepChange(request)"
                            @mousedown="handleInputMouseDown"
                            @mousemove="handleInputMouseMove"
                            @mouseup="handleInputMouseUp"
                          />
                          <div class="field-help">
                            详细描述此步骤的作用和用途
                          </div>
                        </el-form-item>

                        <!-- 请求头配置 -->
                        <el-form-item label="请求头" prop="headers">
                          <div class="map-input-container">
                            <div class="header-pairs">
                              <div
                                v-for="(pair, index) in request.headersPairs"
                                :key="index"
                                class="header-pair"
                              >
                                <el-input
                                  v-model="pair.key"
                                  placeholder="请求头名称"
                                  size="small"
                                  @input="handleHeadersPairChange(request)"
                                  @mousedown="handleInputMouseDown"
                                  @mousemove="handleInputMouseMove"
                                  @mouseup="handleInputMouseUp"
                                  class="pair-key"
                                />
                                <el-input
                                  v-model="pair.value"
                                  placeholder="请求头值，支持表达式 ${requestCode.field}"
                                  size="small"
                                  @input="handleHeadersPairChange(request)"
                                  @mousedown="handleInputMouseDown"
                                  @mousemove="handleInputMouseMove"
                                  @mouseup="handleInputMouseUp"
                                  class="pair-value"
                                />
                                <el-button
                                  size="mini"
                                  icon="el-icon-delete"
                                  @click.stop="removeHeaderPair(request, index)"
                                  class="remove-btn"
                                />
                              </div>
                            </div>
                            <el-button
                              size="small"
                              icon="el-icon-plus"
                              @click.stop="addHeaderPair(request)"
                              class="add-pair-btn"
                            >
                              添加请求头
                            </el-button>
                          </div>
                          <div class="field-help">
                            支持表达式引用前置请求的响应数据
                          </div>
                        </el-form-item>

                        <!-- 请求体配置 -->
                        <el-form-item label="请求体" prop="body" v-if="request.method !== 'GET'">
                          <div class="json-input-container">
                            <div class="json-editor-header">
                              <span class="json-label">JSON 请求体</span>
                              <div class="json-actions">
                                <el-button
                                  size="mini"
                                  icon="el-icon-magic-stick"
                                  @click="formatJsonBody(request)"
                                  class="format-btn"
                                  title="格式化JSON"
                                >
                                  格式化
                                </el-button>
                                <el-button
                                  size="mini"
                                  icon="el-icon-document-copy"
                                  @click="copyJsonBody(request)"
                                  class="copy-btn"
                                  title="复制JSON"
                                >
                                  复制
                                </el-button>
                              </div>
                            </div>
                            <div class="json-editor-wrapper">
                              <el-input
                                v-model="request.bodyJson"
                                type="textarea"
                                :rows="8"
                                placeholder='{"grant_type": "client_credentials", "refresh_token": "${getToken.refresh_token}"}'
                                @mousedown="handleInputMouseDown"
                                @mousemove="handleInputMouseMove"
                                @mouseup="handleInputMouseUp"
                                class="json-textarea"
                              />
                            </div>
                          </div>
                          <div class="field-help">
                            支持表达式引用前置请求的响应数据，请输入有效的JSON格式
                          </div>
                        </el-form-item>

                        <!-- 启用状态 -->
                        <el-form-item label="启用状态">
                          <el-switch
                            v-model="request.enabled"
                            active-text="启用"
                            inactive-text="禁用"
                            @change="handleStepChange(request)"
                          />
                          <div class="field-help">
                            禁用的步骤不会执行，但可以保留配置
                          </div>
                        </el-form-item>

                      </el-form>

                      <!-- 删除步骤按钮 -->
                      <div class="step-actions">
                        <el-button
                          size="small"
                          type="danger"
                          icon="el-icon-delete"
                          @click.stop="removeAuthRequest(index)"
                          class="delete-step-btn"
                        >
                          删除步骤
                        </el-button>
                      </div>
                    </div>
                  </div>
                </el-collapse-transition>
              </div>
            </draggable>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <i class="el-icon-connection"></i>
            <p>暂无认证步骤</p>
            <p class="empty-desc">点击上方按钮添加认证步骤，构建认证链路</p>
          </div>

          <!-- 链路流程图 -->
          <div class="chain-flow" v-if="sortedAuthRequests.length > 1">
            <h5>认证流程图</h5>
            <div class="flow-diagram">
              <div
                v-for="(request, index) in sortedAuthRequests"
                :key="request.id || request.code"
                class="flow-node"
              >
                <div class="node-content">
                  <div class="node-icon">{{ index + 1 }}</div>
                  <div class="node-info">
                    <div class="node-name">{{ request.name || request.code }}</div>
                    <div class="node-desc">{{ request.description || request.url }}</div>
                  </div>
                </div>
                <div v-if="index < sortedAuthRequests.length - 1" class="flow-arrow">
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>


        </div>
      </el-collapse-transition>
    </div>



    <!-- 测试结果对话框 -->
    <el-dialog
      title="认证链路测试结果"
      :visible.sync="testDialogVisible"
      width="90vw"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
      custom-class="connector-test-dialog"
    >
      <div v-if="testResults" class="test-result mac-content">
        <div class="result-summary mac-summary">
          <div v-if="testResults.success" class="success-indicator">
            <div class="success-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="success-text">
              <h3>认证链路测试成功</h3>
              <p>所有认证步骤执行成功，可以查看执行结果和表达式建议</p>
            </div>
          </div>
          <div v-else class="error-indicator">
            <div class="error-icon">
              <i class="el-icon-close"></i>
            </div>
            <div class="error-text">
              <h3>认证链路测试失败</h3>
              <p class="error-message">{{ testResults.errorMessage }}</p>
            </div>
          </div>
        </div>

        <div v-if="testResults.success" class="test-details mac-details">
          <h4 class="section-title">链路执行结果</h4>
          <div class="chain-execution-results mac-cards">
            <div
              v-for="(result, stepCode) in testResults.chainExecutionResults"
              :key="stepCode"
              class="step-result-card mac-card"
            >
              <div class="step-result-header mac-card-header">
                <div class="step-info">
                  <h5>{{ getStepName(stepCode) }}</h5>
                  <span class="step-code">{{ stepCode }}</span>
                </div>
                <div class="success-badge">
                  <i class="el-icon-check"></i>
                  <span>成功</span>
                </div>
              </div>
              <div class="step-result-body mac-card-body">
                <pre class="result-json mac-json">{{ JSON.stringify(result, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <!-- 表达式建议区域 -->
          <div class="expression-suggestions mac-suggestions">
            <h4 class="section-title">表达式建议</h4>
            <div class="suggestions-list mac-tags">
              <div
                v-for="expr in generateExpressionSuggestions()"
                :key="expr"
                class="suggestion-tag mac-tag"
                @click="copyExpression(expr)"
              >
                <i class="el-icon-document-copy"></i>
                <span>{{ expr }}</span>
              </div>
            </div>
            <p class="suggestions-tip mac-tip">
              <i class="el-icon-info"></i>
              点击表达式可复制到剪贴板，用于认证表达式配置
            </p>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer mac-footer">
        <el-button
          @click="testDialogVisible = false"
          class="mac-close-btn"
          size="medium"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { testHttpConnectorAuthChain, testHttpConnectorTokenExtraction, generateSnowflakeId } from '@system/api/integration/http-connector'

export default {
  name: 'HttpConnectorAuthChain',
  components: {
    draggable
  },
  props: {
    connectorId: {
      type: String,
      default: ''
    },
    config: {
      type: Object,
      default: () => ({})
    },
    authRequests: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isCollapsed: false,
      saving: false,
      testLoading: false,
      authRequestsList: [],
      testDialogVisible: false,
      testResults: null,
      expandedSteps: [],
      stepCodeChangeTimer: null, // 步骤编码输入防抖定时器
      stepRules: {
        code: [
          { required: true, message: '请输入步骤编码', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入步骤名称', trigger: 'blur' }
        ],
        method: [
          { required: true, message: '请选择请求方法', trigger: 'change' }
        ],
        url: [
          { required: true, message: '请输入请求URL', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    sortedAuthRequests() {
      return [...this.authRequestsList].sort((a, b) => (a.sort || 0) - (b.sort || 0))
    }
  },
  watch: {
    authRequests: {
      async handler(newRequests) {
        if (newRequests && Array.isArray(newRequests)) {
          // 处理每个请求，为没有有效ID的请求生成新ID
          const processedRequests = []
          for (let index = 0; index < newRequests.length; index++) {
            const request = newRequests[index]

            // 检查是否需要生成新ID
            let requestId = request.id
            if (!requestId || requestId.toString().length < 15) {
              requestId = await this.generateSnowflakeId()
            }

            const processedRequest = {
              ...request,
              sort: request.sort || index,
              id: requestId, // 使用生成的或现有的ID
              // 初始化bodyJson字段（前端编辑用）
              bodyJson: request.bodyJson || (request.body ? JSON.stringify(request.body, null, 2) : '{}'),
              // 确保必要的后端字段存在
              createBy: request.createBy || null,
              updateBy: request.updateBy || null,
              createTime: request.createTime || new Date().toISOString().replace('T', ' ').substring(0, 19),
              updateTime: request.updateTime || null,
              remark: request.remark || null,
              description: request.description || null,
              contentType: request.contentType || null,
              connectorId: request.connectorId || this.connectorId,
              type: request.type || "AUTH"
            }

            // 处理请求头数据 - 优先使用 headersPairs，保持用户输入顺序
            let headersPairs = []
            if (request.headersPairs && Array.isArray(request.headersPairs)) {
              // 如果已经有 headersPairs，直接使用，保持顺序
              headersPairs = [...request.headersPairs]
              // 从 headersPairs 重新生成 headers 对象
              const headersObj = {}
              headersPairs.forEach(pair => {
                if (pair.key && pair.key.trim()) {
                  headersObj[pair.key.trim()] = pair.value || ''
                }
              })
              processedRequest.headers = headersObj
            } else if (request.headers && typeof request.headers === 'object') {
              // 如果只有 headers 对象，转换为 headersPairs
              headersPairs = Object.entries(request.headers).map(([key, value]) => ({ key, value }))
              processedRequest.headers = request.headers
            } else if (typeof request.headers === 'string') {
              // 如果 headers 是字符串，解析为对象
              try {
                const headersObj = JSON.parse(request.headers)
                processedRequest.headers = headersObj
                headersPairs = Object.entries(headersObj).map(([key, value]) => ({ key, value }))
              } catch (e) {
                processedRequest.headers = { 'Content-Type': 'application/json' }
                headersPairs = [{ key: 'Content-Type', value: 'application/json' }]
              }
            } else {
              processedRequest.headers = { 'Content-Type': 'application/json' }
              headersPairs = [{ key: 'Content-Type', value: 'application/json' }]
            }

            // 使用 Vue.set 确保响应式
            this.$set(processedRequest, 'headersPairs', headersPairs)
            processedRequests.push(processedRequest)
          }

          this.authRequestsList = processedRequests
        } else {
          this.authRequestsList = []
        }
      },
      immediate: true,
      deep: true
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.stepCodeChangeTimer) {
      clearTimeout(this.stepCodeChangeTimer)
    }
  },
  methods: {
    // 生成雪花算法ID（调用后端API）
    async generateSnowflakeId() {
      try {
        const data = await generateSnowflakeId()
        console.log(data)
        if (data ) {
          return data.toString() // 确保返回字符串格式
        } else {
          throw new Error('生成ID失败')
        }
      } catch (error) {
        // 降级方案：使用时间戳 + 随机数生成临时ID
        const timestamp = Date.now()
        const random = Math.floor(Math.random() * 10000)
        return `${timestamp}${random}`
      }
    },

    // 切换折叠状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },

    // 获取链路状态
    getChainStatus() {
      if (this.sortedAuthRequests.length === 0) {
        return '未配置'
      }
      return `${this.sortedAuthRequests.length}个步骤`
    },

    // 获取链路状态类型
    getChainStatusType() {
      return this.sortedAuthRequests.length > 0 ? 'success' : 'warning'
    },

    // 获取步骤状态
    getStepStatus(request) {
      if (!request.enabled) {
        return '已禁用'
      }
      if (!request.url || !request.method) {
        return '未完成'
      }
      return '已配置'
    },

    // 获取步骤状态类型
    getStepStatusType(request) {
      if (!request.enabled) {
        return 'info'
      }
      if (!request.url || !request.method) {
        return 'warning'
      }
      return 'success'
    },

    // 获取方法标签类型
    getMethodTagType(method) {
      const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      }
      return typeMap[method] || 'info'
    },

    // 切换步骤展开状态
    toggleStepExpand(stepId) {
      const index = this.expandedSteps.indexOf(stepId)
      if (index > -1) {
        this.expandedSteps.splice(index, 1)
      } else {
        this.expandedSteps.push(stepId)
      }
    },

    // 添加认证请求
    async addAuthRequest() {
      const stepNumber = this.authRequestsList.length + 1
      const currentTime = new Date().toISOString().replace('T', ' ').substring(0, 19)

      // 生成雪花算法ID
      const snowflakeId = await this.generateSnowflakeId()

      const newStep = {
        // 后端需要的字段
        createBy: null,
        updateBy: null,
        createTime: currentTime,
        updateTime: null,
        id: snowflakeId, // 使用后端生成的雪花算法ID
        remark: null,
        code: `step${stepNumber}`,
        name: `认证步骤 ${stepNumber}`,
        description: null,
        contentType: null,
        url: '',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: null,
        connectorId: this.connectorId,
        type: "AUTH",
        enabled: true,

        // 前端用的字段（不会提交给后端）
        headersPairs: [
          { key: 'Content-Type', value: 'application/json' }
        ],
        bodyJson: '{}', // 前端编辑用
        sort: this.authRequestsList.length
      }

      this.authRequestsList.push(newStep)
      this.expandedSteps.push(newStep.id)
      this.handleStepChange(newStep) // 使用handleStepChange来确保数据格式正确
      this.$message.success('认证步骤添加成功')
    },



    // 处理步骤编码变化（防抖处理）
    handleStepCodeChange(request) {
      // 清除之前的定时器
      if (this.stepCodeChangeTimer) {
        clearTimeout(this.stepCodeChangeTimer)
      }

      // 设置新的定时器，延迟触发事件
      this.stepCodeChangeTimer = setTimeout(() => {
        this.handleStepChange(request)
      }, 300) // 300ms 防抖延迟
    },

    // 处理步骤变化
    handleStepChange(request) {
      // 清理数据，只保留后端需要的字段
      const cleanedAuthRequests = this.authRequestsList.map(req => {
        const cleanedRequest = {
          createBy: req.createBy || null,
          updateBy: req.updateBy || null,
          createTime: req.createTime || new Date().toISOString().replace('T', ' ').substring(0, 19),
          updateTime: req.updateTime || null,
          id: req.id,
          remark: req.remark || null,
          code: req.code,
          name: req.name,
          description: req.description || null,
          contentType: req.contentType || null,
          url: req.url,
          method: req.method,
          headers: req.headers || {},
          body: null, // 默认为null
          connectorId: req.connectorId,
          type: req.type || "AUTH",
          enabled: req.enabled
        }

        // 处理body：如果有bodyJson且不为空，则尝试解析为对象
        if (req.bodyJson && req.bodyJson.trim() && req.bodyJson.trim() !== '{}') {
          try {
            cleanedRequest.body = JSON.parse(req.bodyJson)
          } catch (e) {
            // JSON解析失败时设置为null，不发送无效的body给后端
            cleanedRequest.body = null
          }
        }

        return cleanedRequest
      })

      this.$emit('auth-requests-change', cleanedAuthRequests)
    },



    // 处理请求头键值对变化（优化逻辑）
    handleHeadersPairChange(request) {
      // 直接从 headersPairs 生成 headers 对象，不改变 headersPairs 的顺序
      const headersObj = {}
      if (request.headersPairs) {
        request.headersPairs.forEach(pair => {
          // 只要key不为空就保存，允许value为空字符串
          if (pair.key && pair.key.trim()) {
            headersObj[pair.key.trim()] = pair.value || ''
          }
        })
      }
      request.headers = headersObj
      this.handleStepChange(request)
    },





    // 添加请求头键值对（简化）
    addHeaderPair(request) {
      if (!request.headersPairs) {
        this.$set(request, 'headersPairs', [])
      }
      request.headersPairs.push({ key: '', value: '' })
      // 不需要立即调用handleHeadersPairChange，等用户输入key后会自动触发
    },

    // 移除请求头键值对
    removeHeaderPair(request, index) {
      if (request.headersPairs) {
        request.headersPairs.splice(index, 1)
        this.handleHeadersPairChange(request)
      }
    },







    // 格式化JSON
    formatJsonBody(request) {
      if (!request.bodyJson || !request.bodyJson.trim()) {
        request.bodyJson = '{}'
        this.handleStepChange(request)
        return
      }

      try {
        const parsed = JSON.parse(request.bodyJson)
        request.bodyJson = JSON.stringify(parsed, null, 2)
        this.handleStepChange(request)
        this.$message.success('JSON格式化成功')
      } catch (e) {
        this.$message.error('JSON格式错误，无法格式化')
      }
    },

    // 复制JSON内容
    async copyJsonBody(request) {
      if (!request.bodyJson || !request.bodyJson.trim()) {
        this.$message.warning('没有内容可复制')
        return
      }

      try {
        await navigator.clipboard.writeText(request.bodyJson)
        this.$message.success('JSON内容已复制到剪贴板')
      } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = request.bodyJson
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('JSON内容已复制到剪贴板')
      }
    },

    // 处理输入框鼠标事件（防止拖拽冲突）
    handleInputMouseDown(event) {
      event.stopPropagation()
    },

    handleInputMouseMove(event) {
      event.stopPropagation()
    },

    handleInputMouseUp(event) {
      event.stopPropagation()
    },

    // 处理步骤顺序变化
    handleStepOrderChange() {
      // 更新排序
      this.sortedAuthRequests.forEach((request, index) => {
        request.sort = index
      })
      this.$emit('auth-requests-change', this.sortedAuthRequests)
    },

    // 移除认证请求
    removeAuthRequest(index) {
      this.$confirm('确定要删除这个认证步骤吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.authRequestsList.splice(index, 1)
        this.$emit('auth-requests-change', this.authRequestsList)
        this.$message.success('认证步骤删除成功')
      }).catch(() => {})
    },

    // 测试链路
    async testChain() {
      if (this.sortedAuthRequests.length === 0) {
        this.$message.warning('请先添加认证步骤')
        return
      }

      this.testLoading = true
      this.$message.info('开始测试认证链路...')

      try {
        // 准备测试数据，确保格式符合 prompt_word.md 中的要求
        const testData = {
          connectorConfig: {
            type: 'HTTP',
            baseUrl: this.config.baseUrl,
            headers: this.config.headers || {},
            authExpression: this.config.authExpression,
            authLocation: this.config.authLocation,
            authParamName: this.config.authParamName
          },
          authRequests: this.sortedAuthRequests.filter(req => req.enabled)
        }

        const result = await testHttpConnectorAuthChain(testData)

        // 测试链路只返回链路执行结果，用于查看和生成表达式建议
        const processedResult = {
          success: true, // 能执行到这里说明请求成功
          chainExecutionResults: result, // result 直接就是链路执行结果
          // 测试链路不需要这些字段
          // authExpression: this.config.authExpression,
          // extractedToken: this.extractTokenFromResults(result, this.config.authExpression),
          // executionTime: new Date().toLocaleString(),
          // executionDurationMs: 0,
          // executedRequestCount: Object.keys(result || {}).length
        }

        this.testResults = processedResult
        this.testDialogVisible = true
        this.$message.success('认证链路测试成功')
      } catch (error) {
        this.$message.error(`认证链路测试失败: ${error.message || '未知错误'}`)
      } finally {
        // 确保无论成功还是失败都重置loading状态
        this.testLoading = false
      }
    },



    // 获取步骤名称
    getStepName(stepCode) {
      const step = this.authRequestsList.find(req => req.code === stepCode)
      return step ? (step.name || step.code) : stepCode
    },



    // 生成表达式建议
    generateExpressionSuggestions() {
      if (!this.testResults || !this.testResults.chainExecutionResults) return []

      const suggestions = []
      Object.keys(this.testResults.chainExecutionResults).forEach(requestCode => {
        const responseData = this.testResults.chainExecutionResults[requestCode]
        if (typeof responseData === 'object' && responseData !== null) {
          this.extractFieldPaths(responseData, requestCode, '').forEach(path => {
            suggestions.push(`\${${path}}`)
          })
        }
      })
      return suggestions.slice(0, 20) // 限制建议数量
    },

    // 递归提取字段路径
    extractFieldPaths(obj, prefix, currentPath) {
      const paths = []

      if (typeof obj !== 'object' || obj === null) {
        return paths
      }

      Object.keys(obj).forEach(key => {
        const fullPath = currentPath ? `${prefix}.${currentPath}.${key}` : `${prefix}.${key}`
        const value = obj[key]

        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // 递归处理对象
          paths.push(...this.extractFieldPaths(value, prefix, currentPath ? `${currentPath}.${key}` : key))
        } else {
          // 叶子节点
          paths.push(fullPath)
        }
      })

      return paths
    },

    // 复制表达式到剪贴板
    async copyExpression(expression) {
      try {
        await navigator.clipboard.writeText(expression)
        this.$message.success(`已复制: ${expression}`)
      } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = expression
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success(`已复制: ${expression}`)
      }
    },


  }
}
</script>

<style lang="scss" scoped>
.auth-chain-config {
  margin-bottom: 20px;

  .config-section {
    background: white;
    border-radius: 12px;
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 20px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid rgba(148, 163, 184, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
      }

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .section-title {
          font-size: 15px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }

        .status-tag {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .collapse-icon {
          font-size: 14px;
          color: #64748b;
          transition: all 0.2s ease;
        }
      }
    }

    .chain-content {
      padding: 20px;
    }
  }

  .auth-steps {
    .steps-list {
      .auth-step-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        margin-bottom: 16px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-1px);
        }

        &.ghost-card {
          opacity: 0.5;
          transform: rotate(2deg);
        }

        &.chosen-card {
          transform: scale(1.02);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          padding: 16px 20px;
          border-bottom: 1px solid #e2e8f0;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          justify-content: space-between;
          align-items: center;

          &:hover {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
          }

          .header-left {
            display: flex;
            align-items: center;
            gap: 12px;

            .step-order {
              display: flex;
              align-items: center;
              gap: 8px;

              .order-number {
                width: 28px;
                height: 28px;
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: 600;
                box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
              }

              .drag-handle {
                color: #94a3b8;
                cursor: move;
                font-size: 16px;
                transition: color 0.2s ease;

                &:hover {
                  color: #64748b;
                }
              }
            }

            .step-basic-info {
              .step-name {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
              }

              .step-meta {
                display: flex;
                align-items: center;
                gap: 8px;

                .step-url {
                  color: #64748b;
                  font-size: 12px;
                  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                  background: #f1f5f9;
                  padding: 2px 6px;
                  border-radius: 4px;
                }

                .step-method {
                  color: #059669;
                  font-size: 11px;
                  font-weight: 600;
                  background: #d1fae5;
                  padding: 2px 6px;
                  border-radius: 4px;
                }
              }
            }
          }

          .header-right {
            display: flex;
            align-items: center;
            gap: 12px;

            .expand-icon {
              color: #94a3b8;
              transition: all 0.2s ease;
              font-size: 14px;
            }
          }
        }

        .card-content {
          .step-form {
            padding: 20px;

            .form-row {
              display: flex;
              gap: 16px;
              margin-bottom: 16px;

              .form-item-half {
                flex: 1;
              }

              .form-item-full {
                flex: 1;
              }
            }

            .field-help {
              font-size: 12px;
              color: #94a3b8;
              margin-top: 4px;
              line-height: 1.4;
            }

            .url-config-row {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 8px;

              .method-selector {
                width: 120px;
                flex-shrink: 0;

                .colored-method-select {
                  &.method-get {
                    ::v-deep .el-input__inner {
                      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                      color: white;
                      border-color: #10b981;
                      font-weight: 600;
                    }
                  }

                  &.method-post {
                    ::v-deep .el-input__inner {
                      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                      color: white;
                      border-color: #3b82f6;
                      font-weight: 600;
                    }
                  }

                  &.method-put {
                    ::v-deep .el-input__inner {
                      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                      color: white;
                      border-color: #f59e0b;
                      font-weight: 600;
                    }
                  }

                  &.method-delete {
                    ::v-deep .el-input__inner {
                      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                      color: white;
                      border-color: #ef4444;
                      font-weight: 600;
                    }
                  }

                  &.method-patch {
                    ::v-deep .el-input__inner {
                      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                      color: white;
                      border-color: #8b5cf6;
                      font-weight: 600;
                    }
                  }
                }
              }

              .url-input {
                flex: 1;
              }
            }

            .map-input-container {
              .header-pairs {
                .header-pair {
                  display: flex;
                  align-items: center;
                  margin-bottom: 8px;
                  gap: 8px;

                  .pair-key {
                    width: 200px;
                    flex-shrink: 0;
                  }

                  .pair-value {
                    flex: 1;
                  }

                  .remove-btn {
                    padding: 6px 8px;
                    border-radius: 6px;
                    flex-shrink: 0;
                  }
                }
              }

              .add-pair-btn {
                color: #3b82f6;
                font-size: 13px;

                &:hover {
                  color: #2563eb;
                }
              }
            }

            .json-input-container {
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              overflow: hidden;
              background: #fafafa;

              .json-editor-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 8px;
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border-bottom: 1px solid #e2e8f0;

                .json-label {
                  font-size: 12px;
                  font-weight: 600;
                  color: #374151;
                }

                .json-actions {
                  display: flex;
                  gap: 6px;

                  .format-btn {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border: none;
                    color: white;
                    font-size: 11px;
                    padding: 4px 8px;
                    border-radius: 4px;

                    &:hover {
                      background: linear-gradient(135deg, #059669 0%, #047857 100%);
                    }
                  }

                  .copy-btn {
                    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                    border: none;
                    color: white;
                    font-size: 11px;
                    padding: 4px 8px;
                    border-radius: 4px;

                    &:hover {
                      background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                    }
                  }
                }
              }

              .json-editor-wrapper {
                position: relative;

                .json-textarea {
                  border: none;
                  background: transparent;

                  .el-textarea__inner {
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                    font-size: 13px;
                    line-height: 1.6;
                    border: none;
                    background: white;
                    padding: 12px;
                    resize: vertical;
                    min-height: 120px;
                    border-radius: 0;
                    box-shadow: none;

                    &:focus {
                      border: none;
                      box-shadow: none;
                      outline: none;
                    }
                  }

                }
              }
            }

            .step-actions {
              margin-top: 20px;
              padding-top: 16px;
              border-top: 1px solid #e2e8f0;
              display: flex;
              justify-content: flex-end;

              .delete-step-btn {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                border: none;
                color: white;
                border-radius: 8px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
                font-size: 13px;
                padding: 8px 16px;

                &:hover {
                  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
                }

                &:active {
                  transform: translateY(0);
                }
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 2px dashed #cbd5e1;

    i {
      font-size: 48px;
      color: #cbd5e1;
      margin-bottom: 16px;
    }

    p {
      margin: 8px 0;
      color: #64748b;
      font-size: 14px;

      &.empty-desc {
        font-size: 12px;
        color: #94a3b8;
      }
    }
  }

  .chain-flow {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e2e8f0;

    h5 {
      margin: 0 0 16px;
      font-size: 14px;
      font-weight: 600;
      color: #1e293b;
    }

    .flow-diagram {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 12px;

      .flow-node {
        display: flex;
        align-items: center;

        .node-content {
          display: flex;
          align-items: center;
          background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
          border-radius: 8px;
          padding: 8px 12px;
          border: 1px solid #cbd5e1;

          .node-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            margin-right: 8px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
          }

          .node-info {
            .node-name {
              font-size: 12px;
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 2px;
            }

            .node-desc {
              font-size: 10px;
              color: #64748b;
            }
          }
        }

        .flow-arrow {
          margin: 0 8px;
          color: #94a3b8;
          font-size: 16px;
        }
      }
    }
  }



  // MAC风格按钮
  .mac-style-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    &:active {
      transform: translateY(0);
    }

    &.test-chain-btn {
      background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
      box-shadow: 0 2px 8px rgba(17, 153, 142, 0.3);

      &:hover {
        background: linear-gradient(135deg, #0f8a7c 0%, #32d96b 100%);
        box-shadow: 0 4px 12px rgba(17, 153, 142, 0.4);
      }
    }

    &:disabled {
      background: #e0e6ed;
      color: #8c8c8c;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &:hover {
        background: #e0e6ed;
        transform: none;
        box-shadow: none;
      }
    }
  }

  ::v-deep .el-form-item {
    margin-bottom: 16px;

    .el-form-item__label {
      color: #374151;
      font-weight: 500;
      font-size: 13px;
    }

    .el-input {
      .el-input__inner {
        border-radius: 8px;
        border: 1px solid rgba(148, 163, 184, 0.2);
        transition: all 0.2s ease;

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }

    .el-select {
      .el-input__inner {
        border-radius: 8px;
        border: 1px solid rgba(148, 163, 184, 0.2);

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        border-radius: 8px;
        border: 1px solid rgba(148, 163, 184, 0.2);

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }

  .collapse-icon {
    transition: transform 0.3s;
    color: #94a3b8;
  }
}

// MAC风格对话框底部
.mac-footer {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  text-align: right;

  .mac-close-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    padding: 10px 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);

    &:hover {
      background: linear-gradient(135deg, #5b6470 0%, #3f4651 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// 修改蒙版颜色
::v-deep .el-dialog__wrapper {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(4px);
}

// 连接器测试对话框样式
::v-deep .connector-test-dialog {
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  margin-bottom: 2.5vh !important;

  .el-dialog {
    border-radius: 16px !important;
    margin: 0 !important;
  }

  .el-dialog__header {
    padding: 24px 24px 0;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: white;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}
</style>

<!-- 非scoped样式，用于对话框内容 -->
<style lang="scss">
// 测试结果对话框样式（非scoped，因为对话框append-to-body）
.connector-test-dialog {
  .test-result.mac-content {
    .mac-summary {
      margin-bottom: 24px;

      .success-indicator {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
        border: 1px solid #b3f5d1;
        border-radius: 12px;

        .success-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

          i {
            font-size: 24px;
            color: white;
            font-weight: bold;
          }
        }

        .success-text {
          flex: 1;

          h3 {
            margin: 0 0 8px 0;
            color: #065f46;
            font-size: 18px;
            font-weight: 600;
          }

          p {
            margin: 0 0 12px 0;
            color: #047857;
            font-size: 14px;
            line-height: 1.5;
          }

          .token-display {
            label {
              display: block;
              font-size: 14px;
              color: #047857;
              font-weight: 500;
              margin-bottom: 8px;
            }

            .mac-token {
              display: block;
              background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
              border: 1px solid #d1d5db;
              border-radius: 8px;
              padding: 12px 16px;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
              color: #374151;
              word-break: break-all;
              line-height: 1.5;
              max-height: 120px;
              overflow-y: auto;
            }
          }
        }
      }

      .error-indicator {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #fca5a5;
        border-radius: 12px;

        .error-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);

          i {
            font-size: 24px;
            color: white;
            font-weight: bold;
          }
        }

        .error-text {
          flex: 1;

          h3 {
            margin: 0 0 8px 0;
            color: #991b1b;
            font-size: 18px;
            font-weight: 600;
          }

          .error-message {
            margin: 0;
            color: #dc2626;
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }

    // MAC风格详情区域样式
    .mac-details {
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e5e7eb;
      }

      .mac-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 24px;

        .mac-stat-card {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 1px solid #e2e8f0;
          border-radius: 10px;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .stat-label {
            font-weight: 500;
            color: #64748b;
            font-size: 13px;
          }

          .stat-value {
            color: #1e293b;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            font-weight: 500;
            background: white;
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
          }
        }
      }

      .mac-cards {
        .mac-card {
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 12px;
          margin-bottom: 16px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
          }

          .mac-card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .step-info {
              h5 {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
              }

              .step-code {
                font-size: 12px;
                color: #64748b;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                background: #f1f5f9;
                padding: 2px 6px;
                border-radius: 4px;
              }
            }

            .success-badge {
              display: flex;
              align-items: center;
              gap: 6px;
              background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
              color: #166534;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 500;

              i {
                font-size: 12px;
              }
            }
          }

          .mac-card-body {
            padding: 20px;

            .mac-json {
              background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
              border: 1px solid #e2e8f0;
              border-radius: 8px;
              padding: 16px;
              font-size: 12px;
              line-height: 1.6;
              color: #374151;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              max-height: 300px;
              overflow-y: auto;
              margin: 0;
              white-space: pre-wrap;
            }
          }
        }
      }

      // 表达式建议区域样式
      .mac-suggestions {
        margin-top: 24px;
        padding: 20px;
        background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
        border: 1px solid #fbbf24;
        border-radius: 12px;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #92400e;
          margin-bottom: 16px;
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: '💡';
            font-size: 18px;
          }
        }

        .mac-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 12px;

          .mac-tag {
            background: white;
            border: 1px solid #d97706;
            color: #92400e;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            font-weight: 500;

            &:hover {
              background: #f59e0b;
              color: white;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
            }

            i {
              font-size: 12px;
            }
          }
        }

        .mac-tip {
          margin: 0;
          font-size: 12px;
          color: #92400e;
          display: flex;
          align-items: center;
          gap: 6px;

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
