<template>
  <span class="db-icon" :style="{ color: color }">
    <i :class="getIconClass"></i>
  </span>
</template>

<script>
export default {
  name: 'DbIcon',
  props: {
    type: {
      type: String,
      required: true
    },
    color: {
      type: String,
      default: '#409EFF'
    }
  },
  computed: {
    getIconClass() {
      const typeKey = this.type.toLowerCase();
      const iconMap = {
        mysql: 'el-icon-s-platform',
        oracle: 'el-icon-s-management',
        postgresql: 'el-icon-s-operation',
        sqlserver: 'el-icon-s-marketing',
        hive: 'el-icon-s-grid',
        mongodb: 'el-icon-s-opportunity',
        redis: 'el-icon-s-flag',
        elasticsearch: 'el-icon-s-finance',
        db2: 'el-icon-s-claim'
      }
      return iconMap[typeKey] || 'el-icon-s-data'
    }
  }
}
</script>

<style scoped>
.db-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(64, 158, 255, 0.1);
  transition: all 0.3s;
}

.db-icon i {
  transition: transform 0.3s;
}

.db-icon:hover i {
  transform: scale(1.1);
}
</style> 