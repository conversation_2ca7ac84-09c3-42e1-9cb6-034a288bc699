const postcss = []
let pxToConfig = {}

try {
  let files = require('glob').sync('./projects/*/postcss-px-to-plugin.config.js')
  let file = files.find(f => f.indexOf(process.env.npm_config_project_name) > -1)
  if (file) {
    pxToConfig = require(file)
  }

  if (pxToConfig.toViewport === true || typeof pxToConfig.toViewport !== 'boolean') {
    postcss.push(require('postcss-px-to-viewport-8-plugin')(Object.assign({
      viewportWidth: 1920, // 设计稿的视口宽度
      unitPrecision: 5, // 单位转换后保留的精度
      // propList: ['*', '!font-size', '!--txt_*'], // 能转化为vw的属性列表
      propList: ['*'], // 能转化为vw的属性列表
      viewportUnit: 'vw', // 希望使用的视口单位
      fontViewportUnit: 'vw', // 字体使用的视口单位
      selectorBlackList: [], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
      minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
      mediaQuery: false, // 媒体查询里的单位是否需要转换单位
      replace: true, //  是否直接更换属性值，而不添加备用属性
    }, pxToConfig.toViewportConfig || {})))
  }

  if (pxToConfig.toRem === true) {
    postcss.push(require('postcss-pxtorem')(Object.assign({
      rootValue: 19.2,
      unitPrecision: 5,
      // propList: ['font-size', '--txt_*'],
      propList: ['*'],
      minPixelValue: 1,
      selectorBlackList: [],
    }, pxToConfig.toRemConfig || {})))
  }

} catch (error) {
  pxToConfig = {}
}

module.exports = {
  plugins: [
    require('autoprefixer'),//自动添加兼容样式
    ...postcss,
  ]
}
