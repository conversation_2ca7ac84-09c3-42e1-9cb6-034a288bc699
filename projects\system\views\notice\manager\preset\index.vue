<template>
  <div class="preset-task-container">
    <!-- 顶部操作栏 -->
    <div class="action-bar">
      <div class="left">
        <el-input 
          v-model="searchKeyword" 
          placeholder="搜索任务名称" 
          prefix-icon="el-icon-search" 
          clearable
          class="search-input" 
        />
        <el-select
          v-model="selectedGroup"
          filterable
          clearable
          placeholder="按分组筛选"
          class="group-filter"
          @focus="loadGroupNames"
        >
          <el-option
            v-for="item in groupOptions"
            :key="item"
            :label="item"
            :value="item"
          >
            <i class="el-icon-folder"></i>
            <span style="margin-left: 8px">{{ item }}</span>
          </el-option>
        </el-select>
      </div>
      <div class="right">
        <el-button type="primary" icon="el-icon-plus" @click="handleCreateTask">
          新建预设任务
        </el-button>
      </div>
    </div>

    <!-- 预设任务卡片列表 -->
    <el-row :gutter="24">
      <el-col :span="6" v-for="task in filteredTasks" :key="task.id">
        <el-card class="task-card" shadow="hover">
          <div class="task-header">
            <div class="task-info">
              <div class="task-icon" :style="{ backgroundColor: getTaskColor(task.code) }">
                <i :class="getProviderIcon(task.providerId)"></i>
              </div>
              <div class="task-meta">
                <h3>{{ task.name }}</h3>
                <div class="task-tags">
                  <el-tag :type="getProviderTagType(task.providerId)" size="small" effect="dark">
                    {{ getProviderName(task.providerId) }}
                  </el-tag>
                  <el-tag :type="task.enabled ? 'success' : 'info'" size="small" class="status-tag">
                    {{ task.enabled ? '已启用' : '已禁用' }}
                  </el-tag>
                </div>
              </div>
            </div>
            <el-dropdown trigger="click" @command="handleCommand($event, task)">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">
                  <i class="el-icon-edit"></i> 编辑配置
                </el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  <i class="el-icon-delete"></i> 删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>

          <div class="task-content">
            <p class="description">{{ task.description || '暂无描述' }}</p>
            <div class="task-details">
              <div class="detail-item">
                <i class="el-icon-time"></i>
                <span>{{ formatTime(task.updateTime) }}</span>
              </div>
              <div class="detail-item">
                <i class="el-icon-connection"></i>
                <span>{{ task.code }}</span>
              </div>
            </div>
          </div>

          <div class="task-footer">
            <el-switch 
              v-model="task.enabled" 
              @change="handleToggleStatus(task)" 
              active-text="启用"
              inactive-text="禁用"
            ></el-switch>
            <div class="footer-actions">
              <el-button type="primary" size="small" @click="handleExecuteTask(task)">
                <i class="el-icon-video-play"></i>
                执行任务
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新建/编辑任务抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
      :before-close="handleDrawerClose"
    >
      <div class="drawer-content">
        <el-form 
          ref="taskForm" 
          :model="taskForm" 
          :rules="taskRules" 
          label-width="100px"
          class="task-form"
        >
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="taskForm.name" placeholder="请输入任务名称"></el-input>
          </el-form-item>
          
          <el-form-item label="任务代码" prop="code">
            <el-input v-model="taskForm.code" placeholder="请输入任务代码"></el-input>
          </el-form-item>

          <el-form-item label="消息供应商" prop="providerId">
            <el-select 
              v-model="taskForm.providerId" 
              filterable 
              placeholder="请选择消息供应商"
              style="width: 100%"
            >
              <el-option
                v-for="item in providerList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <div class="provider-option">
                  <i :class="getProviderIcon(item.id)" :style="{color: getProviderColor(item.pluginBeanName)}"></i>
                  <span>{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="接收人类型" prop="receiverType">
            <el-radio-group v-model="taskForm.receiverType">
              <el-radio label="USER">用户</el-radio>
              <el-radio label="ALL">全员通知</el-radio>
              <el-radio label="DEPT_ONLY">部门</el-radio>
              <el-radio label="DEPT">部门及以下</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="消息类型" prop="messageType">
            <el-radio-group v-model="taskForm.messageType">
              <el-radio label="CUSTOM">指定消息</el-radio>
              <el-radio label="TEMPLATE">模板消息</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="备注说明" prop="remark">
            <el-input 
              type="textarea" 
              v-model="taskForm.remark" 
              :rows="3"
              placeholder="请输入备注说明"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="drawer-footer">
          <el-button @click="handleDrawerClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { 
  getPresetTaskList, 
  createPresetTask, 
  updatePresetTask, 
  deletePresetTask,
  executePresetTask,
  getProviderList,
  getPresetTaskGroupNames
} from '@system/api/notice/manager'

export default {
  name: 'PresetTask',
  data() {
    return {
      loading: false,
      searchKeyword: '',
      selectedGroup: '', // 选中的分组
      groupOptions: [], // 分组选项
      taskList: [],
      providerList: [],
      drawerVisible: false,
      drawerType: 'create', // create or edit
      submitLoading: false,

      // 表单数据
      taskForm: {
        name: '',
        code: '',
        providerId: '',
        receiverType: 'USER',
        messageType: 'CUSTOM',
        remark: '',
        enabled: true,
        groupName: '' // 添加分组字段
      },
      
      // 表单校验规则
      taskRules: {
        name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入任务代码', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        providerId: [
          { required: true, message: '请选择消息供应商', trigger: 'change' }
        ],
        receiverType: [
          { required: true, message: '请选择接收人类型', trigger: 'change' }
        ],
        messageType: [
          { required: true, message: '请选择消息类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    filteredTasks() {
      return this.taskList.filter(task => {
        const taskName = task?.name || '';
        const searchKeyword = this.searchKeyword || '';
        const matchName = taskName.toLowerCase().includes(searchKeyword.toLowerCase());
        
        // 如果选择了分组，则需要匹配分组
        if (this.selectedGroup) {
          return matchName && task.groupName === this.selectedGroup;
        }
        return matchName;
      })
    },
    drawerTitle() {
      return this.drawerType === 'create' ? '新建预设任务' : '编辑预设任务'
    }
  },
  watch: {
    selectedGroup() {
      // 当分组选择变化时，重新获取任务列表
      this.getTaskList()
    }
  },
  mounted() {
    Promise.all([
      this.getTaskList(),
      this.getProviderList()
    ]).catch(error => {
      console.error('初始化数据失败:', error);
    });
  },
  methods: {
    // 获取预设任务列表
    getTaskList() {
      this.loading = true;
      return getPresetTaskList({size: -1, 'orders[createTime]': 'desc', 'orders[updateTime]': 'desc'})
        .then(({records}) => {
          // 确保 records 是数组，并且每个任务都有必要的属性
          this.taskList = (records || []).map(task => ({
            ...task,
            name: task.name || '',
            code: task.code || '',
            providerId: task.providerId || '',
            enabled: !!task.enabled
          }));
        })
        .catch(error => {
          console.error('获取预设任务列表失败:', error);
          this.taskList = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取供应商列表
    getProviderList() {
      getProviderList({
        size: -1 }).then(({records}) => {
        this.providerList = records || [];
      });
    },

    // 获取供应商图标
    getProviderIcon(pluginBeanName) {
      const iconMap = {
        'NoticeSmtpPlugin': 'el-icon-message',
        'NoticeAliYunSmsPlugin': 'el-icon-mobile',
        'NoticeTencentSmsPlugin': 'el-icon-mobile',
        'NoticeWebhookPlugin': 'el-icon-connection'
      };
      return iconMap[pluginBeanName] || 'el-icon-bell';
    },

    getProviderColor(pluginBeanName) {
        const colorMap = {
        'NoticeSmtpPlugin': '#409EFF',
        'NoticeAliYunSmsPlugin': '#67C23A',
        'NoticeTencentSmsPlugin': '#E6A23C',
        'NoticeWebhookPlugin': '#909399'
      }
      return colorMap[pluginBeanName] || '#409EFF'
    },


    getTaskColor(taskCode) {
        // 但providerCode每次获取到的颜色是一样的 考虑使用hash
        const colors = ['#409EFF', '#67C23A', '#E6A23C', '#909399', '#F56C6C', '#92D050', '#7FBAE6', '#F49F42', '#8EC7C9'];
        const hash = taskCode.split('').reduce((acc, char) => {
            return acc + char.charCodeAt(0)
        }, 0)
        return colors[hash % colors.length]
    },

    // 获取供应商标签类型
    getProviderTagType(pluginBeanName) {
      const typeMap = {
        'NoticeSmtpPlugin': 'primary',
        'NoticeAliYunSmsPlugin': 'success',
        'NoticeTencentSmsPlugin': 'warning',
        'NoticeWebhookPlugin': 'info'
      };
      return typeMap[pluginBeanName] || 'primary';
    },

    // 获取供应商名称
    getProviderName(providerId) {
        const provider = this.providerList.find(item => item.id === providerId)
        return provider?.name || '未知供应商'
    },

    // 格式化时间
    formatTime(time) {
      return time || '-';
    },

    // 处理下拉菜单命令
    handleCommand(command, task) {
      switch (command) {
        case 'edit':
          this.handleEditTask(task);
          break;
        case 'delete':
          this.handleDeleteTask(task);
          break;
      }
    },

    // 处理新建任务
    handleCreateTask() {
      this.$router.push({
        path: '/notice/message/push',
        query: {
          type: 'preset',
          mode: 'create',
          groupName: this.selectedGroup // 传递当前选中的分组
        }
      });
    },

    // 处理编辑任务
    handleEditTask(task) {
      this.$router.push({
        path: '/notice/message/push',
        query: {
          type: 'preset',
          mode: 'edit',
          taskId: task.id,
          groupName: task.groupName // 传递任务的分组
        }
      });
    },

    // 处理删除任务
    handleDeleteTask(task) {
      this.$confirm('确认删除该预设任务吗？', '提示', {
        type: 'warning'
      })
        .then(() => deletePresetTask(task.id))
        .then(() => {
          this.$message.success('删除成功');
          return this.getTaskList();
        })
        .catch(err => {
          if (err !== 'cancel') {
            console.error('删除任务失败:', err);
          }
        });
    },

    // 处理执行任务
    handleExecuteTask(task) {
      this.$confirm('确认执行该预设任务吗？', '提示', {
        type: 'warning'
      })
        .then(() => executePresetTask(task.code))
        .then(() => {
          this.$message.success('任务执行成功');
        })
        .catch(err => {
          if (err !== 'cancel') {
            console.error('执行任务失败:', err);
          }
        });
    },

    // 处理抽屉关闭
    handleDrawerClose() {
      this.$refs.taskForm?.resetFields();
      this.drawerVisible = false;
    },

    // 处理表单提交
    handleSubmit() {
      this.$refs.taskForm.validate(valid => {
        if (!valid) return;

        this.submitLoading = true;
        const request = this.drawerType === 'create' ? 
          createPresetTask(this.taskForm) : 
          updatePresetTask(this.taskForm);

        request.then(() => {
          this.$message.success(this.drawerType === 'create' ? '创建成功' : '更新成功');
          this.drawerVisible = false;
          return this.getTaskList();
        }).finally(() => {
          this.submitLoading = false;
        });
      });
    },

    // 加载分组名称列表
    async loadGroupNames() {
      try {
        const data  = await getPresetTaskGroupNames()
        console.log("xxx",data)
        this.groupOptions = data || []
      } catch (error) {
        console.error('获取分组列表失败:', error)
      }
    },

    // 处理切换任务状态
    handleToggleStatus(task) {
      // 实现切换任务状态的逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.preset-task-container {
  padding: 24px;
  background: inherit;

  .action-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 4px;

    .left {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .search-input {
        width: 280px;

        ::v-deep .el-input__inner {
          border-radius: 20px;
          padding-left: 40px;
        }

        ::v-deep .el-input__prefix {
          left: 15px;
        }
      }

      .group-filter {
        width: 200px;

        ::v-deep .el-input__inner {
          border-radius: 20px;
          padding-left: 15px;
        }

        ::v-deep .el-select__caret {
          margin-right: 10px;
        }

        ::v-deep .el-input__suffix {
          right: 5px;
        }

        ::v-deep .el-select__tags {
          padding-left: 10px;
        }
      }
    }

    .right {
      .el-button {
        border-radius: 20px;
        padding: 10px 24px;

        i {
          margin-right: 6px;
        }
      }
    }
  }

  .task-card {
    margin-bottom: 20px;
    border-radius: 8px;
    transition: all 0.3s;
    border: none;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    }

    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .task-info {
        display: flex;
        gap: 12px;

        .task-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
          background-color: #409EFF;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .task-meta {
          h3 {
            margin: 0 0 6px;
            font-size: 14px;
            font-weight: normal;
            color: #303133;
          }

          .task-tags {
            display: flex;
            gap: 6px;

            .el-tag {
              border-radius: 10px;
              padding: 0 8px;
              font-size: 11px;
            }
          }
        }
      }

      .el-icon-more {
        padding: 8px;
        cursor: pointer;
        color: #909399;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background-color: #f5f7fa;
          color: #606266;
        }
      }
    }

    .task-content {
      margin-bottom: 12px;

      .description {
        height: 36px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #909399;
        line-height: 1.5;
        margin: 0 0 12px;
      }

      .task-details {
        display: flex;
        gap: 16px;

        .detail-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #909399;

          i {
            font-size: 12px;
          }
        }
      }
    }

    .task-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;

      .footer-actions {
        display: flex;
        gap: 6px;
        align-items: center;

        .el-button--primary {
          padding: 6px 12px;
          font-size: 12px;
          border-radius: 20px;
          background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
          border: none;
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
          
          i {
            margin-right: 4px;
            font-size: 14px;
            vertical-align: middle;
          }
        }
      }

      ::v-deep .el-switch {
        &.is-checked {
          .el-switch__core {
            background-color: #67C23A;
            border-color: #67C23A;
          }
        }

        &:not(.is-checked) {
          .el-switch__core {
            background-color: #F56C6C;
            border-color: #F56C6C;
          }
        }
      }
    }
  }
}

.drawer-content {
  padding: 20px;
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;

  .task-form {
    flex: 1;
    overflow-y: auto;
    padding-right: 20px;

    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .drawer-footer {
    padding: 20px 0;
    text-align: right;
    border-top: 1px solid #e4e7ed;
  }
}

.provider-option {
  display: flex;
  align-items: center;
  gap: 10px;
  
  i {
    font-size: 18px;
  }
}
</style> 