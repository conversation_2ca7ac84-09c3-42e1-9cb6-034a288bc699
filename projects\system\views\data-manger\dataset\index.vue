<template>
  <div class="dataset-management">
    <el-container>
      <!-- 修改左侧树形选择区域的结构 -->
      <div class="dataset-tree-wrapper">
        <el-aside width="300px" class="dataset-tree-container" id="resizable">
          <div class="tree-header">
            <div class="header-title">
              <span>数据集列表</span>
              <el-dropdown @command="handleAdd" trigger="click">
                <el-button type="text" size="small" class="add-button">
                  <i class="el-icon-plus"></i> 新增
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="dataset">
                    <i class="el-icon-document"></i> 新增数据集
                  </el-dropdown-item>
                  <el-dropdown-item command="group">
                    <i class="el-icon-folder-add"></i> 新增文件夹
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div class="search-box">
              <el-input
                v-model="searchQuery"
                placeholder="搜索数据集"
                prefix-icon="el-icon-search"
                clearable
                size="small"
                @clear="handleSearchClear"
              />
            </div>
          </div>
          <div class="tree-container">
            <el-tree ref="datasetTree" :data="treeData" :props="defaultProps" :filter-node-method="filterNode"
              node-key="id" highlight-current @node-click="handleNodeClick" @node-expand="handleNodeExpand"
              @node-collapse="handleNodeCollapse" draggable :allow-drop="allowDrop"
              @node-drop="handleDrop">
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span class="node-content">
                  <i :class="getNodeIcon(data)"></i>
                  <span class="node-label">
                    <el-tooltip
                      :content="node.label"
                      placement="right"
                      :disabled="!isEllipsis(node.label)"
                      effect="dark"
                    >
                      <span>{{ node.label }}</span>
                    </el-tooltip>
                    <el-tooltip
                      v-if="data.type === 'dataset' && data.useCache"
                      :content="`缓存策略: ${data.cacheStrategy || '-'} | 过期: ${data.cacheExpireMinutes || '-'}分钟 | 刷新: ${data.refreshIntervalMinutes || '-'}分钟`"
                      placement="right"
                    >
                      <el-tag
                        size="mini"
                        type="success"
                        effect="plain"
                        class="node-cache-tag"
                      >
                        <i class="el-icon-box"></i>
                      </el-tag>
                    </el-tooltip>
                  </span>
                </span>
                <span class="node-actions">
                  <el-button type="text" size="mini" @click.stop="handleView(node, data)">
                    <i class="el-icon-view"></i>
                  </el-button>
                  <el-button type="text" size="mini" @click.stop="handleEdit(node, data)">
                    <i class="el-icon-edit"></i>
                  </el-button>
                  <el-button type="text" size="mini" @click.stop="handleDelete(node, data)">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </span>
              </span>
            </el-tree>
          </div>
        </el-aside>
        <!-- 添加拖拽条 -->
        <div class="resize-handle"></div>
      </div>

      <!-- 右侧内容区域 -->
      <el-container class="main-content">
        <!-- SQL编辑区域 -->
        <el-main class="sql-editor-container">
          <div class="editor-toolbar">
            <div class="toolbar-left">
              <div class="el-button-group">
                <el-button
                    v-if="currentDataset"
                    type="primary"
                    size="small"
                    :loading="saving"
                    @click="saveDataset"
                    :disabled="!hasChanges"
                >
                  <i class="el-icon-check"></i> 保存
                </el-button>
                <el-button
                    size="small"
                    @click="formatSQL"
                    :disabled="!currentDataset"
                >
                  <i class="el-icon-magic-stick"></i> 格式化
                </el-button>
                <el-button
                    size="small"
                    @click="toggleFullscreen"
                    :disabled="!currentDataset"
                >
                  <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
                  {{ isFullscreen ? '退出全屏' : '全屏编辑' }}
                </el-button>
                <el-button
                    size="small"
                    @click="showCacheDialog"
                    :disabled="!currentDataset"
                >
                  <i class="el-icon-box"></i> 缓存配置
                </el-button>
              </div>
            </div>
            <div class="toolbar-right">
              <template v-if="currentDataset && currentDataset.useCache">
                <el-tag 
                    type="success" 
                    effect="light" 
                    class="cache-tag"
                >
                  <i class="el-icon-box"></i>
                  <span class="cache-text">已启用缓存</span>
                  <el-tooltip 
                      effect="dark" 
                      placement="bottom"
                      :content="`策略: ${currentDataset.cacheStrategy || '-'} | 过期: ${currentDataset.cacheExpireMinutes || '-'}分钟 | 刷新: ${currentDataset.refreshIntervalMinutes || '-'}分钟`"
                  >
                    <i class="el-icon-info cache-info"></i>
                  </el-tooltip>
                </el-tag>
              </template>
              <el-button
                  type="primary"
                  size="small"
                  @click="runQuery"
                  :loading="isRunning"
                  :disabled="!currentDataset"
              >
                <i class="el-icon-video-play"></i> 运行查询
              </el-button>
            </div>
          </div>

          <!-- 修改参数输入区域 -->
          <div v-if="currentDataset" class="params-container">
            <div class="params-header" @click="toggleParams">
              <div class="header-left">
                <i :class="['el-icon-arrow-right', { 'is-expanded': showParams }]"></i>
                <span class="params-title">查询参数</span>
                <el-tag v-if="sqlParams.length" size="small" type="info">
                  {{ sqlParams.length }}
                </el-tag>
              </div>
              <div class="header-right">
                <el-button type="primary" size="small" icon="el-icon-plus" @click.stop="showAddParam">
                  添加参数
                </el-button>
              </div>
            </div>

            <el-collapse-transition>
              <div v-show="showParams" class="params-form-wrapper">
                <el-form :model="queryParams" class="params-form" label-width="120px">
                  <div class="params-grid">
                    <el-form-item v-for="param in allParams" :key="param" :label="param" class="param-item">
                      <div class="param-input-group">
                        <el-input :value="queryParams[param]" @input="val => handleParamInput(param, val)"
                          :placeholder="'请输入' + param" size="small" clearable />
                        <el-button type="text" class="delete-param-btn" @click.stop="deleteParam(param)">
                          <i class="el-icon-delete"></i>
                        </el-button>
                      </div>
                    </el-form-item>
                  </div>
                </el-form>
              </div>
            </el-collapse-transition>
          </div>

          <div class="editor-wrapper">
            <div v-if="!currentDataset" class="no-dataset-mask">
              <div class="no-dataset-tip">
                <i class="el-icon-edit"></i>
                <p>请选择数据集进行编辑</p>
              </div>
            </div>
            <div id="editor-container" class="code-editor"></div>
          </div>
        </el-main>

        <!-- 数据预览区域 -->
        <el-footer height="300px" style="position: relative;bottom: auto;" class="preview-container">
          <div class="preview-header">
            <div class="preview-info">
              <el-tag size="small" type="info" effect="plain" v-if="queryStats.time">
                执行耗时: {{ queryStats.time }}ms
              </el-tag>
              <el-tag size="small" type="info" effect="plain" v-if="queryStats.rows">
                返回数据: {{ queryStats.rows }}条
              </el-tag>
            </div>
            <el-button size="small" type="primary" plain icon="el-icon-refresh" @click="refreshPreview"
              :loading="isRefreshing">刷新</el-button>
          </div>
          <el-table v-loading="tableLoading" :data="previewData" border stripe height="calc(100% - 80px)"
            style="inline-size: 100%">
            <el-table-column v-for="col in tableColumns" :key="col.prop" :prop="col.prop" :label="col.label"
              :width="col.width" show-overflow-tooltip />
          </el-table>
          <div class="pagination-container" style="margin-top: 0">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page.sync="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="total" background>
            </el-pagination>
          </div>
        </el-footer>
      </el-container>
    </el-container>

    <!-- 全屏编辑器 -->
    <div class="fullscreen-mask" v-if="isFullscreen">
      <div class="fullscreen-editor">
        <div class="fullscreen-header">
          <div class="title">全屏编辑</div>
          <el-button size="small" @click="toggleFullscreen" class="close-button">
            <i class="el-icon-close"></i> 退出全屏
          </el-button>
        </div>
        <div class="fullscreen-content">
          <div ref="fullscreenEditor" class="code-editor"></div>
        </div>
      </div>
    </div>

    <!-- 添加新增数据集对话框 -->
    <el-dialog title="新增数据集" :visible.sync="addDatasetDialog" width="500px" :append-to-body="true"
      @close="handleDialogClose">
      <el-form ref="datasetForm" :model="datasetForm" :rules="datasetFormRules" label-width="100px">
        <!-- 修改新增数据集对话框中的数据源选择部分 -->
        <el-form-item label="数据源" prop="dataSourceIds">
          <el-select v-model="datasetForm.dataSourceIds" multiple filterable remote reserve-keyword placeholder="请选择数据源"
            :remote-method="remoteSearchDataSources" :loading="dataSourceLoading" style="width: 100%">
            <el-option v-for="item in dataSourceOptions" :key="item.id" :label="item.name" :value="item.id">
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ item.type }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数据集名称" prop="name">
          <el-input v-model="datasetForm.name" placeholder="请输入数据集名称"></el-input>
        </el-form-item>

        <el-form-item label="命名空间" prop="namespace">
          <el-input v-model="datasetForm.namespace" placeholder="请输入命名空间"></el-input>
        </el-form-item>

        <el-form-item label="方法ID" prop="mappedId">
          <el-input v-model="datasetForm.mappedId" placeholder="请输入方法ID"></el-input>
        </el-form-item>

        <!-- 添加所属文件夹字段 -->
        <el-form-item label="所属文件夹" prop="folderId">
          <el-select v-model="datasetForm.folderId" placeholder="请选择文件夹" filterable clearable style="width: 100%">
            <el-option v-for="item in folderOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDatasetDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveDataset">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加编辑对话框 -->
    <el-dialog :title="editDialogTitle" :visible.sync="editDialog" width="500px" :append-to-body="true"
      @close="handleEditDialogClose">
      <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入名称"></el-input>
        </el-form-item>
        <template v-if="editType === 'dataset'">
          <el-form-item label="数据源" prop="dataSourceIds">
            <el-select v-model="editForm.dataSourceIds" multiple filterable remote reserve-keyword placeholder="请选择数据源"
              :remote-method="remoteSearchDataSources" :loading="dataSourceLoading" style="width: 100%">
              <el-option v-for="item in dataSourceOptions" :key="item.id" :label="item.name" :value="item.id">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ item.type }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="命名空间" prop="namespace">
            <el-input v-model="editForm.namespace" placeholder="请输入命名空间"></el-input>
          </el-form-item>
          <el-form-item label="方法ID" prop="mappedId">
            <el-input v-model="editForm.mappedId" placeholder="请输入方法ID"></el-input>
          </el-form-item>
          <!-- 添加所属文件夹字段 -->
          <el-form-item label="所属文件夹" prop="folderId">
            <el-select v-model="editForm.folderId" placeholder="请选择文件夹" filterable clearable style="width: 100%">
              <el-option v-for="item in folderOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveEdit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加详情对话框 -->
    <el-dialog :title="viewDialogTitle" :visible.sync="viewDialog" width="600px" :append-to-body="true">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="名称">
          {{ viewForm.name }}
        </el-descriptions-item>
        <el-descriptions-item label="ID">
          {{ viewForm.id }}
        </el-descriptions-item>
        <template v-if="viewType === 'dataset'">
          <el-descriptions-item label="数据源" :span="2">
            <el-tag v-for="source in viewForm.dataSources" :key="source.id" size="small"
              style="margin-inline-end: 8px; margin-block-end: 4px;">
              {{ source.name }}
              <span style="color: #8492a6; margin-inline-start: 4px;">{{ source.type }}</span>
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="命名空间">
            {{ viewForm.namespace }}
          </el-descriptions-item>
          <el-descriptions-item label="方法ID">
            {{ viewForm.mappedId }}
          </el-descriptions-item>
          <el-descriptions-item label="SQL内容" :span="2">
            <pre class="sql-content">{{ viewForm.mappedContent }}</pre>
          </el-descriptions-item>
        </template>
        <el-descriptions-item label="创建时间">
          {{ viewForm.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ viewForm.updateTime }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 添加自定义参数对话框 -->
    <el-dialog title="添加自定义参数" :visible.sync="paramDialogVisible" width="400px" :append-to-body="true">
      <el-form ref="paramForm" :model="paramForm" :rules="paramRules" label-width="80px">
        <el-form-item label="参数名" prop="name">
          <el-input v-model="paramForm.name" placeholder="请输入参数名"></el-input>
        </el-form-item>
        <el-form-item label="默认值">
          <el-input v-model="paramForm.value" placeholder="请输入默认值"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="paramDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAddParam">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加缓存配置对话框 -->
    <el-dialog
        title="缓存配置"
        :visible.sync="cacheDialog"
        width="600px"
        :append-to-body="true"
        @close="cacheDialog = false"
        custom-class="cache-dialog"
    >
      <el-form
          ref="cacheForm"
          :model="cacheForm"
          :rules="cacheFormRules"
          label-width="120px"
          class="cache-form"
      >
        <el-form-item label="是否启用缓存" prop="useCache">
          <el-switch
              v-model="cacheForm.useCache"
              active-color="#13ce66"
              inactive-color="#ff4949"
          ></el-switch>
        </el-form-item>
        <template v-if="cacheForm.useCache">
          <el-form-item label="缓存策略" prop="cacheStrategy">
            <el-select v-model="cacheForm.cacheStrategy" placeholder="请选择缓存策略" style="width: 100%">
              <el-option
                  v-for="item in cacheStrategies"
                  :key="item.name"
                  :label="item.description"
                  :value="item.name"
              >
                <span style="float: left">{{ item.description }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item 
              label="缓存过期时间" 
              prop="cacheExpireMinutes"
          >
            <el-input-number
                v-model="cacheForm.cacheExpireMinutes"
                :min="-1"
                :max="1440"
                style="width: 160px"
                placeholder="请输入时间"
            ></el-input-number>
            <span class="form-item-tip">
              分钟（设置为-1表示永不过期）
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  <p>缓存过期时间说明：</p>
                  <p>- 设置为正数：表示经过指定分钟数后缓存失效</p>
                  <p>- 设置为-1：表示缓存永不过期</p>
                  <p>- 最大可设置1440分钟（24小时）</p>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          </el-form-item>
          <el-form-item 
              label="刷新间隔时间" 
              prop="refreshIntervalMinutes"
          >
            <el-input-number
                v-model="cacheForm.refreshIntervalMinutes"
                :min="-1"
                :max="1440"
                style="width: 160px"
                placeholder="请输入时间"
            ></el-input-number>
            <span class="form-item-tip">
              分钟（设置为-1表示不自动刷新）
              <el-tooltip effect="dark" placement="top">
                <div slot="content">
                  <p>刷新间隔时间说明：</p>
                  <p>- 设置为正数：表示每隔指定分钟数自动刷新缓存</p>
                  <p>- 设置为-1：表示不进行自动刷新</p>
                  <p>- 最大可设置1440分钟（24小时）</p>
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cacheDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveCache">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 修改编辑器相关的导入
import * as monaco from 'monaco-editor'
import { debounce } from 'lodash'

import {
  getDatasetTree,
  getDatasetDetail,
  createDataset,
  updateDataset,
  createFolder,
  executeSQL,
  deleteFolder,
  deleteDataset,
  getFolderDetail,
  updateFolder,
  getFolderList,
  getCacheStrategies  // 添加缓存策略导入
} from '@system/api/data-manger/dataset'

import { getDataSourceList } from '@system/api/data-manger/datasource'
import { parseSqlParams } from '../sql-parser'

export default {
  name: 'DatasetManagement',
  data() {
    // 添加自定义验证规则
    const validateExpireTime = (rule, value, callback) => {
      if (this.cacheForm.useCache && value === undefined) {
        callback(new Error('请输入缓存过期时间'))
      } else {
        callback()
      }
    }
    const validateRefreshTime = (rule, value, callback) => {
      if (this.cacheForm.useCache && value === undefined) {
        callback(new Error('请输入刷新间隔时间'))
      } else {
        callback()
      }
    }
    
    return {
      editor: null,
      searchQuery: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      isRunning: false,
      isRefreshing: false,
      tableLoading: false,
      previewData: [],
      tableColumns: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      queryStats: {
        time: null,
        rows: null
      },
      dialogVisible: false,
      dialogType: 'add', // 'add' 或 'edit'
      saving: false,
      datasetForm: {
        id: null,
        name: '',
        groupId: '',
        sql: ''
      },
      datasetRules: {
        name: [
          { required: true, message: '请输入数据集名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: '请选择所属分组', trigger: 'change' }
        ],
        sql: [
          { required: true, message: '请输入SQL语句', trigger: 'blur' }
        ]
      },
      groupOptions: [
        { id: '1', name: '销售分析' },
        { id: '2', name: '用户分析' },
        { id: '3', name: '库存管理' }
      ],
      groupDialogVisible: false,
      groupForm: {
        name: ''
      },
      groupRules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      savingGroup: false,
      currentDataset: null,
      isFullscreen: false,
      originalSql: '',  // 用于存储原始SQL
      hasChanges: false, // 用于标记是否有未保存的更改
      fullscreenEditor: null,  // 添加全屏编辑器实例
      addDatasetDialog: false,
      datasetForm: {
        name: '',
        namespace: '',
        mappedId: '',
        dataSourceIds: [],
        folderId: '' // 添加文件夹ID字段
      },
      datasetFormRules: {
        name: [
          { required: true, message: '请输入数据集名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        namespace: [
          { required: true, message: '请输入命名空间', trigger: 'blur' }
        ],
        mappedId: [
          { required: true, message: '请输入方法ID', trigger: 'blur' }
        ],
        dataSourceIds: [
          { required: true, message: '请选择数据源', trigger: 'change' }
        ],
        folderId: [ // 添加文件夹验证规则
          { required: true, message: '请选择所属文件夹', trigger: 'change' }
        ]
      },
      // 添加查询参数相关数据
      queryParams: {},
      sqlParams: [], // 所有参数统一使用 sqlParams
      editDialog: false,
      editType: '', // 'folder' 或 'dataset'
      editForm: {
        id: '',
        name: '',
        namespace: '',
        mappedId: '',
        folderId: '', // 添加文件夹ID字段
        dataSourceIds: []
      },
      editFormRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        namespace: [
          { required: true, message: '请输入命名空间', trigger: 'blur' }
        ],
        mappedId: [
          { required: true, message: '请输入方法ID', trigger: 'blur' }
        ],
        dataSourceIds: [
          { required: true, message: '请选择数据源', trigger: 'change' }
        ],
        folderId: [ // 添加文件夹验证规则
          { required: true, message: '请选择所属文件夹', trigger: 'change' }
        ]
      },
      viewDialog: false,
      viewType: '', // 'folder' 或 'dataset'
      viewForm: {
        id: '',
        name: '',
        namespace: '',
        mappedId: '',
        mappedContent: '',
        createTime: '',
        updateTime: '',
        dataSources: [] // 添加数据源数组
      },
      dataSourceOptions: [], // 数据源选项列表
      dataSourceLoading: false, // 数据源加载状态
      dataSourceSearchTimer: null, // 搜索防抖定时器
      lastSelectedDataSourceIds: [], // 存储上次选择的数据源ID
      paramDialogVisible: false, // 重命名对话框
      paramForm: {
        name: '',
        value: ''
      },
      paramRules: {
        name: [
          { required: true, message: '请输入参数名称', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '参数名只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' }
        ]
      },
      folderOptions: [], // 添加文件夹选项列表
      showParams: false, // 控制参数区域的展开/收起
      cacheDialog: false, // 缓存对话框
      cacheForm: {
        useCache: false,
        cacheStrategy: '',
        cacheExpireMinutes: 0,
        refreshIntervalMinutes: 0
      },
      cacheStrategies: [], // 缓存策略列表
      cacheFormRules: {
        cacheStrategy: [
          { required: true, message: '请选择缓存策略', trigger: 'change' }
        ],
        cacheExpireMinutes: [
          { required: true, message: '请输入缓存过期时间', trigger: 'blur' },
          { validator: validateExpireTime, trigger: 'blur' }
        ],
        refreshIntervalMinutes: [
          { required: true, message: '请输入刷新间隔时间', trigger: 'blur' },
          { validator: validateRefreshTime, trigger: 'blur' }
        ]
      },
      expandedKeys: [], // 添加展开节点的记录
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogType === 'add' ? '新增数据集' : '编辑数据集'
    },
    editDialogTitle() {
      const type = this.editType === 'folder' ? '文件夹' : '数据集'
      return `编辑${type}`
    },
    viewDialogTitle() {
      const type = this.viewType === 'folder' ? '文件夹' : '数据集'
      return `${type}详情`
    },
    // 获取所有参数列表
    allParams() {
      return [...this.sqlParams] // 直接返回 sqlParams
    }
  },
  watch: {
    currentDataset: {
      handler(newVal) {
        if (newVal && this.editor) {
          // 监听编辑器内容变化
          this.editor.onDidChangeModelContent(debounce(() => {
            const newSql = this.editor.getValue()
            if (newSql) {
              // 解析新的SQL参数
              const { sqlParams, queryParams } = parseSqlParams(newSql)

              // 保留已有的自定义参数值
              const existingValues = { ...this.queryParams }

              // 更新参数列表和值
              this.sqlParams = sqlParams
              this.queryParams = queryParams

              // 恢复已有参数的值
              Object.keys(existingValues).forEach(key => {
                if (this.sqlParams.includes(key)) {
                  this.$set(this.queryParams, key, existingValues[key])
                }
              })
              
              // 标记编辑器内容已修改
              this.hasChanges = this.editor.getValue() !== this.originalSql
            }
          }, 300))
        }
      },
      immediate: true
    },
    searchQuery(val) {
      this.$refs.datasetTree.filter(val)
    },
  },
  mounted() {
    // 初始化编辑器
    this.initEditor()

    // 获取数据集树
    this.fetchDatasetTree()

    // 初始化拖拽功能
    this.initResizable()

    // 监听全屏变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.dispose()
      this.editor = null
    }

    // 移除全屏变化监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  methods: {
    // 初始化Monaco编辑器
    initEditor() {
      // 确保 DOM 已经渲染
      this.$nextTick(() => {
        const container = document.getElementById('editor-container')
        if (!container) return

        // 销毁已存在的编辑器
        if (this.editor) {
          this.editor.dispose()
        }

        // 创建新的编辑器实例
        this.editor = monaco.editor.create(container, {
          value: '-- 请在此输入SQL查询语句\nSELECT * FROM users\nWHERE status = 1\nORDER BY create_time DESC;',
          language: 'sql',
          theme: 'vs-dark',
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 14,
          tabSize: 2
        })

        // 添加快捷键
        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
          this.runQuery()
        })

        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KEY_S, () => {
          if (this.currentDataset && this.hasChanges) {
            this.saveDataset()
          }
          return false
        })
      })
    },
    // 获取树形数据
    async fetchDatasetTree() {
      try {
        const data = await getDatasetTree()
        console.log('data', data)
        if (data) {
          this.treeData = this.transformTreeData(data)
          // 恢复展开的节点
          this.$nextTick(() => {
            this.expandedKeys.forEach(key => {
              this.$refs.datasetTree.store.nodesMap[key]?.expand()
            })
          })
        }
      } catch (error) {
        console.error('获取数据集列表失败:', error)
        this.$message.error('获取数据集列表失败：' + error.message)
      }
    },
    // 转换树形数据结构
    transformTreeData(data) {
      const nodes = []

      // 处理根目录下的文件夹
      if (data.subFolders) {
        data.subFolders.forEach(item => {
          if (item.folder) {
            const folderNode = {
              id: item.folder.id,
              name: item.folder.name,
              type: 'group',
              description: item.folder.description,
              parentId: item.folder.parentId,
              parentPath: item.folder.parentPath || [],
              isLeaf: false,
              children: []
            }

            // 递归处理子文件夹，只传递子文件夹的数据
            if (item.subFolders && item.subFolders.length > 0) {
              folderNode.children.push(...this.transformTreeData({
                subFolders: item.subFolders,
                statements: item.subFolders.statements
              }))
            }

            // 添加当前文件夹下的数据集
            if (item.statements) {
              item.statements.forEach(statement => {
                folderNode.children.push({
                  id: statement.id,
                  name: statement.name,
                  type: 'dataset',
                  description: statement.description,
                  folderId: statement.folderId,
                  namespace: statement.namespace,
                  mappedContent: statement.mappedContent,
                  mappedId: statement.mappedId,
                  // 添加缓存相关属性
                  useCache: statement.useCache,
                  cacheStrategy: statement.cacheStrategy,
                  cacheExpireMinutes: statement.cacheExpireMinutes,
                  refreshIntervalMinutes: statement.refreshIntervalMinutes,
                  isLeaf: true
                })
              })
            }

            nodes.push(folderNode)
          }
        })
      }

      // 处理根目录下的数据集
      if (data.statements) {
        data.statements.forEach(statement => {
          nodes.push({
            id: statement.id,
            name: statement.name,
            type: 'dataset',
            description: statement.description,
            folderId: statement.folderId,
            namespace: statement.namespace,
            mappedContent: statement.mappedContent,
            mappedId: statement.mappedId,
            // 添加缓存相关属性
            useCache: statement.useCache,
            cacheStrategy: statement.cacheStrategy,
            cacheExpireMinutes: statement.cacheExpireMinutes,
            refreshIntervalMinutes: statement.refreshIntervalMinutes,
            isLeaf: true
          })
        })
      }

      return nodes
    },
    // 获取节点图标
    getNodeIcon(data) {
      return data.type === 'group' ? 'el-icon-folder' : 'el-icon-document'
    },
    // 节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase())
    },
    // 处理节点点击
    async handleNodeClick(data, node) {
      if (data.type === 'dataset') {
        // 如果点击的是同一个数据集，不做处理
        if (this.currentDataset && this.currentDataset.id === data.id) {
          return
        }

        this.tableColumns = []
        this.previewData = []

        try {
          // 获取数据集详情
          const detail = await getDatasetDetail(data.id)

          // 更新当前数据集，包含完整的详情信息
          this.currentDataset = {
            ...data,
            sql: detail.mappedContent || '',
            dataSourceIds: detail.dataSourceIds || [],
            namespace: detail.namespace || '',
            mappedId: detail.mappedId || '',
            folderId: detail.folderId || '0',
            // 保持缓存相关字段
            useCache: detail.useCache,
            cacheStrategy: detail.cacheStrategy,
            cacheExpireMinutes: detail.cacheExpireMinutes,
            refreshIntervalMinutes: detail.refreshIntervalMinutes
          }

          // 如果有数据源ID，加载数据源选项
          if (detail.dataSourceIds && detail.dataSourceIds.length > 0) {
            await this.loadDataSourceOptions(detail.dataSourceIds)
          }

          // 高亮当前节点
          this.$refs.datasetTree.setCurrentKey(data.id)

          // 设置编辑器内容
          const sql = detail.mappedContent || ''
          if (this.editor) {
            this.editor.setValue(sql)
            this.originalSql = sql
            this.hasChanges = false
            
            // 刷新编辑器
            this.editor.layout()
          }

        } catch (error) {
          console.error('获取数据集详情失败:', error)
          this.$message.error('获取数据集详情失败：' + error.message)
        }
      } else {
        // 点击文件夹时，清除当前选中的数据集
        this.currentDataset = null
        if (this.editor) {
          this.editor.setValue('')
        }
      }
    },
    // 处理搜索清除
    handleSearchClear() {
      this.$refs.datasetTree.filter('')
    },
    // 修改运行查询方法
    async runQuery() {
      if (!this.currentDataset) {
        this.$message.warning('请选择数据集')
        return
      }

      // 如果有未保存的更改，先保存
      if (this.hasChanges) {
        try {
          await this.saveDataset()
        } catch (error) {
          this.$message.error('保存失败，无法执行查询：' + error.message)
          return
        }
      }

      this.isRunning = true
      this.tableLoading = true

      try {
        const startTime = Date.now()

        // 过滤掉空值和空白字符的参数
        const validParams = Object.entries(this.queryParams).reduce((acc, [key, value]) => {
          if (value && value.trim() !== '') {
            acc[key] = value.trim()
          }
          return acc
        }, {})

        // 传入有效的查询参数
        const data = await executeSQL(this.currentDataset.namespace, this.currentDataset.mappedId, validParams)
        const endTime = Date.now()

        // 检查响应格式
        if (!data) {
          throw new Error('未收到响应数据')
        }

        // 更新表格数据和统计信息
        if (Array.isArray(data)) {
          // 使用返回数据中的 data 数组
          const resultData = data

          if (resultData.length > 0) {
            this.tableColumns = Object.keys(resultData[0]).map(key => ({
              prop: key,
              label: key,
              sortable: true
            }))
          } else {
            this.tableColumns = []
          }

          this.previewData = resultData.map(item => {
            const formattedItem = { ...item }
            Object.keys(formattedItem).forEach(key => {
              if (typeof formattedItem[key] === 'boolean') {
                formattedItem[key] = formattedItem[key] ? 'true' : 'false'
              }
            })
            return formattedItem
          })

          this.total = resultData.length

          this.queryStats = {
            time: endTime - startTime,
            rows: resultData.length
          }

          this.$message.success('查询执行成功')
        } else {
          // 如果返回的不是数组，显示完整响应
          this.previewData = [{
            response: `<div class="error-response">${JSON.stringify(response, null, 2)}</div>`
          }]
          this.tableColumns = [{
            prop: 'response',
            label: '错误响应',
            className: 'error-column',
            // 允许HTML渲染
            render: (h, { row }) => {
              return h('div', {
                domProps: {
                  innerHTML: row.response
                }
              })
            }
          }]
          this.total = 1
          this.queryStats = {
            time: endTime - startTime,
            rows: 0
          }
          this.$message.warning('查询结果格式异常')
        }
      } catch (error) {
        console.error('查询执行失败:', error)

        // 显示错误对象的完整内容
        this.previewData = [{
          response: `${JSON.stringify({
            code: error.code || 500,
            msg: error.message || '未知错误',
            error: error.stack || error.toString()
          }, null, 2)}`
        }]
        this.tableColumns = [{
          prop: 'response',
          label: '错误响应',
          className: 'error-column',
          // 允许HTML渲染
          render: (h, { row }) => {
            return h('div', {
              domProps: {
                innerHTML: row.response
              }
            })
          }
        }]
        this.total = 1
        this.queryStats = {
          time: null,
          rows: 0
        }

        this.$message.error('查询执行失败：' + (error.message || '未知错误'))
      } finally {
        this.isRunning = false
        this.tableLoading = false
      }
    },
    // 格式化SQL
    formatSQL: debounce(function () {
      if (this.editor) {
        // 使用Monaco编辑器的格式化功能
        this.editor.getAction('editor.action.formatDocument').run()
        this.$message.success('SQL格式化成功')
      }
    }, 300),
    // 刷新预览数据
    async refreshPreview() {
      this.isRefreshing = true
      try {
        await this.runQuery() // 直接调用 runQuery 方法
        this.$message.success('数据已刷新')
      } catch (error) {
        this.$message.error('刷新失败：' + error.message)
      } finally {
        this.isRefreshing = false
      }
    },
    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.runQuery() // 直接调用 runQuery 重新获取数据
    },
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.runQuery() // 直接调用 runQuery 重新获取数据
    },
    // 获取SQL查询语句
    getValue() {
      return this.editor ? this.editor.getValue() : ''
    },
    // 设置SQL查询语句
    setValue(value) {
      if (this.editor) {
        this.editor.setValue(value || '')
      }
    },
    // 处理新增数据集
    async handleAdd(command) {
      if (command === 'dataset') {
        try {
          // 重置表单
          this.datasetForm = {
            name: '',
            namespace: '',
            mappedId: '',
            dataSourceIds: [],
            folderId: ''
          }

          // 加载数据源列表和文件夹列表
          await Promise.all([
            this.loadDefaultDataSources(),
            this.fetchFolderList()
          ])

          this.addDatasetDialog = true
        } catch (error) {
          this.$message.error('初始化新增数据集失败：' + (error.message || '未知错误'))
        }
      } else if (command === 'group') {
        this.handleAddFolder()
      }
    },

    handleEditDataset(data) {
      this.dialogType = 'edit'
      this.loadDatasetDetail(data.id)
    },

    async loadDatasetDetail(id) {
      try {
        // 从本地树形数据中查找对应的数据集
        let dataset = null
        let groupId = null

        // 遍历查找数据集和所属分组
        this.treeData.forEach(group => {
          const found = group.children.find(d => d.id === id)
          if (found) {
            dataset = found
            groupId = group.id
          }
        })

        if (dataset) {
          this.datasetForm = {
            id: dataset.id,
            name: dataset.name,
            groupId: dataset.groupId || groupId, // 使用直接的分组ID
            sql: dataset.sql
          }
          this.dialogVisible = true
        }
      } catch (error) {
        this.$message.error('获取数据集详情失败：' + error.message)
      }
    },

    async saveDataset() {
      if (!this.currentDataset) return

      this.saving = true
      try {
        const sql = this.editor ? this.editor.getValue() : ''
        // 解析SQL参数
        const { sqlParams } = parseSqlParams(sql)

        const data = {
          id: this.currentDataset.id,
          mappedContent: sql,
          sqlParams,
          namespace: this.currentDataset.namespace,
          mappedId: this.currentDataset.mappedId,
          dataSourceIds: this.currentDataset.dataSourceIds || [],
          // 保持缓存相关字段
          useCache: this.currentDataset.useCache,
          cacheStrategy: this.currentDataset.cacheStrategy,
          cacheExpireMinutes: this.currentDataset.cacheExpireMinutes,
          refreshIntervalMinutes: this.currentDataset.refreshIntervalMinutes
        }

        await updateDataset(data)

        // 检查响应是否成功
        this.$message.success('保存成功')
        this.originalSql = sql
        this.hasChanges = false

        // 刷新树形列表，但保持展开状态
        await this.fetchDatasetTree()

      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败：' + (error || '未知错误'))
      } finally {
        this.saving = false
      }
    },

    handleDialogClose() {
      this.$refs.datasetForm.resetFields()
      // 重置表单时，保留上次选择的数据源
      this.datasetForm.dataSourceIds = [...this.lastSelectedDataSourceIds]
      this.datasetForm.folderId = '' // 清空文件夹选择
    },

    async handleAddFolder() {
      try {
        const { value } = await this.$prompt('请输入文件夹名称', '新增文件夹', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^.{2,50}$/,
          inputErrorMessage: '文件夹名称长度在2-50个字符之间'
        })

        const folderData = {
          parentId: "0",  // 默认添加到根目录
          name: value,
          description: `${value}文件夹`
        }

        await createFolder(folderData)
        this.$message.success('新增文件夹成功')
        // 修改为直接刷新整个树
        await this.fetchDatasetTree()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('新增文件夹失败：' + error.message)
        }
      }
    },

    // 添加保存分组方法
    async saveGroup() {
      try {
        await this.$refs.groupForm.validate()

        // 模拟保存延迟
        this.savingGroup = true
        await new Promise(resolve => setTimeout(resolve, 800))

        // 添加新分组
        const newGroup = {
          id: Date.now().toString(),
          name: this.groupForm.name,
          type: 'group',
          children: []
        }

        // 添加到树形数据和分组选项
        this.treeData.push(newGroup)
        this.groupOptions.push({
          id: newGroup.id,
          name: newGroup.name
        })

        this.$message.success('新增文件夹成功')
        this.groupDialogVisible = false

        // 重置表单
        this.$refs.groupForm.resetFields()

      } catch (error) {
        // 表单验证失败或其他错误
        console.error(error)
      } finally {
        this.savingGroup = false
      }
    },

    // 添加新的方法
    isHighlighted(node, keyword) {
      if (!keyword) return false
      return node.label.toLowerCase().includes(keyword.toLowerCase())
    },

    // 判断是否允许拖拽放置
    allowDrop(draggingNode, dropNode, type) {
      // 如果是数据集，只允许拖到文件夹中或根目录
      if (draggingNode.data.type === 'dataset') {
        return dropNode.data.type === 'group' && type === 'inner'
      }
      // 如果是文件夹
      if (draggingNode.data.type === 'group') {
        // 不允许拖到数据集上
        if (dropNode.data.type === 'dataset') {
          return false
        }
        // 不允许拖到自己或自己的子文件夹中
        const isChild = (parent, child) => {
          if (!child.parent) return false
          if (child.parent === parent) return true
          return isChild(parent, child.parent)
        }
        if (type === 'inner' && isChild(draggingNode, dropNode)) {
          return false
        }
        // 允许拖到其他文件夹中或根目录
        return true
      }
      return false
    },

    // 处理拖拽完成事件
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      try {
        // 处理数据集的移动
        if (draggingNode.data.type === 'dataset') {
          // 获取数据集详情以获取完整信息
          const detail = await getDatasetDetail(draggingNode.data.id)
          if (!detail) {
            throw new Error('获取数据集详情失败')
          }

          const data = {
            id: draggingNode.data.id,
            name: draggingNode.data.name,
            description: draggingNode.data.description,
            namespace: detail.namespace,
            mappedId: detail.mappedId,
            mappedContent: detail.mappedContent || '',
            folderId: dropType === 'inner' ? dropNode.data.id : '0',
            dataSourceIds: detail?.dataSourceIds || []
          }

          const response = await updateDataset(data)

          // 检查响应状态
          if (!response.success) {
            throw new Error(response.msg || '移动数据集失败')
          }

          this.$message.success('移动数据集成功')
        }
        // 处理文件夹的移动
        else if (draggingNode.data.type === 'group') {
          const data = {
            id: draggingNode.data.id,
            name: draggingNode.data.name,
            description: draggingNode.data.description,
            parentId: dropType === 'inner' ? dropNode.data.id :
              dropNode.data.type === 'group' ? dropNode.data.parentId || '0' : '0'
          }

          const response = await updateFolder(data)

          // 检查响应状态
          if (!response.success) {
            throw new Error(response.msg || '移动文件夹失败')
          }

          this.$message.success('移动文件夹成功')
        }

        // 刷新树形列表
        await this.fetchDatasetTree()
      } catch (error) {
        // 显示具体的错误信息
        let errorMessage = '移动失败'
        if (error.response?.data?.msg) {
          errorMessage += `: ${error.response.data.msg}`
        } else if (error.message) {
          errorMessage += `: ${error.message}`
        }

        this.$message.error(errorMessage)

        // 刷新树形列表，恢复原状
        await this.fetchDatasetTree()

        // 如果是数据源相关错误，给出更详细的提示
        if (error.message?.includes('SqlSessionFactory')) {
          this.$message.warning('提示：请确保数据集关联的数据源配置正确')
        }
      }
    },

    // 修改状态变更处理方法
    handleStatusChange(status) {
      if (!this.currentDataset) return

      this.currentDataset.status = status
      this.$message.success(`${status === 'active' ? '启用' : '禁用'}数据集成功`)

      // TODO: 调用后端API更新状态
    },

    // 修改加载节点方法
    async loadNode(node, resolve) {
      try {
        const parentId = node.level === 0 ? 0 : node.data.id
        const data = await getDatasetTree(parentId)

        if (!data) {
          resolve([])
          return
        }

        const nodes = []

        // 如果是根节点
        if (node.level === 0) {
          // 处理根目录下的文件夹
          if (data.subFolders) {
            data.subFolders.forEach(item => {
              if (item.folder) {
                nodes.push({
                  id: item.folder.id,
                  name: item.folder.name,
                  type: 'group',
                  description: item.folder.description,
                  parentId: item.folder.parentId,
                  isLeaf: false
                })
              }
            })
          }

          // 处理根目录下的数据集
          if (data.statements) {
            data.statements.forEach(statement => {
              nodes.push({
                id: statement.id,
                name: statement.name,
                type: 'dataset',
                description: statement.description,
                mappedContent: statement.mappedContent,
                mappedId: statement.mappedId,
                folderId: statement.folderId,
                isLeaf: true
              })
            })
          }
        } else {
          // 如果是子节点，处理子文件夹下的数据集
          const currentFolder = data.subFolders.find(item => item.folder.id === parentId)
          if (currentFolder && currentFolder.statements) {
            currentFolder.statements.forEach(statement => {
              nodes.push({
                id: statement.id,
                name: statement.name,
                type: 'dataset',
                description: statement.description,
                mappedContent: statement.mappedContent,
                mappedId: statement.mappedId,
                folderId: statement.folderId,
                isLeaf: true
              })
            })
          }
        }

        resolve(nodes)
      } catch (error) {
        console.error('加载节点失败:', error)
        this.$message.error('加载数据失败：' + error.message)
        resolve([])
      }
    },

    // 切换数据集
    async switchDataset(dataset) {
      if (dataset) {
        try {
          const detail = await getDatasetDetail(dataset.id)

          const sql = detail.mappedContent || ''
          this.editor.setValue(sql)
          this.originalSql = sql

          // 更新当前数据集的 dataSourceIds
          this.currentDataset.dataSourceIds = detail.dataSourceIds || []

          // 解析SQL中的参数
          const { sqlParams, queryParams } = parseSqlParams(sql)
          this.sqlParams = sqlParams
          this.queryParams = queryParams

          this.previewData = []
          this.tableColumns = []
          this.total = 0
          this.queryStats = {
            time: null,
            rows: null
          }
        } catch (error) {
          console.error('获取数据集详情失败:', error)
          this.$message.error('获取数据集详情失败：' + error.message)
        }
      } else {
        this.editor.setValue('-- 请在此输入SQL查询语句')
        this.originalSql = ''
        this.sqlParams = []
        this.queryParams = {}
      }
      this.hasChanges = false

      this.$nextTick(() => {
        this.editor.refresh()
      })
    },

    // 修改全屏切换方法
    async toggleFullscreen() {
      if (!this.currentDataset) return

      this.isFullscreen = !this.isFullscreen

      try {
        if (this.isFullscreen) {
          // 获取编辑器容器元素
          const editorContainer = this.$el.querySelector('.sql-editor-container')

          // 请求全屏
          if (editorContainer.requestFullscreen) {
            await editorContainer.requestFullscreen()
          } else if (editorContainer.webkitRequestFullscreen) {
            await editorContainer.webkitRequestFullscreen()
          } else if (editorContainer.mozRequestFullScreen) {
            await editorContainer.mozRequestFullScreen()
          } else if (editorContainer.msRequestFullscreen) {
            await editorContainer.msRequestFullscreen()
          }

          // 调整编辑器大小
          this.$nextTick(() => {
            if (this.editor) {
              this.editor.layout()
            }
          })
        } else {
          // 退出全屏
          if (document.exitFullscreen) {
            await document.exitFullscreen()
          } else if (document.webkitExitFullscreen) {
            await document.webkitExitFullscreen()
          } else if (document.mozCancelFullScreen) {
            await document.mozCancelFullScreen()
          } else if (document.msExitFullscreen) {
            await document.msExitFullscreen()
          }

          // 恢复编辑器大小
          this.$nextTick(() => {
            if (this.editor) {
              this.editor.layout()
            }
          })
        }
      } catch (error) {
        console.error('全屏切换失败:', error)
        this.isFullscreen = !this.isFullscreen
      }
    },

    // 添加全屏变化事件监听
    mounted() {
      // ... 其他 mounted 代码

      // 监听全屏变化
      document.addEventListener('fullscreenchange', this.handleFullscreenChange)
      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
    },

    beforeDestroy() {
      // 移除全屏变化监听
      document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
    },

    // 添加全屏变化处理方法
    handleFullscreenChange() {
      const isFullscreen = document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement

      if (!isFullscreen && this.isFullscreen) {
        this.isFullscreen = false
        this.$nextTick(() => {
          this.editor.layout()
        })
      }
    },

    // 添加保存方法
    async handleSaveDataset() {
      try {
        await this.$refs.datasetForm.validate()

        const data = {
          name: this.datasetForm.name,
          namespace: this.datasetForm.namespace,
          mappedId: this.datasetForm.mappedId,
          mappedContent: '',
          folderId: this.datasetForm.folderId || '0',
          dataSourceIds: this.datasetForm.dataSourceIds
        }

        await createDataset(data)
        this.$message.success('新增数据集成功')
        this.addDatasetDialog = false

        // 保存本次选择的数据源ID
        this.lastSelectedDataSourceIds = [...this.datasetForm.dataSourceIds]

        // 刷新树形列表
        await this.fetchDatasetTree()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('新增数据集失败：' + (error.message || '未知错误'))
        }
      }
    },

    // 处理删除
    async handleDelete(node, data) {
      try {
        const confirmType = data.type === 'group' ? '文件夹' : '数据集'
        await this.$confirm(
          `此操作将永久删除该${confirmType}，是否继续？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 根据类型调用不同的删除接口
        if (data.type === 'group') {
          await deleteFolder(data.id)
        } else {
          await deleteDataset(data.id)
        }

        // 删除成功后的处理
        this.$message.success('删除成功')

        // 如果删除的是当前选中的数据集，清空编辑器
        if (this.currentDataset && this.currentDataset.id === data.id) {
          this.currentDataset = null
          this.editor.setValue('')
          this.hasChanges = false
        }

        // 刷新树形列表
        await this.fetchDatasetTree()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败：' + (error.message || '未知错误'))
        }
      }
    },

    // 处理编辑按钮点击
    async handleEdit(node, data) {
      this.editType = data.type === 'group' ? 'folder' : 'dataset'

      try {
        if (this.editType === 'dataset') {
          // 获取数据集详情
          const detail = await getDatasetDetail(data.id)
          if (detail) {
            this.editForm = {
              id: data.id,
              name: data.name,
              namespace: detail.namespace || '',
              mappedId: detail.mappedId || '',
              folderId: detail.folderId || '0',
              dataSourceIds: detail.dataSourceIds || [] // 确保设置数据源IDs
            }

            // 加载数据源选项和文件夹列表
            await Promise.all([
              this.loadDataSourceOptions(detail.dataSourceIds || []),
              this.fetchFolderList()
            ])
          }
        } else {
          // 如果是文件夹，直接使用节点数据
          this.editForm = {
            id: data.id,
            name: data.name,
            description: data.description
          }
        }

        this.editDialog = true
      } catch (error) {
        console.error('加载数据集详情失败:', error)
        this.$message.error('加载数据集详情失败：' + error.message)
      }
    },

    // 处理编辑对话框关闭
    handleEditDialogClose() {
      this.$refs.editForm.resetFields()
      this.editForm = {
        id: '',
        name: '',
        namespace: '',
        mappedId: '',
        folderId: '',
        dataSourceIds: []
      }
    },

    // 处理保存编辑
    async handleSaveEdit() {
      try {
        await this.$refs.editForm.validate()

        if (this.editType === 'dataset') {
          await updateDataset({
            id: this.editForm.id,
            name: this.editForm.name,
            namespace: this.editForm.namespace,
            mappedId: this.editForm.mappedId,
            mappedContent: this.currentDataset?.sql || '',
            folderId: this.editForm.folderId || '0',
            dataSourceIds: this.editForm.dataSourceIds
          })
        } else {
          await updateFolder({
            id: this.editForm.id,
            name: this.editForm.name,
            description: this.editForm.description
          })
        }

        this.$message.success('更新成功')
        this.editDialog = false
        await this.fetchDatasetTree()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更新失败:', error)
          this.$message.error('更新失败：' + (error.message || '未知错误'))
        }
      }
    },

    // 处理查看按钮点击
    async handleView(node, data) {
      try {
        this.viewType = data.type === 'group' ? 'folder' : 'dataset'

        let detail
        if (this.viewType === 'folder') {
          const de = await getFolderDetail(data.id)
          // 确保文件夹数据的完整性
          detail = {
            id: data.id, // 使用树节点的 id
            name: data.name,
            createTime: de?.createTime,
            updateTime: de?.updateTime
          }
        } else {
          detail = await getDatasetDetail(data.id)

          // 加载数据源详情
          if (detail.dataSourceIds && detail.dataSourceIds.length > 0) {
            const sources = await this.loadDataSourceOptions(detail.dataSourceIds)
            detail.dataSources = sources || []
          }
        }

        this.viewForm = {
          id: detail.id,
          name: detail.name,
          namespace: detail.namespace || '',
          mappedId: detail.mappedId || '',
          mappedContent: detail.mappedContent || '',
          createTime: detail.createTime || '',
          updateTime: detail.updateTime || '',
          dataSources: detail.dataSources || []
        }

        this.viewDialog = true
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败：' + error.message)
      }
    },

    // 判断文本是否溢出
    isEllipsis(text) {
      // 创建一个临时span来测量文本宽度
      const span = document.createElement('span')
      span.style.visibility = 'hidden'
      span.style.whiteSpace = 'nowrap'
      span.style.fontSize = '14px' // 与实际文本大小一致
      span.innerText = text
      document.body.appendChild(span)

      const width = span.offsetWidth
      document.body.removeChild(span)

      // 如果文本宽度超过150px(可以根据实际情况调整)，则认为会溢出
      return width > 150
    },

    // 远程搜索数据源
    async remoteSearchDataSources(query) {
      if (this.dataSourceSearchTimer) {
        clearTimeout(this.dataSourceSearchTimer)
      }

      this.dataSourceSearchTimer = setTimeout(async () => {
        this.dataSourceLoading = true
        try {
          const params = {
            current: 1,
            size: -1
          }

          // 如果有搜索关键词，添加到 enums 中
          if (query) {
            params.enums = {
              name: query
            }
          }

          const data = await getDataSourceList(params)

          if (data && data.records) {
            this.dataSourceOptions = data.records.map(item => ({
              id: item.id,
              name: item.name,
              type: item.type
            }))
          }
        } catch (error) {
          console.error('获取数据源列表失败:', error)
          this.$message.error('获取数据源列表失败：' + error.message)
        } finally {
          this.dataSourceLoading = false
        }
      }, 300)
    },

    // 添加加载数据源选项方法
    async loadDataSourceOptions(ids) {
      if (!ids || ids.length === 0) {
        await this.loadDefaultDataSources()
        return []
      }

      this.dataSourceLoading = true
      try {
        // 构造查询参数
        const params = {
          current: 1,
          size: -1,
          'enums[id]': Array.isArray(ids) ? ids.join(',') : ids

        }

        const data = await getDataSourceList(params)

        if (data && data.records) {
          const options = data.records.map(item => ({
            id: item.id,
            name: item.name,
            type: item.type
          }))

          // 更新选项列表
          this.dataSourceOptions = options
          return options
        }
        return []
      } catch (error) {
        console.error('加载数据源选项失败:', error)
        this.$message.error('加载数据源选项失败：' + error.message)
        return []
      } finally {
        this.dataSourceLoading = false
      }
    },

    // 添加加载默认数据源列表方法
    async loadDefaultDataSources() {
      this.dataSourceLoading = true
      try {
        const params = {
          current: 1,
          size: -1
        }

        const data = await getDataSourceList(params)

        if (data && data.records) {
          this.dataSourceOptions = data.records.map(item => ({
            id: item.id,
            name: item.name,
            type: item.type
          }))
        }
      } catch (error) {
        console.error('加载默认数据源列表失败:', error)
        this.$message.error('加载数据源列表失败：' + error.message)
      } finally {
        this.dataSourceLoading = false
      }
    },

    // 添加拖拽调整宽度功能
    initResizable() {
      this.$nextTick(() => {
        const resizable = document.getElementById('resizable')
        const handle = document.querySelector('.resize-handle')

        if (resizable && handle) {
          let startX, startWidth

          handle.addEventListener('mousedown', function (e) {
            startX = e.clientX
            startWidth = parseInt(document.defaultView.getComputedStyle(resizable).width, 10)
            document.documentElement.addEventListener('mousemove', doDrag, false)
            document.documentElement.addEventListener('mouseup', stopDrag, false)
          })

          function doDrag(e) {
            const newWidth = startWidth + e.clientX - startX
            if (newWidth > 200 && newWidth < 600) {
              resizable.style.width = newWidth + 'px'
            }
          }

          function stopDrag() {
            document.documentElement.removeEventListener('mousemove', doDrag, false)
            document.documentElement.removeEventListener('mouseup', stopDrag, false)
          }
        }
      })
    },

    // 显示添加参数对话框
    showAddParam() {
      this.paramForm = {
        name: '',
        value: ''
      }
      this.paramDialogVisible = true
    },

    // 处理添加参数
    async handleAddParam() {
      try {
        await this.$refs.paramForm.validate()

        const paramName = this.paramForm.name
        // 检查参数名是否已存在
        if (this.sqlParams.includes(paramName)) {
          this.$message.warning('参数名已存在')
          return
        }

        // 添加新参数
        this.sqlParams.push(paramName)
        this.$set(this.queryParams, paramName, this.paramForm.value || '')

        // 重置表单
        this.paramForm = {
          name: '',
          value: ''
        }

        this.paramDialogVisible = false
        this.$message.success('添加参数成功')

        // 确保展开参数区域
        this.showParams = true
      } catch (error) {
        console.error('添加参数失败:', error)
      }
    },

    // 删除参数
    deleteParam(param) {
      this.$confirm('确认删除该参数吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从参数列表中移除
        this.sqlParams = this.sqlParams.filter(p => p !== param)
        // 删除对应的值
        this.$delete(this.queryParams, param)
        this.$message.success('参数已删除')
      }).catch(() => {
      })
    },

    // 处理参数输入
    handleParamInput(param, value) {
      this.$set(this.queryParams, param, value)
    },

    // 获取文件夹列表
    async fetchFolderList() {
      try {
        const data = await getFolderList()
        // 确保 data 是数组
        if (Array.isArray(data)) {
          this.folderOptions = data.map(item => ({
            id: item.id,
            name: item.name
          }))
        } else if (data && Array.isArray(data.records)) {
          // 如果返回的是分页数据结构
          this.folderOptions = data.records.map(item => ({
            id: item.id,
            name: item.name
          }))
        } else {
          this.folderOptions = [] // 设置为空数组
          console.warn('文件夹数据格式不正确:', data)
        }
      } catch (error) {
        console.error('获取文件夹列表失败:', error)
        this.$message.error('获取文件夹列表失败：' + error.message)
        this.folderOptions = [] // 出错时设置为空数组
      }
    },

    // 切换参数区域的展开/收起状态
    toggleParams() {
      this.showParams = !this.showParams
    },

    // 处理缓存策略变化
    handleCacheStrategyChange() {
      // 在这里添加处理缓存策略变化的逻辑
      console.log('缓存策略变化:', this.cacheForm.cacheStrategy)
    },

    // 验证缓存过期时间
    validateCacheExpireTime() {
      // 在这里添加验证缓存过期时间的逻辑
      console.log('缓存过期时间:', this.cacheForm.cacheExpireMinutes)
    },

    // 验证刷新间隔时间
    validateCacheRefreshTime() {
      // 在这里添加验证刷新间隔时间的逻辑
      console.log('刷新间隔时间:', this.cacheForm.refreshIntervalMinutes)
    },

    // 处理保存缓存策略
    async handleSaveCache() {
      // 在这里添加保存缓存策略的逻辑
      console.log('保存缓存策略:', this.cacheForm)
      this.cacheDialog = false
    },

    // 显示缓存配置对话框
    showCacheDialog() {
      this.cacheDialog = true
    },

    // 添加缓存相关方法
    async showCacheDialog() {
      if (!this.currentDataset) {
        this.$message.warning('请先选择数据集')
        return
      }

      try {
        // 获取缓存策略列表
        this.cacheStrategies = await getCacheStrategies()

        // 设置当前数据集的缓存配置
        this.cacheForm = {
          useCache: this.currentDataset.useCache || false,
          cacheStrategy: this.currentDataset.cacheStrategy || '',
          cacheExpireMinutes: this.currentDataset.cacheExpireMinutes || 0,
          refreshIntervalMinutes: this.currentDataset.refreshIntervalMinutes || 0
        }

        this.cacheDialog = true
      } catch (error) {
        console.error('获取缓存策略失败:', error)
        this.$message.error('获取缓存策略失败：' + error.message)
      }
    },

    async handleSaveCache() {
      if (this.cacheForm.useCache) {
        await this.$refs.cacheForm.validate()
      }
      
      // 更新数据集的缓存配置
      const data = {
        id: this.currentDataset.id,
        name: this.currentDataset.name,
        namespace: this.currentDataset.namespace,
        mappedId: this.currentDataset.mappedId,
        mappedContent: this.currentDataset.mappedContent,
        folderId: this.currentDataset.folderId,
        dataSourceIds: this.currentDataset.dataSourceIds,
        // 添加缓存配置
        useCache: this.cacheForm.useCache,
        cacheStrategy: this.cacheForm.useCache ? this.cacheForm.cacheStrategy : null,
        cacheExpireMinutes: this.cacheForm.useCache ? this.cacheForm.cacheExpireMinutes : null,
        refreshIntervalMinutes: this.cacheForm.useCache ? this.cacheForm.refreshIntervalMinutes : null
      }

      await updateDataset(data)
      
      // 更新当前数据集的缓存配置
      Object.assign(this.currentDataset, {
        useCache: this.cacheForm.useCache,
        cacheStrategy: this.cacheForm.useCache ? this.cacheForm.cacheStrategy : null,
        cacheExpireMinutes: this.cacheForm.useCache ? this.cacheForm.cacheExpireMinutes : null,
        refreshIntervalMinutes: this.cacheForm.useCache ? this.cacheForm.refreshIntervalMinutes : null
      })

      // 刷新树形数据
      await this.fetchDatasetTree()
      
      this.$message.success('缓存配置已更新')
      this.cacheDialog = false
    },

    // 修改树节点渲染方法，添加缓存标识
    getNodeIcon(data) {
      return data.type === 'group' ? 'el-icon-folder' : 'el-icon-document'
    },

    // 修改节点渲染方法
    renderContent(h, { node, data, store }) {
      return h('span', {
        class: 'custom-tree-node'
      }, [
        h('span', {
          class: 'node-content'
        }, [
          h('i', {
            class: this.getNodeIcon(data)
          }),
          h('el-tooltip', {
            props: {
              content: node.label,
              placement: 'right',
              disabled: !this.isEllipsis(node.label)
            }
          }, [
            h('span', {
              class: {
                'node-label': true,
                'highlight': this.isHighlighted(node, this.searchQuery)
              }
            }, [
              node.label,
              data.type === 'dataset' && data.useCache ? h('el-tag', {
                props: {
                  size: 'mini',
                  type: 'success',
                  effect: 'plain'
                },
                class: 'node-cache-tag',
                attrs: {
                  title: `缓存策略: ${data.cacheStrategy || '-'} | 过期: ${data.cacheExpireMinutes || '-'}分钟 | 刷新: ${data.refreshIntervalMinutes || '-'}分钟`
                }
              }, [
                h('i', {
                  class: 'el-icon-box'
                })
              ]) : null
            ])
          ])
        ]),
        h('span', {
          class: 'node-actions'
        }, [
          h('el-button', {
            props: {
              type: 'text',
              size: 'mini'
            },
            on: {
              click: (e) => {
                e.stopPropagation()
                this.handleView(node, data)
              }
            }
          }, [
            h('i', {
              class: 'el-icon-view'
            })
          ]),
          h('el-button', {
            props: {
              type: 'text',
              size: 'mini'
            },
            on: {
              click: (e) => {
                e.stopPropagation()
                this.handleEdit(node, data)
              }
            }
          }, [
            h('i', {
              class: 'el-icon-edit'
            })
          ]),
          h('el-button', {
            props: {
              type: 'text',
              size: 'mini'
            },
            on: {
              click: (e) => {
                e.stopPropagation()
                this.handleDelete(node, data)
              }
            }
          }, [
            h('i', {
              class: 'el-icon-delete'
            })
          ])
        ])
      ])
    },

    // 添加记录展开节点的方法
    handleNodeExpand(data) {
      this.expandedKeys.push(data.id)
    },

    // 添加记录收起节点的方法
    handleNodeCollapse(data) {
      const index = this.expandedKeys.indexOf(data.id)
      if (index > -1) {
        this.expandedKeys.splice(index, 1)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.dataset-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  overflow: hidden;

  .dataset-tree-wrapper {
    width: 300px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

    .dataset-tree-container {
      width: 100% !important;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding: 0;

      .tree-header {
        padding: 20px;
        border-bottom: 1px solid #eef1f7;
        background: linear-gradient(to right, #fcfcfd, #f9fafc);
        flex-shrink: 0;

        .header-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          span {
            font-size: 18px;
            font-weight: 600;
            color: #1a1f36;
            letter-spacing: 0.5px;
            position: relative;
            padding-left: 12px;

            &:before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 3px;
              height: 16px;
              background: linear-gradient(to bottom, #409EFF, #64B5F6);
              border-radius: 3px;
            }
          }

          .add-button {
            padding: 0;
            font-size: 13px;
            color: #409EFF;
            
            i {
              margin-right: 4px;
              font-size: 14px;
              color: inherit;
              
              &.el-icon--right {
                margin-right: 0;
                margin-left: 4px;
              }
            }

            &:hover {
              color: #66b1ff;
              transform: translateY(-1px);
              
              i {
                color: #66b1ff;
              }
            }
          }
        }

        .search-box {
          .el-input {
            width: 100%;
            ::v-deep .el-input__inner {
              border-radius: 10px;
              height: 36px;
              background: #f7f9fc;
              border: 1px solid #e7ebf3;
              
              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
              
              &:hover {
                border-color: #c0d0e9;
              }
            }
            
            ::v-deep .el-input__prefix {
              left: 10px;
              i {
                color: #8492a6;
              }
            }
          }
        }
      }

      .tree-container {
        flex: 1;
        overflow: auto;
        padding: 16px;
        background-color: #fff;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background: #dcdfe6;
          border-radius: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f5f7fa;
          border-radius: 6px;
        }

        ::v-deep {
          .el-tree {
            background: transparent;
          }

          .el-tree-node__content {
            height: 32px;
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.2s ease;
            
            &:hover {
              background-color: #f5f7fa;
            }

            .custom-tree-node {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding-right: 8px;
              
              .node-content {
                display: flex;
                align-items: center;
                gap: 6px;

                i {
                  font-size: 14px;
                  color: #409EFF;
                  flex-shrink: 0;
                }

                span {
                  color: #606266;
                  text-align: left;
                }
              }
            }
          }
          
          .el-tree-node.is-current > .el-tree-node__content {
            background-color: #ecf5ff !important;
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
            
            .custom-tree-node {
              .node-content {
                span {
                  color: #409EFF;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
  }

  .el-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0;
    height: 100%;

    .info-section {
      background-color: #fff;
      border-radius: 16px;
      padding: 16px;
      box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

      &:hover {
        box-shadow: 0 6px 24px rgba(31, 45, 61, 0.1);
      }

      &:first-child {
        flex-shrink: 0;
      }

      &:last-child {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        font-size: 16px;
        font-weight: 600;
        color: #1a1f36;
        padding-left: 12px;
        border-left: 3px solid #409EFF;
        letter-spacing: 0.5px;

        i {
          margin-right: 8px;
          color: #409EFF;
        }

        .member-count {
          font-size: 13px;
          color: #909399;
          margin-left: 8px;
          font-weight: normal;
        }
      }

      .subtitle {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        color: #909399;
        font-size: 14px;
        margin-bottom: 8px;

        .info-item {
          label {
            color: #606266;
            margin-right: 8px;
            font-weight: 500;
          }
        }
      }

      ::v-deep .el-table {
        &.custom-table {
          border-radius: 12px;
          overflow: hidden;
          margin-top: 12px;
          
          &.el-table--border {
            border-radius: 12px;
            overflow: hidden;
          }

          th {
            background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
            font-weight: 600;
            color: #1a1f36;
            height: 40px;
            padding: 8px 0;
          }

          td {
            padding: 6px 0;
          }
          
          .el-table__body-wrapper::-webkit-scrollbar {
            width: 4px;
            height: 4px;
          }
          
          .el-table__body-wrapper::-webkit-scrollbar-thumb {
            background: rgba(144, 147, 153, 0.3);
            border-radius: 4px;
            
            &:hover {
              background: rgba(144, 147, 153, 0.5);
            }
          }
          
          .el-table__body-wrapper::-webkit-scrollbar-track {
            background: transparent;
          }
        }
      }

      .pagination-container {
        padding: 16px 0 8px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

.resize-handle {
  inline-size: 4px;
  block-size: calc(100% - 32px);
  background-color: transparent;
  cursor: col-resize;
  position: absolute;
  inset-inline-end: -2px;
  inset-block-start: 16px;
  z-index: 100;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(64, 158, 255, 0.5);
  }

  &:active {
    background-color: #409EFF;
  }
}

.main-content {
  flex-direction: column;
  block-size: 100%;
  background-color: #f5f7fa;
  margin-left: 16px;
}

.sql-editor-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  margin: 0;
  overflow: hidden;

  &.fullscreen {
    display: none;
  }

  .editor-toolbar {
    padding: 20px;
    border-bottom: 1px solid #eef1f7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    border-radius: 16px 16px 0 0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .el-button-group {
        .el-button {
          &.el-button--primary {
            &:not(.is-disabled) {
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
              }
            }
          }
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .shortcut-tip {
        color: #909399;
        font-size: 13px;
      }

      .el-button {
        margin-left: 8px;

        &.el-button--primary {
          &:not(.is-disabled) {
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 6px rgba(64, 158, 255, 0.25);
            }
          }
        }
      }
    }
  }

  .editor-wrapper {
    flex: 1;
    background-color: #fff;
    position: relative;

    .code-editor {
      block-size: 100%;
      min-block-size: 300px;
      border: 1px solid #e7ebf3;
      border-radius: 10px;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        border-color: #c0d0e9;
      }

      &:focus-within {
        border-color: #409EFF;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
      }
    }
  }
}

.preview-container {
  border-block-start: 1px solid #eef1f7;
  padding: 20px;
  block-size: 300px !important;
  background-color: #fff;
  border-radius: 16px;
  margin: 16px 0 0;
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .preview-info {
      display: flex;
      gap: 12px;

      .el-tag {
        padding: 0 10px;
        block-size: 24px;
        line-height: 22px;
        border-radius: 12px;
      }
    }
  }

  .el-table {
    border-radius: 12px;
    overflow: hidden;

    ::v-deep {
      th.el-table__cell {
        background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
        color: #1a1f36;
        font-weight: 600;
        height: 40px;
        padding: 8px 0;
      }

      .el-table__row {
        transition: background-color 0.3s;

        &:hover>td {
          background-color: #f5f7fa;
        }
      }
    }
  }

  .pagination-container {
    padding: 16px 0 0;
    text-align: end;
  }
}

::v-deep .el-tree-node {

  &.is-current>.el-tree-node__content {
    background-color: #ecf5ff;

    .node-actions {
      opacity: 1;
    }
  }
}

::v-deep .el-tree-node__content {
  block-size: auto;
  min-block-size: 32px;
  padding: 4px 0;
}

.editor-toolbar {
  .toolbar-left {
    display: flex;
    align-items: center;

    .el-switch {
      ::v-deep .el-switch__label {
        color: #909399;
        font-size: 13px;

        &.is-active {
          color: #409EFF;
        }
      }
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
  }
}

.fullscreen-button {
  &:hover {
    color: #409EFF;
    background-color: #ecf5ff;
  }
}

.fullscreen-mask {
  position: fixed;
  inset-block-start: 0;
  inset-inline-start: 0;
  inset-inline-end: 0;
  inset-block-end: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 2999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .fullscreen-editor {
    background: #fff;
    inline-size: 100%;
    block-size: 100%;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    .fullscreen-header {
      padding: 16px 24px;
      border-block-end: 1px solid #e6e6e6;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f8f9fa;
      border-radius: 8px 8px 0 0;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .close-button {
        font-size: 14px;
        padding: 8px 16px;

        &:hover {
          color: #409EFF;
          background: #ecf5ff;
          border-radius: 4px;
        }
      }
    }

    .fullscreen-content {
      flex: 1;
      padding: 24px;
      overflow: hidden;

      .code-editor {
        block-size: 100%;

        ::v-deep .CodeMirror {
          block-size: 100%;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
        }
      }
    }
  }
}

.editor-wrapper {
  position: relative;

  .no-dataset-mask {
    position: absolute;
    inset-block-start: 0;
    inset-inline-start: 0;
    inset-inline-end: 0;
    inset-block-end: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 1999;
    display: flex;
    align-items: center;
    justify-content: center;

    .no-dataset-tip {
      text-align: center;
      color: #909399;

      i {
        font-size: 48px;
        margin-block-end: 16px;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
}

.toolbar-right {
  .el-button {
    margin-inline-start: 8px;

    &.is-disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
}

.dialog-footer {
  text-align: end;
}

.params-container {
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .params-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;

    &:hover {
      background-color: #f0f2f5;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 14px;
        transition: transform 0.3s;
        color: #909399;

        &.is-expanded {
          transform: rotate(90deg);
        }
      }

      .params-title {
        font-size: 14px;
        font-weight: 500;
        color: #606266;
      }

      .el-tag {
        margin-left: 8px;
      }
    }
  }

  .params-form-wrapper {
    padding: 16px;

    .params-form {
      .params-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 16px;

        .param-item {
          margin-bottom: 0;

          :deep(.el-form-item__label) {
            color: #606266;
            padding-right: 12px;
            line-height: 32px;
          }

          :deep(.el-form-item__content) {
            display: flex;

            .param-input-group {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;

              .el-input {
                flex: 1;

                .el-input__inner {
                  height: 32px;
                  line-height: 32px;
                }
              }

              .delete-param-btn {
                flex-shrink: 0;
                width: 32px;
                height: 32px;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                transition: all 0.3s;

                i {
                  font-size: 14px;
                  color: #909399;
                  transition: color 0.3s;
                }

                &:hover {
                  background-color: #f56c6c;
                  border-color: #f56c6c;

                  i {
                    color: #fff;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  width: 100%;
  min-width: 0; // 添加这行以确保flex子项可以正确收缩

  .node-content {
    flex: 1;
    display: flex;
    align-items: center;
    min-width: 0; // 确保内容可以收缩
    margin-right: 8px;
    gap: 8px; // 使用gap替代margin，更好控制间距

    i {
      flex-shrink: 0; // 防止图标被压缩
      font-size: 14px;
      color: #409EFF;
    }

    .node-label {
      flex: 1;
      display: flex;
      align-items: center;
      min-width: 0; // 确保文本可以收缩
      
      > span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 0; // 确保文本可以收缩
      }

      .node-cache-tag {
        flex-shrink: 0; // 防止缓存标签被压缩
        margin-left: 8px;
        padding: 0 4px;
        height: 18px;
        line-height: 16px;
        background-color: #f0f9eb !important;
        border-color: #e1f3d8 !important;
        color: #67c23a !important;
        
        i {
          font-size: 12px;
          margin-right: 0;
          color: #67c23a;
        }
        
        &:hover {
          background-color: #f5f9f1 !important;
          border-color: #e8f3e0 !important;
        }
      }
    }
  }

  .node-actions {
    flex-shrink: 0; // 防止操作按钮被压缩
    opacity: 0;
    transition: opacity 0.3s;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px; // 与内容保持间距

    .el-button {
      padding: 2px 4px;
      margin: 0;

      i {
        font-size: 14px; // 统一图标大小
      }

      &:hover {
        i.el-icon-view {
          color: #409EFF;
        }

        i.el-icon-edit {
          color: #409EFF;
        }

        i.el-icon-delete {
          color: #f56c6c;
        }
      }
    }
  }
}

// 优化树节点的基础样式
::v-deep .el-tree-node__content {
  height: auto;
  min-height: 32px;
  padding: 4px 0;
  
  &:hover {
    .node-actions {
      opacity: 1;
    }
  }
}

// 优化树容器的滚动条样式
.tree-container {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 6px;
    
    &:hover {
      background: #c0c4cc;
    }
  }
  
  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 6px;
  }
}

.sql-content {
  margin: 0;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-block-size: 300px;
  overflow-y: auto;
}

::v-deep .el-descriptions {
  .el-descriptions-item__label {
    inline-size: 100px;
    background-color: #f5f7fa;
  }

  .el-descriptions-item__content {
    word-break: break-all;
  }

}

::v-deep .el-tooltip__popper {
  max-inline-size: 300px;
  word-break: break-all;
}

.el-select {
  ::v-deep .el-select__tags {
    max-inline-size: calc(100% - 30px);
  }
}

// 修改新增数据集对话框样式
::v-deep .el-dialog {
  border-radius: 16px;
  overflow: hidden;

  .el-dialog__header {
    padding: 20px 24px;
    border-bottom: 1px solid #eef1f7;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    margin: 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: #fff;

    .el-form {
      .el-form-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          color: #606266;
          font-weight: 500;
          padding-right: 16px;
        }

        .el-input,
        .el-select {
          width: 100%;
        }

        .el-select {
          ::v-deep .el-select__tags {
            max-width: calc(100% - 30px);
          }
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #eef1f7;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .dialog-footer {
      .el-button {
        padding: 8px 20px;

        &+.el-button {
          margin-left: 12px;
        }
      }
    }
  }
}

// 数据源选项样式优化
::v-deep .el-select-dropdown__item {
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span:first-child {
    color: #606266;
  }

  span:last-child {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
  }

  &.selected {
    color: #409EFF;
    font-weight: 500;

    span:last-child {
      color: #409EFF;
    }
  }

  &:hover {
    background-color: #f5f7fa;
  }
}

// 优化表单验证提示样式
::v-deep .el-form-item.is-error {
  .el-input__inner,
  .el-select .el-input__inner {
    border-color: #f56c6c;

    &:focus {
      box-shadow: 0 0 0 3px rgba(245, 108, 108, 0.15);
    }
  }

  .el-form-item__error {
    padding-top: 4px;
    font-size: 12px;
  }
}

.cache-dialog {
  .el-dialog__body {
    padding: 20px 30px;
  }
  
  .cache-form {
    .el-form-item {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .el-row {
      margin-bottom: -20px;
      
      .el-col {
        margin-bottom: 20px;
      }
    }
    
    .form-item-tip {
      margin-left: 8px;
      color: #909399;
      font-size: 13px;
      white-space: nowrap;
      
      .el-icon-question {
        margin-left: 4px;
        cursor: pointer;
        color: #909399;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }
}

.cache-tag {
  background-color: #f0f9eb !important;
  border-color: #e1f3d8 !important;
  color: #67c23a !important;
  padding: 4px 8px;
  margin-right: 12px;
  height: 28px;
  line-height: 20px;
  
  .el-icon-box {
    margin-right: 4px;
  }
  
  .cache-text {
    margin-right: 4px;
  }
  
  .cache-info {
    cursor: pointer;
    color: #909399;
    
    &:hover {
      color: #67c23a;
    }
  }
}

.node-cache-tag {
  margin-left: 8px;
  padding: 0 4px;
  height: 18px;
  line-height: 16px;
  background-color: #f0f9eb !important;
  border-color: #e1f3d8 !important;
  color: #67c23a !important;
  
  i {
    font-size: 12px;
    margin-right: 0;
  }
  
  &:hover {
    background-color: #f5f9f1 !important;
    border-color: #e8f3e0 !important;
  }
}

::v-deep .el-tooltip__popper {
  max-width: 300px;
  line-height: 1.6;
  
  p {
    margin: 0;
    padding: 2px 0;
    
    &:first-child {
      font-weight: bold;
      margin-bottom: 4px;
    }
  }
}

.custom-tree-node {
  .node-label {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }
}
</style>