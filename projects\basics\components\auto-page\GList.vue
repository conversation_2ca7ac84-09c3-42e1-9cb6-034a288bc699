<template>
    <div style="width: 100%;">
        <div class="box_line" v-for="(item, index) in valueInput" :key="index">
            <input :disabled="disabled" v-model="item.value" placeholder="输入内容" style="width: 70%;" class="el-input__inner" />
            <div class="btns" v-if="!disabled">
                <div v-show="index == valueInput.length - 1 && valueInput.length != max || valueInput.length == 1" class="btn el-icon-plus" @click="addValue"></div>
                <div v-show="valueInput.length != 1" class="btn el-icon-minus" @click="deleValue(index)"></div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "GList",
        data() {
            return {
                valueInput: [],
            }
        },
        model: {
            props: 'value',
            event: 'value'
        },
        props: {
            disabled: {
                type: Boolean,
                default: false
            },
            value: {
                type: Object,
                default: () => {
                    return []
                }
            },
            max: {
                default: 3,
                type: Number
            }
        },
        computed: {
            values() {
                if (!this.value || this.value.length == 0) {
                    return [{value: ''}]
                } else {
                    return this.value.map(i => {
                        return {value: i}
                    })
                }
            }
        },
        watch: {
            values: {
                handler(val) {
                    this.valueInput = this.values
                },
                immediate: true,
                deep: true
            },
            valueInput: {
                handler(val) {
                    this.setUrl(val)
                },
                immediate: true,
                deep: true
            }
        },
        methods: {
            setUrl(val) {
                let urls = this.valueInput.map(i => i.value)
                this.$emit('value', urls)
            },
            addValue() {
                if (this.valueInput.length < this.max) {
                    this.valueInput.push({value: ''})
                    this.$forceUpdate()
                }
            },
            deleValue(index) {
                if (this.valueInput.length == 1) {
                    this.valueInput[0].value = ''
                } else {
                    this.valueInput.splice(index, 1)
                }
                this.$forceUpdate()
            }
        }
    }
</script>

<style lang="scss" scoped>
.box_line {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
.btn {
    width: 30px;
    height: 30px;
    font-size: 18px;
    border: 1px solid #ccc;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 15px;
    cursor: pointer;
    &:hover {
        background: #ccc;
        color: #fff;
    }
}
.btns {
    display: flex;
    flex-shrink: 0;
}
</style>