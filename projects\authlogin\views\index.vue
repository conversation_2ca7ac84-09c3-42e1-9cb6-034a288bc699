<template>
  <div class="login-container">
    <!-- 添加动态背景 -->
    <div class="animated-background">
      <div class="wave-group">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
      </div>
      <div class="floating-shapes">
        <div class="shape circle"></div>
        <div class="shape square"></div>
        <div class="shape triangle"></div>
      </div>
    </div>
    <loginUI @loginComplete="loginComplete" @loginError="loginError"></loginUI>
  </div>
</template>

<script>

import loginUI from '../components/LoginUI.vue';

export default {
  components: { loginUI },
  data() {
    return {
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },

  mounted() {
  },
  methods: {

    loginError() {

    },
    loginComplete(res) {
      if (this.redirect?.startsWith('http://') || this.redirect?.startsWith('https://')) {
        window.location.href = `${this.redirect}${this.redirect.indexOf('?') > -1 ? '' : '?'}&Authorization=${getToken()}`
      } else {
        this.$router.push({ path: this.redirect || "/" });
      }
    }
  }

}
</script>

<style lang="scss" scoped>
/* 保持原有的样式,添加新的背景和动画样式 */
.login-container {
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

// 添加新的背景动画样式
.animated-background {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.wave-group {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;

  .wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: url('data:image/svg+xml,<svg viewBox="0 0 1440 320" xmlns="http://www.w3.org/2000/svg"><path fill="rgba(255,255,255,0.1)" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') repeat-x;
    animation: wave 10s linear infinite;
    transform-origin: center bottom;

    &.wave1 {
      animation-delay: 0s;
      opacity: 0.3;
    }

    &.wave2 {
      animation-delay: -2s;
      opacity: 0.2;
    }

    &.wave3 {
      animation-delay: -4s;
      opacity: 0.1;
    }
  }
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .shape {
    position: absolute;
    opacity: 0.1;
    background: #fff;

    &.circle {
      width: 200px;
      height: 200px;
      border-radius: 50%;
      top: 20%;
      left: 10%;
      animation: float 6s ease-in-out infinite;
    }

    &.square {
      width: 150px;
      height: 150px;
      top: 50%;
      right: 15%;
      animation: float 8s ease-in-out infinite;
      transform: rotate(45deg);
    }

    &.triangle {
      width: 0;
      height: 0;
      border-left: 100px solid transparent;
      border-right: 100px solid transparent;
      border-bottom: 180px solid rgba(255, 255, 255, 0.1);
      bottom: 15%;
      left: 30%;
      animation: float 7s ease-in-out infinite;
    }
  }
}

// 底部信息样式
.site-footer {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;

  .copyright {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    text-align: center;

    @media screen and (max-width: 768px) {
      font-size: 12px;
    }

    .el-divider--vertical {
      margin: 0 12px;
      background-color: rgba(255, 255, 255, 0.3);

      @media screen and (max-width: 768px) {
        margin: 0 8px;
      }
    }

    .el-link {
      color: rgba(255, 255, 255, 0.8);
      font-size: inherit;

      &:hover {
        color: #fff;
      }
    }
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes wave {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

// 优化登录按钮在移动端的样式
.login-button {
  @media screen and (max-width: 768px) {
    height: 40px !important;
    font-size: 15px !important;
  }
}

// 优化快捷登录按钮在移动端的样式
.method-buttons {
  @media screen and (max-width: 768px) {
    gap: 16px !important;

    .method-btn {
      width: 40px !important;
      height: 40px !important;
      font-size: 20px !important;
    }
  }
}

// 优化验证码容器在移动端的样式
.captcha-container {
  @media screen and (max-width: 768px) {
    gap: 8px;

    .captcha-img {
      width: 100px;
      font-size: 20px;
    }
  }
}
</style>