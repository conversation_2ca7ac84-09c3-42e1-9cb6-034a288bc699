import Vue from 'vue'
import Router from 'vue-router'
// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
let addRoute = Router.prototype.addRoute;

let history = []
export function stack() {
  return history
}

Router.prototype.push = function (location) {
  history.push(location)
  return routerPush.call(this, location).catch(err => err)
}
Router.prototype.replace = function (location) {
  history = []
  return routerReplace.call(this, location).catch(err => err)
}
Router.prototype.addRoutes = function (routes) {
  routes.forEach(route => {
    addRoute.call(this, route)
  });
}

const objBuild = {
  end: function () {
    resetRouter()
  }
}

Object.defineProperty(objBuild, 'end', {
  writable: false,
  configurable: false
})

Router.prototype.concatRoutes = [
  {
    path: '/login',
    component: () => objBuild['/login'] || import('../views/login/index'),
    hidden: true
  },
  //auth给三方授权
  {
    path: '/auth',
    component: () => objBuild['/auth'] || import('../views/auth'),
    hidden: true
  },
  //通用异常页
  {
    path: '/exception',
    component: () => objBuild['/exception'] || import('../views/exception'),
    hidden: true
  },
  {
    path: '/404',
    component: () => objBuild['/404'] || import('../views/404'),
    hidden: true
  }
]
Router.prototype.moduleRoutes = function (routes) {
  this.concatRoutes.push(...routes)
  Router.prototype.addRoutes.call(this, routes)
  resetRouter()
}

Vue.use(Router)

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

export const constantRoutes = [
  {
    path: '/redirect/:path(.*)',
    component: () => import('@/views/tab/redirect'),
    hidden: true,
  },

  // {
  //   path: '/login',
  //   component: () => import('../views/login/index'),
  //   hidden: true
  // },
  {
    path: '/logout',
    component: () => import('../views/logout'),
    hidden: true
  },
  // {
  //   path: '/auth',
  //   component: () => import('../views/auth'),
  //   hidden: true
  // },
  // {
  //   path: '/404',
  //   component: () => import('../views/404'),
  //   hidden: true
  // }

]

export function loginImport(importFun) {
  objBuild['/login'] = importFun
  resetRouter()
}

export function replaceImport(path, importFun) {
  objBuild[path] = importFun
  return objBuild
}

const createRouter = () =>
  new Router({
    base: process.env.BASE_URL,
    mode: process.env.SERVE_ALL ? 'hash' : "history",
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
  router.addRoutes(router.concatRoutes)
}

export default router