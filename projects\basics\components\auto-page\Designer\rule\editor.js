import uniqueId from '@form-create/utils/lib/unique';
import { localeProps } from '@form-create/designer-zrw/src/utils';

const label = '富文本框2';
const name = 'fcEditor2';

export default {
    menu: 'custom',
    icon: 'icon-editor',
    label,
    name,
    event: ['change'],
    validate: ['string'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            $required: false,
            props: {},
        };
    },
    props(_, { t }) {
        return localeProps(t, 'fcEditor.props', [{
            type: 'switch',
            field: 'disabled'
        }]);
    }
};
