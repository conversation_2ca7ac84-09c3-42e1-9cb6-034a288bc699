<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav" />
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />

    <div class="right-menu">
      <notice-icon />
      <div style="margin-right: 10px;margin-left: 10px;">{{ this.$store.state.user.name }}</div>
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <!-- <img src="@/assets/u2144.png?imageView2/1/w/80/h/80" class="user-avatar" /> -->
          <el-avatar :size="rpx(36)" :src="picUrl" class="user-avatar">
            <img src="@/assets/u2144.png?imageView2/1/w/80/h/80" />
          </el-avatar>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link :to="{ path: '/profile/index' }">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <router-link :to="{ path: '/notice/center' }">
            <el-dropdown-item>
              消息中心
              <el-badge v-if="noticeCount" :value="noticeCount" class="notice-badge" />
            </el-dropdown-item>
          </router-link>
          <el-dropdown-item item-type="setting" @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from '@/components/TopNav'
import Hamburger from "@/components/Hamburger";
import NoticeIcon from '@/layout/components/Notice/NoticeIcon.vue'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    TopNav,
    NoticeIcon
  },
  data() {
    return {
      url: process.env.VUE_APP_FILE_URL,
    }
  },
  computed: {
    ...mapGetters(["sidebar", "avatar"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/setReadOnly', false)
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
        this.$store.dispatch('settings/setReadOnly', true)
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    },
    picUrl() {
      return this.url + this.$store.state.user.info.picUrl
    },
    noticeCount() {
      return this.$store.state.user.unreadNoticeCount || 0
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    logout() {
      this.$confirm("确定退出?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$store.dispatch("user/logout").then(() => {
            this.$router.replace(`/login`);
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消退出"
          });
        });
    },
  }
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-sizing: content-box;
  // box-shadow: 0 1px 2px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .right-menu {
    float: right;
    height: 100%;
    margin-right: 20px;
    display: flex;
    align-items: center;

    .avatar-container {
      margin-right: 30px;
      height: 50px;

      .avatar-wrapper {
        height: 50px;
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 36px;
          height: 36px;
          background-color: transparent;
          // border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

.notice-badge {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
</style>
