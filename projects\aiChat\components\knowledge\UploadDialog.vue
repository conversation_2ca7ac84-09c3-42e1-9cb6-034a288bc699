<template>
  <el-dialog 
    title="上传文件到知识库"
    :visible.sync="dialogVisible"
    width="600px"
    @closed="handleClose"
  >
    <el-form :model="form" ref="form" label-width="140px" size="small">
      <!-- 知识库选择 -->
      <el-form-item label="选择知识库" required>
        <el-select 
          v-model="selectedKbName" 
          style="width: 100%"
          placeholder="请选择知识库"
        >
          <el-option
            v-for="kb in knowledgeBases"
            :key="kb.kb_name"
            :label="kb.kb_name"
            :value="kb.kb_name"
          >
            <span>{{ kb.kb_name }}</span>
            <span class="option-desc">{{ kb.kb_info }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 文件上传 -->
      <el-form-item 
        label="选择文件" 
        required
      >
        <el-tooltip
          placement="right"
          popper-class="file-tooltip"
        >
          <div slot="content">
            <p><strong>支持的文件格式：</strong></p>
            <p>文档类：PDF, DOCX, TXT, MD, RTF, ODT</p>
            <p>表格类：CSV, XLSX, XLS, TSV</p>
            <p>演示类：PPT, PPTX</p>
            <p>网页类：HTML, HTM, MHTML, XML</p>
            <p>图片类：PNG, JPG, JPEG, BMP</p>
            <p>邮件类：EML, MSG, ENEX</p>
            <p>代码类：PY, IPYNB, JSON, JSONL, SRT, TOML, RST</p>
            <p class="size-limit">单文件大小限制：200MB</p>
          </div>
          <el-upload
            class="upload-demo"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            multiple
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <!-- <div class="el-upload__tip" slot="tip">
              <el-link type="primary" icon="el-icon-info">查看支持的文件格式</el-link>
            </div> -->
          </el-upload>
        </el-tooltip>
      </el-form-item>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <div class="progress-info">
          <span>正在上传: {{ currentUploadFile }}</span>
          <span>{{ uploadProgress.finished }}/{{ uploadProgress.total }}</span>
        </div>
        <el-progress 
          :percentage="uploadProgress.percentage"
          :status="uploadProgress.status"
        ></el-progress>
      </div>

      <!-- 高级选项展开按钮 -->
      <div class="advanced-options-toggle" @click="showAdvanced = !showAdvanced">
        <span>高级选项</span>
        <i :class="['el-icon-arrow-right', { 'is-active': showAdvanced }]"></i>
      </div>

      <!-- 高级选项内容 -->
      <div v-show="showAdvanced" class="advanced-options">
        <el-form-item label="覆盖已有文件">
          <el-switch v-model="form.override"></el-switch>
        </el-form-item>

        <el-form-item label="立即向量化">
          <el-switch v-model="form.to_vector_store"></el-switch>
        </el-form-item>

        <el-form-item label="中文标题加强">
          <el-switch v-model="form.zh_title_enhance"></el-switch>
        </el-form-item>

        <el-form-item label="暂不保存向量库">
          <el-switch v-model="form.not_refresh_vs_cache"></el-switch>
          <span class="form-tip">仅适用于FAISS向量库</span>
        </el-form-item>

        <el-form-item label="单段文本最大长度">
          <el-input-number 
            v-model="form.chunk_size"
            :min="100"
            :max="2000"
            :step="50"
          ></el-input-number>
          <span class="form-tip">建议范围：100-2000</span>
        </el-form-item>

        <el-form-item label="文本重合长度">
          <el-input-number 
            v-model="form.chunk_overlap"
            :min="0"
            :max="500"
            :step="10"
          ></el-input-number>
          <span class="form-tip">建议范围：0-500</span>
        </el-form-item>

        <el-form-item label="自定义文档内容">
          <el-input
            type="textarea"
            v-model="form.docs"
            :rows="4"
            placeholder="请输入JSON格式的文档内容"
          ></el-input>
          <span class="form-tip">可选，需要是有效的JSON字符串</span>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer">
      <el-button @click="dialogVisible = false" :disabled="uploading">取 消</el-button>
      <el-button 
        type="primary" 
        @click="submitUpload" 
        :loading="uploading"
        :disabled="!fileList.length"
      >
        {{ uploading ? `正在上传 (${uploadProgress.finished}/${uploadProgress.total})` : '开始上传' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from '../../api'

export default {
  name: 'UploadDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    kbName: {
      type: String,
      required: true
    },
    knowledgeBases: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      uploading: false,
      fileList: [],
      showAdvanced: false,
      selectedKbName: '',
      form: {
        override: false,
        to_vector_store: true,
        zh_title_enhance: true,
        not_refresh_vs_cache: false,
        chunk_size: 500,
        chunk_overlap: 50,
        docs: ''
      },
      uploadUrl: process.env.VUE_APP_BASE_API + 'knowledge_base/upload_file',
      uploadProgress: {
        total: 0,
        finished: 0,
        percentage: 0,
        status: ''
      },
      currentUploadFile: '',
      supportedTypes: [
        'html', 'htm', 'mhtml', 'md', 'json', 'jsonl', 'csv', 'pdf', 
        'docx', 'ppt', 'pptx', 'png', 'jpg', 'jpeg', 'bmp', 'eml', 
        'msg', 'rst', 'rtf', 'txt', 'xml', 'epub', 'odt', 'tsv', 
        'xlsx', 'xls', 'xlsd', 'ipynb', 'py', 'srt', 'toml', 'enex'
      ]
    }
  },
  computed: {
    uploadData() {
      const data = {
        knowledge_base_name: this.selectedKbName
      };

      if (this.showAdvanced) {
        Object.assign(data, {
          override: this.form.override,
          to_vector_store: this.form.to_vector_store,
          chunk_size: this.form.chunk_size,
          chunk_overlap: this.form.chunk_overlap,
          zh_title_enhance: this.form.zh_title_enhance,
          not_refresh_vs_cache: this.form.not_refresh_vs_cache
        });

        if (this.form.docs) {
          data.docs = this.form.docs;
        }
      }

      return data;
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.selectedKbName = this.kbName;
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  methods: {
    handleFileChange(file, fileList) {
      // 验证文件
      if (!this.validateFile(file)) {
        const index = fileList.indexOf(file);
        fileList.splice(index, 1);
        return;
      }
      this.fileList = fileList;
    },

    handleFileRemove(file, fileList) {
      this.fileList = fileList;
    },

    validateFile(file) {
      // 检查文件大小
      const isLt200M = file.size / 1024 / 1024 < 200;
      if (!isLt200M) {
        this.$message.error('文件大小不能超过 200MB!');
        return false;
      }

      // 检查文件类型
      const extension = file.name.split('.').pop().toLowerCase();
      const isValidType = this.supportedTypes.includes(extension);
      if (!isValidType) {
        this.$message.error('不支持的文件类型!');
        return false;
      }

      return true;
    },

    async submitUpload() {
      if (!this.fileList.length) {
        this.$message.warning('请选择要上传的文件');
        return;
      }

      this.uploading = true;
      this.uploadProgress = {
        total: this.fileList.length,
        finished: 0,
        percentage: 0,
        status: 'primary'
      };

      try {
        for (const file of this.fileList) {
          this.currentUploadFile = file.name;
          
          const uploadData = {
            files: file.raw,
            knowledge_base_name: this.selectedKbName
          };

          if (this.showAdvanced) {
            Object.assign(uploadData, {
              override: this.form.override,
              to_vector_store: this.form.to_vector_store,
              chunk_size: this.form.chunk_size,
              chunk_overlap: this.form.chunk_overlap,
              zh_title_enhance: this.form.zh_title_enhance,
              not_refresh_vs_cache: this.form.not_refresh_vs_cache,
              docs: this.form.docs || undefined
            });
          }

          await api.knowledge.uploadFiles(uploadData);
          
          this.uploadProgress.finished++;
          this.uploadProgress.percentage = Math.round(
            (this.uploadProgress.finished / this.uploadProgress.total) * 100
          );
        }

        this.uploadProgress.status = 'success';
        this.$message.success('所有文件上传成功');
        this.$emit('success');
        this.dialogVisible = false;
      } catch (error) {
        this.uploadProgress.status = 'exception';
        this.$message.error('上传失败：' + (error.message || '未知错误'));
        console.error('上传失败:', error);
      } finally {
        this.uploading = false;
      }
    },

    handleClose() {
      if (this.uploading) {
        this.$message.warning('文件正在上传中，请等待上传完成');
        return;
      }
      this.fileList = [];
      this.showAdvanced = false;
      this.selectedKbName = '';
      this.uploadProgress = {
        total: 0,
        finished: 0,
        percentage: 0,
        status: ''
      };
      this.currentUploadFile = '';
      this.form = {
        override: false,
        to_vector_store: true,
        zh_title_enhance: true,
        not_refresh_vs_cache: false,
        chunk_size: 500,
        chunk_overlap: 50,
        docs: ''
      };
    }
  }
}
</script>

<style scoped>
.upload-demo {
  text-align: center;
}

.el-upload__tip {
  margin-top: 10px;
  line-height: 1.5;
  white-space: normal;
  word-break: break-all;
}

.form-tip {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.advanced-options-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #409EFF;
  padding: 10px 0;
  margin-bottom: 10px;
}

.advanced-options-toggle i {
  margin-left: 5px;
  transition: transform 0.3s;
}

.advanced-options-toggle i.is-active {
  transform: rotate(90deg);
}

.advanced-options {
  border-top: 1px solid #EBEEF5;
  padding-top: 20px;
  margin-top: 10px;
}

:deep(.el-upload-dragger) {
  width: 360px;
}

:deep(.el-input-number) {
  width: 160px;
}

.option-desc {
  float: right;
  color: #909399;
  font-size: 13px;
  padding-left: 10px;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 15px;
}

.upload-progress {
  padding: 10px 20px;
  margin-top: 10px;
  border-top: 1px solid #EBEEF5;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
}

.el-progress {
  margin-bottom: 10px;
}

:deep(.file-tooltip) {
  max-width: 400px !important;
  padding: 12px !important;
}

:deep(.file-tooltip p) {
  margin: 8px 0;
  line-height: 1.4;
}

:deep(.file-tooltip .size-limit) {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #E6A23C;
}

.el-upload__tip {
  margin-top: 10px;
}

.el-upload__tip .el-link {
  font-size: 13px;
}
</style> 