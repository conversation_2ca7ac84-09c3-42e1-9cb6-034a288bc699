import request from '@/utils/request'

const api = '/notice'

// 获取联系人列表
export function getFriendsList(params) {
  return request({
    url: `${api}/friends`,
    method: 'get',
    params
  })
} 

/**
 * 获取当前用户的订阅信息
 * @returns {Promise} 当前用户的订阅信息
 */
export function getCurrentSubscriptions() {
  return request({
    url: `${api}/subscription/current/account`,
    method: 'get',
    hideExceptionPrompt: true
  })
} 

/**
 * 获取历史消息
 * @param {String} chatType 消息类型(BROADCAST, PRIVATE, GROUP)
 * @param {Number} current 当前页
 * @param {Number} size 每页大小
 * @param {String} keyword 搜索关键词(可选)
 * @returns {Promise}
 */
export function getHistoryMessages(chatType, current = 1, size = 10, keyword) {
  return request({
    url: `${api}/message/current/account/history`,
    method: 'get',
    params: {
      chatType,
      current,
      size,
      keyword
    }
  })
}

/**
 * 获取当前用户的未读消息数量
 * @returns {Promise} 未读消息数量统计
 */
export function getUnreadMessageCount() {
  return request({
    url: `${api}/message/read/unread/count`,
    method: 'get'
  })
}

/**
 * 更新消息已读状态
 * @param {Number} messageId 消息ID
 * @returns {Promise} 更新结果
 */
export function readMessage(messageId) {
  return request({
    url: `${api}/message/read/current/${messageId}`,
    method: 'put'
  })
}

/**
 * 批量标记消息为已读
 * @param {Array} messageIds 消息ID列表
 * @returns {Promise} 更新结果
 */
export function batchReadMessages(messageIds) {
  return request({
    url: `${api}/message/read/read/batch`,
    method: 'post',
    data: messageIds
  })
}

/**
 * 一键标记某类型所有未读消息为已读
 * @param {String} chatType 消息类型（BROADCAST/PRIVATE/GROUP）
 * @returns {Promise} 更新结果
 */
export function readAllMessages(chatType) {
  return request({
    url: `${api}/message/read/read/all`,
    method: 'post',
    params: {
      chatType
    }
  })
}

/**
 * 获取系统配置
 * @param {String} key 配置键
 * @returns {Promise}
 */
export function getSystemConfig(key) {
  return request({
    url: '/system/sys/config/key',
    method: 'get',
    params: { key }
  })
}

/**
 * 获取消息分组统计
 * @param {String} chatType 消息类型(BROADCAST, PRIVATE, GROUP)
 * @returns {Promise}
 */
export function getMessageGroupStats(chatType) {
  return request({
    url: `${api}/message/group/stats`,
    method: 'get',
    params: { chatType }
  })
}

/**
 * 根据分组获取消息列表
 * @param {String} chatType 消息类型(BROADCAST, PRIVATE, GROUP)
 * @param {String} groupName 分组名称
 * @param {Number} current 当前页
 * @param {Number} size 每页大小
 * @returns {Promise}
 */
export function getMessagesByGroup(chatType, groupName, current, size) {
  return request({
    url: `${api}/message/group/messages`,
    method: 'get',
    params: {
      chatType,
      groupName,
      current,
      size
    }
  })
}

/**
 * 导出消息
 * @param {String} url 导出接口地址
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function exportMessages(url, params) {
  return request({
    url,
    method: 'get',
    params,
    responseType: 'blob'
  }).then(data => {
    const blob = new Blob([data.data], {
      type: 'application/vnd.ms-excel'
    })
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    
    // 设置文件名
    const date = new Date()
    const fileName = `消息记录_${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}.xlsx`
    link.download = fileName
    
    link.click()
    window.URL.revokeObjectURL(downloadUrl)
    
    return Promise.resolve('导出成功')
  })
} 