<template>
    <div class="resetpwd-ui-container">
        <el-page-header @back="$emit('goBack')" title="返回登录" content="找回密码" />

        <div v-if="!isReset">
            <div class="title">选择找回方式</div>
            <div class="hit">请选择您想要使用的密码找回验证方式</div>
            <div class="resetpwd-type-options">
                <div class="resetpwd-type-option" v-if="$attrs.configKeys['builtin.forget.phone']">
                    <i class="el-icon-mobile-phone"></i>
                    <h3>手机找回</h3>
                    <p>使用手机验证找回密码</p>
                    <el-button type="primary" plain @click="type = 1; isReset = true">选择</el-button>
                </div>

                <div class="resetpwd-type-option" v-if="$attrs.configKeys['builtin.forget.email']">
                    <i class="el-icon-message"></i>
                    <h3>邮箱找回</h3>
                    <p>使用邮箱验证找回密码</p>
                    <el-button type="primary" plain @click="type = 2; isReset = true">选择</el-button>
                </div>
            </div>
        </div>

        <div v-else>
            <div class="steps-container">
                <el-steps space="120px" align-center :active="stepActive">
                    <el-step :title="type == 1 ? '手机验证' : '邮箱验证'"></el-step>
                    <el-step title="设置密码"></el-step>
                </el-steps>
            </div>
            <template v-if="stepActive == 1">
                <div class="steps-hit">
                    <span v-if="type == 1">为了保护您的账户安全，请验证注册时使用的手机号码。验证通过后，您可以重新设置密码。系统会向您的注册手机号发送验证码。</span>
                    <span v-else>为了保护您的账户安全，请验证注册时使用的邮箱。验证通过后，您可以重置密码。系统会向您的注册邮箱发送验证码</span>
                </div>

                <el-form ref="validateForm" :model="validateForm"
                    :rules="type === 1 ? validatePhoneRules : validateEmailRules" class="validate-form">
                    <el-form-item prop="identifier">
                        <el-input :maxlength="type === 1 ? 11 : 32" v-model="validateForm.identifier"
                            :placeholder="type === 1 ? '请输入手机号码' : '请输入邮箱地址'"
                            :prefix-icon="type === 1 ? 'el-icon-mobile-phone' : 'iconfont ali-icon-youxiang'" />
                    </el-form-item>
                    <el-form-item prop="code">
                        <ValidateCodeInput :countdown="countdown" v-model="validateForm.code"
                            prefix-icon="el-icon-message" @send="clickSend(type === 1 ? 'sms' : 'email')">
                        </ValidateCodeInput>
                    </el-form-item>
                </el-form>
                <div>
                    <el-button :loading="loading" type="primary" class="resetpwd-button"
                        @click="validateNext(type === 1 ? 'sms' : 'email')">验证并继续<i class="el-icon-right"></i>
                    </el-button>
                </div>
            </template>
            <template v-else>
                <div class="steps-hit">
                    <span>为了账户安全，建议使用强密码组合，不要使用与其他账号相同的密码。</span>
                </div>
                <el-form ref="resetPwdForm" :model="resetPwdForm" :rules="resetPwdRules" class="validate-form">
                    <el-form-item prop="newPassword">
                        <el-input maxlength='20' v-model="resetPwdForm.newPassword" type="newPassword"
                            placeholder="请输入新密码" prefix-icon="el-icon-lock" show-password />
                    </el-form-item>
                    <el-form-item prop="confirmPassword">
                        <el-input maxlength='20' v-model="resetPwdForm.confirmPassword" type="confirmPassword"
                            placeholder="请确认新密码" prefix-icon="el-icon-lock" show-password />
                    </el-form-item>
                </el-form>
                <div class="reset-pwd-hit">
                    <div :class="{ met: passwordStrength.length }">
                        <i :class="passwordStrength.length ? 'el-icon-check' : 'el-icon-close'"></i>
                        密码长度为8-20个字符
                    </div>
                    <div :class="{ met: passwordStrength.complexity }">
                        <i :class="passwordStrength.complexity ? 'el-icon-check' : 'el-icon-close'"></i>
                        包含字母、数字和特殊字符@$!%*#?&.
                    </div>
                </div>
                <div>
                    <el-button :loading="loading" type="primary" class="resetpwd-button" @click="submit">确认修改<i
                            class="el-icon-check"></i>
                    </el-button>
                </div>
            </template>

        </div>
    </div>
</template>
<script>
import { sendCode } from '@/api/login/notice'
import { verifyResetCode, resetPassword } from '../api/auth'
import { $encruption, validatePhone, validateEmail, phonePattern, emailPattern, pwdPattern } from './consts'
import ValidateCodeInput from './ValidateCodeInput.vue';
import EmailValidator from './EmailValidator.js'

export default {
    components: {
        ValidateCodeInput
    },
    data() {
        // 修改验证确认密码的方法
        const validateConfirmPassword = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请再次输入密码'))
            } else if (value !== this.resetPwdForm.newPassword) {
                callback(new Error('两次输入的密码不一致'))
            } else {
                callback()
            }
        }
        return {
            type: 0,
            isReset: false,
            loading: false,
            stepActive: 1,
            countdown: 0,
            tempToken: '',
            passwordStrength: {
                length: false,
                complexity: false
            },
            validateForm: {
                identifier: '',
                code: ''
            },
            resetPwdForm: {
                newPassword: '',
                confirmPassword: ''
            },
            resetPwdRules: {
                newPassword: [
                    { required: true, message: '请输入新密码', trigger: 'blur' },
                    { min: 8, max: 20, message: '密码长度在 8 到 20 个字符', trigger: 'blur' },
                    {
                        pattern: pwdPattern,
                        message: '密码必须包含字母、数字和特殊字符',
                        trigger: 'blur'
                    }
                ],
                confirmPassword: [
                    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
                ]
            },
            validatePhoneRules: {
                identifier: [
                    { required: true, trigger: 'blur', validator: validatePhone }
                ],
                code: [
                    { required: true, message: '请输入短信验证码', trigger: 'submit' },
                    { len: 6, message: '验证码长度应为6位', trigger: 'submit' }
                ]
            },
            validateEmailRules: {
                identifier: [
                    { required: true, trigger: 'blur', validator: validateEmail }
                ],
                code: [
                    { required: true, message: '请输入邮箱验证码', trigger: 'submit' },
                    { len: 6, message: '验证码长度应为6位', trigger: 'submit' }
                ]
            }

        };
    },
    watch: {
        'resetPwdForm.newPassword': {
            handler(val) {
                this.passwordStrength.length = val.length >= 8 && val.length <= 20
                this.passwordStrength.complexity = pwdPattern.test(val)
            },
            immediate: true
        }
    },
    destroyed() {
        this.timerInterval && clearInterval(this.timerInterval)
    },
    methods: {
        startCountdown() {
            this.countdown = 60
            this.timerInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.timerInterval && clearInterval(this.timerInterval)
                }
            }, 1000)
        },

        clickSend(type) {
            if (!phonePattern.test(this.validateForm.identifier) && type === 'phone') {
                this.$message.error('请输入正确的手机号')
                return
            }
            if (!EmailValidator.validateEmail(this.validateForm.identifier) && type === 'email') {
                this.$message.error('请输入正确的邮箱号')
                return
            }
            if (this.countdown > 0) {
                return
            }
            sendCode({
                identifier: this.validateForm.identifier,
                type: type,
                scene: 'resetPassword'
            }).then(res => {
                this.$message.success('验证码已发送')
                this.startCountdown()
            }).catch(err => {
                this.$message.error(err || '发送失败')
            })
        },
        validateNext(type) {
            this.$refs.validateForm.validate(valid => {
                if (valid) {
                    this.loading = true
                    verifyResetCode({
                        identifier: this.validateForm.identifier,
                        verifyCode: this.validateForm.code,
                        verifyType: type,
                        verifyScene: 'resetPassword'
                    }).then(res => {
                        this.tempToken = res
                        this.stepActive = 2
                        this.loading = false
                    }).catch(err => {
                        this.loading = false
                        this.$message.error(err || '验证失败')
                    })
                }
            })
        },
        submit() {
            this.$refs.resetPwdForm.validate(valid => {
                if (valid) {
                    this.loading = true
                    const enpassword = $encruption(this.resetPwdForm.newPassword)
                    const enConfirmPassword = $encruption(this.resetPwdForm.confirmPassword)
                    resetPassword(this.tempToken, {
                        password: enpassword,
                        confirmPassword: enConfirmPassword
                    }).then(res => {
                        this.loading = false
                        this.$message.success('修改成功')
                        this.$confirm(res, '提示', {
                            confirmButtonText: '返回',
                            cancelButtonText: '取消',
                            type: 'success'
                        }).then(() => {
                            this.$emit('goBack')
                        }).catch(() => {
                            this.resetPwdForm = {}
                        });
                    }).catch(err => {
                        this.loading = false
                        this.$message.error(err || '修改失败')
                    })
                }
            })
        }
    }
};
</script>
<style lang="scss" scoped>
.resetpwd-ui-container {
    border-width: 1px;
    padding: 32px;
    border-radius: 20px;
    width: 480px;
    min-height: 500px;
    background-color: #fff;
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);


    .resetpwd-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        margin-top: 20px;
        background: rgb(0, 122, 255);
        border-radius: 8px;
        border: none;

        &:hover {
            background: rgb(0, 86, 179);

            i[class^="el-icon-"] {
                transition: all 0.3s;
                transform: translateX(15%);
            }
        }
    }


    .el-page-header {
        ::v-deep .el-page-header__left {
            color: rgb(0, 122, 255);
            transition: all 0.3s;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                color: rgb(0, 86, 179);
                transform: scale(1.05);
            }
        }

        ::v-deep .el-page-header__content {
            color: #000 !important;
        }
    }


    .title {
        font-size: 24px;
        color: #303133;
        font-weight: 500;
        text-align: center;
        margin-top: 20px;
    }

    .hit {
        text-align: center;
        color: #606266;
        font-size: 14px;
        margin-top: 20px;
    }

    .resetpwd-type-options {
        display: flex;
        justify-content: space-around;
        margin-top: 30px;

        .resetpwd-type-option {
            background-color: rgb(245, 247, 250);
            width: 184px;
            height: 220px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px 20px;
            transition: all 0.3s ease;

            &:hover {
                background-color: rgb(255, 255, 255);
                box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px 0px;
                transform: translateY(-2px);

            }

            i[class^="el-icon"] {
                color: rgb(64, 158, 255);
                font-size: 36px;
            }

            h3 {
                color: rgb(48, 49, 51);
                font-size: 16px;
            }

            p {
                color: rgb(144, 147, 153);
                font-size: 14px;
            }

            .el-button {
                width: 134px;
            }
        }
    }

    .steps-container {
        text-align: center;
        margin-top: 20px;

        ::v-deep .el-steps {
            display: flex;
            justify-content: center;

            .el-step__icon {
                width: 40px;
                height: 40px;
            }

            .el-step__line {
                top: 20px;
                left: 70%;
                right: -30%;
                background-color: rgb(229, 231, 235);
            }

            .el-step__title.is-finish {
                color: rgb(0, 122, 255);
            }

            .is-finish {
                .el-step__icon {
                    border: none;
                    background-color: rgb(0, 122, 255);

                    .el-step__icon-inner {
                        color: #fff;
                    }
                }

            }

            .is-process {
                .el-step__icon {
                    border: none;
                    background-color: rgb(229, 231, 235);

                    .el-step__icon-inner {
                        color: rgb(75, 85, 99);
                    }
                }
            }
        }
    }

    .steps-hit {
        padding: 16px;
        background-color: rgba(239, 246, 255, 0.7);
        border: 1px solid #e5e5e5;
        border-radius: 6px;
        margin-top: 20px;

        span {
            color: rgb(75, 85, 99);
            font-size: 14px;
            line-height: 26px;
        }
    }

    .validate-form {
        margin-top: 20px;
    }

    .reset-pwd-hit {
        margin: 16px 0 24px;
        padding: 12px 16px;
        background: #f5f7fa;
        border-radius: 4px;

        >div {
            color: #909399;
            font-size: 14px;
        }

        .met {
            color: #67c23a;
        }

        >div:last-child {
            margin-top: 10px;
        }
    }
}
</style>