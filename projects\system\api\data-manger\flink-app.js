import request from '@/utils/request'

// 获取 Flink 应用列表
export function getFlinkAppList(params) {
  return request({
    url: '/meta/flink/app/list',
    method: 'get',
    params
  })
}

// 获取目标表列表
export function getSinkTableList(params) {
  return request({
    url: `/meta/flink/app/sink/table/list`,
    method: 'get',
    params
  })
}


// 创建 Flink 应用
export function createFlinkApp(data) {
  return request({
    url: '/meta/flink/app',
    method: 'post',
    data
  })
}

// 更新 Flink 应用
export function updateFlinkApp(data) {
  return request({
    url: '/meta/flink/app',
    method: 'put',
    data
  })
}

// 删除 Flink 应用
export function deleteFlinkApp(ids) {
  return request({
    url: `/meta/flink/app/${ids}`,
    method: 'delete'
  })
}
