# 集成板对接

你需要注意以下几个点
首先调用接口，调用接口时，请注意以下规则
1. 发请求的时候，我全局响应已经把data取出来了，不要多取一层
2. 不要太多的异常捕获，我请求工具里面已经做了全局的异常捕获
3. 先把接口放到 /projects/system/api/integration/flow-app.js里面


## 集成板列表

```bash
curl --location --request GET 'http://localhost:8007/integration/flow/app/canvas/list' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive'
```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "records": [
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-05 15:46:04",
                "updateTime": "2025-08-05 15:48:54",
                "id": 1,
                "remark": null,
                "integrationAppId": 1,
                "code": null,
                "canvas": "{\r\n  \"nodes\": [\r\n    {\r\n      \"id\": \"efe347cf-854a-4e7d-a710-acfc70343b2a\",\r\n      \"type\": \"START_NODE\",\r\n      \"x\": 216,\r\n      \"y\": 87.3984375,\r\n      \"properties\": {\r\n        \"width\": 135,\r\n        \"height\": 50\r\n      }\r\n    },\r\n    {\r\n      \"id\": \"2a5c3f90-34ca-4ba3-8b78-424a6df41f95\",\r\n      \"type\": \"END_NODE\",\r\n      \"x\": 547,\r\n      \"y\": 313.3984375,\r\n      \"properties\": {\r\n        \"width\": 135,\r\n        \"height\": 50\r\n      }\r\n    },\r\n    {\r\n      \"id\": \"1916053c-d18f-4501-967b-09ded770ed29\",\r\n      \"type\": \"HTTP_NODE\",\r\n      \"x\": 308,\r\n      \"y\": 205.3984375,\r\n      \"properties\": {\r\n        \"width\": 135,\r\n        \"height\": 50,\r\n        \"name\": \"获取部门树列表\",\r\n        \"url\": \"http://*************/prod-api/system/sys/dept/treeList\",\r\n        \"method\": \"GET\",\r\n        \"contentType\": \"application/json\",\r\n        \"headers\": {\r\n          \"Content-Type\": \"application/json\",\r\n          \"Accept\": \"*/*\",\r\n          \"User-Agent\": \"Apifox/1.0.0 (https://apifox.com)\",\r\n          \"Connection\": \"keep-alive\",\r\n          \"Host\": \"************:8007\",\r\n          \"Accept-Encoding\": \"gzip, deflate, br\",\r\n          \"Authorization\": \"d75206fe-02a0-4cc7-8858-948b2932662d\"\r\n        },\r\n        \"body\": \"{}\",\r\n        \"description\": \"调用系统部门树列表接口\"\r\n      }\r\n    }\r\n  ],\r\n  \"edges\": [\r\n    {\r\n      \"id\": \"95379afe-5522-47d8-b81b-e23567a677ef\",\r\n      \"type\": \"polyline\",\r\n      \"properties\": {},\r\n      \"sourceNodeId\": \"efe347cf-854a-4e7d-a710-acfc70343b2a\",\r\n      \"targetNodeId\": \"1916053c-d18f-4501-967b-09ded770ed29\",\r\n      \"sourceAnchorId\": \"efe347cf-854a-4e7d-a710-acfc70343b2a_outgoing\",\r\n      \"targetAnchorId\": \"1916053c-d18f-4501-967b-09ded770ed29_incomming\",\r\n      \"startPoint\": {\r\n        \"x\": 283.5,\r\n        \"y\": 87.3984375\r\n      },\r\n      \"endPoint\": {\r\n        \"x\": 240.5,\r\n        \"y\": 205.3984375\r\n      },\r\n      \"pointsList\": [\r\n        {\r\n          \"x\": 283.5,\r\n          \"y\": 87.3984375\r\n        },\r\n        {\r\n          \"x\": 313.5,\r\n          \"y\": 87.3984375\r\n        },\r\n        {\r\n          \"x\": 313.5,\r\n          \"y\": 146.3984375\r\n        },\r\n        {\r\n          \"x\": 210.5,\r\n          \"y\": 146.3984375\r\n        },\r\n        {\r\n          \"x\": 210.5,\r\n          \"y\": 205.3984375\r\n        },\r\n        {\r\n          \"x\": 240.5,\r\n          \"y\": 205.3984375\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      \"id\": \"3a320baf-eef8-4e38-b289-2fd7faf5772a\",\r\n      \"type\": \"polyline\",\r\n      \"properties\": {},\r\n      \"sourceNodeId\": \"1916053c-d18f-4501-967b-09ded770ed29\",\r\n      \"targetNodeId\": \"2a5c3f90-34ca-4ba3-8b78-424a6df41f95\",\r\n      \"sourceAnchorId\": \"1916053c-d18f-4501-967b-09ded770ed29_outgoing\",\r\n      \"targetAnchorId\": \"2a5c3f90-34ca-4ba3-8b78-424a6df41f95_incomming\",\r\n      \"startPoint\": {\r\n        \"x\": 375.5,\r\n        \"y\": 205.3984375\r\n      },\r\n      \"endPoint\": {\r\n        \"x\": 479.5,\r\n        \"y\": 313.3984375\r\n      },\r\n      \"pointsList\": [\r\n        {\r\n          \"x\": 375.5,\r\n          \"y\": 205.3984375\r\n        },\r\n        {\r\n          \"x\": 449.5,\r\n          \"y\": 205.3984375\r\n        },\r\n        {\r\n          \"x\": 449.5,\r\n          \"y\": 313.3984375\r\n        },\r\n        {\r\n          \"x\": 479.5,\r\n          \"y\": 313.3984375\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}",
                "xmlContent": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<beans xmlns=\"http://www.springframework.org/schema/beans\"\r\n       xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\r\n       xmlns:int=\"http://www.springframework.org/schema/integration\"\r\n       xmlns:int-webflux=\"http://www.springframework.org/schema/integration/webflux\"\r\n       xsi:schemaLocation=\"http://www.springframework.org/schema/beans\r\n            http://www.springframework.org/schema/beans/spring-beans.xsd\r\n            http://www.springframework.org/schema/integration\r\n            http://www.springframework.org/schema/integration/spring-integration.xsd\r\n            http://www.springframework.org/schema/integration/webflux\r\n            http://www.springframework.org/schema/integration/webflux/spring-integration-webflux.xsd\">\r\n\r\n    <bean id=\"headerMapper\" class=\"org.springframework.integration.http.support.DefaultHttpHeaderMapper\">\r\n        <!-- 透传所有头 -->\r\n        <property name=\"outboundHeaderNames\" value=\"*\"/>\r\n        <!-- 入站时也接收所有头 -->\r\n        <property name=\"inboundHeaderNames\" value=\"*\"/>\r\n    </bean>\r\n\r\n    <!-- 1. WebFlux 入站网关 - 支持动态appCode提取 -->\r\n    <int-webflux:inbound-gateway\r\n            id=\"dynamicProxyInboundGateway\"\r\n            request-channel=\"dynamicProxyRequestChannel\"\r\n            reply-channel=\"dynamicProxyReplyChannel\"\r\n            path=\"/proxy/{appCode}/{connectorCode}/{*remainingPath}\"\r\n            header-mapper=\"headerMapper\"\r\n            reply-timeout=\"5000\">\r\n        <!-- 限制只接收带有这些头的请求 -->\r\n        <!-- 如果请求里没有 User-Agent 或 Accept，网关会立即返回 404 Not Found -->\r\n        <int-webflux:request-mapping headers=\"User-Agent, Accept\"/>\r\n        <!-- 提取路径变量appCode到消息头 -->\r\n        <int-webflux:header name=\"X-SI-AppCode\" expression=\"#pathVariables.appCode\"/>\r\n        <!-- 提取路径变量connectorCode到消息头 -->\r\n        <int-webflux:header name=\"X-SI-ConnectorCode\" expression=\"#pathVariables.connectorCode\"/>\r\n        <!-- 提取剩余路径用于转发 -->\r\n        <int-webflux:header name=\"X-SI-RemainingPath\" expression=\"#pathVariables.remainingPath ?: ''\"/>\r\n    </int-webflux:inbound-gateway>\r\n\r\n    <!-- 2. 入站请求通道 -->\r\n    <int:channel id=\"dynamicProxyRequestChannel\"/>\r\n\r\n    <!-- 3. 官方WebFlux出站网关 - 使用带认证Filter的WebClient -->\r\n    <!-- WebClient的Filter会动态处理URL和认证信息 -->\r\n    <int-webflux:outbound-gateway\r\n            id=\"proxyOutboundGateway\"\r\n            request-channel=\"dynamicProxyRequestChannel\"\r\n            reply-channel=\"dynamicProxyReplyChannel\"\r\n            url-expression=\"'http://placeholder-url'\"\r\n            http-method-expression=\"headers['http_requestMethod']\"\r\n            web-client=\"authEnabledWebClient\"\r\n            header-mapper=\"headerMapper\"\r\n            expected-response-type=\"java.lang.String\"\r\n            reply-timeout=\"10000\"/>\r\n\r\n    <!-- 4. 响应返回通道 -->\r\n    <int:channel id=\"dynamicProxyReplyChannel\"/>\r\n</beans>\r\n",
                "published": false
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    },
    "success": true
}
```

