<template>
  <div>
    <auto-page ref="rules" @updateRow="updateRow" @formChange="formChange" :options="options"
      :form-rule="options.formRule" :defaultQuery="{ parentId: '0', orders: { 'sort': 'asc' } }" @addRow="addRow"
      @clickTableRow="clickTableRow">
    </auto-page>
    <el-dialog :visible.sync="showEditorJson" title="编辑" width="80%" center top="8vh">
      <!-- <MonacoEditor :data="defaultData" ref="monaco"></MonacoEditor> -->
      <transition name="fade">
        <designerEditor v-if="showEditorJson" style="height: 75vh;" :jsonConfig="defaultData" :noCache="true"
          ref="monaco"></designerEditor>
      </transition>
      <el-button type="primary" @click="clickSave">保存</el-button>
    </el-dialog>
  </div>
</template>

<script>
import AutoPage from "@/components/auto-page/AutoPage";
import options from "./options";
import { treeList } from "@system/api/sys/resources"
import MonacoEditor from '@/components/auto-page/MonacoEditor'


export default {
  name: "index",
  components: { AutoPage, MonacoEditor },
  data() {
    return {
      options: null,
      showEditorJson: false,
      currentRow: null,
      defaultData: ''
    }
  },
  created() {
    this.options = options;
    this.assemblyTree()
  },
  methods: {
    clickTableRow(row, column, event) {
      if (row.type == 'MENU' && column.label != '操作') {
        this.currentRow = row
        this.defaultData = row.json ? JSON.stringify(row.json) : ''
        this.showEditorJson = true
      }
    },
    clickSave() {
      try {
        // let moStr = JSON.parse(this.$refs.monaco.editor.getValue())
        let moStr = this.$refs.monaco.getConfig()
        this.showEditorJson = false
        this.$refs.rules.updateList([{ id: this.currentRow.id, json: '' }], { json: moStr })
      } catch (error) {
        console.log('------------------->error', error)
      }
    },
    addRow() {
      setTimeout(() => {
        let api = this.$refs.rules.fApi
        api.getRule('visible').value = true
        api.getRule('status').value = true
      }, 100)
    },
    formChange(field, value, rule, api, setFlag) {

      let data = api.form
      if (data.type == 'APP') {
        api.hidden(false, ['path', 'visible', 'status', 'icon', 'activeMenu', 'windowOpen'])
        api.hidden(true, ['mode', 'component', 'cacheStatus', 'perms'])
      } else if (data.type == 'DIR') {
        api.hidden(false, ['mode', 'path', 'status', 'icon'])
        api.hidden(true, ['component', 'visible', 'cacheStatus', 'activeMenu', 'perms', 'windowOpen'])
      } else if (data.type == 'MENU') {
        api.hidden(false, ['mode', 'path', 'cacheStatus', 'visible', 'status', 'component', 'icon', 'activeMenu', 'perms'])
        api.hidden(true, ['windowOpen'])
      } else if (data.type == 'BUTTON') {
        api.hidden(true, ['mode', 'path', 'cacheStatus', 'visible', , 'status', 'component', 'icon', 'activeMenu', 'windowOpen'])
        api.hidden(false, ['perms'])
        api.getRule('visible').value = true
        api.getRule('status').value = true
        api.getRule('cacheStatus').value = false
      }

    },
    updateRow(type, row) {

    },
    async getTreeList() {
      return new Promise(((resolve, reject) => {
        treeList().then(res => {
          resolve(res)
        })
      }))
    },
    async assemblyTree() {
      let array = await this.getTreeList();
      this.recursion(array)
      this.options.formRule[0].options = array;
    },
    recursion(array) {
      for (let i = 0; i < array.length; i++) {
        array[i].label = array[i].name;
        delete array[i].name;
        if (array[i].children.length > 0) {
          this.recursion(array[i].children)
        }
      }
    }
  }
}
</script>
