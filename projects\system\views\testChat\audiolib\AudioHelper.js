import 'webrtc-adapter';
import processorCodeStr from './AudioWorkletProcessor.js';
export async function checkPermission() {
    let hasPermission = false
    let errorMessage = ''
    try {
        console.warn('开始获取录音权限');
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: false
        })
        hasPermission = true;
        errorMessage = '';
        console.warn('已获取录音权限');
        // 停止所有音轨
        stream.getTracks().forEach(track => track.stop())
    } catch (error) {
        hasPermission = false;
        if (error.name === 'NotAllowedError') {
            errorMessage = '麦克风权限被拒绝，请在浏览器设置中允许访问麦克风';
        } else if (error.name === 'NotFoundError') {
            errorMessage = '未找到麦克风设备';
        } else {
            errorMessage = `获取录音权限失败: ${error.message}`;
        }
        console.error('获取录音权限失败:', error);
    } finally {
        return {
            hasPermission,
            errorMessage
        }
    }
}
export function getUserMedia(audioConstraints = true) {
    return navigator.mediaDevices.getUserMedia({ audio: audioConstraints, video: false })
}

export function newAudioContext(config = { sampleRate: 16000 }) {
    return new (window.AudioContext || window.webkitAudioContext)(config)
}

export function createMediaStreamSource(audioContext, stream) {
    return audioContext.createMediaStreamSource(stream);
}
export function createScriptProcessor(audioContext, bufferSize = 4096) {
    return audioContext.createScriptProcessor(bufferSize, 1, 1);
}
export function createAudioWorkletNode(audioContext) {
    return new Promise((resolve, reject) => {
        audioContext.audioWorklet.addModule('data:text/javascript,' + encodeURIComponent(processorCodeStr))
            .then(() => {
                console.log('AudioWorklet 模块加载成功');
                const node = new AudioWorkletNode(audioContext, 'my-audio-processor')
                node.port.onmessage = (e) => {
                    if (e.data.type === 'AudioProcessor') {
                        const { inputs, outputs, parameters } = e.data.process
                        node.onaudioprocess && node.onaudioprocess(inputs, outputs, parameters)
                    }
                }
                resolve(node);
            })
            .catch(error => {
                console.error('加载 AudioWorklet 模块失败:', error)
                reject(error);
            });
    })
}

//查找支持的音频格式
export function getSupportMimeType() {
    const mimeTypes = [
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/ogg;codecs=opus',
        'audio/wav'
    ];
    return mimeTypes.find(type => MediaRecorder.isTypeSupported(type));
}

// 将音频数据转换为WAV格式
export async function convertToWav(audioChunks, sampleRate = 16000, isFloat32 = false) {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: sampleRate
    })

    const blob = new Blob(audioChunks)
    const arrayBuffer = await blob.arrayBuffer()

    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

    if (audioContext.state !== 'closed') {
        audioContext.close()
    }

    // 创建WAV文件头
    function createWavHeader(audioBuffer, isFloat32 = false) {
        const numChannels = audioBuffer.numberOfChannels
        const sampleRate = audioBuffer.sampleRate
        const format = isFloat32 ? 3 : 1; // 3 for float32, 1 for PCM
        const bitsPerSample = isFloat32 ? 32 : 16
        const bytesPerSample = bitsPerSample / 8
        const blockAlign = numChannels * bytesPerSample
        const byteRate = sampleRate * blockAlign;
        const dataSize = audioBuffer.length * blockAlign
        const buffer = new ArrayBuffer(44)
        const view = new DataView(buffer)

        // WAV文件头
        writeString(view, 0, 'RIFF')
        view.setUint32(4, 36 + dataSize, true)
        writeString(view, 8, 'WAVE')
        writeString(view, 12, 'fmt ')
        view.setUint32(16, 16, true)
        view.setUint16(20, format, true)
        view.setUint16(22, numChannels, true)
        view.setUint32(24, sampleRate, true)
        view.setUint32(28, byteRate, true)
        view.setUint16(32, blockAlign, true)
        view.setUint16(34, bitsPerSample, true)
        writeString(view, 36, 'data')
        view.setUint32(40, dataSize, true)

        return buffer
    }

    function writeString(view, offset, string) {
        for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i))
        }
    }

    // 将音频数据转换为16位整数
    function floatTo16BitPCM(float32Array) {
        const int16Array = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const s = Math.max(-1, Math.min(1, float32Array[i]));
            int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7FFF
        }
        return int16Array
    }

    const wavHeader = createWavHeader(audioBuffer, isFloat32)
    const audioData = audioBuffer.getChannelData(0)
    const pcmData = floatTo16BitPCM(audioData)

    const wavFile = new Blob([wavHeader, pcmData.buffer], { type: 'audio/wav' })

    return wavFile
}

//录音器
export function createMediaRecorder(stream, options = { mimeType: 'audio/webm' }) {
    if (!stream || !(stream instanceof MediaStream)) {
        throw new Error('Invalid MediaStream provided')
    }

    const mediaRecorder = new MediaRecorder(stream, options);
    const audioChunks = [];

    return {
        mediaRecorder,
        start: () => {
            audioChunks.length = 0; // 清空之前的录音数据
            mediaRecorder.ondataavailable = event => {
                if (event.data && event.data.size > 0) {
                    audioChunks.push(event.data);
                }
            };
            mediaRecorder.start(60);
        },
        stop: (save = false) => {
            if (mediaRecorder.state !== 'inactive') {
                mediaRecorder.onstop = async () => {
                    if (!save) {
                        return
                    }

                    try {
                        console.log('开始音频转换...')
                        console.log('音频数据块数量:', audioChunks.length)

                        if (audioChunks.length === 0) {
                            throw new Error('没有录制到音频数据')
                        }

                        const wavBlob = await convertToWav(audioChunks, 16000, false)
                        console.log('WAV转换成功，文件大小:', wavBlob.size, 'bytes')

                        const audioUrl = URL.createObjectURL(wavBlob)
                        const a = document.createElement('a')
                        a.href = audioUrl;
                        a.download = `recording_${new Date().getTime()}.wav`
                        document.body.appendChild(a)
                        a.click();
                        document.body.removeChild(a)

                        setTimeout(() => {
                            URL.revokeObjectURL(audioUrl);
                        }, 100);

                    } catch (error) {
                        console.error('音频转换错误:', error);
                        console.error('错误详情:', {
                            message: error.message,
                            name: error.name,
                            stack: error.stack
                        });
                        throw error;
                    }
                }
                mediaRecorder.stop()
            }
        }
    }
}

export function mediaClose(...args) {
    args.forEach(obj => {

        if (obj && typeof obj.disconnect === 'function') {
            obj.disconnect()
        }

        if (obj instanceof (window.AudioContext || window.webkitAudioContext) && obj.state !== 'closed') {
            obj.close()
        }

        if (obj instanceof MediaStream) {
            obj.getTracks().forEach(track => {
                track.stop()
            })
        }

        obj = null
    })

}

