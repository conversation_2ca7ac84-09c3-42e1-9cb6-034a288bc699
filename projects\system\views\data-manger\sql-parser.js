/**
 * 解析SQL中的参数
 * @param {string} sql SQL语句
 * @returns {{sqlParams: string[], queryParams: Object}} 返回解析后的参数列表和参数值对象
 */
export function parseSqlParams(sql) {
  // 用于匹配 #{参数名}
  const regexPlaceholder = /#\{([^}]+)\}/g
  // 用于匹配 <when test="..."> 或 <if test="...">
  const regexTestCondition = /<when test="([^"]+)">|<if test="([^"]+)">/g

  const params = new Set()      // 存储 #{param} 里的参数
  const conditions = new Set()  // 存储 <when> / <if> 中 test 表达式解析到的变量
  let match

  // 1. 解析所有 #{参数名}
  while ((match = regexPlaceholder.exec(sql)) !== null) {
    params.add(match[1])
  }

  // 2. 解析所有 <when test="..."> / <if test="...">
  while ((match = regexTestCondition.exec(sql)) !== null) {
    const condition = match[1] || match[2]
    if (condition) {
      const conditionVariables = condition.match(/\b\w+\b/g)
      if (conditionVariables) {
        conditionVariables.forEach((variable) => {
          if (!['null', 'and', 'or', '==', '!=', '>', '<', '>=', '<='].includes(variable.toLowerCase())
              && isNaN(variable)) {
            conditions.add(variable)
          }
        })
      }
    }
  }

  // 合并所有参数
  const sqlParams = Array.from(new Set([...Array.from(params), ...Array.from(conditions)]))

  // 初始化参数值对象
  const queryParams = sqlParams.reduce((acc, param) => {
    acc[param] = ''
    return acc
  }, {})

  return {
    sqlParams,
    queryParams
  }
} 