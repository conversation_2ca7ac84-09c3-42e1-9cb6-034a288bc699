import request from '@/utils/request'

const api = '/scheduled/scheduled/task'

export function list(params) {
    return request({
        url: `${api}/list`,
        params
    })
}
export function save(data) {
    return request({
        url: `${api}`,
        method: 'post',
        data
    })
}
export function tryonce(data) {
    return request({
        url: `${api}/tryonce`,
        method: 'post',
        data
    })
}

export function update(data) {
    return request({
        url: `${api}`,
        method: 'put',
        data
    })
}

export function remove(ids) {
    return request({
        url: `${api}/${ids}`,
        method: 'delete'
    })
}
export function groupNames(params) {
    return request({
        url: `${api}/groupNames`,
        params
    })
}
export function count(params) {
    return request({
        url: `${api}/count`,
        params
    })
}
export function types() {
    return request({
        url: `${api}/types`
    })
}
export function statuses() {
    return request({
        url: `${api}/statuses`
    })
}

export function reload() {
    return request({
        url: `${api}/reload`,
        method: 'put'
    })
}

export default {
    list,
    save,
    update,
    remove,
    groupNames,
    count,
    types,
    statuses,
    tryonce,
    reload
}