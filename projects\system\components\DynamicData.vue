<template>
  <div>
    <div class="flex-center" v-for="item, index in datas" :index="index">
      <el-row :gutter="20">
        <el-col :span="11" v-if="!isArray">
          <el-form-item :rules="{trigger: 'blur', message: `请输入${keyPlaceholder}`, required: true}" label-width="0" >
            <el-input v-model="item.key" :placeholder="keyPlaceholder" @input="dataChange" />
          </el-form-item>
        </el-col>
        <el-col :span="isArray ? 22 : 11">
          <el-form-item  label-width="0">
            <el-select v-model="item.value" v-if="valueOptions.length" style="width: 100%;" @change="dataChange" :placeholder="valuePlaceholder" filterable allow-create>
              <el-option v-for="option in valueOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
            </el-select>
            <el-input v-else v-model="item.value" :placeholder="valuePlaceholder" @input="dataChange" />
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeKey(index)"></el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

export default {
  name: 'DynamicData',
  props: {
    value: {
      type: [Object, Array]
    },
    keyWidth: {
      type: String,
      default: '40%'
    },
    keyPlaceholder: {
      type: String,
      default: '键'
    },
    valuePlaceholder: {
      type: String,
      default: '值'
    },
    minSize: {
      type: Number,
      default: 0
    },
    valueOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      datakey: undefined,
      datas:[]
    }
  },
  methods: {
    dataChange() {
      if (this.isArray) {
        this.$emit('change', this.datas.map(({value}) => value).filter(value => value))
      } else {
        var result = this.datas.reduce((data, {key, value}) => {
          if (key) {
            data[key] = value
          }
          return data
        }, {})
        this.$emit('change', result)
      }
    },
    removeKey(index) {
      if (this.datas.length > this.minSize) {
        this.datas.splice(index, 1)
        this.dataChange()
      }
    },
    addKey() {
      if (!this.datas.length) {
        this.datas.push({})
        return 
      }
      var {key, value} = this.datas.at(-1)
      if ((key && !this.isArray) || (value && this.isArray)) {
        this.datas.push({})
      }
    }
  },
  mounted() {
    this.datas = Object.entries(this.value).map(([key, value]) => {return {key, value}})
    if (this.datas.length < this.minSize) {
      let count = this.minSize - this.datas.length
      for (let index = 0; index < count; index++) {
        
        this.datas.push({})
      }
    }
  },
  computed: {
    isArray() {
      return Array.isArray(this.value)
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  }
}
</script>

<style scoped>
</style>