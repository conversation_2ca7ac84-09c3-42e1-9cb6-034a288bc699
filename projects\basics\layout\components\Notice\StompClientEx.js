import { Client } from '@stomp/stompjs'

const activate = Client.prototype.activate
const deactivate = Client.prototype.deactivate
const subscribe = Client.prototype.subscribe

Client.prototype.activate = function () {
    activate.call(this)
    if (!this._subscriptions) {
        this._subscriptions = []
        Object.defineProperty(this, '_subscriptions', {
            writable: false,
            configurable: false
        })
    }
}

Client.prototype.deactivate = async function (options) {
    this._subscriptions.forEach(subscription => {
        subscription.unsubscribe()
    })
    return deactivate.call(this, options)
}

Client.prototype.subscribe = function (destination, callback, headers) {
    const subscription = subscribe.call(this, destination, callback, headers)
    this._subscriptions.push(subscription)
    return subscription
}

Client.prototype.subscribes = function (destinations = [], callback, headers) {
    const subscriptions = []
    destinations.forEach(destination => {
        const subscription = this.subscribe(destination, callback, headers)
        subscriptions.push(subscription)
    })
    return subscriptions
}
