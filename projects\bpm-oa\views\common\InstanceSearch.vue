<script>
import {getGroupModels} from '@bpm-oa-web/api/modelGroup'

export default {
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rangeText: {
      type: Array,
      default: () => {
        return ['开始时间', '结束时间']
      }
    },
    keywordText: {
      type: String,
      default: '发起人、流程名'
    },
    showCode: {
      type: Boolean,
      default: true
    },
    showFinished: {
      type: Boolean,
      default: true
    },
    showFormAbstracts: {
      type: Boolean,
      default: false
    },
    showStartTimes: {
      type: Boolean,
      default: true
    },
    showKeyword: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      options: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '近一小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '近一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
      }
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  mounted() {
    this.loadFormList()
  },
  methods: {
    loadFormList() {
      getGroupModels().then(res => {
        res.data.forEach(group => {
          this.options.push({
            value: group.id,
            label: group.name,
            children: group.items.map(it => {
              return {
                value: it.formId,
                label: it.formName,
              }
            })
          })
        })
      }).catch((err) => {
        this.$err(err, '获取流程清单异常')
      })
    }
  }
}

</script>

<template>
  <div class="w-search">
    <el-cascader v-if="showCode" size="small" clearable :show-all-levels="false" :props="{emitPath: false}"
                 v-model="_value.code" style="width: 150px;" placeholder="筛选流程" :options="options"/>
    <el-date-picker v-if="showStartTimes" size="small" style="margin: 0 20px" v-model="_value.startTimes"
                    type="datetimerange" :picker-options="pickerOptions" range-separator="至"
                    :start-placeholder="rangeText[0]"
                    :end-placeholder="rangeText[1]" align="right" value-format="yyyy-MM-dd HH:mm:ss"/>
    <el-select v-if="showFinished" style="width: 150px; margin-right: 20px" size="small"
               placeholder="流程状态" v-model="_value.finished">
      <el-option :value="null" label="状态-全部"/>
      <el-option :value="false" label="状态-进行中"/>
      <el-option :value="true" label="状态-已结束"/>
    </el-select>
    <el-input v-if="showKeyword" style="width: 250px;" prefix-icon="el-icon-search"
              :placeholder="`搜索关键字（${keywordText}）`"
              size="small" clearable v-model="_value.keyword"/>
    <el-input v-if="showFormAbstracts" style="width: 250px;margin-left: 10px" prefix-icon="el-icon-search"
              placeholder="搜索文件主题"
              size="small" clearable v-model="_value.fieldVal"/>
  </div>
</template>

<style scoped lang="scss">
.w-search {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
