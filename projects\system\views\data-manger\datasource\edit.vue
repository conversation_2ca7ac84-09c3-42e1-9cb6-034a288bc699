<template>
  <div class="datasource-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>编辑数据源</h2>
        <div class="back-button" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          <span>返回数据源列表</span>
        </div>
      </div>
    </div>

    <!-- 编辑表单 -->
    <el-form 
      ref="editForm" 
      :model="form" 
      :rules="rules"
      label-width="120px"
      class="edit-form"
      v-loading="loading"
    >
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <!-- 数据源类型 -->
          <el-col :span="24">
            <el-form-item label="数据源类型">
              <div class="type-display">
                <db-icon :type="form.type" />
                <span>{{ getDbTypeName(form.type) }}</span>
              </div>
            </el-form-item>
          </el-col>

          <!-- 数据源名称 -->
          <el-col :span="12">
            <el-form-item label="数据源名称" prop="name">
              <el-input 
                v-model="form.name"
                placeholder="请输入数据源名称"
              >
                <template slot="append">
                  <el-tooltip content="数据源名称需要唯一" placement="top">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 数据源编码 -->
          <el-col :span="12">
            <el-form-item label="数据源编码" prop="code">
              <el-input 
                v-model="form.code"
                placeholder="请输入数据源编码，只能包含字母、数字和下划线"
              >
                <template slot="append">
                  <el-tooltip content="数据源编码需要唯一，只能包含字母、数字和下划线" placement="top">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 描述 -->
          <el-col :span="24">
            <el-form-item label="描述" prop="description">
              <el-input 
                type="textarea"
                v-model="form.description"
                :rows="3"
                placeholder="请输入数据源描述"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <div class="section-title">连接信息</div>
        <el-row :gutter="20">
          <!-- 主机名 -->
          <el-col :span="12">
            <el-form-item label="主机名" prop="host">
              <el-input 
                v-model="form.host"
                placeholder="请输入数据库主机名"
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 端口号 -->
          <el-col :span="12">
            <el-form-item label="端口号" prop="port">
              <el-input 
                v-model.number="form.port"
                placeholder="请输入数据库端口号"
                type="number"
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 服务名(仅Oracle显示) -->
          <el-col :span="12" v-if="isOracleType">
            <el-form-item label="服务名" prop="serverName">
              <el-input 
                v-model="form.serverName"
                placeholder="请输入数据库服务名"
              >
                <template slot="append">
                  <el-tooltip content="Oracle数据库必填(SID或服务名)" placement="top">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 默认模式(非Oracle显示) -->
          <el-col :span="12" v-if="!isOracleType">
            <el-form-item label="默认模式" prop="defaultSchema">
              <el-input 
                v-model="form.defaultSchema"
                placeholder="请输入默认模式(选填)"
              >
                <template slot="append">
                  <el-tooltip content="数据库默认模式/默认schema" placement="top">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 额外参数 -->
          <el-col :span="12">
            <el-form-item label="连接参数" prop="arg">
              <el-input 
                v-model="form.arg"
                placeholder="示例: useUnicode=true&characterEncoding=utf8"
              >
                <template slot="append">
                  <el-tooltip content="多个参数使用&连接" placement="top">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 数据库驱动 -->
          <el-col :span="24">
            <el-form-item label="数据库驱动" prop="driverClassName">
              <el-input 
                v-model="form.driverClassName"
                placeholder="未填写时将根据数据库类型自动选择"
              >
                <template slot="append">
                  <el-tooltip :content="getDefaultDriver(form.type)" placement="top">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </template>
              </el-input>
              <div class="driver-tip" v-if="!form.driverClassName">
                <i class="el-icon-info"></i>
                <span>默认驱动：{{ getDefaultDriver(form.type) }}</span>
              </div>
            </el-form-item>
          </el-col>

          <!-- 用户名 -->
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input 
                v-model="form.username"
                placeholder="请输入数据库用户名"
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 密码 -->
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input 
                v-model="form.password"
                type="password"
                placeholder="请输入数据库密码"
                show-password
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 预览生成的JDBC URL -->
          <el-col :span="24">
            <el-form-item label="JDBC URL预览">
              <div class="jdbc-preview">
                <span class="preview-content">{{ generatedJdbcUrl }}</span>
                <div class="preview-actions">
                  <el-button 
                    type="text" 
                    class="refresh-btn"
                    @click="refreshJdbcUrlPreview"
                    icon="el-icon-refresh"
                  >
                    刷新预览
                  </el-button>
                  <el-button 
                    type="text" 
                    class="copy-btn"
                    @click="copyGeneratedUrl"
                  >
                    <i class="el-icon-document-copy"></i>
                    复制
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>

          <!-- 数据源分组 -->
          <el-col :span="24">
            <el-form-item label="数据源分组" prop="groupName">
              <el-select 
                v-model="form.groupName" 
                placeholder="请选择或输入数据源分组"
                filterable
                allow-create
                default-first-option
                clearable
                class="full-width-select"
              >
                <el-option
                  v-for="group in groupList"
                  :key="group"
                  :label="group"
                  :value="group"
                />
              </el-select>
              <div class="form-tip">
                <i class="el-icon-info"></i>
                <span>可以选择已有分组或输入新分组名称</span>
              </div>
            </el-form-item>
          </el-col>

          <!-- 测试连接按钮 -->
          <el-col :span="24">
            <el-form-item>
              <el-button 
                type="primary" 
                @click="testConnection"
                :loading="testing"
                icon="el-icon-connection"
              >测试连接</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 底部操作按钮 -->
    <div class="form-actions">
      <el-button @click="goBack">取消</el-button>
      <el-button 
        type="primary" 
        @click="saveChanges"
        :loading="saving"
        icon="el-icon-check"
      >保存修改</el-button>
    </div>
  </div>
</template>

<script>
import DbIcon from './DbIcon.vue'
import { getDataSourceDetail, updateDataSource, testDataSourceConnection, getDataSourceGroups, getSupportedDataSourceTypes, previewJdbcUrl } from '@system/api/data-manger/datasource'

export default {
  name: 'DatasourceEdit',
  components: {
    DbIcon
  },
  data() {
    // 服务名验证规则
    const validateServerName = (rule, value, callback) => {
      if (this.form.type && this.form.type.toLowerCase() === 'oracle' && !value) {
        callback(new Error('Oracle数据库必须填写服务名'));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      testing: false,
      saving: false,
      refreshingSchemas: false,
      form: {
        type: '',
        name: '',
        code: '',
        description: '',
        host: '',
        port: '',
        serverName: '',
        defaultSchema: '',
        arg: '',
        username: '',
        password: '',
        schemaConfig: {
          type: 'physical',
          selected: []
        },
        driverClassName: '',
        groupName: '默认分组'
      },
      previewJdbcUrl: '',
      schemas: [],
      rules: {
        name: [
          { required: true, message: '请输入数据源名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入数据源编码', trigger: 'blur' },
          { pattern: /^[A-Za-z0-9_]+$/, message: '编码只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        host: [
          { required: true, message: '请输入数据库主机名', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入数据库端口号', trigger: 'blur' }
        ],
        serverName: [
          { validator: validateServerName, trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: false, message: '请输入密码', trigger: 'blur' }
        ]
      },
      groupList: ['默认分组'],
      supportedTypesMap: {}
    }
  },
  computed: {
    isOracleType() {
      return this.form.type && this.form.type.toLowerCase() === 'oracle'
    },
    generatedJdbcUrl() {
      return this.previewJdbcUrl || ''
    }
  },
  created() {
    this.fetchSupportedTypes().then(() => {
      this.fetchDatasource();
    });
    this.fetchGroupList();
  },
  methods: {
    async fetchGroupList() {
      try {
        const data = await getDataSourceGroups("groupName");
        this.groupList = data?.length ? data : ['默认分组'];
      } catch (error) {
        console.error('获取分组列表失败:', error);
        this.groupList = ['默认分组'];
      }
    },
    
    async fetchSupportedTypes() {
      try {
        const res = await getSupportedDataSourceTypes();
        if (res && Array.isArray(res)) {
          // 创建一个map用于快速查找
          this.supportedTypesMap = res.reduce((acc, type) => {
            acc[type.code] = type;
            return acc;
          }, {});
        }
      } catch (error) {
        console.error('获取支持的数据源类型失败:', error);
      }
    },
    
    // 获取数据源图标
    getDbIcon(type) {
      const iconMap = {
        mysql: 'el-icon-coin',
        oracle: 'el-icon-platform-eleme',
        postgresql: 'el-icon-data-analysis',
        sqlserver: 'el-icon-office-building',
        hive: 'el-icon-monitor',
        transwarp: 'el-icon-cpu',
        default: 'el-icon-folder-opened'
      }
      return iconMap[type] || iconMap.default
    },
    // 获取数据库类型名称
    getDbTypeName(type) {
      if (this.supportedTypesMap[type]) {
        return this.supportedTypesMap[type].name;
      }
      
      // 兼容性处理
      const nameMap = {
        mysql: 'MySQL',
        oracle: 'Oracle',
        postgresql: 'PostgreSQL'
      }
      return nameMap[type] || type
    },
    // 获取连接示例
    getConnectionExample(type) {
      if (!type || !this.supportedTypesMap[type]) return '';
      
      const template = this.supportedTypesMap[type].jdbcUrlTemplate || '';
      if (this.jdbcPrefix && template.startsWith(this.jdbcPrefix)) {
        // 返回模板中去掉前缀的部分
        return template.substring(this.jdbcPrefix.length);
      }
      
      return template;
    },
    // 获取数据源信息
    async fetchDatasource() {
      const id = this.$route.params.id
      this.loading = true
      try {
        const data = await getDataSourceDetail(id)
        if (data) {
          this.form = {
            id: data.id,
            type: data.type.toLowerCase(),
            name: data.name,
            code: data.code,
            description: data.description,
            host: data.host,
            port: data.port,
            serverName: data.serverName,
            defaultSchema: data.defaultSchema,
            arg: data.arg || '',
            username: data.username,
            password: data.password || '',
            schemaConfig: {
              type: 'physical',
              selected: []
            },
            driverClassName: data.driverClassName || '',
            groupName: data.groupName || '默认分组'
          }
          
          // 获取JDBC URL预览
          this.refreshJdbcUrlPreview();
        }
      } catch (error) {
        this.$message.error('获取数据源信息失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    // 获取表单数据
    getFormData() {
      return {
        id: this.form.id,
        type: this.form.type.toUpperCase(),
        name: this.form.name,
        code: this.form.code,
        description: this.form.description,
        host: this.form.host,
        port: this.form.port,
        serverName: this.form.serverName,
        defaultSchema: this.form.defaultSchema,
        arg: this.form.arg,
        username: this.form.username,
        password: this.form.password,
        driverClassName: this.form.driverClassName || this.getDefaultDriver(this.form.type),
        groupName: this.form.groupName
      }
    },
    // 刷新JDBC URL预览
    async refreshJdbcUrlPreview() {
      const data = await previewJdbcUrl(this.getFormData());
      this.previewJdbcUrl = data;
    },
    // 测试连接
    async testConnection() {
      try {
        await this.$refs.editForm.validate()
      } catch (error) {
        return
      }

      this.testing = true
      try {
        const res = await testDataSourceConnection(this.getFormData())
        this.$message.success('连接成功')
      } finally {
        this.testing = false
      }
    },
    // 保存修改
    async saveChanges() {
      try {
        await this.$refs.editForm.validate()
      } catch (error) {
        return
      }

      this.saving = true
      try {
        await updateDataSource(this.getFormData())
        this.$message.success('保存成功')
        this.goBack()
      } finally {
        this.saving = false
      }
    },
    // 返回列表页
    goBack() {
      this.$router.push('/data/datasource')
    },
    copyExample() {
      const text = this.jdbcPrefix + this.getConnectionExample(this.form.type)
      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('示例已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },
    copyGeneratedUrl() {
      navigator.clipboard.writeText(this.generatedJdbcUrl).then(() => {
        this.$message.success('JDBC URL已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },
    // 获取默认驱动
    getDefaultDriver(type) {
      if (!type || !this.supportedTypesMap[type]) return '';
      return this.supportedTypesMap[type].driverClassName || '';
    }
  }
}
</script>

<style lang="scss" scoped>
.datasource-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  min-height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eef1f7;

    .header-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .back-button {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #409EFF;
        transition: all 0.3s;
        font-size: 14px;
        font-weight: 500;

        i {
          margin-right: 6px;
          font-size: 14px;
        }

        &:hover {
          color: #66b1ff;
          transform: translateX(-3px);
        }
      }
    }
  }

  .edit-form {
    width: 100%;
    margin: 0 auto;
  }

  .form-section {
    width: 100%;
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 22px;
        
        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          line-height: 1.4;
          padding-bottom: 8px;
          color: #1a1f36;
          font-weight: 500;
          display: flex;
          align-items: center;
          height: 38px;
          justify-content: flex-end;
          text-align: right;
        }

        .el-input,
        .el-select,
        .el-cascader {
          width: 100%;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-input-group__prepend,
        .el-input-group__append {
          background-color: #f5f7fa;
          border-color: #e0e5ee;
          color: #909399;
          padding: 0 12px;
        }
        
        .el-input-group__prepend {
          border-top-left-radius: 10px;
          border-bottom-left-radius: 10px;
        }
        
        .el-input-group__append {
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
        }

        .el-textarea__inner {
          border-radius: 10px;
          border-color: #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-select,
        .el-cascader {
          .el-input {
            width: 100%;
          }
        }
      }
    }
  }

  .type-display {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 12px;
    height: 38px;
    line-height: 38px;
    border: 1px solid #e0e5ee;
    border-radius: 10px;
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;

    i {
      color: #409EFF;
      font-size: 18px;
    }
  }

  .url-example {
    margin-top: 8px;
    padding: 12px;
    background: #f8f9fb;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      background: #ecf5ff;

      .copy-btn {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .example-header {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 8px;
      color: #909399;
      font-size: 13px;

      i {
        color: #409EFF;
      }
    }

    .example-content {
      display: flex;
      align-items: center;
      font-family: Monaco, Menlo, Consolas, monospace;
      font-size: 13px;
      line-height: 1.5;
      word-break: break-all;
      
      .prefix {
        color: #909399;
        flex-shrink: 0;
      }
      
      .content {
        color: #409EFF;
        margin-left: 4px;
        flex: 1;
      }

      .copy-btn {
        flex-shrink: 0;
        padding: 4px 8px;
        opacity: 0;
        transform: translateX(10px);
        transition: all 0.3s;
        
        i {
          margin-right: 4px;
        }

        &:hover {
          background: rgba(64, 158, 255, 0.1);
          border-radius: 4px;
        }
      }
    }
  }

  .jdbc-prefix {
    color: #909399;
    user-select: none;
  }

  .driver-tip,
  .form-tip {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    color: #909399;
    font-size: 13px;

    i {
      color: #409EFF;
    }
  }

  .full-width-select {
    width: 100%;
  }

  .form-actions {
    margin-top: 24px;
    padding: 24px 0 0;
    text-align: center;
    border-top: 1px solid #eef1f7;

    .el-button {
      min-width: 120px;
      padding: 10px 20px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      border-radius: 10px;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      & + .el-button {
        margin-left: 16px;
      }
      
      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .jdbc-preview {
    padding: 12px 16px;
    background: #f8f9fb;
    border: 1px solid #e0e5ee;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s;
    font-family: Monaco, Menlo, Consolas, monospace;

    &:hover {
      background: #ecf5ff;
      border-color: #409EFF;
    }

    .preview-content {
      color: #606266;
      flex: 1;
      margin-right: 16px;
      word-break: break-all;
    }

    .preview-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
      
      .refresh-btn,
      .copy-btn {
        padding: 4px 8px;
        
        i {
          margin-right: 4px;
        }

        &:hover {
          background: rgba(64, 158, 255, 0.1);
          border-radius: 4px;
        }
      }
    }
  }
}
</style> 
