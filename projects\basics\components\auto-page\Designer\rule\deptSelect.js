import uniqueId from '@form-create/utils/lib/unique';
import { localeProps } from '@form-create/designer-zrw/src/utils';

const label = '部门选择';
const name = 'deptSelect';

export default {
    menu: 'custom',
    icon: 'icon-editor',
    label,
    name,
    event: ['change'],
    validate: ['string'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            $required: false,
        };
    },
    props(_, { t }) {

        return localeProps(t, 'custom.props', [
            {
                type: 'switch',
                field: 'disabled',
                title: t('custom.props.disabled')
            }]);
    }
};
