<template></template>
<script>
export default {
    computed: {
        path() {
            let routers = this.$store.getters.sidebarRouters.filter(f => f.hidden == false)
            if (routers.length > 0) {
                let re = routers[0]
                let list = []
                this.findPathList(list, re)
                return list.length < 1 ? '/404' : list.join('/')
            }
            return '/404'
        }
    },
    created() {
        this.$router.replace(this.path)
    },
    methods: {
        findPathList(list, re) {
            if (re.path != '/') {
                list.push(re.path)
            }
            if (re.children && re.children.length > 0) {
                let chList = re.children.filter(f => f.hidden == false)
                if (chList.length > 0) {
                    this.findPathList(list, chList[0])
                }
            }
        }
    },
}
</script>