<template>
  <div class="connector-form-container">

    <el-page-header @back="$router.replace('/sys/connector')" :content="`${isEdit ? '编辑' : '添加'}连接器`">
    </el-page-header>

    <el-form ref="connectorForm" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="连接器名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入连接器名称" />
      </el-form-item>
      <el-form-item label="连接器编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入连接器编码" />
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <el-select v-model="formData.icon" placeholder="请选择图标">
          <el-option v-for="icon in iconOptions" :key="icon.value" :label="icon.label" :value="icon.value">
            <i :class="icon.value" style="margin-right: 8px;"></i>
            {{ icon.label }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="图标颜色" prop="iconColor">
        <el-color-picker v-model="formData.iconColor"></el-color-picker>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input v-model="formData.description" placeholder="请输入描述"></el-input>
      </el-form-item>

      <div class="switch-group">
        <el-form-item label="启用二维码" prop="qrCodeEnabled">
          <el-switch v-model="formData.qrCodeEnabled" />
        </el-form-item>

        <el-form-item label="登录页显示" prop="displayOnLoginPage">
          <el-switch v-model="formData.displayOnLoginPage" />
        </el-form-item>

        <el-form-item label="绑定页显示" prop="displayOnBindPage">
          <el-switch v-model="formData.displayOnBindPage" />
        </el-form-item>

        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="formData.enabled" />
        </el-form-item>
      </div>

      <el-form-item label="绑定插件" prop="pluginBeanName">
        <el-select v-model="formData.pluginBeanName" placeholder="请选择插件" @change="handlePluginChange">
          <el-option v-for="item in plugins" :key="item.pluginBeanName" :label="item.name" :value="item.pluginBeanName">
            <span>{{ item.name }}</span>
            <el-tag size="mini" type="success" class="plugin-type-tag">
              {{ item.type }}
            </el-tag>
          </el-option>
        </el-select>
      </el-form-item>

      <template v-if="formData.pluginBeanName === 'OAuthPlugin'">
        <el-form-item label="Client ID" prop="jsonConfig.clientId">
          <el-input v-model="formData.jsonConfig.clientId" placeholder="请输入 Client ID" />
          <el-input v-model="formData.jsonConfig.clientIdAlias" placeholder="请输入参数别名,例如: app_id" class="mt-2" />
          <div class="form-tip">参数别名: 用于适配不同平台的参数名称,非必填</div>
        </el-form-item>

        <el-form-item label="Client Secret" prop="jsonConfig.clientSecret">
          <el-input v-model="formData.jsonConfig.clientSecret" type="password" placeholder="请输入 Client Secret"
            show-password />
          <el-input v-model="formData.jsonConfig.clientSecretAlias" placeholder="请输入参数别名,例如: app_secret" class="mt-2" />
          <div class="form-tip">参数别名: 用于适配不同平台的参数名称,非必填</div>
        </el-form-item>

        <el-form-item label="回调地址" prop="jsonConfig.redirectUri">
          <el-input v-model="formData.jsonConfig.redirectUri" placeholder="请输入回调地址">
            <template slot="append">
              <el-button type="text" @click="handleCopyUrl">
                <i class="el-icon-document-copy"></i>
                复制
              </el-button>
            </template>
          </el-input>
          <el-input v-model="formData.jsonConfig.redirectUriAlias" placeholder="请输入参数别名,例如: redirect_uri"
            class="mt-2" />
          <div class="form-tip">参数别名: 用于适配不同平台的参数名称,非必填</div>
        </el-form-item>


        <el-form-item label="授权接口" prop="jsonConfig.authUrl">
          <el-input v-model="formData.jsonConfig.authUrl" placeholder="请输入授权接口地址">
            <template slot="prepend">GET</template>
          </el-input>
        </el-form-item>

        <el-form-item label="State" prop="jsonConfig.state">
          <el-input v-model="formData.jsonConfig.state" placeholder="请输入状态参数">
          </el-input>
        </el-form-item>

        <el-form-item label="授权范围" prop="jsonConfig.scope">
          <el-input v-model="formData.jsonConfig.scope" placeholder="请输入授权范围,多个用空格分隔" />
          <div class="form-tip">
            例如: snsapi_login
          </div>
        </el-form-item>


        <el-form-item label="Token接口" prop="jsonConfig.tokenUrl">
          <el-input v-model="formData.jsonConfig.tokenUrl" placeholder="请输入获取Token接口地址">
            <template slot="prepend">POST</template>
          </el-input>
          <div class="form-tip">
            <el-link type="info" icon="el-icon-info" @click="showJsonPathHelp">
              查看JsonPath语法说明
            </el-link>
          </div>
          <el-input v-model="formData.jsonConfig.tokenExtract" placeholder="请输入access_token提取表达式，例如: $.access_token" />
        </el-form-item>

        <el-form-item label="用户信息接口" prop="jsonConfig.userInfoUrl">
          <el-input v-model="formData.jsonConfig.userInfoUrl" placeholder="请输入获取用户信息接口地址">
            <template slot="prepend">GET</template>
          </el-input>
        </el-form-item>

        <el-form-item label="字段映射" prop="jsonConfig.fieldMapping">
          <div class="field-mapping">
            <div v-for="(mapping, index) in formData.jsonConfig.fieldMapping" :key="index" class="mapping-row">
              <el-input v-model="mapping.sysFiledName" placeholder="系统字段名" class="mapping-key" />
              <span class="mapping-arrow">
                <i class="el-icon-right"></i>
              </span>
              <el-input v-model="mapping.jsonPath" placeholder="JsonPath提取表达式" class="mapping-value" />
              <el-button type="text" icon="el-icon-delete" class="mapping-delete" @click="removeMapping(index)" />
            </div>
          </div>
          <div class="mapping-actions">
            <el-button type="text" @click="handleAddMapping">
              <i class="el-icon-plus"></i>
              添加字段映射
            </el-button>
          </div>
        </el-form-item>
      </template>

      <template v-if="formData.pluginBeanName === 'WeChatOfficialAuthPlugin'">
        <el-form-item label="AppID" prop="jsonConfig.appId">
          <el-input v-model="formData.jsonConfig.appId" placeholder="请输入微信公众号的 AppID" />
        </el-form-item>

        <el-form-item label="Secret" prop="jsonConfig.secret">
          <el-input v-model="formData.jsonConfig.secret" type="password" placeholder="请输入微信公众号的 Secret" show-password />
        </el-form-item>

        <el-form-item label="消息Token" prop="jsonConfig.token">
          <el-input v-model="formData.jsonConfig.token" placeholder="请输入微信公众号消息Token" />
          <div class="form-tip">此Token并非accessToken，而是微信后台配置的消息验签Token</div>
        </el-form-item>
      </template>

      <template v-if="formData.pluginBeanName === 'DingTalkAuthPlugin'">
        <el-form-item label="AppKey" prop="jsonConfig.appKey">
          <el-input v-model="formData.jsonConfig.appKey" placeholder="请输入钉钉应用的 AppKey" />
        </el-form-item>

        <el-form-item label="AppSecret" prop="jsonConfig.appSecret">
          <el-input v-model="formData.jsonConfig.appSecret" type="password" placeholder="请输入钉钉应用的 AppSecret"
            show-password />
        </el-form-item>

        <el-form-item label="授权接口" prop="jsonConfig.authUrl">
          <el-input v-model="formData.jsonConfig.authUrl" placeholder="请输入钉钉授权接口地址">
            <template slot="prepend">GET</template>
          </el-input>
        </el-form-item>

        <el-form-item label="回调地址" prop="jsonConfig.redirectUri">
          <el-input v-model="formData.jsonConfig.redirectUri" placeholder="请输入回调地址">
            <template slot="append">
              <el-button type="text" @click="handleCopyUrl">
                <i class="el-icon-document-copy"></i>
                复制
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </template>
      <!-- TODO 微信小程序 -->
      <template v-if="formData.pluginBeanName === 'WeChatMiniAuthPlugin'">
        <el-form-item label="AppID" prop="jsonConfig.appId">
          <el-input v-model="formData.jsonConfig.appId" placeholder="请输入微信小程序的 AppID" />
        </el-form-item>

        <el-form-item label="Secret" prop="jsonConfig.secret">
          <el-input v-model="formData.jsonConfig.secret" type="password" placeholder="请输入微信小程序的 Secret" show-password />
        </el-form-item>

        <el-form-item label="用户默认密码" prop="jsonConfig.defaultPassword">
          <el-input v-model="formData.jsonConfig.defaultPassword" placeholder="请输入用户默认密码" />
          <div class="form-tip">未配置时默认为123456</div>
        </el-form-item>

        <el-form-item label="默认角色" prop="jsonConfig.defaultRoleIds">
          <el-select v-model="formData.jsonConfig.defaultRoleIds" multiple placeholder="请选择默认角色" clearable>
            <el-option
              v-for="item in roleOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="默认部门" prop="jsonConfig.defaultDeptId">
          <el-cascader
            v-model="formData.jsonConfig.defaultDeptId"
            :options="deptOptions"
            :props="{
              checkStrictly: true,
              label: 'name',
              value: 'id',
              emitPath: false
            }"
            placeholder="请选择默认部门"
            clearable
            style="width: 100%"
          >
            <template slot-scope="{ node, data }">
              <i class="el-icon-office-building"></i>
              <span>{{ data.name }}</span>
            </template>
          </el-cascader>
        </el-form-item>

        <el-form-item label="默认数据权限" prop="jsonConfig.defaultScope">
          <el-select v-model="formData.jsonConfig.defaultScope" placeholder="请选择默认数据权限">
            <el-option label="仅本人数据权限" value="SELF" />
            <el-option label="本部门数据权限" value="DEPT" />
            <el-option label="本部门及以下数据权限" value="DEPT_CHILD" />
            <el-option label="自定义数据权限" value="CUSTOM" />
            <el-option label="全部数据权限" value="ALL" />
          </el-select>
        </el-form-item>

        <el-form-item label="数据权限部门" prop="jsonConfig.defaultDeptIds" v-if="formData.jsonConfig.defaultScope === 'CUSTOM'">
          <el-cascader
            v-model="formData.jsonConfig.defaultDeptIds"
            :options="deptOptions"
            :props="{
              checkStrictly: true,
              label: 'name',
              value: 'id',
              multiple: true,
              emitPath: false
            }"
            clearable
            placeholder="请选择数据权限部门"
            style="width: 100%"
          >
            <template slot-scope="{ node, data }">
              <i class="el-icon-office-building"></i>
              <span>{{ data.name }}</span>
            </template>
          </el-cascader>
        </el-form-item>
      </template>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="$router.push('/sys/connector')">取消</el-button>
      </el-form-item>
    </el-form>

    <!-- JsonPath帮助对话框 -->
    <el-dialog title="JsonPath 使用帮助" :visible.sync="jsonPathHelpVisible" width="600px" :append-to-body="true">
      <div class="jsonpath-help">
        <h4>JsonPath 语法示例:</h4>
        <el-table :data="jsonPathExamples" border>
          <el-table-column prop="expression" label="表达式" />
          <el-table-column prop="description" label="说明" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getConnectorDetail, createConnector, updateConnector } from '@system/api/sys/connector'
import { getPlugins } from '@system/api/sys/auth'
import { getDeptTreeList } from '@system/api/sys/treeDept'
import { getRoleList } from '@system/api/sys/role'

export default {
  name: 'ConnectorForm',
  data() {
    return {
      isEdit: false,
      jsonPathHelpVisible: false,
      plugins: [],
      formData: {
        name: '',
        code: '',
        enabled: true,
        qrCodeEnabled: false,
        displayOnLoginPage: true,
        displayOnBindPage: true,
        description: '',
        icon: '',
        pluginBeanName: '',
        jsonConfig: {
          clientId: '',
          clientIdAlias: '',
          clientSecret: '',
          clientSecretAlias: '',
          redirectUri: `${window.location.origin}/oauth/callback`,
          redirectUriAlias: '',
          authUrl: '',
          scope: '',
          state: '',
          tokenUrl: '',
          tokenExtract: '',
          userInfoUrl: '',
          fieldMapping: [
            {
              sysFiledName: '',
              jsonPath: ''
            }
          ],
          appId: '',
          secret: '',
          token: ''
        }
      },
      rules: {
        name: [
          { required: true, message: '请输入连接器名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入连接器编码', trigger: 'blur' }
        ],
        'jsonConfig.clientId': [
          { required: true, message: '请输入Client ID', trigger: 'blur' }
        ],
        'jsonConfig.clientSecret': [
          { required: true, message: '请输入Client Secret', trigger: 'blur' }
        ],
        'jsonConfig.redirectUri': [
          { required: true, message: '请输入回调地址', trigger: 'blur' }
        ],
        'jsonConfig.authUrl': [
          { required: true, message: '请输入授权接口地址', trigger: 'blur' }
        ],
        'jsonConfig.tokenUrl': [
          { required: true, message: '请输入Token接口地址', trigger: 'blur' }
        ],
        'jsonConfig.userInfoUrl': [
          { required: true, message: '请输入用户信息接口地址', trigger: 'blur' }
        ],
        'jsonConfig.fieldMapping': [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请至少添加一个字段映射'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        pluginBeanName: [
          { required: true, message: '请选择插件', trigger: 'change' }
        ],
        'jsonConfig.appId': [
          { required: true, message: '请输入AppID', trigger: 'blur' }
        ],
        'jsonConfig.secret': [
          { required: true, message: '请输入Secret', trigger: 'blur' }
        ],
        'jsonConfig.token': [
          { required: true, message: '请输入消息Token', trigger: 'blur' }
        ],
        'jsonConfig.appKey': [
          { required: true, message: '请输入AppKey', trigger: 'blur' }
        ],
        'jsonConfig.appSecret': [
          { required: true, message: '请输入AppSecret', trigger: 'blur' }
        ],
        'jsonConfig.defaultDeptId': [
          { required: true, message: '请选择默认部门', trigger: 'change' }
        ],
        'jsonConfig.defaultScope': [
          { required: true, message: '请选择默认数据权限', trigger: 'change' }
        ],
        'jsonConfig.defaultRoleIds': [
          { required: true, message: '请选择默认角色', trigger: 'change', type: 'array' }
        ]
      },
      jsonPathExamples: [
        {
          expression: '$.access_token',
          description: '提取根节点下的access_token字段'
        },
        {
          expression: '$.data.userinfo.id',
          description: '提取多层嵌套下的id字段'
        },
        {
          expression: '$.list[0].name',
          description: '提取数组第一个元素的name字段'
        }
      ],
      iconOptions: [
        { label: '微信', value: 'iconfont ali-icon-weixin' },
        { label: '钉钉', value: 'iconfont ali-icon-dingding01' },
        { label: '飞书', value: 'el-icon-message-solid' },
        { label: '默认', value: 'el-icon-connection' }
      ],
      pluginConfigs: {},
      roleOptions: [],
      deptOptions: []
    }
  },
  async created() {
    this.isEdit = !!this.$route.params.id
    await this.loadPlugins()
    await this.loadRoleOptions()
    await this.loadDeptOptions()
    if (this.isEdit) {
      this.getConnectorDetail()
    }
  },
  methods: {
    async getConnectorDetail() {
      try {
        const data = await getConnectorDetail({ id: this.$route.params.id })
        this.formData = data
      } catch (error) {
        this.$message.error('获取连接器详情失败')
      }
    },
    async handleSubmit() {
      try {
        const vaild = await this.$refs.connectorForm.validate()
        if (vaild) {
          const method = this.isEdit ? updateConnector : createConnector

          // 直接传入整个表单数据
          await method(this.formData)

          this.$message.success('保存成功')
          this.$router.push('/sys/connector')
        }
      } catch (error) {
      }

    },
    handleCopyUrl() {
      navigator.clipboard.writeText(this.formData.jsonConfig.redirectUri)
        .then(() => {
          this.$message.success('复制成功')
        })
        .catch(() => {
          this.$message.error('复制失败')
        })
    },
    showJsonPathHelp() {
      this.jsonPathHelpVisible = true
    },
    handleAddMapping() {
      if (!this.formData.jsonConfig.fieldMapping) {
        this.$set(this.formData.jsonConfig, 'fieldMapping', [])
      }
      this.formData.jsonConfig.fieldMapping.push({
        sysFiledName: '',
        jsonPath: ''
      })
    },
    removeMapping(index) {
      if (this.formData.jsonConfig.fieldMapping.length > 1) {
        this.formData.jsonConfig.fieldMapping.splice(index, 1)
      } else {
        this.$message.warning('至少保留一个字段映射')
      }
    },
    async loadPlugins() {
      try {
        const data = await getPlugins()
        this.plugins = data
      } catch (error) {
        this.$message.error('获取插件列表失败')
      }
    },
    async loadRoleOptions() {
      try {
        const data = await getRoleList({
          current: 1,
          size: -1 // 获取所有角色
        })
        this.roleOptions = (data?.records || []).map(item => ({
          id: item.id,
          name: item.name,
          code: item.code
        }))
      } catch (error) {
        this.$message.error('获取角色列表失败')
      }
    },
    async loadDeptOptions() {
      try {
        const data = await getDeptTreeList()
        this.deptOptions = data || []
      } catch (error) {
        this.$message.error('获取部门列表失败')
      }
    },
    handlePluginChange(value) {
      const selectedPlugin = this.plugins.find(p => p.pluginBeanName === value)
      if (selectedPlugin && selectedPlugin.type !== '认证插件') {
        this.$message.warning('暂不支持非认证插件绑定')
        this.formData.pluginBeanName = ''
        return
      }

      // 保存当前配置
      if (this.formData.pluginBeanName) {
        this.pluginConfigs[this.formData.pluginBeanName] = { ...this.formData.jsonConfig }
      }

      // 如果有之前保存的配置，使用之前的配置
      if (this.pluginConfigs[value]) {
        this.formData.jsonConfig = { ...this.pluginConfigs[value] }
      } else {
        // 如果没有之前的配置，使用默认配置
        this.formData.jsonConfig = {
          ...(value === 'OAuthPlugin' ? {
            clientId: '',
            clientIdAlias: '',
            clientSecret: '',
            clientSecretAlias: '',
            redirectUri: `${window.location.origin}/oauth/callback`,
            redirectUriAlias: '',
            authUrl: '',
            scope: '',
            tokenUrl: '',
            tokenExtract: '',
            userInfoUrl: '',
            fieldMapping: [
              {
                sysFiledName: '',
                jsonPath: ''
              }
            ]
          } : value === 'WeChatOfficialAuthPlugin' ? {
            appId: '',
            secret: '',
            token: ''
          } : value === 'DingTalkAuthPlugin' ? {
            appKey: '',
            appSecret: '',
            authUrl: '',
            redirectUri: `${window.location.origin}/oauth/callback`
          } : value === 'WeChatMiniAuthPlugin' ? {
            appId: '',
            secret: '',
            defaultPassword: '',
            defaultRoleIds: [],
            defaultDeptId: '',
            defaultScope: 'SELF',
            defaultDeptIds: []
          } : {})
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.connector-form-container {
  margin: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;

  // 开关组样式
  .switch-group {
    display: flex;
    gap: 32px;
    padding: 16px 24px;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 24px;

    .el-form-item {
      margin-bottom: 0;
      flex: 1;
    }

    ::v-deep .el-form-item__label {
      color: #606266;
    }

    ::v-deep .el-switch {
      .el-switch__core {
        width: 44px !important;
        height: 22px;
        border-radius: 22px;

        &:after {
          width: 18px;
          height: 18px;
          top: 1px;
        }
      }

      &.is-checked {
        .el-switch__core {
          background-color: #67c23a;
          border-color: #67c23a;

          &:after {
            margin-left: -19px;
          }
        }
      }
    }
  }

  // 表单布局优化
  ::v-deep .el-form {
    max-width: 800px;
    margin: 0 auto;

    .el-form-item {
      margin-bottom: 24px;

      &__label {
        font-weight: 500;
        padding-right: 24px;
      }

      &__content {

        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }

  // 卡片样式
  .el-card {
    border-radius: 8px;
    border: none;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    ::v-deep .el-card__body {
      padding: 32px;
    }
  }

  // 按钮样式
  ::v-deep .el-button {
    padding: 10px 24px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s;

    &--primary {
      background: #409EFF;
      border-color: #409EFF;

      &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
        transform: translateY(-1px);
      }
    }
  }

  .form-tip {
    margin-top: 4px;
    color: #909399;
    font-size: 12px;
    line-height: 1.4;
  }

  .field-mapping {
    .mapping-row {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .mapping-key,
      .mapping-value {
        width: 300px;
      }

      .mapping-arrow {
        padding: 0 16px;
        color: #909399;
      }

      .mapping-delete {
        margin-left: 8px;
        font-size: 16px;
        color: #F56C6C;
      }
    }
  }

  .mapping-actions {
    margin-top: 8px;

    .el-button {
      padding-left: 0;
    }
  }

  .jsonpath-help {
    h4 {
      margin-top: 0;
      margin-bottom: 16px;
    }
  }

  .mt-2 {
    margin-top: 8px;
  }

  .plugin-type-tag {
    float: right;
    margin-left: 10px;
  }
}
</style>