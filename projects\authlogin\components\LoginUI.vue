<template>
    <div>
        <div v-if="uiType == 'LoginUI' || uiType == 'QrCodeUI'" class="login-ui-container">
          <div class="title-container">
            <svg-icon
                v-if="isSvgIcon"
                :icon-class="systemIcon"
                class="logo"
            />
            <img
                v-else-if="isImageLink"
                :src="resolvedSystemIcon"
                alt="logo"
                class="logo"
            />
            <img
                v-else
                :src="logo"
                alt="logo"
                class="logo"
            />
            <div class="title" v-text="resolvedTitle"></div>
          </div>
          <div v-if="uiType == 'QrCodeUI'">
                <QrCodeUI @goBack="uiType = 'LoginUI'" :connector="currentConnector" v-on="$listeners"></QrCodeUI>
            </div>
            <template v-else>
                <div class="tab-container" v-if="tabs.length > 1" :tabLength="tabs.length">
                    <div v-for="(item, index) in tabs" :key="index" class="tab"
                        :class="{ 'act_tab': tabIndex == index }" @click="changeTab(index)">
                        <img :src="tabIndex == index ? item.ic_act : item.ic" />
                        {{ item.name }}
                    </div>
                </div>
                <div class="content-container">
                    <component :is="tabs[tabIndex].component" v-on="$listeners" :isAgree="isAgree"
                        :configKeys="configKeys">
                    </component>
                </div>

                <div class="quick-login-container" v-if="configKeys['builtin.quick.login']">
                    <QuickLogin :isAgree="isAgree" :configKeys="configKeys"
                        @goQrCodeUI="(connector) => { uiType = 'QrCodeUI'; currentConnector = connector }">
                    </QuickLogin>
                </div>

                <div class="register-container"
                    v-if="configKeys['builtin.register.email'] || configKeys['builtin.register.phone']">
                    <span>还没有账号？</span>
                    <span class="transition-all rbtn" @click="uiType = 'RegisterUI'">立即注册</span>
                </div>

                <div class="user-agreement-container" v-if="configKeys['builtin.user.agreement']">
                    <el-checkbox v-model="isAgree"></el-checkbox>
                    勾选即表示同意
                    <span class="transition-all">用户协议</span> 和
                    <span class="transition-all">隐私政策</span>
                </div>
            </template>
        </div>

        <div v-if="uiType == 'ResetPwdUI'">
            <ResetPwdUI @goBack="uiType = 'LoginUI'" :configKeys="configKeys" />
        </div>

        <div v-if="uiType == 'RegisterUI'">
            <RegisterUI @goBack="uiType = 'LoginUI'" :configKeys="configKeys" />
        </div>
        <div class="version">v{{ $store.getters.version }}</div>
    </div>

</template>
<script>
import RegisterUI from './RegisterUI.vue'
import ResetPwdUI from './ResetPwdUI.vue'
import QrCodeUI from './QrCodeUI.vue'
import QuickLogin from './QuickLogin/index.vue'
import { getConfigByKeys } from '../api/auth'


export default {
    components: {
        RegisterUI,
        ResetPwdUI,
        QrCodeUI,
        QuickLogin
    },
    props: {
        logo: {
            type: String,
            default: require('../assets/logo.svg')
        },
        title: {
            type: String,
            default: 'ExtremePlus 统一身份认证中心'
        }
    },
    data() {
        const defTabs = [
            {
                name: '账号密码',
                component: () => import('./AccountLogin.vue'),
                ic: require('../assets/ic_pc.svg'),
                ic_act: require('../assets/ic_pc_act.svg'),
            },
            {
                name: '手机验证码',
                component: () => import('./PhoneLogin.vue'),
                ic: require('../assets/ic_phone.svg'),
                ic_act: require('../assets/ic_phone_act.svg'),
                configKey: 'builtin.phone.login'
            },
            {
                name: '邮箱登录',
                component: () => import('./EmailLogin.vue'),
                ic: require('../assets/ic_email.svg'),
                ic_act: require('../assets/ic_email_act.svg'),
                configKey: 'builtin.email.login'
            },
        ]
        return {
            uiType: 'LoginUI',
            defTabs,
            tabs: [
                defTabs[0]
            ],
            tabIndex: 0,
            isAgree: false,
            isRegister: true,
            currentConnector: {},
            configKeys: {}
        }
    },
    watch: {
        uiType(n) {
            if (n === 'LoginUI') {
                this.getConfigByKeys()
            }
        }
    },
    created() {
        this.getConfigByKeys()
    },
  computed: {
    systemIcon() {
      return this.configKeys['builtin.ui.logo']?.trim() || '';
    },
    isSvgIcon() {
      return this.systemIcon &&
          !/^https?:\/\//.test(this.systemIcon) &&
          !this.systemIcon.includes('/');
    },
    isImageLink() {
      return this.systemIcon &&
          (/^https?:\/\//.test(this.systemIcon) || this.systemIcon.includes('/'));
    },
    resolvedSystemIcon() {
      if (!this.systemIcon) return '';
      return /^https?:\/\//.test(this.systemIcon)
          ? this.systemIcon
          : `${process.env.VUE_APP_FILE_URL}/${this.systemIcon.replace(process.env.VUE_APP_FILE_URL + '/', '')}`;
    },
    resolvedTitle() {
      const title = this.configKeys['builtin.ui.title'];
      return title && title.length > 2 ? title : this.title;
    }
  },
  methods: {
        changeTab(index) {
            this.tabIndex = index
        },
        getConfigByKeys() {
            getConfigByKeys(['builtin.register.phone', 'builtin.register.email', 'builtin.forget.phone',
                'builtin.forget.email', 'builtin.user.agreement', 'builtin.captcha.type',
                'builtin.quick.login', 'builtin.phone.login', 'builtin.email.login',
                'builtin.ui.logo', 'builtin.ui.title'].join(',')).then(res => {
                    this.configKeys = res || {}
                    this.filterTabs()
                }).catch(err => {
                    this.filterTabs()
                })
        },
        filterTabs() {
            this.tabs = this.defTabs.filter(tab => {
                if (tab.configKey) {
                    return this.configKeys[tab.configKey]
                } else {
                    return true
                }
            })
        }
    }

}
</script>

<style lang="scss" scoped>
.transition-all {
    transition: all 0.3s;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.version {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: rgb(14, 139, 211);
}

.login-ui-container {
    border-width: 1px;
    padding: 32px;
    border-radius: 20px;
    min-width: 480px;
    max-width: 600px;
    min-height: 550px;
    background-color: #fff;
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);

    .title-container {
        display: flex;
        flex-direction: column;
        align-items: center;

        .logo {
            width: 60px;
            height: 60px;
        }

        .title {
            color: rgb(31, 41, 55);
            font-size: 1.25vw;
            line-height: 1.66667vw;
            font-weight: 700;
            margin-top: 20px;
        }
    }

    .tab-container {
        margin-top: 32px;
        overflow-x: auto;
        gap: 16px;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        max-width: 520px;
        width: 100%;
        display: grid;


        .tab {
            color: rgb(75, 85, 99);
            text-align: center;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 20px;
            cursor: pointer;
            pointer-events: auto;

            img {
                width: 14px;
                vertical-align: middle;
                margin-right: 5px;
            }

            &:hover {
                background-color: rgb(243 244 246);
            }
        }

        .act_tab {
            background-color: rgb(0, 122, 255);
            color: #fff;

            &:hover {
                background-color: rgb(0 122 255);
            }
        }
    }

    .tab-container[tabLength='2'] {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }

    .content-container {
        margin-top: 20px;
        min-height: 300px;
    }

    .register-container {
        text-align: center;
        font-size: 14px;
        color: rgb(75, 85, 99);

        .rbtn {
            color: rgb(0, 122, 255);
            cursor: pointer;

            &:hover {
                color: rgb(0, 86, 179);
                transform: scale(1.05);
            }
        }
    }

    .user-agreement-container {
        text-align: center;
        font-size: 14px;
        color: rgb(107, 114, 128);
        margin-top: 28px;

        span {
            color: rgb(0, 122, 255);
            cursor: pointer;

            &:hover {
                color: rgb(0, 86, 179);
                transform: scale(1.05);
            }
        }
    }
}
</style>