import request from '@/utils/request'

const integrationConnectorApi = CONSTANT.INTEGRATION + '/integration/connector'


// 获取集成连接器列表
export function getIntegrationConnectorList(params) {
  return request({
    url: `${integrationConnectorApi}/list`,
    method: 'get',
    params
  })
}

// 获取单个集成连接器基本信息
export function getIntegrationConnectorDetail(id) {
  return request({
    url: `${integrationConnectorApi}`,
    method: 'get',
    params: { id }
  })
}

// 创建集成连接器
export function createIntegrationConnector(data) {
  return request({
    url: `${integrationConnectorApi}`,
    method: 'post',
    data
  })
}

// 更新集成连接器
export function updateIntegrationConnector(data) {
  return request({
    url: `${integrationConnectorApi}`,
    method: 'put',
    data
  })
}

// 删除集成连接器
export function deleteIntegrationConnector(id) {
  return request({
    url: `${integrationConnectorApi}/${id}`,
    method: 'delete'
  })
}

// 更新连接器状态
export function updateIntegrationConnectorStatus(id, enabled) {
  return request({
    url: `${integrationConnectorApi}/status`,
    method: 'put',
    data: { id, enabled }
  })
}


// ------------------- 认证执行器 -------------------

// 获取所有认证执行器
export function getAuthConnectors() {
  return request({
    url: `${integrationConnectorApi}/auth/connectors`,
    method: 'get'
  })
}

// 根据连接器类型获取认证执行器
export function getAuthActuatorsByType(connectorType) {
  return request({
    url: `${integrationConnectorApi}/auth/actuators`,
    method: 'get',
    params: { connectorType }
  })
}

// 测试连接器连接
export function testIntegrationConnectorConnection(data) {
  return request({
    url: `${integrationConnectorApi}/test/connection`,
    method: 'post',
    data
  })
}
