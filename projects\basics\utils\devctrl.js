let reg = /\$\{(\S+)\}/

/**
 * 将el表达式转换为值
 * @param {*} deviceinfo    设备信息
 * @param {*} req           包含表达式的表单
 * @returns                 表单实值
 */
export default function control(deviceinfo, req) {
  var data = {}
  if (deviceinfo && req) {
    data = JSON.parse(JSON.stringify(req))
    Object.keys(data).forEach(key=>{
      var value = data[key]
      if (value && value.search(reg) > -1) {
        var match = value.match(reg)
        var newval = deviceinfo[match[1]] ? deviceinfo[match[1]] : deviceinfo.detail ? deviceinfo.detail[match[1]] : undefined
        if (newval) data[key] = value.replace(match[0], newval)
      }
    })
  }
  return data
}