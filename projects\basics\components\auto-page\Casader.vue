<template>
    <div>
        <el-cascader v-model="cascaderValue" :options="options" :placeholder="placeholder" :disabled="disabled"
            :show-all-levels="showAllLevels" :collapse-tags="collapseTags" :separator="separator" :clearable="clearable"
            :props="{ expandTrigger: expandTrigger, multiple: multiple, checkStrictly: checkStrictly, value: valueProp, label: labelProp }">
        </el-cascader>
    </div>
</template>

<script>
import Emitter from 'element-ui/src/mixins/emitter';

export default {
    mixins: [Emitter],
    data() {
        return {
            cascaderValue: null
        }
    },
    model: {
        prop: 'value',
        event: 'value'
    },
    computed: {
        cascaders: {
            get() {
                return this.value == null ? null : this.value
            },
            set(val) {
                return val
            }
        }
    },
    watch: {
        cascaders: {
            handler(val) {
                this.cascaderValue = val
            },
            deep: true,
            immediate: true
        },
        cascaderValue: {
            handler(val) {
                this.$emit('value', val)
                this.$emit('change', val)
                this.dispatch('ElFormItem', 'el.form.change', [val]);

            },
            deep: true,
            immediate: true
        },
    },
    props: {
        value: {
            type: [String, Number, Array],
            default: ''
        },
        options: {
            type: Array,
            default: function name(params) {
                return []
            }
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        clearable: {
            type: Boolean,
            default: true
        },
        showAllLevels: {
            type: Boolean,
            default: true
        },
        collapseTags: {
            type: Boolean,
            default: false
        },
        separator: {
            type: String,
            default: '/'
        },
        multiple: {
            type: Boolean,
            default: false
        },
        checkStrictly: {
            type: Boolean,
            default: false
        },
        expandTrigger: {
            type: String,
            default: 'click'
        },
        valueProp: {
            type: String,
            default: 'value'
        },
        labelProp: {
            type: String,
            default: 'label'
        },
    }
}
</script>

<style lang="scss" scoped></style>