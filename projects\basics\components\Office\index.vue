<template>
    <component :is="dynamicComponent" v-bind="$attrs" v-on="$listeners"></component>
</template>
<script>

export default {

    data() {
        return {
            dynamicComponent: null,
            ie: 'ActiveXObject' in window && true
        }
    },
    created() {
        this.loadComponent()
    },
    methods: {
        async loadComponent() {
            if (this.ie) {
                window.open(this.$attrs.src)
                // this.download(this.$attrs.src)
            } else {
                this.dynamicComponent = () => import('./VueOffice.vue')
            }
        },
        download(url) {
            let link = document.createElement('a');
            link.href = url;
            link.download = this.getFileNameFromUrl(url);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        getFileNameFromUrl(url) {
            let lastSlashIndex = url.lastIndexOf('/');
            if (lastSlashIndex === -1) {
                return url
            }
            let fileName = url.substring(lastSlashIndex + 1)
            return fileName
        }
    },
}
</script>