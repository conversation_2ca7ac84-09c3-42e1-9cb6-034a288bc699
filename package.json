{"name": "@zrw/vue-cli", "version": "1.0.0", "description": "vue-cli 多项目脚手架", "author": "zrwang4", "license": "MIT", "scripts": {"serve": "vue-cli-service serve", "system": "vue-cli-service serve --project=system", "dev": "vue-cli-service serve --project=system", "build": "vue-cli-service build", "build:sys": "vue-cli-service build --project=system", "build:stage": "node ./config/project-build.stage.js", "build:stage:all": "node ./config/project-build-stage-all.js", "build:all": "node ./config/project-build-all.js", "lint": "vue-cli-service lint"}, "dependencies": {"@form-create/component-wangeditor": "2.5.15", "@form-create/designer-zrw": "1.1.5", "@form-create/element-ui": "2.6.1", "@form-create/utils": "2.5.35", "@jykj/vue-treeselect": "1.0.1", "@logicflow/core": "^2.0.16", "@logicflow/extension": "^2.0.21", "@logicflow/layout": "^1.2.0-alpha.16", "@logicflow/vue-node-registry": "^1.0.18", "@riophae/vue-treeselect": "0.4.0", "@stomp/stompjs": "^7.1.1", "@vuemap/vue-amap": "0.1.17", "@wangeditor-next/editor": "5.6.14", "@wangeditor-next/editor-for-vue2": "1.0.2", "animate.css": "^4.1.1", "animejs": "3.2.2", "axios": "1.6.0", "clipboard": "2.0.11", "codemirror": "5.60.0", "date-fns": "^4.1.0", "echarts": "5.4.3", "echarts-gl": "2.0.9", "element-resize-detector": "1.2.4", "element-ui": "2.15.13", "highcharts": "11.4.8", "js-base64": "3.7.5", "js-beautify": "1.15.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "jsonlint-mod": "1.7.6", "jsonpath-plus": "10.2.0", "jyjh-common": "1.1.0", "lodash": "4.17.21", "markdown-it": "^14.1.0", "marked": "15.0.6", "mitt": "3.0.1", "monaco-editor": "0.49.0", "monaco-editor-esm-webpack-plugin": "2.1.0", "monaco-editor-locales-plugin": "0.0.3", "monaco-editor-webpack-plugin": "7.1.0", "node-cache": "5.1.2", "normalize.css": "8.0.1", "nprogress": "0.2.0", "reconnecting-websocket": "4.4.0", "root-path-loader": "1.0.3", "screenfull": "6.0.2", "spark-md5": "3.0.2", "style-rem-loader": "1.0.2", "style-vw-vh-loader": "1.0.2", "throttle-debounce": "5.0.0", "vue": "2.7.14", "vue-count-to": "1.0.13", "vue-cropper": "0.5.8", "vue-hover-mask": "1.0.0", "vue-json-editor": "1.4.3", "vue-json-pretty": "1.9.4", "vue-router": "3.6.2", "vuex": "3.6.2", "wangeditor": "4.7.15", "webrtc-adapter": "^9.0.3"}, "devDependencies": {"@babel/core": "7.26.9", "@babel/eslint-parser": "7.26.8", "@vue/cli-plugin-babel": "5.0.8", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-plugin-typescript": "5.0.8", "@vue/cli-service": "5.0.8", "chalk": "4.1.2", "core-js": "3.41.0", "eslint": "7.5.0", "eslint-plugin-vue": "9.13.0", "glob": "7.2.3", "minimist": "1.2.8", "path-browserify": "1.0.1", "postcss-px-to-viewport-8-plugin": "1.2.5", "regenerator-runtime": "0.14.1", "sass": "1.63.6", "sass-loader": "13.3.2", "svg-sprite-loader": "6.0.11", "typescript": "5.5.4", "webpack": "5.91.0", "webpack-bundle-analyzer": "4.10.2", "webpack-merge": "5.10.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <=11"]}