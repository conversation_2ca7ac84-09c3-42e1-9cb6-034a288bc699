import Vue from 'vue'
Vue.use({
    install(Vue) {
        Vue.directive('default', {
            inserted(el, binding) {
                if (!el.textContent && typeof el.textContent !== 'number') {
                    el.textContent = binding.value || '--'
                }
            },
            update(el, binding) {
                if (!el.textContent && typeof el.textContent !== 'number') {
                    el.textContent = binding.value || '--'
                }
            }
        })
    }
})