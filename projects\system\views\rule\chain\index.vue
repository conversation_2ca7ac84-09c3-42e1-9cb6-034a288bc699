<template>
  <div class="chain-container">
    <div class="page-header">
      <div class="header-title">
        <h2>编排链管理</h2>
      </div>
      
      <div class="header-tools">
        <div class="unified-search">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入编排链唯一标识或描述"
            prefix-icon="el-icon-search"
            clearable
            class="search-input"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>

          <el-select 
            v-model="queryParams.enabled" 
            placeholder="状态"
            clearable
            class="status-select"
            @change="handleSearch"
          >
            <el-option label="启用" :value="true">
              <i class="el-icon-check" style="color: #67C23A" />
              <span style="margin-left: 8px">启用</span>
            </el-option>
            <el-option label="禁用" :value="false">
              <i class="el-icon-close" style="color: #F56C6C" />
              <span style="margin-left: 8px">禁用</span>
            </el-option>
          </el-select>
        </div>

        <div class="button-group">
          <el-button type="primary" icon="el-icon-refresh" @click="handleReload">
            热刷新
          </el-button>
          <el-button type="primary" icon="el-icon-plus" @click="$router.push('/rule/chain/add')">
            新增编排链
          </el-button>
          <el-button type="success" icon="el-icon-picture" @click="handleVisualAdd">
            可视化新增
          </el-button>
          <el-button 
            type="warning" 
            icon="el-icon-check" 
            @click="handleBatchValidate"
            :loading="validating"
          >
            批量校验
          </el-button>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        fit
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column
          prop="chainName"
          label="编排链唯一标识"
          min-width="150"
        >
          <template slot-scope="scope">
            <div class="chain-cell" :class="{'invalid-rule': scope.row.validateStatus === false}">
              <i class="el-icon-share" />
              <span>{{ scope.row.chainName }}</span>
              <el-tag v-if="scope.row.validateStatus === false" type="danger" size="mini">校验失败</el-tag>
              <el-tag v-if="scope.row.validateStatus === true" type="success" size="mini">校验通过</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="chainDesc"
          label="描述"
          show-overflow-tooltip
          min-width="180"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.chainDesc">{{ scope.row.chainDesc }}</span>
            <span v-else class="no-desc">暂无描述</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="elData"
          label="编排表达式"
          show-overflow-tooltip
          min-width="180"
        />

        <el-table-column
          prop="enabled"
          label="状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabled"
              @change="(val) => handleStatusChange(scope.row, val)"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          min-width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-picture"
              class="visual-edit-btn"
              @click="handleVisualEdit(scope.row)"
            >
              可视化编辑
            </el-button>

            <el-button
              size="mini"
              type="success"
              icon="el-icon-video-play"
              class="action-btn execute-btn"
              @click="handleExecute(scope.row)"
            >
              执行
            </el-button>

            <el-dropdown @command="(command) => handleCommand(command, scope.row)" trigger="click">
              <el-button size="mini" type="info" class="more-actions-btn">
                更多操作<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="view" icon="el-icon-view">查看</el-dropdown-item>
                <el-dropdown-item command="edit" icon="el-icon-edit">编辑</el-dropdown-item>
                <el-dropdown-item command="validate" icon="el-icon-check">校验</el-dropdown-item>
                <el-dropdown-item command="nodes" icon="el-icon-share">查看节点脚本</el-dropdown-item>
                <el-dropdown-item command="delete" icon="el-icon-delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <execute-dialog ref="executeDialog" :chain-name="currentChainName" />
  </div>
</template>

<script>
import {
  getRuleChainList,
  updateRuleChain,
  deleteRuleChain,
  validateRuleExpression,
  getApplicationList,
  resolveElNodes
} from '@system/api/rule/chain'
import { reloadRules } from '@system/api/rule/reload'
import ExecuteDialog from './components/ExecuteDialog.vue'

export default {
  name: 'RuleChainList',
  components: {
    ExecuteDialog
  },
  data() {
    return {
      loading: false,
      validating: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      queryParams: {
        keyword: '',
        application: 'extreme-flow',
        enabled: ''
      },
      applicationList: [],
      currentChainName: '',
      validationErrors: {}
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchApplicationList() {
        const data = await getApplicationList()
        if (data && Array.isArray(data)) {
          this.applicationList = data
        }
    },
    async fetchData() {
      this.loading = true
      try {
        const params = {
          current: this.currentPage,
          size: this.pageSize
        }
        
        if (this.queryParams.keyword) {
          params.keyword = this.queryParams.keyword
        }
        
        // 始终使用 extreme-flow 作为应用服务
        params.application = 'extreme-flow'
        
        if (this.queryParams.enabled !== '') {
          params.enabled = this.queryParams.enabled
        }
        
        const data = await getRuleChainList(params)
        this.tableData = data.records
        this.total = data.total
      } catch (error) {
        this.$message.error('获取编排链列表失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    handleEdit(row) {
      this.$router.push(`/rule/chain/edit/${row.id}`)
    },
    handleView(row) {
      this.$router.push(`/rule/chain/view/${row.id}`)
    },
    handleVisualAdd() {
      // 直接跳转到可视化编排页面，不传递chainId参数
      this.$router.push(`/rule/arrange`)
    },
    handleVisualEdit(row) {
      // 跳转到可视化编辑页面，并传递编排链ID
      if (!row || !row.id) {
        this.$message.error('编排链唯一标识不能为空');
        return;
      }
      
      console.log('可视化编辑, 编排链数据:', row);
      
      // 使用encodeURIComponent确保URL参数正确编码
      const chainId = encodeURIComponent(row.id);
      console.log('编码后的chainId:', chainId);
      
      // 确保chainId是有效值
      if (!chainId || chainId === 'undefined' || chainId === 'null') {
        this.$message.error('无效的编排链唯一标识');
        return;
      }
      
      this.$router.push(`/rule/arrange?chainId=${chainId}`);
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该编排链吗？删除后不可恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await deleteRuleChain(row.id)
        this.$message.success('删除成功')
        this.fetchData()
      } catch (error) {
        if(error !== 'cancel') {
          this.$message.error('删除失败：' + (error.message || '未知错误'))
        }
      }
    },
    async handleStatusChange(row, status) {
      try {
        await updateRuleChain({
          ...row,
          enabled: status
        })
        this.$message.success(`${status ? '启用' : '禁用'}成功`)
      } catch (error) {
        row.enabled = !status // 恢复状态
        this.$message.error(`${status ? '启用' : '禁用'}失败：` + (error.message || '未知错误'))
      }
    },
    async handleReload() {
      try {
        this.loading = true
        await reloadRules()
        this.$message.success('热刷新成功')
        await this.fetchData() // 刷新数据
      } catch (error) {
        this.$message.error('热刷新失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    handleSearch() {
      this.currentPage = 1
      this.fetchData()
    },
    handleExecute(row) {
      this.currentChainName = row.chainName
      this.$nextTick(() => {
        this.$refs.executeDialog.open()
      })
    },
    // 单个编排校验
    async handleValidate(row) {
      try {
        this.$set(row, 'validating', true)
        await validateRuleExpression(row.elData)
        this.$set(row, 'validateStatus', true)
        this.$message.success(`编排 "${row.chainName}" 校验通过`)
      } catch (error) {
        this.$set(row, 'validateStatus', false)
        this.validationErrors[row.id] = error.message || '校验失败'
      } finally {
        this.$set(row, 'validating', false)
      }
    },
    
    // 批量校验所有编排
    async handleBatchValidate() {
      if (this.validating) return
      
      this.validating = true
      this.validationErrors = {}
      
      try {
        const promises = this.tableData.map(async (row) => {
          try {
            this.$set(row, 'validating', true)
            await validateRuleExpression(row.elData)
            this.$set(row, 'validateStatus', true)
            return { id: row.id, success: true }
          } catch (error) {
            this.$set(row, 'validateStatus', false)
            this.validationErrors[row.id] = error.message || '校验失败'
            return { id: row.id, success: false, error: error.message || '未知错误' }
          } finally {
            this.$set(row, 'validating', false)
          }
        })
        
        const results = await Promise.all(promises)
        const failCount = results.filter(r => !r.success).length
        
        if (failCount === 0) {
          this.$message.success('所有编排校验通过')
        } else {
          this.$message.warning(`共有 ${failCount} 条编排校验失败`)
        }
      } catch (error) {
        this.$message.error('批量校验过程中发生错误：' + (error.message || '未知错误'))
      } finally {
        this.validating = false
      }
    },
    // 解析编排中引用的节点
    async handleResolveNodes(row) {
      if (!row.elData) {
        this.$message.warning('编排表达式为空')
        return
      }
      
      try {
        this.$message.info('正在解析节点...')
        const data = await resolveElNodes({ elStr: row.elData })
        
        if (data && Array.isArray(data) && data.length > 0) {
          // 显示节点数量信息
          this.$message.success(`解析到 ${data.length} 个节点，正在跳转到脚本页面...`)
          
          // 使用enums格式传递所有节点
          const nodesParam = data.join(',')
          
          // 跳转到脚本页面，并传递节点列表
          setTimeout(() => {
            this.$router.push({
              path: '/rule/script',
              query: { 
                'enums[scriptId]': nodesParam,
                from: 'chain',
                t: Date.now()
              }
            })
          }, 300)
        } else {
          this.$message.warning('未解析到任何节点')
        }
      } catch (error) {
        this.$message.error('解析节点失败：' + (error.message || '未知错误'))
      }
    },
    // 添加处理下拉命令的方法
    handleCommand(command, row) {
      switch (command) {
        case 'view':
          this.handleView(row)
          break
        case 'edit':
          this.handleEdit(row)
          break
        case 'validate':
          this.handleValidate(row)
          break
        case 'nodes':
          this.handleResolveNodes(row)
          break
        case 'execute':
          this.handleExecute(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chain-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }
    
    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .unified-search {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .search-input {
          width: 250px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;
              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;

              .el-button {
                margin: 0;
                height: 34px;
                border: none;
                padding: 0 16px;
                border-radius: 0 8px 8px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
          }
        }

        .status-select {
          width: 120px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              border-radius: 8px;
              background: #f9fafc;
              border: 1px solid #e0e5ee;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }
          }
        }
      }
      
      .button-group {
        display: flex;
        gap: 12px;
        
        .el-button {
          padding: 8px 16px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 8px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          height: 36px;
          font-size: 14px;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    .statistics-bar {
      background: linear-gradient(to right, #f5f7fa, #f9fafc);
      padding: 14px 20px;
      margin-bottom: 16px;
      border-radius: 10px;
      border: 1px solid #eef1f7;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .stat-item {
        color: #606266;
        font-size: 14px;
        
        i {
          color: #409EFF;
          margin-right: 6px;
        }

        .stat-count {
          font-weight: 600;
          color: #409EFF;
          margin-left: 4px;
        }
      }
    }

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }

      .chain-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #409EFF;
          font-size: 16px;
        }
        
        span {
          color: #1a1f36;
          font-weight: 500;
        }
        
        &.invalid-rule {
          i, span {
            color: #F56C6C;
          }
        }
        
        .el-tag--mini {
          margin-left: 6px;
          height: 20px;
          line-height: 18px;
          padding: 0 6px;
        }
      }

      .no-desc {
        color: #909399;
        font-style: italic;
      }

      .delete-btn {
        color: #F56C6C;
        
        &:hover {
          color: #f78989;
        }
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;
      
      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;
        
        .btn-prev, 
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;
          
          &:not(.disabled):hover {
            border-color: #409EFF;
          }
          
          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }
        
        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }
        
        .el-pagination__jump {
          margin-left: 16px;
        }
        
        .el-select .el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }
        
        .el-pagination__editor.el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }

  ::v-deep .el-tag {
    text-transform: capitalize;
    
    &.el-tag--primary {
      background-color: #409EFF;
      border-color: #409EFF;
    }
    
    &.el-tag--success {
      background-color: transparent;
      border-color: #67C23A;
      color: #67C23A !important;
    }
    
    &.el-tag--warning {
      background-color: #E6A23C;
      border-color: #E6A23C;
    }

    &.el-tag--danger {
      color: #F56C6C;
      border-color: #F56C6C;
      background-color: transparent;
    }
  }

  .visual-edit-btn {
    margin-right: 10px;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    background: #409EFF;
    color: #fff;
    
    i {
      margin-right: 3px;
    }
    
    &:hover {
      background: #66b1ff;
      color: #fff;
    }
  }

  .action-btn {
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 10px;
    
    i {
      margin-right: 3px;
    }
  }
  
  .execute-btn {
    background: #67C23A;
    border-color: #67C23A;
    color: #fff;
    
    &:hover {
      background: #85ce61;
      border-color: #85ce61;
      color: #fff;
    }
  }

  .more-actions-btn {
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    background: #909399;
    color: #fff;
    border-color: #909399;
    
    &:hover {
      background: #a6a9ad;
      border-color: #a6a9ad;
    }
  }

  .analyze-btn {
    color: #409EFF;
    margin: 0 10px;
    
    &:hover {
      color: #66b1ff;
    }
  }
}
</style>
