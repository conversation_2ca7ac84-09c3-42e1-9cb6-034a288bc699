import request from '@/utils/request'

const apiPrefix = 'gateway'

// 获取所有路由列表
export function getRouteList(params) {
  return request({
    url: `${apiPrefix}/route/list`,
    method: 'get',
    params
  })
}

// 获取单个路由详情
export function getRouteDetail(params) {
  return request({
    url: `${apiPrefix}/route`,
    method: 'get',
    params
  })
}

// 创建路由
export function createRoute(data) {
  return request({
    url: `${apiPrefix}/route`,
    method: 'post',
    data
  })
}

// 更新路由
export function updateRoute(data) {
  return request({
    url: `${apiPrefix}/route`,
    method: 'put',
    data
  })
}

// 删除路由
export function deleteRoute(id) {
  return request({
    url: `${apiPrefix}/route/${id}`,
    method: 'delete'
  })
}

// 获取路由下的断言列表
export function getPredicateList(params) {
  return request({
    url: `${apiPrefix}/predicate/list`,
    method: 'get',
    params
  })
}

// 创建断言
export function createPredicate(data) {
  return request({
    url: `${apiPrefix}/predicate`,
    method: 'post',
    data
  })
}

// 更新断言
export function updatePredicate(data) {
  return request({
    url: `${apiPrefix}/predicate`,
    method: 'put',
    data
  })
}

// 删除断言
export function deletePredicate(id) {
  return request({
    url: `${apiPrefix}/predicate/${id}`,
    method: 'delete'
  })
}

// 获取路由下的过滤器列表
export function getFilterList(params) {
  return request({
    url: `${apiPrefix}/filter/list`,
    method: 'get',
    params
  })
}

// 创建过滤器
export function createFilter(data) {
  return request({
    url: `${apiPrefix}/filter`,
    method: 'post',
    data
  })
}

// 更新过滤器
export function updateFilter(data) {
  return request({
    url: `${apiPrefix}/filter`,
    method: 'put',
    data
  })
}

// 删除过滤器
export function deleteFilter(id) {
  return request({
    url: `${apiPrefix}/filter/${id}`,
    method: 'delete'
  })
}

// 获取路由下的安全配置列表
export function getSecurityList(params) {
  return request({
    url: `${apiPrefix}/security/list`,
    method: 'get',
    params
  })
}

// 创建安全配置
export function createSecurity(data) {
  return request({
    url: `${apiPrefix}/security`,
    method: 'post',
    data
  })
}

// 更新安全配置
export function updateSecurity(data) {
  return request({
    url: `${apiPrefix}/security`,
    method: 'put',
    data
  })
}

// 删除安全配置
export function deleteSecurity(id) {
  return request({
    url: `${apiPrefix}/security/${id}`,
    method: 'delete'
  })
}

// 应用网关配置（使配置生效）
export function applyGatewayConfig() {
  return request({
    url: `/actuator/gateway/refresh`,
    method: 'post'
  })
}

// 获取预设的断言类型
export function getPredicateTypes() {
  return request({
    url: `${apiPrefix}/predicate-types`,
    method: 'get'
  })
}

// 获取预设的过滤器类型
export function getFilterTypes() {
  return request({
    url: `${apiPrefix}/filter-types`,
    method: 'get'
  })
}

// 获取预设的过滤器类型
export function matchers() {
  return request({
    url: `${apiPrefix}/security/matchers`,
    method: 'get'
  })
}

export function getLogList(params) {
  return request({
    url: `${apiPrefix}/log/list`,
    method: 'get',
    params
  })
}

export function deleteLog(id) {
  return request({
    url: `${apiPrefix}/log/${id}`,
    method: 'delete',
  })
}

export function getLogConfigList(params) {
  return request({
    url: `${apiPrefix}/log/config/list`,
    method: 'get',
    params
  })
}

export function getLogConfig(params) {
  return request({
    url: `${apiPrefix}/log/config`,
    method: 'get',
    params
  })
}

export function createLogConfig(data) {
  return request({
    url: `${apiPrefix}/log/config`,
    method: 'post',
    data
  })
}

export function updateLogConfig(data, cover) {
  let headers
  if (typeof cover !== 'undefined') {
    headers = {'X-Cover': cover}
  }
  return request({
    url: `${apiPrefix}/log/config`,
    method: 'put',
    data,
    headers
  })
}

export function deleteLogConfig(id) {
  return request({
    url: `${apiPrefix}/log/config/${id}`,
    method: 'delete'
  })
}


