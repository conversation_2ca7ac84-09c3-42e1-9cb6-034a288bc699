<template>
  <div class="basicsNode" :class="`node-type-${nodeConfig.type}`">
    <div class="title">
      <i :class="nodeConfig.icon"></i>
      <span>{{ nodeConfig.label }}</span>
    </div>
    <!-- 执行状态图标 -->
    <div class="execute-status" v-if="executeStatus">
      <i v-if="executeStatus === 'success'" class="el-icon-check status-success"></i>
      <i v-if="executeStatus === 'error'" class="el-icon-close status-error"></i>
    </div>
    <div></div>
  </div>
</template>

<script>
import { EventType } from '@logicflow/core'
import { vueNodesMap } from '@logicflow/vue-node-registry'
import { getNodeByType } from '../panel/config.js'

export default {
  name: 'basicsNode',
  inject: ['getNode', 'getGraph'],
  data() {
    return {
      properties: {},
      nodeConfig: {}
    }
  },
  computed: {
    executeStatus() {
      return this.properties.executeStatus || null
    }
  },
  async mounted() {
    const node = this.getNode()
    const graph = this.getGraph()

    graph.eventCenter.on(EventType.NODE_PROPERTIES_CHANGE, (eventData) => {

      const keys = eventData.keys || []
      const content = vueNodesMap[node.type]
      if (content && eventData.id === node.id) {
        const { effect } = content
        // 如果没有定义 effect，则默认更新；如果定义了 effect，则只有在 effect 中的属性发生变化时才更新
        if (!effect || keys.some((key) => effect.includes(key))) {
          this.properties = eventData.properties

          this.testUpdateIncomingEdgesStatus(node, graph)
        }
      }
    })

    this.nodeConfig = await getNodeByType(node.type)

  },
  methods: {
    testUpdateIncomingEdgesStatus(node, graph) {
      const outgoingEdges = graph.getNodeOutgoingEdge(node.id)

      outgoingEdges.forEach(edge => {
        const targetNode = graph.getNodeModelById(edge.targetNodeId)
        const targetStatus = targetNode?.properties?.executeStatus
        const currentStatus = node.properties.executeStatus

        if (targetStatus && currentStatus) {
          edge.setProperties({
            executeStatus: currentStatus
          })
        } else {
          edge.setProperties({
            executeStatus: null
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
@media (min-width: 200px) {
  .basicsNode {
    width: 160px;
    height: 50px;
    background-color: white;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    position: relative;

    .title {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 5px;
      font-size: 14px;

      i {
        margin-right: 5px;
        font-size: 22px;
        color: #606266;
      }

      span {
        color: black;
      }
    }

    .execute-status {
      position: absolute;
      top: 50%;
      right: 8px;
      width: 20px;
      height: 20px;
      margin-top: -10px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .status-success {
        color: #67C23A;
        font-size: 14px;
        font-weight: bold;
      }

      .status-error {
        color: #F56C6C;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
}
</style>
