import uniqueId from '@form-create/utils/lib/unique';
import { localeProps } from '@form-create/designer-zrw/src/utils';

const label = '人员选择';
const name = 'userSelect';

export default {
    menu: 'custom',
    icon: 'icon-editor',
    label,
    name,
    event: ['change'],
    validate: ['string'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            $required: false,
            props: {
                componentType:'account',
                componentName:'人员选择'
            },
        };
    },
    props(_, { t }) {

        return localeProps(t, 'custom.props', [
            {
                type: 'switch',
                field: 'disabled',
                title: t('custom.props.disabled')
            }]);
    }
};
