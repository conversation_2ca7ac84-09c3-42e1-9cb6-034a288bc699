import request from '@/utils/request'

const api = CONSTANT.SYSTEM +  '/sys/resources'

export function getRouters() {
  return request({
    url: `${api}/getRouters`,
    method: 'get'
  })
}

export function treeList() {
  return request({
    url: `${api}/treeList`,
    method: 'get',
  })
}

//在线用户列表
export function getOnlines() {
  return request({
    url: `${api}/onlines`,
    method: 'get',
  })
}

//下线
export function offline(token) {
  return request({
    url: `${api}/offline/`+token,
    method: 'delete',
  })
}