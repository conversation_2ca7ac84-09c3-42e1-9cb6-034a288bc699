<template>
    <div style="position: relative;">
        <div v-if="loading" class="empty_state_box_loading">
        </div>
        <div v-else-if="empty" class="empty_state_box_ui">
        </div>

        <el-table v-bind="$attrs" v-on="$listeners" empty-text=" " :data="tableData">
            <slot></slot>
        </el-table>
    </div>

</template>
<script>

export default {
    name: 'TableUiBox',
    props: {
        empty: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        },

    },
    computed: {
        tableData() {
            if (this.empty || this.loading) {
                return [];
            }
            return this.$attrs.data || [];
        }
    }
}
</script>