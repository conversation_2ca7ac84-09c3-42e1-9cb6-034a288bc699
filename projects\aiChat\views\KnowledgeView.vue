<template>
  <div class="knowledge-view">
    <div class="header">
      <div class="header-left">
        <h2>知识库管理</h2>
        <el-tooltip content="刷新数据" placement="right">
          <el-button 
            type="text"
            :loading="loading"
            @click="refreshData"
            class="refresh-btn"
          >
            <i class="el-icon-refresh"></i>
          </el-button>
        </el-tooltip>
      </div>
      <el-button type="primary" @click="showCreateDialog">创建知识库</el-button>
    </div>
    
    <el-collapse v-model="activeKBs" @change="handleCollapseChange">
      <el-collapse-item 
        v-for="kb in knowledgeBases" 
        :key="kb.kb_name"
        :name="kb.kb_name"
      >
        <template slot="title">
          <div class="kb-header">
            <div class="kb-main-info">
              <span class="kb-name">{{ kb.kb_name }}</span>
              <span class="kb-desc">{{ kb.kb_info }}</span>
            </div>
            <div class="kb-info">
              <el-tag size="small" type="info">{{ kb.embed_model }}</el-tag>
              <el-tag size="small">文件数：{{ kb.file_count }}</el-tag>
              <span class="create-time">{{ formatDate(kb.create_time) }}</span>
              <div class="kb-actions">
                <el-tooltip content="上传文件" placement="top">
                  <el-button 
                    type="text"
                    size="mini"
                    @click.stop="handleUpload(kb)"
                  >
                    <i class="el-icon-upload2"></i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="修改知识库" placement="top">
                  <el-button 
                    type="text"
                    size="mini"
                    @click.stop="handleEdit(kb)"
                  >
                    <i class="el-icon-edit"></i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除知识库" placement="top">
                  <el-button 
                    type="text"
                    size="mini"
                    @click.stop="handleDelete(kb)"
                  >
                    <i class="el-icon-delete"></i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="搜索知识库" placement="top">
                  <el-button 
                    type="text"
                    size="mini"
                    @click.stop="handleSearch(kb)"
                  >
                    <i class="el-icon-search"></i>
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>
        
        <!-- 知识库内容 -->
        <div class="kb-content">
          <knowledge-file-list
            :files="kb.files || []"
            :loading="loadingFiles[kb.kb_name]"
            :kb-name="kb.kb_name"
            @view-detail="handleViewDetail"
            @reload="loadFiles(kb)"
            @upload="handleUpload(kb)"
          />
        </div>
      </el-collapse-item>
    </el-collapse>

    <!-- 添加修改知识库对话框 -->
    <el-dialog 
      :title="editMode ? '修改知识库' : '创建知识库'" 
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form 
        :model="form" 
        ref="form" 
        :rules="rules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="知识库名称" prop="name">
          <el-input 
            v-model="form.name" 
            placeholder="请输入知识库名称"
            clearable
            :disabled="editMode"
          >
            <template slot="prepend">KB_</template>
          </el-input>
        </el-form-item>

        <el-form-item label="知识库描述">
          <el-input 
            v-model="form.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入知识库描述信息"
          ></el-input>
        </el-form-item>

        <el-form-item label="向量库类型">
          <el-select 
            v-model="form.vectorStoreType"
            style="width: 100%"
            :disabled="editMode"
          >
            <el-option 
              v-for="item in vectorStoreTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span>{{ item.label }}</span>
              <span class="option-desc">{{ item.desc }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="嵌入模型">
          <el-select 
            v-model="form.embedModel"
            style="width: 100%"
          >
            <el-option 
              v-for="item in embedModels"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span>{{ item.label }}</span>
              <span class="option-desc">{{ item.desc }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelEdit">取 消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
        >{{ editMode ? '保 存' : '创 建' }}</el-button>
      </div>
    </el-dialog>

    <!-- 上传文件对话框 -->
    <upload-dialog
      :visible.sync="uploadDialogVisible"
      :kb-name="currentKB?.kb_name"
      :knowledge-bases="knowledgeBases"
      @success="handleUploadSuccess"
    />

    <!-- 文件详情对话框 -->
    <file-detail
      :visible.sync="detailDialogVisible"
      :file="currentFile"
    />

    <knowledge-search 
      :knowledge-base-name="currentKnowledgeBase"
      v-if="currentKnowledgeBase"
    />

    <knowledge-search
      :visible.sync="searchDialogVisible"
      :knowledge-base-name="currentKB?.kb_name"
    />
  </div>
</template>

<script>
import api from '../api'
import { formatDate } from '../utils/format'
import KnowledgeFileList from '../components/knowledge/KnowledgeFileList.vue'
import FileDetail from '../components/knowledge/FileDetail.vue'
import UploadDialog from '../components/knowledge/UploadDialog.vue'
import KnowledgeSearch from '../components/knowledge/KnowledgeSearch.vue'

export default {
  name: 'KnowledgeView',
  components: {
    KnowledgeFileList,
    FileDetail,
    UploadDialog,
    KnowledgeSearch
  },
  data() {
    return {
      knowledgeBases: [],
      loading: false,
      loadingFiles: {},
      dialogVisible: false,
      uploadDialogVisible: false,
      editMode: false,
      submitting: false,
      form: {
        name: '',
        description: '',
        vectorStoreType: 'Faiss',
        embedModel: 'm3e-base'
      },
      rules: {
        name: [
          { required: true, message: '请输入知识库名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
          { 
            pattern: /^[a-zA-Z0-9_-]+$/, 
            message: '只能包含字母、数字、下划线和连字符', 
            trigger: 'blur'
          }
        ]
      },
      currentKB: null,
      uploadUrl: process.env.VUE_APP_BASE_API + 'knowledge_base/upload_file',
      uploadData: {},
      activeKBs: [],
      detailDialogVisible: false,
      currentFile: null,
      vectorStoreTypes: [
        { 
          label: 'Faiss', 
          value: 'Faiss',
          desc: '高性能向量检索库，适合单机部署'
        },
        { 
          label: 'Milvus', 
          value: 'Milvus',
          desc: '分布式向量数据库，适合大规模部署'
        },
        { 
          label: 'Chroma', 
          value: 'Chroma',
          desc: '轻量级向量存储，适合开发测试'
        }
      ],
      embedModels: [
        { 
          label: 'm3e-base', 
          value: 'm3e-base',
          desc: '通用中文向量模型，支持中英文'
        },
        { 
          label: 'text2vec-base', 
          value: 'text2vec-base',
          desc: '轻量级中文向量模型'
        },
        { 
          label: 'bge-base-zh', 
          value: 'bge-base-zh',
          desc: 'BGE系列中文基础模型'
        }
      ],
      searchDialogVisible: false,
    }
  },
  methods: {
    formatDate,
    
    async loadKnowledgeBases() {
      try {
        this.loading = true;
        const response = await api.knowledge.getList();
        this.knowledgeBases = response.data;
      } catch (error) {
        this.$message.error('加载知识库列表失败');
        console.error(error);
      } finally {
        this.loading = false;
      }
    },

    async loadFiles(kb) {
      if (this.loadingFiles[kb.kb_name]) return;
      
      try {
        this.$set(this.loadingFiles, kb.kb_name, true);
        const response = await api.knowledge.getFiles(kb.kb_name);
        this.$set(kb, 'files', response.data || []);
      } catch (error) {
        this.$message.error('加载文件列表失败');
        console.error(error);
      } finally {
        this.$set(this.loadingFiles, kb.kb_name, false);
      }
    },

    showCreateDialog() {
      this.editMode = false;
      this.dialogVisible = true;
      this.form = {
        name: '',
        description: '',
        vectorStoreType: 'Faiss',
        embedModel: 'm3e-base'
      };
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    handleEdit(kb) {
      this.editMode = true;
      this.dialogVisible = true;
      this.form = {
        name: kb.kb_name.replace('KB_', ''),
        description: kb.kb_info || '',
        vectorStoreType: kb.vector_store_type || 'Faiss',
        embedModel: kb.embed_model
      };
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    cancelEdit() {
      this.dialogVisible = false;
      this.$refs.form.resetFields();
    },

    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        this.submitting = true;
        if (this.editMode) {
          await api.knowledge.update({
            name: 'KB_' + this.form.name,
            description: this.form.description,
            embedModel: this.form.embedModel
          });
          this.$message.success('修改成功');
        } else {
          await api.knowledge.create({
            name: 'KB_' + this.form.name,
            description: this.form.description,
            vectorStoreType: this.form.vectorStoreType,
            embedModel: this.form.embedModel
          });
          this.$message.success('创建成功');
          this.activeKBs.push('KB_' + this.form.name);
        }
        this.dialogVisible = false;
        await this.loadKnowledgeBases();
      } catch (error) {
        if (error === false) return;
        this.$message.error((this.editMode ? '修改' : '创建') + '失败：' + (error.message || '未知错误'));
        console.error(error);
      } finally {
        this.submitting = false;
      }
    },

    handleViewDetail(file) {
      this.currentFile = file;
      this.detailDialogVisible = true;
    },

    handleUpload(kb) {
      this.currentKB = kb;
      this.uploadDialogVisible = true;
      this.uploadData = {
        knowledge_base_name: kb.kb_name
      };
    },

    async handleDelete(kb) {
      try {
        await this.$confirm('确认删除该知识库吗？', '提示', {
          type: 'warning'
        });
        await api.knowledge.delete(kb.kb_name);
        this.$message.success('删除成功');
        this.loadKnowledgeBases();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败');
          console.error(error);
        }
      }
    },

    beforeUpload(file) {
      const validTypes = ['text/plain', 'text/markdown', 'application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!validTypes.includes(file.type)) {
        this.$message.error('不支持的文件类型');
        return false;
      }
      return true;
    },

    handleUploadSuccess() {
      this.$message.success('上传成功');
      const kb = this.knowledgeBases.find(item => item.kb_name === this.currentKB.kb_name);
      if (kb) {
        this.loadFiles(kb);
      }
    },

    handleUploadError() {
      this.$message.error('上传失败');
    },

    handleCollapseChange(activeNames) {
      if (!activeNames || (Array.isArray(activeNames) && activeNames.length === 0)) {
        return;
      }

      const newActiveKBs = Array.isArray(activeNames) ? activeNames : [activeNames];
      
      newActiveKBs.forEach(kbName => {
        const kb = this.knowledgeBases.find(item => item.kb_name === kbName);
        if (kb && (!kb.files || kb.files.length === 0)) {
          this.loadFiles(kb);
        }
      });
    },

    async refreshData() {
      await this.loadKnowledgeBases();
      this.activeKBs.forEach(kbName => {
        const kb = this.knowledgeBases.find(item => item.kb_name === kbName);
        if (kb) {
          this.loadFiles(kb);
        }
      });
    },

    handleSearch(kb) {
      this.currentKB = kb;
      this.searchDialogVisible = true;
    }
  },
  created() {
    this.loadKnowledgeBases();
  }
}
</script>

<style scoped>
.knowledge-view {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
}

.kb-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.kb-main-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kb-name {
  font-weight: bold;
  font-size: 16px;
}

.kb-desc {
  color: #606266;
  font-size: 14px;
}

.kb-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.create-time {
  color: #909399;
  font-size: 13px;
}

.kb-content {
  padding: 16px;
}

.kb-actions {
  display: flex;
  gap: 2px;
  margin-left: 8px;
}

.kb-actions .el-button {
  padding: 0 1px;
}

.kb-actions .el-button i {
  font-size: 16px;
}

.kb-actions .el-button:hover {
  color: #409EFF;
}

.kb-actions .el-button:last-child:hover {
  color: #F56C6C;
}

.file-table {
  margin-top: 12px;
}

.upload-demo {
  text-align: center;
}

.el-upload__tip {
  margin-top: 10px;
}

:deep(.el-collapse-item__header) {
  padding: 12px;
}

:deep(.el-collapse-item__content) {
  padding: 0;
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-input-group__prepend) {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: bold;
}

.option-desc {
  float: right;
  color: #909399;
  font-size: 13px;
  padding-left: 10px;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 15px;
}

:deep(.el-select-dropdown__item span) {
  display: inline-block;
}

.file-detail {
  padding: 10px;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions-item__label) {
  width: 120px;
  background-color: #f5f7fa;
}

:deep(.el-button--text) {
  padding: 0;
}

:deep(.el-button--text:hover) {
  color: #409EFF;
  text-decoration: underline;
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.file-actions .el-button {
  padding: 0 4px;
}

.file-actions .el-button i {
  font-size: 16px;
}

.file-actions .el-button:not(:disabled):hover {
  color: #409EFF;
}

.file-actions .el-button:last-child:not(:disabled):hover {
  color: #F56C6C;
}

.file-actions .el-button[disabled] {
  color: #C0C4CC;
  cursor: not-allowed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.refresh-btn {
  padding: 0;
  font-size: 18px;
}

.refresh-btn:hover {
  color: #409EFF;
}

.refresh-btn i {
  transition: transform 0.3s ease;
}

.refresh-btn:hover i {
  transform: rotate(180deg);
}

.refresh-btn.is-loading i {
  animation: none;
}
</style> 