<template>
    <div style="padding-left: 16px;padding-right: 16px;">
        <div class="box1">
            <img src="./ic_heart.png" />
            <div class="t">
                你或许不是熠熠生辉的星辰， 但你的热血定能点亮生命的灯火！感谢您对无偿献血的支持。
            </div>
        </div>
        <div class="box2">
            <img src="./ic_reboot.png" />
            <div class="box2-1">
                <div class="t">哈喽，我是献血AI小助手~</div>
                <div class="t2">您可以询问有关献血的一切信息（献血条件、报销流程、献血权益、注意事项等），我们将为您提供最贴心的服务～</div>
            </div>
        </div>
        <div class="box3">
            <div class="t">你可以这样问</div>
            <div>
                <span class="t2" @click="$emit('chat','近期服药或接种疫苗是否影响献血？')">近期服药或接种疫苗是否影响献血？</span>
            </div>
            <div>
                <span class="t2" @click="$emit('chat','献血是否有年龄限制？')">献血是否有年龄限制？</span>
            </div>
            <div>
                <span class="t2" @click="$emit('chat','两次献血之间需要间隔多久？')">两次献血之间需要间隔多久？</span>
            </div>
        </div>

    </div>
</template>
<script>
</script>
<style lang="scss" scoped>
.box1 {
    padding: 8px;
    border-radius: 8px;
    background: linear-gradient(270deg, rgba(255, 103, 108, 0.2) 0%, rgba(255, 188, 101, 0.2) 100%);
    border: 1px solid rgba(255, 255, 255, 1);
    display: flex;

    img {
        width: 20px;
        height: 20px;
    }

    .t {
        font-size: 12px;
        line-height: 18px;
        color: rgba(255, 105, 108, 1);
        align-content: center;
        margin-left: 8px;
    }
}

.box2 {
    margin-top: 10px;

    img {
        width: 40px;
        height: 40px;
        display: block;
    }

    .box2-1 {
        padding: 12px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 1);

        .t {
            font-size: 14px;
            font-weight: bold;
            color: rgba(27, 27, 27, 1);
        }

        .t2 {
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            color: rgba(27, 27, 27, 1);
            margin-top: 4px;
        }
    }
}

.box3 {
    margin-top: 10px;

    .t {
        font-size: 14px;
        color: rgba(116, 121, 140, 1);
    }

    .t2 {
        font-size: 14px;
        padding: 6px 9px;
        color: rgba(0, 0, 0, 1);
        display: inline-block;
        border-radius: 4px;
        background: rgba(255, 255, 255, 1);
        margin-top: 6px;
    }
}
</style>