<template>
    <div>
        <el-tooltip effect="dark" :content="$refs.cascader?.inputValue" :disabled="!value" placement="top">
            <div class="content" v-show="readonly">{{ $refs.cascader?.inputValue || emptyText }}</div>
        </el-tooltip>
        <el-cascader v-show="!readonly" ref="cascader" :props="props" :collapse-tags="collapseTags"
            :placeholder="placeholder" :separator="separator" :options="deptTree" v-model="deptIds" style="width: 100%;"
            :clearable="clearable" :show-all-levels="showAllLevels" :filterable="filterable" />
    </div>
</template>

<script>
import { list, treeList } from '@system/api/sys/dept'
import { readonly } from 'vue';

/**
 * 部门选择器
 */
export default {
    name: 'SysDeptSelector',
    props: {
        /**
         * 绑定值，可以是部门id或者部门id数组
         * @type {Array|String|Number}
         */
        value: [Array, Number, String],
        /**
         * 是否只读，只读时，将不显示输入框，只显示已选部门名称
         * @type {Boolean}
         */
        readonly: {
            type: Boolean,
            default: false
        },
        /**
         * 是否显示清除按钮
         * @type {Boolean}
         */
        clearable: {
            type: Boolean,
            default: true
        },
        /**
         * 是否多选
         * @type {Boolean}
         */
        multiple: {
            type: Boolean,
            default: false
        },
        /**
         * 是否折叠单选tag
         * @type {Boolean}
         */
        collapseTags: {
            type: Boolean,
            default: true
        },
        /**
         * 是否显示所有选中节点的父节点
         * @type {Boolean}
         */
        showAllLevels: {
            type: Boolean,
            default: true
        },
        /**
         * 输入框占位文本
         * @type {String}
         */
        placeholder: {
            type: String,
            default: '请选择部门'
        },
        /**
         * 空文本
         * @type {String}
         */
        emptyText: {
            type: String,
            default: '--'
        },
        /**
         * 选项分隔符
         * @type {Object}
         */
        separator: {
            type: String,
            default: '/'
        },
        /**
         * 是否可搜索
         * @type {Boolean}
         */
        filterable: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            deptTree: [],
            queryParams: {
                size: -1,
                parentId: 0
            }
        }
    },
    watch: {
    },
    computed: {
        depts() {

            return {}
        },
        props() {
            return {
                lazy: false,
                value: 'id',
                label: 'name',
                multiple: this.multiple,
                checkStrictly: true,
                emitPath: false,
                lazyLoad: (node, resolve) => {
                    if (!node.root) {
                        this.queryParams.parentId = node.value
                    }
                    this.deptList().then(res => {
                        resolve(res)
                    })
                }
            }
        },
        deptIds: {
            set(val) {
                this.$emit('change', val)
            },
            get() {
                return this.value
            }
        }
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    mounted() {
        treeList().then(res => {
            this.deptTree = res
        })
    },
    methods: {
        deptList() {
            return list(this.queryParams).then(res => {
                return res.records
            })
        }
    }
}

</script>
<style scoped>
.content {
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
}
</style>