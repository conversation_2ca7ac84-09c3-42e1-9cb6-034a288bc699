import request from '@/utils/request'


// 获取连接器配置列表
export function getConnectorList(params) {
  return request({
    url: 'system/auth/connector/config/list',
    method: 'get',
    params
  })
}

// 获取连接器配置详情
export function getConnectorDetail(params) {
  return request({
    url: `system/auth/connector/config`,
    method: 'get',
    params
  })
}

// 创建连接器配置
export function createConnector(data) {
  return request({
    url: 'system/auth/connector/config',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 更新连接器配置
export function updateConnector(data) {
  return request({
    url: 'system/auth/connector/config',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 更新连接器状态
export function updateConnectorStatus(id, enabled) {
  return request({
    url: 'system/auth/connector/config',
    method: 'put',
    data: {
      id,
      enabled
    },
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 删除连接器配置
export function deleteConnector(ids) {
  return request({
    url: `system/auth/connector/config/${ids}`,
    method: 'delete'
  })
}

// 获取用户已绑定的应用列表
export function getUserBindApps() {
  return request({
    url: 'system/auth/connector/config/bind/apps',
    method: 'get'
  })
}

// 获取可绑定的应用列表
export function getBindableApps() {
  return request({
    url: 'system/auth/connector/config/bindable',
    method: 'get'
  })
} 