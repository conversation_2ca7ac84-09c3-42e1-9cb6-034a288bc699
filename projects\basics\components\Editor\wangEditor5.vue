<template>
  <div class="editor" style="border: 1px solid #ccc;">
    <Toolbar class="toolbar" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
    <Editor class="text" v-model="editorContent" :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
  </div>
</template>
<style src="./style.css"></style>
<script>
import Vue from 'vue'
import { Editor, Toolbar } from "@wangeditor-next/editor-for-vue2"
import { getToken } from '@/utils/auth'

export default Vue.extend({
  components: { Editor, Toolbar },
  props: {
    content: {
      type: String,
      required: true,
      default: ''
    },
    disable: {
      type: Boolean,
      default: false
    }
  },

  data() {
    let self = this;
    return {
      editorContent: this.content,
      url: process.env.VUE_APP_FILE_URL,
      editor: null,
      html: '',
      fileUrl: process.env.VUE_APP_BASE_API + '/file/minio/uploadFile',
      toolbarConfig: {
        excludeKeys: ['group-video', 'fullScreen'],
        // toolbarKeys: [
        //   "bold", "underline", "italic", "clearStyle", "color", "bgColor", "fontSize", "fontFamily",
        //   "uploadImage", "justifyLeft", "justifyRight", "justifyCenter", "justifyJustify"
        // ]
      },
      mode: 'default',
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          uploadImage: {
            // 用户自定义上传图片
            customUpload(file, insertFn) {
              const axios = require("axios");
              const FormData = require("form-data");
              const data = new FormData();
              data.append("file", file); // file 即选中的文件
              const config = {
                method: "post",
                url: self.fileUrl, //上传图片地址
                headers: {
                  "Content-Type": "multipart/form-data",
                  'authorization': getToken(),
                },
                data: data
              };
              axios(config).then(function (res) {
                let url = self.url + res.data.data //拼接成可浏览的图片地址
                insertFn(url, url.substring(url.lastIndexOf('/') + 1), url) //插入图片
              }).catch(function (error) {
                console.log(error);
              });
            }
          }
        }
      },
    }
  },
  watch: {
    disable: {
      handler: function (n) {
        if (n) {
          this.editor && this.editor.disable(); // 禁用编辑器
        } else {
          this.editor && this.editor.enable(); // 启用编辑器
        }
      },
      immediate: true,
      deep: true
    },
    content: {
      handler: function (n) {
        this.editorContent = n
        this.$emit("onContentChange", n)
      }
    },
    editorContent(n) {
      this.$emit("onContentChange", n)
    }
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      if (this.disable) {
        this.editor.disable(); // 禁用编辑器
      } else {
        this.editor.enable(); // 启用编辑器
      }
    },
  },
  mounted() {
  },
  beforeDestroy() {
    this.editor && this.editor.destroy()
    this.editor = null
  }
})
</script>
<style lang="scss" scoped>
.toolbar {
  border-bottom: 1px solid #ccc;
}

.text {
  height: 400px;

  ::v-deep .w-e-modal {
    .babel-container {
      line-height: 30px;
      padding-left: 5px;
      padding-right: 5px;
      margin-bottom: 0px;

      >span {
        margin-bottom: 5px
      }
    }
  }
}
</style>
