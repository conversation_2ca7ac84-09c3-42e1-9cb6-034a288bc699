export class SSEClient {
  constructor(url, options = {}) {
    this.url = url
    this.options = options
    this.controller = null
  }

  async connect(data, callbacks = {}) {
    const { onMessage, onError, onComplete } = callbacks
    this.controller = new AbortController()
    const { signal } = this.controller

    try {
      const response = await fetch(this.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          ...this.options.headers
        },
        body: JSON.stringify(data),
        signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          onComplete?.()
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (!line.trim() || line === 'data:[DONE]') continue
          
          if (line.startsWith('data:')) {
            try {
              const data = JSON.parse(line.substring(5))
              onMessage?.(data)
            } catch (e) {
              console.warn('解析SSE数据失败:', e, line)
            }
          }
        }
      }
    } catch (error) {
      onError?.(error)
      throw error
    } finally {
      this.disconnect()
    }
  }

  disconnect() {
    if (this.controller) {
      this.controller.abort()
      this.controller = null
    }
  }
} 