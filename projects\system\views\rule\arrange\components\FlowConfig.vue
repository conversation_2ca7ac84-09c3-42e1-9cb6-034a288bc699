<template>
  <div class="flow-config">
    <el-form
        ref="form"
        :model="formData"
        label-width="80px"
        size="small"
    >
      <el-form-item label="流程ID" required>
        <el-input
            v-model="formData.chain_id"
            placeholder="请输入流程ID"
            @change="handleChange"
        ></el-input>
      </el-form-item>

      <el-form-item label="流程名称" required>
        <el-input
            v-model="formData.name"
            placeholder="请输入流程名称"
            @change="handleChange"
        ></el-input>
      </el-form-item>

      <el-form-item label="流程描述">
        <el-input
            type="textarea"
            v-model="formData.description"
            placeholder="请输入流程描述"
            :rows="3"
            @change="handleChange"
        ></el-input>
      </el-form-item>

      <el-form-item label="流程表达式">
        <monaco-editor
            v-model="formData.flow"
            language="liteflow"
            style="height: 200px"
            @change="handleChange"
        />
        <div class="expression-helper">
          <p>表达式语法说明:</p>
          <ul>
            <li>THEN(a, b): 串行执行a和b</li>
            <li>WHEN(a, b): 并行执行a和b</li>
            <li>IF(condition, a, b): 条件判断,满足执行a,否则执行b</li>
            <li>SWITCH(condition).to(a, b, c): 多分支条件判断</li>
            <li>FOR(3).do(a): 循环执行a三次</li>
            <li>WHILE(condition).do(a): 条件循环执行a</li>
          </ul>
        </div>
      </el-form-item>

      <el-form-item label="输入参数">
        <param-table
            v-model="formData.input_params"
            @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="输出参数">
        <param-table
            v-model="formData.output_params"
            @change="handleChange"
        />
      </el-form-item>

      <el-form-item label="超时设置">
        <el-input-number
            v-model="formData.timeout"
            :min="0"
            :step="1000"
            placeholder="超时时间(毫秒)"
            @change="handleChange"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="重试设置">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-input-number
                v-model="formData.retry.times"
                :min="0"
                placeholder="重试次数"
                @change="handleChange"
            ></el-input-number>
          </el-col>
          <el-col :span="12">
            <el-input-number
                v-model="formData.retry.interval"
                :min="0"
                :step="1000"
                placeholder="重试间隔(毫秒)"
                @change="handleChange"
            ></el-input-number>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import ParamTable from './ParamTable.vue'
import MonacoEditor from './MonacoEditor.vue'

export default {
  name: 'FlowConfig',

  components: {
    ParamTable,
    MonacoEditor
  },

  props: {
    flow: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      formData: {
        chain_id: '',
        name: '',
        description: '',
        flow: '',
        input_params: {},
        output_params: {},
        timeout: 30000,
        retry: {
          times: 0,
          interval: 1000
        }
      }
    }
  },

  watch: {
    flow: {
      immediate: true,
      handler(flow) {
        this.formData = {
          ...this.formData,
          ...JSON.parse(JSON.stringify(flow))
        }
      }
    }
  },

  methods: {
    handleChange() {
      this.$emit('update', this.formData)
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-config {
  .expression-helper {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;

    p {
      margin: 0 0 8px;
      font-weight: bold;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style> 