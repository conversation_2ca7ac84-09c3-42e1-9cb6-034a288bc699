<template>
  <div class="resource-permission-container">
    <el-row :gutter="24">
      <!-- 左侧角色/部门列表 -->
      <el-col :span="6">
        <div class="card-container role-panel">
          <div class="card-header">
            <div class="header-title">
              <h2>
                <!-- 添加视图切换按钮 -->
                <span>{{ viewType === 'role' ? '角色列表' : '部门列表' }}</span>
                <div class="view-switcher">
                  <el-tooltip :content="viewType === 'role' ? '切换到部门视角' : '切换到角色视角'" placement="top">
                    <el-button 
                      type="text" 
                      class="switch-btn" 
                      @click="handleViewTypeSwitch">
                      <i :class="viewType === 'role' ? 'el-icon-office-building' : 'el-icon-user'"></i>
                      <span>{{ viewType === 'role' ? '部门视角' : '角色视角' }}</span>
                    </el-button>
                  </el-tooltip>
                </div>
              </h2>
            </div>
            <div class="search-bar">
              <el-input
                v-model="searchKeyword"
                :placeholder="viewType === 'role' ? '搜索角色名称' : '搜索部门名称'"
                prefix-icon="el-icon-search"
                clearable
                @input="filterItems"
              />
            </div>
          </div>
          <div class="card-body role-list">
            <!-- 角色视图 -->
            <el-radio-group v-if="viewType === 'role'" v-model="selectedRoleId" @change="handleRoleChange">
              <template v-if="roleList.length > 0">
                <div
                  v-for="role in filteredRoleList"
                  :key="role.id"
                  class="role-item"
                  :class="{ 'selected': selectedRoleId === role.id }"
                >
                  <el-radio :label="role.id" :disabled="role.code === 'admin'">
                    <div class="role-info">
                      <div class="role-name">
                        <i class="el-icon-user"></i>
                        <span>{{ role.name }}</span>
                        <el-tag v-if="role.code === 'admin'" size="mini" type="danger">超级管理员</el-tag>
                      </div>
                      <div class="role-code">{{ role.code }}</div>
                      <div class="role-status">
                        <el-tag :type="role.valid ? 'success' : 'danger'" size="mini" effect="dark">
                          {{ role.valid ? '启用' : '禁用' }}
                        </el-tag>
                      </div>
                    </div>
                  </el-radio>
                </div>
              </template>
              <div v-else class="empty-data">
                <i class="el-icon-document"></i>
                <span>暂无角色数据</span>
              </div>
            </el-radio-group>
            
            <!-- 部门视图 -->
            <el-tree
              v-else
              ref="deptTree"
              :data="deptTreeData"
              :props="{
                label: 'name',
                children: 'children'
              }"
              node-key="id"
              highlight-current
              :filter-node-method="filterDeptNode"
              @node-click="handleDeptNodeClick"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <div class="node-content">
                  <i class="el-icon-office-building"></i>
                  <span :class="{ 'highlight': isHighlighted(node, searchKeyword) }">
                    {{ node.label }}
                  </span>
                  <span class="dept-code" v-if="data.code">({{ data.code }})</span>
                </div>
              </span>
            </el-tree>
          </div>
        </div>
      </el-col>

      <!-- 右侧权限面板 -->
      <el-col :span="18">
        <div class="card-container resource-panel">
          <div class="card-header compact-header">
            <div class="header-title-row">
              <div class="header-title">
                <h2>
                  <span class="title-text">{{ permissionType === 'resource' ? '菜单权限' : '接口权限' }}</span>
                  <div class="permission-type-tabs">
                    <el-radio-group v-model="permissionType" size="mini">
                      <el-radio-button label="resource">
                        <i class="el-icon-menu"></i> 菜单
                      </el-radio-button>
                      <el-radio-button label="api">
                        <i class="el-icon-connection"></i> 接口
                      </el-radio-button>
                    </el-radio-group>
                  </div>
                </h2>
              </div>
              
              <div class="action-bar">
                <el-input
                  v-if="permissionType === 'resource'"
                  v-model="permissionKeyword" 
                  placeholder="搜索资源名称"
                  prefix-icon="el-icon-search"
                  clearable
                  @clear="resetFilter"
                  size="small"
                  style="width: 220px; margin-right: 12px;"
                />
                <el-button size="small" type="primary" @click="savePermission" :loading="saveLoading" :disabled="!canSave">
                  <i class="el-icon-check"></i> 保存配置
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="card-body resource-tree-container">
            <div v-if="!canSave" class="empty-selection">
              <i class="el-icon-warning-outline"></i>
              <p>{{ viewType === 'role' ? '请先选择左侧的角色' : '请先选择左侧的部门' }}</p>
            </div>
            
            <template v-else-if="viewType === 'role' && selectedRole && selectedRole.code === 'admin'">
              <div class="admin-tip">
                <i class="el-icon-info-circle"></i>
                <p>超级管理员默认拥有所有权限，无需配置</p>
              </div>
            </template>

            <!-- 菜单资源权限树 -->
            <resource-permission
              v-else-if="permissionType === 'resource'"
              ref="resourcePermission"
              :selectedResourceIds="selectedResourceIds"
              :keyword="resourceKeyword"
            />
            
            <!-- 接口权限 -->
            <api-permission
              v-else
              ref="apiPermission"
              :selectedRole="selectedItem"
              :keyword="apiKeyword"
              @selection-change="handleApiSelectionChange"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getRoleList, updateRole } from '@system/api/sys/role'
import { saveRolePermissions } from '@system/api/sys/resourcePermission'
import { getDeptTreeList } from '@system/api/sys/treeAccount'
import { setDeptResources, setDeptPermissions } from '@system/api/sys/treeDept'
import ResourcePermission from './components/ResourcePermission.vue'
import ApiPermission from './components/ApiPermission.vue'

export default {
  name: 'PermissionManager',
  components: {
    ResourcePermission,
    ApiPermission
  },
  data() {
    return {
      // 视图类型：角色 role 或 部门 dept
      viewType: 'role',
      
      // 角色相关数据
      roleList: [],
      filteredRoleList: [],
      selectedRoleId: null,
      selectedRole: null,
      
      // 部门相关数据
      deptTreeData: [],
      selectedDeptId: null,
      selectedDept: null,
      
      // 通用搜索关键字
      searchKeyword: '',
      
      // 权限相关
      resourceKeyword: '',
      apiKeyword: '',
      permissionKeyword: '',
      selectedResourceIds: [],
      selectedApiList: [],
      saveLoading: false,
      permissionType: 'resource'
    }
  },
  computed: {
    // 当前选中的项（角色或部门）
    selectedItem() {
      return this.viewType === 'role' ? this.selectedRole : this.selectedDept;
    },
    
    // 是否可以保存（选中了角色或部门，且不是admin角色）
    canSave() {
      if (this.viewType === 'role') {
        return this.selectedRoleId && this.selectedRoleId !== 0 && 
          (!this.selectedRole || this.selectedRole.code !== 'admin');
      } else {
        return this.selectedDeptId && this.selectedDeptId !== 0;
      }
    }
  },
  watch: {
    permissionType() {
      // 权限类型变更时，如果已选角色或部门，确保权限数据更新
      if (this.selectedItem) {
        this.updatePermissionData()
      }
    },
    permissionKeyword(val) {
      // 根据当前类型同步到对应关键字
      if (this.permissionType === 'resource') {
        this.resourceKeyword = val
      } else {
        this.apiKeyword = val
      }
    },
    searchKeyword(val) {
      if (this.viewType === 'role') {
        this.filterItems();
      } else if (this.$refs.deptTree) {
        this.$refs.deptTree.filter(val);
      }
    }
  },
  created() {
    this.getRoles()
  },
  methods: {
    // 切换视图类型（角色/部门）
    handleViewTypeSwitch() {
      this.viewType = this.viewType === 'role' ? 'dept' : 'role';
      this.searchKeyword = ''; // 清空搜索关键字
      
      // 重置选中状态
      this.selectedRoleId = null;
      this.selectedRole = null;
      this.selectedDeptId = null;
      this.selectedDept = null;
      this.selectedResourceIds = [];
      this.selectedApiList = [];
      
      // 加载相应数据
      if (this.viewType === 'role') {
        if (this.roleList.length === 0) {
          this.getRoles();
        }
      } else {
        if (this.deptTreeData.length === 0) {
          this.getDeptTree();
        }
      }
    },
    
    // 获取角色列表
    async getRoles() {
      try {
        const data = await getRoleList({
          current: 1,
          size: -1 // 获取所有角色
        })
        this.roleList = data?.records || []
        this.filteredRoleList = [...this.roleList]
      } catch (error) {
        console.error('获取角色列表失败:', error)
        this.$message.error('获取角色列表失败')
      }
    },
    
    // 获取部门树
    async getDeptTree() {
      try {
        const data = await getDeptTreeList();
        this.deptTreeData = data || [];
      } catch (error) {
        console.error('获取部门树失败:', error);
        this.$message.error('获取部门树失败');
      }
    },

    // 判断节点是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false;
      return node.label.toLowerCase().includes(keyword.toLowerCase());
    },

    // 部门树节点过滤
    filterDeptNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().includes(value.toLowerCase()) || 
             (data.code && data.code.toLowerCase().includes(value.toLowerCase()));
    },

    // 过滤列表项
    filterItems() {
      if (this.viewType === 'role') {
        if (!this.searchKeyword) {
          this.filteredRoleList = [...this.roleList]
          return
        }
        
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredRoleList = this.roleList.filter(role => 
          role.name.toLowerCase().includes(keyword) || 
          role.code.toLowerCase().includes(keyword)
        )
      }
    },

    // 角色选择变更
    handleRoleChange(roleId) {
      // 先清空当前选中的资源权限
      this.selectedResourceIds = []
      this.selectedApiList = []
      
      // 然后设置新选中的角色
      this.selectedRoleId = roleId
      this.selectedRole = this.roleList.find(role => role.id === roleId)
      
      if (!roleId || roleId === 0 || (this.selectedRole && this.selectedRole.code === 'admin')) {
        return
      }
      
      // 最后更新权限数据
      this.updatePermissionData()
    },
    
    // 部门节点点击
    handleDeptNodeClick(dept) {
      // 先清空当前选中的资源权限
      this.selectedResourceIds = [];
      this.selectedApiList = [];
      
      // 设置新选中的部门
      this.selectedDeptId = dept.id;
      this.selectedDept = dept;
      
      // 更新权限数据
      this.updatePermissionData();
    },

    // 更新权限数据
    updatePermissionData() {
      if (!this.selectedItem) return;
      
      if (this.permissionType === 'resource') {
        // 直接从选中项中获取resourceIds
        this.selectedResourceIds = this.selectedItem.resourceIds || []
        
        // 确保在下一个事件循环中更新树的选中状态
        this.$nextTick(() => {
          if (this.$refs.resourcePermission) {
            this.$refs.resourcePermission.$refs.resourceTree.setCheckedKeys(this.selectedResourceIds)
          }
        })
      } else {
        // 从permissions中获取接口权限
        this.selectedApiList = this.selectedItem.permissions || []
      }
    },

    // 重置过滤
    resetFilter() {
      if (this.permissionType === 'resource' && this.$refs.resourcePermission) {
        this.$refs.resourcePermission.resetResourceFilter()
      }
    },

    // 处理接口选择变更
    handleApiSelectionChange(permissions) {
      this.selectedApiList = permissions
    },

    // 保存权限配置
    async savePermission() {
      if (!this.canSave) {
        this.$message.warning(this.viewType === 'role' ? '请先选择角色' : '请先选择部门')
        return
      }
      
      this.saveLoading = true
      try {
        if (this.viewType === 'role') {
          // 角色权限保存逻辑
          if (this.permissionType === 'resource') {
            // 获取选中的资源ID
            const resourceIds = this.$refs.resourcePermission.getSelectedResourceIds()
            
            // 构建更新角色的对象 - 确保包含所有必要的原始字段
            const roleToUpdate = { 
              ...this.selectedRole,
              resourceIds, 
              // 如果permissions为null，则保持为null而不是undefined
              permissions: this.selectedRole.permissions
            }
            
            // 使用API: PUT /sys/role 接受一个角色数组
            await updateRole([roleToUpdate])
            
            // 更新当前角色对象中的resourceIds
            if (this.selectedRole) {
              this.selectedRole.resourceIds = resourceIds
              
              // 更新角色列表中对应角色的resourceIds
              const roleIndex = this.roleList.findIndex(role => role.id === this.selectedRoleId)
              if (roleIndex !== -1) {
                this.roleList[roleIndex].resourceIds = resourceIds
              }
            }
          } else {
            // 保存接口权限 - 更新role对象中的permissions
            const permissions = this.selectedApiList
            
            // 构建更新角色的对象 - 确保包含所有必要的原始字段
            const roleToUpdate = { 
              ...this.selectedRole,
              permissions,
              // 确保resourceIds字段存在
              resourceIds: this.selectedRole.resourceIds || []
            }
            
            // 使用API: PUT /sys/role 接受一个角色数组
            await updateRole([roleToUpdate])
            
            // 更新当前角色对象中的permissions
            if (this.selectedRole) {
              this.selectedRole.permissions = permissions
              
              // 更新角色列表中对应角色的permissions
              const roleIndex = this.roleList.findIndex(role => role.id === this.selectedRoleId)
              if (roleIndex !== -1) {
                this.roleList[roleIndex].permissions = permissions
              }
            }
          }
          
          this.$message.success('角色权限配置保存成功')
        } else {
          // 部门权限保存逻辑
          if (this.permissionType === 'resource') {
            // 获取选中的资源ID
            const resourceIds = this.$refs.resourcePermission.getSelectedResourceIds()
            
            // 调用设置部门资源关联的API
            await setDeptResources(this.selectedDeptId, resourceIds)
            
            // 更新当前部门对象中的resourceIds
            if (this.selectedDept) {
              this.selectedDept.resourceIds = resourceIds
            }
          } else {
            // 保存接口权限
            const permissions = this.selectedApiList
            
            // 调用设置部门权限关联的API
            await setDeptPermissions(this.selectedDeptId, permissions)
            
            // 更新当前部门对象中的permissions
            if (this.selectedDept) {
              this.selectedDept.permissions = permissions
            }
          }
          
          this.$message.success('部门权限配置保存成功')
        }
      } catch (error) {
        console.error('保存权限配置失败:', error)
        this.$message.error('保存权限配置失败')
      } finally {
        this.saveLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-permission-container {
  height: calc(100vh - 75px);
  background: inherit;
  padding: 12px;
  margin: 12px;
  overflow: hidden;

  .el-row {
    height: 100%;
    width: 100%;
    margin: 0 !important;
    
    .el-col {
      height: 100%;
    }
  }

  .card-container {
    height: 100%;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    display: flex;
    flex-direction: column;
    // overflow: hidden;
    overflow-y: auto;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(31, 45, 61, 0.1);
    }
  }

  .card-header {
    padding: 20px;
    border-bottom: 1px solid #eef1f7;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    
    &.compact-header {
      padding: 16px 20px;
      
      .header-title-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .header-title {
          h2 {
            margin: 0;
          }
        }
        
        .action-bar {
          margin-top: 0;
        }
      }
    }
    
    .header-title {
      h2 {
        margin: 0 0 12px 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
        
        .title-text {
          margin-right: 16px;
        }
        
        .permission-type-tabs {
          position: relative;
          top: -1px;
          
          ::v-deep .el-radio-group {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border-radius: 6px;
            overflow: hidden;
            
            .el-radio-button {
              &:first-child .el-radio-button__inner {
                border-left: none;
              }
              
              .el-radio-button__inner {
                padding: 8px 16px;
                font-size: 13px;
                height: 32px;
                line-height: 16px;
                display: flex;
                align-items: center;
                gap: 5px;
                transition: all 0.3s ease;
                
                i {
                  font-size: 14px;
                }
              }
              
              &.is-active .el-radio-button__inner {
                background-color: #409EFF;
                color: white;
                box-shadow: 0 2px 12px rgba(64, 158, 255, 0.4);
              }
            }
          }
        }
        
        // 新增视图切换按钮样式
        .view-switcher {
          .switch-btn {
            padding: 3px 8px;
            color: #409EFF;
            background: rgba(64, 158, 255, 0.1);
            border: none;
            border-radius: 4px;
            transition: all 0.3s ease;
            
            &:hover {
              background: rgba(64, 158, 255, 0.2);
              transform: translateY(-1px);
            }
            
            i {
              margin-right: 4px;
              font-size: 14px;
            }
            
            span {
              font-size: 12px;
              font-weight: 500;
            }
          }
        }
      }
    }

    .search-bar {
      width: 100%;
      margin-top: 8px;
      
      .el-input {
        ::v-deep .el-input__inner {
          border-radius: 8px;
          height: 36px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }
      }
    }

    .action-bar {
      display: flex;
      align-items: center;
      margin-top: 8px;
      
      .el-button {
        padding: 8px 14px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
      
      ::v-deep .el-input__inner {
        border-radius: 6px;
        height: 32px;
        background: #fff;
        border: 1px solid #e0e5ee;
        
        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
        }
        
        &:hover {
          border-color: #c0d0e9;
        }
      }
    }
  }

  .card-body {
    flex: 1;
    overflow: auto;
    padding: 20px;
    height: 0;
    min-height: 0;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &.resource-tree-container {
      padding-top: 0;
    }
  }

  .role-list {
    .el-radio-group {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .role-item {
      border-radius: 10px;
      padding: 12px 16px;
      transition: all 0.3s ease;
      background-color: #f8f9fb;
      border: 1px solid #ebeef5;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
      
      &.selected {
        background-color: #ecf5ff;
        border-color: #b3d8ff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          left: -2px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 60%;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 2px;
        }
      }
      
      .el-radio {
        margin-right: 0;
        width: 100%;
        display: flex;
        align-items: flex-start;
        
        ::v-deep .el-radio__label {
          padding-left: 8px;
          width: 100%;
        }
        
        ::v-deep .el-radio__input.is-disabled + .el-radio__label {
          color: #909399;
        }
      }
      
      .role-info {
        width: 100%;
        
        .role-name {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 6px;
          
          i {
            color: #409EFF;
            font-size: 16px;
          }
          
          .el-tag {
            margin-left: auto;
            border-radius: 4px;
            font-weight: bold;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 22px;
            line-height: 1;
          }
        }
        
        .role-code {
          color: #909399;
          font-size: 12px;
          margin-bottom: 6px;
          padding-left: 24px;
        }
        
        .role-status {
          display: flex;
          justify-content: flex-end;
          
          .el-tag {
            border-radius: 12px;
            padding: 2px 10px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 22px;
            line-height: 1;
          }
        }
      }
    }

    .empty-data {
      color: #909399;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      
      i {
        font-size: 32px;
        margin-bottom: 8px;
        color: #c0c4cc;
      }
    }
    
    // 部门树样式
    ::v-deep .el-tree {
      background: transparent;
      
      .el-tree-node__content {
        height: 36px;
        border-radius: 8px;
        margin: 4px 0;
        transition: all 0.2s ease;
        
        &:hover {
          background-color: #f5f7fa;
        }
      }
      
      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #ecf5ff !important;
        color: #409EFF;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
      }
      
      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .node-content {
          display: flex;
          align-items: center;
          gap: 8px;
          
          i {
            color: #409EFF;
            font-size: 14px;
          }
          
          .dept-code {
            color: #909399;
            font-size: 12px;
            margin-left: 4px;
          }
        }
      }
    }
  }

  .resource-tree-container {
    position: relative;
    overflow: auto;
    padding-top: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .empty-selection,
    .admin-tip {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      color: #606266;
      background-color: #fff;
      border: 1px dashed #d9d9d9;
      border-radius: 8px;
      margin-top: 20px;
      flex-shrink: 0;
      
      i {
        font-size: 60px;
        margin-bottom: 20px;
        color: #e6a23c;
        opacity: 0.8;
      }
      
      p {
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .admin-tip {
      i {
        color: #409EFF;
      }
    }
    
    .resource-permission-container {
      flex: 1;
      min-height: 0;
      height: auto !important;
    }
  }
}

.highlight {
  color: #409EFF;
  font-weight: 600;
  position: relative;
  z-index: 1;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: rgba(64, 158, 255, 0.2);
    z-index: -1;
    border-radius: 3px;
  }
}
</style> 