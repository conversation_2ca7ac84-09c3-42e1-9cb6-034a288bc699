import uniqueId from '@form-create/utils/lib/unique';
import { localeOptions, localeProps, makeTreeOptions, makeTreeOptionsRule } from '@form-create/designer-zrw/src/utils';

const label = 'tree选择器';
const name = 'treeSelect';

export default {
    menu: 'custom',
    icon: 'icon-tree',
    label,
    name,
    event: ['change'],
    validate: ['string'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            $required: false,
            props: {
                options: makeTreeOptions(t('props.options'), { label: 'label', value: 'id' }, 3)
            },
        };
    },
    props(_, { t }) {

        return localeProps(t, 'custom.props', [
            makeTreeOptionsRule({ t, to: 'props.options' }),
            {
                type: 'switch',
                field: 'multiple',
                title: '是否多选'
            },
            {
                type: 'switch',
                field: 'disabled',
                title: t('custom.props.disabled')
            }]);
    }
};
