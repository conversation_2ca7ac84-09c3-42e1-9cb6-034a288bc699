import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

import router, { replaceImport } from "@/router"
replaceImport('/login', import('@authlogin/views/index.vue'))
replaceImport('/auth', import('@authlogin/views/auth.vue')).end()

Vue.config.productionTip = false
// 全局注册 Element UI
Vue.use(ElementUI)

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')


router.moduleRoutes([
  {
    path: '/',
    redirect: '/chat'
  },
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('./views/ChatView.vue'),

  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: () => import('./views/KnowledgeView.vue'),
  },
  {
    path: '/kb-chat',
    name: '<PERSON><PERSON>hat',
    component: () => import('./views/KBChatView.vue'),
    meta: {
      title: '知识库对话'
    }
  }
])
