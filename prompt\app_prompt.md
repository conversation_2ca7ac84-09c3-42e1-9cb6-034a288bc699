# 代理应用对接

1. 首先删除认证配置及其相关组件，取而代之的是一个选择连接器的组件

其逻辑是，代理应用会选择可以使用哪些连接器。可以多选

接口如下

## 获取连接器列表（size=-1为全部）
```bash
curl --location --request GET 'http://localhost:8007/integration/connector/list?current=1&size=10' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive'
```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "records": [
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-04 14:34:53",
                "updateTime": null,
                "id": "1952256908972843010",
                "remark": "",
                "code": "extreme",
                "name": "201Extreme-Plus",
                "type": "HTTP",
                "description": "",
                "enabled": true,
                "authActuator": "CHAIN_AUTH",
                "tokenCacheSeconds": 86400,
                "config": {
                    "type": "HTTP",
                    "baseUrl": "http://*************/prod-api",
                    "authExpression": "${login.data.accessToken}",
                    "authLocation": "HEADER",
                    "authParamName": "Authorization",
                    "authActuator": "CHAIN_AUTH"
                }
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    },
    "success": true
}
```

## 选择/更新连接器

```bash
curl --location --request POST 'http://localhost:8007/integration/app/use/connectors' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive' \
--data-raw '{
    "appId": "1",
    "connectorIds": [
        "8",
        "98"
    ]
}'
```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true,
    "success": true
}
```

## 获取应用关联的连接器ID列表

```bash
curl --location --request GET 'http://localhost:8007/integration/app/1/connectors' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive'
```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        98,
        8
    ],
    "success": true
}
```