<template>
  <div class="param-table">
    <div class="table-toolbar">
      <el-button
          type="text"
          icon="el-icon-plus"
          @click="addParam"
      >添加参数</el-button>
    </div>

    <el-table
        :data="tableData"
        border
        size="small"
    >
      <el-table-column label="参数名" min-width="120">
        <template slot-scope="scope">
          <el-input
              v-model="scope.row.name"
              placeholder="参数名"
              @change="handleParamChange"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column label="参数值" min-width="200">
        <template slot-scope="scope">
          <el-input
              v-model="scope.row.value"
              placeholder="参数值或引用表达式"
              @change="handleParamChange"
          >
            <el-dropdown
                slot="append"
                trigger="click"
                @command="command => handleReference(command, scope.row)"
            >
              <el-button type="text">
                <i class="el-icon-link"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="input">输入参数</el-dropdown-item>
                <el-dropdown-item
                    v-for="node in availableNodes"
                    :key="node.id"
                    :command="node.id"
                >{{ node.name }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-input>
        </template>
      </el-table-column>

      <el-table-column label="必填" width="80" align="center">
        <template slot-scope="scope">
          <el-checkbox
              v-model="scope.row.required"
              @change="handleParamChange"
          ></el-checkbox>
        </template>
      </el-table-column>

      <el-table-column label="描述" min-width="150">
        <template slot-scope="scope">
          <el-input
              v-model="scope.row.description"
              placeholder="参数描述"
              @change="handleParamChange"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="80" align="center">
        <template slot-scope="scope">
          <el-button
              type="text"
              icon="el-icon-delete"
              @click="removeParam(scope.$index)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'ParamTable',

  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    availableNodes: {
      type: Array,
      default: () => []
    }
  },

  computed: {
    tableData() {
      return Object.entries(this.value).map(([name, config]) => ({
        name,
        value: typeof config === 'object' ? config.value : config,
        required: config.required || false,
        description: config.description || ''
      }))
    }
  },

  methods: {
    addParam() {
      const param = {
        name: '',
        value: '',
        required: false,
        description: ''
      }
      this.updateValue([...this.tableData, param])
    },

    removeParam(index) {
      const data = [...this.tableData]
      data.splice(index, 1)
      this.updateValue(data)
    },

    handleParamChange() {
      this.updateValue(this.tableData)
    },

    handleReference(command, row) {
      if (command === 'input') {
        row.value = '{$input.}'
      } else {
        row.value = `{${command}.}`
      }
      this.handleParamChange()
    },

    updateValue(data) {
      const value = data.reduce((acc, param) => {
        if (param.name) {
          acc[param.name] = {
            value: param.value,
            required: param.required,
            description: param.description
          }
        }
        return acc
      }, {})

      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.param-table {
  .table-toolbar {
    margin-bottom: 10px;
  }
}
</style> 