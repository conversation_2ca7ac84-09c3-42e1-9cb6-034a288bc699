<template>
  <div class="knowledge-list">
    <div v-for="item in items" :key="item.id" class="knowledge-item">
      <div class="item-content">
        <h3>{{ item.title }}</h3>
        <p>{{ item.content }}</p>
      </div>
      <div class="item-actions">
        <button class="edit-btn" @click="$emit('edit', item)">编辑</button>
        <button class="delete-btn" @click="$emit('delete', item.id)">删除</button>
      </div>
    </div>
    <div v-if="items.length === 0" class="empty-state">
      暂无知识条目
    </div>
  </div>
</template>

<script>
export default {
  name: 'KnowledgeList',
  props: {
    items: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
.knowledge-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.knowledge-item {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.item-content {
  flex: 1;
}

.item-content h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.item-content p {
  margin: 0;
  color: #666;
}

.item-actions {
  display: flex;
  gap: 10px;
}

button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.edit-btn {
  background: #42b983;
  color: white;
}

.delete-btn {
  background: #ff4444;
  color: white;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
}
</style> 