<template>
  <div
    class="editable-input"
    :class="{
      'is-editing': isEditing,
      'is-new': isNew,
      'is-hovered': isHovered,
    }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <el-input
      ref="input"
      v-model="tempValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      :readonly="!isEditing && !isNew"
      :style="{ width: width }"
      @blur="handleBlur"
    ></el-input>

    <div v-show="isChange" class="tip_value">
      <span class="tip_value_txt" :style="{ width: width }">{{ value }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "EditableInput",

  props: {
    // 初始值
    value: {
      type: [String, Number],
      default: "",
    },
    // 编辑器占位符
    placeholder: {
      type: String,
      default: "请输入",
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 清空
    clearable: {
      type: Boolean,
      default: false,
    },
    // 失焦时是否立即保存
    saveOnBlur: {
      type: Boolean,
      default: true,
    },
    // 编辑状态
    isEditing: {
      type: Boolean,
      default: false,
    },
    // 新增状态
    isNew: {
      type: Boolean,
      default: false,
    },
    // 宽度
    width: {
      type: [String],
      default: "100%",
    },
  },

  data() {
    return {
      tempValue: "",
      isHovered: false,
      isChange: false,
    };
  },

  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.tempValue = newVal;
      },
    },
    isHovered(newVal) {
      if (newVal && (this.isEditing || this.isNew)) {
        this.$nextTick(() => {
          this.focusInput();
        });
      }
    },
    isEditing: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.tempValue = this.value;
          this.$nextTick(() => {
            this.focusInput();
          });
        } else {
          this.tempValue = this.value;
          this.isChange = false;
        }
      },
    },
    isNew: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.tempValue = "";
          this.$nextTick(() => {
            this.focusInput();
          });
        }
      },
    },
  },

  methods: {
    handleBlur() {
      if (!this.isEditing && this.isNew) {
        this.$emit("change", this.tempValue);
      } else {
        this.tempValue !== this.value ? (this.isChange = true) : (this.isChange = false);
        this.$emit("tempChange", this.tempValue);
      }
    },

    handleMouseEnter() {
      this.isHovered = true;
    },

    handleMouseLeave() {
      this.isHovered = false;
    },

    focusInput() {
      const input = this.$refs.input;
      if (input) {
        input.focus();
      }
    },
  },
};
</script>

<style scoped>
.editable-input {
  display: inline-block;
  position: relative;
  width: 100%;
}

.tip_value {
  display: flex;
  justify-content: left;
  color: #b4b4b4;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tip_value_txt {
  margin-left: 10px;
  margin-top: 5px;
  text-align: left;
}

/* 基础状态 */
.editable-input :deep(.el-input__inner) {
  border: 0px solid transparent;
  background-color: transparent;
  transition: all 0.3s;
  cursor: pointer;
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
  text-align: center;
}

/* 编辑状态下的hover效果 */
.editable-input:not(.is-new).is-hovered :deep(.el-input__inner) {
  background-color: #f5f7fa;
  border-color: transparent;
}

/* 编辑状态激活时 */
.editable-input.is-editing :deep(.el-input__inner) {
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  cursor: text;
  text-align: left;
}

/* 编辑状态下的focus效果 */
.editable-input.is-editing :deep(.el-input__inner:focus) {
  border-color: #409eff;
}

/* 新增状态下的hover效果 */
.editable-input.is-new.is-hovered :deep(.el-input__inner) {
  border: 1px solid #409eff;
  background: #ffffff;
}

/* 新增状态激活时 */
.editable-input.is-new :deep(.el-input__inner) {
  border: 1px solid #c3c3c3;
  background-color: #ffffff;
  cursor: text;
  text-align: left;
}

/* 新增状态下的focus效果 */
.editable-input.is-new :deep(.el-input__inner:focus) {
  border-color: #1c85ee !important;
  background-color: #ffffff;
}

/* 禁用状态 */
.editable-input :deep(.el-input.is-disabled .el-input__inner) {
  background-color: transparent;
  border-color: transparent;
  cursor: not-allowed;
}
</style>
