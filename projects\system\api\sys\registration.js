import request from '@/utils/request'

const api = CONSTANT.SYSTEM

// 获取用户信息
export function getUserInfo() {
  return request({
    url: api + '/info',
    method: 'get',
  })
}

// 获取注册申请列表
export function getRegistrationList(params) {
  return request({
    url: api + '/sys/registration/list',
    method: 'get',
    params
  })
}

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: api + '/sys/role/list',
    method: 'get',
    params
  })
}

// 审核注册申请
export function auditRegistration(data) {
  return request({
    url: api + '/sys/registration/audit',
    method: 'put',
    data
  })
}

export function putProfile(data) {
  return request({
    url: api + '/profile',
    method: 'put',
    data
  })
}

