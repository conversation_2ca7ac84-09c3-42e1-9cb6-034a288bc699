<!-- 日志分析组件 -->
<template>
  <div class="log-analysis">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <div class="search-area">
          <!-- 左侧搜索条件 -->
          <div class="search-conditions">
            <el-form-item label="TraceId">
              <el-input 
                v-model="searchForm.traceId" 
                placeholder="请输入 TraceId"
                clearable
                size="small"
                style="width: 200px"
                @keyup.enter.native="handleSearch">
              </el-input>
            </el-form-item>
            <el-form-item label="应用">
              <el-select 
                v-model="searchForm.appNames" 
                placeholder="请选择应用"
                multiple
                collapse-tags
                clearable
                size="small"
                style="width: 200px"
                @change="handleAppNamesChange">
                <el-option
                  v-for="app in appList"
                  :key="app.key"
                  :label="app.key"
                  :value="app.key">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="searchForm.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                style="width: 320px"
                size="small"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
          </div>

          <!-- 右侧功能区 -->
          <div class="function-area">
            <div class="button-group">
              <el-button type="primary" size="small" @click="handleSearch" :loading="loading" icon="el-icon-search">查询</el-button>
              <el-button size="small" @click="handleReset" icon="el-icon-refresh">重置</el-button>
            </div>
            
            <div class="display-fields">
              <el-popover
                placement="bottom"
                width="400"
                trigger="click">
                <el-checkbox-group v-model="displayFields" class="field-group">
                  <el-checkbox label="timestamp">时间</el-checkbox>
                  <el-checkbox label="level">日志级别</el-checkbox>
                  <el-checkbox label="app">应用名称</el-checkbox>
                  <el-checkbox label="thread">线程</el-checkbox>
                  <el-checkbox label="class">类名</el-checkbox>
                  <el-checkbox label="message">日志内容</el-checkbox>
                  <el-checkbox label="traceId">TraceId</el-checkbox>
                </el-checkbox-group>
                <el-button slot="reference" type="text" size="small">
                  <i class="el-icon-s-operation"></i>
                  显示字段
                </el-button>
              </el-popover>
            </div>

            <div class="auto-refresh">
              <el-switch
                v-model="autoRefresh"
                active-text="实时追踪"
                size="small"
                @change="handleAutoRefreshChange">
              </el-switch>
              <el-input-number 
                v-model="refreshSeconds"
                :min="1"
                :max="60"
                size="small"
                controls-position="right"
                :disabled="!autoRefresh"
                @change="handleRefreshSecondsChange">
              </el-input-number>
              <span class="refresh-unit">秒</span>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>

    <!-- 日志展示 -->
    <el-card class="log-card">
      <div class="log-console" 
           ref="logConsole"
           @scroll="handleScroll">
        <div v-for="(log, index) in sortedLogs" 
             :key="log._source['@timestamp'] + index" 
             class="log-line"
             :class="[
               getLogLevelClass(log._source.level),
               { 'new-log': log.isNew }
             ]"
             :style="{
               willChange: 'transform, opacity'
             }">
          <span v-if="displayFields.includes('timestamp')" class="log-time">{{ formatTime(log._source['@timestamp']) }}</span>
          <span v-if="displayFields.includes('level')" class="log-level">{{ log._source.level }}</span>
          <span v-if="displayFields.includes('app')" class="log-app">[{{ log._source.app }}]</span>
          <span v-if="displayFields.includes('thread')" class="log-thread">[{{ log._source.thread }}]</span>
          <span v-if="displayFields.includes('class')" class="log-class">{{ log._source.class }}</span>
          <span v-if="displayFields.includes('message')" class="log-message">{{ log._source.message }}</span>
          <el-button 
            v-if="displayFields.includes('traceId') && log._source.tid"
            type="text" 
            class="trace-link"
            @click="handleTraceSearch(log._source.tid)">
            TraceId: {{ log._source.tid }}
          </el-button>
        </div>
        <div v-if="noMore" class="no-more">
          没有更多数据了
        </div>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.log-analysis {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 8px;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  
  .search-card {
    flex: 0 0 auto;
    margin-bottom: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
    background: #fff;
    
    .search-form {
      padding: 12px;
      
      .search-area {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        min-height: 32px;
        
        .search-conditions {
          flex: 1;
          display: flex;
          align-items: center;
          flex-wrap: nowrap;
          gap: 12px;
          
          .el-form-item {
            margin: 0;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            
            &__label {
              color: #606266;
              padding-right: 8px;
              font-weight: normal;
              font-size: 13px;
              line-height: 32px;
            }

            .el-input {
              width: 200px;
            }
            
            .el-select {
              width: 200px;
            }
            
            .el-date-picker {
              width: 320px;
              
              .el-range-input {
                font-size: 12px !important;
              }
              
              .el-range-separator {
                line-height: 24px;
              }
            }
          }
        }
        
        .function-area {
          display: flex;
          align-items: center;
          gap: 16px;
          padding-left: 16px;
          border-left: 1px solid #ebeef5;
          min-height: 32px;
          
          .button-group {
            display: flex;
            gap: 8px;
            
            .el-button {
              margin: 0;
              padding: 5px 12px;
            }
          }
          
          .display-fields {
            .el-button {
              padding: 0;
              height: auto;
              color: #606266;
              font-size: 13px;
              display: flex;
              align-items: center;
              
              i {
                margin-right: 4px;
                font-size: 14px;
              }
            }
          }
          
          .auto-refresh {
            display: flex;
            align-items: center;
            gap: 8px;
            height: 32px;
            
            .el-switch {
              margin: 0;
            }
            
            .el-input-number {
              width: 80px;
              
              .el-input__inner {
                padding-right: 30px !important;
                font-size: 12px;
              }
            }
            
            .refresh-unit {
              color: #606266;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
  
  .log-card {
    position: relative;
    flex: 1;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    min-height: 0;
    display: flex;
    flex-direction: column;
    
    .log-console {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #1e1e1e;
      color: #d4d4d4;
      font-family: 'JetBrains Mono', Consolas, Monaco, 'Courier New', monospace;
      padding: 15px;
      overflow-y: scroll;
      border-radius: 4px;
      
      .log-line {
        padding: 6px 8px;
        white-space: pre-wrap;
        word-break: break-all;
        font-size: 13px;
        border-radius: 4px;
        transition: all 0.3s ease-in-out;
        opacity: 1;
        transform: translateY(0);
        
        &.new-log {
          animation: fadeInUp 0.3s ease-out;
        }
        
        &:hover {
          background: rgba(255, 255, 255, 0.05);
        }
        
        .log-time {
          color: #6a9955;
          margin-right: 10px;
        }
        
        .log-level {
          margin-right: 10px;
          font-weight: bold;
          padding: 2px 6px;
          border-radius: 3px;
        }
        
        .log-app {
          color: #569cd6;
          margin-right: 10px;
        }
        
        .log-thread {
          color: #4ec9b0;
          margin-right: 10px;
        }
        
        .log-class {
          color: #9cdcfe;
          margin-right: 10px;
        }
        
        .log-message {
          color: #d4d4d4;
        }
        
        .trace-link {
          margin-left: 10px;
          color: #dcdcaa;
          text-decoration: underline;
          
          &:hover {
            color: #e6db74;
          }
        }
      }
      
      .log-error {
        .log-level { 
          color: #f14c4c;
          background: rgba(241, 76, 76, 0.1);
        }
      }
      
      .log-warn {
        .log-level { 
          color: #cca700;
          background: rgba(204, 167, 0, 0.1);
        }
      }
      
      .log-info {
        .log-level { 
          color: #3794ff;
          background: rgba(55, 148, 255, 0.1);
        }
      }
      
      .log-debug {
        .log-level { 
          color: #6a9955;
          background: rgba(106, 153, 85, 0.1);
        }
      }
      
      .no-more {
        text-align: center;
        padding: 15px 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

.field-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 12px;
  
  .el-checkbox {
    margin: 0;
    
    &__label {
      font-size: 13px;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<script>
import { queryLogs, getAppList } from '../../../api/log-analysis/es'
import { format, subHours, subDays, subWeeks, subMonths } from 'date-fns'

export default {
  name: 'LogAnalysis',
  data() {
    return {
      searchForm: {
        timeRange: [],
        appNames: [],
        traceId: ''
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近1小时',
          onClick(picker) {
            const end = new Date()
            const start = subHours(end, 1)
            picker.$emit('pick', [format(start, 'yyyy-MM-dd HH:mm:ss'), format(end, 'yyyy-MM-dd HH:mm:ss')])
          }
        }, {
          text: '最近6小时',
          onClick(picker) {
            const end = new Date()
            const start = subHours(end, 6)
            picker.$emit('pick', [format(start, 'yyyy-MM-dd HH:mm:ss'), format(end, 'yyyy-MM-dd HH:mm:ss')])
          }
        }, {
          text: '最近12小时',
          onClick(picker) {
            const end = new Date()
            const start = subHours(end, 12)
            picker.$emit('pick', [format(start, 'yyyy-MM-dd HH:mm:ss'), format(end, 'yyyy-MM-dd HH:mm:ss')])
          }
        }, {
          text: '最近1天',
          onClick(picker) {
            const end = new Date()
            const start = subDays(end, 1)
            picker.$emit('pick', [format(start, 'yyyy-MM-dd HH:mm:ss'), format(end, 'yyyy-MM-dd HH:mm:ss')])
          }
        }, {
          text: '最近1周',
          onClick(picker) {
            const end = new Date()
            const start = subWeeks(end, 1)
            picker.$emit('pick', [format(start, 'yyyy-MM-dd HH:mm:ss'), format(end, 'yyyy-MM-dd HH:mm:ss')])
          }
        }, {
          text: '最近1个月',
          onClick(picker) {
            const end = new Date()
            const start = subMonths(end, 1)
            picker.$emit('pick', [format(start, 'yyyy-MM-dd HH:mm:ss'), format(end, 'yyyy-MM-dd HH:mm:ss')])
          }
        }]
      },
      loading: false,
      logList: [],
      appList: [],
      pageSize: 100,
      activeTab: 'traceSearch',
      autoRefresh: false,
      refreshInterval: null,
      displayFields: ['timestamp', 'level', 'app', 'message','class'],
      noMore: false,
      scrollThreshold: 100, // 距离底部多少像素时触发加载
      isLoading: false, // 防止重复加载
      refreshSeconds: 5,
      shouldScrollToBottom: true,
      debounceTimer: null,
      rafId: null,
    }
  },
  computed: {
    sortedLogs() {
      return [...this.logList].sort((a, b) => {
        return new Date(a._source['@timestamp']) - new Date(b._source['@timestamp'])
      })
    }
  },
  created() {
    this.initData()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
    }
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }
  },
  watch: {
    'searchForm.traceId': {
      handler(newVal) {
        if (newVal) {
          // 当traceId不为空时，清空时间范围
          this.searchForm.timeRange = []
        } else {
          // 当traceId为空时，设置默认时间范围为最近1天
          const end = new Date()
          const start = subDays(end, 1)
          this.searchForm.timeRange = [
            format(start, 'yyyy-MM-dd HH:mm:ss'),
            format(end, 'yyyy-MM-dd HH:mm:ss')
          ]
        }
      }
    },
    'searchForm.appNames': {
      handler() {
        this.resetLogList()
        this.handleSearch()
      }
    },
    sortedLogs: {
      handler() {
        if (this.autoRefresh) {
          this.scrollToBottom()
        }
      },
      deep: true
    }
  },
  methods: {
    async initData() {
      // 设置默认时间范围为最近1天
      const end = new Date()
      const start = subDays(end, 1)
      this.searchForm.timeRange = [
        format(start, 'yyyy-MM-dd HH:mm:ss'),
        format(end, 'yyyy-MM-dd HH:mm:ss')
      ]
      
      // 获取应用列表
      await this.fetchAppList()
      
      // 如果URL中包含traceId参数，自动执行查询
      const urlParams = new URLSearchParams(window.location.search)
      const traceId = urlParams.get('traceId')
      if (traceId) {
        this.searchForm.traceId = traceId
        this.activeTab = 'traceSearch'
        this.handleSearch()
      }
    },
    async fetchAppList() {
      try {
        const res = await getAppList()
        this.appList = res.aggregations.apps.buckets
      } catch (error) {
        // 警告提示，使用此功能需要安装ES服务
        this.$message.warning('使用此功能需要安装ES服务')
        // this.$message.error('获取应用列表失败')
      }
    },
    async handleSearch() {
      if (this.loading) return;
      
      this.loading = true;
      
      try {
        // 如果不是实时追踪模式，先清空日志列表
        if (!this.autoRefresh) {
          this.resetLogList()
        }

        const params = {
          appNames: this.searchForm.appNames,
          traceId: this.searchForm.traceId,
          pageSize: 100,
          from: 0
        }

        if (!this.searchForm.traceId) {
          params.startTime = this.searchForm.timeRange[0] ? format(new Date(this.searchForm.timeRange[0]), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : undefined
          params.endTime = this.searchForm.timeRange[1] ? format(new Date(this.searchForm.timeRange[1]), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : undefined
        }
        
        const res = await queryLogs(params)
        const logs = res.hits.hits
        
        if (logs.length < 100) {
          this.noMore = true
        }
        
        // 只在实时追踪模式下追加新日志，否则替换全部日志
        if (this.autoRefresh && this.logList.length > 0) {
          const lastTimestamp = this.logList[this.logList.length - 1]._source['@timestamp']
          const newLogs = logs.filter(log => {
            // 检查时间戳和内容是否重复
            return !this.logList.some(existingLog => 
              existingLog._source['@timestamp'] === log._source['@timestamp'] &&
              existingLog._source.message === log._source.message
            )
          })
          
          // 标记新日志
          newLogs.forEach(log => {
            log.isNew = true
          })
          
          if (newLogs.length > 0) {
            this.logList = [...this.logList, ...newLogs]
            
            // 一段时间后移除新日志标记
            setTimeout(() => {
              newLogs.forEach(log => {
                log.isNew = false
              })
            }, 1000)
          }
        } else {
          this.logList = logs
          // 非实时追踪模式下，重置滚动位置到顶部
          if (!this.autoRefresh) {
            this.$nextTick(() => {
              const element = this.$refs.logConsole
              if (element) {
                element.scrollTop = 0
              }
            })
          }
        }
        
        if (this.searchForm.traceId) {
          const url = new URL(window.location)
          url.searchParams.set('traceId', this.searchForm.traceId)
          window.history.pushState({}, '', url)
        }
      } catch (error) {
        this.$message.error('查询日志失败')
      } finally {
        this.loading = false
      }
    },
    handleReset() {
      this.searchForm = {
        timeRange: [],
        appNames: [],
        traceId: ''
      }
      // 清除URL参数
      const url = new URL(window.location)
      url.searchParams.delete('traceId')
      window.history.pushState({}, '', url)
      
      this.handleSearch()
    },
    handleTraceSearch(traceId) {
      this.searchForm.traceId = traceId
      this.activeTab = 'traceSearch'
      this.handleSearch()
    },
    formatTime(timestamp) {
      return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss.SSS')
    },
    getLogLevelType(level) {
      const typeMap = {
        'ERROR': 'danger',
        'WARN': 'warning',
        'INFO': 'info',
        'DEBUG': 'success'
      }
      return typeMap[level] || 'info'
    },
    getLogLevelClass(level) {
      const typeMap = {
        'ERROR': 'log-error',
        'WARN': 'log-warn',
        'INFO': 'log-info',
        'DEBUG': 'log-debug'
      }
      return typeMap[level] || 'log-info'
    },
    startAutoRefresh() {
      this.stopAutoRefresh() // 清除可能存在的旧定时器
      this.refreshInterval = setInterval(() => {
        this.handleSearch()
      }, this.refreshSeconds * 1000)
    },
    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    },
    handleScroll(e) {
      // 使用 requestAnimationFrame 优化滚动性能
      if (this.rafId) {
        cancelAnimationFrame(this.rafId)
      }
      
      this.rafId = requestAnimationFrame(() => {
        const element = this.$refs.logConsole
        if (!element) return
        
        // 只在实时追踪模式下检查是否需要自动滚动
        if (this.autoRefresh) {
          const scrollBottom = element.scrollHeight - element.scrollTop - element.clientHeight
          this.shouldScrollToBottom = scrollBottom < 50
        }
        
        // 加载更多的逻辑
        if (!this.autoRefresh) { // 只在非实时追踪模式下启用加载更多
          const scrollBottom = element.scrollHeight - element.scrollTop - element.clientHeight
          if (scrollBottom < this.scrollThreshold && !this.loading && !this.noMore && !this.isLoading) {
            // 使用防抖优化加载更多的触发
            if (this.debounceTimer) {
              clearTimeout(this.debounceTimer)
            }
            this.debounceTimer = setTimeout(() => {
              this.loadMore()
            }, 200)
          }
        }
      })
    },
    async loadMore() {
      if (this.isLoading || this.noMore) return
      
      this.isLoading = true
      this.loading = true
      
      try {
        const params = {
          appNames: this.searchForm.appNames,
          traceId: this.searchForm.traceId,
          pageSize: 100,
          from: this.logList.length
        }

        // 只有在没有traceId时才添加时间参数
        if (!this.searchForm.traceId) {
          params.startTime = this.searchForm.timeRange[0] ? format(new Date(this.searchForm.timeRange[0]), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : undefined
          params.endTime = this.searchForm.timeRange[1] ? format(new Date(this.searchForm.timeRange[1]), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") : undefined
        }
        
        const res = await queryLogs(params)
        const newLogs = res.hits.hits
        
        // 如果返回的数据少于请求的数量，说明没有更多数据了
        if (newLogs.length < 100) {
          this.noMore = true
        }
        
        // 根据查询类型决定如何添加新数据
        if (this.searchForm.traceId) {
          // TraceId查询时按时间正序排列
          this.logList = [...this.logList, ...newLogs].sort((a, b) => {
            return new Date(a._source['@timestamp']) - new Date(b._source['@timestamp'])
          })
        } else {
          // 普通日志查询时保持默认的倒序排列
          this.logList = [...this.logList, ...newLogs]
        }
      } catch (error) {
        this.$message.error('加载更多日志失败')
      } finally {
        this.loading = false
        this.isLoading = false
      }
    },
    // 重置加载状态
    resetLoadStatus() {
      this.noMore = false
      this.isLoading = false
      this.logList = []
    },
    handleAutoRefreshChange(value) {
      if (value) {
        this.shouldScrollToBottom = true // 开启实时追踪时，强制开启自动滚动
        this.noMore = false // 重置加载更多状态
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
    },
    handleRefreshSecondsChange(value) {
      if (this.autoRefresh) {
        this.stopAutoRefresh()
        this.startAutoRefresh()
      }
    },
    scrollToBottom() {
      if (!this.shouldScrollToBottom) return
      
      this.$nextTick(() => {
        const element = this.$refs.logConsole
        if (element) {
          element.scrollTop = element.scrollHeight
        }
      })
    },
    resetLogList() {
      this.logList = []
      this.noMore = false
      this.isLoading = false
    },
    handleAppNamesChange(value) {
      // 当应用选择发生变化时，重置日志列表并重新查询
      this.resetLogList()
      this.handleSearch()
    },
  }
}
</script> 