<template>
  <div>
    <auto-page :options="options" :form-rule="options.formRule" :defaultQuery="{parentId:0,orders:{'orderNum':'asc'}}" ref="rules"
               @addRow="addRow" @updateRow="updateRow">
    </auto-page>
  </div>
</template>

<script>
  import options from './options'
  import AutoPage from "@/components/auto-page/AutoPage";
  import {list} from "@system/api/sys/account"
  import {treeList} from "@system/api/sys/dept"
  export default {
    components:{AutoPage},
    data(){
      return{
        options:null
      }
    },
    created(){
      this.options = options
      this.assemblyTree();
    },
    methods:{
      addRow(){
        this.$refs.rules.formRuleDrawerNes[5].value = true
      },
      async updateRow(type,row){
        this.$refs.rules.formRuleDrawerNes[3].options = await this.accountList(row.id)
      },
      async accountList(deptId){
        return new Promise(((resolve, reject) => {
          list({size:-1,valid:true,deptId:deptId}).then(res=>{
            let array=[];
            res.records.forEach((item)=>{
              array.push({value:item.id,label:item.name})
            })
            resolve(array)
          })
        }))
      },
      async getTreeList() {
        return new Promise(((resolve, reject) => {
          treeList().then(res => {
            resolve(res)
          })
        }))
      },
      async assemblyTree(){
        let array = await this.getTreeList();
        this.recursion(array)
        this.options.formRule = this.options.formRule.map(is => {
          if (is.field === "parentId") {
            is.options = array
          }
          return is
        })
      },
      recursion(array){
        for (let i = 0; i < array.length; i++) {
          array[i].label = array[i].name;
          delete array[i].name;
          if (array[i].children.length > 0){
            this.recursion(array[i].children)
          }
        }
      }
    }
  }
</script>
