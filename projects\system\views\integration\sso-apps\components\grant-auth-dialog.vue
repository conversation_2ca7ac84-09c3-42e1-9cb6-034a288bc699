<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="grant-auth-content">
      <!-- 搜索和筛选 -->
      <div class="filter-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用户或角色"
          prefix-icon="el-icon-search"
          clearable
          class="search-input"
        >
          <el-select 
            v-model="filterType" 
            slot="append" 
            style="width: 100px"
          >
            <el-option label="全部" value="all"></el-option>
            <el-option label="用户" value="user"></el-option>
            <el-option label="角色" value="role"></el-option>
          </el-select>
        </el-input>
      </div>

      <!-- 待选列表 -->
      <el-table
        ref="multipleTable"
        :data="filteredList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="名称" min-width="180">
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar 
                :size="32"
                :style="{ backgroundColor: scope.row.color }"
              >
                {{ scope.row.name.charAt(0) }}
              </el-avatar>
              <div class="info">
                <span class="name">{{ scope.row.name }}</span>
                <span class="type">{{ scope.row.type === 'user' ? '用户' : '角色' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="部门" prop="department"></el-table-column>
        <el-table-column label="描述" prop="description" show-overflow-tooltip></el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange"
        >
        </el-pagination>
      </div>
    </div>

    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button 
        type="primary" 
        :disabled="!selectedItems.length"
        @click="handleConfirm"
      >
        确定授权 ({{ selectedItems.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'GrantAuthDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '授权用户/角色'
    },
    permissionType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchKeyword: '',
      filterType: 'all',
      loading: false,
      currentPage: 1,
      pageSize: 10,
      selectedItems: [],
      // 模拟数据 - 实际应该从接口获取
      userList: [
        {
          id: 101,
          name: '陈一',
          type: 'user',
          department: '技术部',
          description: '前端开发工程师',
          color: '#1890FF'
        },
        {
          id: 102,
          name: '研发组',
          type: 'role',
          department: '技术部',
          description: '研发团队角色组',
          color: '#722ED1'
        },
        {
          id: 103,
          name: '王二',
          type: 'user',
          department: '产品部',
          description: '产品经理',
          color: '#13C2C2'
        },
        {
          id: 104,
          name: '产品组',
          type: 'role',
          department: '产品部',
          description: '产品团队角色组',
          color: '#52C41A'
        },
        {
          id: 105,
          name: '李三',
          type: 'user',
          department: '市场部',
          description: '市场专员',
          color: '#F5222D'
        }
      ]
    }
  },
  computed: {
    filteredList() {
      let result = this.userList
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(item => 
          item.name.toLowerCase().includes(keyword) ||
          item.department.toLowerCase().includes(keyword) ||
          item.description.toLowerCase().includes(keyword)
        )
      }
      
      // 类型筛选
      if (this.filterType !== 'all') {
        result = result.filter(item => item.type === this.filterType)
      }
      
      // 分页
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return result.slice(start, end)
    },
    total() {
      return this.userList.length
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
      if (!val) {
        this.reset()
      }
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.selectedItems = val
    },
    handlePageChange(page) {
      this.currentPage = page
    },
    handleConfirm() {
      this.$emit('confirm', {
        items: this.selectedItems,
        permissionType: this.permissionType
      })
      this.dialogVisible = false
    },
    reset() {
      this.searchKeyword = ''
      this.filterType = 'all'
      this.currentPage = 1
      this.selectedItems = []
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearSelection()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.grant-auth-content {
  .filter-bar {
    margin-bottom: 20px;
    
    .search-input {
      width: 360px;
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .info {
      display: flex;
      flex-direction: column;

      .name {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }

      .type {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 