<template>
    <transition enter-active-class="animate__animated animate__fadeInRight animate__faster"
        leave-active-class="animate__animated animate__fadeOut animate__faster">
        <div class="drawer-class" v-if="drawer">
            <div class="drawer-header">
                <div class="title">
                    <i :class="nodeConfig.icon"></i>
                    <span>{{ nodeConfig.label }}</span>
                </div>
                <div class="close" @click="cancel()">
                    <i class="el-icon-close"></i>
                </div>
            </div>
            <div style="padding:0 10px">
                <el-input class="describe-input" v-model="describeInput" placeholder="添加描述..."></el-input>
            </div>
            <div class="line-class"></div>
            <div class="container">
                <component ref="propertyPanel" v-if="nodeConfig.propertyPanel" :is="nodeConfig.propertyPanel"
                    :properties="properties" :node="node" />
            </div>
            <div class="footer">
                <el-button size="mini" @click="cancel()">取 消</el-button>
                <el-button size="mini" type="primary" @click="updateProperties()">确 定</el-button>
            </div>
        </div>
    </transition>
</template>
<script>
import LogicFlow from "@logicflow/core"
import 'animate.css';
import { getNodeByType } from './config.js'


export default {

    props: {
        visible: {
            type: Boolean,
            required: true
        },
        node: {
            type: Object
        },
        lf: {
            type: LogicFlow,
            required: true
        }
    },
    provide() {
        return {
            incomingEdge: () => this.getNodeIncomingEdge(),
            outgoingEdge: () => this.getNodeOutgoingEdge(),
            incomingNode: () => this.getNodeIncomingNode(),
            outgoingNode: () => this.getNodeOutgoingNode()
        }
    },
    data() {
        return {
            drawer: this.visible,
            describeInput: '',
            properties: {},
            nodeConfig: {}
        }
    },

    watch: {
        visible(n) {
            this.drawer = n
        },
        drawer(n) {
            this.$emit('update:visible', n)
        },

        node: {
            async handler(n) {
                if (n) {
                    this.properties = { ...n.properties }
                    this.nodeConfig = await getNodeByType(this.node.type)
                }
            },
            deep: true,
            immediate: true,
        },

    },

    methods: {
        getNodeIncomingEdge() {
            return this.lf.getNodeIncomingEdge(this.node.id)
        },
        getNodeOutgoingEdge() {
            return this.lf.getNodeOutgoingEdge(this.node.id)
        },
        getNodeIncomingNode() {
            return this.lf.getNodeIncomingNode(this.node.id)
        },
        getNodeOutgoingNode() {
            return this.lf.getNodeOutgoingNode(this.node.id)
        },
        cancel() {
            this.properties = { ...this.node.properties }
            this.drawer = false
        },
        async updateProperties() {
            let propertyPanel = this.$refs.propertyPanel
            if (propertyPanel) {
                if ( !propertyPanel.validate || await propertyPanel.validate()) {
                    let properties = this.$refs.propertyPanel?.getProperties()
                    properties && this.lf.setProperties(this.node.id, properties)
                } else {
                    return
                }
            }
            this.drawer = false
        }
    },

}
</script>

<style lang="scss" scoped>
.drawer-class {
    display: flex;
    flex-direction: column;
    position: absolute;
    z-index: 999;
    border-radius: 15px;
    min-width: 25%;
    height: 88%;
    background: #fff;
    bottom: 10px;
    right: 10px;
    filter: drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.2));
    font-size: 13px;
    box-sizing: border-box;

    .drawer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 16px 0 16px;
        margin-bottom: 5px;

        .title {
            display: flex;
            align-items: center;
            padding: 5px;
            font-size: 14px;

            i {
                margin-right: 5px;
                font-size: 22px;
                color: #606266;
            }

            span {
                color: black;
            }
        }

        .close {
            font-weight: bold;
            cursor: pointer;
            padding: 3px;
            font-size: 15px;
            border-radius: 5px;
        }
    }

    .line-class {
        height: 1px;
        background: #ecedee;
        margin: 13px 0;
    }

    .describe-input {
        ::v-deep .el-input__inner {
            font-size: 13px;
            border: none;
            height: 30px;
            border-radius: 8px;

            &:hover {
                filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.1));
            }

        }

    }

    .container {
        padding: 0 20px;
        flex: 1;
        overflow-y: auto;
    }

    .footer {
        padding-top: 20px;
        padding-bottom: 20px;
        text-align: center;
    }

}
</style>
<style lang="scss">
.el-form-item--small .el-form-item__label {
    line-height: 22px;
}
</style>