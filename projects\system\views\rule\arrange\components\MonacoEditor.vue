<template>
  <div
      class="monaco-editor-container"
      ref="container"
  ></div>
</template>

<script>
import * as monaco from 'monaco-editor'

// 注册LiteFlow表达式语言
monaco.languages.register({ id: 'liteflow' })
monaco.languages.setMonarchTokensProvider('liteflow', {
  keywords: [
    'THEN', 'WHEN', 'IF', 'SWITCH', 'FOR', 'WHILE', 'DO', 'TO'
  ],

  tokenizer: {
    root: [
      [/[a-zA-Z_]\w*/, {
        cases: {
          '@keywords': 'keyword',
          '@default': 'identifier'
        }
      }],
      [/[{}()\[\]]/, '@brackets'],
      [/[,.]/, 'delimiter'],
      [/\d+/, 'number'],
      [/"([^"\\]|\\.)*$/, 'string.invalid'],
      [/"/, { token: 'string.quote', bracket: '@open', next: '@string' }],
      [/\/\/.*$/, 'comment']
    ],

    string: [
      [/[^\\"]+/, 'string'],
      [/"/, { token: 'string.quote', bracket: '@close', next: '@pop' }]
    ]
  }
})

export default {
  name: 'MonacoEditor',

  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'javascript'
    },
    theme: {
      type: String,
      default: 'vs'
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      editor: null,
      defaultOptions: {
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        automaticLayout: true,
        fontSize: 14,
        lineNumbers: 'on',
        roundedSelection: false,
        scrollBeyondLastLine: false,
        readOnly: false,
        theme: this.theme,
        language: this.language
      }
    }
  },

  watch: {
    value(newValue) {
      if (this.editor && newValue !== this.editor.getValue()) {
        this.editor.setValue(newValue)
      }
    },

    language(newValue) {
      if (this.editor) {
        monaco.editor.setModelLanguage(this.editor.getModel(), newValue)
      }
    },

    theme(newValue) {
      if (this.editor) {
        monaco.editor.setTheme(newValue)
      }
    },

    options: {
      deep: true,
      handler(newValue) {
        if (this.editor) {
          this.editor.updateOptions(newValue)
        }
      }
    }
  },

  mounted() {
    this.initMonaco()
  },

  beforeDestroy() {
    this.destroyMonaco()
  },

  methods: {
    initMonaco() {
      const options = {
        ...this.defaultOptions,
        ...this.options,
        value: this.value,
        language: this.language
      }

      this.editor = monaco.editor.create(this.$refs.container, options)

      // 监听内容变化
      this.editor.onDidChangeModelContent(() => {
        const value = this.editor.getValue()
        if (value !== this.value) {
          this.$emit('input', value)
          this.$emit('change', value)
        }
      })

      // 监听光标位置变化
      this.editor.onDidChangeCursorPosition(e => {
        this.$emit('cursor-position-change', e)
      })

      // 监听编辑器获取焦点
      this.editor.onDidFocusEditorText(() => {
        this.$emit('focus')
      })

      // 监听编辑器失去焦点
      this.editor.onDidBlurEditorText(() => {
        this.$emit('blur')
      })
    },

    destroyMonaco() {
      if (this.editor) {
        this.editor.dispose()
        this.editor = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.monaco-editor-container {
  width: 100%;
  height: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style> 