import Vue from "vue";

import './empty.css'

Vue.use({
  install(Vue) {
    Vue.directive("empty", {
      inserted(el, binding) {
        let dom = document.createElement("div");
        let name = Object.keys(binding.modifiers)[0];
        if (name) {
          dom.className = `directive_empty_box_${name}`;
        } else {
          dom.className = "directive_empty_box";
        }
        el.emptyDom = dom;
        if (binding.value && true) {
          dom.isShow = true;
          el.appendChild(dom);
        }
        let elStyle = getComputedStyle(el);
        let value = elStyle.getPropertyValue("position");
        if (value != "relative") {
          el.style.setProperty("position", "relative");
        }
      },
      update(el, binding) {
        let dom = el.emptyDom;
        if (binding.value && true && !dom.isShow) {
          dom.isShow = true;
          el.appendChild(dom);
        } else {
          if (!binding.value && dom.isShow) {
            dom.isShow = false;
            el.removeChild(dom);
          }
        }
      },
      unbind(el) {
        try {
          el.emptyDom.isShow = false;
          el.removeChild(el.emptyDom);
          el.emptyDom = null;
        } catch (error) {}
      },
    });
  },
});
