import uniqueId from '@form-create/utils/lib/unique';
import { localeProps } from '@form-create/designer-zrw/src/utils';

const label = 'GList';
const name = 'glist';

export default {
    menu: 'custom',
    icon: 'icon-editor',
    label,
    name,
    event: ['change'],
    validate: ['string'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            $required: false,
            props: {},
        };
    },
    props(_, { t }) {

        return localeProps(t, 'custom.props', [
            {
                type: 'inputNumber',
                field: 'max',
                value: 3,
                title: '最大长度',
            },
            {
                type: 'switch',
                field: 'disabled',
                title: t('custom.props.disabled')
            }]);
    }
};
