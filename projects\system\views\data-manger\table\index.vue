<template>
  <div class="department-management">
    <!-- 左侧树形结构 -->
    <div class="department-tree">
      <div class="tree-header">
        <div class="header-title">
          <i class="el-icon-connection"></i>
          <span>数据源</span>
          <div class="header-actions">
            <el-dropdown @command="handleSyncCommand" trigger="click">
              <el-button type="text" size="small" :loading="syncing">
                <i class="el-icon-refresh"></i> 同步结构
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="current" :disabled="!currentDataSourceId">
                  <i class="el-icon-refresh"></i> 同步当前数据源
                </el-dropdown-item>
                <el-dropdown-item command="all">
                  <i class="el-icon-refresh"></i> 同步所有数据源
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索表名"
            prefix-icon="el-icon-search"
            clearable
            size="small"
            @clear="handleSearchClear"
          />
        </div>
      </div>
      <div class="tree-container">
        <el-tree
          ref="tableTree"
          :data="treeData"
          :props="defaultProps"
          node-key="id"
          :filter-node-method="filterNode"
          highlight-current
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <!-- 数据源节点 -->
            <template v-if="data.type === 'datasource'">
              <div class="node-content">
                <i :class="getDbTypeIcon(data.dbType)"></i>
                <span>{{ data.name }}</span>
              </div>
              <el-tag size="mini" :type="getTagType(data.dbType)">
                {{ data.dbType }}
              </el-tag>
            </template>
            <!-- 表节点 -->
            <template v-else>
              <div class="node-content">
                <i class="el-icon-tickets"></i>
                <span>{{ data.name }}</span>
                <span class="table-comment" v-if="data.comment">{{ data.comment }}</span>
              </div>
            </template>
          </span>
        </el-tree>
      </div>
    </div>

    <!-- 右侧表详情 -->
    <div class="department-detail" v-if="currentTable" v-loading="loading">
      <div class="detail-content">
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-tickets"></i>
            <span>{{ currentTable.name }}</span>
            <span class="table-comment" v-if="currentTable.comment">{{ currentTable.comment }}</span>
            <el-button 
              type="primary"
              size="small"
              class="action-btn primary-action-btn"
              @click="handleAddColumn"
              style="margin-left: auto;"
            >
              <i class="el-icon-plus icon-white"></i> 新增字段
            </el-button>
          </div>
          <div class="subtitle" style="margin-top: 10px;">
            <span class="info-item">
              <label>引擎:</label> {{ currentTable.engine }}
            </span>
            <span class="info-item">
              <label>记录数:</label> {{ currentTable.rows }}
            </span>
            <span class="info-item">
              <label>字符集:</label> {{ currentTable.charset }}
            </span>
            <span class="info-item">
              <label>类型:</label> {{ currentTable.tableType }}
            </span>
            <span class="info-item">
              <label>创建时间:</label> {{ currentTable.createTime }}
            </span>
          </div>
        </div>

        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-info"></i>
            <span>表结构信息</span>
          </div>

          <el-table
            :data="currentTable.columns"
            style="width: 100%"
            size="small"
            border
            class="custom-table"
          >
            <el-table-column prop="name" label="字段名" width="180" />
            <el-table-column prop="columnType" label="数据类型" width="120">
              <template slot-scope="scope">
                <span class="type-tag">{{ scope.row.columnType }}</span>
                <span class="length" v-if="scope.row.length">({{ scope.row.length }})</span>
              </template>
            </el-table-column>
            <el-table-column prop="nullable" label="允许空" width="80">
              <template slot-scope="scope">
                <el-tag size="mini" :type="scope.row.nullable ? 'info' : 'danger'">
                  {{ scope.row.nullable ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="metaKey" label="键" width="80">
              <template slot-scope="scope">
                <el-tag size="mini" type="primary" v-if="scope.row.metaKey === 'PRI'">主键</el-tag>
                <el-tag size="mini" type="success" v-else-if="scope.row.metaKey === 'UNI'">唯一</el-tag>
                <el-tag size="mini" type="warning" v-else-if="scope.row.metaKey === 'MUL'">索引</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="defaultValue" label="默认值" width="120">
              <template slot-scope="scope">
                <span class="default-value" v-if="scope.row.defaultValue !== null">{{ scope.row.defaultValue }}</span>
                <span class="null-value" v-else>NULL</span>
              </template>
            </el-table-column>
            <el-table-column prop="columnComment" label="注释" show-overflow-tooltip />
            <el-table-column prop="columnPosition" label="位置" width="60" align="center" />
          </el-table>
        </div>
      </div>
    </div>

    <!-- 数据源详情/新建表 -->
    <div class="department-detail" v-else-if="currentDataSourceId && !currentTable" v-loading="loading">
      <div class="detail-content">
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-connection"></i>
            <span>{{ getCurrentDataSource.name }}</span>
            <el-button 
              type="primary"
              size="small"
              class="action-btn primary-action-btn"
              @click="handleCreateTable"
              style="margin-left: auto;"
            >
              <i class="el-icon-plus icon-white"></i> 新建表
            </el-button>
          </div>
          <div class="datasource-info">
            <p>数据库类型: <span>{{ getCurrentDataSource.type }}</span></p>
            <p>表数量: <span>{{ getDatasourceTables.length }}</span></p>
          </div>
        </div>

        <!-- 表列表 -->
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-tickets"></i>
            <span>数据表</span>
            <span class="member-count" v-if="getDatasourceTables.length">
              共 {{ getDatasourceTables.length }} 个
            </span>
          </div>
          <el-table
            :data="paginatedTables"
            style="width: 100%"
            border
            stripe
            highlight-current-row
            height="calc(100% - 110px)"
            class="custom-table"
          >
            <el-table-column prop="name" label="表名" min-width="120">
              <template slot-scope="{ row }">
                <div class="table-info">
                  <i class="el-icon-tickets"></i>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="metaComment" label="表注释" min-width="150" show-overflow-tooltip />
            <el-table-column prop="engine" label="引擎" width="100" align="center" />
            <el-table-column prop="totalRows" label="记录数" width="100" align="center" />
            <el-table-column prop="charset" label="字符集" width="100" align="center" />
            <el-table-column prop="tableType" label="类型" width="80" align="center" />
            <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
            <el-table-column label="操作" width="120" align="center" class-name="operation-column">
              <template slot-scope="{ row }">
                <el-button
                  type="text"
                  size="mini"
                  class="view-btn"
                  @click="handleViewTable(row)"
                >
                  <i class="el-icon-view"></i> 查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container" v-if="getDatasourceTables.length > 0">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              layout="total, prev, pager, next"
              :total="getDatasourceTables.length"
              background
            >
            </el-pagination>
          </div>

          <div class="empty-tip" v-if="getDatasourceTables.length === 0">
            <i class="el-icon-info"></i>
            <span>暂无数据表，请使用"新建表"按钮创建</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else v-loading="loading">
      <i class="el-icon-tickets"></i>
      <p>请选择左侧数据源或表查看详细信息</p>
    </div>

    <!-- 新建表对话框 -->
    <el-dialog
      title="新建表"
      :visible.sync="createTableDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      append-to-body
      class="department-detail-dialog"
    >
      <el-form :model="createTableForm" ref="createTableForm" label-width="100px">
        <el-form-item label="DDL语句" prop="ddl" required>
          <el-input
            type="textarea"
            v-model="createTableForm.ddl"
            :rows="10"
            placeholder="请输入建表SQL语句"
          ></el-input>
          <div class="form-tip">
            <i class="el-icon-info"></i>
            <span>支持标准的MySQL建表语句</span>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button class="cancel-btn" @click="createTableDialogVisible = false">取 消</el-button>
        <el-button type="primary" class="primary-action-btn" @click="submitCreateTable" :loading="creating">
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 新增字段对话框 -->
    <el-dialog
      title="新增字段"
      :visible.sync="addColumnDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      append-to-body
      class="department-detail-dialog"
    >
      <el-form :model="columnForm" ref="columnForm" label-width="100px" :rules="columnFormRules">
        <el-form-item label="字段名称" prop="name" required>
          <el-input v-model="columnForm.name" placeholder="请输入字段名称"></el-input>
        </el-form-item>
        <el-form-item label="数据类型" prop="columnType" required>
          <el-select v-model="columnForm.columnType" placeholder="请选择数据类型" @change="handleTypeChange">
            <el-option
              v-for="type in dataTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="字段长度" prop="length" v-if="showLengthField">
          <el-input-number v-model="columnForm.length" :min="1" :max="getMaxLengthForType()"></el-input-number>
        </el-form-item>
        <el-form-item label="允许为空">
          <el-switch v-model="columnForm.nullable"></el-switch>
        </el-form-item>
        <el-form-item label="默认值" prop="defaultValue">
          <el-input v-model="columnForm.defaultValue" placeholder="请输入默认值"></el-input>
        </el-form-item>
        <el-form-item label="字段注释" prop="columnComment">
          <el-input v-model="columnForm.columnComment" placeholder="请输入字段注释"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button class="cancel-btn" @click="addColumnDialogVisible = false">取 消</el-button>
        <el-button type="primary" class="primary-action-btn" @click="submitAddColumn" :loading="addingColumn">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDataSourceList } from '@system/api/data-manger/datasource'
import { getTableList, getColumnList, syncTables, createTableFromDDL, createColumn } from  '@system/api/data-manger/table'

export default {
  name: 'TableManagement',
  data() {
    return {
      searchKeyword: '',
      loading: false,
      syncing: false,
      currentTable: null,
      dataSources: [],
      tables: [], // 存储表列表
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      currentDataSourceId: null, // 添加当前选中的数据源ID
      createTableDialogVisible: false,
      addColumnDialogVisible: false,
      creating: false,
      addingColumn: false,
      createTableForm: {
        ddl: '',
        datasourceId: null
      },
      columnForm: {
        name: '',
        columnType: '',
        length: null,
        nullable: true,
        defaultValue: '',
        columnComment: '',
        tableId: null
      },
      columnFormRules: {
        name: [
          { required: true, message: '请输入字段名称', trigger: 'blur' },
          { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '字段名只能包含字母、数字和下划线，且不能以数字开头', trigger: 'blur' }
        ],
        columnType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ]
      },
      showLengthField: true,
      dataTypes: [
        { label: 'VARCHAR', value: 'VARCHAR', needsLength: true, defaultLength: 255, maxLength: 65535 },
        { label: 'INT', value: 'INT', needsLength: true, defaultLength: 11, maxLength: 11 },
        { label: 'BIGINT', value: 'BIGINT', needsLength: true, defaultLength: 19, maxLength: 19 },
        { label: 'DECIMAL', value: 'DECIMAL', needsLength: true, defaultLength: 10, maxLength: 65 },
        { label: 'DATETIME', value: 'DATETIME', needsLength: false },
        { label: 'TEXT', value: 'TEXT', needsLength: false },
        { label: 'LONGTEXT', value: 'LONGTEXT', needsLength: false },
        { label: 'TINYINT', value: 'TINYINT', needsLength: true, defaultLength: 4, maxLength: 4 },
        { label: 'FLOAT', value: 'FLOAT', needsLength: false },
        { label: 'DOUBLE', value: 'DOUBLE', needsLength: false },
        { label: 'DATE', value: 'DATE', needsLength: false },
        { label: 'TIME', value: 'TIME', needsLength: false },
        { label: 'TIMESTAMP', value: 'TIMESTAMP', needsLength: false },
        { label: 'CHAR', value: 'CHAR', needsLength: true, defaultLength: 10, maxLength: 255 }
      ],
      currentPage: 1,
      pageSize: 10
    }
  },
  computed: {
    treeData() {
      return this.dataSources.map(ds => ({
        id: ds.id,
        type: 'datasource',
        name: ds.name,
        dbType: ds.type,
        children: this.tables
          .filter(table => table.datasourceId === ds.id)
          .map(table => ({
            id: table.id,
            type: 'table',
            name: table.name,
            comment: table.metaComment,
            engine: table.engine,
            rows: table.totalRows,
            charset: table.charset,
            tableType: table.tableType,
            createTime: table.createTime,
            datasourceId: table.datasourceId,
            databaseName: table.databaseName
          }))
      }))
    },
    // 获取当前选中的数据源信息
    getCurrentDataSource() {
      if (!this.currentDataSourceId) return {}
      const dataSource = this.dataSources.find(ds => ds.id === this.currentDataSourceId)
      return dataSource || {}
    },
    // 获取当前数据源的表
    getDatasourceTables() {
      if (!this.currentDataSourceId) return []
      return this.tables.filter(table => table.datasourceId === this.currentDataSourceId)
    },
    // 获取当前数据源的分页后的表
    paginatedTables() {
      if (!this.currentDataSourceId) return []
      const tables = this.getDatasourceTables
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return tables.slice(start, end)
    }
  },
  watch: {
    searchKeyword(val) {
      this.$refs.tableTree.filter(val)
    }
  },
  created() {
    // 从路由参数获取数据源ID
    const datasourceId = this.$route.query.id
    if (datasourceId) {
      // 如果有指定数据源ID，只加载该数据源的表
      this.initWithDataSource(datasourceId)
    } else {
      // 否则加载所有数据源的表
      this.initAllDataSources()
    }
  },
  methods: {
    async initAllDataSources() {
      this.loading = true
      try {
        // 获取所有数据源列表
        const dsRes = await getDataSourceList()
        this.dataSources = dsRes.records

        // 获取所有数据源的表
        if (this.dataSources.length > 0) {
          const promises = this.dataSources.map(ds => this.loadTables(ds.id))
          await Promise.all(promises)
        }
      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    async initWithDataSource(datasourceId) {
      this.loading = true
      try {
        // 获取指定数据源信息
        const dsRes = await getDataSourceList()
        const dataSource = dsRes.records.find(ds => ds.id === datasourceId)
        if (dataSource) {
          this.dataSources = [dataSource] // 只保存当前数据源
          this.currentDataSourceId = datasourceId
          await this.loadTables(datasourceId)
        } else {
          this.$message.error('未找到指定的数据源')
        }
      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    async loadTables(datasourceId) {
      try {
        const res = await getTableList({ datasourceId })
        // 过滤掉已存在的同数据源的表，再添加新的表
        this.tables = [
          ...this.tables.filter(table => table.datasourceId !== datasourceId),
          ...res.records
        ]
      } catch (error) {
        this.$message.error(`加载数据源(${datasourceId})的表失败：${error.message}`)
      }
    },
    async handleNodeClick(data) {
      if (data.type === 'datasource') {
        this.currentDataSourceId = data.id
        this.currentTable = null
        // 重置分页
        this.currentPage = 1
      } else if (data.type === 'table') {
        try {
          const res = await getColumnList({ tableId: data.id })
          // 从树形数据或tables中获取更完整的表信息
          const tableInfo = this.tables.find(t => t.id === data.id) || data
          
          this.currentTable = {
            ...data,
            columns: res.records,
            comment: tableInfo.metaComment,
            engine: tableInfo.engine,
            rows: tableInfo.totalRows,
            charset: tableInfo.charset,
            tableType: tableInfo.tableType,
            createTime: tableInfo.createTime,
            databaseName: tableInfo.databaseName
          }
        } catch (error) {
          this.$message.error('加载表结构失败：' + error.message)
        }
      }
    },
    async handleSyncCommand(command) {
      if (command === 'current') {
        await this.syncCurrentDataSource()
      } else if (command === 'all') {
        await this.syncAllDataSources()
      }
    },
    async syncCurrentDataSource() {
      if (!this.currentDataSourceId) {
        this.$message.warning('请先选择数据源')
        return
      }

      this.syncing = true
      try {
        await syncTables(this.currentDataSourceId)
        this.$message.success('同步成功')
        // 重新加载当前数据源的表
        this.tables = this.tables.filter(table => table.datasourceId !== this.currentDataSourceId)
        await this.loadTables(this.currentDataSourceId)
      } catch (error) {
        this.$message.error('同步失败：' + error.message)
      } finally {
        this.syncing = false
      }
    },
    async syncAllDataSources() {
      if (this.dataSources.length === 0) {
        this.$message.warning('没有可同步的数据源')
        return
      }

      this.syncing = true
      try {
        // 清空现有表数据
        this.tables = []
        
        // 依次同步每个数据源
        for (const ds of this.dataSources) {
          await syncTables(ds.id)
          await this.loadTables(ds.id)
        }
        
        this.$message.success('所有数据源同步完成')
      } catch (error) {
        this.$message.error('同步失败：' + error.message)
      } finally {
        this.syncing = false
      }
    },
    filterNode(value, data) {
      if (!value) return true
      const searchValue = value.toLowerCase()
      if (data.type === 'datasource') {
        return data.name.toLowerCase().includes(searchValue)
      } else {
        return data.name.toLowerCase().includes(searchValue) || 
               data.comment?.toLowerCase().includes(searchValue)
      }
    },
    getDbTypeIcon(type) {
      const iconMap = {
        'MYSQL': 'el-icon-coffee',
        'ORACLE': 'el-icon-orange',
        'POSTGRESQL': 'el-icon-platform-eleme'
      }
      return iconMap[type] || 'el-icon-platform-eleme'
    },
    getTagType(type) {
      const typeMap = {
        'MYSQL': 'primary',
        'ORACLE': 'warning',
        'POSTGRESQL': 'success'
      }
      return typeMap[type] || 'info'
    },
    handleSearchClear() {
      this.searchKeyword = ''
    },
    handleCreateTable() {
      this.createTableForm.datasourceId = this.currentDataSourceId
      this.createTableForm.ddl = ''
      this.createTableDialogVisible = true
    },
    async submitCreateTable() {
      if (!this.createTableForm.ddl.trim()) {
        this.$message.warning('请输入DDL语句')
        return
      }

      this.creating = true
      try {
        await createTableFromDDL(this.createTableForm)
        this.$message.success('创建成功')
        this.createTableDialogVisible = false
        // 重新加载表列表
        await this.loadTables(this.currentDataSourceId)
      } catch (error) {
        this.$message.error('创建失败：' + error.message)
      } finally {
        this.creating = false
      }
    },
    handleAddColumn() {
      this.columnForm = {
        name: '',
        columnType: 'VARCHAR',
        length: 255,
        nullable: true,
        defaultValue: '',
        columnComment: '',
        tableId: this.currentTable.id
      }
      this.handleTypeChange('VARCHAR')
      this.addColumnDialogVisible = true
    },
    async submitAddColumn() {
      // 使用表单验证
      this.$refs.columnForm.validate(async (valid) => {
        if (valid) {
          // 确保长度字段在需要时有值
          if (this.showLengthField && !this.columnForm.length) {
            const type = this.dataTypes.find(t => t.value === this.columnForm.columnType)
            if (type && type.defaultLength) {
              this.columnForm.length = type.defaultLength
            }
          }

          this.addingColumn = true
          try {
            await createColumn(this.columnForm)
            this.$message.success('添加成功')
            this.addColumnDialogVisible = false
            // 重新加载字段列表
            const res = await getColumnList({ tableId: this.currentTable.id })
            this.currentTable.columns = res.records
          } catch (error) {
            this.$message.error('添加失败：' + error.message)
          } finally {
            this.addingColumn = false
          }
        } else {
          return false
        }
      })
    },
    handleTypeChange(typeValue) {
      const type = this.dataTypes.find(t => t.value === typeValue)
      if (type) {
        this.showLengthField = type.needsLength
        if (type.needsLength && type.defaultLength) {
          this.columnForm.length = type.defaultLength
        } else {
          this.columnForm.length = null
        }
      } else {
        this.showLengthField = false
        this.columnForm.length = null
      }
    },
    getMaxLengthForType() {
      const type = this.dataTypes.find(t => t.value === this.columnForm.columnType)
      return type && type.maxLength ? type.maxLength : 65535
    },
    handleViewTable(table) {
      // 通过表ID查找对应的树节点并点击
      const node = this.$refs.tableTree.getNode(table.id)
      if (node) {
        this.$refs.tableTree.setCurrentKey(table.id)
        // 直接使用table数据进行处理，避免再次请求
        this.handleNodeClick({
          id: table.id,
          type: 'table',
          name: table.name
        })
      }
    },
    handleCurrentChange(page) {
      this.currentPage = page
    }
  }
}
</script>

<style lang="scss" scoped>
.department-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  gap: 24px;
  overflow: hidden;

  .department-tree {
    width: 280px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

    .tree-header {
      padding: 20px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .header-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        span {
          font-size: 18px;
          font-weight: 600;
          color: #1a1f36;
          letter-spacing: 0.5px;
          position: relative;
          padding-left: 12px;

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: linear-gradient(to bottom, #409EFF, #64B5F6);
            border-radius: 3px;
          }
        }

        i {
          font-size: 18px;
          color: #409EFF;
          margin-right: 6px;
        }

        .header-actions {
          margin-left: auto;
          display: flex;
          gap: 12px;
          
          .el-dropdown {
            cursor: pointer;
          }
          
          .el-button--text {
            padding: 0;
            font-size: 13px;
            color: #409EFF;
            
            i {
              margin-right: 4px;
              font-size: 14px;
              color: inherit;
              
              &.el-icon--right {
                margin-right: 0;
                margin-left: 4px;
              }
            }

            &:hover {
              color: #66b1ff;
              transform: translateY(-1px);
              
              i {
                color: #66b1ff;
              }
            }
          }
        }
      }

      .search-box {
        .el-input {
          width: 100%;
          ::v-deep .el-input__inner {
            border-radius: 10px;
            height: 36px;
            background: #f7f9fc;
            border: 1px solid #e7ebf3;
            
            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
          
          ::v-deep .el-input__prefix {
            left: 10px;
            i {
              color: #8492a6;
            }
          }
        }
      }
    }

    .tree-container {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #fff;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      ::v-deep {
        .el-tree {
          background: transparent;
        }

        .el-tree-node__content {
          height: 32px;
          border-radius: 8px;
          margin: 2px 0;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #f5f7fa;
          }

          .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;
            
            .node-content {
              display: flex;
              align-items: center;
              gap: 6px;

              i {
                font-size: 14px;
                color: #409EFF;
                flex-shrink: 0;
              }

              span {
                color: #606266;
                text-align: left;
              }
              
              .table-comment {
                color: #909399;
                font-size: 12px;
                margin-left: 8px;
                max-width: 120px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .el-tag {
              margin-left: auto;
              height: 20px;
              padding: 0 8px;
              font-size: 11px;
              line-height: 18px;
              border-radius: 10px;
              flex-shrink: 0;
            }
          }
          
          .el-tree-node.is-current > .el-tree-node__content {
            background-color: #ecf5ff !important;
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
            
            .custom-tree-node {
              .node-content {
                span {
                  color: #409EFF;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
  }

  .department-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;
    padding: 0;
    height: 100%;

    .detail-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: 100%;

      .info-section {
        background-color: #fff;
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

        &:hover {
          box-shadow: 0 6px 24px rgba(31, 45, 61, 0.1);
        }

        &:first-child {
          flex-shrink: 0;
        }

        &:last-child {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;

          i {
            margin-right: 8px;
            color: #409EFF;
          }

          .table-comment {
            color: #606266;
            font-size: 14px;
            margin-left: 10px;
          }

          .member-count {
            font-size: 13px;
            color: #909399;
            margin-left: 8px;
            font-weight: normal;
          }
        }

        .subtitle {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          color: #909399;
          font-size: 14px;
          margin-bottom: 8px;

          .info-item {
            label {
              color: #606266;
              margin-right: 8px;
              font-weight: 500;
            }
          }
        }

        .datasource-info {
          background-color: #f8f9fb;
          padding: 15px;
          border-radius: 12px;
          margin: 15px 0;
          
          p {
            margin: 8px 0;
            color: #606266;
            font-size: 14px;
            
            span {
              color: #1a1f36;
              font-weight: 500;
              margin-left: 6px;
            }
          }
        }

        .datasource-tip {
          display: flex;
          align-items: center;
          gap: 10px;
          background-color: rgba(64, 158, 255, 0.1);
          padding: 12px;
          border-radius: 8px;
          border-left: 3px solid #409EFF;
          
          i {
            color: #409EFF;
            font-size: 16px;
          }
          
          span {
            color: #606266;
            font-size: 14px;
          }
        }

        .table-info {
          display: flex;
          align-items: center;
          gap: 6px;

          i {
            color: #409EFF;
            font-size: 14px;
          }
        }
        
        .pagination-container {
          padding: 16px 0 8px;
          display: flex;
          justify-content: center;
        }
        
        .empty-tip {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 30px 0;
          color: #909399;
          
          i {
            margin-right: 8px;
            font-size: 16px;
            color: #e6a23c;
          }
        }

        ::v-deep .el-table {
          &.custom-table {
            border-radius: 12px;
            overflow: hidden;
            margin-top: 12px;
            
            &.el-table--border {
              border-radius: 12px;
              overflow: hidden;
            }

            th {
              background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
              font-weight: 600;
              color: #1a1f36;
              height: 40px;
              padding: 8px 0;
            }

            td {
              padding: 6px 0;
            }
            
            .el-table__body-wrapper::-webkit-scrollbar {
              width: 4px;
              height: 4px;
            }
            
            .el-table__body-wrapper::-webkit-scrollbar-thumb {
              background: rgba(144, 147, 153, 0.3);
              border-radius: 4px;
              
              &:hover {
                background: rgba(144, 147, 153, 0.5);
              }
            }
            
            .el-table__body-wrapper::-webkit-scrollbar-track {
              background: transparent;
            }
            
            .el-tag {
              border-radius: 12px;
              padding: 0 10px;
              height: 22px;
              line-height: 20px;
            }
            
            .type-tag {
              color: #409EFF;
              font-family: monospace;
              font-weight: 500;
              text-transform: uppercase;
            }

            .length {
              color: #909399;
              font-family: monospace;
            }

            .default-value {
              color: #67c23a;
              font-family: monospace;
            }

            .null-value {
              color: #909399;
              font-style: italic;
            }

            .operation-column {
              .cell {
                white-space: nowrap;
                display: flex;
                justify-content: center;
                gap: 4px;

                .el-button {
                  padding: 2px 4px;
                  font-size: 12px;
                  margin: 0;
                  height: 24px;
                  line-height: 1;
                  display: inline-flex;
                  align-items: center;

                  i {
                    margin-right: 4px;
                    font-size: 13px;
                  }
                  
                  &.view-btn {
                    color: #409EFF;
                    transition: all 0.3s;
                    border-radius: 4px;
                    padding: 3px 10px;
                    
                    &:hover {
                      background-color: rgba(64, 158, 255, 0.1);
                      color: #66b1ff;
                    }
                    
                    i {
                      color: #409EFF;
                      margin-right: 4px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    animation: fadeIn 0.3s ease;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      color: #dcdfe6;
    }

    p {
      font-size: 14px;
      margin: 0;
    }
  }
}

::v-deep {
  .department-detail-dialog {
    border-radius: 20px;
    overflow: hidden;

    .el-dialog__header {
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
      
      .el-dialog__headerbtn {
        top: 20px;
        right: 20px;
      }
    }

    .el-dialog__body {
      padding: 24px;
      background: #fff;
    }
    
    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #ebeef5;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);
      
      .el-button i {
        color: inherit;
      }
    }
  }

  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    
    i {
      margin-right: 8px;
      font-size: 14px;
    }

    &.is-disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}

.form-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: rgba(64, 158, 255, 0.05);
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid #409EFF;

  i {
    color: #409EFF;
  }
}

.action-btn {
  &.el-button--text {
    color: #409EFF;
    padding: 0;
    
    &:hover {
      color: #66b1ff;
      background-color: transparent;
      transform: translateY(-1px);
    }
    
    i {
      font-size: 14px;
      margin-right: 4px;
    }
  }
  
  &.primary-action-btn {
    height: 32px;
    padding: 0 16px;
    background: linear-gradient(135deg, #409EFF, #64B5F6);
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;
    color: #ffffff;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
      background: linear-gradient(135deg, #66b1ff, #7fc6ff);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    }
    
    i {
      margin-right: 6px;
      font-size: 15px;
      color: #ffffff !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      font-weight: bold;
    }
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载动画样式覆盖 */
::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  
  .el-loading-spinner {
    .circular {
      width: 40px;
      height: 40px;
    }
    
    .path {
      stroke: #409EFF;
      stroke-width: 3;
    }
    
    .el-loading-text {
      color: #409EFF;
      font-size: 14px;
      margin-top: 10px;
    }
  }
}

::v-deep .el-pagination {
  button {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }
  
  .el-pager li {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }
}

.cancel-btn {
  background: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  
  &:hover {
    background: #e9ecf2;
    border-color: #d3d6de;
    color: #333;
  }
}

.icon-white {
  color: #ffffff !important;
}
</style> 