<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="部署框架前端" type="DEPLOY_HOST_RUN_CONFIGURATION" factoryName="Deploy to Host">
    <option name="accountModel" />
    <option name="accountModels" />
    <option name="address" />
    <option name="afterCommand" value="cd ..;rm -rf ./system;mv temp/system ./;rm -rf ./temp" />
    <option name="alreadyReset" value="true" />
    <option name="autoOpen" value="false" />
    <option name="beforeCommand" value="" />
    <option name="defaultTabIdx" value="0" />
    <option name="ecsInstance">
      <EcsInstance>
        <option name="OSType" />
        <option name="instanceId" />
        <option name="instanceName" />
        <option name="netType" />
        <option name="privateIps" />
        <option name="publicIps" />
        <option name="regionId" />
        <option name="tags" />
      </EcsInstance>
    </option>
    <option name="ecsInstances" />
    <option name="hostIds">
      <list>
        <option value="1" />
      </list>
    </option>
    <option name="hostTagId" value="0" />
    <option name="location" value="/mnt/vdb/nginx/html/temp" />
    <option name="pathOrUrl" value="D:\CompanyFront\vue-cli-test\dist\system" />
    <option name="tagId" value="0" />
    <option name="terminalCommand" />
    <option name="type" value="HOST" />
    <option name="uploadType" value="FILE" />
    <method v="2">
      <option name="NpmBeforeRunTask" enabled="true">
        <package-json value="$PROJECT_DIR$/package.json" />
        <command value="run" />
        <scripts>
          <script value="build:sys" />
        </scripts>
        <node-interpreter value="project" />
        <envs />
      </option>
    </method>
  </configuration>
</component>