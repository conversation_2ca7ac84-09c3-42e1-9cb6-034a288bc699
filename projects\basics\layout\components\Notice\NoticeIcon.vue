<template>
  <div class="notification-wrapper" v-if="serviceAvailable">
    <el-popover placement="bottom" :width="rpx(380)"
      trigger="manual"
      popper-class="notification-popover"
      v-model="notificationVisible" :visible-arrow="false" :open-delay="0" :close-delay="0">
      <div class="notification-panel">
        <div class="notification-header">
          <div class="header-title">
            <i class="el-icon-bell"></i>
            <span>通知中心</span>
            <el-tag size="small" type="danger" v-if="unreadCount">{{ unreadCount }}条未读</el-tag>
          </div>
          <div class="header-actions">
            <el-button type="text" @click="handleReadAll" v-if="unreadCount">
              <i class="el-icon-check"></i>
              全部已读
            </el-button>
            <el-button type="text" @click="handleCloseNotification">
              <i class="el-icon-close"></i>
              关闭
            </el-button>
          </div>
        </div>

        <el-tabs v-model="activeTab" class="notification-tabs">
          <el-tab-pane name="group">
            <span slot="label" class="tab-label">
              系统
              <el-badge v-if="unreadGroup > 0" :value="unreadGroup" class="tab-badge" type="danger"></el-badge>
            </span>
            <div class="notification-list" v-if="groupMessages.length" ref="groupList">
              <div v-for="(msg, index) in groupMessages" :key="msg.id" class="notification-item" :class="{ unread: !msg.read }">
                <div class="user-avatar" v-if="msg.disPlayPicUrl">
                  <img :src="getFileUrl(msg.disPlayPicUrl)" alt="头像">
                </div>
                <div class="item-icon group" v-else>
                  <i class="el-icon-user-solid"></i>
                </div>
                <div class="item-content" @click="handleNavigation(msg)">
                  <div class="item-main">
                    <div class="item-header">
                      <div class="item-title">{{ msg.displayName || msg.title }}</div>
                    </div>
                    <div class="item-desc">
                      <template v-if="msg.type === 'group' && msg.groupSpeakerDisplayName">
                        <div class="item-desc rich-content" v-html="msg.groupSpeakerDisplayName + ': ' + msg.content"></div>
                      </template>
                      <template v-else>
                        <span class="rich-content" v-html="msg.content"></span>
                      </template>
                    </div>
                    <div class="item-meta">
                      <span class="time">{{ formatTime(msg.time) }}</span>
                    </div>
                  </div>
                  <div class="item-right">
                    <div class="item-actions" v-if="msg.link">
                      <el-button type="primary" size="mini" round @click.stop="handleProcess(msg)">处理</el-button>
                    </div>
                    <div class="item-status">
                      <el-tag size="mini" :type="msg.read ? 'info' : 'danger'" effect="plain">
                        {{ msg.read ? '已读' : '未读' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="pagination.group.loading" class="loading-more">
                <i class="el-icon-loading"></i> 加载更多...
              </div>
            </div>
            <div v-else class="empty-state">
              <i class="el-icon-chat-dot-square"></i>
              <p>暂无系统消息</p>
            </div>
          </el-tab-pane>
          <el-tab-pane name="system">
            <span slot="label" class="tab-label">
              公告
              <el-badge v-if="unreadSystem > 0" :value="unreadSystem" class="tab-badge" type="danger"></el-badge>
            </span>
            <div class="notification-list" v-if="systemMessages.length" ref="systemList">
              <div v-for="(msg, index) in systemMessages" :key="msg.id" class="notification-item" :class="{ unread: !msg.read }">
                <div class="user-avatar" v-if="msg.disPlayPicUrl">
                  <img :src="getFileUrl(msg.disPlayPicUrl)" alt="头像">
                </div>
                <div class="item-icon system" v-else>
                  <i class="el-icon-bell"></i>
                </div>
                <div class="item-content" @click="handleNavigation(msg)">
                  <div class="item-main">
                    <div class="item-header">
                      <div class="item-title">{{ msg.displayName || msg.title }}</div>
                    </div>
                    <div class="item-desc rich-content" v-html="msg.content"></div>
                    <div class="item-meta">
                      <span class="time">{{ formatTime(msg.time) }}</span>
                    </div>
                  </div>
                  <div class="item-right">
                    <div class="item-actions" v-if="msg.link">
                      <el-button type="primary" size="mini" round @click.stop="handleProcess(msg)">处理</el-button>
                    </div>
                    <div class="item-status">
                      <el-tag size="mini" :type="msg.read ? 'info' : 'danger'" effect="plain">
                        {{ msg.read ? '已读' : '未读' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="pagination.system.loading" class="loading-more">
                <i class="el-icon-loading"></i> 加载更多...
              </div>
            </div>
            <div v-else class="empty-state">
              <i class="el-icon-chat-dot-square"></i>
              <p>暂无公告消息</p>
            </div>
          </el-tab-pane>
          <el-tab-pane name="admin">
            <span slot="label" class="tab-label">
              私信
              <el-badge v-if="unreadAdmin > 0" :value="unreadAdmin" class="tab-badge" type="danger"></el-badge>
            </span>
            <div class="notification-list" v-if="adminMessages.length" ref="adminList">
              <div v-for="(msg, index) in adminMessages" :key="msg.id" class="notification-item" :class="{ unread: !msg.read }">
                <div class="user-avatar" v-if="msg.disPlayPicUrl">
                  <img :src="getFileUrl(msg.disPlayPicUrl)" alt="头像">
                </div>
                <div class="item-icon admin" v-else>
                  <i class="el-icon-user"></i>
                </div>
                <div class="item-content" @click="handleNavigation(msg)">
                  <div class="item-main">
                    <div class="item-header">
                      <div class="item-title">{{ msg.displayName || msg.title }}</div>
                    </div>
                    <div class="item-desc rich-content" v-html="msg.content"></div>
                    <div class="item-meta">
                      <span class="time">{{ formatTime(msg.time) }}</span>
                    </div>
                  </div>
                  <div class="item-right">
                    <div class="item-actions" v-if="msg.link">
                      <el-button type="primary" size="mini" round @click.stop="handleProcess(msg)">处理</el-button>
                    </div>
                    <div class="item-status">
                      <el-tag size="mini" :type="msg.read ? 'info' : 'danger'" effect="plain">
                        {{ msg.read ? '已读' : '未读' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="pagination.admin.loading" class="loading-more">
                <i class="el-icon-loading"></i> 加载更多...
              </div>
            </div>
            <div v-else class="empty-state">
              <i class="el-icon-chat-dot-square"></i>
              <p>暂无私信通知</p>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="notification-footer">
          <el-button type="text" class="view-all" @click="handleViewAllMessages">
            查看全部消息
            <i class="el-icon-arrow-right"></i>
          </el-button>
          <div class="tip-text">
            <i class="el-icon-info-circle"></i>
            温馨提示: 已读所有消息后，再点击铃铛即可收起
          </div>
        </div>
      </div>

      <div slot="reference" class="notification-icon" :class="{ 'has-unread': unreadCount, 'shake': unreadCount }" @click="toggleNotification">
        <i class="el-icon-bell"></i>
        <div v-if="unreadCount" class="unread-dot"></div>
      </div>
    </el-popover>

    
  </div>
</template>

<script>
import chatSocketService, { formatTime } from '@system/api/notice/chatSocket.js'
import { 
  getCurrentSubscriptions, 
  getHistoryMessages, 
  readMessage, 
  getUnreadMessageCount,
  batchReadMessages,
  readAllMessages,
  getSystemConfig
} from '@system/api/notice/chat.js'

export default {
  name: 'NoticeIcon',
  data() {
    return {
      notificationVisible: false,
      activeTab: 'group',
      systemMessages: [],
      adminMessages: [],
      groupMessages: [],
      loading: false,
      serviceAvailable: true,
      notifications: [],
      currentSubscriptions: [],
      defaultDisplayCount: 3,
      touchStartY: 0,
      scrollSpeed: 0,
      isScrolling: false,
      // 分页数据
      pagination: {
        system: { current: 1, size: 10, hasMore: true, loading: false },
        admin: { current: 1, size: 10, hasMore: true, loading: false },
        group: { current: 1, size: 10, hasMore: true, loading: false }
      },
      // 未读消息计数
      unreadCounts: {
        broadcast: 0,
        private: 0,
        group: 0
      },
      // 消息详情弹窗
      messageDetailVisible: false,
      currentMessage: null,
      // 滚动观察器
      scrollObservers: {
        system: null,
        admin: null,
        group: null
      },
      // 强制提示配置
      mandatoryReminder: false
    }
  },
  mounted() {
    this.initWebSocket()
    this.setupScrollObservers()
    this.getMandatoryReminderConfig()
  },
  beforeDestroy() {
    // 清理所有观察器
    Object.values(this.scrollObservers).forEach(observer => {
      if (observer) {
        observer.disconnect()
      }
    })
    chatSocketService.unsubscribeAll()
  },
  computed: {
    unreadCount() {
      return this.unreadCounts.broadcast + this.unreadCounts.private + this.unreadCounts.group
    },
    unreadSystem() {
      return this.unreadCounts.broadcast
    },
    unreadAdmin() {
      return this.unreadCounts.private
    },
    unreadGroup() {
      return this.unreadCounts.group
    },
    displayedSystemMessages() {
      return this.systemMessages
    },
    displayedAdminMessages() {
      return this.adminMessages
    },
    displayedGroupMessages() {
      return this.groupMessages
    },
    fileBaseUrl() {
      return process.env.VUE_APP_FILE_URL || ''
    }
  },
  watch: {
    unreadCount(newVal) {
      if (newVal > 0) {
        this.notificationVisible = true
      } else if (newVal === 0) {
        this.notificationVisible = false
      }
    }
  },
  methods: {
    // 获取强制提示配置
    async getMandatoryReminderConfig() {
      try {
        const response = await getSystemConfig('builtin.notice.im.mandatoryReminder')
        if (response !== undefined) {
          this.mandatoryReminder = response
        }
        // console.log('获取通知强制提示配置成功', this.mandatoryReminder)
      } catch (error) {
        console.warn('获取通知强制提示配置失败，使用默认配置', error)
      }
    },

    toggleNotification() {
      if (this.notificationVisible) {
        if (this.mandatoryReminder && this.unreadCount > 0) {
          this.$message({
            type: 'warning',
            message: '请先阅读所有未读消息'
          })
        } else {
          this.notificationVisible = false
        }
      } else {
        this.notificationVisible = true
      }
    },

    async initWebSocket() {
      try {
        // 先获取订阅信息
        const response = await getCurrentSubscriptions()
        const subscriptions = response || {}
        
        // 检查是否有任何可用的订阅
        if (!subscriptions.broadcastTopics?.length && 
            !subscriptions.groupTopics?.length && 
            !subscriptions.privateQueue) {
          console.log('没有可用的订阅信息，不启用通知服务')
          this.serviceAvailable = false
          return
        }

        // 先建立一个连接
        try {
          await chatSocketService.connect()
          console.log('WebSocket连接已建立')
        } catch (error) {
          console.error('WebSocket连接失败，不启用通知服务:', error.message)
          this.serviceAvailable = false
          return
        }
        
        // 订阅收集所有需要订阅的主题
        const allTopics = [
          ...(subscriptions.broadcastTopics || []),
          ...(subscriptions.groupTopics || [])
        ]
        
        if (subscriptions.privateQueue) {
          allTopics.push(subscriptions.privateQueue)
        }
        
        // 批量订阅所有主题
        console.log(`开始订阅 ${allTopics.length} 个主题...`)
        let subscribeSuccess = true;
        for (const topic of allTopics) {
          try {
            await chatSocketService.subscribe(topic, this.handleMessage)
          } catch (error) {
            console.error(`订阅主题 ${topic} 失败:`, error)
            subscribeSuccess = false;
            break;
          }
        }
        
        // 如果任何一个订阅失败，则将服务设置为不可用
        if (!subscribeSuccess) {
          console.log('有订阅失败，不启用通知服务')
          this.serviceAvailable = false;
          chatSocketService.unsubscribeAll();
          return;
        }
        
        this.serviceAvailable = true;
        
        // 订阅完成后获取历史消息
        console.log('订阅完成，获取历史消息')
        this.fetchUnreadCounts()
        this.fetchHistoryMessages()
      } catch (error) {
        console.log('获取订阅信息失败-> 不启用通知服务 错误信息: ', error)
        this.serviceAvailable = false
      }
    },

    handleMessage(message) {
      if (!message) return
      
      const notification = chatSocketService.handleNewMessage(message)
      console.log('notification', notification)
      
      // 处理撤回消息
      if (message.recall) {
        // 根据消息类型找到对应的消息列表
        let targetList
        switch (message.chatType) {
          case 'BROADCAST':
            targetList = this.systemMessages
            break
          case 'PRIVATE':
            targetList = this.adminMessages
            break
          case 'GROUP':
            targetList = this.groupMessages
            break
        }
        
        // 找到并处理被撤回的消息
        const index = targetList.findIndex(msg => msg.id === message.id)
        if (index !== -1) {
          const targetMessage = targetList[index]
          // 如果消息未读，更新未读计数
          if (!targetMessage.read) {
            switch (message.chatType) {
              case 'BROADCAST':
                this.unreadCounts.broadcast = Math.max(0, this.unreadCounts.broadcast - 1)
                break
              case 'PRIVATE':
                this.unreadCounts.private = Math.max(0, this.unreadCounts.private - 1)
                break
              case 'GROUP':
                this.unreadCounts.group = Math.max(0, this.unreadCounts.group - 1)
                break
            }
          }
          // 移除被撤回的消息
          targetList.splice(index, 1)
        }
        return
      }
      
      // 处理正常消息
      switch (notification.type) {
        case 'system':
          this.systemMessages.unshift(notification)
          this.activeTab = 'system'
          // 更新广播消息未读计数
          this.unreadCounts.broadcast++
          break
        case 'admin':
          this.adminMessages.unshift(notification)
          this.activeTab = 'admin'
          // 更新私信未读计数
          this.unreadCounts.private++
          break
        case 'group':
          this.groupMessages.unshift(notification)
          this.activeTab = 'group'
          // 更新系统未读计数
          this.unreadCounts.group++
          break
      }
      
      // 显示通知
      this.$notify({
        title: '新消息提醒',
        message: '你收到了一条消息',
        position: 'top-right',
        duration: 4500,
        type: 'success',
        customClass: 'notice-icon-notification',
        onClick: () => {
          this.notificationVisible = true
          this.focusNewMessage(notification)
        }
      })
      
      // 如果通知面板已打开，自动滚动到新消息
      if (this.notificationVisible) {
        this.$nextTick(() => {
          this.focusNewMessage(notification)
        })
      }
    },

    focusNewMessage(message) {
      if (!message) return
      
      // 确保打开对应的标签页
      this.activeTab = message.type
      
      // 等待DOM更新
      this.$nextTick(() => {
        // 获取对应的消息列表DOM
        const listRef = this.getCurrentList()
        if (!listRef) return
        
        // 由于新消息是unshift到数组前面，所以第一个元素就是新消息
        const firstMessageElement = listRef.querySelector('.notification-item')
        if (firstMessageElement) {
          // 滚动到该元素，添加平滑滚动
          listRef.scrollTo({
            top: 0,
            behavior: 'smooth'
          })
          
          // 添加高亮效果
          firstMessageElement.classList.add('highlight-new-message')
          
          // 3秒后移除高亮效果
          setTimeout(() => {
            firstMessageElement.classList.remove('highlight-new-message')
          }, 3000)
        }
      })
    },

    handleReadMessage(message) {
      if (!message.read) {
        // 调用接口更新消息已读状态
        if (message.id) {
          readMessage(message.id).then(() => {
            message.read = true
            // 根据消息类型减少未读计数
            switch (message.type) {
              case 'system':
                this.unreadCounts.broadcast = Math.max(0, this.unreadCounts.broadcast - 1)
                break
              case 'admin':
                this.unreadCounts.private = Math.max(0, this.unreadCounts.private - 1)
                break
              case 'group':
                this.unreadCounts.group = Math.max(0, this.unreadCounts.group - 1)
                break
            }
          }).catch(error => {
            console.error('设置消息已读失败:', error)
          })
        } else {
          message.read = true
        }
      }
    },

    handleReadAll() {
      // 根据当前激活标签页决定消息类型
      let chatType
      switch (this.activeTab) {
        case 'system':
          chatType = 'BROADCAST'
          break
        case 'admin':
          chatType = 'PRIVATE'
          break
        case 'group':
          chatType = 'GROUP'
          break
        default:
          chatType = null
      }
      
      if (chatType) {
        // 使用一键标记为已读API
        readAllMessages(chatType).then(() => {
          // 更新UI中的消息状态
          if (chatType === 'BROADCAST') {
            this.systemMessages.forEach(msg => { msg.read = true })
          } else if (chatType === 'PRIVATE') {
            this.adminMessages.forEach(msg => { msg.read = true })
          } else if (chatType === 'GROUP') {
            this.groupMessages.forEach(msg => { msg.read = true })
          }
          
          // 更新未读计数
          this.fetchUnreadCounts()
          
          this.$message({
            type: 'success',
            message: '已将所有消息标记为已读'
          })
        }).catch(error => {
          console.error('设置所有消息已读失败:', error)
        })
      } else {
        // 如果没有选择特定标签页，则获取所有未读消息ID
        const allMessageIds = [
          ...this.systemMessages.filter(msg => !msg.read).map(msg => msg.id),
          ...this.adminMessages.filter(msg => !msg.read).map(msg => msg.id),
          ...this.groupMessages.filter(msg => !msg.read).map(msg => msg.id)
        ].filter(id => id) // 过滤掉可能的undefined或null
        
        if (allMessageIds.length > 0) {
          // 使用批量标记已读API
          batchReadMessages(allMessageIds).then(() => {
            // 更新UI中的消息状态
            this.systemMessages.forEach(msg => { if (allMessageIds.includes(msg.id)) msg.read = true })
            this.adminMessages.forEach(msg => { if (allMessageIds.includes(msg.id)) msg.read = true })
            this.groupMessages.forEach(msg => { if (allMessageIds.includes(msg.id)) msg.read = true })
            
            // 更新未读计数
            this.fetchUnreadCounts()
            
            this.$message({
              type: 'success',
              message: '已将所有消息标记为已读'
            })
          }).catch(error => {
            console.error('批量设置消息已读失败:', error)
          })
        }
      }
    },

    formatTime,

    handleWheel(e) {
      e.preventDefault()
      const list = this.getCurrentList()
      if (!list) return
      
      const delta = e.deltaY
      list.scrollTop += delta
      
      // 检查是否滚动到底部，如果是则加载更多数据
      this.checkScrollBottom(list)
    },
    
    handleTouchStart(e) {
      this.touchStartY = e.touches[0].clientY
    },
    
    handleTouchMove(e) {
      const list = this.getCurrentList()
      if (!list) return
      
      const currentY = e.touches[0].clientY
      const deltaY = this.touchStartY - currentY
      this.touchStartY = currentY
      
      list.scrollTop += deltaY
      
      // 检查是否滚动到底部，如果是则加载更多数据
      this.checkScrollBottom(list)
    },
    
    checkScrollBottom(list) {
      // 判断是否滚动到底部（距离底部30px以内）
      if (list.scrollHeight - list.scrollTop - list.clientHeight < 30) {
        this.loadMoreMessages()
      }
    },
    
    // 加载更多消息
    loadMoreMessages() {
      const tabType = this.activeTab
      const pagination = this.pagination[tabType]
      
      // 如果没有更多数据或者正在加载中，则返回
      if (!pagination.hasMore || pagination.loading) return
      
      pagination.loading = true
      
      // 根据当前标签页决定消息类型
      let chatType
      switch (tabType) {
        case 'system':
          chatType = 'BROADCAST'
          break
        case 'admin':
          chatType = 'PRIVATE'
          break
        case 'group':
          chatType = 'GROUP'
          break
        default:
          return
      }
      
      // 下一页
      const nextPage = pagination.current + 1
      
      // 加载更多数据
      getHistoryMessages(chatType, nextPage, pagination.size)
        .then(response => {
          if (response && response.records && response.records.length > 0) {
            // 格式化消息数据
            const messages = response.records.map(msg => {
              const defaultTitle = tabType === 'system' ? '公告' : (tabType === 'admin' ? '管理员' : '系统')
              return {
                id: msg.id,
                title: msg.title || chatSocketService.getMessageTitle(msg),
                content: msg.content || '',
                time: msg.sendTime || new Date().toISOString(),
                read: msg.read || false,
                type: tabType === 'system' ? 'system' : (tabType === 'admin' ? 'admin' : 'group'),
                sender: msg.fromUsername || defaultTitle,
                link: msg.link || '',
                displayName: msg.displayName || msg.fromUsername || defaultTitle,
                disPlayPicUrl: msg.disPlayPicUrl || '',
                groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
              }
            })
            
            // 添加到现有消息列表
            if (tabType === 'system') {
              this.systemMessages = [...this.systemMessages, ...messages]
            } else if (tabType === 'admin') {
              this.adminMessages = [...this.adminMessages, ...messages]
            } else if (tabType === 'group') {
              this.groupMessages = [...this.groupMessages, ...messages]
            }
            
            // 更新分页信息
            pagination.current = nextPage
            pagination.hasMore = response.records.length >= pagination.size // 如果返回的数据量小于页大小，说明没有更多数据了
          } else {
            pagination.hasMore = false
          }
          pagination.loading = false
        })
        .catch(error => {
          console.error(`加载更多${tabType}消息失败:`, error)
          pagination.loading = false
        })
    },
    
    // 重置分页数据
    resetPagination() {
      this.pagination = {
        system: { current: 1, size: 10, hasMore: true, loading: false },
        admin: { current: 1, size: 10, hasMore: true, loading: false },
        group: { current: 1, size: 10, hasMore: true, loading: false }
      }
    },
    
    // 获取历史消息
    async fetchHistoryMessages() {
      this.resetPagination()
      try {
        // 获取公告广播消息
        const systemResponse = await getHistoryMessages('BROADCAST', 1, this.pagination.system.size)
        if (systemResponse && systemResponse.records) {
          this.systemMessages = systemResponse.records.map(msg => ({
            id: msg.id,
            title: msg.title || chatSocketService.getMessageTitle(msg),
            content: msg.content || '',
            time: msg.sendTime || new Date().toISOString(),
            read: msg.read || false,
            type: 'system',
            sender: msg.fromUsername || '公告',
            link: msg.link || '',
            displayName: msg.displayName || msg.fromUsername || '公告',
            disPlayPicUrl: msg.disPlayPicUrl || '',
            groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
          }))
          this.pagination.system.hasMore = systemResponse.records.length >= this.pagination.system.size
        }
        
        // 获取私信消息
        const privateResponse = await getHistoryMessages('PRIVATE', 1, this.pagination.admin.size)
        if (privateResponse && privateResponse.records) {
          this.adminMessages = privateResponse.records.map(msg => ({
            id: msg.id,
            title: msg.title || chatSocketService.getMessageTitle(msg),
            content: msg.content || '',
            time: msg.sendTime || new Date().toISOString(),
            read: msg.read || false,
            type: 'admin',
            sender: msg.fromUsername || '管理员',
            link: msg.link || '',
            displayName: msg.displayName || msg.fromUsername || '管理员',
            disPlayPicUrl: msg.disPlayPicUrl || '',
            groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
          }))
          this.pagination.admin.hasMore = privateResponse.records.length >= this.pagination.admin.size
        }
        
        // 获取系统消息
        const groupResponse = await getHistoryMessages('GROUP', 1, this.pagination.group.size)
        if (groupResponse && groupResponse.records) {
          this.groupMessages = groupResponse.records.map(msg => ({
            id: msg.id,
            title: msg.title || chatSocketService.getMessageTitle(msg),
            content: msg.content || '',
            time: msg.sendTime || new Date().toISOString(),
            read: msg.read || false,
            type: 'group',
            sender: msg.fromUsername || '系统',
            link: msg.link || '',
            displayName: msg.displayName || msg.fromUsername || '系统',
            disPlayPicUrl: msg.disPlayPicUrl || '',
            groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
          }))
          this.pagination.group.hasMore = groupResponse.records.length >= this.pagination.group.size
        }
        
        // 获取未读计数
        await this.fetchUnreadCounts()
      } catch (error) {
        console.error('获取历史消息失败:', error)
      }
    },

    getCurrentList() {
      switch (this.activeTab) {
        case 'system':
          return this.$refs.systemList
        case 'admin':
          return this.$refs.adminList
        case 'group':
          return this.$refs.groupList
        default:
          return null
      }
    },

    // 获取未读消息数量
    async fetchUnreadCounts() {
      try {
        const response = await getUnreadMessageCount()
        if (response) {
          this.unreadCounts = {
            broadcast: response.broadcastUnReadCount || 0,
            private: response.privateUnReadCount || 0,
            group: response.groupUnReadCount || 0
          }
          this.updateActiveTabByUnreadCount()
        }
      } catch (error) {
        console.error('获取未读消息数量失败:', error)
      }
    },

    // 根据未读消息数量更新activeTab
    updateActiveTabByUnreadCount() {
      // 优先选择有未读消息的标签页
      if (this.unreadCounts.group > 0) {
        this.activeTab = 'group'
      } else if (this.unreadCounts.broadcast > 0) {
        this.activeTab = 'system'
      } else if (this.unreadCounts.private > 0) {
        this.activeTab = 'admin'
      }
      // 如果都没有未读消息，保持默认的group
    },

    handleNavigation(message) {
      // 标记消息为已读
      if (!message.read) {
        this.handleReadMessage(message);
      }
      
      // 跳转到消息中心并传递当前消息信息
      this.$router.push({
        path: '/notice/center',
        query: {
          messageId: message.id,
          messageType: message.type
        }
      });
    },

    handleProcess(message) {
      // 确保消息已读
      if (!message.read) {
        this.handleReadMessage(message);
      }
      
      // 如果有处理链接，则导航到该链接
      if (message.link) {
        // 检查是否为协议开头的链接 (http://, https://, ftp://, mailto:等)
        if (/^[a-zA-Z]+:\/\//.test(message.link) || message.link.startsWith('mailto:') || message.link.startsWith('tel:')) {
          // 协议开头，执行硬跳转
          window.open(message.link, '_blank')
        } else {
          // 非协议开头，执行路由跳转
          this.$router.push(message.link)
        }
      } else {
        // 如果没有链接，可以显示一个提示或执行其他操作
        this.$message({
          type: 'info',
          message: '此消息无需处理或缺少处理链接'
        });
      }
    },

    getFileUrl(disPlayPicUrl) {
      if (!disPlayPicUrl) return ''
      return this.fileBaseUrl + disPlayPicUrl
    },

    setupScrollObservers() {
      this.$nextTick(() => {
        // 为每个列表设置观察器
        ['system', 'admin', 'group'].forEach(type => {
          const listEl = this.$refs[`${type}List`]
          if (listEl) {
            // 创建观察器
            const observer = new IntersectionObserver(
              (entries) => {
                const target = entries[0]
                if (target.isIntersecting && !this.pagination[type].loading) {
                  this.loadMoreMessages()
                }
              },
              {
                root: listEl,
                threshold: 0.1
              }
            )

            // 创建并观察加载更多的触发元素
            const loadingEl = document.createElement('div')
            loadingEl.style.height = '10px'
            listEl.appendChild(loadingEl)
            observer.observe(loadingEl)

            // 保存观察器引用
            this.scrollObservers[type] = observer
          }
        })
      })
    },

    handleCloseNotification() {
      this.notificationVisible = false;
    },

    handleViewAllMessages() {
      // 跳转到消息中心并传递当前标签页信息
      this.$router.push({
        path: '/notice/center',
        query: {
          activeTab: this.activeTab
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.notification-wrapper {
  height: 40px;
  display: flex;
  align-items: center;

  .notification-badge {
    height: 100%;
    display: flex;
    align-items: center;

    ::v-deep .el-badge__content {
      top: 8px;
      right: 8px;
    }
  }

  .notification-icon {
    position: relative;
    height: 40px;
    width: 40px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    cursor: pointer;
    transition: all 0.3s;

    i {
      font-size: 20px;
      color: #606266;
      transition: all 0.3s;
      line-height: 1;
    }

    &:hover {
      background-color: #ecf5ff;
      border-color: #d9ecff;

      i {
        color: #409EFF;
      }
    }

    &.has-unread {
      animation: shake 1s cubic-bezier(.36, .07, .19, .97) infinite;
    }

    &.shake {
      animation: shake 0.5s cubic-bezier(.36, .07, .19, .97) infinite;
    }

    .unread-dot {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 8px;
      height: 8px;
      background-color: #f56c6c;
      border-radius: 50%;
      border: 2px solid #fff;
    }
  }
}

@keyframes shake {
  0%, 100% {
    transform: rotate(0);
  }
  25% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(5deg);
  }
}

.tab-label {
  position: relative;
  display: inline-block;
}

.loading-more {
  text-align: center;
  padding: 10px 0;
  color: #909399;
  font-size: 13px;
  
  i {
    margin-right: 5px;
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.speaker-name {
  font-weight: 500;
  color: #303133;
}

.highlight-new-message {
  animation: highlight-pulse 3s cubic-bezier(0.22, 0.61, 0.36, 1);
}

@keyframes highlight-pulse {
  0% {
    background-color: rgba(64, 158, 255, 0.2);
  }
  50% {
    background-color: rgba(64, 158, 255, 0.1);
  }
  100% {
    background-color: transparent;
  }
}
</style>

<style lang="scss">
.notification-popover {
  padding: 0 !important;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  border: none !important;

  .notification-panel {
    .notification-header {
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #303133;

        i {
          font-size: 18px;
          color: #409EFF;
        }

        .el-tag {
          margin-left: 8px;
        }
      }
    }

    .notification-tabs {
      .el-tabs__header {
        margin: 0;
        padding: 0 20px;
        background-color: #fff;
        border-bottom: 1px solid #ebeef5;
      }

      .el-tabs__nav-wrap::after {
        height: 1px;
      }

      .el-tabs__item {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
      }

      .el-tabs__content {
        padding: 0;
      }
    }

    .notification-list {
      max-height: 400px;
      overflow-y: auto;
      padding: 8px 0;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      will-change: transform;
      transform: translateZ(0);
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(144, 147, 153, 0.5);
        }
      }
      
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }

      .notification-item {
        transform: translateZ(0);
        backface-visibility: hidden;
        padding: 14px 20px;
        display: flex;
        align-items: flex-start;
        gap: 14px;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
        border-bottom: 1px solid #f0f2f5;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f5f8ff;
        }

        &.unread {
          background-color: #f0f7ff;

          &:hover {
            background-color: #e6f1ff;
          }

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: #409EFF;
            border-radius: 0 2px 2px 0;
          }
        }

        .item-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);

          i {
            font-size: 18px;
            color: #fff;
          }

          &.system {
            background: linear-gradient(135deg, #409EFF, #007AFF);
          }

          &.admin {
            background: linear-gradient(135deg, #67C23A, #4CAF50);
          }

          &.group {
            background: linear-gradient(135deg, #E6A23C, #FF9800);
          }
        }

        .item-content {
          flex: 1;
          min-width: 0;
          cursor: pointer;
          display: flex;
          
          .item-main {
            flex: 1;
            min-width: 0;
          }
          
          .item-right {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            margin-left: 16px;
            width: 60px;
          }

          .item-header {
            margin-bottom: 6px;
          }

          .item-title {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            line-height: 1.4;
          }
          
          .item-actions {
            margin-bottom: 4px;
            text-align: center;
            width: 100%;
            
            .el-button {
              padding: 6px 12px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              font-weight: 500;
              transition: all 0.3s;
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
              }
            }
          }
          
          .item-status {
            display: flex;
            justify-content: center;
            width: 100%;
            
            .el-tag {
              text-align: center;
              padding: 0 8px;
              height: 22px;
              line-height: 20px;
              font-weight: 500;
              border-radius: 11px;
            }
          }

          .item-desc {
            font-size: 13px;
            color: #606266;
            margin-bottom: 10px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            
            &.rich-content {
              white-space: pre-line;  // 保留换行但合并空格
            }
          }

          .item-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
            color: #909399;

            .sender {
              color: #606266;
              font-weight: 500;
            }

            .time {
              color: #909399;
            }
          }
        }
      }
    }

    .empty-state {
      padding: 40px 0;
      text-align: center;
      color: #909399;

      i {
        font-size: 32px;
        margin-bottom: 8px;
        color: #dcdfe6;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    .notification-footer {
      padding: 12px 20px;
      border-top: 1px solid #ebeef5;
      text-align: center;

      .view-all {
        width: 100%;
        color: #606266;
        font-size: 13px;

        &:hover {
          color: #409EFF;
        }

        i {
          margin-left: 4px;
          font-size: 12px;
        }
      }

      .tip-text {
        margin-top: 10px;
        padding: 8px 12px;
        font-size: 12px;
        color: #67C23A;
        background-color: #f0f9eb;
        border: 1px solid #e1f3d8;
        border-radius: 4px;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 6px;
          font-size: 14px;
          color: #67C23A;
        }
      }
    }
  }
}

/* 消息详情弹窗样式 */
.message-detail-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  
  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
    
    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      
      .el-dialog__close {
        font-size: 18px;
        color: #909399;
        font-weight: bold;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    background: #f8f9fb;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    
    .el-button {
      padding: 9px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      & + .el-button {
        margin-left: 12px;
      }

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);
        
        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .message-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1f36;
    margin-bottom: 16px;
    padding-left: 12px;
    border-left: 3px solid #409EFF;
    letter-spacing: 0.5px;
  }

  .message-content-container {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px;
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    position: relative;
    margin-bottom: 20px;
    border: 1px solid #eef1f7;

    &:hover {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    }

    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .message-content {
    &.rich-content {
      white-space: pre-wrap;  // 保留所有空白字符
    }
  }

  .message-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #909399;
    font-size: 13px;
    
    .time {
      background-color: #f0f2f5;
      padding: 4px 10px;
      border-radius: 12px;
      display: inline-block;
    }
  }
}

/* 添加全局富文本样式 */
.rich-content {
  line-height: 1.8;
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;

  ::v-deep * {
    max-width: 100%;
  }
  
  ::v-deep img {
    max-width: 100%;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  ::v-deep a {
    color: #409EFF;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  ::v-deep p {
    margin: 8px 0;
  }
  
  ::v-deep ul, ::v-deep ol {
    padding-left: 20px;
  }
}

/* 只覆盖带有特定类名的通知组件样式 */
.notice-icon-notification {
  z-index: 99999999 !important;
}
</style>