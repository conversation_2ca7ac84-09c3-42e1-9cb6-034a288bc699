<template>
  <div class="integration-app">
    <!-- 顶部搜索和筛选栏 -->
    <div class="header-section">
      <div class="search-filter-bar">
        <div class="custom-search-input">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索应用名称、编码或描述..."
            class="search-input-field"
            @keyup.enter="handleSearch"
          />
          <button
            v-if="searchKeyword"
            class="clear-button"
            @click="clearSearch"
            title="清除"
          >
            <i class="el-icon-close"></i>
          </button>
          <button
            class="search-button"
            @click="handleSearch"
            title="搜索"
          >
            <i class="el-icon-search"></i>
            <span>搜索</span>
          </button>
        </div>

        <el-select
          v-model="selectedType"
          placeholder="应用类型"
          clearable
          class="filter-select"
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in appTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          >
            <i :class="type.icon"></i>
            <span style="margin-left: 8px;">{{ type.label }}</span>
          </el-option>
        </el-select>

        <el-select
          v-model="selectedGroup"
          placeholder="应用分组"
          clearable
          class="filter-select"
          @change="handleGroupChange"
        >
          <el-option
            v-for="group in appGroups"
            :key="group.name"
            :label="`${group.name} (${group.count})`"
            :value="group.name"
          />
        </el-select>

        <el-select
          v-model="selectedStatus"
          placeholder="应用状态"
          clearable
          class="filter-select"
          @change="handleStatusChange"
        >
          <el-option
            v-for="status in appStatuses"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          >
            <i :class="status.icon" :style="{ color: status.color }"></i>
            <span style="margin-left: 8px;">{{ status.label }}</span>
          </el-option>
        </el-select>

      </div>
    </div>

    <!-- 应用列表 -->
    <div
      class="app-container"
      v-loading="loading"
    >
      <!-- 网格视图 -->
      <div class="grid-view">
        <div class="app-grid">
          <!-- 新增应用卡片 -->
          <div class="app-card create-card" @click="handleCreateApp">
            <div class="create-card-content">
              <div class="create-icon">
                <i class="el-icon-plus"></i>
              </div>
              <div class="create-text">
                <h3>创建应用</h3>
                <div class="create-options">
                  <div class="create-option" @click.stop="handleCreateFromTemplate">
                    <i class="el-icon-document"></i>
                    <span>创建空白应用</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 应用卡片 -->
          <div
            v-for="app in filteredApps"
            :key="app.id"
            class="app-card"
            @click="handleAppClick(app)"
          >
            <div class="app-card-header">
              <div class="app-icon">
                <img
                  v-if="app.icon && app.icon.trim()"
                  :src="app.icon"
                  :alt="app.name"
                  @error="handleImageError"
                />
                <div v-else class="default-icon" :style="getDefaultIconStyle(app.name)">
                  {{ getAppIconLetter(app.name) }}
                </div>
              </div>

              <div class="app-info">
                <div class="app-title">
                  <h3 class="app-name" :title="app.name">{{ app.name }}</h3>
                </div>
                <p class="app-group" v-if="app.groupName">{{ app.groupName }}</p>
              </div>

              <div class="app-type-badge" :class="getTypeBadgeClass(app.type)">
                <span class="type-text">{{ getTypeLabel(app.type) }}</span>
              </div>
            </div>

            <div class="app-card-body">
              <p class="app-description" :title="app.description">
                {{ app.description || '暂无描述' }}
              </p>

              <div class="app-tags">
                <div class="tags-left">
                  <el-tag
                    size="mini"
                    class="app-tag"
                  >
                    {{ app.code }}
                  </el-tag>
                </div>
                <div class="app-status-badge" :class="getAppStatusClass(app)">
                  <i :class="getAppStatusIcon(app)"></i>
                  <span class="status-text">{{ getAppStatusText(app) }}</span>
                </div>
              </div>
            </div>

            <div class="app-card-footer">
              <span class="create-time">
                {{ formatDate(app.createTime) }}
              </span>
              <div class="app-actions">
                <el-button
                  type="text"
                  icon="el-icon-view"
                  size="mini"
                  @click.stop="handleViewDetail(app)"
                  title="查看详情"
                />
                <el-dropdown
                  @command="(command) => handleDropdownCommand(command, app)"
                  trigger="click"
                  @click.stop
                  placement="bottom-end"
                  popper-class="app-dropdown-menu"
                >
                  <el-button
                    type="text"
                    icon="el-icon-more"
                    size="mini"
                    title="更多操作"
                    @click.stop
                    class="more-btn"
                  />
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="delete" class="dropdown-item-custom delete-item">
                      <i class="el-icon-delete"></i>
                      <span>删除应用</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredApps.length === 0 && !loading" class="empty-state">
        <i class="el-icon-box"></i>
        <p>暂无应用数据</p>
        <p class="empty-tip">{{ searchKeyword ? '尝试调整搜索条件' : '暂时没有集成应用' }}</p>
      </div>

    </div>

    <!-- 分页组件 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background
      />
    </div>

    <!-- 创建应用对话框 -->
    <CreateAppDialog
      :visible.sync="showCreateDialog"
      @created="handleAppCreated"
    />
  </div>
</template>

<script>
import {
  getIntegrationAppList,
  getIntegrationAppCount,
  deleteIntegrationApp
} from '@system/api/integration/integration-app'
import { getDefaultIconStyle as getIconStyle, getAppIconLetter as getIconLetter } from '@system/utils/appIconUtils'
import CreateAppDialog from './components/CreateAppDialog.vue'

export default {
  name: 'IntegrationAppList',
  components: {
    CreateAppDialog
  },
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedType: '',
      selectedGroup: '',
      selectedStatus: '',

      // 数据
      appList: [],
      appTypes: [
        { value: 'PROXY', label: '代理应用', icon: 'el-icon-connection' },
        { value: 'FLOW', label: '集成流', icon: 'el-icon-share' }
      ],
      appGroups: [],
      appStatuses: [
        { value: true, label: '启用', icon: 'el-icon-check', color: '#52c41a' },
        { value: false, label: '禁用', icon: 'el-icon-close', color: '#ff4d4f' }
      ],

      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,

      // 状态
      loading: false,

      // 对话框状态
      showCreateDialog: false
    }
  },
  
  computed: {
    filteredApps() {
      return this.appList
    }
  },
  
  created() {
    this.initData()
  },


  
  methods: {
    async initData() {
      this.loading = true
      await Promise.all([
        this.loadAppList(true),
        this.loadAppGroups()
      ])
      this.loading = false
    },

    // 加载应用分组数据
    async loadAppGroups() {
      try {
        const response = await getIntegrationAppCount({
          groups: ['groupName']
        })

        if (Array.isArray(response)) {
          this.appGroups = response.map(item => ({
            name: item.groupName || '未分组',
            count: item.count || 0
          })).filter(group => group.name && group.name !== '未分组' || group.count > 0)
        } else {
          console.warn('Unexpected response format for app groups:', response)
          this.appGroups = []
        }
      } catch (error) {
        console.error('加载应用分组失败:', error)
        this.appGroups = []
      }
    },

    async loadAppList(reset = false) {
      try {
        if (reset) {
          this.currentPage = 1
          this.total = 0
        }

        // 构建请求参数
        const params = {
          current: this.currentPage,
          size: this.pageSize,
          'orders[sort]':'asc',
          ...(this.searchKeyword?.trim() && { keyword: this.searchKeyword.trim() }),
          ...(this.selectedType && { type: this.selectedType }),
          ...(this.selectedGroup && { groupName: this.selectedGroup }),
          ...(this.selectedStatus !== '' && { enabled: this.selectedStatus })
        }

        const response = await getIntegrationAppList(params)

        // 全局响应已经把data取出来了，直接使用
        let newList = []
        let total = 0

        if (response && response.records) {
          // MyBatis Plus 分页结构
          newList = response.records.map(app => ({
            ...app,
            tags: Array.isArray(app.tags) ? app.tags : (app.tags ? [app.tags] : []) // 确保tags始终是数组
          }))
          total = response.total || 0
        } else if (response && response.list) {
          // 自定义分页结构
          newList = response.list.map(app => ({
            ...app,
            tags: Array.isArray(app.tags) ? app.tags : (app.tags ? [app.tags] : []) // 确保tags始终是数组
          }))
          total = response.total || 0
        } else if (Array.isArray(response)) {
          // 直接返回数组
          newList = response.map(app => ({
            ...app,
            tags: Array.isArray(app.tags) ? app.tags : (app.tags ? [app.tags] : []) // 确保tags始终是数组
          }))
          total = response.length
        } else {
          console.warn('Unexpected response format:', response)
          newList = []
          total = 0
        }

        // 直接设置应用列表
        this.appList = newList
        this.total = total
      } catch (error) {
        console.error('加载应用列表失败:', error)
        this.$message.error('加载应用列表失败，请稍后重试')
        this.appList = []
        this.total = 0
      }
    },





    // 分页事件处理
    handleSizeChange(newSize) {
      this.pageSize = newSize
      this.currentPage = 1
      this.loadAppList(true)
    },

    handleCurrentChange(newPage) {
      this.currentPage = newPage
      this.loadAppList(false)
    },
    
    // 搜索处理
    handleSearch() {
      this.loadAppList(true)
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.loadAppList(true)
    },

    // 类型筛选
    handleTypeChange() {
      this.loadAppList(true)
    },

    // 分组筛选
    handleGroupChange() {
      this.loadAppList(true)
    },

    // 状态筛选
    handleStatusChange() {
      this.loadAppList(true)
    },
    
    // 应用操作
    handleAppClick(app) {
      console.log('点击应用:', app)
      // 跳转到应用详情页面
      if (app.type === 'PROXY') {
        // 使用动态路由跳转到代理应用详情页面
        this.$router.push({
          path: `/integration/integration-app/proxy-app`,
          query: {
            id: app.id,
            code: app.code,
            name: app.name
          }
        })
      } else if (app.type === 'FLOW') {
        // 跳转到集成流应用详情页面
        this.$router.push({
          path: `/integration/integration-app/flow-app`,
          query: {
            id: app.id,
            code: app.code,
            name: app.name
          }
        })
      } else {
        // 其他类型应用的处理逻辑
        console.log('其他类型应用:', app.type)
        this.$message.info('该应用类型暂不支持详情查看')
      }
    },

    handleViewDetail(app) {
      // 与点击卡片逻辑一致，直接进入详情页
      this.handleAppClick(app)
    },

    // 处理下拉菜单命令
    handleDropdownCommand(command, app) {
      switch (command) {
        case 'delete':
          this.handleDeleteApp(app)
          break
        default:
          console.log('未知命令:', command)
      }
    },

    // 删除应用
    async handleDeleteApp(app) {
      try {
        await this.$confirm(
          `确定要删除应用"${app.name}"吗？此操作不可撤销。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )

        // 调用删除API
        await deleteIntegrationApp(app.id)

        // 删除成功提示
        this.$message.success(`应用"${app.name}"删除成功`)

        // 刷新列表
        this.loadAppList(true)

      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除应用失败:', error)
          this.$message.error('删除应用失败，请稍后重试')
        }
      }
    },

    // 创建应用操作
    handleCreateApp() {
      this.showCreateDialog = true
    },

    handleCreateFromTemplate() {
      this.showCreateDialog = true
    },



    // 应用创建成功回调
    handleAppCreated(appData) {
      console.log('应用创建成功:', appData)
      // 刷新应用列表
      this.loadAppList(true)
    },
    
    // 工具方法
    getTypeIcon(type) {
      const iconMap = {
        'PROXY': 'el-icon-connection',
        'FLOW': 'el-icon-share'
      }
      return iconMap[type] || 'el-icon-box'
    },

    getTypeLabel(type) {
      const labelMap = {
        'PROXY': '代理',
        'FLOW': '集成流'
      }
      return labelMap[type] || type
    },

    getTypeTagType(type) {
      const typeMap = {
        'PROXY': 'info',
        'FLOW': 'warning'
      }
      return typeMap[type] || ''
    },

    getTypeBadgeClass(type) {
      const classMap = {
        'PROXY': 'badge-proxy',
        'FLOW': 'badge-flow'
      }
      return classMap[type] || 'badge-default'
    },

    // 获取应用状态样式类
    getAppStatusClass(app) {
      return app.enabled ? 'status-enabled' : 'status-disabled'
    },

    // 获取应用状态图标
    getAppStatusIcon(app) {
      return app.enabled ? 'el-icon-check' : 'el-icon-close'
    },

    // 获取应用状态文本
    getAppStatusText(app) {
      return app.enabled ? '启用' : '禁用'
    },

    // 获取默认图标样式（包装工具函数）
    getDefaultIconStyle(appName) {
      return getIconStyle(appName, {
        fontSize: '20px',
        fontWeight: '600',
        color: '#fff'
      })
    },

    // 获取应用图标字母（包装工具函数）
    getAppIconLetter(appName) {
      return getIconLetter(appName)
    },



    handleImageError(event) {
      // 图片加载失败时隐藏img标签，显示默认图标
      event.target.style.display = 'none'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.integration-app {
  padding: 16px 16px 8px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #f0f2f5 100%);
  min-height: calc(100vh - 60px);
  height: calc(100vh - 84px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .header-section {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      pointer-events: none;
    }

    .search-filter-bar {
      display: flex;
      align-items: center;
      gap: 32px;
      flex-wrap: wrap;
      position: relative;
      z-index: 1;

      .custom-search-input {
        flex: 1;
        min-width: 280px;
        max-width: 380px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        overflow: hidden;

        &:hover {
          transform: translateY(-1px);
          border-color: rgba(24, 144, 255, 0.3);
          background: rgba(255, 255, 255, 0.95);
        }

        &:focus-within {
          transform: translateY(-1px);
          border-color: #1890ff;
          background: white;
          box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .search-input-field {
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          padding: 0 16px;
          height: 40px;
          font-size: 14px;
          color: #262626;
          caret-color: #1890ff;

          &::placeholder {
            color: #8c8c8c;
            font-size: 13px;
          }
        }

        .clear-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          border: none;
          background: transparent;
          color: #8c8c8c;
          cursor: pointer;
          border-radius: 50%;
          margin-right: 8px;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.06);
            color: #262626;
          }

          i {
            font-size: 14px;
          }
        }

        .search-button {
          display: flex;
          align-items: center;
          gap: 6px;
          border: none;
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          color: white;
          padding: 0 16px;
          height: 40px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
          font-size: 14px;
          font-weight: 500;

          &:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          }

          &:active {
            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
          }

          i {
            font-size: 16px;
          }

          span {
            font-size: 14px;
          }
        }
      }

      .filter-select {
        min-width: 150px;

        ::v-deep .el-input__inner {
          background: rgba(255, 255, 255, 0.9);
          border: 1px solid rgba(0, 0, 0, 0.08);
          border-radius: 12px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          height: 40px;
          font-size: 14px;
          backdrop-filter: blur(8px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          cursor: pointer;
          caret-color: transparent;

          &:hover {
            border-color: rgba(24, 144, 255, 0.3);
            background: rgba(255, 255, 255, 0.95);
          }

          &:focus {
            border-color: #1890ff;
            background: white;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
            caret-color: transparent;
          }
        }

        ::v-deep .el-input__suffix {
          color: #8c8c8c;
          transition: color 0.3s ease;
          pointer-events: none;
        }

        &:hover {
          ::v-deep .el-input__suffix {
            color: #1890ff;
          }
        }
      }
    }
  }

  .app-container {
    flex: 1;
    min-height: 400px;
    display: flex;
    flex-direction: column;

    // 网格视图样式
    .grid-view {
      flex: 1;

      .app-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
        padding: 4px;
      }

      .app-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        height: 200px;
        display: flex;
        flex-direction: column;
        border: 1px solid rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;

        // 新增卡片样式
        &.create-card {
          border: 2px dashed #d9d9d9;
          background: #fafafa;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            border-color: #1890ff;
            background: #f6f8ff;
            transform: translateY(-2px);

            .create-icon {
              color: #1890ff;
              transform: scale(1.1);
            }
          }

          .create-card-content {
            text-align: center;
            width: 100%;

            .create-icon {
              font-size: 32px;
              color: #bfbfbf;
              margin-bottom: 16px;
              transition: all 0.3s ease;
            }

            .create-text {
              h3 {
                font-size: 16px;
                color: #262626;
                margin: 0 0 16px 0;
                font-weight: 600;
              }

              .create-options {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .create-option {
                  display: flex;
                  align-items: center;
                  padding: 8px 12px;
                  border-radius: 6px;
                  background: white;
                  border: 1px solid #e8e8e8;
                  transition: all 0.2s ease;
                  cursor: pointer;

                  &:hover {
                    border-color: #1890ff;
                    background: #f6f8ff;
                    transform: translateX(4px);
                  }

                  i {
                    font-size: 14px;
                    color: #666;
                    margin-right: 8px;
                    width: 16px;
                  }

                  span {
                    font-size: 12px;
                    color: #666;
                    font-weight: 500;
                  }
                }
              }
            }
          }
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background-image: linear-gradient(-225deg, #231557 0%, #44107A 29%, #FF1361 67%, #FFF800 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          transform: translateY(-4px);
          border-color: rgba(24, 144, 255, 0.2);

          &::before {
            opacity: 1;
          }
        }

        .app-card-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 10px;

          .app-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f2f5;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .default-icon {
              font-size: 20px;
              font-weight: 500;
              border-radius: 10px;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .app-info {
            flex: 1;
            min-width: 0;

            .app-title {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .app-name {
                font-size: 14px;
                font-weight: 600;
                color: #262626;
                margin: 0;
                flex: 1;
                min-width: 0;
                line-height: 1.3;
                word-break: break-word;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              .app-code-tag {
                flex-shrink: 0;
                background: rgba(24, 144, 255, 0.1);
                color: #1890ff;
                border: none;
                font-size: 10px;
                height: 18px;
                line-height: 16px;
                padding: 0 6px;
                border-radius: 9px;
                font-weight: 500;
              }
            }

            .app-group {
              font-size: 11px;
              color: #666;
              margin: 0;
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }



          .app-type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            flex-shrink: 0;
            align-self: flex-start;

            &.badge-proxy {
              background: #e6f7ff;
              color: #1890ff;
            }

            &.badge-flow {
              background: #fff7e6;
              color: #fa8c16;
            }

            &.badge-default {
              background: #f0f0f0;
              color: #666;
            }

            .type-text {
              font-size: 11px;
            }
          }
        }

        .app-card-body {
          flex: 1;
          display: flex;
          flex-direction: column;

          .app-description {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            margin: 0 0 12px 0;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            flex: 1;
          }

          .app-tags {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;

            .tags-left {
              display: flex;
              flex-wrap: wrap;
              gap: 6px;
              flex: 1;
              min-width: 0;

              .app-tag {
                font-size: 10px;
                height: 20px;
                line-height: 18px;
                padding: 0 6px;
                border: none;
                background: linear-gradient(135deg, #f6f8fa, #e8e8e8);
                color: #586069;
                border-radius: 10px;
                font-weight: 500;
                transition: all 0.2s ease;

                &:hover {
                  background: linear-gradient(135deg, #e8e8e8, #d9d9d9);
                  transform: translateY(-1px);
                }
              }

              .more-tags {
                font-size: 10px;
                color: #8c8c8c;
                padding: 0 4px;
                background: #f0f0f0;
                border-radius: 8px;
                height: 20px;
                line-height: 20px;
              }
            }

            .app-status-badge {
              display: inline-flex;
              align-items: center;
              gap: 4px;
              padding: 3px 8px;
              border-radius: 12px;
              font-size: 10px;
              font-weight: 600;
              transition: all 0.3s ease;
              margin-left: 8px;
              flex-shrink: 0;

              i {
                font-size: 10px;
              }

              .status-text {
                font-weight: 600;
              }

              &.status-enabled {
                background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
                color: #ffffff;
                box-shadow: 0 1px 4px rgba(82, 196, 26, 0.3);

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.4);
                }
              }

              &.status-disabled {
                background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
                color: #ffffff;
                box-shadow: 0 1px 4px rgba(255, 77, 79, 0.3);

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.4);
                }
              }
            }
          }
        }

        .app-card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12px;
          padding-top: 8px;
          border-top: 1px solid #f0f0f0;

          .create-time {
            font-size: 12px;
            color: #999;
            font-weight: 500;
          }

          .app-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 6px 10px;
              color: #8c8c8c;
              border-radius: 6px;
              transition: all 0.2s ease;

              &:hover {
                color: #1890ff;
                background: rgba(24, 144, 255, 0.1);
                transform: scale(1.05);
              }
            }

            .more-btn {
              padding: 4px;
              border-radius: 4px;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(24, 144, 255, 0.1);
                color: #1890ff;
              }

              i {
                font-size: 14px;
              }
            }
          }
        }
      }
    }



    // 空状态样式
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #8c8c8c;

      i {
        font-size: 64px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }

      p {
        margin: 8px 0;
        font-size: 14px;

        &.empty-tip {
          font-size: 12px;
          color: #bfbfbf;
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: auto;
    margin-bottom: 0;
    padding: 12px 20px 8px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.3);

    ::v-deep .el-pagination {
      .btn-prev,
      .btn-next,
      .el-pager li {
        background: #ffffff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin: 0 4px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        color: #606266;
        min-width: 32px;
        height: 32px;
        line-height: 30px;

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
          background: #f0f8ff;
          transform: translateY(-1px);
        }

        &.is-active {
          background: #1890ff !important;
          color: #ffffff !important;
          font-weight: 600;
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        &.disabled {
          color: #c0c4cc;
          background: #f5f7fa;
          border-color: #e4e7ed;
          cursor: not-allowed;

          &:hover {
            color: #c0c4cc;
            background: #f5f7fa;
            border-color: #e4e7ed;
            transform: none;
          }
        }
      }

      .el-pagination__sizes,
      .el-pagination__total,
      .el-pagination__jump {
        color: #606266;
        font-weight: 500;

        .el-input__inner {
          background: #ffffff;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          transition: all 0.3s ease;
          color: #606266;

          &:focus {
            border-color: #1890ff;
            background: #ffffff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }

      .el-pagination__total {
        margin-right: 16px;
      }

      .el-pagination__jump {
        margin-left: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .integration-app {
    padding: 12px 12px 6px 12px;

    .header-section {
      padding: 20px 16px;
      margin-bottom: 20px;

      .search-filter-bar {
        flex-direction: column;
        gap: 24px;
        align-items: stretch;

        .search-input {
          min-width: auto;
          max-width: none;

          ::v-deep .el-input__inner {
            height: 44px;
            font-size: 16px; // 防止iOS缩放
          }
        }

        .filter-select {
          min-width: auto;

          ::v-deep .el-input__inner {
            height: 44px;
            font-size: 16px; // 防止iOS缩放
          }
        }
      }
    }

    .grid-view {
      .app-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .app-card {
        height: auto;
        min-height: 240px;
        padding: 18px;
      }
    }

    .pagination-wrapper {
      margin-top: 16px;
      padding: 10px 16px 6px 16px;
      border-radius: 10px;
    }
  }
}

@media (max-width: 480px) {
  .integration-app {
    padding: 8px 8px 4px 8px;

    .header-section {
      padding: 16px 12px;
      margin-bottom: 16px;
      border-radius: 12px;

      .search-filter-bar {
        gap: 20px;

        .search-input {
          ::v-deep .el-input__inner {
            height: 42px;
            border-radius: 10px;
            font-size: 16px;
          }
        }

        .filter-select {
          ::v-deep .el-input__inner {
            height: 42px;
            border-radius: 10px;
            font-size: 16px;
          }
        }
      }
    }

    .grid-view {
      .app-grid {
        gap: 12px;
      }

      .app-card {
        padding: 16px;
        min-height: 220px;

        .app-card-header {
          margin-bottom: 12px;
          gap: 10px;

          .app-icon {
            width: 40px;
            height: 40px;
          }

          .app-info {
            .app-title {
              .app-name {
                font-size: 15px;
              }

              .app-code-tag {
                font-size: 9px;
                height: 16px;
                line-height: 14px;
                padding: 0 4px;
              }
            }

            .app-group {
              font-size: 10px;
            }
          }
        }

        .app-card-body {
          .app-description {
            font-size: 12px;
            -webkit-line-clamp: 2;
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: 12px;
      padding: 8px 12px 4px 12px;
      border-radius: 8px;
    }
  }
}

// 全局下拉菜单样式
::v-deep .el-select-dropdown {
  border: none;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin-top: 4px;

  .el-select-dropdown__item {
    padding: 12px 16px;
    font-size: 14px;
    color: #262626;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 8px;

    &:hover {
      background: rgba(24, 144, 255, 0.08);
      color: #1890ff;
      transform: translateX(2px);
    }

    &.selected {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      font-weight: 500;

      &::after {
        color: #1890ff;
      }
    }

    i {
      margin-right: 8px;
      font-size: 14px;
    }
  }

  .el-scrollbar__view {
    padding: 0;
  }
}

// 多选标签样式
::v-deep .el-tag {
  &.el-tag--info {
    background: rgba(24, 144, 255, 0.1);
    border-color: rgba(24, 144, 255, 0.2);
    color: #1890ff;
    border-radius: 6px;
    font-size: 12px;
    height: 24px;
    line-height: 22px;
    padding: 0 8px;

    .el-tag__close {
      color: #1890ff;

      &:hover {
        background: rgba(24, 144, 255, 0.2);
        color: white;
      }
    }
  }
}

// 下拉菜单美化样式
::v-deep .app-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  padding: 4px 0;
  min-width: 120px;

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.4;
    transition: all 0.2s ease;

    &.dropdown-item-custom {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 14px;
        width: 14px;
        text-align: center;
      }

      span {
        flex: 1;
      }
    }

    &:hover {
      background: #f6f8fa;
      color: #1890ff;
    }

    &.delete-item:hover {
      background: #fff2f0;
      color: #ff4d4f;
    }

    &:not(:last-child) {
      margin-bottom: 2px;
    }
  }

  .popper__arrow {
    border-bottom-color: #e8e8e8;
  }

  .popper__arrow::after {
    border-bottom-color: #fff;
  }
}
</style>
