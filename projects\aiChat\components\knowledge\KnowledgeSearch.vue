<template>
  <el-dialog
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    title="知识库搜索"
    width="800px"
    @close="handleClose"
  >
    <el-form :model="searchForm" class="search-form" label-width="80px">
      <el-form-item label="搜索内容" required>
        <el-input 
          v-model="searchForm.query" 
          type="textarea"
          :rows="4"
          placeholder="请输入搜索内容"
          resize="none">
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="选择文件">
        <el-select 
          v-model="searchForm.file_name" 
          placeholder="请选择文件"
          clearable
          style="width: 100%">
          <el-option
            v-for="file in fileList"
            :key="file.name"
            :label="file.name"
            :value="file.name">
          </el-option>
        </el-select>
      </el-form-item> -->
      
      <!-- 高级设置开关 -->
      <el-form-item>
        <div class="advanced-settings-toggle">
          <el-link 
            type="primary" 
            :underline="false"
            @click="showAdvanced = !showAdvanced"
          >
            {{ showAdvanced ? '收起高级设置' : '展开高级设置' }}
            <i :class="showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-link>
        </div>
      </el-form-item>

      <!-- 高级设置区域 -->
      <div v-show="showAdvanced" class="advanced-settings">
        <el-form-item label="返回数量">
          <el-input-number 
            v-model="searchForm.top_k" 
            :min="0"
            :max="10"
            :step="1"
            controls-position="right"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
        
        <el-form-item label="相关度">
          <el-input-number 
            v-model="searchForm.score_threshold" 
            :min="0"
            :max="2"
            :step="1"
            controls-position="right"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
      </div>

      <el-form-item>
        <el-button 
          type="primary" 
          @click="handleSearch" 
          :loading="searching"
          style="width: 100%">
          搜索
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 搜索结果列表 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <el-card v-for="(result, index) in searchResults" 
               :key="index" 
               class="result-card"
               shadow="hover">
        <div class="result-content">
          <p class="content-text">{{ result.page_content }}</p>
          <div class="result-meta">
            <span>来源：{{ result.metadata.source }}</span>
            <span>相关度：{{ result.score.toFixed(2) }}</span>
          </div>
        </div>
      </el-card>
    </div>
    <div v-else-if="searched" class="empty-result">
      暂无搜索结果
    </div>
  </el-dialog>
</template>

<script>
import api from '../../api/knowledge'

export default {
  name: 'KnowledgeSearch',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    knowledgeBaseName: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      searchForm: {
        query: '',
        file_name: '',
        top_k: 3,
        score_threshold: 1,
        metadata: {}
      },
      searchResults: [],
      fileList: [],
      searching: false,
      searched: false,
      showAdvanced: false // 控制高级设置的显示/隐藏
    }
  },

  methods: {
    async handleSearch() {
      if (!this.searchForm.query.trim()) {
        this.$message.warning('请输入搜索内容')
        return
      }

      try {
        this.searching = true
        const results = await api.search({
          ...this.searchForm,
          knowledge_base_name: this.knowledgeBaseName,
          query: this.searchForm.query.trim()
        })
        this.searchResults = results
        this.searched = true
      } catch (error) {
        this.$message.error('搜索失败：' + error.message)
      } finally {
        this.searching = false
      }
    },

    async loadFileList() {
      try {
        const files = await api.getFiles(this.knowledgeBaseName)
        this.fileList = files || []
      } catch (error) {
        this.$message.error('获取文件列表失败：' + error.message)
      }
    },

    handleClose() {
      this.clearSearch()
      this.$emit('update:visible', false)
    },

    clearSearch() {
      this.searchForm = {
        query: '',
        file_name: '',
        top_k: 3,
        score_threshold: 1,
        metadata: {}
      }
      this.searchResults = []
      this.searched = false
      this.showAdvanced = false
    }
  },

  watch: {
    visible(val) {
      if (val) {
        this.loadFileList()
      }
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}

.advanced-settings-toggle {
  text-align: right;
  margin-bottom: 10px;
}

.advanced-settings-toggle .el-link {
  font-size: 14px;
}

.advanced-settings-toggle i {
  margin-left: 5px;
}

.advanced-settings {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-results {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
}

.result-card {
  margin-bottom: 15px;
}

.result-content {
  font-size: 14px;
}

.content-text {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.result-meta {
  margin-top: 10px;
  color: #666;
  font-size: 12px;
  display: flex;
  gap: 15px;
}

.empty-result {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 30px 0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__content) {
  line-height: 1;
}

:deep(.el-input-number) {
  width: 100%;
}
</style> 