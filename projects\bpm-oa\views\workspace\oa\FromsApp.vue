<template>
  <div v-loading="loading">
    <div class="fixed-search hidden-xs-only">
      <el-input size="small" clearable placeholder="搜索表单" prefix-icon="el-icon-search" v-model="searchForm"></el-input>
      <!-- <el-popover placement="bottom-end" width="480" trigger="click">
        <div class="notify">
          <el-empty :image-size="50" description="暂无消息 😁" v-if="notify.total === 0"></el-empty>
          <div v-for="msg in notify.records" :key="msg.id" class="notify-item">
            <el-row>
              <el-col :span="2">
                <div class="notify-item-type-icon">
                  <i class="el-icon-success" v-if="msg.type === 'SUCCESS'" style="color: #02b068"></i>
                  <i class="el-icon-warning" v-else-if="msg.type === 'WARNING'" style="color: #f78f5f"></i>
                  <i class="el-icon-error" v-else-if="msg.type === 'ERROR'" style="color: #f25643"></i>
                  <i class="el-icon-info" v-else style="color: #8c8c8c"></i>
                </div>
              </el-col>
              <el-col :span="22">
                <div class="notify-item-title" @click="toNotify(msg)">{{ msg.title }}</div>
                <ellipsis hoverTip class="notify-item-content" :content="msg.content" />
              </el-col>
            </el-row>
            <span class="notify-item-time">{{ msg.createTime.substring(5, 16) }}</span>
            <el-button type="text" class="notify-btn" @click="readNotify(msg.id)">已读</el-button>
          </div>
        </div>
        <div class="notify-action" v-show="notify.total > 0">
          <el-button type="text" @click="--params.pageNo" :disabled="params.pageNo <= 1">上一页</el-button>
          <el-button type="text" @click="readNotify(null)">本页已读</el-button>
          <el-button type="text" @click="++params.pageNo" :disabled="notify.total <= params.pageSize * notify.current">
            下一页
          </el-button>
        </div>
        <el-badge class="notify-badge" slot="reference" :hidden="notify.total === 0" :value="notify.total">
          <i class="el-icon-bell"></i>
        </el-badge>
      </el-popover> -->
    </div>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="count" @click="to('unfinished')">
          <div>
            <p>待我处理</p>
            <div>{{ taskCount.todo }}</div>
          </div>
          <img src="../../../assets/image/pending.png">
        </div>
      </el-col>
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="count" @click="to('submit')">
          <div>
            <p>我发起的</p>
            <div>{{ taskCount.mySubmited }}</div>
          </div>
          <img src="../../../assets/image/submit.png">
        </div>
      </el-col>
      <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
        <div class="count" @click="to('cc')">
          <div>
            <p>抄送我的</p>
            <div>{{ taskCount.cc }}</div>
          </div>
          <img src="../../../assets/image/cc.png">
        </div>
      </el-col>
    </el-row>
    <div class="group" v-if="searchForm && searchForm.trim() !== ''">
      <div class="group-title">搜索结果</div>
      <div class="group-container">
        <div class="group-item" v-for="(item, index) in searchResult" :key="index" @click="enterItem(item)">
          <div>
            <iconify :icon="item.logo.icon" class="w-form-icon" :style="'background: ' + item.logo.background">
            </iconify>
            <ellipsis class="item-name" hover-tip :content="item.formName" />
          </div>
        </div>
        <el-empty v-if="searchResult.length === 0" :image-size="100"
          :description="`未搜索到 '${searchForm}' 相关表单`"></el-empty>
      </div>
    </div>
    <div v-else>
      <div class="group" v-if="recentlyUsed && recentlyUsed.length > 0">
        <div class="group-title">
          最近使用
          <el-link style="float:right;" :underline="false" type="text" icon="el-icon-delete"
            @click="clearUsed">清空</el-link>
        </div>
        <div class="group-container">
          <div class="group-item" v-for="(item, index) in recentlyUsed" :key="index" @click="enterItem(item)">
            <div>
              <iconify :icon="item.logo.icon" class="w-form-icon" :style="'background: ' + item.logo.background">
              </iconify>
              <ellipsis class="item-name" hover-tip :content="item.formName" />
            </div>
          </div>
        </div>
      </div>

      <div class="group" v-for="(group, index) in formList.list" :key="index"
        v-show="group.items.length > 0 && group.id > 0">
        <div class="group-title">{{ group.name }}</div>
        <div class="group-container">
          <div class="group-item" v-for="(item, index) in group.items" :key="index" @click="enterItem(item)">
            <div>
              <iconify :icon="item.logo.icon" class="w-form-icon" :style="'background: ' + item.logo.background">
              </iconify>
              <ellipsis class="item-name" hover-tip :content="item.formName" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <w-dialog v-if="!isMobile" :ok-loading="submitLoading" :title="`发起 - ${selectForm.formName}`" closeFree
      width="1100px" v-model="openItemDl" okText="提 交" @cancel="openItemDl = false" @ok="submitForm">
      <initiate-process ref="processForm" @render-ok="submitLoading = false" :code="selectForm.formId" v-if="openItemDl"
        @fail="submitLoading = false" @ok="openItemDl = false, submitLoading = false" />
    </w-dialog>
  </div>

</template>

<script>
import { getGroupModelsByUser, getProcessCountData } from '@bpm-oa-web/api/modelGroup'
import InitiateProcess from "../InitiateProcess";
import { Icon } from '@iconify/vue2';
import { getUserNotify, readNotify } from '@bpm-oa-web/api/notify'
import mixin_notify from '@bpm-oa-web/mixin_notify.js'

export default {
  name: "ApprovalGroup",
  components: { InitiateProcess, Icon },
  mixins: [mixin_notify],
  data() {
    return {
      taskCount: {
        todo: 0,
        mySubmited: 0,
        cc: 0
      },
      submitLoading: false,
      recentlyUsed: [],
      //searchResult: [],
      searchForm: '',
      loading: false,
      openItemDl: false,
      selectForm: {},
      formItem: {},
      actives: [],
      formList: {
        list: [],
        inputs: '',
        searchResult: []
      },
      pending: {
        list: []
      },
      popupStyle: {
        height: '100%',
        width: '100%',
        background: '#f7f7f9'
      },
      params: {
        pageSize: 5,
        pageNo: 1
      },
      notify: {
        records: []
      },
    }
  },
  // beforeDestroy() {
  //   if (this.timer) {
  //     clearInterval(this.timer)
  //   }
  // },
  mounted() {
    this.getGroupModels()
    this.getCountData()
    this.recentlyUsed = JSON.parse(localStorage.getItem(`recentlyUsed:${(this.loginUser || {}).id}`) || '[]')
    // if (this.loginUser.id) {
    //   this.getUserNotify()
    //   this.timerGetNotify(5)
    // }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
    loginUser() {
      return this.$store.state.bpm.loginUser
    },
    searchResult() {
      let result = []
      this.formList.list.forEach(group => {
        group.items.forEach(item => {
          if (item.formName.indexOf(this.searchForm) > -1) {
            result.push(item)
          }
        })
      })
      return result
    }
  },
  methods: {
    getUserNotify() {
      getUserNotify(this.params).then(res => {
        this.notify = res.data
      }).catch(err => {
        if (this.timer) {
          clearInterval(this.timer)
        }
        this.$err(err, '获取通知消息失败')
      })
    },
    toNotify(msg) {
      if (this.$isNotEmpty(msg.instanceId)) {
        this.selectInstance = msg.instanceId
        this.processVisible = true
        this.readNotify(msg.id)
      }
    },
    readNotify(id) {
      let list = id ? [id] : this.notify.records.map(n => n.id);
      readNotify(list).then(rsp => {
        this.$ok(rsp, '已读成功')
        this.getUserNotify()
      }).catch(err => {
        this.$err(err, '已读失败')
      })
    },
    timerGetNotify(cycle) {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => this.getUserNotify(), cycle * 1000)
    },
    getGroupModels() {
      this.loading = true
      const idSet = new Set()
      getGroupModelsByUser().then(rsp => {
        this.loading = false
        this.formList.list = rsp.data
        this.formList.list.forEach(group => {
          this.actives.push(group.name)
          group.items.forEach(item => {
            item.logo = JSON.parse(item.logo)
            idSet.add(item.formId)
          })
        })
        this.groups = rsp.data
        this.filterRecentlyUsed(idSet)
      }).catch(err => {
        this.loading = false
        this.$err(err, '获取分组异常')
        this.recentlyUsed.length = 0
      })
    },
    async filterRecentlyUsed(collect) {
      this.recentlyUsed = this.recentlyUsed.filter(v => collect.has(v.formId))
    },
    getCountData() {
      getProcessCountData().then(rsp => {
        this.taskCount = rsp.data
      })
    },
    to(path) {
      this.$router.push('/workspace/' + path)
    },
    enterItem(item) {
      if (!this.$isNotEmpty(item.processDefId)) {
        this.$message.warning("该流程还未发布😥")
        return
      }
      this.selectForm = item
      this.openItemDl = true
      this.submitLoading = true
      this.recentlyUsed.removeByKey('formId', item.formId)
      this.recentlyUsed.unshift(item)
      if (this.recentlyUsed.length >= 20) {
        this.recentlyUsed.splice(1, this.recentlyUsed.length - 1)
      }
      localStorage.setItem(`recentlyUsed:${(this.loginUser || {}).id}`, JSON.stringify(this.recentlyUsed))
      if (this.isMobile) {
        this.$router.push('/mbinitiate?code=' + item.formId)
      }
    },
    clearUsed() {
      this.recentlyUsed = []
      localStorage.setItem(`recentlyUsed:${(this.loginUser || {}).id}`, '[]')
    },
    submitForm() {
      this.$refs.processForm.validate((validForm, validProcess) => {
        if (!this.isMobile) {
          if (validForm && validProcess) {
            this.submitLoading = true
            this.$refs.processForm.submit()
          } else {
            this.$message.warning("请完善表单/流程选项😥")
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.group {
  border-radius: 15px;
  padding: 20px 20px;
  margin: 0 15px 15px 15px;
  background: $theme-aside-bgc;

  .group-title {
    font-size: 15px;
    color: #303133;
    margin-bottom: 5px;
  }

  .group-container {}

  .group-item {
    display: inline-block;
    cursor: pointer;
    border-radius: 10px;
    text-align: center;
    margin: 5px;
    padding: 10px;
    width: 70px;

    .w-form-icon {
      padding: 8px;
      border-radius: 8px;
      font-size: 20px;
      color: #fff;
      background: #38adff;
      height: 20px;
      width: 20px;
      line-height: 20px;

      &:hover {
        box-shadow: 0 0 15px 0 #9f9999;
        //padding: 10px;
      }
    }

    .item-name {
      font-size: 12px;
      color: #303133;
      max-width: 80px;
      margin-top: 3px;
    }

    &>div {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}

.fixed-search {
  position: fixed;
  top: 25px;
  right: 220px;
}

.count {
  cursor: pointer;
  position: relative;
  background: $theme-aside-bgc;
  border-radius: 8px;
  height: 80px;
  margin: 0 15px 20px 15px;

  &>div {
    left: 15px;
    position: absolute;

    div {
      color: #b9b8b8;
      font-size: 17px;
      font-weight: bold;
    }
  }

  p {
    color: #303133;
    font-size: 14px;
  }

  img {
    position: absolute;
    right: 15px;
    top: 20px;
    width: 40px;
    height: 40px;
  }

  &:hover {
    box-shadow: 0 0 10px #eeeeee;
  }
}

// .notify {
//   max-height: 200px;
//   background: $theme-aside-bgc;
//   overflow-y: auto;

//   .notify-item:last-child {
//     border-bottom: 2px solid $theme-aside-bgc;
//   }

//   .notify-item {
//     border-top: 2px solid $theme-aside-bgc;
//     padding: 5px;
//     background: white;
//     position: relative;
//     border-radius: 5px;

//     .notify-item-title {
//       cursor: pointer;
//       color: #3b3b3b;

//       &:hover {
//         color: $theme-primary;
//       }
//     }

//     .notify-item-content {
//       color: #8c8c8c;
//       padding: 5px 0;
//       font-size: smaller;
//     }

//     .notify-item-time {
//       position: absolute;
//       right: 45px;
//       top: 7px;
//       font-size: 12px;
//       color: #8c8c8c;
//     }

//     .notify-btn {
//       position: absolute;
//       right: 5px;
//       top: 8px;
//     }

//     .notify-item-type-icon {
//       font-size: 18px;
//     }
//   }
// }

// .notify-action {
//   display: flex;
//   justify-content: space-between;
// }

// .notify-badge {
//   float: right;
//   position: absolute;
//   top: 50%;
//   margin-left: 20px;
//   transform: translateY(-50%);
// }
</style>
