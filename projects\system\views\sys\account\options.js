export default {
  api: 'sys/account',
  search: {
    isShow: true,
    showReset: true
  },
  table: {},
  formRule: [
    {
      type: "input",
      field: "username",
      className: "username-dom",
      title: "用户名",
      value: null,
      isSearch: true,              //是否为search内容
      isTable: true,               //是否为table内容
      isScope: false,              //是否自定义表格内容
      isSearchValidate: [],        //search列 validate
      isSearchCol: {              //search列 col
        md: { span: 6 }
      },
      props: {
        placeholder: '请输入',
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'blur',
          required: true,
          message: '用户名不能为空'
        }
      ],
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "input",
      field: "password",
      title: "密码",
      value: null,
      isSearch: false,
      isTable: false,
      props: {
        type: "password",
        showPassword: true,
        placeholder: '默认是123456,请输入密码',
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "input",
      field: "name",
      className: "name-dom",
      title: "名称",
      value: null,
      isSearch: true,
      isTable: true,
      isScope: false,
      isSearchValidate: [],
      isSearchCol: {              //search列 col
        md: { span: 6 }
      },
      props: {
        placeholder: "请输入名称",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        { trigger: 'blur', required: true, message: '名称不能为空' }
      ],
      col: { md: { span: 18 } }
    },
    {
      type: "input",
      field: "phone",
      className: "phone-dom", // 设置组件的class属性
      title: "手机号", // 组件的名称, 选填
      value: null,
      isSearch: true,
      isSearchCol: { md: { span: 6 } },
      isSearchValidate: [],
      isTable: true,
      props: {
        placeholder: "请输入手机号",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        { trigger: 'blur', required: true, message: '手机号不能为空' }
      ],
      isScope: false,
      col: { md: { span: 18 } }
    },
    {
      type: "treeSelect",
      field: "deptId",
      className: "deptId-dom",
      title: "所属部门",
      value: null,
      isSearch: false,
      isTable: true,
      isScope: false,
      options: [],
      props: {
        valueName: 'id',
        multiple: false,
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true // 是否显示清空按钮
      },
      validate: [
        {
          trigger: "change",
          required: true,
          message: "所属部门不能为空"
        }
      ],
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "treeSelect",
      field: "postId",
      className: "postId-dom",
      title: "所属职位",
      value: "",
      isSearch: false,
      isTable: true,
      isScope: false,
      options: [],
      props: {
        valueName: 'id',
        multiple: false,
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "select",
      field: "positionIds",
      className: "positionIds-dom",
      title: "角色",
      value: [],
      isSearch: false,
      isTable: false,
      isScope: false,
      options: [],
      props: {
        multiple: true,
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true // 是否显示清空按钮
      },
      validate: [
        {
          trigger: "change",
          required: true,
          message: "角色不能为空"
        }
      ],
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "select",
      field: "scope",
      className: "scope-dom",
      title: "数据权限",
      value: null,
      isSearch: false,
      isTable: true,
      isScope: false,
      options: [
        { value: "ALL", label: "全部数据", disabled: false },
        { value: "DEPT", label: "部门数据", disabled: false },
        { value: "DEPTS", label: "部门及以下", disabled: false },
        { value: "SELF", label: "仅本人", disabled: false },
        { value: "CUSTOM", label: "自定义", disabled: false },
      ],
      props: {
        multiple: false,
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true // 是否显示清空按钮
      },
      validate: [
        {
          trigger: "change",
          required: true,
          message: "数据权限不能为空"
        }
      ],
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "userDeptCascader",
      field: "deptIds",
      title: "数据权限部门",
      value: "",
      isSearch: false,
      isTable: false,
      isScope: false,
      options: [],
      props: {
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: {
        md: { span: 18 }
      },
      style: {
        width: '100%'
      }
    },
    {
      type: "select",
      field: "valid",
      className: "isValid-dom",
      title: "状态",
      isSearch: true,
      isSearchValidate: [],
      isSearchCol: { md: { span: 6 } },
      options: [
        { value: true, label: "正常", disabled: false },
        { value: false, label: "停用", disabled: false }
      ],
      isTable: true,
      col: { md: { span: 18 } },
      props: {
        multiple: false,
        placeholder: "请选择状态",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change',
          required: true,
          message: "请选择状态"
        }
      ],
    },
    {
      type: "uploader",
      title: "头像",
      field: "picUrl",
      isSearch: false,
      isHidden: false,
      isTable: false,
      col: {
        md: {
          span: 18
        }
      },
      props: {
        limit: 1,
        disabled: false,
        readonly: false,
        placeholder: "请输入",
        clearable: true
      }
    },
    {
      type: "input",
      field: "host",
      className: "host-dom", // 设置组件的class属性
      title: "host", // 组件的名称, 选填
      value: null,
      isSearch: false,
      isTable: true,
      props: {
        placeholder: "请输入ip",
        disabled: false,
        readonly: false,
        clearable: true
      },
      isScope: true,
      col: { md: { span: 18 } }
    },
  ]
}
