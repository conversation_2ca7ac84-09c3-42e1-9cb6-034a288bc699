/**
 * 应用图标工具函数
 * 用于统一管理应用图标的样式和颜色
 */

// 预定义的渐变色彩方案
const ICON_GRADIENTS = [
  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
  'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
  'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
  'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  'linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%)',
  'linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%)',
  'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
  'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)'
]

/**
 * 根据应用名称获取默认图标样式
 * @param {string} appName - 应用名称
 * @param {Object} options - 配置选项
 * @param {string} options.fontSize - 字体大小，默认 '20px'
 * @param {string} options.fontWeight - 字体粗细，默认 '600'
 * @param {string} options.color - 字体颜色，默认 '#fff'
 * @returns {Object} 样式对象
 */
export function getDefaultIconStyle(appName, options = {}) {
  const {
    fontSize = '20px',
    fontWeight = '600',
    color = '#fff'
  } = options

  // 根据应用名称的第一个字符计算颜色索引
  const colorIndex = (appName?.charCodeAt(0) || 0) % ICON_GRADIENTS.length
  
  return {
    background: ICON_GRADIENTS[colorIndex],
    color,
    fontWeight,
    fontSize
  }
}

/**
 * 获取应用图标的首字母
 * @param {string} appName - 应用名称
 * @returns {string} 首字母（大写）
 */
export function getAppIconLetter(appName) {
  return appName ? appName.charAt(0).toUpperCase() : 'A'
}

/**
 * 获取应用类型对应的标签类型
 * @param {string} appType - 应用类型
 * @returns {string} Element UI 标签类型
 */
export function getAppTypeTagType(appType) {
  const typeMap = {
    'PROXY': 'success',
    'FLOW': 'primary'
  }
  return typeMap[appType] || 'info'
}

/**
 * 获取应用类型对应的显示文本
 * @param {string} appType - 应用类型
 * @returns {string} 显示文本
 */
export function getAppTypeLabel(appType) {
  const typeMap = {
    'PROXY': '代理应用',
    'FLOW': '集成流'
  }
  return typeMap[appType] || appType
}

/**
 * 格式化日期时间
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(dateString) {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @param {Function} onSuccess - 成功回调
 * @param {Function} onError - 失败回调
 */
export function copyToClipboard(text, onSuccess, onError) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      onSuccess && onSuccess()
    }).catch(() => {
      fallbackCopyTextToClipboard(text, onSuccess, onError)
    })
  } else {
    fallbackCopyTextToClipboard(text, onSuccess, onError)
  }
}

/**
 * 备用复制方法（兼容旧浏览器）
 * @param {string} text - 要复制的文本
 * @param {Function} onSuccess - 成功回调
 * @param {Function} onError - 失败回调
 */
function fallbackCopyTextToClipboard(text, onSuccess, onError) {
  const textArea = document.createElement('textarea')
  textArea.value = text
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  try {
    document.execCommand('copy')
    onSuccess && onSuccess()
  } catch (err) {
    onError && onError(err)
  }
  document.body.removeChild(textArea)
}
