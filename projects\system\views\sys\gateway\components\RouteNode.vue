<template>
  <div class="route-node" :class="{ 'is-active': isActive }" @click="handleClick">
    <div class="route-node-header">
      <div class="route-info">
        <div class="node-icon">
          <i class="el-icon-guide"></i>
        </div>
        <div class="route-title">
          <div class="route-name">{{ route.name }}</div>
          <div class="route-id">ID: {{ route.id }}</div>
        </div>
      </div>
      <div class="route-actions">
        <el-tooltip content="编辑路由" placement="top">
          <el-button type="text" icon="el-icon-edit" @click.stop="$emit('edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除路由" placement="top">
          <el-button type="text" icon="el-icon-delete" @click.stop="$emit('delete')"></el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="route-content">
      <div class="route-item">
        <span class="label">URI:</span>
        <span class="value">{{ route.uri }}</span>
      </div>
      <div v-if="route.pattern" class="route-item">
        <span class="label">匹配路径:</span>
        <span class="value">{{ route.pattern }}</span>
      </div>
      <div class="route-stats">
        <div class="stat-item">
          <i class="el-icon-fork-spoon"></i>
          <span>断言: {{ predicatesCount }}</span>
        </div>
        <div class="stat-item">
          <i class="el-icon-magic-stick"></i>
          <span>过滤器: {{ filtersCount }}</span>
        </div>
        <div class="stat-item">
          <i class="el-icon-lock"></i>
          <span>安全配置: {{ securityCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RouteNode',
  props: {
    route: {
      type: Object,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    },
    predicatesCount: {
      type: Number,
      default: 0
    },
    filtersCount: {
      type: Number,
      default: 0
    },
    securityCount: {
      type: Number,
      default: 0
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.route);
    }
  }
}
</script>

<style lang="scss" scoped>
.route-node {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  
  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
  }
  
  &.is-active {
    border-left: 4px solid #409EFF;
    background-color: #EDF5FF;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      border-width: 0 24px 24px 0;
      border-style: solid;
      border-color: #409EFF #fff;
    }
  }
  
  .route-node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .route-info {
      display: flex;
      align-items: center;
      
      .node-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: linear-gradient(135deg, #409EFF, #64B5F6);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 12px;
        
        i {
          color: #fff;
          font-size: 20px;
        }
      }
      
      .route-title {
        .route-name {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .route-id {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .route-actions {
      display: flex;
      
      .el-button {
        font-size: 18px;
        padding: 6px;
        
        &:hover {
          color: #409EFF;
        }
        
        &:last-child:hover {
          color: #F56C6C;
        }
      }
    }
  }
  
  .route-content {
    .route-item {
      margin-bottom: 8px;
      display: flex;
      font-size: 14px;
      
      .label {
        color: #606266;
        min-width: 80px;
      }
      
      .value {
        color: #303133;
        word-break: break-all;
        flex: 1;
      }
    }
    
    .route-stats {
      margin-top: 16px;
      display: flex;
      justify-content: space-between;
      border-top: 1px dashed #EBEEF5;
      padding-top: 12px;
      
      .stat-item {
        display: flex;
        align-items: center;
        
        i {
          margin-right: 4px;
          color: #909399;
        }
        
        span {
          font-size: 13px;
          color: #606266;
        }
      }
    }
  }
}
</style> 