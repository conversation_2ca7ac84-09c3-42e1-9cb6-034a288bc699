<template>
  <div class="datasource-container">
    <div class="page-header">
      <div class="header-title">
        <h2>数据源管理</h2>
      </div>
    </div>
    <div class="table-toolbar">
      <div class="filter-container">
        <div class="unified-search">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入数据源名称或JDBC URL"
            prefix-icon="el-icon-search"
            clearable
            class="search-input"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>

          <el-select 
            v-model="queryParams.groupName" 
            placeholder="分组"
            clearable
            class="status-select"
            @change="handleSearch"
          >
            <el-option
              v-for="group in groupList"
              :key="group"
              :label="group"
              :value="group"
            >
              <i class="el-icon-folder-opened" style="color: #409EFF" />
              <span style="margin-left: 8px">{{ group }}</span>
            </el-option>
          </el-select>

          <el-select 
            v-model="queryParams.type" 
            placeholder="数据库类型"
            clearable
            class="status-select"
            @change="handleSearch"
          >
            <el-option
              v-for="type in dbTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            >
              <i class="el-icon-connection" style="color: #409EFF" />
              <span style="margin-left: 8px">{{ type.label }}</span>
            </el-option>
          </el-select>
        </div>
      </div>

      <div class="button-group">
        <el-button type="primary" icon="el-icon-plus" @click="$router.push('/data/datasource/add')">
          新增数据源
        </el-button>
      </div>
    </div>

    <div class="table-wrapper">
      <div class="statistics-bar">
        <span class="stat-item">
          <i class="el-icon-connection"></i>
          总数据源: <span class="stat-count">{{ total }}</span>
        </span>
      </div>
      
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        fit
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column
          prop="name"
          label="数据源名称"
          min-width="150"
        >
          <template slot-scope="scope">
            <div class="datasource-cell">
              <i class="el-icon-connection" />
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="type"
          label="类型"
          width="130"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag :type="getTagType(scope.row.type)" effect="dark">
              {{ getDbTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="description"
          label="描述"
          show-overflow-tooltip
          min-width="180"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.description && scope.row.description.trim()">{{ scope.row.description }}</span>
            <span v-else class="no-desc">暂无描述</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="jdbcUrl"
          label="JDBC URL"
          show-overflow-tooltip
          min-width="180"
        />

        <el-table-column
          prop="username"
          label="用户名"
          width="120"
        />

        <el-table-column
          prop="code"
          label="数据源编码"
          min-width="120"
        />

        <el-table-column
          prop="groupName"
          label="数据源分组"
          width="120"
        />

        <el-table-column
          label="状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabled"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-text="scope.row.enabled ? '' : ''"
              :inactive-text="scope.row.enabled ? '' : ''"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          min-width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              type="text" 
              icon="el-icon-connection"
              @click="handleTestConnection(scope.row)"
              :loading="scope.row.testing"
              class="connection-btn"
            >
              测试连接
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getDataSourceList, testDataSourceConnection, deleteDataSource, getDataSourceGroups, getSupportedDataSourceTypes, updateDataSourceStatus, updateDataSource } from '@system/api/data-manger/datasource'

export default {
  name: 'DatasourceList',
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      queryParams: {
        keyword: '',
        groupName: '',
        type: ''
      },
      dbTypes: [
        { label: 'MySQL', value: 'MYSQL' },
        { label: 'Oracle', value: 'ORACLE' },
        { label: 'PostgreSQL', value: 'POSTGRESQL' }
      ],
      groupList: [],
      supportedTypesMap: {}
    }
  },
  created() {
    this.fetchSupportedTypes().then(() => {
      this.fetchGroupList();
      this.fetchData();
    });
  },
  methods: {
    async fetchSupportedTypes() {
      try {
        const res = await getSupportedDataSourceTypes();
        if (res && Array.isArray(res)) {
          // 创建一个map用于快速查找
          this.supportedTypesMap = res.reduce((acc, type) => {
            acc[type.code.toUpperCase()] = type;
            return acc;
          }, {});
          
          // 更新dbTypes用于筛选选项
          this.dbTypes = res.map(type => ({
            label: type.name,
            value: type.code.toUpperCase()
          }));
        }
      } catch (error) {
        console.error('获取支持的数据源类型失败:', error);
      }
    },

    async fetchData() {
      this.loading = true
      try {
        // 构建查询参数，过滤掉空值
        const params = {
          current: this.currentPage,
          size: this.pageSize
        }
        
        // 只有在有值的情况下才添加查询参数
        if (this.queryParams.keyword) {
          params.keyword = this.queryParams.keyword
        }
        if (this.queryParams.groupName) {
          params.groupName = this.queryParams.groupName
        }
        if (this.queryParams.type) {
          params.type = this.queryParams.type
        }
        
        const data = await getDataSourceList(params)
        this.tableData = data.records
        this.total = data.total
        
        // 更新分组列表
        const groups = new Set(data.records.map(item => item.groupName).filter(Boolean))
        if(groups.size > 0) {
          this.groupList = Array.from(groups)
        }
      } catch (error) {
        this.$message.error('获取数据源列表失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    handleEdit(row) {
      this.$router.push(`/data/datasource/edit/${row.id}`)
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该数据源吗？删除后不可恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const res = await deleteDataSource(row.id)
        this.$message.success('删除成功')
        this.fetchData() // 刷新列表
      } catch (error) {
        if(error !== 'cancel') { // 排除取消操作的情况
          this.$message.error('删除失败：' + (error.message || '未知错误'))
        }
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    async handleTestConnection(row) {
      this.$set(row, 'testing', true)
      
      try {
        const data = {
          id: row.id,
          type: row.type,
          name: row.name,
          code: row.code,
          description: row.description,
          host: row.host,
          port: row.port,
          serverName: row.serverName,
          defaultSchema: row.defaultSchema,
          arg: row.arg,
          username: row.username,
          password: row.password,
          driverClassName: row.driverClassName,
          groupName: row.groupName,
          enabled: row.enabled
        }
        
        const res = await testDataSourceConnection(data)
        this.$message.success('连接成功')
      } catch (error) {
        this.$message.error(error.message || '连接失败')
      } finally {
        this.$set(row, 'testing', false)
      }
    },
    handleSearch() {
      this.currentPage = 1
      this.fetchData()
    },
    handleReset() {
      this.queryParams = {
        keyword: '',
        groupName: '',
        type: ''
      }
      this.handleSearch()
    },
    getTagType(type) {
      // 如果在supportedTypesMap中有对应的类型，就使用预定义的映射
      if (this.supportedTypesMap[type]) {
        const typeCode = type.toLowerCase();
        
        if (typeCode.includes('mysql')) {
          return 'primary';
        } else if (typeCode.includes('oracle')) {
          return 'warning';
        } else if (typeCode.includes('postgresql')) {
          return 'success';
        } else if (typeCode.includes('transwarp')) {
          return 'info';
        }
      }
      
      // 兼容原有逻辑
      const typeMap = {
        'MYSQL': 'primary',
        'ORACLE': 'warning',
        'POSTGRESQL': 'success'
      }
      return typeMap[type] || 'info'
    },
    async fetchGroupList() {
      try {
        const data = await getDataSourceGroups("groupName")
        this.groupList = data || ['默认分组']
      } catch (error) {
        this.$message.error('获取分组列表失败：' + (error.message || '未知错误'))
        this.groupList = ['默认分组']
      }
    },
    // 获取数据库类型的显示名称
    getDbTypeName(code) {
      if (this.supportedTypesMap[code]) {
        return this.supportedTypesMap[code].name;
      }
      return code;
    },
    async handleStatusChange(row) {
      try {
        await this.$confirm(
          `确认${row.enabled ? '启用' : '停用'}该数据源吗？`, 
          '提示', 
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: row.enabled ? 'success' : 'warning'
          }
        )
        
        // 使用updateDataSource API更新状态
        await updateDataSource({
          id: row.id,
          enabled: row.enabled
        })
        this.$message.success(`${row.enabled ? '启用' : '停用'}成功`)
      } catch (error) {
        if(error !== 'cancel') {
          this.$message.error(`操作失败：${error.message || '未知错误'}`)
          // 恢复开关状态
          row.enabled = !row.enabled
        } else {
          // 取消操作时也需要恢复状态
          row.enabled = !row.enabled
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.datasource-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }
  }

  .table-toolbar {
    margin-bottom: 24px;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .filter-container {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;

      .unified-search {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .search-input {
          width: 330px;
          ::v-deep {
            .el-input__inner {
              height: 42px;
              line-height: 42px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 42px;
              border-top-left-radius: 10px;
              border-bottom-left-radius: 10px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 14px;
              .el-icon-search {
                font-size: 18px;
                line-height: 42px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 10px;
              border-bottom-right-radius: 10px;

              .el-button {
                margin: 0;
                height: 40px;
                border: none;
                padding: 0 20px;
                border-radius: 0 10px 10px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
          }
        }

        .status-select {
          width: 120px;
          ::v-deep {
            .el-input__inner {
              height: 42px;
              line-height: 42px;
              border-radius: 10px;
              background: #f9fafc;
              border: 1px solid #e0e5ee;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }
          }
        }
      }
    }

    .button-group {
      display: flex;
      gap: 12px;
      
      .el-button {
        padding: 9px 18px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        border-radius: 10px;
        background-color: #409EFF;
        border-color: #409EFF;
        overflow: hidden;
        z-index: 1;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
          z-index: -1;
        }

        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }

        i {
          margin-right: 6px;
          font-size: 16px;
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    .statistics-bar {
      background: linear-gradient(to right, #f5f7fa, #f9fafc);
      padding: 14px 20px;
      margin-bottom: 16px;
      border-radius: 10px;
      border: 1px solid #eef1f7;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .stat-item {
        color: #606266;
        font-size: 14px;
        
        i {
          color: #409EFF;
          margin-right: 6px;
        }

        .stat-count {
          font-weight: 600;
          color: #409EFF;
          margin-left: 4px;
        }
      }
    }

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }

      .datasource-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #409EFF;
          font-size: 16px;
        }
        
        span {
          color: #1a1f36;
          font-weight: 500;
        }
      }

      .no-desc {
        color: #909399;
        font-style: italic;
      }

      .delete-btn {
        color: #F56C6C;
        
        &:hover {
          color: #f78989;
        }
      }

      .connection-btn {
        color: #67C23A;
        
        &:hover {
          color: #85ce61;
        }
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;
      
      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;
        
        .btn-prev, 
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;
          
          &:not(.disabled):hover {
            border-color: #409EFF;
          }
          
          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }
        
        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }
        
        .el-pagination__jump {
          margin-left: 16px;
        }
        
        .el-select .el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }
        
        .el-pagination__editor.el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }

  ::v-deep .el-tag {
    text-transform: capitalize;
    
    &.el-tag--primary {
      background-color: #409EFF;
      border-color: #409EFF;
    }
    
    &.el-tag--success {
      background-color: #67C23A;
      border-color: #67C23A;
    }
    
    &.el-tag--warning {
      background-color: #E6A23C;
      border-color: #E6A23C;
    }

    &.el-tag--danger {
      background-color: #F56C6C;
      border-color: #F56C6C;
    }
  }
}
</style> 