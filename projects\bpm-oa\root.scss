 section.el-container {
     .w-form-icon {
         width: 30px !important;
         height: 30px !important;
         border-radius: 15px !important;
         font-size: 20px !important;
     }
 }

 section.app-main {

     >div {
         margin: 24px;
         padding: 20px !important;
         background: white;
         border-radius: 10px;


         .fixed-search {
             right: 320px;
         }

         .w-form-icon {
             width: 30px !important;
             height: 30px !important;
             border-radius: 15px !important;
             font-size: 20px !important;
         }
     }

     >div.from-panel {

         .form-group-item {
             height: auto;
             min-height: 30px;


             .el-badge__content.el-badge__content--warning {
                 top: 10%;
             }
         }
     }
 }

 .preview .actions {
     height: 60px !important;
 }

 .a-img {

     >div {
         --size: 38px !important
     }

     >.el-avatar {
         width: 38px !important;
         height: 38px !important;
         line-height: 38px !important;
     }
 }

 .add-user {
     >.el-icon-plus {
         padding: 6px !important;
     }
 }