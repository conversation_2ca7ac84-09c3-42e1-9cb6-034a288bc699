#app {

  //顶部导航栏颜色
  .tags-view-wrapper {
    background-color: #ffffff;
  }

  .navbar {
    background: #ffffff;
  }

  //侧边导航栏顶上颜色
  .sidebar-logo-container {
    background-color: rgba(255, 255, 255, 1);
  }

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 120px);
      }
    }

    &.has-logo.has-search {
      .el-scrollbar {
        height: calc(100% - 160px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      padding-left: 20px;
      padding-right: 20px;
      background-color: transparent !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      min-width: auto !important;
      background-color: $subMenuBg !important;
      color: rgba(85, 100, 125, 1) !important;
      border-radius: 8px;

      i {
        color: rgba(85, 100, 125, 1) !important;
      }

      &:hover {
        background-color: $menuHover !important;
        border-radius: 8px;
      }
    }

    .is-active>.el-submenu__title {
      // color: $subMenuActiveText !important;
      // background-color: rgba(67, 101, 220, 1) !important;
      // border-radius: 8px;
      // color: white !important;

      // i {
      // 	color: white !important;
      // }
    }


    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: auto !important;
      background-color: $subMenuBg !important;
      color: rgba(151, 159, 173, 1) !important;
      padding-left: 20px !important;
      border-radius: 8px;

      i {
        color: rgba(85, 100, 125, 1) !important;
      }

      &:hover {
        border-radius: 8px;
        background-color: $subMenuHover !important;
      }
    }

    .is-active.el-menu-item {
      border-radius: 8px;
      // background-color: rgba(67, 101, 220, 0.15) !important;
      // color: rgba(67, 101, 220, 1) !important;
      background-color: rgba(67, 101, 220, 1) !important;
      color: white !important;

      &:hover {
        border-radius: 8px;
        background-color: rgba(67, 101, 220, 1) !important;
        color: white !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;

      .el-submenu {
        overflow: hidden;

        &>.el-submenu__title {
          padding: 0 !important;

          .svg-icon {
            margin-left: 20px;
          }

          .el-submenu__icon-arrow {
            display: none;
          }
        }
      }

      .el-menu--collapse {
        padding-left: 0;
        padding-right: 0;

        .el-submenu {
          &>.el-submenu__title {
            &>span {
              height: 0;
              width: 0;
              overflow: hidden;
              visibility: hidden;
              display: inline-block;
            }
          }
        }

        .is-active>.el-submenu__title {
          background-color: rgba(67, 101, 220, 1) !important;
          border-radius: 8px;
          color: white !important;

        }
      }
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }


  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }


  // when menu collapsed
  .el-menu--vertical {
    background-color: $menuBg !important;

    &>.el-menu {
      .svg-icon {
        margin-right: 16px;
      }
    }

    .nest-menu .el-submenu>.el-submenu__title,
    .el-menu-item {
      background-color: $subMenuBg !important;
      color: rgba(151, 159, 173, 1) !important;

      &:hover {
        background-color: $menuHover !important;
      }
    }

    .is-active.el-menu-item {
      background-color: rgba(67, 101, 220, 1) !important;
      color: white !important;
    }

    // the scroll bar appears when the subMenu is too long
    >.el-menu--popup {
      max-height: 100vh;
      overflow-y: auto;
      padding: 0;
      background-color: transparent !important;
      margin-right: 0 !important;

      &::-webkit-scrollbar-track-piece {
        background: #d3dce6;
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #99a9bf;
        border-radius: 20px;
      }
    }
  }
}