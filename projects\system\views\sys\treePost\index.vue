<template>
  <div class="post-management">
    <!-- 左侧职位树 -->
    <div class="post-tree">
      <div class="tree-header">
        <div class="header-title">
          <span>职位列表</span>
          <el-button type="primary" size="small" @click="handleAdd">
            <i class="el-icon-plus"></i> 新增职位
          </el-button>
        </div>
        <div class="search-box">
          <el-input v-model="searchKeyword" placeholder="搜索职位名称/编码" prefix-icon="el-icon-search" clearable size="small"
            @clear="handleSearchClear" />
        </div>
      </div>
      <div class="tree-container">
        <el-tree ref="postTree" :data="filteredPostList" :props="defaultProps" :filter-node-method="filterNode"
          node-key="id" highlight-current @node-click="handleNodeClick">
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <div class="node-content">
              <i class="el-icon-user"></i>
              <span :class="{ 'highlight': isHighlighted(node, searchKeyword) }">
                {{ node.label }}
              </span>
            </div>
            <div class="node-actions">
              <el-tooltip content="编辑" placement="top">
                <el-button size="mini" type="text" @click.stop="handleEdit(data)">
                  <i class="el-icon-edit"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button size="mini" type="text" class="danger" @click.stop="handleDelete(data)">
                  <i class="el-icon-delete"></i>
                </el-button>
              </el-tooltip>
            </div>
          </span>
        </el-tree>
      </div>
    </div>

    <!-- 右侧职位详情 -->
    <div class="post-detail" v-if="currentPost">
      <div class="detail-content">
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-info"></i>
            <span>基本信息</span>
            <div class="section-actions">
              <el-button type="primary" size="small" icon="el-icon-edit" class="action-button"
                @click="handleEdit(currentPost)">
                编辑职位
              </el-button>
              <el-button type="danger" size="small" icon="el-icon-delete" class="action-button"
                @click="handleDelete(currentPost)">
                删除职位
              </el-button>
            </div>
          </div>
          <div class="info-list">
            <div class="info-item">
              <label>职位名称：</label>
              <div class="value-with-icon">
                <i class="el-icon-user" style="color: #409EFF;"></i>
                <span>{{ currentPost.name }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>职位编码：</label>
              <div class="value-with-icon">
                <i class="el-icon-collection-tag" style="color: #67C23A;"></i>
                <span>{{ currentPost.code }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>排序号：</label>
              <div class="value-with-icon">
                <i class="el-icon-sort" style="color: #E6A23C;"></i>
                <span>{{ currentPost.sort }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>状态：</label>
              <div class="value-with-icon">
                <i class="el-icon-circle-check" :style="{ color: currentPost.enabled ? '#67C23A' : '#909399' }"></i>
                <span>{{ currentPost.enabled ? '启用' : '禁用' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>上级职位：</label>
              <div class="value-with-icon">
                <i class="el-icon-user"></i>
                <span>{{ getParentPostName(currentPost.parentId) || '无' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <div class="value-with-icon">
                <i class="el-icon-time"></i>
                <span>{{ currentPost.createTime }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>更新时间：</label>
              <div class="value-with-icon">
                <i class="el-icon-time"></i>
                <span>{{ currentPost.updateTime }}</span>
              </div>
            </div>
            <div class="info-item full">
              <label>备注说明：</label>
              <div class="value-with-icon">
                <i class="el-icon-document"></i>
                <span>{{ currentPost.remark || '暂无描述' }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-user"></i>
            <span>下属职位</span>
            <span class="member-count" v-if="currentPost.children">
              共 {{ currentPost.children.length }} 个
            </span>
          </div>
          <el-table :data="paginatedChildren" style="width: 100%" border stripe highlight-current-row
            height="calc(100% - 110px)" class="custom-table">
            <el-table-column prop="code" label="职位编码" :width=rpx(100) align="center">
              <template slot-scope="{ row }">
                <el-tag size="medium" effect="plain">{{ row.code }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="职位名称" min-width="120">
              <template slot-scope="{ row }">
                <div class="post-info">
                  <i class="el-icon-user"></i>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="sort" label="排序号" width="100" align="center" />
            <el-table-column prop="enabled" label="状态" width="100" align="center">
              <template slot-scope="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'info'" effect="plain">
                  {{ row.enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center" class-name="operation-column">
              <template slot-scope="{ row }">
                <el-tooltip content="编辑" placement="top">
                  <el-button size="mini" type="text" @click="handleEdit(row)">
                    <i class="el-icon-edit"></i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button size="mini" type="text" class="danger" @click="handleDelete(row)">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="currentPost.children ? currentPost.children.length : 0" background>
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑职位对话框 -->
    <el-dialog :title="dialogType === 'add' ? '新增职位' : '编辑职位'" :visible.sync="dialogVisible" width="550px"
      :close-on-click-modal="false" :append-to-body="true" custom-class="post-dialog">
      <el-form ref="postForm" :model="postForm" :rules="formRules" label-width="100px" class="post-form">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-form-item label="职位名称" prop="name">
            <el-input v-model="postForm.name" placeholder="请输入职位名称" class="custom-input">
              <template slot="prepend">
                <i class="el-icon-user"></i>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="职位编码" prop="code">
            <el-input v-model="postForm.code" placeholder="请输入职位编码" class="custom-input">
              <template slot="prepend">
                <i class="el-icon-collection-tag"></i>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="上级职位" prop="parentId">
            <el-cascader v-model="postForm.parentId" :options="disabledCurrentPostTree" :props="{
              checkStrictly: true,
              label: 'name',
              value: 'id',
              emitPath: false
            }" placeholder="请选择上级职位" clearable class="custom-cascader">
              <template slot-scope="{ node, data }">
                <i class="el-icon-user"></i>
                <span>{{ data.name }}</span>
                <span class="post-code">({{ data.code }})</span>
              </template>
            </el-cascader>
          </el-form-item>

          <el-form-item label="排序号" prop="sort">
            <el-input-number v-model="postForm.sort" :min="0" :max="999" controls-position="right"
              class="custom-number-input" />
          </el-form-item>

          <el-form-item label="状态" prop="enabled">
            <el-switch v-model="postForm.enabled" active-color="#13ce66" inactive-color="#ff4949"
              :active-value="true" :inactive-value="false">
            </el-switch>
          </el-form-item>

          <el-form-item label="备注说明" prop="remark">
            <el-input v-model="postForm.remark" type="textarea" :rows="3" placeholder="请输入备注说明"
              class="custom-textarea" />
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPostTreeList, getPostInfo, addPost, updatePost, deletePost } from '@system/api/sys/treePost'

export default {
  name: 'PostManagement',
  data() {
    return {
      searchKeyword: '',
      postList: [],
      currentPost: null,
      dialogVisible: false,
      dialogType: 'add',
      postForm: {
        name: '',
        code: '',
        parentId: null,
        sort: 0,
        enabled: true,
        remark: ''
      },
      formRules: {
        name: [
          { required: true, message: '请输入职位名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入职位编码', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },
      submitLoading: false,
      expandedKeys: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      currentPage: 1,
      pageSize: 10
    }
  },
  computed: {
    paginatedChildren() {
      if (!this.currentPost || !this.currentPost.children) {
        return [];
      }
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.currentPost.children.slice(start, end);
    },
    filteredPostList() {
      if (!this.searchKeyword) {
        return this.postList
      }
      const keyword = this.searchKeyword.toLowerCase()
      return this.filterTreeData(this.postList, (node) =>
        node.name.toLowerCase().includes(keyword) ||
        node.code.toLowerCase().includes(keyword)
      )
    },
    disabledCurrentPostTree() {
      if (this.dialogType !== 'edit' || !this.postForm.id) {
        return this.postList
      }
      
      // 深拷贝树数据
      const cloneData = JSON.parse(JSON.stringify(this.postList))
      
      // 递归禁用当前职位及其子职位
      const disableCurrentAndChildren = (nodes, targetId) => {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === targetId) {
            nodes[i].disabled = true
            return true
          }
          
          if (nodes[i].children && nodes[i].children.length > 0) {
            const found = disableCurrentAndChildren(nodes[i].children, targetId)
            if (found) {
              return true
            }
          }
        }
        return false
      }
      
      // 查找并标记当前职位节点为disabled
      disableCurrentAndChildren(cloneData, this.postForm.id)
      
      // 递归禁用当前职位的所有子职位
      const disableChildrenNodes = (nodes, parentId) => {
        const parent = this.findPostById(this.postList, parentId)
        if (!parent || !parent.children) return
        
        // 为每个子职位禁用
        const allChildren = this.getAllChildrenIds(parent)
        
        const recursiveDisable = (treeNodes, disabledIds) => {
          for (let i = 0; i < treeNodes.length; i++) {
            if (disabledIds.includes(treeNodes[i].id)) {
              treeNodes[i].disabled = true
            }
            
            if (treeNodes[i].children && treeNodes[i].children.length > 0) {
              recursiveDisable(treeNodes[i].children, disabledIds)
            }
          }
        }
        
        recursiveDisable(cloneData, allChildren)
      }
      
      // 禁用当前职位的所有子职位
      disableChildrenNodes(cloneData, this.postForm.id)
      
      return cloneData
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    // 获取职位列表
    async getList() {
      const data = await getPostTreeList({ filterEnabled: false })
      this.postList = data || []

      // 如果有数据，选中第一个职位
      if (this.postList.length > 0 && !this.currentPost) {
        this.$nextTick(() => {
          this.handleNodeClick(this.postList[0])
        })
      }
    },

    // 清除搜索
    handleSearchClear() {
      this.searchKeyword = ''
    },

    // 选中当前行
    handleNodeClick(node) {
      if (node) {
        this.currentPost = node
      }
    },

    // 新增职位
    handleAdd() {
      this.dialogType = 'add'
      this.postForm = {
        name: '',
        code: '',
        parentId: null,
        sort: 0,
        enabled: true,
        remark: ''
      }
      this.dialogVisible = true
    },

    // 编辑职位
    handleEdit(data) {
      this.dialogType = 'edit'
      this.postForm = {
        id: data.id,
        name: data.name,
        code: data.code,
        parentId: data.parentId,
        sort: data.sort,
        enabled: data.enabled,
        remark: data.remark
      }
      this.dialogVisible = true
    },

    // 删除职位
    handleDelete(data) {
      this.$confirm('确定要删除该职位吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deletePost(data.id)
        this.$message.success('删除成功')

        // 如果删除的是当前选中的职位，清空当前职位
        if (this.currentPost && this.currentPost.id === data.id) {
          this.currentPost = null
        }

        // 刷新职位列表
        this.getList()
      })
    },

    // 提交表单
    async handleSubmit() {
      await this.$refs.postForm.validate()

      this.submitLoading = true
      const submitData = {
        ...this.postForm
      }

      try {
        if (this.dialogType === 'add') {
          await addPost(submitData)
          this.$message.success('新增职位成功')
        } else {
          await updatePost(submitData)
          this.$message.success('更新职位成功')
        }

        this.dialogVisible = false
        // 刷新职位列表
        await this.getList()
      } finally {
        this.submitLoading = false
      }
    },

    // 根据ID查找职位
    findPostById(posts, id) {
      for (const post of posts) {
        if (post.id === id) {
          return post
        }
        if (post.children && post.children.length > 0) {
          const found = this.findPostById(post.children, id)
          if (found) return found
        }
      }
      return null
    },

    // 获取所有子职位ID
    getAllChildrenIds(post) {
      const ids = []
      
      const collectIds = (node) => {
        if (!node) return
        ids.push(node.id)
        
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            collectIds(child)
          })
        }
      }
      
      if (post.children && post.children.length > 0) {
        post.children.forEach(child => {
          collectIds(child)
        })
      }
      
      return ids
    },

    // 获取上级职位名称
    getParentPostName(parentId) {
      if (!parentId) return null
      const parent = this.findPostById(this.postList, parentId)
      return parent ? parent.name : null
    },

    // 过滤树数据
    filterTreeData(data, predicate) {
      // 深拷贝数据
      const cloneData = JSON.parse(JSON.stringify(data))
      
      // 递归过滤
      const filterNode = (nodes) => {
        return nodes.filter(node => {
          // 如果当前职位匹配条件
          if (predicate(node)) {
            return true
          }
          
          // 如果有子职位，递归过滤子职位
          if (node.children && node.children.length) {
            const filteredChildren = filterNode(node.children)
            if (filteredChildren.length) {
              node.children = filteredChildren
              return true
            }
          }
          
          return false
        })
      }
      
      return filterNode(cloneData)
    },

    // 判断职位是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false
      const label = node.label.toLowerCase()
      return label.includes(keyword.toLowerCase())
    },

    // 过滤职位
    filterNode(value, data) {
      if (!this.searchKeyword) return true
      const keyword = this.searchKeyword.toLowerCase()
      const label = data.name.toLowerCase()
      return label.includes(keyword) || data.code.toLowerCase().includes(keyword)
    }
  }
}
</script>

<style lang="scss" scoped>
.post-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  gap: 24px;
  overflow: hidden;

  .post-tree {
    width: 280px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

    .tree-header {
      padding: 20px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .header-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        span {
          font-size: 18px;
          font-weight: 600;
          color: #1a1f36;
          letter-spacing: 0.5px;
          position: relative;
          padding-left: 12px;

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: linear-gradient(to bottom, #409EFF, #64B5F6);
            border-radius: 3px;
          }
        }

        .el-button {
          padding: 9px 18px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 10px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            transform: translateY(-2px);
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }

      .search-box {
        .el-input {
          width: 100%;

          ::v-deep .el-input__inner {
            border-radius: 10px;
            height: 36px;
            background: #f7f9fc;
            border: 1px solid #e7ebf3;

            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }

            &:hover {
              border-color: #c0d0e9;
            }
          }

          ::v-deep .el-input__prefix {
            left: 10px;

            i {
              color: #8492a6;
            }
          }
        }
      }
    }

    .tree-container {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #fff;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      ::v-deep .el-tree {
        background: transparent;

        .el-tree-node__content {
          height: 32px;
          border-radius: 8px;
          margin: 2px 0;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f5f7fa;
          }

          .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;

            .node-content {
              display: flex;
              align-items: center;
              gap: 8px;

              i {
                font-size: 14px;
              }

              span {
                font-size: 14px;
                color: #1a1f36;
              }
            }

            .node-actions {
              display: none;
              gap: 2px;

              .el-button {
                padding: 1px 2px;
                margin: 0;

                i {
                  font-size: 13px;
                  margin: 0;
                }

                &.danger {
                  color: #F56C6C;
                }
              }
            }
          }
        }

        .el-tree-node.is-current>.el-tree-node__content {
          background-color: #ecf5ff !important;
          color: #409EFF;
          font-weight: 500;
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);

          .node-content {
            span {
              color: #409EFF;
            }
          }

          .node-actions {
            display: flex;
          }
        }
      }
    }
  }

  .post-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;
    padding: 0;
    height: 100%;

    .detail-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: 100%;

      .info-section {
        background-color: #fff;
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

        &:hover {
          box-shadow: 0 6px 24px rgba(31, 45, 61, 0.1);
        }

        &:first-child {
          flex-shrink: 0;
          height: 220px;
        }

        &:last-child {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          height: calc(100% - 236px);

          .el-table {
            flex: 1;
            overflow: hidden;
          }
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;

          i {
            margin-right: 8px;
            color: #409EFF;
          }

          .section-actions {
            margin-left: auto;
            display: flex;
            gap: 12px;

            .action-button {
              padding: 6px 12px;
              font-weight: 500;
              transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
              border-radius: 8px;

              &.el-button--primary {
                background-color: #409EFF;
                border-color: #409EFF;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);

                &:hover {
                  transform: translateY(-2px);
                  background-color: #5aacff;
                  border-color: #5aacff;
                  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                }
              }

              &.el-button--danger {
                background-color: #F56C6C;
                border-color: #F56C6C;
                box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);

                &:hover {
                  transform: translateY(-2px);
                  background-color: #f78989;
                  border-color: #f78989;
                  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
                }
              }

              i {
                margin-right: 4px;
                font-size: 14px;
              }
            }
          }
        }

        .info-list {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .info-item {
            display: flex;
            align-items: center;

            &.full {
              grid-column: span 2;
            }

            label {
              width: 90px;
              color: #606266;
              font-weight: 500;
            }

            .value-with-icon {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 6px;

              i {
                font-size: 14px;
                color: #409EFF;
                opacity: 0.8;
              }

              span {
                color: #2c3e50;
                line-height: 1.4;
              }
            }
          }
        }
      }
    }
  }
}

/* 弹出框样式 */
::v-deep .post-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    background: #f8f9fb;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-button {
      border-radius: 10px;
      padding: 10px 24px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);

        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

.post-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          color: #606266;
          font-weight: 500;
        }

        .el-input__inner,
        .el-textarea__inner {
          border-radius: 10px;
          border: 1px solid #e0e5ee;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }

          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-input-group__prepend {
          background: #f7f9fc;
          border: 1px solid #e0e5ee;
          border-right: none;
          border-radius: 10px 0 0 10px;
          padding: 0 12px;

          i {
            color: #409EFF;
          }
        }

        .el-switch {
          .el-switch__core {
            border-radius: 100px;
            height: 24px;
            width: 48px !important;

            &:after {
              height: 20px;
              width: 20px;
              top: 1px;
            }
          }

          &.is-checked {
            .el-switch__core::after {
              margin-left: -21px;
            }
          }
        }
      }
    }
  }
}

// 添加空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #dcdfe6;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

// 修改表格和分页样式
.custom-table {
  &.el-table--border {
    border-radius: 12px;
    overflow: hidden;
    margin-top: 12px;
  }

  ::v-deep {
    th {
      background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
      font-weight: 600;
      color: #1a1f36;
      height: 40px;
      padding: 8px 0;
    }

    td {
      padding: 6px 0;
    }

    .post-info {
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        color: #409EFF;
        font-size: 14px;
      }
    }

    .el-tag {
      border-radius: 12px;
      padding: 0 10px;
      height: 22px;
      line-height: 20px;
    }

    .operation-column {
      .cell {
        white-space: nowrap;
        display: flex;
        justify-content: center;
        gap: 4px;

        .el-button {
          padding: 2px 4px;
          font-size: 12px;
          margin: 0;
          height: 24px;
          line-height: 1;
          display: inline-flex;
          align-items: center;

          i {
            margin-right: 0;
            font-size: 13px;
          }

          &.danger {
            color: #F56C6C;
          }
        }
      }
    }

    .el-table__body-wrapper::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(144, 147, 153, 0.3);

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }

    .el-table__body-wrapper::-webkit-scrollbar-track {
      background: transparent;
    }
  }
}

.pagination-container {
  padding: 16px 0 0;
  display: flex;
  justify-content: flex-end;
  background: #fff;
  border-top: 1px solid #f0f2f5;

  ::v-deep .el-pagination {
    padding: 0;
    font-weight: normal;

    .btn-prev,
    .btn-next,
    .el-pager li {
      background: transparent;
      border: 1px solid #e0e5ee;

      &:hover:not(.disabled) {
        border-color: #409EFF;
      }

      &.active {
        background: #409EFF;
        border-color: #409EFF;
        color: #fff;
      }
    }

    .el-pagination__sizes {
      margin-right: 15px;

      .el-input__inner {
        border-radius: 4px;
        border-color: #e0e5ee;

        &:hover {
          border-color: #c0d0e9;
        }
      }
    }

    .el-pagination__total {
      margin-right: 15px;
    }

    .el-pagination__jump {
      margin-left: 15px;

      .el-input__inner {
        border-radius: 4px;
        border-color: #e0e5ee;

        &:hover {
          border-color: #c0d0e9;
        }
      }
    }
  }
}
</style> 