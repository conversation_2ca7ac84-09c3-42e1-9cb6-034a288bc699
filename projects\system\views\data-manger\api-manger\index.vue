<template>
  <div class="api-management">
    <el-container>
      <!-- 左侧API树 -->
      <div class="api-tree-wrapper">
        <el-aside width="300px" class="api-tree-container" id="resizable">
          <div class="tree-header">
            <div class="header-title">
              <span>API列表</span>
              <el-dropdown @command="handleAdd" trigger="click">
                <el-button type="primary" size="small" class="add-button">
                  <i class="el-icon-plus"></i>
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="api">
                    <i class="el-icon-connection"></i> 新增API
                  </el-dropdown-item>
                  <el-dropdown-item command="group">
                    <i class="el-icon-folder-add"></i> 新增文件夹
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
            <div class="search-box">
              <el-input v-model="searchQuery" placeholder="搜索API..." prefix-icon="el-icon-search" clearable size="small"
                @clear="handleSearchClear" />
            </div>
          </div>
          <div class="tree-container">
            <el-tree ref="apiTree" :data="treeData" :props="defaultProps" :filter-node-method="filterNode" node-key="id"
              highlight-current @node-click="handleNodeClick" draggable :allow-drop="allowDrop" @node-drop="handleDrop">
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span class="node-content">
                  <i :class="getNodeIcon(data)"></i>
                  <el-tooltip :content="node.label" placement="right" :disabled="!isEllipsis(node.label)">
                    <span :class="{ 'highlight': isHighlighted(node, searchQuery) }" class="node-label">
                      {{ node.label }}
                    </span>
                  </el-tooltip>
                  <!-- 添加发布状态标签 -->
                  <el-tag v-if="data.type === 'api'" size="mini" :type="data.published ? 'success' : 'danger'"
                    class="publish-status-tag">
                    {{ data.published ? '已发布' : '未发布' }}
                  </el-tag>
                </span>
                <span class="node-actions">
                  <el-button type="text" size="mini" @click.stop="handleDelete(node, data)">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </span>
              </span>
            </el-tree>
          </div>
        </el-aside>
        <div class="resize-handle"></div>
      </div>

      <!-- 右侧内容区域 -->
      <el-container class="main-content">
        <el-main class="api-detail-container">
          <div v-if="noApiSelected" class="no-api-mask">
            <div class="no-api-tip">
              <i class="el-icon-connection"></i>
              <p>请选择接口进行编辑</p>
            </div>
          </div>
          <!-- API地址区域 -->
          <div class="api-detail-header card-container">
            <div class="section-header">
              <div class="section-title">
                <span>基本信息</span>
              </div>
              <div class="section-actions">
                <el-button type="primary" size="small" @click="saveApiDefinition" :disabled="!currentApi">
                  <i class="el-icon-check"></i> 保存
                </el-button>
              </div>
            </div>
            <div class="card-content">
              <div class="api-title">
                <el-tag size="small" type="primary" class="method-tag">GET</el-tag>
                <div class="name-input-wrapper">
                  <el-input v-if="currentApi" v-model="currentApi.name" placeholder="请输入接口名称" size="medium"
                    @change="handleNameChange" class="api-name-input">
                    <template slot="prepend">名称</template>
                  </el-input>
                </div>
                <!-- 添加发布状态开关 -->
                <el-switch v-if="currentApi" v-model="currentApi.published" active-color="#13ce66"
                  inactive-color="#ff4949" :active-text="currentApi.published ? '已发布' : '未发布'"
                  @change="handlePublishChange" />
                <el-tag size="medium" effect="plain" :type="getDatasetTagType" class="dataset-tag" @click="goToDataset">
                  <i class="el-icon-connection"></i>
                  <template v-if="currentApi?.datasetId">
                    {{ getDatasetDisplayName }}
                  </template>
                  <template v-else>
                    未绑定数据集
                  </template>
                </el-tag>
              </div>
              <div class="api-url-section">
                <div class="url-header">
                  <div class="url-info">
                    <div class="url-content">
                      <div class="url-path">
                        <span class="base-url">{{ apiBaseUrl }}/</span>
                        <el-input v-model="apiPath" size="small" placeholder="namespace/method-id" class="path-input"
                          @change="handlePathChange" />
                      </div>
                      <el-button type="text" size="small" @click="copyApiUrl" class="copy-button">
                        <i class="el-icon-document-copy"></i> 复制
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 移动到 api-url-section 后面 -->
              <div class="api-description">
                <div class="description-header" @click="descriptionVisible = !descriptionVisible">
                  <span>描述</span>
                  <i :class="['el-icon-arrow-' + (descriptionVisible ? 'up' : 'down')]"></i>
                </div>
                <el-collapse-transition>
                  <div v-show="descriptionVisible" class="description-content">
                    <el-input v-if="currentApi" v-model="currentApi.description" type="textarea" :rows="3"
                      placeholder="请输入API描述..." resize="none" @change="handleDescriptionChange" />
                  </div>
                </el-collapse-transition>
              </div>
            </div>
          </div>

          <!-- 参数配置区域 -->
          <div class="params-section card-container">
            <div class="section-header">
              <div class="section-title">
                <span>请求参数</span>
              </div>
              <div class="section-actions">
                <el-button type="primary" size="small" @click="addParameter" plain>
                  <i class="el-icon-plus"></i> 添加参数
                </el-button>
              </div>
            </div>
            <div class="card-content">
              <el-table :data="parameters" key="params-table" border row-key="id" style="width: 100%">
                <el-table-column width="60" align="center">
                  <template slot-scope="scope">
                    <i class="el-icon-rank handle" style="cursor: move;"></i>
                  </template>
                </el-table-column>

                <el-table-column label="参数名" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name" placeholder="请输入参数名" size="small" />
                  </template>
                </el-table-column>

                <el-table-column label="参数值" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value" placeholder="请输入参数值" size="small" />
                  </template>
                </el-table-column>

                <el-table-column label="必填" width="100" align="center">
                  <template slot-scope="scope">
                    <el-switch v-model="scope.row.required" active-color="#409EFF" />
                  </template>
                </el-table-column>

                <el-table-column label="类型" width="150">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.type" placeholder="请选择类型" size="small">
                      <el-option label="String" value="string" />
                      <el-option label="Integer" value="integer" />
                      <el-option label="Number" value="number" />
                      <el-option label="Boolean" value="boolean" />
                      <el-option label="Array" value="array" />
                      <el-option label="Object" value="object" />
                    </el-select>
                  </template>
                </el-table-column>

                <el-table-column label="说明" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.description" type="textarea" :rows="1" placeholder="请输入说明"
                      size="small" />
                  </template>
                </el-table-column>

                <el-table-column width="100" align="center">
                  <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.enabled" @change="handleParamEnableChange(scope.row)" />
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="removeParameter(scope.$index)" class="delete-button">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 响应区域 -->
          <div class="response-section card-container">
            <div class="section-header">
              <div class="section-title">
                <span>响应信息</span>
              </div>
              <div class="section-actions">
                <el-tag size="small" type="info" effect="plain" v-if="queryStats.time">
                  执行耗时: {{ queryStats.time }}ms
                </el-tag>
                <el-tag size="small" type="info" effect="plain" v-if="queryStats.rows">
                  返回数据: {{ queryStats.rows }}条
                </el-tag>
                <el-button type="primary" size="small" @click="executeApi" :loading="executing">
                  <i class="el-icon-video-play"></i> 执行
                </el-button>
              </div>
            </div>
            <div class="card-content">
              <div class="response-content">
                <!-- 左侧响应定义 -->
                <div class="response-schema">
                  <div class="schema-header">响应定义</div>
                  <div class="schema-content">
                    <el-table :data="responseSchema" key="response-schema-table" border style="width: 100%" row-key="name"
                      :tree-props="{ children: 'children' }">
                      <el-table-column prop="name" label="字段" min-width="200">
                        <template slot-scope="scope">
                          <span :style="{ marginLeft: scope.row.level * 20 + 'px' }">
                            {{ scope.row.name }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="type" label="类型" width="120">
                        <template slot-scope="scope">
                          <el-tag :type="getTypeTagType(scope.row.type)" size="small" effect="light">
                            {{ scope.row.type }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="description" label="说明" min-width="200">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.description" type="text" size="small" placeholder="请输入说明" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
                <!-- 右侧响应结果 -->
                <div class="response-result">
                  <div class="result-header">
                    <span>响应结果</span>
                    <div class="result-actions">
                      <el-tooltip content="编辑响应" placement="top">
                        <el-button type="text" size="small" @click="handleEditResponse" :disabled="!apiResponse">
                          <i class="el-icon-edit-outline"></i>
                        </el-button>
                      </el-tooltip>
                      <el-tooltip content="提取响应定义" placement="top">
                        <el-button type="text" size="small" @click="extractResponseSchema" :disabled="!apiResponse">
                          <i class="el-icon-document-add"></i>
                        </el-button>
                      </el-tooltip>
                      <el-tooltip content="复制响应结果" placement="top">
                        <el-button type="text" size="small" @click="copyResponse" :disabled="!apiResponse">
                          <i class="el-icon-document-copy"></i>
                        </el-button>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="result-content">
                    <div v-if="isEditingResponse" class="response-editor">
                      <el-input v-model="editingResponseText" type="textarea" :rows="10"
                        :autosize="{ minRows: 10, maxRows: 20 }" placeholder="请输入JSON格式的响应数据" />
                      <div class="editor-actions">
                        <el-button size="small" @click="cancelEditResponse">取消</el-button>
                        <el-button type="primary" size="small" @click="saveEditResponse">保存</el-button>
                      </div>
                    </div>
                    <template v-else>
                      <pre v-if="apiResponse" v-html="formatJson(apiResponse)" class="json-viewer"></pre>
                      <div v-else class="no-response">
                        <i class="el-icon-warning-outline"></i>
                        <p>暂无响应数据</p>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>

    <!-- 添加编辑对话框 -->
    <el-dialog :title="editDialogTitle" :visible.sync="editDialog" width="500px" :append-to-body="true"
      @close="handleEditDialogClose">
      <el-form ref="editForm" :model="editForm" :rules="editFormRules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入名称"></el-input>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
        </el-form-item>

        <el-form-item label="绑定数据集" prop="datasetId">
          <el-select v-model="editForm.datasetId" placeholder="请选择数据集" filterable clearable style="width: 100%"
            @focus="handleDatasetFocus" @change="handleDatasetChange">
            <el-option v-for="item in datasetOptions" :key="item.id" :label="item.name" :value="item.id">
              <span>{{ item.name }}</span>
              <span style="color: #8492a6; font-size: 13px; margin-left: 8px">
                {{ item.namespace }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="所属文件夹" prop="folderId">
          <el-select v-model="editForm.folderId" placeholder="请选择文件夹" filterable clearable style="width: 100%">
            <el-option v-for="item in folderOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- 在编辑表单中添加发布状态开关 -->
        <el-form-item label="发布状态" prop="published">
          <el-switch v-model="editForm.published" active-color="#13ce66" inactive-color="#ff4949" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveEdit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加详情对话框 -->
    <el-dialog :title="viewDialogTitle" :visible.sync="viewDialog" width="800px" :append-to-body="true">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="名称">
          {{ viewForm.name }}
        </el-descriptions-item>
        <el-descriptions-item label="ID">
          {{ viewForm.id }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ viewForm.description }}
        </el-descriptions-item>
        <template v-if="viewType === 'api'">
          <el-descriptions-item label="参数定义" :span="2">
            <el-table :data="viewForm.parameters" key="view-params-table" border size="small">
              <el-table-column prop="name" label="参数名" min-width="120"></el-table-column>
              <el-table-column prop="type" label="类型" width="100"></el-table-column>
              <el-table-column prop="description" label="说明"></el-table-column>
            </el-table>
          </el-descriptions-item>
          <el-descriptions-item label="响应定义" :span="2">
            <el-table :data="viewForm.responseSchema" key="view-response-schema-table" border size="small">
              <el-table-column prop="name" label="字段名" min-width="120"></el-table-column>
              <el-table-column prop="type" label="类型" width="100"></el-table-column>
              <el-table-column prop="description" label="说明"></el-table-column>
            </el-table>
          </el-descriptions-item>
        </template>
        <el-descriptions-item label="创建时间">
          {{ viewForm.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ viewForm.updateTime }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 添加数据集详情对话框 -->
    <el-dialog title="绑定数据集" :visible.sync="datasetDialog" width="800px" :append-to-body="true">
      <dataset-detail v-if="datasetDialog" :api-id="currentApi?.id" :dataset-id="currentApi?.datasetId"
        @close="datasetDialog = false" @refresh="handleDatasetBindingChange" />
    </el-dialog>
  </div>
</template>

<script>
import {
  getDatasetTree,
  createFolder,
  deleteFolder,
  getFolderDetail,
  updateFolder,
  getFolderList,
  getDatasetDetail,
  getDatasetList
} from '@system/api/data-manger/dataset'
import {
  getApiDetail,
  createApi,
  updateApi,
  deleteApi,
  
} from '@system/api/data-manger/api-management'
import axios from 'axios'
import { getToken } from '@/utils/auth'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css' // 或其他主题样式
import json from 'highlight.js/lib/languages/json'
import DatasetDetail from '../dataset/components/dataset-detail'
import { parseSqlParams } from '../sql-parser'
import Sortable from 'sortablejs'
hljs.registerLanguage('json', json)

const copyToClipboard = (text) => {
  return new Promise((resolve, reject) => {
    try {
      // 创建临时textarea元素
      const textarea = document.createElement('textarea')
      textarea.value = text
      // 设置位置在屏幕外
      textarea.style.position = 'fixed'
      textarea.style.left = '-9999px'
      document.body.appendChild(textarea)
      // 选择文本
      textarea.select()
      // 执行复制命令
      document.execCommand('copy')
      // 移除临时元素
      document.body.removeChild(textarea)
      resolve()
    } catch (err) {
      reject(err)
    }
  })
}

export default {
  name: 'ApiManagement',
  data() {
    return {
      searchQuery: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      currentApi: null,
      apiBaseUrl: `${process.env.VUE_APP_BASE_API}${process.env.VUE_APP_BASE_API.endsWith('/') ? '' : '/'}${CONSTANT.META}/api/execute`,
      parameters: [],
      responseSchema: [],
      apiResponse: null,
      executing: false,
      editDialog: false,
      editType: '',
      editForm: {
        name: '',
        description: '',
        datasetId: '',
        folderId: '',
        published: false // 添加 published 字段
      },
      editFormRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: false, message: '请输入描述', trigger: 'blur' }
        ],
        datasetId: [
          { required: true, message: '请选择数据集', trigger: 'change' }
        ],
        folderId: [
          { required: true, message: '请选择文件夹', trigger: 'change' }
        ]
      },
      viewDialog: false,
      viewType: '',
      viewForm: {
        id: '',
        name: '',
        description: '',
        datasetId: null,
        datasetName: '',
        folderId: null,
        folderName: '',
        requestSchema: [],
        responseSchema: [],
        examplesRequest: '',
        examplesResponse: ''
      },
      noApiSelected: true,
      queryStats: {
        time: null,
        rows: null
      },
      datasetDialog: false,
      datasetOptions: [],
      folderOptions: [],
      sqlParams: [], // 存储SQL中的参数名
      queryParams: {}, // 存储参数值
      datasetLoading: false,
      datasetSearchTimer: null,
      sortable: null,
      apiPath: '', // 添加 apiPath 数据
      uri: '', // 添加 uri 字段
      isEditingResponse: false,
      editingResponseText: '',
      description: '', // 添加描述字段
      descriptionVisible: true, // 控制描述区域的展开/收起
    }
  },
  created() {
    this.fetchApiTree()
  },
  mounted() {
    this.initResizable()
    this.$nextTick(() => {
      this.initSortable()
    })

    // 添加全局快捷键监听
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    // 移除全局快捷键监听
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    // 添加快捷键处理方法
    handleKeyDown(e) {
      // Ctrl+S 或 Command+S
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        this.saveApiDefinition()
      }
      // Ctrl+Enter 或 Command+Enter
      else if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault()
        this.executeApi() // 注意这里使用 executeApi 而不是 runApi
      }
    },

    // 获取API树
    async fetchApiTree() {
      try {
        const data = await getDatasetTree({ type: 'api' })
        this.treeData = this.transformTreeData(data)
      } catch (error) {
        console.error('获取API列表失败:', error)
        this.$message.error('获取API列表失败：' + error.message)
      }
    },

    // 节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      const name = data.name || ''
      return name.toLowerCase().includes(value.toLowerCase())
    },

    transformTreeData(data) {
      const nodes = []

      // 处理根目录下的文件夹
      if (data.subFolders) {
        data.subFolders.forEach(item => {
          if (item.folder) {
            const folderNode = {
              id: item.folder.id,
              name: item.folder.name,
              type: 'group',
              description: item.folder.description,
              parentId: item.folder.parentId,
              parentPath: item.folder.parentPath || [],
              isLeaf: false,
              children: []
            }

            // 递归处理子文件夹，只传递子文件夹的数据
            if (item.subFolders && item.subFolders.length > 0) {
              folderNode.children.push(...this.transformTreeData({
                subFolders: item.subFolders,
                apis: item.subFolders.apis // 只传递子文件夹自己的 apis
              }))
            }

            // 添加当前文件夹下的 API
            if (item.apis) {
              item.apis.forEach(api => {
                folderNode.children.push({
                  id: api.id,
                  name: api.name,
                  type: 'api',
                  description: api.description,
                  datasetId: api.datasetId,
                  folderId: api.folderId,
                  published: api.published, // 添加 published 字段
                  isLeaf: true
                })
              })
            }

            nodes.push(folderNode)
          }
        })
      }

      // 处理根目录下的 API
      if (data.apis) {
        data.apis.forEach(api => {
          nodes.push({
            id: api.id,
            name: api.name,
            type: 'api',
            description: api.description,
            datasetId: api.datasetId,
            folderId: api.folderId,
            published: api.published, // 添加 published 字段
            isLeaf: true
          })
        })
      }

      return nodes
    },

    // 节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      const name = data.name || ''
      return name.toLowerCase().includes(value.toLowerCase())
    },

    // 获取节点图标
    getNodeIcon(data) {
      return data.type === 'group' ? 'el-icon-folder' : 'el-icon-connection'
    },

    // 判断文本是否溢出
    isEllipsis(text) {
      const span = document.createElement('span')
      span.style.visibility = 'hidden'
      span.style.whiteSpace = 'nowrap'
      span.style.fontSize = '14px'
      span.innerText = text
      document.body.appendChild(span)

      const width = span.offsetWidth
      document.body.removeChild(span)

      return width > 150
    },

    // 处理新增
    async handleAdd(command) {
      if (command === 'api') {
        this.editType = 'api'
        this.editForm = {
          name: '',
          description: '',
          datasetId: '',
          folderId: '',
          published: false // 添加 published 字段
        }

        try {
          // 打开对话框前加载文件夹列表和数据集列表
          await Promise.all([
            this.fetchFolderList(),
            this.handleDatasetFocus() // 使用相同的方法加载数据集列表
          ])

          this.editDialog = true
        } catch (error) {
          console.error('初始化对话框失败:', error)
          this.$message.error('初始化对话框失败：' + error.message)
        }
      } else if (command === 'group') {
        try {
          const value = await this.$prompt('请输入文件夹名称', '新增文件夹', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern: /^.{2,50}$/,
            inputErrorMessage: '文件夹名称长度在2-50个字符之间'
          })

          const folderData = {
            parentId: "0",
            name: value,
            description: `${value}文件夹`
          }

          await createFolder(folderData)
          this.$message.success('新增文件夹成功')
          await this.fetchApiTree()
        } catch (error) {
          if (error !== 'cancel') {
            this.$message.error('新增文件夹失败：' + error.message)
          }
        }
      }
    },

    // 处理删除
    async handleDelete(node, data) {
      try {
        await this.$confirm(
          `确认删除${data.type === 'group' ? '文件夹' : 'API'} "${data.name}" 吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        if (data.type === 'group') {
          await deleteFolder(data.id)
          this.$message.success('删除文件夹成功')
        } else {
          await deleteApi(data.id)
          this.$message.success('删除API成功')
        }
        await this.fetchApiTree()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败：' + error.message)
        }
      }
    },

    // 允许放置的判断
    allowDrop(draggingNode, dropNode, type) {
      // 不允许拖放到自己下面
      if (draggingNode.data.id === dropNode.data.id) {
        return false
      }

      // 如果是拖动API
      if (draggingNode.data.type === 'api') {
        // API只能拖到文件夹中
        return dropNode.data.type === 'group'
      }

      // 如果是拖动文件夹
      if (draggingNode.data.type === 'group') {
        // 文件夹只能拖到其他文件夹中
        if (dropNode.data.type !== 'group') {
          return false
        }

        // 不能拖到自己的子文件夹中
        const isChild = (parent, child) => {
          if (!child.parentPath) return false
          return child.parentPath.includes(parent.id)
        }

        return !isChild(draggingNode.data, dropNode.data)
      }

      return false
    },

    // 处理拖拽完成
    async handleDrop(draggingNode, dropNode, type, event) {
      try {
        const dragData = draggingNode.data
        const dropData = dropNode.data

        // 如果是拖动API
        if (dragData.type === 'api') {
          await updateApi({
            id: dragData.id,
            folderId: type === 'inner' ? dropData.id : '0' // 如果不是拖入文件夹内部，则设置为根目录
          })
        }
        // 如果是拖动文件夹
        else if (dragData.type === 'group') {
          await updateFolder({
            id: dragData.id,
            name: dragData.name,
            description: dragData.description,
            // 根据拖拽类型和位置确定新的父ID
            parentId: type === 'inner' ? dropData.id :
              dropNode.data.type === 'group' ? dropNode.data.parentId || '0' : '0'
          })
        }

        this.$message.success('移动成功')
        await this.fetchApiTree() // 重新加载树结构
      } catch (error) {
        console.error('移动失败:', error)
        this.$message.error('移动失败：' + (error.message || '未知错误'))
        await this.fetchApiTree() // 发生错误时也重新加载树结构
      }
    },

    // 处理搜索框清空
    handleSearchClear() {
      this.searchQuery = ''
      this.$refs.apiTree.filter('')
    },

    // 复制API地址
    async copyApiUrl() {
      if (!this.currentApi) {
        this.$message.warning('请先选择一个API')
        return
      }

      const url = `${this.apiBaseUrl}/${this.apiPath}`
      try {
        await copyToClipboard(url)
        this.$message.success('复制成功')
      } catch (error) {
        this.$message.error('复制失败')
      }
    },

    // 添加参数
    addParameter() {
      this.parameters.push({
        id: Date.now(),
        name: '',
        value: '',
        type: 'string',
        description: '',
        required: false,
        enabled: true, // 默认启用
      })
    },

    // 删除参数
    removeParameter(index) {
      this.parameters.splice(index, 1)
    },

    // 执行API
    async executeApi() {
      if (!this.currentApi) return

      this.executing = true
      const startTime = Date.now()

      try {
        // 只收集启用的参数
        const queryParams = this.parameters.reduce((acc, param) => {
          if (param.enabled && param.name && param.value) {
            acc[param.name] = param.value
          }
          return acc
        }, {})

        // 调用API
        const response = await axios.get(`${this.apiBaseUrl}/${this.uri}`, {
          headers: {
            Authorization: getToken()
          },
          params: queryParams
        })

        const endTime = Date.now()

        this.apiResponse = response.data
        this.queryStats = {
          time: endTime - startTime,
          rows: Array.isArray(response.data) ? response.data.length : 1
        }
        if (response.code === 200) {
          this.$message.success('执行成功')
        }
      } catch (error) {
        console.error('执行失败:', error)
        this.$message.error('执行失败：' + (error.message || '未知错误'))
        this.apiResponse = null
        this.queryStats = {
          time: null,
          rows: null
        }
      } finally {
        this.executing = false
      }
    },

    // 格式化JSON
    formatJson(response) {
      if (!response) return ''
      try {
        // 保持完整响应结构
        const formatted = JSON.stringify(response, null, 2)
        // 使用 highlight.js 进行语法高亮
        return hljs.highlight('json', formatted).value
      } catch (error) {
        console.error('JSON格式化失败:', error)
        return JSON.stringify(response)
      }
    },

    // 处理节点点击
    handleNodeClick(data, node) {
      if (data.type === 'api') {
        this.switchApi(data)
      } else {
        // 点击文件夹时的处理
        this.currentApi = null
        this.noApiSelected = true
      }
    },

    // 添加切换 API 的方法
    async switchApi(data) {
      // 先设置基本信息
      this.currentApi = {
        ...data,
        name: data.name || '',
        description: data.description || '', // 确保设置 description
        published: data.published || false
      }
      this.noApiSelected = false

      // 加载 API 详情
      await this.loadApiDetail(data.id)
    },

    // 修改名称变更处理方法
    handleNameChange(value) {
      if (this.currentApi) {
        if (this.currentApi.name !== value) {
          this.currentApi.name = value
        }
      }
    },

    // 修改保存方法
    async saveApiDefinition() {
      if (!this.currentApi) return

      try {
        const saveData = {
          id: this.currentApi.id,
          name: this.currentApi.name,
          description: this.currentApi.description || '', // 确保设置 description
          datasetId: this.currentApi.datasetId,
          folderId: this.currentApi.folderId,
          published: this.currentApi.published,
          uri: this.uri, // 添加 uri 字段
          requestSchema: this.parameters.map((param, index) => ({
            name: param.name,
            type: param.type,
            description: param.description,
            required: param.required,
            orderNum: index,
            value: param.value,
            enabled: param.enabled, // 保存启用状态
          })),
          responseSchema: this.responseSchema,
          examplesRequest: this.parameters.reduce((acc, param) => {
            if (param.name && param.value) {
              acc[param.name] = this.convertParamValue(param.value, param.type)
            }
            return acc
          }, {}),
          examplesResponse: this.apiResponse
        }

        await updateApi(saveData)
        this.$message.success('API定义保存成功')
        await this.fetchApiTree()
      } catch (error) {
        console.error('保存API定义失败:', error)
        this.$message.error('保存API定义失败：' + (error.message || '未知错误'))
      }
    },

    // 添加新的辅助方法，用于转换参数值到正确的类型
    convertParamValue(value, type) {
      if (!value) return null

      switch (type) {
        case 'integer':
          return parseInt(value, 10)
        case 'number':
          return parseFloat(value)
        case 'boolean':
          return value.toLowerCase() === 'true'
        case 'array':
          try {
            return JSON.parse(value)
          } catch (e) {
            return []
          }
        case 'object':
          try {
            return JSON.parse(value)
          } catch (e) {
            return {}
          }
        default:
          return value // string类型直接返回
      }
    },

    // 添加监听输入框值变化的方法
    handleInputChange() {
    },

    // 加载API详情
    async loadApiDetail(id) {
      if (!id) return

      try {
        const apiData = await getApiDetail(id)
        if (!apiData) return

        // 更新当前 API 的基本信息
        this.currentApi = {
          ...apiData,
          published: apiData.published
        }

        // 如果有数据集信息，获取数据集详情
        if (apiData.datasetId) {
          try {
            const datasetData = await getDatasetDetail(apiData.datasetId)
            if (datasetData) {
              this.currentApi = {
                ...this.currentApi,
                datasetName: datasetData.name,
                datasetNamespace: datasetData.namespace
              }

              // 设置 API 路径：优先使用已保存的 uri，如果没有则使用默认值
              this.uri = apiData.uri || `${datasetData.namespace || ''}/${datasetData.mappedId}`
              this.apiPath = this.uri // 同步显示值

              // 解析 SQL 参数
              if (datasetData.mappedContent) {
                this.handleDatasetSqlParse(datasetData.mappedContent)
              }
            }
          } catch (error) {
            console.error('获取数据集详情失败:', error)
            this.$message.warning('获取数据集详情失败：' + error.message)
          }
        } else {
          this.uri = apiData.uri || apiData.id
          this.apiPath = this.uri
        }

        // 设置参数列表
        this.parameters = (apiData.requestSchema || [])
          .sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0))
          .map(schema => ({
            id: Date.now() + Math.random(),
            name: schema.name,
            value: schema.value || '',
            type: schema.type || 'string',
            description: schema.description || '',
            required: schema.required || false,
            enabled: schema.enabled !== false, // 如果后端没有这个字段，默认为true
          }))

        // 设置响应定义
        this.responseSchema = apiData.responseSchema || []

        // 设置响应示例
        if (apiData.examplesResponse) {
          this.apiResponse = apiData.examplesResponse
        }
      } catch (error) {
        console.error('加载API详情失败:', error)
        this.$message.error('加载API详情失败：' + error.message)
      }
    },

    // 添加处理数据集SQL解析的方法
    handleDatasetSqlParse(sql) {
      if (!sql) return

      try {
        const { sqlParams } = parseSqlParams(sql)

        // 清理旧的参数列表，只保留 SQL 中存在的参数
        const existingParams = this.parameters.filter(p => sqlParams.includes(p.name))

        // 添加新的参数
        sqlParams.forEach(param => {
          if (!existingParams.some(p => p.name === param)) {
            existingParams.push({
              id: Date.now() + Math.random(),
              name: param,
              value: '',
              type: 'string',
              description: '',
              required: false
            })
          }
        })

        // 更新参数列表
        this.parameters = existingParams
      } catch (error) {
        console.error('解析SQL参数失败:', error)
      }
    },

    // 处理查看
    async handleView(node, data) {
      try {
        this.viewType = data.type === 'group' ? 'folder' : 'api'

        let detail
        if (this.viewType === 'folder') {
          const res = await getFolderDetail(data.id)
          detail = res.data
        } else {
          const res = await getApiDetail(data.id)
          detail = res.data
        }

        this.viewForm = {
          id: detail.id,
          name: detail.name,
          description: detail.description,
          createTime: detail.createTime,
          updateTime: detail.updateTime
        }

        if (this.viewType === 'api') {
          this.viewForm = {
            ...this.viewForm,
            parameters: detail.parameters || [],
            responseSchema: detail.responseSchema || []
          }
        }

        this.viewDialog = true
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败：' + error.message)
      }
    },

    // 处理编辑
    async handleEdit(node, data) {
      try {
        // 加载数据集和文件夹列表
        await Promise.all([
          this.fetchDatasetList(),
          this.fetchFolderList()
        ])

        // 如果是编辑模式，加载API详情
        if (data.id) {
          const detail = await getApiDetail(data.id)

          this.editForm = {
            id: detail.id,
            name: detail.name,
            description: detail.description,
            datasetId: detail.datasetId,
            folderId: detail.folderId,
            requestSchema: detail.requestSchema || [],
            responseSchema: detail.responseSchema || [],
            examplesRequest: detail.examplesRequest ? JSON.stringify(detail.examplesRequest, null, 2) : '',
            examplesResponse: detail.examplesResponse ? JSON.stringify(detail.examplesResponse, null, 2) : '',
            published: detail.published || false // 添加 published 字段
          }
        } else {
          // 新增模式，重置表单
          this.editForm = {
            name: '',
            description: '',
            datasetId: '',
            folderId: '',
            requestSchema: [],
            responseSchema: [],
            examplesRequest: '',
            examplesResponse: '',
            published: false // 添加 published 字段
          }
        }

        this.editDialog = true
      } catch (error) {
        console.error('初始化编辑对话框失败:', error)
        this.$message.error('初始化编辑对话框失败：' + error.message)
      }
    },

    // 处理保存编辑
    async handleSaveEdit() {
      try {
        await this.$refs.editForm.validate()

        const data = {
          name: this.editForm.name,
          description: this.editForm.description,
          datasetId: this.editForm.datasetId,
          folderId: this.editForm.folderId || '0',
          published: this.editForm.published // 添加 published 字段
        }

        if (this.editForm.id) {
          await updateApi(data)
          this.$message.success('更新API成功')
        } else {
          await createApi(data)
          this.$message.success('新增API成功')
        }

        this.editDialog = false
        await this.fetchApiTree()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('保存失败:', error)
          this.$message.error('保存失败：' + error.message)
        }
      }
    },

    // 处理对话框关闭
    handleEditDialogClose() {
      this.$refs.editForm.resetFields()
      this.editForm = {
        name: '',
        description: '',
        datasetId: '',
        folderId: '',
        published: false // 添加 published 字段
      }
      this.datasetOptions = []
      this.folderOptions = []
    },

    // 添加拖拽功能方法
    initResizable() {
      const resizable = document.getElementById('resizable')
      const handle = document.querySelector('.resize-handle')
      let startX, startWidth

      handle.addEventListener('mousedown', function (e) {
        startX = e.clientX
        startWidth = parseInt(document.defaultView.getComputedStyle(resizable).width, 10)
        document.documentElement.addEventListener('mousemove', doDrag, false)
        document.documentElement.addEventListener('mouseup', stopDrag, false)
      })

      function doDrag(e) {
        const newWidth = startWidth + e.clientX - startX
        if (newWidth > 200 && newWidth < 600) {
          resizable.style.width = newWidth + 'px'
        }
      }

      function stopDrag() {
        document.documentElement.removeEventListener('mousemove', doDrag, false)
        document.documentElement.removeEventListener('mouseup', stopDrag, false)
      }
    },

    // 添加高亮搜索方法
    isHighlighted(node, keyword) {
      if (!keyword) return false
      const label = node.label || node.data.name || ''
      return label.toLowerCase().includes(keyword.toLowerCase())
    },

    // 提取响应定义
    extractResponseSchema() {
      if (!this.apiResponse) return

      try {
        const extractSchema = (obj, parentKey = '', level = 0) => {
          const schema = []

          if (obj === null) {
            // 查找现有字段定义
            const existingField = this.responseSchema.find(f => f.name === parentKey.split('.').pop())
            return [{
              name: parentKey.split('.').pop(),
              type: 'null',
              description: existingField?.description || '', // 保留现有描述
              level
            }]
          }

          if (Array.isArray(obj)) {
            if (obj.length > 0) {
              const arraySchema = extractSchema(obj[0], parentKey, level)
              arraySchema[0].type = 'array<' + arraySchema[0].type + '>'
              return arraySchema
            }
            // 查找现有字段定义
            const existingField = this.responseSchema.find(f => f.name === parentKey.split('.').pop())
            return [{
              name: parentKey.split('.').pop(),
              type: 'array',
              description: existingField?.description || '', // 保留现有描述
              level
            }]
          }

          if (typeof obj === 'object') {
            Object.entries(obj).forEach(([key, value]) => {
              const fieldName = parentKey ? key : key
              const fieldType = Array.isArray(value) ? 'array' :
                value === null ? 'null' :
                  typeof value

              // 递归查找现有字段定义（支持嵌套结构）
              const findExistingField = (schema, name) => {
                for (const field of schema) {
                  if (field.name === name) return field
                  if (field.children) {
                    const found = findExistingField(field.children, name)
                    if (found) return found
                  }
                }
                return null
              }

              const existingField = findExistingField(this.responseSchema, fieldName)

              const schemaItem = {
                name: fieldName,
                type: fieldType,
                description: existingField?.description || '', // 保留现有描述
                level
              }

              schema.push(schemaItem)

              if (value && typeof value === 'object') {
                const children = extractSchema(value, key, level + 1)
                if (children.length > 0) {
                  schemaItem.children = children
                  if (!Array.isArray(value)) {
                    schemaItem.type = 'object'
                  }
                }
              }
            })
          } else {
            // 查找现有字段定义
            const existingField = this.responseSchema.find(f => f.name === parentKey.split('.').pop())
            schema.push({
              name: parentKey.split('.').pop(),
              type: typeof obj,
              description: existingField?.description || '', // 保留现有描述
              level
            })
          }

          return schema
        }

        // 提取新的响应结构
        const newSchema = extractSchema(this.apiResponse)

        // 更新响应定义，但保留现有描述
        this.responseSchema = newSchema

        this.$message.success('响应定义提取成功')
      } catch (error) {
        console.error('提取响应定义失败:', error)
        this.$message.error('提取响应定义失败')
      }
    },

    // 复制响应结果
    async copyResponse() {
      if (!this.apiResponse) return

      try {
        const text = JSON.stringify(this.apiResponse, null, 2)
        await copyToClipboard(text)
        this.$message.success('复制成功')
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败')
      }
    },

    // 修改 goToDataset 方法
    async goToDataset() {
      try {
        // 显示更改绑定的确认对话框
        await this.$confirm(
          '更改绑定将会刷新请求参数、请求示例、响应定义和响应示例，是否确认更改？',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 用户确认后显示数据集对话框
        this.datasetDialog = true
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('操作失败：' + error.message)
        }
      }
    },

    // 获取数据集列表
    async fetchDatasetList() {
      try {
        const data = await getDatasetList({
          name: this.searchQuery // 添加搜索关键词
        })

        if (data && data.records) {
          this.datasetOptions = data.records.map(item => ({
            id: item.id,
            name: item.name,
            namespace: item.namespace
          }))
        }
      } catch (error) {
        console.error('获取数据集列表失败:', error)
        this.$message.error('获取数据集列表失败：' + error.message)
      }
    },

    // 获取文件夹列表
    async fetchFolderList() {
      try {
        const data = await getFolderList()
        // 确保 data 是数组
        if (Array.isArray(data)) {
          this.folderOptions = data.map(item => ({
            id: item.id,
            name: item.name
          }))
        } else if (data && Array.isArray(data.records)) {
          // 如果返回的是分页数据结构
          this.folderOptions = data.records.map(item => ({
            id: item.id,
            name: item.name
          }))
        } else {
          this.folderOptions = [] // 设置为空数组
          console.warn('文件夹数据格式不正确:', data)
        }
      } catch (error) {
        console.error('获取文件夹列表失败:', error)
        this.$message.error('获取文件夹列表失败：' + error.message)
        this.folderOptions = [] // 出错时设置为空数组
      }
    },

    // 数据集选择框获得焦点时触发
    async handleDatasetFocus() {
      if (this.datasetOptions.length === 0) {
        try {
          const data = await getDatasetList()
          if (data && Array.isArray(data.records)) {
            this.datasetOptions = data.records.map(item => ({
              id: item.id,
              name: item.name,
              namespace: item.namespace || ''
            }))
          } else {
            console.warn('数据集数据格式不正确:', data)
          }
        } catch (error) {
          console.error('获取数据集列表失败:', error)
          this.$message.error('获取数据集列表失败：' + error.message)
        }
      }
    },

    // 远程搜索数据集
    remoteSearchDatasets(query) {
      if (this.datasetSearchTimer) {
        clearTimeout(this.datasetSearchTimer)
      }

      this.datasetSearchTimer = setTimeout(async () => {
        this.datasetLoading = true
        try {
          const data = await getDatasetList({
            name: query || undefined // 如果query为空字符串则不传此参数
          })

          if (data && Array.isArray(data.records)) {
            this.datasetOptions = data.records.map(item => ({
              id: item.id,
              name: item.name,
              namespace: item.namespace || ''
            }))
          } else {
            this.datasetOptions = []
            console.warn('数据集数据格式不正确:', data)
          }
        } catch (error) {
          console.error('获取数据集列表失败:', error)
          this.$message.error('获取数据集列表失败：' + error.message)
          this.datasetOptions = []
        } finally {
          this.datasetLoading = false
        }
      }, 300)
    },

    // 获取类型标签的样式
    getTypeTagType(type) {
      // 处理复合类型,如 array<string>
      const baseType = type.includes('<') ? type.split('<')[0] : type

      const typeMap = {
        'string': '',           // 默认灰色
        'number': 'success',    // 绿色
        'integer': 'success',   // 绿色
        'boolean': 'warning',   // 黄色
        'object': 'info',       // 蓝灰色
        'array': 'primary',     // 蓝色
        'null': 'danger'        // 红色
      }
      return typeMap[baseType] || ''
    },

    // 修改 handleDatasetBindingChange 方法
    async handleDatasetBindingChange() {
      this.datasetDialog = false

      // 重新加载当前 API 的详情
      if (this.currentApi) {
        try {
          // 获取最新的 API 详情
          const apiData = await getApiDetail(this.currentApi.id)
          if (apiData && apiData.datasetId) {
            // 获取新绑定的数据集详情
            const datasetData = await getDatasetDetail(apiData.datasetId)
            if (datasetData) {
              // 更新 currentApi 的数据集相关信息
              this.currentApi = {
                ...apiData,
                datasetName: datasetData.name,
                datasetNamespace: datasetData.namespace
              }

              // 设置 API 路径
              this.uri = apiData.uri || `${datasetData.namespace || ''}/${datasetData.mappedId}`
              this.apiPath = this.uri // 同步显示值

              // 解析 SQL 参数并更新参数列表
              if (datasetData.mappedContent) {
                this.handleDatasetSqlParse(datasetData.mappedContent)
              }
            }
          }

          // 刷新树形列表
          await this.fetchApiTree()
        } catch (error) {
          console.error('更新数据集绑定失败:', error)
          this.$message.error('更新数据集绑定失败：' + error.message)
        }
      }
    },

    // 初始化拖拽排序
    initSortable() {
      const tbody = document.querySelector('.params-section .el-table__body-wrapper tbody')
      this.sortable = Sortable.create(tbody, {
        handle: '.handle', // 拖动手柄
        animation: 150,
        onEnd: ({ oldIndex, newIndex }) => {
          const currRow = this.parameters.splice(oldIndex, 1)[0]
          this.parameters.splice(newIndex, 0, currRow)
        }
      })
    },

    // 修改数据集选择的处理方法
    async handleDatasetChange(datasetId) {
      if (!datasetId) return

      try {
        const data = await getDatasetDetail(datasetId)
        if (data) {
          // 自动填充数据集的名称和描述
          this.editForm.name = data.name || ''
          this.editForm.description = data.description || ''

          // 如果是编辑模式，同时更新当前API的信息
          if (this.currentApi) {
            this.currentApi.name = data.name || ''
            this.currentApi.description = data.description || ''
            this.currentApi.datasetName = data.name
            this.currentApi.datasetNamespace = data.namespace

            // 更新 API 路径
            const defaultUri = `${data.namespace || ''}/${data.mappedId}`
            // 如果 uri 为空或等于旧的默认值，则更新为新的默认值
            if (!this.uri || this.uri === this.apiPath) {
              this.uri = defaultUri
              this.apiPath = defaultUri
            }

            // 解析SQL参数
            if (data.mappedContent) {
              this.handleDatasetSqlParse(data.mappedContent)
            }
          }
        }
      } catch (error) {
        console.error('获取数据集详情失败:', error)
        this.$message.warning('获取数据集详情失败：' + error.message)
      }
    },

    // 添加发布状态变更处理方法
    async handlePublishChange(value) {
      if (!this.currentApi) return
      console.log(value)

      try {
        await updateApi({
          id: this.currentApi.id,
          published: value
        })

        this.$message.success(value ? '发布成功' : '取消发布成功')
        // 更新树节点状态
        await this.fetchApiTree()
      } catch (error) {
        // 恢复原状态
        this.currentApi.published = !value
        this.$message.error((value ? '发布' : '取消发布') + '失败：' + error.message)
      }
    },

    // 处理路径变更
    handlePathChange(value) {
      this.apiPath = value
      this.uri = value // 同时更新 uri
      if (this.currentApi) {
        this.currentApi.uri = value // 确保 currentApi 中的 uri 也同步更新
      }
    },

    // 添加处理参数启用状态变更的方法
    handleParamEnableChange(param) {
      // 这里可以添加一些额外的处理逻辑
      console.log(`Parameter ${param.name} enabled: ${param.enabled}`)
    },

    // 处理编辑响应
    handleEditResponse() {
      this.isEditingResponse = true
      this.editingResponseText = JSON.stringify(this.apiResponse, null, 2)
    },

    // 取消编辑响应
    cancelEditResponse() {
      this.isEditingResponse = false
      this.editingResponseText = ''
    },

    // 保存编辑后的响应
    saveEditResponse() {
      try {
        // 尝试解析JSON
        const parsedResponse = JSON.parse(this.editingResponseText)
        this.apiResponse = parsedResponse
        this.isEditingResponse = false
        this.$message.success('响应结果更新成功')
      } catch (error) {
        this.$message.error('JSON格式不正确，请检查后重试')
      }
    },

    async runApi() {
      if (!this.currentApi) {
        this.$message.warning('请先选择一个 API')
        return
      }

      try {
        // 这里添加运行 API 的逻辑
        this.$message.success('API 运行成功')
      } catch (error) {
        this.$message.error('API 运行失败: ' + error.message)
      }
    },

    handleDescriptionChange(value) {
      if (this.currentApi) {
        this.currentApi.description = value
      }
    },
  },
  computed: {
    viewDialogTitle() {
      return this.viewType === 'group' ? '文件夹详情' : 'API详情'
    },
    editDialogTitle() {
      const type = this.editType === 'group' ? '文件夹' : 'API'
      return `编辑${type}`
    },
    getDatasetTagType() {
      if (!this.currentApi?.datasetId) return 'info'
      if (!this.currentApi?.datasetName) return 'info'

      // 使用更优雅的颜色
      const colors = [
        'warning',   // 橙色
        'primary',   // 蓝色
        'purple',    // 紫色
        'cyan',      // 青色
        'indigo'     // 靛蓝色
      ]

      const hash = this.currentApi.datasetName.split('').reduce((acc, char) => {
        return char.charCodeAt(0) + ((acc << 5) - acc)
      }, 0)

      return colors[Math.abs(hash) % colors.length]
    },

    getDatasetDisplayName() {
      if (!this.currentApi?.datasetId) return '未绑定数据集'
      return this.currentApi.datasetName || '未知数据集'
    },

    // 获取请求方法的类型
    getMethodTagType() {
      const methodTypes = {
        'GET': 'success',    // 绿色
        'POST': 'warning',   // 橙色
        'PUT': 'primary',    // 紫色
        'DELETE': 'danger'   // 红色
      }
      return methodTypes['GET'] || 'info'
    }
  },
  components: {
    DatasetDetail
  }
}
</script>

<style lang="scss" scoped>
.api-management {
  height: calc(100vh - 50px);
  background: #f5f7fa;
  padding: 16px;

  .el-container {
    height: 100%;
  }
}

.api-tree-wrapper {
  position: relative;
  display: flex;
  height: 100%;
}

.api-tree-container {
  position: relative;
  min-width: 200px;
  max-width: 600px;
  transition: width 0.1s;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  margin-right: 16px;
  border: none;

  .tree-header {
    padding: 20px;
    border-bottom: 1px solid #eef1f7;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    flex-shrink: 0;

    .header-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      span {
        font-size: 18px;
        font-weight: 600;
        color: #1a1f36;
        letter-spacing: 0.5px;
        position: relative;
        padding-left: 12px;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .add-button {
        background: linear-gradient(135deg, #409EFF 0%, #64B5F6 100%);
        border: none;
        padding: 8px 12px;
        height: 32px;
        line-height: 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
        color: #fff;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }

        i {
          margin-right: 0;
          font-size: 12px;
          color: #fff;
          
          &.el-icon--right {
            margin-left: 4px;
          }
        }
      }
    }

    .search-box {
      .el-input {
        width: 100%;
        ::v-deep .el-input__inner {
          border-radius: 10px;
          height: 36px;
          background: #f7f9fc;
          border: 1px solid #e7ebf3;
          
          &:focus {
            background: #fff;
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }
        
        ::v-deep .el-input__prefix {
          left: 10px;
          i {
            color: #8492a6;
          }
        }
      }
    }
  }

  .tree-container {
    flex: 1;
    overflow: auto;
    padding: 16px;
    background-color: #fff;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 6px;
    }

    ::v-deep {
      .el-tree {
        background: transparent;
      }

      .el-tree-node__content {
        height: 32px;
        border-radius: 8px;
        margin: 2px 0;
        transition: all 0.2s ease;
        
        &:hover {
          background-color: #f5f7fa;
        }
      }
      
      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #ecf5ff !important;
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  width: 100%;
  min-width: 0;

  .node-content {
    flex: 1;
    display: flex;
    align-items: center;
    min-width: 0;
    margin-right: 8px;
    gap: 6px;

    i {
      flex-shrink: 0;
      color: #409EFF;
      font-size: 14px;
    }

    .node-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      min-width: 0;
      color: #606266;
      
      &.highlight {
        color: #409EFF;
        font-weight: 500;
      }
    }

    // 添加发布状态标签样式
    .publish-status-tag {
      margin-left: 4px;
      font-size: 12px;
      height: 20px;
      line-height: 18px;
      padding: 0 6px;
      border-radius: 10px;
      
      &.el-tag--success {
        color: #67c23a;
        background: #f0f9eb;
        border-color: #b3e19d;
      }
      
      &.el-tag--danger {
        color: #f56c6c;
        background: #fef0f0;
        border-color: #fab6b6;
      }
    }
  }

  .node-actions {
    flex-shrink: 0;
    opacity: 0;
    transition: opacity 0.3s;
    display: flex;
    align-items: center;
    gap: 4px;

    .el-button {
      padding: 2px 4px;
      margin: 0;

      &:hover {
        color: #f56c6c;
      }
    }
  }
}

::v-deep .el-tree-node__content {
  height: auto;
  min-height: 32px;
  padding: 4px 0;

  &:hover {
    .custom-tree-node .node-actions {
      opacity: 1;
    }
  }
}

::v-deep .el-tree-node {
  &.is-current > .el-tree-node__content {
    background-color: #ecf5ff;
    
    .custom-tree-node {
      .node-content {
        .node-label {
          color: #409EFF;
          font-weight: 500;
        }
      }
      
      .node-actions {
        opacity: 1;
      }
    }
  }
}

.api-detail-header {
  background: #fff;
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

  .api-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .method-tag {
      order: 1;
      height: 36px;
      line-height: 34px;
      font-weight: 600;
      padding: 0 12px;
      font-size: 13px;
    }

    .name-input-wrapper {
      order: 2;
      flex: 1;
      min-width: 0;
    }

    .el-switch {
      order: 3;
      margin-right: 12px;
    }

    .dataset-tag {
      order: 4;
      display: inline-flex;
      align-items: center;
      height: 36px;
      line-height: 34px;
      padding: 0 16px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.3s;
      white-space: nowrap;
      border-style: dashed;
      gap: 6px;

      i {
        font-size: 14px;
        transition: transform 0.3s;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

        i {
          transform: scale(1.1);
        }
      }

      // 未绑定时的样式
      &.el-tag--info {
        color: #409EFF;
        border-color: #409EFF;
        background-color: #ecf5ff;
        
        &::after {
          content: '点击绑定数据集';
          margin-left: 4px;
          font-size: 12px;
          color: #409EFF;
          opacity: 0.8;
        }

        i {
          color: #409EFF;
        }
      }

      // 已绑定时的优雅颜色样式
      &.el-tag--warning {
        border-style: solid;
      }

      &.el-tag--primary {
        border-style: solid;
      }

      &.el-tag--purple {
        border-style: solid;
      }

      &.el-tag--cyan {
        border-style: solid;
      }

      &.el-tag--indigo {
        border-style: solid;
      }
    }
  }

  .api-url-section {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 12px;

    .url-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      .url-info {
        flex: 1;

        .url-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #fff;
          padding: 8px 16px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;

          .url-path {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fff;
            padding: 8px 16px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;

            .base-url {
              color: #409EFF;
              font-family: monospace;
              font-size: 14px;
            }

            .path-input {
              width: 100%;
              margin-left: 8px;
              border: none;
              outline: none;
              font-size: 14px;
            }
          }

          .copy-button {
            color: #909399;

            &:hover {
              color: #409EFF;
            }
          }
        }
      }
    }
  }
}

.api-detail-container {
  position: relative;
  padding: 0;
  scrollbar-width: thin;
}

.resize-handle {
  width: 4px;
  height: calc(100% - 32px);
  background-color: transparent;
  cursor: col-resize;
  position: absolute;
  right: -2px;
  top: 16px;
  z-index: 100;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(64, 158, 255, 0.5);
  }

  &:active {
    background-color: #409EFF;
  }
}

.main-content {
  flex: 1;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  scrollbar-width: thin;

  .api-detail-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: transparent;
    box-shadow: none;
    gap: 16px;
  }
}

.api-detail-container {
  padding: 0;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.node-label {
  &.highlight {
    color: #409EFF;
    font-weight: 500;
  }
}

.response-section {
  display: flex;
  flex-direction: column;

  .card-content {
    padding: 20px;
  }

  .response-content {
    display: flex;
    gap: 16px;

    .response-schema,
    .response-result {
      width: 50%;
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .schema-header,
      .result-header {
        padding: 12px 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #ebeef5;
        font-weight: 500;
        color: #606266;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .schema-content,
      .result-content {
        padding: 16px;
      }
    }

    .response-result {
      .result-content {
        background: #fafafa;

        .json-viewer {
          margin: 0;
          font-family: Monaco, Menlo, Consolas, monospace;
          font-size: 13px;
          line-height: 1.6;
          color: #444;
          white-space: pre-wrap;
        }

        .no-response {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #909399;
          padding: 32px 0;

          i {
            font-size: 32px;
            margin-bottom: 12px;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

.no-api-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;

  .no-api-tip {
    text-align: center;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 16px;
    }
  }
}

::v-deep .el-table {
  .el-button--text {
    padding: 0;

    &:hover {
      color: #409EFF;
    }

    &.delete-button:hover {
      color: #f56c6c;
    }
  }
}

.card-container {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  border: none;
  transition: box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eef1f7;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    border-radius: 16px 16px 0 0;
    margin: 0;
    flex-shrink: 0;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }

    .section-actions {
      display: flex;
      align-items: center;
      gap: 12px;

      .el-button {
        margin-left: 0;
        
        &.el-button--primary {
          background: linear-gradient(135deg, #409EFF 0%, #64B5F6 100%);
          border: none;
          padding: 8px 16px;
          height: 32px;
          line-height: 16px;
          border-radius: 8px;
          transition: all 0.3s ease;
          font-weight: 500;
          color: #fff;
          
          i {
            margin-right: 4px;
            font-size: 14px;
          }
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }

        &.el-button--plain {
          background: linear-gradient(135deg, #409EFF 0%, #64B5F6 100%);
          border: none;
          padding: 8px 16px;
          height: 32px;
          line-height: 16px;
          border-radius: 8px;
          transition: all 0.3s ease;
          font-weight: 500;
          color: #fff;
          
          i {
            margin-right: 4px;
            font-size: 14px;
          }
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }
      }

      .el-tag {
        height: 28px;
        line-height: 26px;
        border-radius: 14px;
        padding: 0 12px;
        margin-right: 8px;
        
        &:last-of-type {
          margin-right: 16px;
        }
      }
    }
  }

  .card-content {
    padding: 20px;
    flex: 1;
    min-height: 0;
  }
}

.params-section {
  .el-table {
    margin-top: 0;
  }
}

.response-section {
  display: flex;
  flex-direction: column;
  overflow: visible;

  .card-content {
    display: flex;
    flex-direction: column;
  }

  .response-content {
    display: flex;
    gap: 16px;

    .response-schema,
    .response-result {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #eef1f7;
      border-radius: 12px;
      overflow: hidden;

      .schema-header,
      .result-header {
        padding: 16px;
        background: linear-gradient(to right, #fcfcfd, #f9fafc);
        border-bottom: 1px solid #eef1f7;
        font-weight: 600;
        color: #1a1f36;
      }

      .schema-content,
      .result-content {
        padding: 20px;
      }
    }

    .response-result {
      .result-content {
        background: #fcfcfd;

        .json-viewer {
          font-family: Monaco, Menlo, Consolas, monospace;
          font-size: 13px;
          line-height: 1.6;
          color: #1a1f36;
        }
      }
    }
  }
}

::v-deep .el-table {
  border-radius: 12px;
  overflow: hidden;

  th {
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    color: #1a1f36;
    font-weight: 600;
    padding: 16px;
  }

  td {
    padding: 12px 16px;
  }
}

::v-deep .el-tag {
  text-transform: capitalize;
  font-family: Monaco, Menlo, Consolas, monospace;

  &.el-tag--light {
    border-width: 1px;

    &.el-tag--success {
      background-color: #f0f9eb;
    }

    &.el-tag--warning {
      background-color: #fdf6ec;
    }

    &.el-tag--info {
      background-color: #f4f4f5;
    }

    &.el-tag--primary {
      background-color: #ecf5ff;
    }

    &.el-tag--danger {
      background-color: #fef0f0;
    }
  }
}

.params-section {
  .handle {
    color: #909399;
    cursor: move;

    &:hover {
      color: #409EFF;
    }
  }

  ::v-deep .el-table__row {
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  ::v-deep .el-switch__core {
    width: 40px;
    height: 20px;
  }
}

.url-content {
  .url-path {
    display: flex;
    align-items: center;
    flex: 1;

    .base-url {
      color: #606266;
      margin-right: 4px;
      font-family: monospace;
    }

    .path-input {
      flex: 1;

      ::v-deep .el-input__inner {
        border: none;
        background: transparent;
        color: #409EFF;
        font-family: monospace;
        padding: 0;

        &:hover,
        &:focus {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

.url-path {
  display: flex;
  align-items: center;
  flex: 1;
  background: #fcfcfd;
  border-radius: 12px;
  border: 1px solid #eef1f7;
  padding: 16px;

  .base-url {
    color: #1a1f36;
    font-weight: 500;
  }

  .path-input {
    flex: 1;
    margin-left: 16px;

    ::v-deep .el-input__inner {
      border: none;
      background: transparent;
      color: #409EFF;
      font-family: monospace;
      padding: 0;
      font-weight: 500;

      &:hover,
      &:focus {
        background-color: #f5f7fa;
      }
    }
  }
}

// 删除重复的样式定义
.url-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;

  .copy-button {
    flex-shrink: 0;
  }
}

.response-editor {
  display: flex;
  flex-direction: column;
  height: 100%;

  .el-input {
    flex: 1;

    ::v-deep .el-textarea__inner {
      font-family: Monaco, Menlo, Consolas, monospace;
      font-size: 13px;
      line-height: 1.6;
      color: #444;
      padding: 12px;
      border-radius: 4px;
      background-color: #fafafa;
      resize: none;

      &:focus {
        border-color: #409EFF;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.15);
      }
    }
  }

  .editor-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid #ebeef5;
    margin-top: 12px;
  }
}

.result-actions {
  display: flex;
  gap: 8px;

  .el-button {
    font-size: 16px;

    &:hover {
      color: #409EFF;
    }
  }
}

.response-result {
  .result-content {
    height: 400px;
    overflow: auto;
    scrollbar-width: thin;


  }
}

.api-description {
  margin-top: 16px;

  .description-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    font-weight: 600;
    color: #1a1f36;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      color: #409EFF;
    }

    i {
      font-size: 12px;
      transition: transform 0.3s;
    }
  }

  .description-content {
    padding: 8px 0;

    .el-textarea {
      width: 100%;

      ::v-deep .el-textarea__inner {
        border-radius: 12px;
        border-color: #eef1f7;
        background: #fcfcfd;
        
        &:hover {
          border-color: #c0d0e9;
        }
        
        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
        }
      }
    }
  }
}
</style>