<template>
  <el-form :model="form" size="small" ref="form">
    <div style="display: flex; align-items: center; justify-content: space-between;">
      <label class="el-form-item__label">缓存设置</label>
      <el-button @click="$refs.dynamicData.addKey()" size="small" type="primary" plain>
        <i class="el-icon-plus"></i>
      </el-button>
    </div>
    <el-row>
      <DynamicData v-model="form.store" ref="dynamicData" style="width: 350px;" :value-options="valueOptions"/>
    </el-row>
  </el-form>
</template>

<script>
import { isEqual } from 'element-ui/src/utils/util';
import DynamicData from '@system/components/DynamicData'
export default {
  components: {
    DynamicData
  },
  data() {
    return {
      form: {},
      valueOptions: [
        {
          label: '系统时间字符串',
          value: 'T(java.sql.Timestamp).valueOf(T(java.time.LocalDateTime).now())'
        },
        {
          label: '系统日期字符串',
          value: 'T(java.time.LocalDate).now()'
        },
        {
          label: '系统时间戳(毫秒)',
          value: 'T(System).currentTimeMillis()'
        },
        {
          label: '系统时间戳(秒)',
          value: 'T(System).currentTimeMillis() / 1000'
        },
      ]
    }
  },
  props: {
    properties: {
        type: Object,
        default: () => ({})
    },
    node: {
        type: Object,
        default: () => ({})
    }
  },
  methods: {
    getProperties() {
      return this.form
    },
    async validate() {
        return new Promise(resolve => {
            this.$refs.form.validate(valid => {
                resolve(valid)
            })
        })
    }
  },
  watch: {
    properties: {
        handler(newVal) {
            if (!isEqual(newVal, this.form)) {
                this.form = { ...this.form, ...newVal }
            }
        },
        immediate: true,
        deep: true
    },
  },
}
</script>