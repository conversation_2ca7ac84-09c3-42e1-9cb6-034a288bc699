// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar
$menuText: #bfcbd9;
$menuActiveText: #409EFF;
$subMenuActiveText: #f4f4f5; // https://github.com/ElemeFE/element/issues/12951

$menuBg: rgba(255, 255, 255, 1);
$menuHover: rgba(67, 101, 220, 0.15);
$sidebarTitle: #ffffff;

$menuLightBg: #ffffff;
$menuLightHover: #f0f1f5;
$sidebarLightTitle: #001529;

$subMenuBg: rgba(255, 255, 255, 1);
$subMenuHover: rgba(67, 101, 220, 0.15);

$sideBarWidth: 260px;



// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  sidebarTitle: $sidebarTitle;
  sidebarLightTitle: $sidebarLightTitle
}
