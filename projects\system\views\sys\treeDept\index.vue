<template>
  <div class="department-management">
    <!-- 左侧部门树 -->
    <div class="department-tree">
      <div class="tree-header">
        <div class="header-title">
          <span>部门列表</span>
          <el-button type="primary" size="small" @click="handleAdd">
            <i class="el-icon-plus"></i> 新增部门
          </el-button>
        </div>
        <div class="search-box">
          <el-input v-model="searchKeyword" placeholder="搜索部门名称/负责人" prefix-icon="el-icon-search" clearable size="small"
            @clear="handleSearchClear" />
        </div>
      </div>
      <div class="tree-container">
        <el-tree ref="departmentTree" :data="treeData" :props="defaultProps" :filter-node-method="filterNode"
          node-key="id" highlight-current draggable :allow-drop="allowDrop" :allow-drag="allowDrag"
          :default-expanded-keys="expandedKeys" @node-click="handleNodeClick" @node-contextmenu="handleContextMenu"
          @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" @node-drag-start="handleDragStart"
          @node-drag-enter="handleDragEnter" @node-drag-leave="handleDragLeave" @node-drag-end="handleDragEnd"
          @node-drop="handleDrop">
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <div class="node-content">
              <i class="el-icon-office-building"></i>
              <span :class="{ 'highlight': isHighlighted(node, searchKeyword) }">
                {{ node.label }}
              </span>
            </div>
            <div class="node-actions">
              <el-tooltip content="查看详情" placement="top">
                <el-button size="mini" type="text" @click.stop="handleView(data)">
                  <i class="el-icon-view"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <el-button size="mini" type="text" @click.stop="handleEdit(data)">
                  <i class="el-icon-edit"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button size="mini" type="text" class="danger" @click.stop="handleDelete(node, data)">
                  <i class="el-icon-delete"></i>
                </el-button>
              </el-tooltip>
            </div>
          </span>
        </el-tree>
      </div>
    </div>

    <!-- 右侧部门详情 -->
    <div class="department-detail" v-if="currentDepartment">
      <div class="detail-content">
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-info"></i>
            <span>基本信息</span>
            <div class="section-actions">
              <el-button type="primary" size="small" icon="el-icon-edit" class="action-button"
                @click="handleEdit(currentDepartment)">
                编辑部门
              </el-button>
              <el-button type="danger" size="small" icon="el-icon-delete" class="action-button"
                @click="handleDelete(null, currentDepartment)">
                删除部门
              </el-button>
            </div>
          </div>
          <div class="info-list">
            <div class="info-item">
              <label>部门名称：</label>
              <div class="value-with-icon">
                <i class="el-icon-office-building" style="color: #409EFF;"></i>
                <span>{{ currentDepartment.name }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>部门编码：</label>
              <div class="value-with-icon">
                <i class="el-icon-collection-tag" style="color: #67C23A;"></i>
                <span>{{ currentDepartment.code }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>排序号：</label>
              <div class="value-with-icon">
                <i class="el-icon-sort" style="color: #E6A23C;"></i>
                <span>{{ currentDepartment.sort }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>负责人：</label>
              <div class="value-with-icon">
                <i class="el-icon-user" style="color: #F56C6C;"></i>
                <el-popover placement="right" width="300" trigger="hover" popper-class="leader-popover"
                  :append-to-body="false" v-if="currentDepartment.leaderId != null">
                  <div class="leader-card" v-loading="leaderLoading">
                    <template v-if="leaderInfo">
                      <div class="leader-header">
                        <el-avatar :size="60" :src="leaderInfo.picUrl">
                          {{ leaderInfo.name ? leaderInfo.name.charAt(0) : 'U' }}
                        </el-avatar>
                        <div class="leader-basic">
                          <h3>{{ leaderInfo.nickName || leaderInfo.name }}</h3>
                          <p>{{ leaderInfo.username }}</p>
                        </div>
                      </div>
                      <div class="leader-info">
                        <div class="info-row">
                          <i class="el-icon-phone"></i>
                          <span>{{ leaderInfo.phone || '暂无' }}</span>
                        </div>
                        <div class="info-row">
                          <i class="el-icon-message"></i>
                          <span>{{ leaderInfo.email || '暂无' }}</span>
                        </div>
                        <div class="info-row">
                          <i class="el-icon-time"></i>
                          <span>最后登录：{{ leaderInfo.lastLoginTime || '暂无' }}</span>
                        </div>
                      </div>
                    </template>
                  </div>
                  <span slot="reference" class="leader-name">{{ leaderInfo ? (leaderInfo.nickName || leaderInfo.name) :
                    currentDepartment.leaderId }}</span>
                </el-popover>
                <span v-else>暂无</span>
                <el-tooltip content="鼠标悬停查看详细信息" placement="top" v-if="currentDepartment.leaderId != null">
                  <i class="el-icon-info-circle hover-tip"></i>
                </el-tooltip>
              </div>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <div class="value-with-icon">
                <i class="el-icon-time"></i>
                <span>{{ currentDepartment.createTime }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>更新时间：</label>
              <div class="value-with-icon">
                <i class="el-icon-time"></i>
                <span>{{ currentDepartment.updateTime }}</span>
              </div>
            </div>
            <div class="info-item full">
              <label>备注说明：</label>
              <div class="value-with-icon">
                <i class="el-icon-document"></i>
                <span>{{ currentDepartment.remark || '暂无描述' }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-office-building"></i>
            <span>下属部门</span>
            <span class="member-count" v-if="currentDepartment.children">
              共 {{ currentDepartment.children.length }} 个
            </span>
          </div>
          <el-table :data="paginatedChildren" style="width: 100%" border stripe highlight-current-row
            height="calc(100% - 110px)" class="custom-table">
            <el-table-column prop="code" label="部门编码" :width=rpx(100) align="center">
              <template slot-scope="{ row }">
                <el-tag size="medium" effect="plain">{{ row.code }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="部门名称" min-width="120">
              <template slot-scope="{ row }">
                <div class="dept-info">
                  <i class="el-icon-office-building"></i>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="leaderId" label="负责人" width="120">
              <template slot-scope="{ row }">
                <el-popover placement="right" width="300" trigger="hover" popper-class="leader-popover"
                  :append-to-body="false" v-if="row.leaderId != null">
                  <div class="leader-card" v-loading="row.leaderLoading">
                    <template v-if="row.leaderInfo">
                      <div class="leader-header">
                        <el-avatar :size="60" :src="row.leaderInfo.picUrl">
                          {{ row.leaderInfo.name ? row.leaderInfo.name.charAt(0) : 'U' }}
                        </el-avatar>
                        <div class="leader-basic">
                          <h4>{{ row.leaderInfo.nickName || row.leaderInfo.name || row.leaderInfo.username }}</h4>
                          <p>{{ row.leaderInfo.username }}</p>
                        </div>
                      </div>
                      <div class="leader-info">
                        <div class="info-row">
                          <i class="el-icon-phone"></i>
                          <span>{{ row.leaderInfo.phone || '暂无' }}</span>
                        </div>
                        <div class="info-row">
                          <i class="el-icon-message"></i>
                          <span>{{ row.leaderInfo.email || '暂无' }}</span>
                        </div>
                        <div class="info-row">
                          <i class="el-icon-time"></i>
                          <span>最后登录：{{ row.leaderInfo.lastLoginTime || '暂无' }}</span>
                        </div>
                      </div>
                    </template>
                  </div>
                  <span slot="reference" class="leader-name">
                    {{ row.leaderInfo ? (row.leaderInfo.nickName || row.leaderInfo.name || row.leaderInfo.username) : '未知' }}
                  </span>
                </el-popover>
                <span v-else>暂无</span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注说明" min-width="200" show-overflow-tooltip />
            <el-table-column label="操作" width="220" align="center" class-name="operation-column">
              <template slot-scope="{ row }">
                <el-tooltip content="查看详情" placement="top">
                  <el-button size="mini" type="text" @click="handleView(row)">
                    <i class="el-icon-view"></i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="编辑" placement="top">
                  <el-button size="mini" type="text" @click="handleEdit(row)">
                    <i class="el-icon-edit"></i>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button size="mini" type="text" class="danger" @click="handleDelete(null, row)">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="currentDepartment.children ? currentDepartment.children.length : 0" background>
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑部门对话框 -->
    <el-dialog :title="dialogType === 'add' ? '新增部门' : '编辑部门'" :visible.sync="dialogVisible" width="650px"
      :close-on-click-modal="false" :append-to-body="true" custom-class="department-dialog">
      <el-form ref="departmentForm" :model="departmentForm" :rules="formRules" label-width="100px"
        class="department-form">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-form-item label="部门名称" prop="name">
            <el-input v-model="departmentForm.name" placeholder="请输入部门名称" class="custom-input">
              <template slot="prepend">
                <i class="el-icon-office-building"></i>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="部门编码" prop="code">
            <el-input v-model="departmentForm.code" placeholder="请输入部门编码" class="custom-input">
              <template slot="prepend">
                <i class="el-icon-collection-tag"></i>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="上级部门" prop="parentId">
            <el-cascader v-model="departmentForm.parentId" :options="disabledCurrentDeptTree" :props="{
              checkStrictly: true,
              label: 'name',
              value: 'id',
              emitPath: false
            }" placeholder="请选择上级部门" clearable class="custom-cascader">
              <template slot-scope="{ node, data }">
                <i class="el-icon-office-building"></i>
                <span>{{ data.name }}</span>
                <span class="dept-code">({{ data.code }})</span>
              </template>
            </el-cascader>
          </el-form-item>

          <el-form-item label="排序号" prop="sort">
            <el-input-number v-model="departmentForm.sort" :min="0" :max="999" controls-position="right"
              class="custom-number-input" />
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="section-title">其他信息</div>
          <el-form-item label="负责人" prop="leaderId">
            <el-select v-model="departmentForm.leaderId" filterable placeholder="请选择负责人" class="custom-select"
              @focus="handleLeaderFocus" :loading="userLoading" clearable>
              <el-option v-for="item in userOptions" :key="item.id" :label="item.nickName || item.name || item.username"
                :value="item.id">
                <div class="user-option">
                  <el-avatar :size="28" :src="item.picUrl">
                    {{ (item.nickName || item.name) ? (item.nickName || item.name).charAt(0) : 'U' }}
                  </el-avatar>
                  <div class="user-info">
                    <span class="name">{{ item.nickName || item.name || item.username }}</span>
                    <span class="extra-info">
                      <i class="el-icon-phone"></i>
                      {{ item.phone || '暂无联系方式' }}
                    </span>
                  </div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="备注说明" prop="remark">
            <el-input v-model="departmentForm.remark" type="textarea" :rows="3" placeholder="请输入备注说明"
              class="custom-textarea" />
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 添加查看详情对话框 -->
    <el-dialog title="部门详情" :visible.sync="detailDialogVisible" width="600px" :append-to-body="true"
      custom-class="department-detail-dialog">
      <div class="detail-content">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <div class="info-item">
            <label>部门名称：</label>
            <div class="value-with-icon">
              <i class="el-icon-office-building" style="color: #409EFF;"></i>
              <span>{{ detailData.name }}</span>
            </div>
          </div>
          <div class="info-item">
            <label>部门编码：</label>
            <div class="value-with-icon">
              <i class="el-icon-collection-tag" style="color: #67C23A;"></i>
              <span>{{ detailData.code }}</span>
            </div>
          </div>
          <div class="info-item">
            <label>排序号：</label>
            <div class="value-with-icon">
              <i class="el-icon-sort" style="color: #E6A23C;"></i>
              <span>{{ detailData.sort }}</span>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="section-title">其他信息</div>
          <div class="info-item">
            <label>负责人：</label>
            <div class="value-with-icon">
              <i class="el-icon-user" style="color: #F56C6C;"></i>
              <span>{{ detailData.leaderInfo ? (detailData.leaderInfo.nickName || detailData.leaderInfo.name) : '暂无' }}</span>
            </div>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <div class="value-with-icon">
              <i class="el-icon-time"></i>
              <span>{{ detailData.createTime }}</span>
            </div>
          </div>
          <div class="info-item">
            <label>更新时间：</label>
            <div class="value-with-icon">
              <i class="el-icon-time"></i>
              <span>{{ detailData.updateTime }}</span>
            </div>
          </div>
          <div class="info-item">
            <label>备注说明：</label>
            <div class="value-with-icon">
              <i class="el-icon-document"></i>
              <span>{{ detailData.remark || '暂无描述' }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDeptTreeList, getUserInfo, getUserList, getDeptUsers, addDept, updateDept, deleteDept } from '@system/api/sys/treeDept'

export default {
  name: 'DepartmentManagement',
  data() {
    return {
      searchKeyword: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      currentDepartment: null,
      dialogVisible: false,
      dialogType: 'add',
      departmentForm: {
        name: '',
        code: '',
        parentId: '',
        leaderId: '',
        remark: '',
        sort: 0
      },
      formRules: {
        name: [
          { required: true, message: '请输入部门名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '部门名称不能包含空格', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入部门编码', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '部门编码不能包含空格', trigger: 'blur' }
        ],
      },
      submitLoading: false,
      departmentOptions: [],
      managerOptions: [
        {
          id: '1',
          name: '张三',
          position: '总经理',
          avatar: ''
        },
        {
          id: '2',
          name: '李四',
          position: '技术总监',
          avatar: ''
        },
        {
          id: '3',
          name: '王五',
          position: '研发经理',
          avatar: ''
        }
      ],
      leaderInfo: null,
      leaderLoading: false,
      userOptions: [],
      userLoading: false,
      detailDialogVisible: false,
      detailData: {},
      currentPage: 1,
      pageSize: 10,
      expandedKeys: []
    }
  },
  computed: {
    paginatedChildren() {
      if (!this.currentDepartment || !this.currentDepartment.children) {
        return [];
      }
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.currentDepartment.children.slice(start, end);
    },
    disabledCurrentDeptTree() {
      if (this.dialogType !== 'edit' || !this.departmentForm.id) {
        return this.treeData;
      }
      
      // 深拷贝树数据
      const cloneData = JSON.parse(JSON.stringify(this.treeData));
      
      // 递归禁用当前部门及其子部门
      const disableCurrentAndChildren = (nodes, targetId) => {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === targetId) {
            nodes[i].disabled = true;
            return true;
          }
          
          if (nodes[i].children && nodes[i].children.length > 0) {
            const found = disableCurrentAndChildren(nodes[i].children, targetId);
            if (found) {
              return true;
            }
          }
        }
        return false;
      };
      
      // 查找并标记当前部门节点为disabled
      disableCurrentAndChildren(cloneData, this.departmentForm.id);
      
      // 递归禁用当前部门的所有子节点
      const disableChildrenNodes = (nodes, parentId) => {
        const parent = this.findDepartmentById(this.treeData, parentId);
        if (!parent || !parent.children) return;
        
        // 为每个子节点禁用
        const allChildren = this.getAllChildrenIds(parent);
        
        const recursiveDisable = (treeNodes, disabledIds) => {
          for (let i = 0; i < treeNodes.length; i++) {
            if (disabledIds.includes(treeNodes[i].id)) {
              treeNodes[i].disabled = true;
            }
            
            if (treeNodes[i].children && treeNodes[i].children.length > 0) {
              recursiveDisable(treeNodes[i].children, disabledIds);
            }
          }
        };
        
        recursiveDisable(cloneData, allChildren);
      };
      
      // 禁用当前部门的所有子节点
      disableChildrenNodes(cloneData, this.departmentForm.id);
      
      return cloneData;
    },
  },
  created() {
    this.getList()
  },
  methods: {
    // 保存当前展开的节点
    saveExpandedState() {
      if (this.$refs.departmentTree &&
        this.$refs.departmentTree.store &&
        this.$refs.departmentTree.store.states &&
        this.$refs.departmentTree.store.states.expandedNodes) {
        // 获取所有当前展开的节点的key
        const expandedNodes = this.$refs.departmentTree.store.states.expandedNodes;
        this.expandedKeys = expandedNodes.map(node => node.data.id);
      }
    },

    // 恢复之前展开的节点
    restoreExpandedKeys() {
      // 不需要手动处理，由:default-expanded-keys控制
    },

    // 允许拖拽的节点
    allowDrag(node) {
      // 所有类型的节点都可以拖拽
      return true
    },

    // 允许放置的位置
    allowDrop(draggingNode, dropNode, type) {
      // 允许所有拖拽操作
      return true
    },

    // 开始拖拽
    handleDragStart(node, ev) {
      console.log('drag start', node)
    },

    // 拖拽进入其他节点
    handleDragEnter(draggingNode, dropNode, ev) {
      console.log('tree drag enter: ', dropNode.label)
    },

    // 拖拽离开节点
    handleDragLeave(draggingNode, dropNode, ev) {
      console.log('tree drag leave: ', dropNode.label)
    },

    // 拖拽结束
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      console.log('tree drag end: ', dropType)
    },

    // 放置节点
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      if (!draggingNode || !dropNode) {
        console.warn('拖拽操作缺少必要的节点信息');
        return;
      }

      const draggingData = draggingNode.data
      const dropData = dropNode.data

      if (!draggingData || !dropData) {
        console.warn('拖拽操作缺少必要的数据信息');
        return;
      }

      // 计算新的 parentId 和 sort
      let newParentId = 0
      let newSort = 0

      if (dropType === 'inner') {
        // 放到目标节点内部
        newParentId = dropData.id
        newSort = dropData.children ? dropData.children.length : 0
      } else {
        // 放到目标节点的前面或后面
        newParentId = dropData.parentId || 0

        if (dropType === 'before') {
          // 放到目标节点前面，将 sort 设置为目标节点的 sort
          newSort = dropData.sort
        } else {
          // 放到目标节点后面，将 sort 设置为目标节点的 sort + 1
          newSort = dropData.sort + 1
        }
      }

      // 构建确认信息
      const confirmMessage = dropType === 'inner' 
        ? `确认将 "${draggingData.name}" 移动到 "${dropData.name}" 内部吗？`
        : dropType === 'before' 
          ? `确认将 "${draggingData.name}" 移动到 "${dropData.name}" 前面吗？`
          : `确认将 "${draggingData.name}" 移动到 "${dropData.name}" 后面吗？`

      // 显示确认对话框
      this.$confirm(confirmMessage, '移动确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 用户确认后执行移动操作
        
        // 在更新前保存当前节点的展开状态
        this.saveExpandedState()

        // 记住当前选中的节点
        const currentId = draggingData.id

        try {
          // 为了避免 sort 值相同导致的问题，先获取同级所有节点
          let siblingNodes = []

          // 查找兄弟节点
          const findSiblings = (nodes, parentId) => {
            for (let node of nodes) {
              if (node.id === parentId) {
                return node.children || []
              }
              if (node.children && node.children.length > 0) {
                const found = findSiblings(node.children, parentId)
                if (found.length > 0) return found
              }
            }
            if (parentId === 0 || parentId === null) {
              return this.treeData.filter(item => !item.parentId || item.parentId === 0)
            }
            return []
          }

          siblingNodes = findSiblings(this.treeData, newParentId)

          // 创建更新操作队列
          const updateQueue = []

          // 添加拖拽节点更新
          updateQueue.push({
            ...draggingData,
            parentId: newParentId,
            sort: newSort
          })

          // 如果是前插或后插，需要更新其他受影响节点的排序
          if (dropType !== 'inner') {
            // 找到受影响的节点（排序值需要调整的节点）
            const affectedNodes = siblingNodes.filter(node => {
              // 排除当前拖动的节点
              if (node.id === draggingData.id) return false

              if (dropType === 'before') {
                // 所有原本排序值 >= 目标节点排序值的其他节点需要+1
                return node.sort >= newSort
              } else {
                // 所有原本排序值 > 目标节点排序值的其他节点需要+1
                return node.sort > dropData.sort
              }
            })

            // 将所有需要更新的节点添加到队列
            affectedNodes.forEach(node => {
              updateQueue.push({
                ...node,
                sort: node.sort + 1
              })
            })
          }

          try {
            await updateDept(updateQueue)

            this.$message.success('移动成功')

            // 重新加载树数据
            await this.getList()

            // 确保当前节点仍然选中
            this.$nextTick(() => {
              if (this.$refs.departmentTree) {
                this.$refs.departmentTree.setCurrentKey(currentId)
                // 查找更新后的节点数据
                const findNodeById = (nodes, id) => {
                  for (let node of nodes) {
                    if (node.id === id) {
                      return node
                    }
                    if (node.children && node.children.length > 0) {
                      const found = findNodeById(node.children, id)
                      if (found) return found
                    }
                  }
                  return null
                }

                const updatedNode = findNodeById(this.treeData, currentId)
                if (updatedNode) {
                  this.handleNodeClick(updatedNode)
                }
              }
            })
          } catch (error) {
            console.error('批量更新节点失败', error)
            this.$message.error('移动失败：批量更新节点时出错')
            // 重新加载树数据，恢复原状
            await this.getList()
          }
        } catch (error) {
          console.error('移动处理失败', error)
          this.$message.error('移动失败：' + (error.message || '未知错误'))
          // 重新加载树数据，恢复原状
          await this.getList()
        }
      }).catch(() => {
        // 用户取消操作，重新加载树数据恢复原状
        this.getList()
      })
    },

    // 节点展开时的处理
    handleNodeExpand(data, node) {
      // 将展开的节点 id 添加到 expandedKeys 数组中
      if (data && data.id && !this.expandedKeys.includes(data.id)) {
        this.expandedKeys.push(data.id);
      }
    },

    // 节点收起时的处理
    handleNodeCollapse(data, node) {
      // 从 expandedKeys 数组中移除收起的节点 id
      if (data && data.id) {
        const index = this.expandedKeys.indexOf(data.id);
        if (index !== -1) {
          this.expandedKeys.splice(index, 1);
        }
      }
    },

    // 获取部门树列表
    async getList() {
      try {
        const data = await getDeptTreeList()
        this.treeData = data

        // 如果有数据，选中第一个节点
        if (this.treeData && this.treeData.length > 0) {
          this.$nextTick(() => {
            // 如果没有当前选中的部门或者当前部门已被删除，则选中第一个节点
            if (!this.currentDepartment || !this.findDepartmentById(this.treeData, this.currentDepartment.id)) {
              const firstNode = this.treeData[0]
              this.$refs.departmentTree.setCurrentKey(firstNode.id)
              this.handleNodeClick(firstNode)
            } else {
              // 否则重新选中当前部门
              this.$refs.departmentTree.setCurrentKey(this.currentDepartment.id)
            }
          })
        }
      } catch (error) {
        this.$message.error('获取部门列表失败')
      }
    },

    // 根据ID查找部门
    findDepartmentById(departments, id) {
      for (const dept of departments) {
        if (dept.id === id) {
          return dept;
        }
        if (dept.children && dept.children.length > 0) {
          const found = this.findDepartmentById(dept.children, id);
          if (found) return found;
        }
      }
      return null;
    },

    // 初始化部门选项
    initDepartmentOptions() {
      this.departmentOptions = this.formatDepartmentOptions(this.treeData)
    },

    // 格式化部门选项
    formatDepartmentOptions(data) {
      return data.map(item => {
        const node = {
          id: item.id,
          name: item.name
        }
        if (item.children) {
          node.children = this.formatDepartmentOptions(item.children)
        }
        return node
      })
    },

    // 搜索过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase()) ||
        (data.leaderId && data.leaderId.toString().includes(value))
    },

    // 清除搜索
    handleSearchClear() {
      this.$refs.departmentTree.filter('')
    },

    // 判断节点是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false
      return node.label.toLowerCase().includes(keyword.toLowerCase())
    },

    // 添加或修改获取头像背景色的方法
    getAvatarColor(name) {
      // 预定义一组好看的渐变色
      const gradients = [
        'linear-gradient(135deg, #FF9A9E 0%, #FAD0C4 100%)',
        'linear-gradient(135deg, #A18CD1 0%, #FBC2EB 100%)',
        'linear-gradient(135deg, #FF9A9E 0%, #FAD0C4 100%)',
        'linear-gradient(135deg, #FFECD2 0%, #FCB69F 100%)',
        'linear-gradient(135deg, #84FAB0 0%, #8FD3F4 100%)',
        'linear-gradient(135deg, #88D3CE 0%, #6E45E2 100%)',
        'linear-gradient(135deg, #D4FC79 0%, #96E6A1 100%)',
        'linear-gradient(135deg, #E0C3FC 0%, #8EC5FC 100%)',
        'linear-gradient(135deg, #F093FB 0%, #F5576C 100%)',
        'linear-gradient(135deg, #43E97B 0%, #38F9D7 100%)'
      ]

      // 根据名字生成索引
      if (!name) return gradients[0]
      const index = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % gradients.length
      return gradients[index]
    },

    // 修改节点点击方法
    async handleNodeClick(data, node) {
      this.currentDepartment = data
      this.leaderInfo = null
      this.currentPage = 1 // 重置分页

      if (data.leaderId != null) {
        await this.getLeaderInfo(data.leaderId)
      }

      // 如果有子部门，批量获取负责人信息
      if (data.children && data.children.length > 0) {
        await this.getChildrenLeaders(data.children)
      }
    },

    // 添加批量获取子部门负责人信息的方法
    async getChildrenLeaders(children) {
      // 收集所有需要获取信息的负责人ID
      const leaderIds = children
        .filter(item => item.leaderId != null)
        .map(item => item.leaderId)

      if (leaderIds.length === 0) return

      try {
        const data = await getUserList(leaderIds)
        // 从分页数据中获取用户列表
        const leaderMap = new Map(data.records.map(leader => [leader.id, {
          ...leader,
          picUrl: leader.picUrl ? process.env.VUE_APP_FILE_URL + '/' + leader.picUrl : ''
        }]))

        // 更新每个子部门的负责人信息
        children.forEach(dept => {
          if (dept.leaderId != null && leaderMap.has(dept.leaderId)) {
            this.$set(dept, 'leaderInfo', leaderMap.get(dept.leaderId))
          }
        })
      } catch (error) {
        this.$message.error('获取负责人信息失败')
      }
    },

    // 修改获取负责人信息方法
    async getLeaderInfo(id) {
      if (id != null) {
        this.leaderLoading = true

        try {
          const leaderInfo = await getUserInfo(id)
          // 处理头像URL
          if (leaderInfo.picUrl) {
            leaderInfo.picUrl = process.env.VUE_APP_FILE_URL + '/' + leaderInfo.picUrl
          }
          this.leaderInfo = leaderInfo
        } catch (error) {
          this.$message.error('获取负责人信息失败')
        } finally {
          this.leaderLoading = false
        }
      }
    },

    // 修改查看详情方法
    async handleView(data) {
      this.detailData = data
      if (data.leaderId != null && !data.leaderInfo) {
        try {
          const leaderInfo = await getUserInfo(data.leaderId)
          // 处理头像URL
          if (leaderInfo.picUrl) {
            leaderInfo.picUrl = process.env.VUE_APP_FILE_URL + '/' + leaderInfo.picUrl
          }
          this.$set(this.detailData, 'leaderInfo', leaderInfo)
        } catch (error) {
          console.error('获取负责人信息失败:', error)
        }
      }
      this.detailDialogVisible = true
    },

    // 右键菜单
    handleContextMenu(event, data, node, component) {
      // TODO: 实现右键菜单
      event.preventDefault()
    },

    // 新增部门
    handleAdd() {
      this.dialogType = 'add'
      this.departmentForm = {
        name: '',
        code: '',
        parentId: '',
        leaderId: '',
        remark: '',
        sort: 0
      }
      this.dialogVisible = true
    },

    // 编辑部门
    async handleEdit(data) {
      this.dialogType = 'edit'
      this.departmentForm = {
        id: data.id,
        name: data.name,
        code: data.code,
        parentId: data.parentId,
        sort: data.sort,
        remark: data.remark,
        leaderId: data.leaderId
      }

      // 如果有负责人信息，直接使用
      if (data.leaderInfo) {
        this.userOptions = [data.leaderInfo]
      } else if (data.leaderId != null) {
        // 如果没有负责人信息但有leaderId，获取负责人信息
        try {
          const leaderInfo = await getUserInfo(data.leaderId)
          this.userOptions = [leaderInfo]
        } catch (error) {
          console.error('获取负责人信息失败:', error)
        }
      }

      this.dialogVisible = true
    },

    // 删除部门
    handleDelete(node, data) {
      // 如果是从树节点删除，需要检查子节点
      const hasChildren = node && node.childNodes && node.childNodes.length > 0
      const message = hasChildren
        ? '确定要删除该部门及其所有子部门吗？'
        : '确定要删除该部门吗？'

      this.$confirm(message, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        // 在删除前保存当前节点的展开状态
        this.saveExpandedState()

        await deleteDept(data.id)
        this.$message.success('删除成功')

        // 如果删除的是当前选中的部门，清空当前部门
        if (this.currentDepartment && this.currentDepartment.id === data.id) {
          this.currentDepartment = null
        }

        // 刷新部门树
        this.getList()
      }).catch(() => {
        // 取消删除操作
      })
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.departmentForm.validate()

        this.submitLoading = true
        const submitData = {
          ...this.departmentForm,
          valid: true
        }

        // 如果parentId为空，设置为0
        if (!submitData.parentId) {
          submitData.parentId = 0
        }

        try {
          // 在更新前保存当前节点的展开状态
          this.saveExpandedState()
          
          // 保存当前选中部门的ID和编辑的部门ID
          const currentDeptId = this.currentDepartment ? this.currentDepartment.id : null

          if (this.dialogType === 'add') {
            await addDept(submitData)
            this.$message.success('新增部门成功')
          } else {
            await updateDept(submitData)
            this.$message.success('更新部门成功')
          }

          this.dialogVisible = false
          // 刷新部门树
          await this.getList()
          
          // 在树刷新后重新获取当前部门
          if (currentDeptId) {
            const updatedCurrentDept = this.findDepartmentById(this.treeData, currentDeptId)
            if (updatedCurrentDept) {
              // 重新设置当前部门，确保UI更新
              this.currentDepartment = updatedCurrentDept
              
              // 如果当前部门有子部门，重新获取子部门负责人信息
              if (updatedCurrentDept.children && updatedCurrentDept.children.length > 0) {
                await this.getChildrenLeaders(updatedCurrentDept.children)
              }
              
              // 重新选中树节点
              this.$nextTick(() => {
                if (this.$refs.departmentTree) {
                  this.$refs.departmentTree.setCurrentKey(currentDeptId)
                }
              })
            }
          }
        } finally {
          this.submitLoading = false
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    // 修改负责人悬浮处理方法
    async handleLeaderFocus() {
      if (this.userOptions.length === 0 || !this.userOptions.some(u => u.id !== this.departmentForm.leaderId)) {
        this.userLoading = true
        try {
          // 获取当前部门的用户列表
          const data = await getDeptUsers(this.currentDepartment.id)
          const users = Array.isArray(data) ? data : (data.records || [])

          // 处理用户头像URL
          const processedUsers = users.map(user => ({
            ...user,
            picUrl: user.picUrl ? process.env.VUE_APP_FILE_URL + '/' + user.picUrl : ''
          }))

          // 如果当前已选择了负责人，确保它在列表中
          if (this.departmentForm.leaderId != null && this.userOptions.length > 0) {
            const currentLeader = this.userOptions[0]
            this.userOptions = [
              currentLeader,
              ...processedUsers.filter(user => user.id !== currentLeader.id)
            ]
          } else {
            this.userOptions = processedUsers
          }
        } catch (error) {
          console.error('获取用户列表失败:', error)
          this.$message.error('获取用户列表失败')
        } finally {
          this.userLoading = false
        }
      }
    },

    // 重置表单时清空用户选项
    resetForm() {
      this.$refs.departmentForm.resetFields()
      this.userOptions = []
    },

    // 关闭对话框时清空用户选项
    handleClose() {
      this.resetForm()
      this.dialogVisible = false
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; // 重置到第一页
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    // 获取所有子部门ID
    getAllChildrenIds(dept) {
      const ids = [];
      
      const collectIds = (node) => {
        if (!node) return;
        ids.push(node.id);
        
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            collectIds(child);
          });
        }
      };
      
      if (dept.children && dept.children.length > 0) {
        dept.children.forEach(child => {
          collectIds(child);
        });
      }
      
      return ids;
    },
  },
  watch: {
    searchKeyword(val) {
      this.$refs.departmentTree.filter(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.department-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  gap: 24px;
  overflow: hidden;

  .department-tree {
    width: 280px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

    .tree-header {
      padding: 20px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .header-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        span {
          font-size: 18px;
          font-weight: 600;
          color: #1a1f36;
          letter-spacing: 0.5px;
          position: relative;
          padding-left: 12px;

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: linear-gradient(to bottom, #409EFF, #64B5F6);
            border-radius: 3px;
          }
        }

        .el-button {
          padding: 9px 18px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 10px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            transform: translateY(-2px);
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }

      .search-box {
        .el-input {
          width: 100%;

          ::v-deep .el-input__inner {
            border-radius: 10px;
            height: 36px;
            background: #f7f9fc;
            border: 1px solid #e7ebf3;

            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }

            &:hover {
              border-color: #c0d0e9;
            }
          }

          ::v-deep .el-input__prefix {
            left: 10px;

            i {
              color: #8492a6;
            }
          }
        }
      }
    }

    .tree-container {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #fff;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      ::v-deep .el-tree {
        background: transparent;

        .el-tree-node__content {
          height: 32px;
          border-radius: 8px;
          margin: 2px 0;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f5f7fa;
          }

          .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;

            .node-content {
              display: flex;
              align-items: center;
              gap: 8px;

              i {
                font-size: 14px;
              }

              span {
                font-size: 14px;
                color: #1a1f36;
              }
            }

            .node-actions {
              display: none;
              gap: 2px;

              .el-button {
                padding: 1px 2px;
                margin: 0;

                i {
                  font-size: 13px;
                  margin: 0;
                }

                &.danger {
                  color: #F56C6C;
                }
              }
            }
          }
        }

        .el-tree-node.is-current>.el-tree-node__content {
          background-color: #ecf5ff !important;
          color: #409EFF;
          font-weight: 500;
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);

          .node-content {
            span {
              color: #409EFF;
            }
          }

          .node-actions {
            display: flex;
          }
        }
      }
    }
  }

  .department-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;
    padding: 0;
    height: 100%;

    .detail-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: 100%;

      .info-section {
        background-color: #fff;
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

        &:hover {
          box-shadow: 0 6px 24px rgba(31, 45, 61, 0.1);
        }

        &:first-child {
          flex-shrink: 0;
        }

        &:last-child {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;

          i {
            margin-right: 8px;
            color: #409EFF;
          }

          .section-actions {
            margin-left: auto;
            display: flex;
            gap: 12px;

            .action-button {
              padding: 6px 12px;
              font-weight: 500;
              transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
              border-radius: 8px;

              &.el-button--primary {
                background-color: #409EFF;
                border-color: #409EFF;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);

                &:hover {
                  transform: translateY(-2px);
                  background-color: #5aacff;
                  border-color: #5aacff;
                  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                }
              }

              &.el-button--danger {
                background-color: #F56C6C;
                border-color: #F56C6C;
                box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);

                &:hover {
                  transform: translateY(-2px);
                  background-color: #f78989;
                  border-color: #f78989;
                  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
                }
              }

              i {
                margin-right: 4px;
                font-size: 14px;
              }
            }
          }

          .member-count {
            margin-left: auto;
            font-size: 14px;
            color: #8492a6;
            font-weight: normal;
          }
        }

        .info-list {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .info-item {
            display: flex;
            align-items: center;

            &.full {
              grid-column: span 2;
            }

            label {
              width: 90px;
              color: #606266;
              font-weight: 500;
            }

            .value-with-icon {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 6px;

              i {
                font-size: 14px;
                color: #409EFF;
                opacity: 0.8;
              }

              span {
                color: #2c3e50;
                line-height: 1.4;
              }
            }
          }
        }

        ::v-deep .el-table {
          &.el-table--border {
            border-radius: 12px;
            overflow: hidden;
            margin-top: 12px;
          }

          th {
            background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
            font-weight: 600;
            color: #1a1f36;
            height: 40px;
            padding: 8px 0;
          }

          td {
            padding: 6px 0;
          }

          .dept-info {
            display: flex;
            align-items: center;
            gap: 6px;

            i {
              color: #409EFF;
              font-size: 14px;
            }
          }

          .el-tag {
            border-radius: 12px;
            padding: 0 10px;
            height: 22px;
            line-height: 20px;
          }

          .operation-column {
            .cell {
              white-space: nowrap;
              display: flex;
              justify-content: center;
              gap: 4px;

              .el-button {
                padding: 2px 4px;
                font-size: 12px;
                margin: 0;
                height: 24px;
                line-height: 1;
                display: inline-flex;
                align-items: center;

                i {
                  margin-right: 0;
                  font-size: 13px;
                }

                &.danger {
                  color: #F56C6C;
                }
              }
            }
          }
        }

        .pagination-container {
          padding: 16px 0 0;
          display: flex;
          justify-content: flex-end;
          background: #fff;
          border-top: 1px solid #f0f2f5;

          ::v-deep .el-pagination {
            padding: 0;
            font-weight: normal;

            .btn-prev,
            .btn-next,
            .el-pager li {
              background: transparent;
              border: 1px solid #e0e5ee;

              &:hover:not(.disabled) {
                border-color: #409EFF;
              }

              &.active {
                background: #409EFF;
                border-color: #409EFF;
                color: #fff;
              }
            }

            .el-pagination__sizes {
              margin-right: 15px;

              .el-input__inner {
                border-radius: 4px;
                border-color: #e0e5ee;

                &:hover {
                  border-color: #c0d0e9;
                }
              }
            }

            .el-pagination__total {
              margin-right: 15px;
            }

            .el-pagination__jump {
              margin-left: 15px;

              .el-input__inner {
                border-radius: 4px;
                border-color: #e0e5ee;

                &:hover {
                  border-color: #c0d0e9;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 弹出框样式 */
::v-deep .department-dialog,
.department-detail-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    background: #f8f9fb;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-button {
      border-radius: 10px;
      padding: 10px 24px;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);

        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

/* 负责人卡片样式 */
.leader-card {
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin: 0;
  width: 100%;

  .leader-header {
    padding: 20px;
    background: linear-gradient(135deg, #f6f9fe 0%, #ecf5ff 100%);
    display: flex;
    align-items: center;
    gap: 16px;
    border-bottom: 1px solid #e8f2ff;

    .el-avatar {
      border: 2px solid #fff;
      box-shadow: 0 4px 8px rgba(64, 158, 255, 0.15);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .leader-basic {

      h3,
      h4 {
        margin: 0 0 4px;
        font-size: 16px;
        color: #1a1f36;
        font-weight: 600;
      }

      p {
        margin: 0;
        font-size: 13px;
        color: #606266;
        opacity: 0.8;
      }
    }
  }

  .leader-info {
    padding: 16px;

    .info-row {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      color: #606266;
      font-size: 13px;
      border-bottom: 1px solid #f0f2f5;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: #f9fafc;
      }

      &:last-child {
        border-bottom: none;
      }

      i {
        color: #409EFF;
        width: 16px;
        text-align: center;
        opacity: 0.8;
      }
    }
  }
}

::v-deep .leader-popover {
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  z-index: 9999 !important;
  position: fixed !important;

  .el-popover__title {
    display: none;
  }

  .popper__arrow {
    display: none !important;
  }
}

::v-deep .el-popover.leader-popover {
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  overflow: visible !important;
  z-index: 9999 !important;
}

/* 表单样式 */
.department-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          color: #606266;
          font-weight: 500;
        }

        .el-input__inner,
        .el-textarea__inner {
          border-radius: 10px;
          border: 1px solid #e0e5ee;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }

          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-input-group__prepend {
          background: #f7f9fc;
          border: 1px solid #e0e5ee;
          border-right: none;
          border-radius: 10px 0 0 10px;
          padding: 0 12px;

          i {
            color: #409EFF;
          }
        }
      }
    }
  }
}

/* 用户选择器样式 */
.user-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 12px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  min-height: inherit;

  .el-avatar {
    flex: 0 0 32px;
    width: 32px !important;
    height: 32px !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: #e1f0ff;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #2b85e4;
    font-weight: 600;

    &::v-deep.el-avatar--circle {
      background: #e1f0ff;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #2b85e4;
    }
  }

  .user-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-right: 8px;
    margin-top: 5px;

    .name {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
      line-height: 1.4;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }

    .extra-info {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;

      i {
        margin-right: 4px;
      }
    }
  }
}

/* 修改 Select 下拉框的样式 */
::v-deep .el-select-dropdown__item {
  padding: 0;
  height: auto;
  min-height: 56px;

  &.selected {
    .user-option {
      background-color: #f5f7fa;

      .name {
        color: #077cf1;
        font-weight: 600;
      }
    }
  }

  &:hover {
    background-color: transparent;

    .user-option {
      background-color: #f5f7fa;
    }
  }
}

.leader-name {
  color: #409EFF;
  cursor: pointer;
  position: relative;
  padding-bottom: 2px;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover:after {
    transform: scaleX(1);
  }
}

.hover-tip {
  margin-left: 4px;
  color: #909399;
  font-size: 14px;
  transition: color 0.3s ease;
}

.custom-table {
  ::v-deep {
    .el-table__body-wrapper::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(144, 147, 153, 0.3);

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }

    .el-table__body-wrapper::-webkit-scrollbar-track {
      background: transparent;
    }
  }
}

.department-detail-dialog {
  .detail-content {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .form-section {
      background-color: #f8f9fb;
      border-radius: 16px;
      padding: 24px;
      box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1a1f36;
        margin-bottom: 20px;
        padding-left: 12px;
        border-left: 3px solid #409EFF;
        letter-spacing: 0.5px;
      }

      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          width: 90px;
          color: #606266;
          font-weight: 500;
          flex-shrink: 0;
        }

        .value-with-icon {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 8px;
          min-height: 24px;

          i {
            font-size: 16px;
            width: 16px;
            text-align: center;
          }

          span {
            color: #1a1f36;
            flex: 1;
            word-break: break-all;
          }
        }
      }
    }
  }
}

/* 修改dialog本身的样式 */
::v-deep .department-detail-dialog {
  border-radius: 20px;
  overflow: hidden;

  .el-dialog__header {
    padding: 20px 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: #fff;
  }
}

/* 拖拽相关样式 */
.el-tree-node__drop-prev {
  border-top: 2px solid #409EFF;
  margin-top: -1px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: -8px;
    width: 100%;
    height: 16px;
    background-color: rgba(64, 158, 255, 0.1);
    z-index: -1;
  }
}

.el-tree-node__drop-next {
  border-bottom: 2px solid #409EFF;
  margin-bottom: -1px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 100%;
    height: 16px;
    background-color: rgba(64, 158, 255, 0.1);
    z-index: -1;
  }
}

.el-tree-node__drop-inner {
  background-color: #ecf5ff;
  border: 2px solid #409EFF;
  margin: -2px;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.is-dragging {
  .el-tree-node__content {
    background-color: #f5f7fa;
    opacity: 0.8;
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.tip-box {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #909399;
  margin-top: 10px;
  padding: 0 20px;

  i {
    margin-right: 5px;
    color: #409EFF;
  }
}
</style>
