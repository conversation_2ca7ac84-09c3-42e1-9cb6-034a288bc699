import request from '@/utils/request'

// 获取菜单资源树列表
export function getResourceTreeList() {
  return request({
    url: '/system/sys/resources/treeList',
    method: 'get',
    // 不显示加载提示
    loadingDisabled: true
  })
}

// 新增菜单资源
export function addResource(data) {
  return request({
    url: '/system/sys/resources',
    method: 'post',
    data,
    // 不显示加载提示
    loadingDisabled: true
  })
}

// 更新菜单资源
export function updateResource(data) {
  return request({
    url: '/system/sys/resources',
    method: 'put',
    data,
    // 不显示加载提示
    loadingDisabled: true
  })
}

// 删除菜单资源
export function deleteResource(id) {
  return request({
    url: `/system/sys/resources/${id}`,
    method: 'delete',
    // 不显示加载提示
    loadingDisabled: true
  })
}
