<template>
  <el-dialog :title="(value?.id ? '修改' : '新增') + '任务'" :visible.sync="visible" :width="showCron ? '60%' : '30%'" :before-close="handleClose" :close-on-click-modal="false">
    <el-row :gutter="20" v-if="value">
      <el-col :span="showCron ? 12 : 24">
        <el-form :model="value" label-width="80px" :rules="rules" ref="formRef">
          <div class="label-group">任务信息</div>
          <el-form-item label="任务名" prop="name">
            <el-input v-model="value.name" />
          </el-form-item>
          <el-row :gitter="20">
            <el-col :span="12">
              <el-form-item label="分组" prop="groupName">
                <el-select v-model="value.groupName" style="width: 100%;" allow-create filterable>
                  <el-option v-for="(group, index) in groups" :index="index" :label="group.name" :value="group.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类型" prop="jobType">
                <el-select v-model="value.jobType" style="width: 100%;" @change="handleJobTypeChange">
                  <el-option v-for="(type, index) in types" :index="index" :label="type" :value="type" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="触发类型">
            <el-radio-group v-model="triggerType" size="mini">
              <el-radio-button label="loop" key="loop">循环触发</el-radio-button>
              <el-tooltip effect="dark" content="只触发一次" placement="top-start">
                <el-radio-button label="deplay" key="deplay">延迟触发</el-radio-button>
              </el-tooltip>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="cron" prop="cron" v-if="triggerType === 'loop'">
            <el-input v-model="value.cron">
              <template slot="append">
                <el-button type="primary" @click="showCron = !showCron">
                  生成表达式
                  <i class="el-icon-time el-icon--right"></i>
                </el-button>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="开始时间" prop="startTime" :rules="{ required: triggerType === 'deplay', message: '请选择任务开始时间', trigger: 'change' }">
            <el-date-picker 
              v-model="value.startTime" 
              clearable type="datetime" 
              style="width: 100%;" 
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
          <el-row :gitter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="value.status" style="width: 100%;">
                  <el-option v-for="(status, index) in statuses" :index="index" :label="status.label" :value="status.value" v-show="status.begin" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="concurrent">
                <el-switch
                  v-model="value.concurrent"
                  active-color="#13ce66"
                  inactive-color="#409EFF"
                  active-text="异步"
                  inactive-text="同步">
                </el-switch>
                <!-- <el-switch v-model="value.concurrent"></el-switch> -->
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="重试次数" prop="retryCount">
            <el-slider v-model="value.retryCount" :step="1" show-input></el-slider>
            <div class="tips">报错次数达到重试次数后状态会修改为<span style="color: red;">失败</span>，任务终止。<br>
              值为：0时，报错不会终止任务。</div>
          </el-form-item>
          <el-form-item label="超时(秒)" prop="timeout">
            <el-slider v-model="value.timeout" :step="1" show-input></el-slider>
          </el-form-item>
          <div class="label-group">任务数据</div>
          <div v-if="value.jobType  === 'dubbo'">
            <el-form-item label="调用类" prop="jobData.className" :rules="{required: true, message: '请输入调用类', trigger: 'blur'}">
              <el-input v-model="value.jobData.className" placeholder="全类名" />
            </el-form-item>
            <el-form-item label="调用方法" prop="jobData.methodName" :rules="{required: true, message: '请输入调用方法', trigger: 'blur'}">
              <el-input v-model="value.jobData.methodName" placeholder="方法名" />
            </el-form-item>
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <label class="el-form-item__label" style="width: 80px; text-align:right">方法参数</label>
              <el-button icon="el-icon-plus" circle type="primary" size="mini" @click="$refs.jobDataArgs.addKey()"></el-button>
            </div>
            <DynamicData ref="jobDataArgs" v-model="jobDataArgs" keyWidth="100%" keyPlaceholder="全类名" valuePlaceholder="参数值" />
          </div>
          <el-form-item label="任务数据" prop="jobData" v-else>
            <el-input type="textarea" :rows="4" v-model="jobData"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col v-show="showCron" :span="12">
        <cronTab @fill="handlerFill" :expression="value.cron"
        @hide="showCron = false"/>
      </el-col>
    </el-row>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handlerSubmit" :loading="loading">提 交</el-button>
    </span>

  </el-dialog>
</template>

<script>
import task from '@system/api/scheduled/task'
import cronTab from '@/components/auto-page/Crontab/index'
import DynamicData from '@system/components/DynamicData'

const cronreg = /^\s*($|#|\w+\s*=|(\?|\*|(?:[0-5]?\d)(?:(?:-|\/|\,)(?:[0-5]?\d))?(?:,(?:[0-5]?\d)(?:(?:-|\/|\,)(?:[0-5]?\d))?)*)\s+(\?|\*|(?:[0-5]?\d)(?:(?:-|\/|\,)(?:[0-5]?\d))?(?:,(?:[0-5]?\d)(?:(?:-|\/|\,)(?:[0-5]?\d))?)*)\s+(\?|\*|(?:[01]?\d|2[0-3])(?:(?:-|\/|\,)(?:[01]?\d|2[0-3]))?(?:,(?:[01]?\d|2[0-3])(?:(?:-|\/|\,)(?:[01]?\d|2[0-3]))?)*)\s+(\?|\*|(?:0?[1-9]|[12]\d|3[01])(?:(?:-|\/|\,)(?:0?[1-9]|[12]\d|3[01]))?(?:,(?:0?[1-9]|[12]\d|3[01])(?:(?:-|\/|\,)(?:0?[1-9]|[12]\d|3[01]))?)*)\s+(\?|\*|(?:[1-9]|1[012])(?:(?:-|\/|\,)(?:[1-9]|1[012]))?(?:L|W)?(?:,(?:[1-9]|1[012])(?:(?:-|\/|\,)(?:[1-9]|1[012]))?(?:L|W)?)*|\?|\*|(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?(?:,(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?)*)\s+(\?|\*|(?:[0-6])(?:(?:-|\/|\,|#)(?:[0-6]))?(?:L)?(?:,(?:[0-6])(?:(?:-|\/|\,|#)(?:[0-6]))?(?:L)?)*|\?|\*|(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?(?:,(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?)*)(|\s)+(\?|\*|(?:|\d{4})(?:(?:-|\/|\,)(?:|\d{4}))?(?:,(?:|\d{4})(?:(?:-|\/|\,)(?:|\d{4}))?)*))$/


export default {
  name: 'taskDialog',
  components: {
    cronTab, DynamicData
  },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        groupName: [
          { required: true, message: '请选择任务分组', trigger: 'change' }
        ],
        jobType: [
          { required: true, message: '请选择任务分组', trigger: 'change' }
        ],
        cron: [
          { required: true, message: '请输入cron表达式', trigger: 'blur' },
          { pattern: cronreg, message: '请输入正确的cron表达式', trigger: 'blur'}
        ],
        retryCount: [
          { required: true, message: '请输入重试次数', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      showCron: false,
      loading: false,
      triggerType: 'loop'
    };
  },
  mounted() {
    this.triggerType = (!this.value?.id || this.value?.cron) ? 'loop' : 'deplay'
  },
  watch: {
    triggerType(val) {
      if (val === 'loop') {
        this.rules.cron = [
          { required: true, message: '请输入cron表达式', trigger: 'blur' },
          { pattern: cronreg, message: '请输入正确的cron表达式', trigger: 'blur'}
        ]
        
      } else {
        delete this.rules.cron
        this.$set(this.value, 'cron', '')
      }
    }
  },
  computed: {
    jobDataArgs: {
      get() {
        var {jobData} = this.value
        var jobDataArgs = {}
        if (jobData && jobData.parameterTypes) {
          var {parameterTypes, args}  = jobData
          for (let i in parameterTypes) {
            jobDataArgs[parameterTypes[i]] = args[i]
          }
        }
        return jobDataArgs
      },
      set(value) {
        var entry = Object.entries(value)
        this.$set(this.value.jobData, 'parameterTypes', entry.map(item => item[0]))
        this.$set(this.value.jobData, 'args', entry.map(item => item[1]))
      }
    },
    jobData: {
      set(value) {
        try { 
          this.$set(this.value, 'jobData', JSON.parse(value))
        } catch (e) { 
          this.$set(this.value, 'jobData', value)
        }
      },
      get() {
        if (typeof this.value.jobData === 'string') {
          return this.value.jobData
        }
        return JSON.stringify(this.value.jobData)
      }
    },
    visible() {
      return typeof (this.value) !== 'undefined'
    }
  },
  props: {
    types: {
      type: Array,
      default: () => {
        return []
      }
    },
    value: {
      type: Object
    },
    groups: {
      type: Array,
      default: () => {
        return []
      }
    },
    statuses: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  methods: {
    handleJobTypeChange(val, old) {
      this.$set(this.value, 'jobData', {})
    },
    handlerFill(val) {
      this.$set(this.value, 'cron', val)
    },
    handleClose() {
      this.$emit('change', undefined)
    },
    handlerSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          const result = this.value.id ? task.update(this.value) : task.save(this.value)
          result.then(res => {
            this.$message.success('操作成功')
            this.loading = false
            var data = this.value
            if (!data.id) {
              data.id = res[0].id
            }
            this.$emit('after-submit', data)
            this.handleClose()
          }).catch(e => {
            this.loading = false
          })
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped> 
.label-group {
  border-left: 5px solid #409EFF; 
  padding-left: 15px;
  margin-bottom: 20px;
  font-weight: bolder;
}
.tips {
  line-height: 20px;
  color: #909399;
}
</style>