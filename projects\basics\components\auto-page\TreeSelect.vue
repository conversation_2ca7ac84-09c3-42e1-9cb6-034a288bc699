<template>
    <div>
        <Treeselect :multiple="multiple" :options="options" :placeholder="placeholder" :flat="flat" :disabled="disabled"
            :normalizer="tenantIdnormalizer" v-model="treeValue" />
    </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import Emitter from 'element-ui/src/mixins/emitter';

export default {
    components: { Treeselect },
    mixins: [Emitter],
    data() {
        return {
            treeValue: ''
        }
    },
    computed: {
        trees: {
            get() {
                if (!this.multiple) {
                    if (this.value) {
                        return this.value
                    } else {
                        return null
                    }
                } else {
                    return this.value || []
                }
            },
            set(val) {
            }
        }
    },
    watch: {
        trees: {
            handler(val) {
                this.treeValue = val
            },
            deep: true,
            immediate: true
        },
        treeValue: {
            handler(val) {
                this.$emit('value', val)
                this.$emit('change', val)
                this.dispatch('ElFormItem', 'el.form.change', [val]);
            },
            deep: true,
            immediate: true
        },
    },
    model: {
        prop: 'value',
        event: 'value'
    },
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        flat: {
            type: Boolean,
            default: false
        },
        multiple: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        value: {
            type: [String, Number, Array],
            default: ''
        },
        labelName: {
            type: String,
            default: 'label'
        },
        valueName: {
            type: String,
            default: 'id'
        },
        options: {
            type: Array,
            default: function name(params) {
                return []
            }
        }
    },
    methods: {
        tenantIdnormalizer(node) {
            if (node.children && !node.children.length) {
                delete node.children
            }
            return {
                id: node[this.valueName],
                label: node[this.labelName],
                children: node.children
            }
        }
    }
}
</script>

<style lang="scss" scoped></style>