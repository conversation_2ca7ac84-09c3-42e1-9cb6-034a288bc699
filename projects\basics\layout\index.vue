<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <component :is="config.sidebarComponent" v-if="!sidebar.hide" class="sidebar-container" />
    <!-- <sidebar v-if="!sidebar.hide" class="sidebar-container" /> -->

    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container" v-loading="loading"
      element-loading-text="Loading" customClass="custom-loading-mask-class">
      <div :class="{ 'fixed-header': fixedHeader }">
        <!-- <navbar /> -->
        <component :is="config.navbarComponent" />
      
        <component :is="config.tagsViewComponent" v-if="needTagsView"></component>
        <!-- <tags-view v-if="needTagsView" /> -->
      </div>
      <app-main />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
    </div>
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import { Navbar, Sidebar, AppMain, Settings, TagsView } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import variables from '@/styles/variables.module.scss'
import { mapState } from 'vuex'
import config from './layout.config'

export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    Settings,
    TagsView,
    RightPanel
  },
  mixins: [ResizeMixin],
  provide() {
    return {
      navbarSlotDom: null
    }
  },
  data() {
    return {
      config,
    }
  },
  computed: {
    loading() {
      return this.$store.state.loading.loadingCount > 0
    },
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      showSettings: state => state.settings.showSettings,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    },
    variables() {
      return variables;
    }
  },
  watch: {
    showSettings(val) {
      this.$store.dispatch('settings/setReadOnly', !val)
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";
@import "@/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px)
}

.sidebarHide .fixed-header {
  width: 100% !important;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
