
import { vueNodesMap } from '@logicflow/vue-node-registry'

export default function nodeEvent() {
    this.lf.on('node:mouseenter', ({ data }) => {
        const nodeId = data.id;
        // 元素置顶
        this.lf.toFront(nodeId);

        const edges = this.lf.graphModel.edges;
        edges.forEach(edge => {
            if (edge.sourceNodeId === nodeId || edge.targetNodeId === nodeId) {
                edge.setHighlight(true);
            }
        });
    });

    this.lf.on('node:mouseleave', ({ data }) => {
        const nodeId = data.id;
        const edges = this.lf.graphModel.edges;
        edges.forEach(edge => {
            if (edge.sourceNodeId === nodeId || edge.targetNodeId === nodeId) {
                edge.setHighlight(false);
            }
        });
    });

    this.lf.on('node:click', ({ data }) => {
        this.clickNode = data
        const model = this.lf.getNodeModelById(data.id)
        if (vueNodesMap[data.type].component && model.propertyPanel) {
            this.nodeDrawerVisible = true
        } else {
            this.nodeDrawerVisible = false
        }
    });

    // 点击空白区域
    this.lf.on('blank:click', () => {
        this.nodeDrawerVisible = false
        this.clickNode = null
    })


    // // 空白处右键也可以监听
    // this.lf.on('blank:contextmenu', ({ e }) => {
    // });

    // // 监听节点右键事件
    // this.lf.on('node:contextmenu', ({ e, data }) => {
    // });
}
