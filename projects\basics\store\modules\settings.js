import defaultSettings from '@/settings'
import variables from '@/styles/element-variables.module.scss'

const { showSettings, fixedHeader, sidebarLogo, sideTheme, topNav, tagsView, dynamicTitle } = defaultSettings
const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''

import { getApp } from '@/api/resource/resources'

const state = {
  showSettings: showSettings,
  title: '',
  icon: '',
  logo: '',
  theme: storageSetting.theme || variables.theme,
  sideTheme: storageSetting.sideTheme || sideTheme,
  topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
  tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
  fixedHeader: storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,
  sidebarLogo: storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,
  dynamicTitle: storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle,
  readOnly: false
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  }
}

const actions = {
  changeSetting({ commit }, data) {
    if (data.key == 'readOnly' || !state.readOnly) {
      commit('CHANGE_SETTING', data)
    } else {
      console.warn('readOnly is true, cannot change setting')
    }
  },
  setReadOnly({ commit }, readOnly) {
    state.readOnly = readOnly
  },
  // 设置网页标题
  setTitle({ commit }, title) {
    if (!state.readOnly) {
      state.title = title
    } else {
      console.warn('readOnly is true, cannot change title')
    }
  },
  setIcon({ commit }, icon) {
    if (!state.readOnly) {
      state.icon = icon
    } else {
      console.warn('readOnly is true, cannot change icon')
    }
  },
  getApp({ commit }, readOnly = true) {
    if (!state.readOnly || !readOnly) {
      getApp().then(data => {
        let title = data?.name
        let icon = data?.icon
        state.title = title
        state.icon = icon
      }).catch(err => {
        console.log(err)
      })
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

