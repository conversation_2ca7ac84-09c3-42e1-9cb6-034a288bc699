import router from '@/router'
import { resetRouter } from '@/router'
import store from '@/store'
// import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken, removeToken } from '@/utils/auth' // get token from cookie
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/auth', '/exception'] // no redirect whitelist

const premState = {
  initRouters: 0,
  version: true
}

router.beforeEach(async (to, from, next) => {

  // start progress bar
  NProgress.start()

  window.router_from_login = from.path == '/login'

  if (!store.getters.version && premState.version) {
    premState.version = false
    store.dispatch('version/refresh')
  }

  // determine whether the user has logged in
  const hasToken = getToken()

  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      if (from.path === '/login') {
        next('/')
      } else {
        await store.dispatch('user/resetToken')
        premState.initRouters = 0
        next()
      }
      NProgress.done()
    } else {
      try {
        const hasGetUserInfo = store.state.user.username
        if (!hasGetUserInfo) {
          store.dispatch('user/getInfo').catch(() => {
            if (window.location.href.indexOf('/login') == -1) {
              removeToken()
              window.location.reload()
            }
          })
        }
      } catch (error) {

      }

      if (premState.initRouters == 2) {
        next()
      } else {
        try {
          if (premState.initRouters == 0) {
            premState.initRouters = 1
            await store.dispatch('user/getRouters').then((data) => {
              store.dispatch('settings/getApp')
              store.dispatch('generateRoutes', data).then(() => {
                resetRouter()
                router.addRoutes(store.getters.routes)
                premState.initRouters = 2
                next({ ...to, replace: true })
              })
            })
          }
        } catch (error) {
          console.error('permission-->:', error);
          premState.initRouters = 0
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          next(`/login`)
          NProgress.done()
        }
      }
    }
  } else {
    premState.initRouters = 0
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      await store.dispatch('user/resetToken')
      if (to.path != '/404' && to.path != '/logout') {
        next(`/login?redirect=${to.path}`)
      } else {
        next('/login')
      }
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
