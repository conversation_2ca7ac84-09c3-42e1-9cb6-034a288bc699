<template>
  <div class="filter-node" :class="{ 'is-active': isActive,disabled: filter.enable < 1 }" @click="$emit('click', filter)">
    <div class="filter-header">
      <div class="filter-name">
        <i class="el-icon-magic-stick"></i>
        <span>{{ filter.name }}</span>
      </div>
      <div class="filter-actions">
        <el-switch v-model="filter.enable" style="transform: scale(0.7)" @change="$emit('enableChange')"></el-switch>
        <el-tooltip content="编辑过滤器" placement="top">
          <el-button type="text" icon="el-icon-edit" @click.stop="$emit('edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除过滤器" placement="top">
          <el-button type="text" icon="el-icon-delete" @click.stop="$emit('delete')"></el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="filter-content">
      <div v-if="filter.arg && Object.keys(filter.arg).length" class="arg-list">
        <div v-for="(value, key) in filter.arg" :key="key" class="arg-item">
          <span class="arg-key">{{ key }}:</span>
          <span class="arg-value">{{ value }}</span>
        </div>
      </div>
      <div v-else class="no-args">无参数</div>
    </div>
    <div class="filter-description">
      <span>{{ getFilterDescription() }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterNode',
  props: {
    filter: {
      type: Object,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterDescriptions: {
        AddRequestHeader: '添加请求头',
        AddResponseHeader: '添加响应头',
        AddRequestParameter: '添加请求参数',
        PrefixPath: '路径前缀',
        RemoveRequestHeader: '移除请求头',
        RemoveResponseHeader: '移除响应头',
        RemoveRequestParameter: '移除请求参数',
        RewritePath: '重写路径',
        SetPath: '设置路径',
        SetResponseHeader: '设置响应头',
        SetStatus: '设置状态码',
        StripPrefix: '去除前缀',
        RequestSize: '请求大小限制',
        RequestRateLimiter: '请求限流',
        CircuitBreaker: '断路器',
        Retry: '重试机制',
        RedirectTo: '重定向',
        FallbackHeaders: '降级请求头',
        Hystrix: 'Hystrix断路器'
      }
    }
  },
  methods: {
    getFilterDescription() {
      // 根据过滤器类型返回描述
      if (this.filterDescriptions[this.filter.name]) {
        return this.filterDescriptions[this.filter.name];
      }
      
      return `${this.filter.name} 过滤器`;
    }
  }
}
</script>

<style lang="scss" scoped>
.disabled {
  opacity: 0.4;
}
.filter-node {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px;
  margin-bottom: 12px;
  border-left: 3px solid #E6A23C;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  &.is-active {
    background-color: #FCF6EF;
    
    &:before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      border-width: 0 18px 18px 0;
      border-style: solid;
      border-color: #E6A23C #fff;
    }
  }
  
  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .filter-name {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 6px;
        color: #E6A23C;
      }
    }
    
    .filter-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .el-button {
        font-size: 16px;
        padding: 4px;
        
        &:hover {
          color: #409EFF;
        }
        
        &:last-child:hover {
          color: #F56C6C;
        }
      }
    }
  }
  
  .filter-content {
    margin-bottom: 8px;
    
    .arg-list {
      .arg-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 13px;
        
        .arg-key {
          color: #909399;
          min-width: 70px;
          margin-right: 8px;
        }
        
        .arg-value {
          color: #303133;
          word-break: break-all;
        }
      }
    }
    
    .no-args {
      font-size: 13px;
      color: #909399;
      font-style: italic;
    }
  }
  
  .filter-description {
    font-size: 12px;
    color: #909399;
    padding-top: 6px;
    border-top: 1px dashed #EBEEF5;
  }
}
</style> 