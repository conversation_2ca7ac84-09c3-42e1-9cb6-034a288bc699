<template>
    <div class="register-ui-container">
        <el-page-header @back="$emit('goBack')" title="返回登录" content="注册" />

        <div v-if="!isForm">
            <div class="title">选择注册方式</div>
            <div class="hit">请选择您想要使用的注册验证方式</div>
            <div class="register-type-options">
                <div class="register-type-option" v-if="$attrs.configKeys['builtin.register.phone']">
                    <i class="el-icon-mobile-phone"></i>
                    <h3>手机注册</h3>
                    <p>使用手机进行验证注册</p>
                    <el-button type="primary" plain @click="type = 1; isForm = true">选择</el-button>
                </div>

                <div class="register-type-option" v-if="$attrs.configKeys['builtin.register.email']">
                    <i class="el-icon-message"></i>
                    <h3>邮箱注册</h3>
                    <p>使用邮箱进行验证注册</p>
                    <el-button type="primary" plain @click="type = 2; isForm = true">选择</el-button>
                </div>
            </div>
        </div>

        <component v-else-if="config.registerForm && isForm" :is="config.registerForm" @goBack="$emit('goBack')" />

        <div v-else class="register-type-form">
            <div class="title">创建新用户</div>
            <div class="avatar-upload">
                <el-upload class="avatar-uploader" action="#" :show-file-list="false" accept="image/png,image/jpeg"
                    :before-upload="beforeAvatarUpload" :http-request="handleAvatarUpload"
                    :on-success="handleImgSuccess" :on-error="handleImgError">
                    <img v-if="registerForm?.picUrl?.length > 1" :src="url + registerForm.picUrl" class="avatar">
                    <div v-else class="avatar-placeholder">
                        <i class="el-icon-plus avatar-icon"></i>
                        <span>点击上传头像</span>
                    </div>
                </el-upload>
            </div>
            <div class="form-container">
                <el-form ref="registerForm" :model="registerForm"
                    :rules="type === 1 ? phoneRegisterRules : emailRegisterRules">
                    <el-form-item prop="name">
                        <el-input maxlength='20' v-model="registerForm.name" placeholder="请输入姓名"
                            prefix-icon="el-icon-user" />
                    </el-form-item>

                    <el-form-item prop="username">
                        <el-input maxlength='20' v-model="registerForm.username" placeholder="请输入用户名"
                            prefix-icon="el-icon-user-solid" />
                    </el-form-item>

                    <el-form-item prop="password">
                        <el-input maxlength='20' v-model="registerForm.password" type="password" placeholder="请输入密码"
                            prefix-icon="el-icon-lock" show-password />
                    </el-form-item>

                    <el-form-item prop="confirmPassword">
                        <el-input maxlength='20' v-model="registerForm.confirmPassword" type="password"
                            placeholder="请再次输入密码" prefix-icon="el-icon-lock" show-password />
                    </el-form-item>
                    <el-form-item v-if="type === 2" prop="phone">
                        <el-input maxlength='11' v-model="registerForm.phone" placeholder="请输入手机号"
                            prefix-icon="el-icon-mobile-phone" />
                    </el-form-item>
                    <el-form-item prop="deptId">
                        <div style="position: relative;">
                            <i class="el-icon-office-building dept-cascader-icon"></i>
                            <userDeptCascader class="dept-cascader" style="width: 100%;" v-model="registerForm.deptId"
                                placeholder="请选择部门" :props="{
                                    value: 'id',
                                    label: 'name',
                                    multiple: false,
                                    emitPath: false,
                                    checkStrictly: true,
                                }">
                            </userDeptCascader>
                        </div>

                    </el-form-item>

                    <template v-if="type === 1">
                        <el-form-item prop="phone">
                            <el-input maxlength='11' v-model="registerForm.phone" placeholder="请输入手机号"
                                prefix-icon="el-icon-mobile-phone" />
                        </el-form-item>

                        <el-form-item prop="code">
                            <ValidateCodeInput v-model="registerForm.code" :countdown="countdown" placeholder="请输入短信验证码"
                                prefix-icon="el-icon-key" @send="clickSend(registerForm.phone, 'sms')" />
                        </el-form-item>
                    </template>
                    <template v-else>
                        <el-form-item prop="email">
                            <el-input maxlength='32' v-model="registerForm.email" placeholder="请输入邮箱"
                                prefix-icon="iconfont ali-icon-youxiang" />
                        </el-form-item>

                        <el-form-item prop="code">
                            <ValidateCodeInput v-model="registerForm.code" :countdown="countdown" placeholder="请输入邮箱验证码"
                                prefix-icon="el-icon-key" @send="clickSend(registerForm.email, 'email')" />
                        </el-form-item>
                    </template>

                </el-form>

                <form-create v-model="fApi" :rule="formRule" :option="formOption"></form-create>
            </div>
            <div class="register-pwd-hit">
                <div :class="{ met: passwordStrength.length }">
                    <i :class="passwordStrength.length ? 'el-icon-check' : 'el-icon-close'"></i>
                    密码长度为8-20个字符
                </div>
                <div :class="{ met: passwordStrength.complexity }">
                    <i :class="passwordStrength.complexity ? 'el-icon-check' : 'el-icon-close'"></i>
                    包含字母、数字和特殊字符@$!%*#?&.
                </div>
            </div>
            <div>
                <el-button :loading="loading" type="primary" class="register-button"
                    @click="handleRegister">注册</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import UploadFileFormData from './UploadFileFormData'
import userDeptCascader from '@/components/auto-page/UserSelect/userDeptCascader.vue'
import ValidateCodeInput from './ValidateCodeInput.vue';
import { sendCode } from '@/api/login/notice'
import { register, getConfigByKey } from '../api/auth'
import { $encruption, validatePhone, validateEmail, phonePattern, emailPattern, pwdPattern } from './consts'
import EmailValidator from './EmailValidator.js'

import config from './LoginLayout.config.js'

export default {
    components: {
        userDeptCascader,
        ValidateCodeInput
    },
    data() {
        const registerRules = {
            name: [
                { required: true, message: '请输入姓名', trigger: 'blur' },
                { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
            ],
            username: [
                { required: true, message: '请输入用户名', trigger: 'blur' },
                { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
            ],
            password: [
                { required: true, message: '请输入密码', trigger: 'blur' },
                { min: 8, max: 20, message: '密码长度在 8 到 20 个字符', trigger: 'blur' },
                {
                    pattern: pwdPattern,
                    message: '密码必须包含字母、数字和特殊字符',
                    trigger: 'blur'
                }
            ],
            confirmPassword: [
                {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (!value) {
                            callback(new Error('请再次输入密码'))
                        } else if (value !== this.registerForm.password) {
                            callback(new Error('两次输入的密码不一致'))
                        } else {
                            callback()
                        }
                    },
                    trigger: 'blur'
                }
            ],
            deptId: [
                { required: true, message: '请选择所属部门', trigger: 'change' }
            ],
            phone: [
                {
                    required: true,
                    trigger: 'blur',
                    validator: validatePhone
                }
            ],
            code: [
                { required: true, message: '请输入短信验证码', trigger: 'submit' },
                { len: 6, message: '验证码长度应为6位', trigger: 'submit' }
            ]

        }
        return {
            config,
            url: process.env.VUE_APP_FILE_URL,
            type: 0,
            isForm: false,
            loading: false,
            fApi: {},
            formRule: [],
            formOption: {
                submitBtn: false,
                resetBtn: false,
            },
            registerForm: {
                password: ''
            },
            countdown: 0,
            passwordStrength: {
                length: false,
                complexity: false
            },
            phoneRegisterRules: registerRules,
            emailRegisterRules: {
                ...registerRules,
                email: [
                    { required: true, trigger: 'blur', validator: validateEmail }
                ],
                code: [
                    { required: true, message: '请输入邮箱验证码', trigger: 'submit' },
                    { len: 6, message: '验证码长度应为6位', trigger: 'submit' }
                ]
            }
        };
    },
    watch: {
        'registerForm.password': {
            handler(val) {
                this.passwordStrength.length = val.length >= 8 && val.length <= 20
                this.passwordStrength.complexity = pwdPattern.test(val)
            },
            immediate: true
        }
    },
    destroyed() {
        this.timerInterval && clearInterval(this.timerInterval)
    },
    mounted() {
        this.getConfigByKey()
    },
    methods: {
        getConfigByKey() {
            getConfigByKey('builtin.register.custom.fields').then(res => {
                this.formRule = res || []
            })
        },
        beforeAvatarUpload(file) {
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
                this.$message.error('上传头像图片大小不能超过 2 MB!')
                return false;
            }
            return true;
        },
        handleAvatarUpload({ file, onProgress, onSuccess, onError }) {
            UploadFileFormData({ file, onProgress, onSuccess, onError })
        },
        handleImgSuccess(res, file) {
            this.$set(this.registerForm, 'picUrl', res.data)
        },
        handleImgError(err, file) {
            this.$message.error('上传失败');
        },
        startCountdown() {
            this.countdown = 60
            this.timerInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.timerInterval && clearInterval(this.timerInterval)
                }
            }, 1000)
        },
        clickSend(identifier, type) {
            if (!phonePattern.test(identifier) && type === 'phone') {
                this.$message.error('请输入正确的手机号')
                return
            }
            if (!EmailValidator.validateEmail(identifier) && type === 'email') {
                this.$message.error('请输入正确的邮箱号')
                return
            }

            if (this.countdown > 0) {
                return
            }
            sendCode({
                identifier: identifier,
                type: type,
                scene: 'register'
            }).then(res => {
                this.$message.success('验证码已发送')
                this.startCountdown()
            }).catch(err => {
                this.$message.error(err || '发送失败')
            })
        },
        async handleRegister() {

            try {

                let fv = await this.fApi.validate()
                this.$refs.registerForm.validate((valid) => {
                    if (valid && fv) {
                        this.loading = true
                        const enpassword = $encruption(this.registerForm.password);

                        const registerData = {
                            ...this.registerForm,
                            password: enpassword,
                            verifyType: this.type === 1 ? 'sms' : 'email',
                            verifyCode: this.registerForm.code,
                            extParams: this.fApi.formData()
                        }
                        delete registerData.confirmPassword
                        delete registerData.code

                        register(registerData).then(res => {
                            this.loading = false
                            this.$message.success('注册申请提交成功')
                            this.$confirm(res, '提示', {
                                confirmButtonText: '返回',
                                cancelButtonText: '取消',
                                type: 'success'
                            }).then(() => {
                                this.$emit('goBack')
                            }).catch(() => {
                                this.registerForm = {}
                            });
                        }).catch((err) => {
                            this.loading = false
                            this.$message.error(err || '注册申请失败')
                        })
                    }
                })
            } catch (error) {
                this.$refs.registerForm.validate()
            }
        },
    }
};
</script>
<style lang="scss" scoped>
.register-ui-container {
    border-width: 1px;
    padding: 32px;
    border-radius: 20px;
    width: 480px;
    min-height: 500px;
    background-color: #fff;
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);


    .register-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        margin-top: 20px;
        background: rgb(0, 122, 255);
        border-radius: 8px;
        border: none;

        &:hover {
            background: rgb(0, 86, 179);
        }
    }


    .el-page-header {
        ::v-deep .el-page-header__left {
            color: rgb(0, 122, 255);
            transition: all 0.3s;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                color: rgb(0, 86, 179);
                transform: scale(1.05);
            }
        }

        ::v-deep .el-page-header__content {
            color: #000 !important;
        }
    }


    .title {
        font-size: 24px;
        color: #303133;
        font-weight: 500;
        text-align: center;
        margin-top: 20px;
    }

    .hit {
        text-align: center;
        color: #606266;
        font-size: 14px;
        margin-top: 20px;
    }

    .register-type-options {
        display: flex;
        justify-content: space-around;
        margin-top: 30px;

        .register-type-option {
            background-color: rgb(245, 247, 250);
            width: 184px;
            height: 220px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px 20px;
            transition: all 0.3s ease;

            &:hover {
                background-color: rgb(255, 255, 255);
                box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px 0px;
                transform: translateY(-2px);

            }

            i[class^="el-icon"] {
                color: rgb(64, 158, 255);
                font-size: 36px;
            }

            h3 {
                color: rgb(48, 49, 51);
                font-size: 16px;
            }

            p {
                color: rgb(144, 147, 153);
                font-size: 14px;
            }

            .el-button {
                width: 134px;
            }
        }
    }

    .register-type-form {

        .form-container {
            max-height: 550px;
            overflow-y: scroll;
            scrollbar-width: none;

            &::-webkit-scrollbar {
                display: none;
            }
        }


        .avatar-upload {
            text-align: center;
            margin-top: 10px;
            margin-bottom: 10px;

            .avatar-uploader {
                ::v-deep .el-upload {
                    cursor: pointer;
                    position: relative;
                    overflow: hidden;
                    display: inline-block;
                    border: none;
                }
            }

            .avatar {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                object-fit: cover;
            }

            .avatar-placeholder {
                width: 100px;
                height: 100px;
                border-radius: 50%;
                background: #f5f7fa;
                border: 1px dashed #d9d9d9;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                    border-color: #409EFF;
                    background: #f5f7fa;

                    .avatar-icon {
                        color: #409EFF;
                    }

                    span {
                        color: #409EFF;
                    }
                }

                .avatar-icon {
                    font-size: 24px;
                    color: #8c939d;
                    margin-bottom: 4px;
                }

                span {
                    font-size: 12px;
                    color: #8c939d;
                }
            }
        }

        .register-pwd-hit {
            margin: 16px 0 24px;
            padding: 12px 16px;
            background: #f5f7fa;
            border-radius: 4px;

            >div {
                color: #909399;
                font-size: 14px;
            }

            .met {
                color: #67c23a;
            }

            >div:last-child {
                margin-top: 10px;
            }
        }

        .dept-cascader-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            color: rgb(148, 174, 217);
            z-index: 1;
        }

        .dept-cascader {
            ::v-deep .el-input__inner {
                padding-left: 30px;
            }
        }
    }
}
</style>