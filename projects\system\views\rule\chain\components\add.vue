<template>
  <div class="chain-container">
    <div class="page-header">
      <div class="header-title">
        <h2>新增规则链</h2>
        <div class="back-button" @click="goBack">
          <i class="el-icon-arrow-left"></i>
          <span>返回规则链列表</span>
        </div>
      </div>
    </div>

    <el-form 
      ref="chainForm" 
      :model="form" 
      :rules="rules"
      label-width="120px"
      class="chain-form"
    >
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则链唯一标识" prop="chainName">
              <el-input 
                v-model="form.chainName"
                placeholder="请输入规则链唯一标识"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="应用服务" prop="application">
              <el-input
                v-model="form.application"
                placeholder="应用服务"
                readonly
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="规则链描述" prop="chainDesc">
              <el-input 
                type="textarea"
                v-model="form.chainDesc"
                :rows="3"
                placeholder="请输入规则链描述"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <div class="section-title">规则配置</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="规则表达式" prop="elData">
              <div class="editor-container" id="el-editor"></div>
              <div class="expression-actions">
                <el-button 
                  type="primary" 
                  size="small" 
                  icon="el-icon-check"
                  @click="handleValidate"
                  :loading="validating"
                >
                  校验表达式
                </el-button>
              </div>
              <div class="expression-tip">
                <i class="el-icon-info"></i>
                <span>支持的表达式格式：THEN(a, b)、WHEN(a).THEN(b)、IF(a).ELSE(b) 等</span>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input 
                type="textarea"
                v-model="form.remark"
                :rows="3"
                placeholder="请输入备注信息"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="状态">
              <el-switch
                v-model="form.enabled"
                active-text="启用"
                inactive-text="禁用"
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="form-actions">
      <el-button @click="goBack">取消</el-button>
      <el-button 
        type="warning" 
        @click="handleExecute"
        :loading="executing"
        icon="el-icon-video-play"
      >执行测试</el-button>
      <el-button 
        type="primary" 
        @click="saveChain"
        :loading="saving"
        icon="el-icon-check"
      >保存</el-button>
    </div>

    <execute-dialog ref="executeDialog" :chain-name="currentChainName" />
  </div>
</template>

<script>
import { createRuleChain, executeRuleChain, validateRuleExpression, getApplicationList } from '@system/api/rule/chain'
import * as monaco from 'monaco-editor'
import { debounce } from 'lodash'
import ExecuteDialog from './ExecuteDialog.vue'

export default {
  name: 'ChainAdd',
  components: {
    ExecuteDialog
  },
  data() {
    return {
      saving: false,
      executing: false,
      validating: false,
      executeResult: null,
      contextEditor: null,
      currentChainName: '',
      form: {
        chainName: '',
        application: 'extreme-flow',
        chainDesc: '',
        elData: '',
        remark: '',
        enabled: true
      },
      rules: {
        chainName: [
          { required: true, message: '请输入规则链唯一标识', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        application: [
          { required: true, message: '请选择应用服务', trigger: 'change' }
        ],
        elData: [
          { required: true, message: '请输入规则表达式', trigger: 'blur' }
        ]
      },
      applicationList: [],
      editor: null
    }
  },
  created() {
    this.fetchApplicationList()
  },
  mounted() {
    this.createEditor()
  },
  methods: {
    async fetchApplicationList() {
        const data = await getApplicationList()
        if (data && Array.isArray(data)) {
          this.applicationList = data
        }
    },
    createEditor() {
      this.$nextTick(() => {
        const container = document.getElementById('el-editor')
        if (!container) return

        // 销毁已存在的编辑器
        if (this.editor) {
          this.editor.dispose()
        }

        // 创建新的编辑器实例
        this.editor = monaco.editor.create(container, {
          value: this.form.elData,
          language: 'javascript',
          theme: 'vs-dark',
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 14,
          tabSize: 2
        })

        // 监听内容变化
        this.editor.onDidChangeModelContent(
          debounce(() => {
            this.form.elData = this.editor.getValue()
          }, 300)
        )
      })
    },
    goBack() {
      this.$router.push('/rule/chain')
    },
    async saveChain() {
      // 编辑器内容手动赋值给表单
      if (this.editor) {
        this.form.elData = this.editor.getValue()
      }
      
      let hasError = false
      try {
        await this.$refs.chainForm.validate()
      } catch (error) {
        hasError = true
      }

      // 验证规则表达式
      try {
        await validateRuleExpression(this.form.elData)
      } catch (error) {
        // 如果校验失败，弹出二次确认
        try {
          await this.$confirm(error.message || '规则表达式校验失败，是否确定要保存？', '规则校验警告', {
            confirmButtonText: '确定保存',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          })
          // 用户确认要保存，继续执行
        } catch (e) {
          // 用户取消，终止保存
          return
        }
      }

      this.saving = true
      try {
        await createRuleChain(this.form)
        this.$message.success('保存成功')
        this.goBack()
      } catch (error) {
        this.$message.error('保存失败：' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    },
    handleExecute() {
      if (!this.form.chainName) {
        this.$message.warning('请先填写规则链名称')
        return
      }
      this.currentChainName = this.form.chainName
      this.$nextTick(() => {
        this.$refs.executeDialog.open()
      })
    },
    async handleValidate() {
      if (!this.editor) return
      
      const elData = this.editor.getValue()
      if (!elData) {
        this.$message.warning('请先输入规则表达式')
        return
      }
      
      this.validating = true
      try {
        await validateRuleExpression(elData)
        this.$message.success('规则表达式校验通过')
      }  finally {
        this.validating = false
      }
    },
    createContextEditor() {
      const container = document.getElementById('context-editor')
      if (!container) return

      // 销毁已存在的编辑器
      if (this.contextEditor) {
        this.contextEditor.dispose()
      }

      // 创建新的编辑器实例
      this.contextEditor = monaco.editor.create(container, {
        value: JSON.stringify({ input: '', timestamp: Date.now() }, null, 2),
        language: 'json',
        theme: 'vs-dark',
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        automaticLayout: true,
        lineNumbers: 'on',
        fontSize: 14,
        tabSize: 2
      })
    },
    async confirmExecute() {
      if (!this.contextEditor) return
      
      let contextData = {}
      try {
        contextData = JSON.parse(this.contextEditor.getValue())
      } catch (error) {
        this.$message.error('上下文数据格式不正确，请输入有效的JSON格式')
        return
      }
      
      this.executing = true
      try {
        const result = await executeRuleChain({
          chainName: this.form.chainName,
          contextData
        })
        this.executeResult = result.data
        this.executeDialogVisible = false
        this.resultDialogVisible = true
      } catch (error) {
        this.$message.error('执行失败：' + (error.message || '未知错误'))
      } finally {
        this.executing = false
      }
    },
    beforeDestroy() {
      // 销毁编辑器实例
      if (this.editor) {
        this.editor.dispose()
      }
      if (this.contextEditor) {
        this.contextEditor.dispose()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chain-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  min-height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eef1f7;

    .header-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .back-button {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #409EFF;
        transition: all 0.3s;
        font-size: 14px;
        font-weight: 500;

        i {
          margin-right: 6px;
          font-size: 14px;
        }

        &:hover {
          color: #66b1ff;
          transform: translateX(-3px);
        }
      }
    }
  }

  .chain-form {
    width: 100%;
    padding: 0;
  }

  .form-section {
    width: 100%;
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 22px;
        
        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          line-height: 1.4;
          padding-bottom: 8px;
          color: #1a1f36;
          font-weight: 500;
          display: flex;
          align-items: center;
          height: 38px;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-textarea__inner {
          border-radius: 10px;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }
      }

      .el-select {
        width: 100%;
      }
    }
  }

  .expression-tip {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    color: #909399;
    font-size: 13px;

    i {
      color: #409EFF;
    }
  }

  .expression-actions {
    margin-top: 10px;
    margin-bottom: 4px;
    display: flex;
    justify-content: flex-end;
  }

  .editor-container {
    height: 200px;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e0e5ee;
    transition: all 0.3s;
    width: 100%;
    
    &:hover {
      border-color: #c0d0e9;
    }
    
    &:focus-within {
      border-color: #409EFF;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
    }
  }

  .form-actions {
    margin-top: 24px;
    padding: 24px 0 0;
    text-align: center;
    border-top: 1px solid #eef1f7;

    .el-button {
      min-width: 120px;
      padding: 10px 20px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      border-radius: 10px;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      & + .el-button {
        margin-left: 16px;
      }
      
      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .execute-form {
    .editor-container {
      height: 300px;
      border-radius: 4px;
      border: 1px solid #DCDFE6;
      margin-bottom: 10px;
    }
    
    .context-tip {
      color: #909399;
      font-size: 12px;
      line-height: 1.2;
      
      i {
        margin-right: 4px;
      }
    }
  }

  .result-content {
    .result-item {
      margin-bottom: 15px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .result-label {
        font-weight: bold;
        margin-bottom: 5px;
        color: #606266;
      }
      
      .result-value {
        background-color: #f5f7fa;
        padding: 10px;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
</style> 