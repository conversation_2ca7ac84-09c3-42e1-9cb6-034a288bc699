<template>
  <div class="log-config-page">
    <el-button v-if="routeId" type="primary" icon="el-icon-plus" @click="handleAddConfig" class="add-button">新增配置</el-button>
    <div class="config-list">
      <el-card v-for="(config, index) in logConfigList" :key="index" class="log-config-card">
        <div class="card-content">
          <div class="card-info">
            <i class="el-icon-setting" style="color: #409EFF;" />
            <div class="method-tag">
               <el-tag :type="getMethodTagType(config.method)" size="small">{{config.method || 'All'}}</el-tag>
            </div>
            <div class="include-tag">
               <el-tag :type="config.include ? 'success' : 'danger'" size="small">{{config.include ? '包含' : '排除'}}</el-tag>
            </div>
            <span class="create-time">{{ config.createTime }}</span>
          </div>
          <div class="card-actions">
            <el-switch v-model="config.enable" style="scale: 0.7;" @change="logConfigEnableChange(config)"></el-switch>
            <el-button type="text" icon="el-icon-edit" @click="handleEdit(config)" class="action-btn"></el-button>
            <el-button type="text" icon="el-icon-delete" @click="handleDelete(config.id)" class="action-btn"></el-button>
          </div>
        </div>
        <div class="patterns-list">
          <div v-for="(pattern, index) in config.patterns" :key="index" class="pattern-item">
            {{ pattern }}
          </div>
        </div>
      </el-card>
    </div>

    <el-dialog
      :title="form?.id ? '修改配置' : '新增配置'"
      :visible.sync="dialogVisible"
      width="600px"
      append-to-body
      class="config-dialog"
    >
      <el-form v-if="form" :model="form" :rules="rules" label-width="80px" ref="formRef" class="config-form">
        <el-form-item label="方法" prop="method">
          <el-select v-model="form.method" placeholder="请选择请求方法" class="method-select">
            <el-option label="ALL" :value="undefined" />
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
          </el-select>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否开启" prop="enable">
              <el-switch v-model="form.enable" class="enable-switch-form"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="include">
              <el-switch
                active-text="包含"
                inactive-text="排除"
                v-model="form.include"
                class="include-switch-form"
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort"  />
        </el-form-item>
        <div class="patterns-header">
          <label class="el-form-item__label" style="width: 80px;">匹配路径</label>
          <el-button type="primary" icon="el-icon-plus" @click="addPattern" size="mini" circle></el-button>
        </div>
        <el-form-item
          v-for="(pattern, index) in form.patterns"
          :key="index"
          :prop="`patterns[${index}]`"
          label-width="0"
          :rules="{ required: true, message: '请输入匹配路径', trigger: 'blur' }"
          class="pattern-form-item"
        >
          <el-input v-model="form.patterns[index]" class="pattern-input">
            <template slot="append">
              <el-button type="danger" icon="el-icon-delete" size="mini" @click="removePattern(index)"></el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="form = undefined">取 消</el-button>
        <el-button type="primary" @click="handlerSubmit" :loading="loading">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {getLogConfigList, createLogConfig, updateLogConfig, deleteLogConfig} from '@system/api/sys/gateway'
import DynamicData from '@system/components/DynamicData'
export default {
  name: 'LogConfig',
  components: { DynamicData },
  props: {
    routeId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      logConfigList: [],
      queryParams: {
        size: -1
      },
      form: undefined,
      loading: false,
      rules: {
        sort: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    routeId(val) {
      if (val) {
        this.getLogConfigList()
      }
    }
  },
  computed: { 
    dialogVisible: {
      get() {
        return typeof this.form !== 'undefined'
      },
      set(val) {
        if (!val) {
          this.form = undefined
        }
      }
    }
  },
  mounted() {
    this.getLogConfigList()
  },
  methods: {
    getLogConfigList() {
      this.queryParams.routeId = this.routeId
      getLogConfigList(this.queryParams).then(res => {
        this.logConfigList = res.records
      })
    },
    handleEdit(config) {
      config.patterns = config.patterns || []
      this.form = JSON.parse(JSON.stringify(config))
    },
    handleDelete(id) {
      this.$confirm('确认删除数据吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteLogConfig(id).then(res => {
          this.$message.success('删除成功')
          this.getLogConfigList()
          this.loading = false
        }).catch(e => {
          this.loading = false
        })
      })
    },
    getMethodTagType(method) {
      const types = {
        GET: 'success',
        POST: 'primary',
        PUT: 'warning',
        DELETE: 'danger'
      }
      return types[method] || 'info'
    },
    handlerSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          const result = this.form.id ? updateLogConfig(this.form, true) : createLogConfig(this.form)
          this.loading = true
          result.then(res => {
            this.$message.success('操作成功')
            this.getLogConfigList()
            this.form = undefined
            this.loading = false
          }).catch(err => {
            this.loading = false
          })
        }
      })
    },
    removePattern(index) {
      if (this.form.patterns.length > 1) {
        this.form.patterns.splice(index, 1)
      }
    },
    addPattern() {
      this.form.patterns.push('')
    },
    logConfigEnableChange(config) {
      this.$confirm('确认修改配置状态', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        updateLogConfig({
          id: config.id,
          enable: config.enable
        }).then(res => {
          this.$message.success('操作成功')
          this.loading = false
        }).catch(e => {
          this.loading = false
          config.enable = !config.enable
        })
      })
    },
    handleAddConfig() {
      this.form = {
        method: 'GET',
        enable: true,
        include: true,
        patterns: [''],
        sort: Math.max.apply(null,[...this.logConfigList.map(q => q.sort), -1]) + 1,
        routeId: this.routeId
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.log-config-page {
  position: relative;
}

.add-button {
  border-radius: 10px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

.config-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.log-config-card {
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  }

  .el-card__body {
    padding: 16px;
  }
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .method-tag, .include-tag {
    .el-tag {
      border-radius: 6px;
      height: 24px;
      line-height: 24px;
      padding: 0 8px;
      font-weight: 500;
    }
  }

  .create-time {
    color: #909399;
    font-size: 13px;
  }
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 8px;

  .action-btn {
    padding: 4px;
    font-size: 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }
  }
}

.patterns-list {
  margin-top: 12px;
  border-top: 1px solid #ebeef5;
  padding-top: 12px;

  .pattern-item {
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 6px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #606266;
    word-break: break-all;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.config-dialog {
  .config-form {
    .method-select {
      width: 100%;

      ::v-deep .el-input__inner {
        border-radius: 10px;
        border: 1px solid #e0e5ee;
        transition: all 0.3s ease;
      }
    }

    .patterns-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 0 8px 0 0;
    }

    .pattern-form-item {
      margin-bottom: 16px;

      .pattern-input {
        width: 100%;

        ::v-deep .el-input__inner {
          border-radius: 10px;
          border: 1px solid #e0e5ee;
          transition: all 0.3s ease;
        }
      }
    }

    .el-input-number {
      width: 100%;
      
      ::v-deep .el-input__inner {
        border-radius: 10px;
        border: 1px solid #e0e5ee;
        transition: all 0.3s ease;
      }
    }
  }
}

.enable-switch-form {
  transform: scale(0.9);
}

.include-switch-form {
  .el-switch__label {
    font-size: 12px;
  }
}

::v-deep .el-dialog {
  border-radius: 10px;
  overflow: hidden;

  .el-dialog__header {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
  }
}
</style>


