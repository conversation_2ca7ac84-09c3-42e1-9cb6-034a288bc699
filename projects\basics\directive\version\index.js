import store from '@/store'


/**
 * 符号	    示例	    含义	      允许的版本范围
 * ^	      ^5.6	    兼容版本	  >=5.6.0 <6.0.0
 * ~	      ~5.6	    近似版本	  >=5.6.0 <5.7.0
 * >	      >5.6	    大于	      >5.6.0
 * >=	      >=5.6	    大于等于	  >=5.6.0
 * <	      <5.6	    小于	      <5.6.0
 * <=	      <=5.6	    小于等于	  <=5.6.0
 * 无前缀	   5.6	    精确版本	  5.6.0
 */


/**
 * 比较系统版本号
 * @param {*} range 版本范围 (如 "^1.2.0")
 */
var match = function(range) {
  let version = store.getters.version
  return satisfies(version, range)
}

export default {
  async inserted(el, { value }, {context, componentInstance}) {
    if (!match(value)) {
      await componentInstance.$destroy()
      el.parentNode && el.parentNode.removeChild(el)
      Object.keys(context.$refs).forEach(refName => {
        if (context.$refs[refName] === componentInstance) {
          delete context.$refs[refName];
        }
      });
    }
  },
  match
}

/**
 * 解析版本号为数字数组
 * @param {string} version 版本字符串 (如 "1.2.3-alpha.1")
 * @returns {object} { numbers: [1,2,3], suffix: '-alpha.1' }
 */
function parseVersion(version) {
  // 处理v前缀
  if (version.startsWith('v') || version.startsWith('V')) {
    version = version.substring(1);
  }

  // 分离数字部分和后缀
  const suffixIndex = version.search(/[^0-9.]/);
  let numbersPart = version;
  let suffix = '';

  if (suffixIndex !== -1) {
    numbersPart = version.substring(0, suffixIndex);
    suffix = version.substring(suffixIndex);
  }

  // 解析数字部分
  const numbers = numbersPart.split('.').map(Number);
  
  // 确保至少有3位 (主版本.次版本.修订号)
  while (numbers.length < 3) {
    numbers.push(0);
  }

  return { numbers, suffix };
}

/**
 * 比较两个版本号
 * @param {string} v1 版本1
 * @param {string} v2 版本2
 * @returns {number} 1(v1>v2), 0(v1=v2), -1(v1<v2)
 */
function compareVersions(v1, v2) {
  const parsed1 = parseVersion(v1);
  const parsed2 = parseVersion(v2);
  const nums1 = parsed1.numbers;
  const nums2 = parsed2.numbers;

  // 比较主版本、次版本、修订号
  for (let i = 0; i < 3; i++) {
    if (nums1[i] > nums2[i]) return 1;
    if (nums1[i] < nums2[i]) return -1;
  }

  // 数字部分相同，比较后缀
  if (parsed1.suffix || parsed2.suffix) {
    // 简单实现：有后缀的版本小于无后缀的版本
    if (!parsed1.suffix) return 1;
    if (!parsed2.suffix) return -1;
    return parsed1.suffix.localeCompare(parsed2.suffix);
  }

  return 0;
}

const regex = /^([~^>=<]{0,2})(\d.+)/

/**
 * 检查版本是否满足范围要求
 * @param {string} version 要检查的版本
 * @param {string} range 版本范围 (如 "^1.2.0")
 * @returns {boolean}
 */
function satisfies(version, range) {
  // 处理常见范围符号
  if (range === '*' || range === '') return true;

  // 精确匹配
  if (!range.match(/^[~^>=<]/)) {
    return compareVersions(version, range) === 0;
  }
  var match = range.match(regex)

  if (!match) {
    return compareVersions(version, range) === 0;
  }

  const operator = match[1];
  const rangeVersion = match[2].trim();

  const comparison = compareVersions(version, rangeVersion);

  switch (operator) {
    case '^': // 兼容版本 (相同主版本)
      return satisfiesMajor(version, rangeVersion);
    case '~': // 近似版本 (相同主次版本)
      return satisfiesMinor(version, rangeVersion);
    case '>':
      return comparison > 0;
    case '<':
      return comparison < 0;
    case '>=':
      return comparison >= 0;
    case '<=':
      return comparison <= 0;
    default:
      return false;
  }
}

// ^ 符号实现 (主版本相同)
function satisfiesMajor(version, rangeVersion) {
  const v = parseVersion(version);
  const r = parseVersion(rangeVersion);
  
  // 主版本不同
  if (v.numbers[0] !== r.numbers[0]) return false;
  
  // 当前版本 >= 范围版本
  return compareVersions(version, rangeVersion) >= 0;
}

// ~ 符号实现 (主次版本相同)
function satisfiesMinor(version, rangeVersion) {
  const v = parseVersion(version);
  const r = parseVersion(rangeVersion);
  
  // 主版本或次版本不同
  if (v.numbers[0] !== r.numbers[0] || v.numbers[1] !== r.numbers[1]) return false;
  
  // 当前版本 >= 范围版本
  return compareVersions(version, rangeVersion) >= 0;
}