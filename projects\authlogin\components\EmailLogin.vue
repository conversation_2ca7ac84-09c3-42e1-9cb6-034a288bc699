<template>
    <div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
            <el-form-item prop="email">
                <el-input maxlength="32" v-model="loginForm.email" placeholder="邮箱号"
                    prefix-icon="iconfont ali-icon-youxiang" />
            </el-form-item>
            <el-form-item prop="emailCode">
                <ValidateCodeInput :countdown="countdown" v-model="loginForm.emailCode" prefix-icon="el-icon-message"
                    @send="clickSend()">
                </ValidateCodeInput>
            </el-form-item>

            <el-form-item>
                <el-button v-enter="handleLogin" :loading="loading" type="primary" class="login-button"
                    @click="handleLogin">登
                    录</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { sendCode } from '@/api/login/notice'
import { validateEmail, emailPattern } from './consts'
import ValidateCodeInput from './ValidateCodeInput.vue';
import EmailValidator from './EmailValidator.js'
import LoginMixin from "./LoginMixin.js";

export default {
    components: {
        ValidateCodeInput
    },
    mixins: [LoginMixin],
    data() {
        return {
            loading: false,
            countdown: 0,
            loginForm: {
                email: '',
                emailCode: ''
            },

            loginRules: {
                email: [
                    { required: true, trigger: 'blur', validator: validateEmail }
                ],
                emailCode: [
                    { required: true, message: '请输入短信验证码', trigger: 'submit' },
                    { len: 6, message: '验证码长度应为6位', trigger: 'submit' }
                ]
            }
        }
    },

    destroyed() {
        this.timerInterval && clearInterval(this.timerInterval)
    },

    methods: {

        startCountdown() {
            this.countdown = 60
            this.timerInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.timerInterval && clearInterval(this.timerInterval)
                }
            }, 1000)
        },

        clickSend() {
            if (!EmailValidator.validateEmail(this.loginForm.email)) {
                this.$message.error('请输入正确的邮箱号')
                return
            }
            if (this.countdown > 0) {
                return
            }
            sendCode({
                identifier: this.loginForm.email,
                type: 'email',
                scene: 'login'
            }).then(res => {
                this.$message.success('验证码已发送')
                this.startCountdown()
            }).catch(err => {
                this.$message.error(err || '发送失败')
            })
        },
        handleLogin() {

            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    if (!this.checkAgreement()) {
                        return
                    }
                    this.loading = true
                    this.$store
                        .dispatch("user/emailLogin", {data: {
                            email: this.loginForm.email,
                            code: this.loginForm.emailCode
                        }}).then(res => {
                            this.loading = false
                            this.loginComplete(res)
                        }).catch((err) => {
                            this.loading = false
                            console.log("error submit!!", err)
                            this.loginError(err)
                        })
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.login-form {
    .el-input {
        height: 44px;

        ::v-deep .el-input__inner {
            height: 44px;
            padding-left: 48px;
            font-size: 16px;
        }

        ::v-deep .el-input__prefix {
            left: 10px;
            font-size: 18px;
        }

        .el-input__inner:focus {
            border-color: #1682e6;
        }
    }

    .sms {
        height: 100%;
        display: flex;
        align-items: center;

        .el-button:not(:disabled) {
            background-color: rgb(0, 122, 255);
            color: #fff;
            border: none;

            &:hover {
                background-color: rgb(0, 86, 179);
            }
        }

        .el-button:disabled {
            border: none;
        }
    }

    .login-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        margin-top: 20px;
        background: rgb(0, 122, 255);
        border-radius: 8px;
        border: none;

        &:hover {
            background: rgb(0, 86, 179);
        }
    }
}
</style>
