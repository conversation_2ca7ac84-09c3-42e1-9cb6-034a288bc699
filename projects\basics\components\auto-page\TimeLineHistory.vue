<template>
    <div>
        <h3>历史审批意见</h3>
        <el-timeline v-if="historyList.length > 0">
            <el-timeline-item
                v-for="(item, index) in historyList"
                :key="index"
                :timestamp="item.created_time">
                <div>审批人：{{ item.submitter || item.approver }}</div>
                <div>审批意见：{{ item.content }}</div>
            </el-timeline-item>
        </el-timeline>
        <div v-if="historyList.length === 0">暂无数据</div>
    </div>
</template>

<script>
    export default {
        name: 'TimeLineHistory',
        props: {
            historyList: {
                type: Array,
                default: () => []
            }
        },
        data() {
            return {
            }
        },
        created() {
        },
        methods: {
        }
    }
</script>

<style lang="scss" scoped>

</style>