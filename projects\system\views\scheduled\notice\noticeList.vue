<template>
  <el-card shadow="always">
    <div class="label" slot="header">
      <label>通知记录</label>
      <div v-show="value">
        <el-date-picker
          v-model="requestParams.ranges.createTime"
          type="datetimerange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          clearable
          value-format="yyyy-MM-dd HH:mm:ss"
          class="search-item"
          @change="refreshList"
        >
        </el-date-picker>
        <el-select v-model="requestParams.responseCode" class="search-item" clearable placeholder="执行结果" @change="refreshList">
          <el-option v-for="entry in Object.entries(dict.responseCode)" :value="entry[0]" :label="entry[1].label" :key="entry[0]"></el-option>
        </el-select>
      </div>
    </div>
    <div>
      <!-- 表格视图 -->
      <el-table :data="records" border style="height: 57vh; overflow-y: auto;" v-loading="loading">
        <el-table-column type="expand">
          <template slot-scope="props">
            <div v-html="JSON.parse(props.row.messageContent).content" />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="运行时间" />
        <el-table-column prop="providerName" label="通知渠道" />
        <el-table-column prop="responseCode" label="执行结果">
          <template slot-scope="scope">
            <el-tag size="medium" :type="dict.responseCode[scope.row.responseCode].tag">{{ dict.responseCode[scope.row.responseCode].label }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 10px;">
        <el-pagination :current-page.sync="requestParams.current" :page-sizes="[10, 20, 50, 100]" :page-size.sync="requestParams.size" @size-change="refreshList" @current-change="refreshList"
          layout="total, sizes, prev, pager, next, jumper" :total="total" />
      </div>
    </div>
  </el-card>
</template>
<script>
import api from "@system/api/scheduled";
import * as echarts from 'echarts';
import {getMessageLogList} from '@system/api/notice/manager'
import dict from './dict'

export default {
  name: 'noticeList',
  props: {
    value: {
      type: [String, Number]
    }
  },
  data() {
    return {
      records: [],
      loading: false,
      total: 0,
      requestParams: {
        current: 1,
        size: 10,
        ranges: {},
        orders: {
          createTime: 'asc'
        }
      }
    }
  },
  computed: {
    dict() {
      return dict
    }
  },
  watch: {
    value: {
      handler(val) {
        this.refreshList()
      },
      immediate: true
    }
  },
  methods: {
    refreshList() {
      if (this.value) {
        this.loading = true
        this.requestParams.presetTaskId = this.value
        getMessageLogList(this.requestParams).then(res => {
          this.total = res.total
          this.records = res.records
          this.loading = false
        }).catch(e => {
          this.loading = false
        })
      } else {
        this.records = []
        this.total = 0
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-item {
  margin-left: 20px;
}
</style>