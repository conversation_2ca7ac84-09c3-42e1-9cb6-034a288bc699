export default {
  api:'sys/notice',
  search: {
    isShow: true,
    showReset: true
  },
  table: {},
  formRule:[
    {
      type: "input",
      field: "title",
      className: "title-dom",
      title: "标题",
      isSearch:true,
      isSearchCol:{md:{span:10}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 18}},
      props:{
        placeholder:"请输入标题",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"标题不能为空"
        }
      ]
    },
    {
      type: "select",
      field: "type",
      className: "type-dom",
      title: "类型",
      isSearch: true,
      isSearchValidate: [],
      isSearchCol: {md: {span: 10}},
      options: [
        {value: 1, label: "类型1", disabled: false},
        {value: 2, label: "类型2", disabled: false}
      ],
      isTable: true,
      col: {md: {span: 18}},
      props: {
        multiple: false,
        placeholder: "请选择类型",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change',
          required: true,
          message: "请选择类型"
        }
      ],
    },
    {
      type: "input",
      field: "content",
      className: "content-dom",
      title: "内容",
      isSearch:false,
      isTable:true,
      col:{md:{span: 18}},
      props:{
        placeholder:"请输入内容",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"内容不能为空"
        }
      ]
    },
    {
      type: "input",
      field: "createBy",
      className: "createBy-dom",
      title: "创建人",
      isScope:true,
      isSearch:false,
      isTable:true,
      isHidden:true,
    },
    {
      type: "input",
      field: "remark",
      className: "remark-dom",
      title: "备注",
      isSearch:false,
      isTable:false,
      col:{md:{span: 18}},
      props:{
        type: "textarea",
        placeholder:"请输入备注",
        readonly: false,
        clearable: true,
        disabled: false
      },
    },

  ]
}
