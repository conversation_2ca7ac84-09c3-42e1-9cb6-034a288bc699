<template>
  <div>
    <!-- 执行输入对话框 -->
    <el-dialog
      title="执行规则链"
      :visible.sync="executeDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      custom-class="chain-dialog execute-dialog"
    >
      <div class="dialog-content">
        <div class="form-section">
          <div class="section-title">
            <i class="el-icon-video-play"></i>
            <span>执行设置</span>
          </div>
          
          <el-form ref="executeForm" :model="executeForm" label-width="100px">
            <el-form-item label="规则链名称">
              <el-input v-model="executeForm.chainName" disabled></el-input>
            </el-form-item>
            
            <el-form-item label="上下文数据">
              <div class="editor-wrapper">
                <div class="editor-container" id="context-editor"></div>
              </div>
              <div class="context-tip">
                <i class="el-icon-info"></i>
                <span>请输入JSON格式的上下文数据，可以为空</span>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="executeDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmExecute" 
          :loading="executing"
        >执行</el-button>
      </div>
    </el-dialog>

    <!-- 执行结果对话框 -->
    <el-dialog
      title="执行结果"
      :visible.sync="resultDialogVisible"
      width="60%"
      append-to-body
      destroy-on-close
      custom-class="chain-dialog result-dialog"
    >
      <div v-loading="executing" class="dialog-content">
        <div v-if="executeResult" class="result-content">
          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-s-operation"></i>
              <span>执行步骤</span>
            </div>
            <div class="execution-steps">
              <div class="step-value">{{ executeResult.executeStepStr }}</div>
            </div>
          </div>
          
          <div class="form-section">
            <div class="section-title">
              <i class="el-icon-document"></i>
              <span>上下文数据</span>
            </div>
            <div class="editor-wrapper">
              <div class="editor-container" id="result-editor"></div>
            </div>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="resultDialogVisible = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { executeRuleChain } from '@system/api/rule/chain'
import * as monaco from 'monaco-editor'

export default {
  name: 'ExecuteDialog',
  props: {
    // 链名称
    chainName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      executeDialogVisible: false,
      resultDialogVisible: false,
      executing: false,
      executeForm: {
        chainName: '',
        contextData: {}
      },
      executeResult: null,
      contextEditor: null,
      resultEditor: null
    }
  },
  watch: {
    chainName: {
      immediate: true,
      handler(val) {
        this.executeForm.chainName = val
      }
    }
  },
  methods: {
    // 打开执行对话框
    open() {
      this.executeDialogVisible = true
      this.$nextTick(() => {
        setTimeout(() => {
          this.createContextEditor()
        }, 100)
      })
    },
    
    // 创建上下文编辑器
    createContextEditor() {
      const container = document.getElementById('context-editor')
      if (!container) {
        console.error('找不到编辑器容器')
        return
      }

      // 销毁已存在的编辑器
      if (this.contextEditor) {
        this.contextEditor.dispose()
      }

      try {
        // 确保容器有明确的尺寸
        container.style.width = '100%'
        container.style.height = '200px'
        
        // 创建新的编辑器实例
        this.contextEditor = monaco.editor.create(container, {
          value: JSON.stringify({
            input: '',
            timestamp: Date.now()
          }, null, 2),
          language: 'json',
          theme: 'vs-dark',
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 14,
          tabSize: 2
        })
        
        // 手动触发布局更新
        setTimeout(() => {
          if (this.contextEditor) {
            this.contextEditor.layout()
          }
        }, 200)
      } catch (error) {
        console.error('创建编辑器失败:', error)
      }
    },
    
    // 创建结果编辑器
    createResultEditor() {
      const container = document.getElementById('result-editor')
      if (!container) {
        console.error('找不到结果编辑器容器')
        return
      }

      try {
        // 确保容器有明确的尺寸
        container.style.width = '100%'
        container.style.height = '300px'
        
        // 创建新的编辑器实例
        if (this.resultEditor) {
          this.resultEditor.dispose()
        }
        
        // 格式化JSON数据
        const resultData = JSON.stringify(this.executeResult.contextData, null, 2)
        
        this.resultEditor = monaco.editor.create(container, {
          value: resultData,
          language: 'json',
          theme: 'vs-dark',
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 14,
          tabSize: 2,
          readOnly: true // 设置为只读
        })
        
        // 手动触发布局更新
        setTimeout(() => {
          if (this.resultEditor) {
            this.resultEditor.layout()
          }
        }, 200)
      } catch (error) {
        console.error('创建结果编辑器失败:', error)
      }
    },
    
    // 确认执行
    async confirmExecute() {
      if (!this.contextEditor) return
      
      let contextData = {}
      try {
        const editorContent = this.contextEditor.getValue()
        if (editorContent.trim()) {
          contextData = JSON.parse(editorContent)
        }
      } catch (error) {
        this.$message.error('上下文数据格式不正确，请输入有效的JSON格式')
        return
      }
      
      this.executing = true
      try {
        const data = await executeRuleChain({
          chainName: this.executeForm.chainName,
          contextData
        })
        
        this.executeResult = data
        this.executeDialogVisible = false
        this.resultDialogVisible = true
        
        // 显示结果对话框后创建结果编辑器
        this.$nextTick(() => {
          setTimeout(() => {
            this.createResultEditor()
          }, 100)
        })
      } catch (error) {
        this.$message.error('执行失败：' + (error.message || '未知错误'))
      } finally {
        this.executing = false
      }
    }
  },
  beforeDestroy() {
    if (this.contextEditor) {
      this.contextEditor.dispose()
    }
    if (this.resultEditor) {
      this.resultEditor.dispose()
    }
  }
}
</script>

<style lang="scss" scoped>
.chain-dialog {
  ::v-deep .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
    margin-top: 8vh !important;
    max-height: 84vh;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
      padding: 24px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
      
      .el-dialog__headerbtn {
        top: 20px;
        right: 20px;
        
        .el-dialog__close {
          font-size: 18px;
          color: #909399;
          font-weight: bold;
          
          &:hover {
            color: #409EFF;
          }
        }
      }
    }

    .el-dialog__body {
      padding: 0;
      overflow-y: auto;
      background-color: #f8f9fb;

      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 3px;
        
        &:hover {
          background: rgba(144, 147, 153, 0.5);
        }
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }

    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #ebeef5;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);
      
      .el-button {
        padding: 9px 20px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

        & + .el-button {
          margin-left: 12px;
        }

        &--default {
          border-color: #dcdfe6;
          background: linear-gradient(to bottom, #fff, #f9fafc);
          
          &:hover {
            border-color: #c0c4cc;
            color: #606266;
            background: #f5f7fa;
          }
        }

        &--primary {
          background: #409EFF;
          border-color: #409EFF;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          
          &:hover {
            transform: translateY(-2px);
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
  }
  
  .dialog-content {
    padding: 24px;
  }
  
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);
    
    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
        color: #409EFF;
        font-size: 18px;
      }
    }
    
    ::v-deep .el-form-item {
      margin-bottom: 22px;
      
      &:last-child {
        margin-bottom: 0;
      }

      .el-form-item__label {
        line-height: 1.4;
        padding-bottom: 8px;
        color: #1a1f36;
        font-weight: 500;
        display: flex;
        align-items: center;
        height: 38px;
      }

      .el-input__inner {
        border-radius: 10px;
        height: 38px;
        border: 1px solid #e0e5ee;
        
        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
        }
        
        &:hover {
          border-color: #c0d0e9;
        }
      }
    }
  }
  
  .editor-wrapper {
    width: 100%;
    height: 200px;
    position: relative;
    margin-bottom: 10px;
    border-radius: 10px;
    overflow: hidden;
  }
  
  .editor-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100% !important;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e0e5ee;
    transition: all 0.3s;
    
    &:hover {
      border-color: #c0d0e9;
    }
    
    &:focus-within {
      border-color: #409EFF;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
    }
  }
  
  .context-tip {
    color: #909399;
    font-size: 12px;
    line-height: 1.2;
    margin-top: 8px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 4px;
      color: #409EFF;
    }
  }
}

.result-content {
  .execution-steps {
    position: relative;
    
    .step-value {
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
      line-height: 1.6;
      color: #1a1f36;
      position: relative;
      padding-left: 20px;
      
      &:before {
        content: '';
        position: absolute;
        left: 6px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 1px;
      }
    }
  }
  
  .editor-wrapper {
    height: 300px;
  }
}
</style> 