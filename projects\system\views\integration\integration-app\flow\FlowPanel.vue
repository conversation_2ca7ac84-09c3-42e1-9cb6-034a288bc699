<template>
    <div class="integration-flow" v-resize-detector="resize">
        <!-- 流程选择器、发布状态和操作工具栏 -->
        <div class="flow-header">
            <!-- 左侧：合并后的发布状态控制（包含流程选择功能） -->
            <div class="publish-control" v-if="currentFlow">
                <!-- 流程选择器（仅在有多个流程时显示） -->
                <div class="flow-selector-section" v-if="availableFlows.length > 1">
                    <div class="selector-label">画布选择:</div>
                    <el-select
                        v-model="currentFlowId"
                        @change="switchFlow"
                        placeholder="选择集成版本"
                        class="flow-select"
                        size="small"
                    >
                        <el-option
                            v-for="flow in availableFlows"
                            :key="flow.id"
                            :label="flow.name || `集成板 ${flow.id}`"
                            :value="flow.id"
                        >
                            <span class="option-name">{{ flow.name || `集成板 ${flow.id}` }}</span>
                            <span class="option-code">{{ flow.code }}</span>
                        </el-option>
                    </el-select>
                </div>

                <!-- 发布状态信息 -->
                <div class="publish-info">
                    <el-tag
                        size="small"
                        :type="currentFlow.published ? 'success' : 'warning'"
                        effect="plain"
                        class="status-tag"
                    >
                        {{ currentFlow.published ? '已发布' : '草稿' }}
                    </el-tag>
                </div>

                <!-- 发布状态切换 -->
                <div class="publish-switch">
                    <el-switch
                        v-model="currentFlow.published"
                        active-text=""
                        inactive-text=""
                        :loading="publishLoading"
                        @change="handlePublishChange"
                        size="small"
                    />
                </div>
            </div>

            <!-- 右侧：操作按钮 -->
            <div class="action-tools">
                <el-button type="primary" @click="saveFlow()" :loading="loading">保存</el-button>
            </div>
        </div>

        <!-- 直接显示流程画布，无需标签页包装 -->
        <div class="flow-canvas-container">
            <flow ref="flow" @init="initFlow"></flow>
        </div>
    </div>
</template>
<script>
import flow from '@extreme-flow/flow.vue'

import { updateFlowCanvas, getFlowCanvasDetail, getFlowCanvasList, registerFlowCanvas, unregisterFlowCanvas } from '@system/api/integration/flow-app.js'

export default {
    components: {
        flow
    },
    props: {
        appId: {
            type: [String, Number],
            default: '229844566202'
        },
        flowId: {
            type: [String, Number],
            default: '1948575107825844225'
        }
    },
    data() {
        return {
            flowData: {},
            availableFlows: [],
            currentFlowId: null,
            loading: false,
            currentFlow: null, // 当前选中的流程对象
            publishLoading: false // 发布状态切换加载状态
        }
    },
    mounted() {
        this.initializeFlow()
    },
    watch: {
        // 监听flowId变化，处理从集成板导航的情况
        flowId: {
            handler(newFlowId, oldFlowId) {
                if (newFlowId && newFlowId !== oldFlowId) {
                    this.currentFlowId = newFlowId
                    this.loadFlowData()

                    // 更新下拉框选中状态
                    const selectedFlow = this.availableFlows.find(flow =>
                        flow.id == newFlowId || flow.id === newFlowId
                    )
                    if (selectedFlow) {
                        this.currentFlow = { ...selectedFlow } // 设置当前流程对象
                        this.$emit('flow-selected', selectedFlow)
                    }
                }
            },
            immediate: false
        }
    },
    methods: {
        // 初始化流程 - 支持自动选择和从集成板导航
        async initializeFlow() {
            // 首先加载可用的流程列表
            await this.loadAvailableFlows()

            if (this.flowId) {
                // 如果指定了flowId（从集成板导航过来），直接使用它
                this.currentFlowId = this.flowId
                this.loadFlowData()

                // 确保下拉框显示正确的选中项
                const selectedFlow = this.availableFlows.find(flow =>
                    flow.id == this.flowId || flow.id === this.flowId
                )
                if (selectedFlow) {
                    this.currentFlow = { ...selectedFlow } // 设置当前流程对象
                    this.$emit('flow-selected', selectedFlow)
                }
            } else if (this.availableFlows.length > 0) {
                // 如果没有指定flowId，自动选择第一个可用的流程
                this.currentFlowId = this.availableFlows[0].id
                this.currentFlow = { ...this.availableFlows[0] } // 设置当前流程对象
                this.loadFlowData()
                // 通知父组件当前选中的流程
                this.$emit('flow-selected', this.availableFlows[0])
            }
        },

        // 加载可用的流程列表
        async loadAvailableFlows() {
            if (!this.appId) return

            this.loading = true
            try {
                const response = await getFlowCanvasList({
                    current: 1,
                    size: -1,
                    integrationAppId: this.appId
                })
                this.availableFlows = response.records || []
            } catch (error) {
                console.error('加载流程列表失败:', error)
                this.$message.error('加载流程列表失败')
                this.availableFlows = []
            } finally {
                this.loading = false
            }
        },

        // 切换流程
        switchFlow(flowId) {
            const selectedFlow = this.availableFlows.find(flow => flow.id === flowId)
            if (selectedFlow) {
                this.currentFlowId = flowId
                this.currentFlow = { ...selectedFlow } // 设置当前流程对象
                this.loadFlowData()
                this.$emit('flow-selected', selectedFlow)
            }
        },

        initFlow() {
            if (this.currentFlowId || this.flowId) {
                this.loadFlowData();
            }
        },
        loadFlowData() {
            const flowId = this.currentFlowId || this.flowId
            if (!flowId) return

            getFlowCanvasDetail(flowId).then(res => {
                this.$nextTick(() => {
                    res.canvas && this.$refs.flow.render(JSON.parse(res.canvas))
                })
            }).catch(error => {
                console.error('加载流程数据失败:', error)
                this.$message.error('加载流程数据失败')
            })
        },
        resize() {
            this.$refs.flow.resize()
        },
        saveFlow() {
            const flowId = this.currentFlowId || this.flowId
            if (!flowId) {
                this.$message.warning('请先选择一个流程')
                return
            }

            let json = this.$refs.flow.getFlowJson()
            this.loading = true
            updateFlowCanvas({
                id: flowId,
                integrationAppId: this.appId,
                canvas: json
            }).then(() => {
                this.$message.success('保存成功');
                this.loading = false
            }).catch(error => {
                console.error('保存失败:', error)
                this.$message.error('保存失败')
                this.loading = false
            });
        },

        // 发布状态切换 - 先保存再发布
        async handlePublishChange(newValue) {
            if (!this.currentFlow) return

            const originalValue = !newValue
            this.publishLoading = true

            try {
                if (newValue) {
                    // 发布流程：1. 先保存当前流程配置
                    const flowId = this.currentFlowId || this.flowId
                    if (!flowId) {
                        this.$message.warning('请先选择一个流程')
                        this.currentFlow.published = originalValue
                        return
                    }

                    // 获取当前流程配置并保存
                    // let json = this.$refs.flow.getFlowJson()
                    // await updateFlowCanvas({
                    //     id: flowId,
                    //     integrationAppId: this.appId,
                    //     canvas: json
                    // })

                    // 2. 然后调用注册接口发布
                    await registerFlowCanvas(this.currentFlow.id)
                    this.$message.success(`集成板"${this.currentFlow.name || this.currentFlow.id}"发布成功`)
                } else {
                    // 取消发布：调用卸载接口
                    await unregisterFlowCanvas(this.currentFlow.id)
                    this.$message.success(`集成板"${this.currentFlow.name || this.currentFlow.id}"已取消发布`)
                }

                // 重新加载流程列表以保持状态同步
                await this.loadAvailableFlows()

                // 更新当前流程对象的发布状态
                this.currentFlow.published = newValue

                // 通知父组件状态变化
                this.$emit('flow-publish-changed', {
                    flow: this.currentFlow,
                    published: newValue
                })

            } catch (error) {
                console.error('发布状态切换失败:', error)
                // 恢复原始状态
                this.currentFlow.published = originalValue
            } finally {
                this.publishLoading = false
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.integration-flow {
    position: relative;
    width: 100%;
    height: 100%;

    .flow-header {
        position: absolute;
        top: 12px;
        left: 20px;
        right: 20px;
        z-index: 10;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;
        min-height: 40px; /* 确保一致的高度 */
    }



    .publish-control {
        display: flex;
        align-items: center;
        gap: 16px;
        background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
        padding: 8px 16px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
        border: 1px solid #e1e8ed;
        min-height: 40px;
        backdrop-filter: blur(8px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        flex-shrink: 0; /* 防止收缩 */
        white-space: nowrap; /* 防止换行 */

        /* 流程选择器部分 */
        .flow-selector-section {
            display: flex;
            align-items: center;
            gap: 12px;
            padding-right: 16px;
            border-right: 1px solid #e1e8ed;

            .selector-label {
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
                white-space: nowrap;
                letter-spacing: 0.025em;
                display: flex;
                align-items: center;

                &::before {
                    content: '';
                    width: 4px;
                    height: 16px;
                    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                    border-radius: 2px;
                    margin-right: 8px;
                }
            }

            .flow-select {
                min-width: 180px;
                max-width: 280px;

                ::v-deep .el-input__inner {
                    border: 1px solid #d1d9e0;
                    border-radius: 8px;
                    font-size: 13px;
                    height: 28px;
                    padding: 0 10px;
                    background: rgba(255, 255, 255, 0.9);
                    color: #1e293b;
                    font-weight: 500;
                    transition: all 0.2s ease;

                    &:hover {
                        border-color: #3b82f6;
                        background: #ffffff;
                        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
                    }

                    &:focus {
                        border-color: #3b82f6;
                        background: #ffffff;
                        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 8px rgba(59, 130, 246, 0.15);
                    }
                }

                ::v-deep .el-input__suffix {
                    right: 6px;

                    .el-input__suffix-inner {
                        .el-select__caret {
                            color: #64748b;
                            font-size: 12px;
                            transition: all 0.2s ease;
                        }
                    }
                }

                &.is-focus {
                    ::v-deep .el-input__suffix-inner .el-select__caret {
                        color: #3b82f6;
                    }
                }
            }
        }

        .publish-info {
            display: flex;
            align-items: center;
            gap: 8px;

            .status-tag {
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 6px;
                font-weight: 500;
                line-height: 1;
            }

            .flow-name {
                font-size: 14px;
                font-weight: 600;
                color: #1e293b;
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .publish-switch {
            display: flex;
            align-items: center;
            gap: 8px;

            ::v-deep .el-switch {
                &.el-switch--small {
                    .el-switch__core {
                        width: 32px;
                        height: 18px;
                        border-radius: 9px;

                        &:after {
                            width: 14px;
                            height: 14px;
                            border-radius: 7px;
                        }
                    }

                    &.is-checked .el-switch__core:after {
                        margin-left: -15px;
                    }
                }

                .el-switch__core {
                    background: #dcdfe6;
                    border: none;

                    &:after {
                        background: white;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
                    }
                }

                &.is-checked .el-switch__core {
                    background: #10b981;
                }
            }

            .publish-label {
                font-size: 12px;
                color: #64748b;
                font-weight: 500;
                min-width: 32px;
            }
        }
    }

    /* 下拉框选项样式 */
    .option-name {
        font-weight: 600;
        color: #1e293b;
        display: block;
        font-size: 14px;
        line-height: 1.3;
    }

    .option-code {
        font-size: 12px;
        color: #64748b;
        margin-left: 8px;
        font-family: 'SF Mono', 'Monaco', 'Menlo', monospace;
        background: rgba(59, 130, 246, 0.08);
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
    }

    /* 增强下拉框样式 */
    ::v-deep .el-select-dropdown {
        border: 1px solid #e1e8ed !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08) !important;
        padding: 8px 0 !important;
        background: #ffffff !important;
        backdrop-filter: blur(12px);

        .el-select-dropdown__item {
            padding: 12px 16px !important;
            line-height: 1.4 !important;
            transition: all 0.2s ease !important;
            border-radius: 0 !important;
            margin: 0 8px !important;
            border-radius: 8px !important;

            &:hover {
                background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
                color: #1e293b !important;
            }

            &.selected {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
                color: #ffffff !important;
                font-weight: 600 !important;

                .option-name {
                    color: #ffffff !important;
                }

                .option-code {
                    background: rgba(255, 255, 255, 0.2) !important;
                    color: #ffffff !important;
                }
            }

            .option-name {
                display: block !important;
                margin-bottom: 4px !important;
            }

            .option-code {
                display: inline-block !important;
                margin-left: 0 !important;
                margin-top: 0 !important;
            }
        }
    }

    .flow-canvas-container {
        position: absolute;
        top: 64px; /* 减少顶部间距，因为工具栏现在在同一行 */
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        background: #fafbfc;
        border-radius: 12px 12px 0 0;
        border: 1px solid #e1e8ed;
        border-bottom: none;
    }

    .action-tools {
        display: flex;
        gap: 8px;
        flex-shrink: 0; /* 防止收缩 */
        align-items: center;

        .el-button {
            height: 36px;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
            border: 1px solid #d1d9e0;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            color: #1e293b;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                border-color: #3b82f6;
                color: #3b82f6;
            }

            &.el-button--primary {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border-color: #3b82f6;
                color: #ffffff;

                &:hover {
                    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                    border-color: #1d4ed8;
                    color: #ffffff;
                }
            }
        }
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .flow-header {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
            height: auto;
            min-height: auto;
        }



        .publish-control {
            flex-direction: column;
            gap: 12px;
            height: auto;
            min-height: auto;

            .flow-selector-section {
                border-right: none;
                border-bottom: 1px solid #e1e8ed;
                padding-right: 0;
                padding-bottom: 12px;
            }
        }

        .flow-canvas-container {
            top: 116px; /* 调整为垂直布局时的高度 */
        }
    }

    @media (max-width: 768px) {
        .flow-header {
            position: relative;
            top: auto;
            left: auto;
            right: auto;
            margin: 12px 16px 8px 16px;
        }



        .publish-control {
            width: 100%;
            min-height: 36px;
            flex-direction: column;
            gap: 8px;

            .flow-selector-section {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e1e8ed;
                padding-right: 0;
                padding-bottom: 8px;

                .flow-select {
                    min-width: auto;
                    max-width: none;
                    flex: 1;
                }
            }

            .publish-info {
                .flow-name {
                    max-width: 120px;
                }
            }
        }

        .action-tools {
            width: 100%;
            justify-content: flex-end;
            flex-wrap: wrap;
            gap: 6px;

            .el-button {
                height: 32px;
                padding: 0 12px;
                font-size: 13px;
            }
        }

        .flow-canvas-container {
            position: relative;
            top: auto;
            margin: 0 16px 16px 16px;
            height: calc(100vh - 240px); /* 根据头部元素调整高度 */
        }
    }

    /* 中等屏幕优化 */
    @media (max-width: 1024px) and (min-width: 769px) {
        .flow-header {
            gap: 12px;
        }



        .action-tools {
            gap: 6px;

            .el-button {
                padding: 0 12px;
                font-size: 13px;
            }
        }

        .publish-control {
            .flow-selector-section {
                .flow-select {
                    min-width: 160px;
                    max-width: 240px;
                }
            }

            .publish-info {
                .flow-name {
                    max-width: 140px;
                }
            }
        }
    }

    /* 确保下拉框不被截断 */
    ::v-deep .el-select-dropdown {
        z-index: 9999 !important;
    }

    /* 最大化流程画布空间 */
    ::v-deep .flow-container {
        height: 100% !important;
        width: 100% !important;
        overflow: hidden;
    }

    /* 确保流程组件使用所有可用空间 */
    flow {
        display: block;
        width: 100%;
        height: 100%;
        flex: 1;
    }
}
</style>