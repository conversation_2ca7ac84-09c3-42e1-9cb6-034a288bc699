<template>
  <div class="amap-page-container">
    <el-amap :center="center" :zoom="zoom" @init="init" @click="clickMarker" class="amap-demo" :disabled="disabled">
      <el-amap-search-box placeholder="请输入关键字" :debounce="1000" @select="selectPoi"
        @choose="choosePoi"></el-amap-search-box>
      <el-amap-marker v-for="(marker, index) in markers" :key="index" :position="marker.position"
        @click="(e) => { clickArrayMarker(marker, e) }"></el-amap-marker>
      <el-amap-polygon v-if="polygon.path" @adjust="getPo" @end="getPo" :path="polygon.path" :visible="polygon.visible"
        :editable="polygon.edit" :draggable="polygon.draggable"></el-amap-polygon>
    </el-amap>
  </div>
</template>

<script>
export default {
  data() {
    return {
      center: [111.55797063, 30.65971344],
      map: null,
      markers: [],
      polygon: {
        draggable: !this.disabled,
        visible: true,
        edit: !this.disabled,
        path: [],
      }
    }
  },
  model: {
    props: 'value',
    event: 'value'
  },
  props: {
    coordinates: Array,
    mode: {
      type: Number,
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: [Object, Array],
      default: null
    },
    zoom: {
      type: Number,
      default: 11
    },
    type: {
      type: String,
      default: 'Point'
    }
  },
  computed: {
    finValue() {
      if (this.value) {
        let obj = {
          ...this.value
        }
        if (this.type == 'Point') {
          obj.coordinates = this.markers.length ? this.markers[0].position : []
        } else if (this.type == 'Polygon') {
          obj.coordinates = this.polygon.path.length ? [this.polygon.path] : []
        }
        return obj
      } else {
        let coordinates = []
        if (this.type == 'Point') {
          coordinates = this.markers.length ? this.markers[0].position : []
        } else if (this.type == 'Polygon') {
          coordinates = this.polygon.path.length ? [this.polygon.path] : []
        }
        return {
          type: this.type,
          crs: {
            type: 'name',
            properties: {
              name: 'EPSG:0'
            }
          },
          coordinates
        }
      }
    },
  },
  watch: {
    finValue: {
      handler(val) {
        if (this.mode >= 1) {
          this.$emit('value', val)
        }
      },
      deep: true
    },
    coordinates(n) {
      this.center = n
    }
  },

  mounted() {
    if (this.coordinates) {
      this.center = this.coordinates
    }
  },
  methods: {
    getPo(e, t) {
      this.polygon.path = e.target._opts.path
    },
    offset(lat, lon) {
      return [lat + 0.0056, lon - 0.0028]
    },
    init(map) {
      this.map = map;
      if (this.value) {
        if (this.type == 'Point') {
          this.center = this.offset(...this.value.coordinates)
          this.markers = [{ position: this.offset(...this.value.coordinates) }]
        } else if (this.type == 'Polygon') {
          this.polygon.path = this.value.coordinates[0]?.map(m => this.offset(...m)) || []
        }
      }
    },
    clickMarker(w) {
      if (this.type == 'Point') {
        this.markers = [{
          position: [w.lnglat.lng, w.lnglat.lat]
        }]
        this.$emit('value', [w.lnglat.lng, w.lnglat.lat])
      } else if (this.type == 'Polygon' && !this.polygon.path.length) {
        let pr = 0.02
        this.polygon.path = [[w.lnglat.lng, w.lnglat.lat], [w.lnglat.lng + pr, w.lnglat.lat], [w.lnglat.lng + pr, w.lnglat.lat + pr], [w.lnglat.lng, w.lnglat.lat + pr],]
      }
    },
    selectPoi(e) {
      let position = [e.poi.location.lng, e.poi.location.lat]
      this.center = position
      if (this.type == "Polygon") {
        return;
      }
      this.markers = [{
        position
      }]
      this.$emit('value', position)
    },
    choosePoi(e) {
      console.log('choosepoi', e)
    },
    clickArrayMarker() {

    }
  }
}
</script>

<style>
.amap-sug-result {
  z-index: 3000 !important;
}
</style>
<style lang="scss" scoped>
.amap-page-container {
  height: 400px;
  width: 400px;
}

.amap-demo {
  height: 100%;
  width: 100%;
}
</style>