<template>
  <div class="module-select">
    <TopBar :is-module-select="true" />
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <h1>欢迎回来，{{ $store.state.user.nickName || $store.state.user.name}}！</h1>
        <p class="subtitle">请选择要进入的系统</p>
      </div>

      <!-- 最近使用模块 -->
      <div v-if="recentModules?.length > 0 && (modules?.length > 0 || apps?.length > 0)" class="recent-modules">
        <h2>最近使用</h2>
        <div class="module-list">
          <div v-for="module in recentModules" :key="module.id" class="recent-module-item"
            @click="navigateToModule(module)">
            <!-- <i :class="module.icon"></i> -->
            <svg-icon :icon-class="module.icon" style="height: 30px;width: 16px;" />
            <span>{{ module.name }}</span>
          </div>
        </div>
      </div>

      <!-- 所有模块 -->
      <div class="all-modules" v-if="modules?.length > 0">
        <h2>所有模块</h2>
        <el-row :gutter="24">
          <el-col v-for="(module, index) in modules" :key="module.id" :xs="24" :sm="12" :md="8" :lg="6">
            <div class="module-card" @click="navigateToModule(module)"
              :style="{ '--color': colors[index % colors.length] }">
              <div class="module-icon" :style="{ color: colors[index % colors.length] }">
                <!-- <i :class="module.icon"></i> -->
                <svg-icon :icon-class="module.icon" style="height: 60px;width: 32px;" />
              </div>
              <div class="module-info">
                <h3 :title="module.name">{{ module.name }}</h3>
                <p>{{ module.remark }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="all-modules" v-if="apps?.length > 0">
        <h2>应用列表</h2>
        <el-row :gutter="24">
          <el-col v-for="(module, index) in apps" :key="module.id" :xs="24" :sm="12" :md="8" :lg="6">
            <div class="module-card" @click="linkTo(module.address)"
              :style="{ '--color': colors[index % colors.length] }">
              <div class="module-icon" :style="{ color: colors[index % colors.length] }">
                <!-- <i :class="module.icon"></i> -->
                <svg-icon :icon-class="module.icon" style="height: 60px;width: 32px;" />
              </div>
              <div class="module-info">
                <h3 :title="module.name">{{ module.name }}</h3>
                <p>{{ module.description }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 个人信息对话框 -->
    <el-dialog title="个人信息" :visible.sync="profileDialogVisible" width="460px" custom-class="profile-dialog">
      <div class="profile-content">
        <div class="profile-header">
          <el-avatar :size="88" :src="userInfo.avatar" :style="{ backgroundColor: '#409EFF' }">
            {{ (userInfo.nickName || userInfo.name)?.charAt(0) }}
          </el-avatar>
          <h2>{{ userInfo.nickName || userInfo.name }}</h2>
          <p class="role-tag">{{ userInfo.role }}</p>
        </div>
        <div class="profile-info">
          <div class="info-item">
            <i class="el-icon-user"></i>
            <label>工号</label>
            <span>{{ userInfo.employeeId }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-office-building"></i>
            <label>部门</label>
            <span>{{ userInfo.department }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-message"></i>
            <label>邮箱</label>
            <span>{{ userInfo.email }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-mobile-phone"></i>
            <label>手机</label>
            <span>{{ userInfo.phone }}</span>
          </div>
          <div class="info-item">
            <i class="el-icon-date"></i>
            <label>入职</label>
            <span>{{ userInfo.joinDate }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TopBar from './TopBar.vue'
import '@/icons'
import { getScopeList } from '../../api/home/<USER>'
import { getAppList } from '../../api/home/<USER>'

export default {
  name: 'ModuleSelect',
  components: {
    TopBar,
  },
  data() {
    return {
      profileDialogVisible: false,
      userInfo: {
        name: '张明',
        role: '系统管理员',
        avatar: '',
        employeeId: 'EMP20240301',
        department: '技术研发部',
        email: '<EMAIL>',
        phone: '13800138000',
        joinDate: '2023-06-15'
      },
      colors: ['#1890FF', '#722ED1', '#13C2C2', '#F5222D'],
      apps: [],
      modules: [],
      modules2: [
        {
          id: 1,
          name: '集成平台',
          description: '提供单点登录和应用集成服务，实现系统间的无缝对接',
          icon: 'el-icon-share',
          path: '/sso-apps/list',
          color: '#1890FF',
          type: 'integration'
        },
        {
          id: 2,
          name: '数据治理',
          description: '提供数据源和数据集管理功能，实现数据的统一管理和治理',
          icon: 'el-icon-folder',
          path: '/data-governance/datasource',
          color: '#722ED1',
          type: 'data'
        },
        {
          id: 3,
          name: '系统管理',
          description: '提供系统监控和运维管理功能，保障系统稳定运行',
          icon: 'el-icon-monitor',
          path: '/dashboard',
          color: '#13C2C2',
          type: 'system'
        },
        {
          id: 4,
          name: '数字孪生',
          description: '提供数字孪生模型管理和在线编辑功能，实现虚实融合',
          icon: 'el-icon-cpu',
          path: '/digital-twin/model-management',
          color: '#F5222D',
          type: 'digital-twin'
        }
      ],
      recentModules: [],
      notificationVisible: false,
      unreadCount: 0,
      systemMessages: [
        {
          id: 1,
          title: '系统升级通知',
          content: '系统将于今晚 23:00 进行升级维护，预计持续 2 小时',
          time: '2024-03-15 10:00:00',
          read: false,
          type: 'system'
        }
        // ... 其他消息
      ],
      adminMessages: [
        // ... 管理员消息
      ]
    }
  },
  created() {
    this.$store.dispatch('user/getInfo').catch(() => {})

    getScopeList().then(response => {
      this.modules = response?.records || []
    }).catch(error => {
      this.modules = []
    })

    this.getAppList()

    const recentModulesStr = localStorage.getItem('recentModules')
    if (recentModulesStr) {
      this.recentModules = JSON.parse(recentModulesStr).slice(0, 4)
    }
    this.calculateUnreadCount()
  },
  methods: {

    linkTo(url, newWindow = true, replace = false) {
      if (newWindow) {
        window.open(url, '_blank')
      } else {
        if (replace) {
          window.location.replace(url)
        } else {
          window.location.href = url
        }
      }
    },
    goSystem(item) {
      if (item?.path?.indexOf('https://') > -1 || item?.path?.indexOf('http://') > -1) {
        this.linkTo(item.path, item.windowOpen || false)
      } else {
        if (item?.activeMenu?.length > 0 && process.env.BASE_URL.length > 1) {
          if (item.activeMenu.indexOf('https://') > -1 || item.activeMenu.indexOf('http://') > -1) {
            this.linkTo(`${item.activeMenu}/${item.path}`, item.windowOpen || false)
          } else {
            this.linkTo(`${window.location.origin}/${item.activeMenu}/${item.path}`, item.windowOpen || false)
          }
        } else {
          this.linkTo(`${window.location.origin}/${item.path}`, item.windowOpen || false)
        }
      }
    },
    getAppList() {
      getAppList().then(data => {
        this.apps = data?.records || []
      }).catch(error => {
        this.apps = []
      })
    },
    navigateToModule(module) {
      localStorage.setItem('currentModuleType', module.type)
      this.updateRecentModules(module)
      // this.$router.push(module.path)
      this.goSystem(module)
    },
    updateRecentModules(module) {
      let recent = JSON.parse(localStorage.getItem('recentModules') || '[]')
      recent = recent.filter(item => item.id !== module.id)
      recent.unshift(module)
      recent = recent.slice(0, 4)
      this.recentModules = recent
      localStorage.setItem('recentModules', JSON.stringify(recent))
    },
    calculateUnreadCount() {
      const unreadSystem = this.systemMessages.filter(msg => !msg.read).length
      const unreadAdmin = this.adminMessages.filter(msg => !msg.read).length
      this.unreadCount = unreadSystem + unreadAdmin
    },
    handleReadMessage(message) {
      message.read = true
      this.calculateUnreadCount()
    },
    handleReadAll() {
      const messages = [...this.systemMessages, ...this.adminMessages]
      messages.forEach(msg => {
        msg.read = true
      })
      this.calculateUnreadCount()
    },
    formatTime(time) {
      return time
    },
  }
}
</script>

<style lang="scss" scoped>
.module-select {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);

  .main-content {
    padding: 104px 40px 40px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .welcome-section {
    text-align: center;
    margin-bottom: 48px;

    h1 {
      font-size: 32px;
      color: #1f2f3d;
      margin-bottom: 12px;
      font-weight: 600;
      background: linear-gradient(120deg, #1f2f3d, #409EFF);
      -webkit-background-clip: text;
      color: transparent;
    }

    .subtitle {
      font-size: 16px;
      color: #606266;
    }
  }

  .recent-modules {
    margin-bottom: 48px;

    h2 {
      font-size: 20px;
      margin-bottom: 20px;
      color: #1f2f3d;
      font-weight: 500;
    }

    .module-list {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }

    .recent-module-item {
      padding: 16px 24px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      gap: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

      i {
        font-size: 20px;
        color: #409EFF;
      }

      span {
        font-size: 15px;
        color: #303133;
        font-weight: 500;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        background: rgba(255, 255, 255, 0.95);
      }
    }
  }

  .all-modules {
    h2 {
      font-size: 20px;
      margin-bottom: 24px;
      color: #1f2f3d;
      font-weight: 500;
    }

    .module-card {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 16px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s;
      margin-bottom: 24px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--color), rgba(255, 255, 255, 0.5));
        opacity: 0;
        transition: opacity 0.3s;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        background: rgba(255, 255, 255, 0.95);

        &::before {
          opacity: 1;
        }

        .module-icon {
          transform: scale(1.05);
        }
      }

      .module-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        transition: all 0.3s;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        i {
          font-size: 28px;
          color: white;
        }
      }

      .module-info {
        h3 {
          margin: 0 0 12px;
          font-size: 18px;
          color: #303133;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #606266;
          line-height: 1.6;
          height: 44px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}

.module-card {
  --color: #409EFF;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color), rgba(255, 255, 255, 0.5));
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.95);

    &::before {
      opacity: 1;
    }

    .module-icon {
      transform: scale(1.05);
    }
  }

  .module-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.3s;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    i {
      font-size: 28px;
      color: white;
    }
  }

  .module-info {
    h3 {
      margin: 0 0 12px;
      font-size: 18px;
      color: #303133;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #606266;
      line-height: 1.6;
      height: 44px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
}

.profile-dialog {
  ::v-deep .el-dialog__header {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    margin: 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }
  }

  ::v-deep .el-dialog__body {
    padding: 0;
  }

  .profile-content {
    padding: 32px 24px;
  }

  .profile-header {
    text-align: center;
    margin-bottom: 36px;
    padding: 0 20px;

    .el-avatar {
      border: 4px solid rgba(64, 158, 255, 0.1);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    h2 {
      margin: 16px 0 8px;
      font-size: 22px;
      color: #303133;
      font-weight: 600;
    }

    .role-tag {
      display: inline-block;
      padding: 4px 16px;
      background: linear-gradient(45deg, #409EFF, #007AFF);
      color: white;
      border-radius: 20px;
      font-size: 13px;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }

  .profile-info {
    background-color: #f8f9fb;
    border-radius: 12px;
    padding: 20px;

    .info-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-radius: 8px;
      transition: all 0.3s;

      &:hover {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }

      &+.info-item {
        margin-top: 8px;
      }

      i {
        font-size: 18px;
        color: #409EFF;
        margin-right: 12px;
      }

      label {
        width: 40px;
        color: #909399;
        font-size: 14px;
      }

      span {
        flex: 1;
        margin-left: 16px;
        color: #303133;
        font-size: 14px;
      }
    }
  }
}

// 复用 layout 中的通知相关样式
.notification-wrapper {
  height: 40px;
  display: flex;
  align-items: center;

  .notification-badge {
    height: 100%;
    display: flex;
    align-items: center;

    ::v-deep .el-badge__content {
      top: 8px;
      right: 8px;
    }
  }

  .notification-icon {
    padding: 0;
    cursor: pointer;
    border-radius: 20px; // 改为圆角以匹配头像样式
    transition: all 0.3s;
    height: 40px;
    width: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;

    i {
      font-size: 20px;
      color: #606266;
      transition: all 0.3s;
      line-height: 1;
    }

    &:hover {
      background-color: #ecf5ff;
      border-color: #d9ecff;

      i {
        color: #409EFF;
      }
    }

    &.has-unread {
      animation: shake 1s cubic-bezier(.36, .07, .19, .97) infinite;
    }
  }
}

// 确保添加其他必要的通知相关样式...
// 可以直接复用 layout 中的其他通知样式

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

@keyframes shake {

  10%,
  90% {
    transform: rotate(-2deg);
  }

  20%,
  80% {
    transform: rotate(2deg);
  }

  30%,
  50%,
  70% {
    transform: rotate(-1deg);
  }

  40%,
  60% {
    transform: rotate(1deg);
  }
}
</style>

<style>
.notification-popover {
  padding: 0 !important;
  border-radius: 8px;
}
</style>
