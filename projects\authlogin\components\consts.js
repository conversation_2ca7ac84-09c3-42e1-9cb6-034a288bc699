export const phonePattern = /^1[3-9]\d{9}$/
export const pwdPattern = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&.])[A-Za-z\d@$!%*#?&.]{8,20}$/
export const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
import EmailValidator from './EmailValidator.js'
export const validatePhone = (rule, value, callback) => {
    if (!value) {
        callback(new Error('请输入手机号'))
    } else if (!phonePattern.test(value)) {
        callback(new Error('请输入正确的手机号'))
    } else {
        callback()
    }
}

export const validateEmail = (rule, value, callback) => {
    if (!value) {
        callback(new Error('请输入邮箱号'))
    } else if (!EmailValidator.validateEmail(value)) {
        callback(new Error('请输入正确的邮箱'))
    } else {
        callback()
    }
}

import JsEncrypt from 'jsencrypt'
const encrypt = new JsEncrypt()
// 公钥
encrypt.setPublicKey(
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDSUi13ttnt7W2IwdE/5vtm6Yup9mEXop42iPn7DSYTZUlb16wLMLdp4nISrwCAliPwoHWBJ67tDGKZY+MCuaddfr7Mc1f1SKtBzuTdxqjnxagpzEFAyP3VerbPz/vdp7WQCbxmox0N/WUSUTfIm2g8yo3TogSJB48gLgbyRgwShwIDAQAB'
)
export function $encruption(obj) {
    return encrypt.encrypt(obj)
}


