<template>
  <el-form :model="form" size="small" :rules="rules" ref="form">
    <el-form-item label="路径" prop="path">
      <el-input v-model="form.path"></el-input>
    </el-form-item>
    <el-form-item label="方法" prop="supportedMethods">
      <el-select v-model="form.supportedMethods" style="width: 100%;" >
        <el-option label="GET" value="GET" />
        <el-option label="POST" value="POST" />
        <el-option label="PUT" value="PUT" />
        <el-option label="DELETE" value="DELETE" />
      </el-select>
    </el-form-item>
    <div style="text-align: center;">
      <el-link type="primary" :underline="false" @click="() => higherOption = !higherOption">高级选项<i :class="higherOption ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" /></el-link>
    </div>
    <div v-show="higherOption">
      <el-form-item label="响应超时" prop="replyTimeout">
        <el-input v-model="form.replyTimeout" type="number">
          <template slot="suffix">ms</template>
        </el-input>
      </el-form-item>
      <el-form-item label="负载" prop="payloadExpression">
        <el-input v-model="form.payloadExpression" placeholder="#requestParams.toSingleValueMap()" ></el-input>
      </el-form-item>
    </div>
  </el-form>
</template>
<script>
import { isEqual } from 'element-ui/src/utils/util';
export default {
  name: 'HttpInboundTrigger',
  data() {
    return {
      form: {},
      rules: {
        'path': [
          { required: true, message: '请输入路径', trigger: 'blur' }
        ],
        'supportedMethods': [
          { required: true, message: '请选择请求方式', trigger: 'change' }
        ]
      },
      higherOption: false
    }
  },
  methods: {
    getProperties() {
      return this.form
    },
    async validate() {
        return new Promise(resolve => {
            this.$refs.form.validate(valid => {
                resolve(valid)
            })
        })
    }
  },
  props: {
    properties: {
        type: Object,
        default: () => ({})
    },
    node: {
        type: Object,
        default: () => ({})
    }
  },
  watch: {
    properties: {
        handler(newVal) {
            if (!isEqual(newVal, this.form)) {
                this.form = { ...this.form, ...newVal }
            }
        },
        immediate: true,
        deep: true
    },
  },
}
</script>