import request from '@/utils/request'

const api = '/rule'

// 获取规则链列表
export function getRuleChainList(params) {
  return request({
    url: api + '/rule/chain/list',
    method: 'get',
    params
  })
}

// 获取规则链详情
export function getRuleChainDetail(id) {
    return request({
        url: api + '/rule/chain',
        method: 'get',
        params: { id }
      })
}

// 创建规则链
export function createRuleChain(data) {
  return request({
    url: api + '/rule/chain',
    method: 'post',
    data
  })
}

// 更新规则链
export function updateRuleChain(data) {
  return request({
    url: api + '/rule/chain',
    method: 'put',
    data
  })
}

// 删除规则链
export function deleteRuleChain(id) {
  return request({
    url: api + `/rule/chain/${id}`,
    method: 'delete'
  })
}

// 执行规则链
export function executeRuleChain(data) {
  return request({
    url: api + '/rule/chain/execute',
    method: 'post',
    data
  })
}

// 校验规则表达式
export function validateRuleExpression(elStr) {
  return request({
    url: api + '/rule/chain/validate',
    method: 'post',
    data: { elStr }
  })
}

// 获取应用列表
export function getApplicationList(groupName = 'application') {
  return request({
    url: api + '/rule/chain/groupNames',
    method: 'get',
    params: { groupName }
  })
}

// 添加解析规则引用节点的API函数
export function resolveElNodes(data) {
  return request({
    url: api + '/rule/chain/resolveElNodes',
    method: 'post',
    data
  })
}
