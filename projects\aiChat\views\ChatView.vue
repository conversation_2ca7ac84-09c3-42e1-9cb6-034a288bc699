<template>
  <div class="chat-view">
    <div class="chat-container" ref="chatContainer">
      <div class="clear-history-float" v-if="messages.length">
        <button @click="showClearConfirm">
          <i class="el-icon-delete"></i>
          清空对话
        </button>
      </div>
      <chat-history :messages="messages" :loading="currentAiMessage ? loading && !currentAiMessage.content : loading" @resend="handleResend" />
    </div>
    <chat-options :model="selectedModel" :models="models" :loading="!models.length" @model-change="handleModelChange"
      @knowledge-base-change="handleKnowledgeBaseChange" />
    <chat-input ref="chatInput" @send="handleSend" :loading="loading" :disabled="!models.length"
      :placeholder="models.length ? '请输入问题...' : '正在加载模型..'" :selectedModel="selectedModel" />
  </div>
</template>

<script>
import ChatHistory from '../components/chat/ChatHistory.vue'
import ChatInput from '../components/chat/ChatInput.vue'
import ChatOptions from '../components/chat/ChatOptions.vue'
import api from '../api'

export default {
  name: 'ChatView',
  components: {
    ChatHistory,
    ChatInput,
    ChatOptions
  },
  data() {
    return {
      messages: [],
      loading: false,
      currentAiMessage: null,
      models: [],
      selectedModel: '',
      selectedKnowledgeBase: ''
    }
  },
  methods: {
    // 保存消息到本地存储
    saveMessages() {
      localStorage.setItem('chatMessages', JSON.stringify(this.messages))
    },

    // 从本地存储加载消息
    loadMessages() {
      const savedMessages = localStorage.getItem('chatMessages')
      if (savedMessages) {
        try {
          const messages = JSON.parse(savedMessages)
          // 确保时间戳是 Date 对象
          this.messages = messages.map(msg => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        } catch (error) {
          console.error('加载历史消息失败：', error)
          this.messages = []
        }
      }
    },

    // 清空聊天历史
    clearHistory() {
      this.messages = []
      localStorage.removeItem('chatMessages')
    },

    async loadModels() {
      try {
        this.models = await api.chat.getModels();
        if (this.models && this.models.length > 0) {
          this.selectedModel = this.models.at(-1).id || '';
        } else {
          this.selectedModel = '';
        }
      } catch (error) {
        console.error('加载模型列表失败：', error);
        this.selectedModel = '';
      }
    },

    handleModelChange(modelName) {
      this.selectedModel = modelName;
    },

    resetState() {
      this.loading = false;
      this.currentAiMessage = null;
    },

    handleKnowledgeBaseChange(kbId) {
      this.selectedKnowledgeBase = kbId;
    },

    scrollToBottom() {
      const container = this.$refs.chatContainer;
      if (container) {
        // 使用 setTimeout 确保在 DOM 更新后滚动
        setTimeout(() => {
          container.scrollTop = container.scrollHeight;
        }, 100);
      }
    },

    async handleSend({ message }) {
      if (this.loading) return;

      try {
        this.loading = true;

        let finalMessage = message;
        // 添加用户消息
        const userMessage = {
          type: 'user',
          content: message,
          timestamp: new Date()
        }
        this.messages.push(userMessage)

        // 创建初始的 AI 消息
        this.currentAiMessage = {
          type: 'ai',
          content: '',
          timestamp: new Date()
        }
        this.messages.push(this.currentAiMessage)

        // 添加消息后立即滚动到底部
        this.scrollToBottom()

        // 如果选择了知识库，先搜索知识库
        if (this.selectedKnowledgeBase) {
          try {
            const searchResults = await api.knowledge.search({
              knowledge_base_name: this.selectedKnowledgeBase,
              query: message,
              top_k: 3,
              score_threshold: 1
            });

            if (searchResults && searchResults.length > 0) {
              const searchContent = searchResults
                .map(item => item.page_content)
                .join('\n');

              finalMessage = `【指令】根据已知信息，简洁和专业的来回答问题。如果无法从中得到答案，请说 "根据已知信息无法回答该问题"，不允许在答案中添加编造成分，答案请使用中文。\n\n【已知信息】${searchContent}\n\n【问题】${message}`;
            }
          } catch (error) {
            console.error('搜索知识库失败：', error);
            finalMessage = message;
          }
        }

        // 保存到本地存储
        this.saveMessages()

        await api.chat.sendMessage(
          {
            model: this.selectedModel,
            messages: [
              ...this.messages
                // 10条上下文
                .slice(-12, -2)
                .filter(msg => msg.type === 'user' || msg.type === 'ai')
                .map(msg => ({
                  role: msg.type === 'user' ? 'user' : 'assistant',
                  content: msg.content
                })),
              {
                role: "user",
                content: finalMessage
              }
            ],
            stream: true
          },
          // 处理数据流
          (response) => {
            if (this.currentAiMessage) {
              this.currentAiMessage.content = response;
              this.$nextTick(() => {
                // 每次内容更新后滚动到底部
                this.scrollToBottom();
              });
            }
          },
          // 处理错误
          (error) => {
            console.error('发送消息失败：', error);
            if (this.currentAiMessage) {
              this.currentAiMessage.content = '抱歉，发生了一些错误，请稍后重试。';
              this.saveMessages() // 保存错误消息
            }
            this.resetState();
          },
          // 完成回调
          () => {
            this.resetState();
            this.saveMessages() // 确保最终状态被保存
          }
        );
      } catch (error) {
        console.error('发送消息失败：', error);
        if (this.currentAiMessage) {
          this.currentAiMessage.content = '抱歉，发生了一些错误，请稍后重试。';
          this.saveMessages() // 保存错误消息
        }
        this.resetState();
        // 错误消息显示后也滚动到底部
        this.scrollToBottom();
      }
    },

    // 添加确认对话框方法
    showClearConfirm() {
      this.$confirm('确认清空所有对话记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'custom-message-box',
        center: true
      }).then(() => {
        this.clearHistory();
        this.$message({
          type: 'success',
          message: '已清空对话记录'
        });
      }).catch(() => {
        // 取消时不做任何操作
      });
    },

    // 修改重发处理方法
    handleResend(message) {
      // 将消息复制到输入框
      if (this.$refs.chatInput) {
        this.$refs.chatInput.setMessage(message);
      }
    }
  },
  created() {
    this.loadMessages() // 加载历史消息
    this.loadModels()
  },
  // 在组件销毁前保存消息
  beforeDestroy() {
    this.saveMessages()
  }
}
</script>

<style scoped>
.chat-view {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eaeaea;
  overflow: hidden auto;
}

/* 滚动条样式优化 */
.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: transparent;
}

.chat-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 添加滚动条的内边距，防止内容贴边 */
.chat-history {
  padding-right: 6px;
}

.clear-history-float {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  justify-content: center;
  padding: 8px;
  background: linear-gradient(to bottom, rgba(255,255,255,0.95) 60%, rgba(255,255,255,0));
}

.clear-history-float button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  color: #4a5568;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.clear-history-float button:hover {
  background: #edf2f7;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.clear-history-float button:active {
  transform: translateY(0);
}

.clear-history-float i {
  font-size: 14px;
}

@media (max-width: 768px) {
  .chat-view {
    height: calc(100vh - 100px);
    padding: 0 10px;
  }

  .chat-container {
    padding: 15px;
    border-radius: 12px;
  }
}

@media screen and (max-width: 768px) {
  .chat-view {
    height: calc(100vh - 80px);
    padding: 0 8px;
    gap: 10px;
    max-width: 100%;
  }

  .chat-container {
    padding: 10px;
    border-radius: 8px;
  }

  .clear-history-float {
    padding: 6px;
  }

  .clear-history-float button {
    padding: 4px 10px;
    font-size: 12px;
  }

  .clear-history-float i {
    font-size: 12px;
  }

  .custom-message-box {
    width: 90% !important;
    margin: 15vh auto !important;
  }
  
  .custom-message-box .el-message-box__header {
    padding: 20px !important;
    background: #f8f9fa !important;
    border-bottom: 1px solid #eaeaea !important;
  }
  
  .custom-message-box .el-message-box__title {
    font-size: 18px !important;
    line-height: 1.4 !important;
    font-weight: 600 !important;
  }
  
  .custom-message-box .el-message-box__content {
    padding: 25px 20px !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
  }
  
  .custom-message-box .el-message-box__message {
    color: #333 !important;
  }
  
  .custom-message-box .el-message-box__btns {
    padding: 15px 20px 20px !important;
    display: flex !important;
    gap: 12px !important;
  }
  
  .custom-message-box .el-message-box__btns button {
    flex: 1 !important;
    margin-left: 0 !important;
    font-size: 16px !important;
    padding: 12px 20px !important;
    height: auto !important;
    border-radius: 6px !important;
  }
  
  .el-message {
    min-width: auto !important;
    width: 90% !important;
    padding: 15px 20px !important;
    border-radius: 8px !important;
    top: 20px !important;
  }
  
  .el-message .el-message__content {
    font-size: 15px !important;
    line-height: 1.4 !important;
  }
}
</style>

<!-- 添加全局样式 -->
<style>
/* 移动端确认弹窗样式优化 */
@media screen and (max-width: 768px) {
  .el-message-box {
    width: 90% !important;
    margin: 15vh auto !important;
  }
  
  .el-message-box__header {
    padding: 20px !important;
    background: #f8f9fa !important;
    border-bottom: 1px solid #eaeaea !important;
  }
  
  .el-message-box__title {
    font-size: 18px !important;
    line-height: 1.4 !important;
    font-weight: 600 !important;
  }
  
  .el-message-box__content {
    padding: 25px 20px !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
  }
  
  .el-message-box__message {
    color: #333 !important;
  }
  
  .el-message-box__btns {
    padding: 15px 20px 20px !important;
    display: flex !important;
    gap: 12px !important;
  }
  
  .el-message-box__btns button {
    flex: 1 !important;
    margin-left: 0 !important;
    font-size: 16px !important;
    padding: 12px 20px !important;
    height: auto !important;
    border-radius: 6px !important;
  }
  
  .el-message {
    min-width: auto !important;
    width: 90% !important;
    padding: 15px 20px !important;
    border-radius: 8px !important;
    top: 20px !important;
  }
  
  .el-message .el-message__content {
    font-size: 15px !important;
    line-height: 1.4 !important;
  }
}

/* 确认框样式 */
.el-message-box {
  border-radius: 16px !important;
}

.el-message-box__btns button {
  border-radius: 10px !important;
}

.el-message {
  border-radius: 12px !important;
}

@media screen and (max-width: 768px) {
  .el-message-box {
    border-radius: 14px !important;
  }

  .el-message-box__btns button {
    border-radius: 8px !important;
  }

  .el-message {
    border-radius: 10px !important;
  }
}
</style>