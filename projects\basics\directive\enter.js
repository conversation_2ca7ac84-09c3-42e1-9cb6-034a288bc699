import Vue from 'vue';

Vue.use({
    install(Vue) {
        Vue.directive('enter', {
            inserted(el, binding) {
                if (typeof binding.value !== 'function') {
                    console.warn('v-enter directive expects a function as its value');
                    return;
                }
                const handler = (event) => {
                    // 检查是否是回车键
                    if (event.key === 'Enter') {
                        binding.value(event);
                    }
                };
                window.addEventListener('keydown', handler);
                el.__enterClickHandler__ = handler;
            },

            unbind(el) {
                if (el.__enterClickHandler__) {
                    window.removeEventListener('keydown', el.__enterClickHandler__);
                    delete el.__enterClickHandler__;
                }
            }
        })
    }
})