import Vue from 'vue'
import elementResizeDetectorMaker from 'element-resize-detector';
const erd = elementResizeDetectorMaker()

const debounce = function (func, delay = 1000 / 60) {
    let timeoutId;
    return function () {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, arguments);
        }, delay);
    };
}
Vue.use({
    install(Vue) {
        Vue.directive('resize-detector', {
            bind(el, binding) {
                if (binding.value instanceof Function) {
                    el.debounce = debounce(binding.value)
                    window.addEventListener('resize', el.debounce)
                }
            },
            inserted(el, binding) {
                let time = binding.modifiers?.zero && true ? 0 : 5000
                el.timeoutId = setTimeout(() => {
                    erd.listenTo(el, el.debounce)
                }, time)
            },
            unbind(el, binding) {
                el.timeoutId && clearTimeout(el.timeoutId)
                if (binding.value instanceof Function && el.debounce) {
                    window.removeEventListener('resize', el.debounce)
                    el.debounce = null
                }
                try {
                    erd.uninstall(el)
                } catch (error) {
                }
            }
        })
    },
})