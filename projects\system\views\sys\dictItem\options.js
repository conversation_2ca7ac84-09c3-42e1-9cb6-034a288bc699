export default {
  api:"sys/dict/item",
  search:{
    isShow:true,
    showReset:true
  },
  formRule:[
    {
      type: "hidden",
      field: "dictId",
      title: "父级id",
      value: null,
      isSearch:false,
      isTable:false,
    },
    {
      type: "input",
      field: "value",
      className: "value-dom",
      title: "字典值",
      isSearch:true,
      isSearchCol:{md:{span:5}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 20}},
      props:{
        placeholder:"请输入",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"字典值不能为空"
        }
      ]
    },
    {
      type: "input",
      field: "name",
      className: "name-dom",
      title: "标签",
      isSearch:true,
      isSearchCol:{md:{span:5}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 20}},
      props:{
        placeholder:"请输入",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"标签不能为空"
        }
      ]
    },
    {
      type: "input",
      field: "sort",
      className: "sort-dom",
      title: "排序",
      isTable:true,
      col:{md:{span: 20}},
      props:{
        placeholder:"请输入",
        type: "number",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"排序不能为空"
        }
      ]
    },
    {
      type: "input",
      field: "remark",
      className: "remark-dom",
      title: "备注",
      isSearch:false,
      isTable:false,
      col:{md:{span: 20}},
      props:{
        type: "textarea",
        placeholder:"请输入备注",
        readonly: false,
        clearable: true,
        disabled: false
      },
    },
    {
      type: "DatePicker",
      field: "createTime",
      className: "createTime-dom",
      title: "创建时间",
      isSearch: true,
      isSearchCol: {md:{span: 5}},
      isSearchValidate:[],
      isSearchProps:{
        type: "datetimerange",
        format:"yyyy-MM-dd",
        valueFormat:"yyyy-MM-dd"
      },
      isTable: true,
      col: {md: {span: 20}},
      isHidden:true,
    },
  ]
}
