<template>
  <el-card shadow="always">
    <div class="label" slot="header">
      <label>{{value.name || '任务详情'}}</label>
      <el-button v-if="this.value?.id" type="text" @click="tryonce" icon="el-icon-caret-right" :loading="loading">{{ loading ? '执行中' : '执行一次'}}</el-button>
    </div>
    <el-descriptions>
      <el-descriptions-item label="任务组">{{ value.groupName }}</el-descriptions-item>
      <el-descriptions-item label="cron">{{ value.cron }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-dropdown v-if="'undefined' !== typeof(value.status) " trigger="click" @command="handleCommand">
          <el-tag size="small" 
            style="cursor: pointer;"
            :type="statusmap[value.status]?.tag"
          >{{ statusmap[value.status].name }}</el-tag>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item 
              v-for="(status, index) in statuses" 
              :index="index" 
              v-show="status.begin && status.value != value.status"
              :command="status.value"
            >{{ status.label }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-descriptions-item>
      <el-descriptions-item label="是否并发">
        <el-tag size="small" v-if="'undefined' !== typeof(value.concurrent) " >{{ value.concurrent ? '是': '否' }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="重试次数">{{ value.retryCount }}</el-descriptions-item>
      <el-descriptions-item label="超时时间">{{ value.timeout }}秒</el-descriptions-item>
      <el-descriptions-item label="开始时间">{{ value.startTime || '立即执行' }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ value.createTime }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{ value.updateTime }}</el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>
<script>
import task from '@system/api/scheduled/task'
export default {
  name: 'taskDetail',
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      }
    },
    statuses: {
      type: Array,
      default: () => {
        return []
      }

    }
  },
  data() {
    return {
      statusmap: {
        'RUNNING': {
          name: '运行中',
          tag: '',
          begin: true
        },
        'STOPPED': {
          name: '暂停中',
          tag: 'info',
          begin: true
        },
        'FAILED': {
          name: '错误',
          tag: 'danger',
          begin: true
        },
        'COMPLETE': {
          name: '成功',
          tag: 'success',
          begin: true
        }
      },
      loading: false
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  methods: {
    tryonce() {
      this.loading = true
      task.tryonce(this.value).then(res => {
        
        if(res.success) {
          this.$notify({
            title: '执行成功',
            type: 'success'
          });
        } else {
          this.$notify.error({
            title: '执行失败',
          });
        }


        this.loading = false
      }).catch(e => {
        this.loading = false
      })
    },
    handleCommand(command) {
      var status = this.statuses.find(s => s.value === command)
      if  (status) {
        this.$confirm(`是否将任务状态修改为: ${status.label}?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          task.update({id: this.value.id, status: command}).then(res => {
            this.$message({
              type: 'success',
              message: '修改成功!'
            });
            this.$set(this.value, 'status', command)
          })
        })
      }
    }
  }
}
</script>

<style scoped>

.label {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>