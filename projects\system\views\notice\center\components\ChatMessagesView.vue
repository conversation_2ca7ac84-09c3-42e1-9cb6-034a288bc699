<template>
  <div class="chat-container">
    <!-- 左侧联系人列表 -->
    <aside class="chat-list">
      <!-- 搜索框 -->
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索联系人或对话"
          prefix-icon="el-icon-search"
          @input="handleSearch"
          clearable
          class="search-input"
        >
        </el-input>
      </div>

      <!-- 联系人列表 -->
      <div class="chat-items" ref="contactListContainer" @scroll="handleContactListScroll">
        <div
          v-for="(contact, index) in filteredContacts"
          :key="contact.id"
          class="chat-item"
          :class="{ active: currentContact && currentContact.id === contact.id }"
          @click="selectContact(contact)"
        >
          <div class="chat-item-avatar">
            <el-avatar
              :size="48" 
              :src="contact.avatar"
              shape="square"
              :class="{ 'online': contact.online }"
            >
              {{ contact.name.charAt(0) }}
            </el-avatar>
            <div v-if="contact.online" class="online-status"></div>
          </div>

          <div class="chat-item-content">
            <div class="chat-item-header">
              <div class="chat-info">
                <h3>{{ contact.name }}</h3>
                <el-tag v-if="contact.tag" size="mini" :type="getTagType(contact.tag)" effect="plain">
                  {{ contact.tag }}
                </el-tag>
              </div>
              <div class="chat-time">{{ formatTime(contact.lastMessageTime) }}</div>
            </div>
            <div class="chat-item-footer">
              <p class="last-message">{{ getContactLastMessage(contact) }}</p>
              <div v-if="contact.unreadCount > 0" class="unread-badge">
                {{ contact.unreadCount }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载更多提示 -->
        <div v-if="loading" class="loading-more">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>
      </div>
    </aside>

    <!-- 右侧聊天窗口 -->
    <div class="chat-window">
      <template v-if="currentContact">
        <!-- 聊天窗口顶部 -->
        <div class="chat-header">
          <div class="chat-title">
            <span class="name">{{ currentContact.name }}</span>
            <el-tag v-if="currentContact.tag" size="mini" :type="getTagType(currentContact.tag)">{{ currentContact.tag }}</el-tag>
          </div>
          <div class="chat-actions">
            <el-dropdown trigger="click">
              <i class="el-icon-more"></i>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="markAllRead">全部已读</el-dropdown-item>
                <el-dropdown-item @click.native="clearMessages" divided>清空聊天记录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" ref="messageContainer">
          <div v-if="currentMessages.length === 0" class="empty-messages">
            <i class="el-icon-chat-dot-square"></i>
            <p>暂无消息记录</p>
          </div>
          <div v-else>
            <!-- 虚拟滚动容器 -->
            <div class="messages-container">
              <div v-for="(message, index) in currentMessages" :key="message.id">
                <!-- 日期分隔符 - 居中显示 -->
                <div v-if="showDateDivider(message, index)" class="date-divider">
                  <span class="date-text">{{ formatDate(message.time) }}</span>
                </div>
                
                <div 
                  class="message-item"
                  :class="{ 
                    'self': message.sender === currentUser.id,
                    'other': message.sender !== currentUser.id,
                    'system': message.type === 'system'
                  }"
                >
                  <!-- 消息时间 - 只在需要时显示 -->
                  <div v-if="showMessageTime(message, index)" class="message-time">
                    {{ formatMessageTime(message.time) }}
                  </div>

                  <!-- 消息内容 -->
                  <div class="message-content" :class="message.type === 'system' ? 'system-message' : (message.sender === currentUser.id ? 'self-message' : 'other-message')">
                    <!-- 对方头像 - 只在最后一条消息或时间间隔大时显示 -->
                    <template v-if="message.sender !== currentUser.id && message.type !== 'system' && showAvatar(message, index)">
                      <div class="avatar">
                        <el-avatar :size="40" :src="currentContact.avatar" shape="square">
                          {{ currentContact.name.charAt(0) }}
                        </el-avatar>
                      </div>
                    </template>
                    
                    <!-- 对方消息的占位空间，保持布局一致 -->
                    <template v-else-if="message.sender !== currentUser.id && message.type !== 'system'">
                      <div class="avatar-placeholder"></div>
                    </template>
                    
                    <div class="message-bubble" :class="{'no-avatar': message.sender !== currentUser.id && message.type !== 'system' && !showAvatar(message, index)}">
                      <!-- 不显示发送者名称，因为是单聊 -->
                      
                      <!-- 消息内容 -->
                      <div class="message-text" v-if="message.type === 'text'">
                        {{ message.content }}
                      </div>
                      <div class="message-image" v-else-if="message.type === 'image'">
                        <el-image 
                          :src="message.content"
                          :preview-src-list="[message.content]"
                          fit="cover"
                        >
                        </el-image>
                      </div>
                      <div class="message-file" v-else-if="message.type === 'file'">
                        <i class="el-icon-document"></i>
                        <span class="file-name">{{ message.fileName }}</span>
                        <el-button type="text" size="mini" @click="downloadFile(message)">
                          <i class="el-icon-download"></i>
                        </el-button>
                      </div>
                      <div v-else>
                        内容显示错误: {{ JSON.stringify(message) }}
                      </div>
                    </div>
                    
                    <!-- 自己的头像 - 只在最后一条消息或时间间隔大时显示 -->
                    <template v-if="message.sender === currentUser.id && showAvatar(message, index)">
                      <div class="avatar">
                        <el-avatar :size="40" :src="currentUser.avatar" shape="square">
                          {{ currentUser.name.charAt(0) }}
                        </el-avatar>
                      </div>
                    </template>
                    
                    <!-- 自己消息的占位空间，保持布局一致 -->
                    <template v-else-if="message.sender === currentUser.id">
                      <div class="avatar-placeholder"></div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 消息输入区域 -->
        <div class="chat-input-area">
          <div class="input-toolbar">
            <i class="el-icon-picture-outline-round toolbar-icon" @click="toggleEmojiPanel"></i>
            <i class="el-icon-folder toolbar-icon" @click="selectFile"></i>
            <input 
              type="file" 
              ref="fileInput" 
              style="display: none" 
              @change="handleFileChange"
            >
          </div>
          <div class="input-container">
            <el-input
              type="textarea"
              v-model="messageText"
              placeholder="输入消息..."
              :rows="3"
              resize="none"
              @keyup.enter.native.exact="handleEnterPress"
              @keyup.ctrl.enter.native="addNewLine"
              class="message-textarea"
            >
            </el-input>
          </div>
          <div class="send-action">
            <el-button type="primary" :disabled="!messageText.trim()" @click="sendMessage">发送</el-button>
          </div>
        </div>
      </template>

      <!-- 未选择联系人时的提示 -->
      <div v-else class="no-contact-selected">
        <i class="el-icon-chat-dot-square"></i>
        <p>请选择一个联系人开始聊天</p>
      </div>
    </div>
  </div>
</template>

<script>
import { getFriendsList } from '@system/api/notice/chat.js'
import chatSocketService from '@system/api/notice/chatSocket.js'
import { getCurrentSubscriptions } from '@system/api/notice/chat.js'

export default {
  name: 'ChatMessagesView',
  props: {
    contacts: {
      type: Array,
      required: true
    },
    messages: {
      type: Object,
      required: true
    },
    currentUser: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      currentContact: null,
      messageText: '',
      searchQuery: '',
      emojiPanelVisible: false,
      friendsList: [], // 存储从API获取的好友列表
      loading: false, // 加载状态
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        pages: 0
      },
      hasMore: true, // 是否有更多数据
      loadingMore: false, // 是否正在加载更多
      chatSubscription: null // 存储WebSocket订阅
    }
  },
  computed: {
    currentMessages() {
      if (!this.currentContact) return []
      console.log('获取消息列表:', this.currentContact.id, this.messages[this.currentContact.id])
      return this.messages[this.currentContact.id] || []
    },
    filteredContacts() {
      // 如果父组件提供了contacts且不进行搜索，则直接使用父组件数据
      const sourceContacts = this.friendsList.length > 0 ? this.friendsList : this.contacts
      
      if (!this.searchQuery) {
        return sourceContacts
      }
      
      const keyword = this.searchQuery.toLowerCase()
      return sourceContacts.filter(contact =>
        contact.name.toLowerCase().includes(keyword) ||
        this.getContactLastMessage(contact).toLowerCase().includes(keyword)
      )
    }
  },
  methods: {
    handleSearch() {
      // 搜索联系人, computed直接更新
    },
    
    // 初始化WebSocket连接
    initWebSocket() {
      if (!this.currentUser) {
        console.error('无法初始化WebSocket: 缺少用户信息')
        return Promise.reject(new Error('缺少用户信息'))
      }

      // 初始化WebSocket连接
      return this.subscribeChat()
        .catch(error => {
          console.error('WebSocket连接或订阅失败:', error)
          this.$message.error(`WebSocket连接失败: ${error.message}`)
          throw error
        })
    },

    // 订阅聊天消息
    async subscribeChat() {
      try {
        // 先获取订阅信息
        const response = await getCurrentSubscriptions()
        const subscriptions = response || {}
        
        // 检查是否有可用的订阅，至少需要私信队列
        if (!subscriptions.privateQueue) {
          console.warn('没有可用的私信队列，聊天功能可能无法正常工作')
        }
        
        // 先确保连接
        await chatSocketService.connect()
        
        // 订阅私信队列，这是聊天最关键的
        if (subscriptions.privateQueue) {
          await chatSocketService.subscribe(subscriptions.privateQueue, (message) => {
            console.log('接收到聊天消息:', message)
            this.handleIncomingMessage(message)
          })
          console.log('成功订阅私信队列:', subscriptions.privateQueue)
        }
        
        // 可选：也订阅广播和群组消息，确保完整体验
        if (subscriptions.broadcastTopics?.length) {
          for (const topic of subscriptions.broadcastTopics) {
            await chatSocketService.subscribe(topic, (message) => {
              console.log('接收到广播消息:', message)
              this.handleIncomingMessage(message)
            })
          }
        }
        
        if (subscriptions.groupTopics?.length) {
          for (const topic of subscriptions.groupTopics) {
            await chatSocketService.subscribe(topic, (message) => {
              console.log('接收到群组消息:', message)
              this.handleIncomingMessage(message)
            })
          }
        }
        
        return true
      } catch (error) {
        console.error('订阅聊天消息失败:', error)
        throw error
      }
    },

    // 处理接收到的消息
    handleIncomingMessage(message) {
      if (!message) return

      // 从消息中获取发送者信息
      const senderName = message.fromName || message.sender || message.fromAccountId
      if (!senderName) {
        console.warn('收到消息但没有发送者信息:', message)
        return
      }

      // 查找联系人 - 优先使用name查找，兼容id
      const contact = this.findContactByNameOrId(senderName)
      if (!contact) {
        console.warn(`未找到发送者(${senderName})对应的联系人`)
        // 这里可以考虑通过API获取未知联系人的信息并添加到联系人列表
        return
      }

      // 创建消息对象
      const now = new Date()
      const newMessage = {
        id: message.id || 'msg_' + Date.now(),
        sender: contact.id, // 使用联系人的ID作为发送者标识以保持一致性
        content: message.content || '',
        type: message.type || 'text',
        time: message.timestamp || this.formatCurrentTime(now),
        read: this.currentContact && this.currentContact.id === contact.id,
        fileName: message.fileName // 如果是文件消息
      }

      // 通知父组件添加消息
      this.$emit('send-message', {
        contactId: contact.id,
        message: newMessage
      })

      // 如果当前没有选中联系人或选中的不是消息发送者，增加未读计数
      if (!this.currentContact || this.currentContact.id !== contact.id) {
        // 更新联系人未读消息数
        const contactIndex = this.contacts.findIndex(c => c.id === contact.id)
        if (contactIndex !== -1) {
          const updatedContact = {
            ...this.contacts[contactIndex],
            unreadCount: (this.contacts[contactIndex].unreadCount || 0) + 1,
            lastMessageTime: newMessage.time
          }
          // 通知父组件更新联系人
          this.$emit('update-contact', updatedContact)
        }
      } else {
        // 如果是当前选中的联系人发送的消息，滚动到底部显示
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 根据名称或ID查找联系人
    findContactByNameOrId(nameOrId) {
      if (!nameOrId) return null
      
      // 先尝试通过name查找
      let contact = this.contacts.find(contact => 
        contact.name === nameOrId || 
        contact.username === nameOrId
      )
      
      // 如果找不到，再尝试通过id查找
      if (!contact) {
        contact = this.contacts.find(contact => contact.id === nameOrId)
      }
      
      return contact
    },
    
    // 获取联系人列表
    getFriends() {
      if (this.loading || (this.loadingMore && !this.hasMore)) return
      
      const isLoadingMore = this.loadingMore
      const params = {
        current: this.pagination.current,
        size: this.pagination.size
      }
      
      this.loading = true
      
      getFriendsList(params).then(response => {
        const records = response.records || []
        
        // 转换数据格式
        const formattedContacts = records.map(item => {
          const picUrl =process.env.VUE_APP_FILE_URL + item.picUrl
          
          return {
            id: item.id,
            name: item.name,
            avatar: picUrl,
            tag: item.tag || '',
            online: item.online || false,
            lastMessageTime: item.lastLoginTime || new Date().toISOString(),
            unreadCount: 0 // 默认为0，后续可能需要从消息接口获取
          }
        })
        
        // 更新分页信息
        this.pagination.current = response.current || this.pagination.current
        this.pagination.size = response.size || this.pagination.size
        this.pagination.total = response.total || 0
        this.pagination.pages = response.pages || 0
        
        // 判断是否还有更多数据
        this.hasMore = this.pagination.current < this.pagination.pages
        
        // 追加或覆盖数据
        if (isLoadingMore) {
          this.friendsList = [...this.friendsList, ...formattedContacts]
        } else {
          this.friendsList = formattedContacts
        }
        
        // 更新父组件的contacts
        this.$emit('update-contacts', this.friendsList)
      }).finally(() => {
        this.loading = false
        this.loadingMore = false
      })
    },
    
    // 加载更多数据
    loadMore() {
      if (this.loading || !this.hasMore) return
      
      this.loadingMore = true
      this.pagination.current += 1
      this.getFriends()
    },
    
    // 处理联系人列表滚动
    handleContactListScroll(e) {
      const container = this.$refs.contactListContainer
      if (!container) return
      
      // 检测是否滚动到底部附近
      const scrollPosition = container.scrollTop + container.clientHeight
      const scrollHeight = container.scrollHeight
      
      // 当滚动到距底部100px时加载更多
      if (scrollHeight - scrollPosition < 100 && !this.loading && this.hasMore) {
        this.loadMore()
      }
    },
    
    selectContact(contact) {
      this.currentContact = contact
      
      // 标记该联系人的所有消息为已读
      if (this.messages[contact.id]) {
        this.messages[contact.id].forEach(msg => {
          if (msg.sender !== this.currentUser.id) {
            msg.read = true
          }
        })
      }
      
      // 通知父组件更新联系人未读消息数
      this.$emit('mark-all-read', contact.id)
      
      // 滚动到最新消息
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    markAllRead() {
      if (!this.currentContact) return
      this.$emit('mark-all-read', this.currentContact.id)
    },
    
    clearMessages() {
      if (!this.currentContact) return
      this.$emit('clear-messages', this.currentContact.id)
    },
    
    toggleEmojiPanel() {
      this.emojiPanelVisible = !this.emojiPanelVisible
      // 表情面板功能暂不实现
      this.$message.info('表情功能开发中...')
    },
    
    selectFile() {
      this.$refs.fileInput.click()
    },
    
    handleFileChange(event) {
      const files = event.target.files
      if (!files || files.length === 0) return
      
      const file = files[0]
      
      // 判断文件类型
      if (file.type.startsWith('image/')) {
        // 处理图片
        this.sendImageMessage(file)
      } else {
        // 处理其他文件
        this.sendFileMessage(file)
      }
      
      // 清空文件输入框，以便下次选择同一文件时也能触发change事件
      this.$refs.fileInput.value = ''
    },
    
    sendImageMessage(file) {
      // 模拟上传图片，实际场景应调用上传API
      this.$message.info('图片上传功能开发中...')
      
      // 模拟上传成功后发送图片消息
      const fileUrl = URL.createObjectURL(file)
      this.sendMessage({
        content: fileUrl,
        type: 'image'
      })
    },
    
    sendFileMessage(file) {
      // 模拟上传文件，实际场景应调用上传API
      this.$message.info('文件上传功能开发中...')
      
      // 模拟上传成功后发送文件消息
      this.sendMessage({
        content: '文件已上传',
        fileName: file.name,
        type: 'file'
      })
    },
    
    sendMessage(customMessage) {
      if ((!this.messageText.trim() && !customMessage) || !this.currentContact) return
      
      const now = new Date()
      // 使用本地时间格式
      const time = this.formatCurrentTime(now)
      
      // 构建消息对象
      const message = customMessage ? {
        id: 'msg_' + Date.now(),
        sender: this.currentUser.id,
        ...customMessage,
        time,
        read: true
      } : {
        id: 'msg_' + Date.now(),
        sender: this.currentUser.id,
        content: this.messageText.trim(),
        type: 'text',
        time,
        read: true
      }
      
      // 通知父组件添加消息
      this.$emit('send-message', {
        contactId: this.currentContact.id,
        message
      })
      
      // 使用WebSocket发送消息到服务器
      this.sendMessageViaWebSocket(message)
      
      // 清空输入框
      this.messageText = ''
      
      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    // 通过WebSocket发送消息
    sendMessageViaWebSocket(message) {
      if (!this.currentContact) return
      
      // 获取当前登录用户的name - 从Vuex store中获取
      const currentUserName = this.$store.getters.name || this.$store.getters.username
      
      // 构建要发送的消息 - 根据后端期望的格式调整
      const wsMessage = {
        sender: currentUserName, // 使用登录信息中的name
        content: message.content,
        type: message.type || 'text',
        fileName: message.fileName,
        timestamp: message.time
      }
      
      // 使用联系人的name属性作为接收者标识
      const receiverName = this.currentContact.name || this.currentContact.username || this.currentContact.id
      
      console.log('发送消息:', wsMessage, '接收者:', receiverName)
      
      // 使用WebSocket服务发送消息
      chatSocketService.sendToUser(receiverName, wsMessage)
        .catch(error => {
          console.error('发送消息失败:', error)
          this.$message.error(`消息发送失败: ${error.message}`)
        })
    },
    
    addNewLine(e) {
      e.preventDefault()
      this.messageText += '\n'
    },
    
    formatCurrentTime(date = new Date()) {
      // 格式化为本地时间: 年-月-日 时:分:秒
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    
    formatMessageTime(time) {
      const date = new Date(time)
      if (isNaN(date.getTime())) {
        return time // 如果解析失败，直接返回原始时间字符串
      }
      
      // 格式化为本地时间: 时:分
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${hours}:${minutes}`
    },
    
    formatTime(time) {
      const date = new Date(time)
      if (isNaN(date.getTime())) {
        return time // 如果解析失败，直接返回原始时间字符串
      }
      
      const now = new Date()
      // 时间差，以分钟为单位
      const diff = (now - date) / 1000 / 60
      
      if (diff < 60) {
        return `${Math.floor(diff)} 分钟前`
      } else if (diff < 1440) {
        return `${Math.floor(diff / 60)} 小时前`
      } else if (diff < 10080) { // 一周内
        const days = Math.floor(diff / 1440)
        return `${days} 天前`
      } else {
        // 显示日期
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }
    },
    
    formatDate(time) {
      const date = new Date(time)
      if (isNaN(date.getTime())) {
        return time // 如果解析失败，直接返回原始时间字符串
      }
      
      // 格式化为本地日期: 年-月-日
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      const today = new Date()
      const isToday = today.getDate() === date.getDate() && 
                      today.getMonth() === date.getMonth() && 
                      today.getFullYear() === date.getFullYear()
      
      if (isToday) {
        return '今天'
      }
      
      return `${year}年${month}月${day}日`
    },
    
    scrollToBottom() {
      const container = this.$refs.messageContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    
    showDateDivider(message, index) {
      if (index === 0) return true
      
      const prevMessage = this.currentMessages[index - 1]
      const prevDate = new Date(prevMessage.time).toDateString()
      const currentDate = new Date(message.time).toDateString()
      
      return prevDate !== currentDate
    },
    
    showMessageTime(message, index) {
      // 第一条消息总是显示时间
      if (index === 0) return true
      
      const prevMessage = this.currentMessages[index - 1]
      
      // 不再检查发送者是否不同，让连续对话也不显示时间
      
      // 如果两条消息间隔超过5分钟，显示时间
      const prevTime = new Date(prevMessage.time).getTime()
      const currentTime = new Date(message.time).getTime()
      const timeDiff = Math.abs(currentTime - prevTime) / (1000 * 60) // 时间差(分钟)
      
      // 或者如果日期不同，显示时间
      const prevDate = new Date(prevMessage.time).toDateString()
      const currentDate = new Date(message.time).toDateString()
      const dateChanged = prevDate !== currentDate
      
      return timeDiff > 5 || dateChanged
    },
    
    downloadFile(message) {
      this.$message.info(`下载文件: ${message.fileName}`)
      // 实际场景应调用下载API
    },
    
    getTagType(tag) {
      switch (tag) {
        case '官方':
          return 'primary'
        case '认证用户':
          return 'success'
        case '客服':
          return 'warning'
        default:
          return 'info'
      }
    },
    
    getContactLastMessage(contact) {
      // 获取联系人最后一条消息
      const contactMessages = this.messages[contact.id]
      if (!contactMessages || contactMessages.length === 0) {
        return '暂无消息'
      }
      
      const lastMessage = contactMessages[contactMessages.length - 1]
      if (lastMessage.type === 'text') {
        const maxLength = 10 // 消息的最大显示长度
        return lastMessage.content.length > maxLength 
          ? lastMessage.content.substring(0, maxLength) + '...' 
          : lastMessage.content
      } else if (lastMessage.type === 'image') {
        return '[图片]'
      } else if (lastMessage.type === 'file') {
        return '[文件] ' + (lastMessage.fileName || '')
      } else {
        return '未知消息类型'
      }
    },
    
    handleEnterPress(e) {
      e.preventDefault()
      console.log('Enter键被按下，发送消息')
      if (!this.messageText.trim()) return
      this.sendMessage()
    },
    
    showAvatar(message, index) {
      // 是否是当前发送者的最后一条消息
      const isLastMessageOfSender = this.isLastMessageOfSender(message, index)
      
      // 与下一条消息是否间隔较长(超过5分钟)
      const hasTimeDiff = this.hasTimeDifferenceWithNext(message, index)
      
      // 当是最后一条消息或有时间差时显示头像
      return isLastMessageOfSender || hasTimeDiff
    },
    
    isLastMessageOfSender(message, index) {
      // 如果是最后一条消息
      if (index === this.currentMessages.length - 1) return true
      
      // 如果下一条消息的发送者不同
      const nextMessage = this.currentMessages[index + 1]
      return nextMessage.sender !== message.sender
    },
    
    hasTimeDifferenceWithNext(message, index) {
      // 如果是最后一条消息
      if (index === this.currentMessages.length - 1) return false
      
      const nextMessage = this.currentMessages[index + 1]
      // 如果下一条消息有时间显示(间隔>5分钟)，则也显示头像
      const currentTime = new Date(message.time).getTime()
      const nextTime = new Date(nextMessage.time).getTime()
      const timeDiff = Math.abs(nextTime - currentTime) / (1000 * 60) // 时间差(分钟)
      
      return timeDiff > 5
    }
  },
  created() {
    // 打印当前登录用户信息，帮助调试
    console.log('当前登录用户信息:', {
      id: this.$store.getters.userId,
      name: this.$store.getters.name,
      username: this.$store.getters.username,
      nickName: this.$store.getters.nickName,
      currentUser: this.currentUser
    })

    // 组件创建时获取联系人列表
    this.getFriends()
    
    // 初始化WebSocket连接
    this.initWebSocket()
      .then(() => {
        console.log('聊天WebSocket初始化成功')
      })
      .catch(error => {
        console.error('聊天WebSocket初始化失败:', error)
      })
  },
  beforeDestroy() {
    // 组件销毁时断开WebSocket连接
    chatSocketService.unsubscribeAll()
  }
}
</script>

<style lang="scss" scoped>
.chat-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  height: 100%;
  height: 750px;

  // 左侧联系人列表
  .chat-list {
    flex: 0 0 280px;
    background-color: #fff;
    border-right: 1px solid #e6e6e6;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .search-container {
      padding: 16px;
      flex-shrink: 0;
    }
    
    .search-input {
      width: 100%;
      
      ::v-deep .el-input__inner {
        border-radius: 16px;
        background-color: #f5f7fa;
        border: none;
        padding-left: 35px;
        
        &:focus {
          background-color: #fff;
          border: 1px solid #dcdfe6;
        }
      }
      
      ::v-deep .el-input__prefix {
        left: 10px;
      }
    }

    .chat-items {
      flex: 1;
      overflow-y: auto;
      padding: 0 8px;
      scrollbar-width: thin; /* Firefox */
      
      /* WebKit (Chrome/Safari/Edge) */
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 2px;
      }

      .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        color: #909399;
        font-size: 13px;
        
        i {
          margin-right: 6px;
        }
      }

      .chat-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 4px;

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          background-color: #ecf5ff;
        }

        .chat-item-avatar {
          position: relative;
          margin-right: 12px;
          
          .el-avatar {
            background: #409EFF;
            transition: all 0.3s ease;
          }
          
          .online-status {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 10px;
            height: 10px;
            background-color: #67C23A;
            border: 2px solid #fff;
            border-radius: 50%;
            transform: translate(25%, 25%);
          }
        }

        .chat-item-content {
          flex: 1;
          min-width: 0;
          
          .chat-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            .chat-info {
              display: flex;
              align-items: center;
              min-width: 0;

              h3 {
                font-size: 14px;
                font-weight: 500;
                color: #303133;
                margin: 0;
                margin-right: 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .chat-time {
              font-size: 12px;
              color: #909399;
              white-space: nowrap;
            }
          }

          .chat-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .last-message {
              flex: 1;
              font-size: 13px;
              color: #606266;
              margin: 0;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-right: 8px;
            }

            .unread-badge {
              min-width: 18px;
              height: 18px;
              padding: 0 6px;
              background-color: #f56c6c;
              border-radius: 9px;
              color: #fff;
              font-size: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }

  // 右侧聊天窗口
  .chat-window {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 聊天窗口顶部
    .chat-header {
      flex-shrink: 0;
      padding: 16px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;

      .chat-title {
        .name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-right: 8px;
        }
      }

      .chat-actions {
        .el-dropdown {
          .el-icon-more {
            font-size: 18px;
            color: #909399;
            cursor: pointer;
          }
        }
      }
    }

    // 聊天消息区域
    .chat-messages {
      flex: 1;
      position: relative;
      overflow-y: auto;
      padding: 16px;
      background-color: #f5f7fa;
      scrollbar-width: thin; /* Firefox */
      
      /* WebKit (Chrome/Safari/Edge) */
      &::-webkit-scrollbar {
        width: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 2px;
      }

      .empty-messages {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        
        .el-icon-chat-dot-square {
          font-size: 48px;
          color: #909399;
          margin-bottom: 20px;
        }

        p {
          color: #909399;
          font-size: 14px;
        }
      }
      
      .messages-container {
        display: flex;
        flex-direction: column;
        
        .date-divider {
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 20px 0;
          
          .date-text {
            background-color: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            padding: 2px 10px;
            font-size: 12px;
            color: #909399;
          }
        }

        .message-item {
          display: flex;
          flex-direction: column;
          margin-bottom: 16px;

          .message-time {
            font-size: 12px;
            color: #909399;
            text-align: center;
            margin: 8px 0 4px;
          }

          .message-content {
            display: flex;
            align-items: flex-start;
            max-width: 80%;

            &.self-message {
              margin-left: auto;
              flex-direction: row;

              .message-bubble {
                margin-right: 12px;
                margin-left: 0;
                background: #ecf5ff;
                border: 1px solid #d9ecff;
                max-width: 400px;
                min-width: 54px; /* 确保很短的消息也有一定宽度 */
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;

                &.no-avatar {
                  border-top-right-radius: 4px;
                }

                &::before {
                  right: -8px;
                  left: auto;
                  border-left-color: #ecf5ff;
                  border-right-color: transparent;
                }

                .message-text {
                  color: #409EFF;
                }
              }
            }

            &.other-message {
              margin-right: auto;

              .message-bubble {
                margin-left: 12px;
                background: #fff;
                border: 1px solid #ebeef5;
                max-width: 400px;
                min-width: 54px; /* 确保很短的消息也有一定宽度 */
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;

                &.no-avatar {
                  border-top-left-radius: 4px;
                }

                &::before {
                  left: -8px;
                  border-right-color: #fff;
                  border-left-color: transparent;
                }

                .sender-name {
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 4px;
                }
              }
            }

            &.system-message {
              margin: 8px auto;
              max-width: 90%;

              .message-bubble {
                background: rgba(144, 147, 153, 0.1);
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                margin: 0;

                &::before {
                  display: none;
                }

                .message-text {
                  color: #606266;
                  font-size: 13px;
                  text-align: center;
                }
              }
            }

            .avatar {
              flex-shrink: 0;
              width: 40px;

              .el-avatar {
                border: 2px solid #fff;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                background: #409EFF;
                color: #fff;
                font-weight: bold;
              }
            }
            
            .avatar-placeholder {
              width: 40px;
              flex-shrink: 0;
            }

            .message-bubble {
              position: relative;
              padding: 10px 12px;
              border-radius: 8px;
              max-width: 100%;
              width: auto;
              display: inline-block;
              word-wrap: break-word;
              word-break: break-word;

              &.no-avatar {
                &::before {
                  display: none;
                }
              }

              &::before {
                content: '';
                position: absolute;
                top: 14px;
                border: 4px solid transparent;
              }
              
              .message-text {
                font-size: 14px;
                line-height: 1.6;
                word-wrap: break-word;
                word-break: break-word;
                white-space: pre-wrap;
              }

              .message-image {
                margin: 4px 0;
                border-radius: 4px;
                overflow: hidden;

                .el-image {
                  max-width: 240px;
                  border-radius: 4px;
                }
              }

              .message-file {
                display: flex;
                align-items: center;
                background: rgba(0, 0, 0, 0.02);
                padding: 8px;
                border-radius: 4px;

                i {
                  font-size: 20px;
                  color: #409EFF;
                  margin-right: 8px;
                }

                .file-name {
                  flex: 1;
                  font-size: 13px;
                  color: #606266;
                  margin-right: 8px;
                }
              }
            }
          }
        }
      }
    }

    // 消息输入区域
    .chat-input-area {
      flex-shrink: 0;
      padding: 16px;
      border-top: 1px solid #ebeef5;
      background-color: #fff;

      .input-toolbar {
        margin-bottom: 16px;

        .toolbar-icon {
          font-size: 18px;
          color: #909399;
          cursor: pointer;
          margin-right: 8px;
          transition: color 0.3s;
          
          &:hover {
            color: #409EFF;
          }
        }
      }

      .input-container {
        margin-bottom: 16px;
        position: relative;
        
        .message-textarea {
          ::v-deep .el-textarea__inner {
            border-radius: 8px;
            border-color: #dcdfe6;
            padding: 10px 14px;
            transition: all 0.3s;
            resize: none;
            
            &:hover, &:focus {
              border-color: #409EFF;
            }
            
            /* 滚动条样式 */
            scrollbar-width: thin;
            
            &::-webkit-scrollbar {
              width: 4px;
            }
            
            &::-webkit-scrollbar-track {
              background: transparent;
            }
            
            &::-webkit-scrollbar-thumb {
              background-color: rgba(144, 147, 153, 0.3);
              border-radius: 2px;
            }
          }
        }
        

      }

      .send-action {
        text-align: right;
        
        .el-button {
          border-radius: 20px;
          padding: 8px 20px;
          transition: all 0.3s;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
    
    // 未选择联系人时的提示
    .no-contact-selected {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: #f8f9fb;
      
      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 16px;
      }
      
      p {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}
</style> 