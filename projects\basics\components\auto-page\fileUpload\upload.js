import request from '@/utils/request'
let api = CONSTANT.FILE
//单文件上传
export function singleFileUpload(data) {
    return request({
        url: api + '/minio/single',
        method: 'post',
        data
    })
}
//上传信息
// export function uploadScreenshot(data){
//     return request({
//         url:'upload/multipart/uploadScreenshot',
//         method:'post',
//         data
//     })
// }
//
// //上传信息
// export function uploadFileInfo(data){
//     return request({
//         url:'upload/multipart/uploadFileInfo',
//         method:'post',
//         data
//     })
// }

// 上传校验
export function checkUpload(MD5, params) {
    return request({
        // url: `upload/multipart/check?md5=${MD5}`,
        url: api + `/minio/check/${MD5}`,
        method: 'get',
        params
    })
};


// 初始化上传
export function initUpload(data) {
    return request({
        // url: `upload/multipart/init`,
        url: api + `/minio/multipart/init`,
        method: 'post',
        data
    })
};


// 初始化上传
export function mergeUpload(data) {
    return request({
        // url: `upload/multipart/merge`,
        url: api + `/minio/multipart/merge`,
        method: 'post',
        data
    })
};


