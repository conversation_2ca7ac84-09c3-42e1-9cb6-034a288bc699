import request from "@/utils/request";

const apiDict = CONSTANT.SYSTEM + "/sys/dict/scope";
const api = CONSTANT.SYSTEM + "/sys/dict/item";

//获取全部数据字典
export function list(params) {
  return request({
    url: `${apiDict}/list`,
    method: "get",
    params,
  });
}

export function getInfoById(params) {
  return request({
    url: `${api}/scope/list`,
    method: "get",
    params,
  });
}

export function addDict(data) {
  return request({
    url: `${api}`,
    method: "post",
    data,
  });
}

export function getInfo(params) {
  return request({
    url: `${api}`,
    method: "get",
    params,
  });
}

export function update(data) {
  return request({
    url: `${api}`,
    method: "put",
    data,
  });
}

//单删
export function delDict(id) {
  return request({
    url: `${api}/` + id,
    method: "delete",
  });
}

//批删
export function delDictList(ids) {
    return request({
      url: `${api}/` + ids,
      method: "delete",
    });
  }
