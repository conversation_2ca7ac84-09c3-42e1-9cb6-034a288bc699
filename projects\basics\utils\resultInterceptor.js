import { Message } from 'element-ui'
import { removeToken } from '@/utils/auth'

export default [(response) => {
    if (!response.config.loadingDisabled) {
        window.loadingInstance && window.loadingInstance.delayClose()
    }

    if (response.status != 200) {
        return Promise.reject('请重试')
    }

    if (response.request.responseType === 'blob' || response.request.responseType === 'arraybuffer') {
        return response
    }
    let res = response.data
    // if the custom code is not 20000, it is judged as an error.
    // 非框架内请求直接返回结果
    if (typeof (res.code) === "undefined") {
        return res
    }
    else if (200 === res.code) {
        return res.data
    }
    else if (res.code === 401 && !response.config.hideExceptionPrompt) {
        if (window.location.href.indexOf('/login') == -1) {
            if (!new URLSearchParams(window.location.search).get('debug')) {
                removeToken()
                window.location.reload()
            } else {
                console.error(res.msg, response.config.url)
            }
        }
        return Promise.reject(res.msg)
    } else {
        if (!response.config.hideExceptionPrompt) {
            Message({
                message: res.msg || '系统异常，请重试!',
                type: 'error',
                duration: 5 * 1000
            })
        }

        return Promise.reject(res.msg || '请重新登录')
    }
}, (error) => {
    window.loadingInstance && window.loadingInstance.delayClose()
    let response = error.response;
    if (!error.config.hideExceptionPrompt) {
        Message({
            message: error.message,
            type: "error",
            duration: 5 * 1000,
        });
    }

    return Promise.reject(response.data);
}]