import chatSocketService, { formatTime } from './chatSocket.js'
import { 
  getCurrentSubscriptions, 
  getHistoryMessages, 
  readMessage, 
  getUnreadMessageCount,
  batchReadMessages,
  readAllMessages
} from './chat.js'

/**
 * 通知服务类，用于管理通知相关的功能
 */
class NoticeService {
  constructor() {
    this.systemMessages = []
    this.adminMessages = []
    this.groupMessages = []
    this.serviceAvailable = true
    this.pagination = {
      system: { current: 1, size: 10, hasMore: true, loading: false },
      admin: { current: 1, size: 10, hasMore: true, loading: false },
      group: { current: 1, size: 10, hasMore: true, loading: false }
    }
    this.unreadCounts = {
      broadcast: 0,
      private: 0,
      group: 0
    }
  }

  /**
   * 初始化WebSocket连接和订阅
   * @returns {Promise} 初始化结果
   */
  async initWebSocket() {
    try {
      // 先获取订阅信息
      const response = await getCurrentSubscriptions()
      const subscriptions = response || {}
      
      // 检查是否有任何可用的订阅
      if (!subscriptions.broadcastTopics?.length && 
          !subscriptions.groupTopics?.length && 
          !subscriptions.privateQueue) {
        console.log('没有可用的订阅信息，不启用通知服务')
        this.serviceAvailable = false
        return false
      }

      // 先建立一个连接
      await chatSocketService.connect()
      console.log('WebSocket连接已建立')
      
      // 订阅收集所有需要订阅的主题
      const allTopics = [
        ...(subscriptions.broadcastTopics || []),
        ...(subscriptions.groupTopics || [])
      ]
      
      if (subscriptions.privateQueue) {
        allTopics.push(subscriptions.privateQueue)
      }
      
      // 批量订阅所有主题
      console.log(`开始订阅 ${allTopics.length} 个主题...`)
      for (const topic of allTopics) {
        try {
          await chatSocketService.subscribe(topic, this.handleMessage.bind(this))
        } catch (error) {
          console.error(`订阅主题 ${topic} 失败:`, error)
        }
      }
      
      this.serviceAvailable = true
      
      // 订阅完成后获取历史消息
      console.log('订阅完成，获取历史消息')
      await this.fetchUnreadCounts()
      await this.fetchHistoryMessages()
      
      return true
    } catch (error) {
      console.log('获取订阅信息失败-> 不启用通知服务 错误信息: ', error)
      this.serviceAvailable = false
      return false
    }
  }

  /**
   * 处理接收到的消息
   * @param {Object} message 消息对象
   */
  handleMessage(message) {
    if (!message) return
    
    const notification = chatSocketService.handleNewMessage(message)
    
    // 根据消息类型添加到对应的列表
    switch (notification.type) {
      case 'system':
        this.systemMessages.unshift(notification)
        break
      case 'admin':
        this.adminMessages.unshift(notification)
        break
      case 'group':
        this.groupMessages.unshift(notification)
        break
    }
    
    // 更新未读消息计数
    this.fetchUnreadCounts()
  }

  /**
   * 重置分页数据
   */
  resetPagination() {
    this.pagination = {
      system: { current: 1, size: 10, hasMore: true, loading: false },
      admin: { current: 1, size: 10, hasMore: true, loading: false },
      group: { current: 1, size: 10, hasMore: true, loading: false }
    }
  }

  /**
   * 获取历史消息
   * @returns {Promise} 获取历史消息的结果
   */
  async fetchHistoryMessages() {
    this.resetPagination()
    try {
      // 获取系统广播消息
      const systemResponse = await getHistoryMessages('BROADCAST', 1, this.pagination.system.size)
      if (systemResponse && systemResponse.records) {
        this.systemMessages = systemResponse.records.map(msg => ({
          id: msg.id,
          title: msg.title || chatSocketService.getMessageTitle(msg),
          content: msg.content || '',
          time: msg.sendTime || new Date().toISOString(),
          read: msg.read || false,
          type: 'system',
          sender: msg.fromUsername || '系统',
          link: msg.link || '',
          displayName: msg.displayName || msg.fromUsername || '系统',
          picUrl: msg.picUrl || '',
          groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
        }))
        this.pagination.system.hasMore = systemResponse.records.length >= this.pagination.system.size
      }
      
      // 获取私信消息
      const privateResponse = await getHistoryMessages('PRIVATE', 1, this.pagination.admin.size)
      if (privateResponse && privateResponse.records) {
        this.adminMessages = privateResponse.records.map(msg => ({
          id: msg.id,
          title: msg.title || chatSocketService.getMessageTitle(msg),
          content: msg.content || '',
          time: msg.sendTime || new Date().toISOString(),
          read: msg.read || false,
          type: 'admin',
          sender: msg.fromUsername || '管理员',
          link: msg.link || '',
          displayName: msg.displayName || msg.fromUsername || '管理员',
          picUrl: msg.picUrl || '',
          groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
        }))
        this.pagination.admin.hasMore = privateResponse.records.length >= this.pagination.admin.size
      }
      
      // 获取群组消息
      const groupResponse = await getHistoryMessages('GROUP', 1, this.pagination.group.size)
      if (groupResponse && groupResponse.records) {
        this.groupMessages = groupResponse.records.map(msg => ({
          id: msg.id,
          title: msg.title || chatSocketService.getMessageTitle(msg),
          content: msg.content || '',
          time: msg.sendTime || new Date().toISOString(),
          read: msg.read || false,
          type: 'group',
          sender: msg.fromUsername || '群组',
          link: msg.link || '',
          displayName: msg.displayName || msg.fromUsername || '群组',
          picUrl: msg.picUrl || '',
          groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
        }))
        this.pagination.group.hasMore = groupResponse.records.length >= this.pagination.group.size
      }
      
      return {
        systemMessages: this.systemMessages,
        adminMessages: this.adminMessages,
        groupMessages: this.groupMessages
      }
    } catch (error) {
      console.error('获取历史消息失败:', error)
      throw error
    }
  }

  /**
   * 加载更多消息
   * @param {String} tabType 标签类型(system/admin/group)
   * @returns {Promise} 加载结果
   */
  async loadMoreMessages(tabType) {
    const pagination = this.pagination[tabType]
    
    // 如果没有更多数据或者正在加载中，则返回
    if (!pagination.hasMore || pagination.loading) {
      return false
    }
    
    pagination.loading = true
    
    // 根据当前标签页决定消息类型
    let chatType
    switch (tabType) {
      case 'system':
        chatType = 'BROADCAST'
        break
      case 'admin':
        chatType = 'PRIVATE'
        break
      case 'group':
        chatType = 'GROUP'
        break
      default:
        pagination.loading = false
        return false
    }
    
    // 下一页
    const nextPage = pagination.current + 1
    
    try {
      // 加载更多数据
      const response = await getHistoryMessages(chatType, nextPage, pagination.size)
      
      if (response && response.records && response.records.length > 0) {
        // 格式化消息数据
        const messages = response.records.map(msg => {
          const defaultTitle = tabType === 'system' ? '系统' : (tabType === 'admin' ? '管理员' : '群组')
          return {
            id: msg.id,
            title: msg.title || chatSocketService.getMessageTitle(msg),
            content: msg.content || '',
            time: msg.sendTime || new Date().toISOString(),
            read: msg.read || false,
            type: tabType === 'system' ? 'system' : (tabType === 'admin' ? 'admin' : 'group'),
            sender: msg.fromUsername || defaultTitle,
            link: msg.link || '',
            displayName: msg.displayName || msg.fromUsername || defaultTitle,
            picUrl: msg.picUrl || '',
            groupSpeakerDisplayName: msg.groupSpeakerDisplayName || ''
          }
        })
        
        // 添加到现有消息列表
        if (tabType === 'system') {
          this.systemMessages = [...this.systemMessages, ...messages]
        } else if (tabType === 'admin') {
          this.adminMessages = [...this.adminMessages, ...messages]
        } else if (tabType === 'group') {
          this.groupMessages = [...this.groupMessages, ...messages]
        }
        
        // 更新分页信息
        pagination.current = nextPage
        pagination.hasMore = response.records.length >= pagination.size // 如果返回的数据量小于页大小，说明没有更多数据了
        
        pagination.loading = false
        return true
      } else {
        pagination.hasMore = false
        pagination.loading = false
        return false
      }
    } catch (error) {
      console.error(`加载更多${tabType}消息失败:`, error)
      pagination.loading = false
      return false
    }
  }

  /**
   * 获取未读消息数量
   * @returns {Promise} 未读消息数量
   */
  async fetchUnreadCounts() {
    try {
      const response = await getUnreadMessageCount()
      if (response) {
        this.unreadCounts = {
          broadcast: response.broadcastUnReadCount || 0,
          private: response.privateUnReadCount || 0,
          group: response.groupUnReadCount || 0
        }
      }
      return this.unreadCounts
    } catch (error) {
      console.error('获取未读消息数量失败:', error)
      throw error
    }
  }

  /**
   * 标记消息为已读
   * @param {Object} message 消息对象
   * @returns {Promise} 更新结果
   */
  async handleReadMessage(message) {
    if (!message.read) {
      try {
        // 调用接口更新消息已读状态
        if (message.id) {
          await readMessage(message.id)
          message.read = true
          // 更新未读计数
          await this.fetchUnreadCounts()
          return true
        } else {
          message.read = true
          return true
        }
      } catch (error) {
        console.error('设置消息已读失败:', error)
        throw error
      }
    }
    return false
  }

  /**
   * 标记所有消息为已读
   * @param {String} activeTab 当前标签页(system/admin/group)
   * @returns {Promise} 更新结果
   */
  async handleReadAll(activeTab) {
    // 根据当前激活标签页决定消息类型
    let chatType
    switch (activeTab) {
      case 'system':
        chatType = 'BROADCAST'
        break
      case 'admin':
        chatType = 'PRIVATE'
        break
      case 'group':
        chatType = 'GROUP'
        break
      default:
        chatType = null
    }
    
    try {
      if (chatType) {
        // 使用一键标记为已读API
        await readAllMessages(chatType)
        
        // 更新UI中的消息状态
        if (chatType === 'BROADCAST') {
          this.systemMessages.forEach(msg => { msg.read = true })
        } else if (chatType === 'PRIVATE') {
          this.adminMessages.forEach(msg => { msg.read = true })
        } else if (chatType === 'GROUP') {
          this.groupMessages.forEach(msg => { msg.read = true })
        }
        
        // 更新未读计数
        await this.fetchUnreadCounts()
        return true
      } else {
        // 如果没有选择特定标签页，则获取所有未读消息ID
        const allMessageIds = [
          ...this.systemMessages.filter(msg => !msg.read).map(msg => msg.id),
          ...this.adminMessages.filter(msg => !msg.read).map(msg => msg.id),
          ...this.groupMessages.filter(msg => !msg.read).map(msg => msg.id)
        ].filter(id => id) // 过滤掉可能的undefined或null
        
        if (allMessageIds.length > 0) {
          // 使用批量标记已读API
          await batchReadMessages(allMessageIds)
          
          // 更新UI中的消息状态
          this.systemMessages.forEach(msg => { if (allMessageIds.includes(msg.id)) msg.read = true })
          this.adminMessages.forEach(msg => { if (allMessageIds.includes(msg.id)) msg.read = true })
          this.groupMessages.forEach(msg => { if (allMessageIds.includes(msg.id)) msg.read = true })
          
          // 更新未读计数
          await this.fetchUnreadCounts()
          return true
        }
        return false
      }
    } catch (error) {
      console.error('设置所有消息已读失败:', error)
      throw error
    }
  }

  /**
   * 获取总未读消息数
   * @returns {Number} 未读消息总数
   */
  get unreadCount() {
    return this.unreadCounts.broadcast + this.unreadCounts.private + this.unreadCounts.group
  }

  /**
   * 获取系统未读消息数
   * @returns {Number} 系统未读消息数
   */
  get unreadSystem() {
    return this.unreadCounts.broadcast
  }

  /**
   * 获取私信未读消息数
   * @returns {Number} 私信未读消息数
   */
  get unreadAdmin() {
    return this.unreadCounts.private
  }

  /**
   * 获取群组未读消息数
   * @returns {Number} 群组未读消息数
   */
  get unreadGroup() {
    return this.unreadCounts.group
  }

  /**
   * 获取所有消息
   * @returns {Object} 所有消息
   */
  getAllMessages() {
    return {
      systemMessages: this.systemMessages,
      adminMessages: this.adminMessages,
      groupMessages: this.groupMessages,
      pagination: this.pagination,
      unreadCounts: this.unreadCounts
    }
  }

  /**
   * 获取文件URL
   * @param {String} picUrl 图片URL
   * @returns {String} 完整的文件URL
   */
  getFileUrl(picUrl) {
    if (!picUrl) return ''
    const fileBaseUrl = process.env.VUE_APP_FILE_URL || ''
    return fileBaseUrl + picUrl
  }
}

// 创建单例
const noticeService = new NoticeService()

export default noticeService
export { formatTime } 