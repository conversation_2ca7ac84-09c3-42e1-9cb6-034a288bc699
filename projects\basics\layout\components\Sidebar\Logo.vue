<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <svg-icon
          v-if="icon.length > 1"
          class="sidebar-logo"
          :icon-class="icon"
        ></svg-icon>
        <img v-else-if="logo" :src="logo" class="sidebar-logo" />
        <h1 v-else class="sidebar-title">{{ title }}</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <svg-icon
          v-if="icon.length > 1"
          class="sidebar-logo"
          :icon-class="icon"
        ></svg-icon>
        <img v-else-if="logo" :src="logo" class="sidebar-logo" />
        <h1 class="sidebar-title">{{ title }}</h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      // title: '后台管理系统',
      // logo: require('@/assets/logo.png')
    };
  },
  computed: {
    ...mapState({
      title: (state) => state.settings.title || "后台管理系统",
      logo: (state) => state.settings.logo || require("@/assets/logo.png"),
      icon: (state) => state.settings.icon || "",
    }),
  },
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 100px;
  line-height: 100px;
  // background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
      color: rgba(85, 100, 125, 1);
    }

    & .sidebar-title {
      display: inline-block;
      vertical-align: middle;
      width: 162px;
      color: rgba(53, 67, 84, 1);
      font-weight: bold;
      font-style: italic;
      font-size: 18px;
      line-height: 28px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
