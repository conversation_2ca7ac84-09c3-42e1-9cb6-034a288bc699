import request from '@/utils/request'
import { getToken } from '@/utils/auth'

/**
 * 请求前缀
 */
// var api = 'device/info'

/**
 * 获取列表
 * @param params {
 *  name:"",
 *  current: 1,
 *  size: 10,
 *  ranges:{
 *      createTime:[2023-06-13 00:00:00, 2023-06-14 00:00:00]       //范围查询
 *  }
 *  enums:{
 *      name:[1,2,4,5]                                                //枚举查询
 *  },
 *  nulls:{
 *      isRelease: true,                                            //空查询
 *      isShow: false
 *  },
 *  orders:{
 *      id: 'desc',                                                 //排序
 *      createTime: 'asc'
 *  }
 * }
 * @returns [Array]
 */
export function list(api, params, base = CONSTANT.SYSTEM, scope) {
    base = base || CONSTANT.SYSTEM
    scope = typeof(scope) === 'undefined' ? true : scope
    return request({
        url: base + (scope ? `/${api}/scope/list` : `/${api}/list`),
        params
    })
}

/**
 * 获取下拉列表
*/
// export function treeList(api, params, base) {
//     base = base || CONSTANT.SYSTEM
//     return request({
//         url: base + `/${api}/list`,
//         params
//     })
// }

export function treeList(api, params) {
    return request({
      url: `${api}`,
      method: 'get',
      params
    })
  }

  
/**
 * 获取单个对象
 * @param id {Object}
 * @returns {Object}
 */
export function get(api, id, base ) {
    base = base || CONSTANT.SYSTEM
    return request({
        url: base + `/${api}/${id}`
    })
}

/**
 * 存储
 * @param data {Object} or [Array]
 * @returns [Array]
 */
export function save(api, data, base) {
    base = base || CONSTANT.SYSTEM
    return request({
        url: base + `/${api}`,
        method: 'post',
        data
    })
}

/**
 * 更新
 * @param data {Object} or [Array]
 * @returns {true} or {false}
 */
export function update(api, data, base) {
    base = base || CONSTANT.SYSTEM
    return request({
        url: base + `/${api}`,
        method: 'put',
        data
    })
}

/**
 * 删除
 * @param ids [Array]
 * @returns {true} or {false}
 */
export function remove(api, ids, base) {
    base = base || CONSTANT.SYSTEM
    return request({
        url: base + `/${api}/${ids}`,
        method: 'delete'
    })
}

/**
 * 审批记录
 */
export function history(params, base) {
    base = base || CONSTANT.SYSTEM
    return request({
        url: base + `/form/data/approval_log/list`,
        params
    })
}

/**
 * 审批
 */
export function action(data, base, collectionName) {
    base = base || CONSTANT.SYSTEM
    return request({
        url: base + `/approval/${collectionName}/action`,
        method: 'put',
        data
    })
}

/**
 * 是否显示审批
 */
export function approval(params, base) {
    base = base || CONSTANT.SYSTEM
    return request({
        url: base + `/form/info`,
        params
    })
}


/**
 * 导出
 */
export function exports(api, params, base) {
    base = base || CONSTANT.SYSTEM
    request({
        method: 'get',
        url: base + `/${api}/export`,
        headers: {
            'authentication_info': getToken(),
        },
        params,
        responseType: 'blob'
    }).then((data) => {
        let blob = new Blob([data.data], {
            type: 'application/vnd.ms-excel'
        })
        let url = window.URL.createObjectURL(blob)
        let a = document.createElement('a')
        a.href = url
        let fileName = decodeURI((data.headers['content-disposition'].split("="))[1]).slice(7)
        a.download = fileName
        a.click()
        window.URL.revokeObjectURL(a.href)
    })
}


/**
 * 导入模板
 */
export function importsTemplate(api, params, base) {
    base = base || CONSTANT.SYSTEM
    request({
        method: 'get',
        url: base + `/${api}/template`,
        headers: {
            'authentication_info': getToken(),
        },
        params,
        responseType: 'blob'
    }).then((data) => {
        let blob = new Blob([data.data], {
            type: 'application/vnd.ms-excel'
        })
        let url = window.URL.createObjectURL(blob)
        let a = document.createElement('a')
        a.href = url
        let fileName = decodeURI((data.headers['content-disposition'].split("="))[1]).slice(7)
        a.download = fileName
        a.click()
        window.URL.revokeObjectURL(a.href)
    })
}
