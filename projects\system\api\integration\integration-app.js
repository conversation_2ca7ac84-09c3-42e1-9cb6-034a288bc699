import request from '@/utils/request'

const integrationAppApi = CONSTANT.INTEGRATION+ '/integration/app'

// 获取集成应用列表
export function getIntegrationAppList(params) {
  return request({
    url: `${integrationAppApi}/list`,
    method: 'get',
    params
  })
}

// 获取集成应用分组统计
export function getIntegrationAppCount(params) {
  return request({
    url: `${integrationAppApi}/count`,
    method: 'get',
    params
  })
}

// 获取单个集成应用详情
export function getIntegrationAppDetail(id) {
  return request({
    url: `${integrationAppApi}`,
    method: 'get',
    params: { id }
  })
}

// 创建集成应用
export function createIntegrationApp(data) {
  return request({
    url: `${integrationAppApi}`,
    method: 'post',
    data
  })
}

// 更新集成应用
export function updateIntegrationApp(data) {
  return request({
    url: `${integrationAppApi}`,
    method: 'put',
    data
  })
}

// 删除集成应用
export function deleteIntegrationApp(id) {
  return request({
    url: `${integrationAppApi}/${id}`,
    method: 'delete'
  })
}
