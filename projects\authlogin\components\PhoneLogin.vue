<template>
    <div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
            <el-form-item prop="phone">
                <el-input maxlength="11" v-model="loginForm.phone" placeholder="手机号"
                    prefix-icon="el-icon-mobile-phone" />
            </el-form-item>
            <el-form-item prop="smsCode">
                <ValidateCodeInput :countdown="countdown" v-model="loginForm.smsCode" prefix-icon="el-icon-message"
                    @send="clickSend()">
                </ValidateCodeInput>
            </el-form-item>

            <el-form-item>
                <el-button v-enter="handleLogin" :loading="loading" type="primary" class="login-button"
                    @click="handleLogin">登
                    录</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { sendCode } from '@/api/login/notice'
import { validatePhone, phonePattern } from './consts'
import ValidateCodeInput from './ValidateCodeInput.vue';
import LoginMixin from "./LoginMixin.js";

export default {
    components: {
        ValidateCodeInput
    },
    mixins: [LoginMixin],
    data() {
        return {
            loading: false,
            countdown: 0,
            loginForm: {
                phone: '',
                smsCode: '',
            },

            loginRules: {
                phone: [
                    { required: true, trigger: 'blur', validator: validatePhone }
                ],
                smsCode: [
                    { required: true, message: '请输入短信验证码', trigger: 'submit' },
                    { len: 6, message: '验证码长度应为6位', trigger: 'submit' }
                ]
            }
        }
    },
    destroyed() {
        this.timerInterval && clearInterval(this.timerInterval)
    },

    methods: {

        startCountdown() {
            this.countdown = 60
            this.timerInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.timerInterval && clearInterval(this.timerInterval)
                }
            }, 1000)
        },

        clickSend() {
            if (!phonePattern.test(this.loginForm.phone)) {
                this.$message.error('请输入正确的手机号')
                return
            }
            if (this.countdown > 0) {
                return
            }
            sendCode({
                identifier: this.loginForm.phone,
                type: 'sms',
                scene: 'login'
            }).then(res => {
                this.$message.success('验证码已发送')
                this.startCountdown()
            }).catch(err => {
                this.$message.error(err || '发送失败')
            })
        },

        handleLogin() {

            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    if (!this.checkAgreement()) {
                        return
                    }
                    this.loading = true
                    this.$store
                        .dispatch("user/smsLogin", {data: {
                            phone: this.loginForm.phone,
                            code: this.loginForm.smsCode
                        }}).then(res => {
                            this.loading = false
                            this.loginComplete(res)
                        }).catch((err) => {
                            this.loading = false
                            console.log("error submit!!", err)
                            this.loginError(err)
                        })
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.login-form {
    .el-input {
        height: 44px;

        ::v-deep .el-input__inner {
            height: 44px;
            padding-left: 48px;
            font-size: 16px;
        }

        ::v-deep .el-input__prefix {
            left: 10px;
            font-size: 18px;
        }

        .el-input__inner:focus {
            border-color: #1682e6;
        }
    }

    .sms {
        height: 100%;
        display: flex;
        align-items: center;

        .el-button:not(:disabled) {
            background-color: rgb(0, 122, 255);
            color: #fff;
            border: none;

            &:hover {
                background-color: rgb(0, 86, 179);
            }
        }

        .el-button:disabled {
            border: none;
        }
    }

    .login-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        margin-top: 20px;
        background: rgb(0, 122, 255);
        border-radius: 8px;
        border: none;

        &:hover {
            background: rgb(0, 86, 179);
        }
    }
}
</style>
