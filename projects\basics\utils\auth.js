// import Cookies from 'js-cookie'

const TokenKey = 'gd_token'

// export function getToken() {

//   return Cookies.get(TokenKey)
// }

// export function setToken(token) {
//   return Cookies.set(Token<PERSON><PERSON>, token)
// }

// export function removeToken() {
//   return Cookies.remove(TokenKey)
// }


export function getToken() {
  const searchParams = new URLSearchParams(window.location.search)
  const authorization = searchParams.get('Authorization')
  if (authorization && authorization.length > 0) {
    setToken(authorization)

    searchParams.delete('Authorization')
    window.location.replace(`${window.location.origin}${window.location.pathname}?${searchParams.toString()}`)

    return authorization
  }
  return localStorage.getItem(TokenKey)
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}

