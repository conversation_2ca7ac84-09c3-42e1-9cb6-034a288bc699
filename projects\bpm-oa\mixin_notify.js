import Vue from 'vue'

import Notify from './Notify.vue'

export default {
    inject: ['navbarSlotDom'],
    mounted() {
        const slotdom = this.navbarSlotDom && this.navbarSlotDom()
        if (slotdom) {
            const badge = slotdom.querySelector('div.notify-badge')
            if (!badge) {
                let element = document.createElement('div')
                this.vue = new Vue({
                    render: h => h(Notify)
                }).$mount(element)
                slotdom.appendChild(this.vue.$el)
            }
        }
    },
    destroyed() {
        // this.vue && this.vue.$el?.parentNode?.removeChild(this.vue.$el)
        // this.vue && this.vue.$destroy()
    },
}
