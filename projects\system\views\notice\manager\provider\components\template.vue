<template>
  <div class="template-container">
    <div class="page-header">
      <div class="header-title">
        <h2>消息模板管理</h2>
      </div>

      <div class="header-tools">
        <div class="unified-search">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入模板名称搜索"
            prefix-icon="el-icon-search"
            clearable
            class="search-input"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          />

          <el-select v-model="queryParams.groupName" placeholder="分组" clearable class="group-select"
            @change="handleSearch">
            <el-option v-for="item in groupOptions" :key="item" :label="item" :value="item">
              <i class="el-icon-folder-opened" style="color: #409EFF" />
              <span style="margin-left: 8px">{{ item }}</span>
            </el-option>
          </el-select>
        </div>

        <div class="button-group">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
            添加模板
          </el-button>
          <el-button type="primary" icon="el-icon-refresh" @click="loadTemplates">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <el-table
        :data="templates"
        v-loading="loading"
        border
        stripe
        fit
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column prop="name" label="模板名称" min-width="150">
          <template slot-scope="scope">
            <el-link type="primary" @click="handleEdit(scope.row)">{{ scope.row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="模板编码" min-width="150" />
        <el-table-column prop="groupName" label="分组" width="150">
          <template slot-scope="scope">
            <el-tag type="success" size="medium" v-if="scope.row.groupName">{{ scope.row.groupName }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="thirdTemplateCode" label="第三方模板编码" min-width="150">
          <template slot-scope="scope">
            {{ scope.row.thirdTemplateCode || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="templateContent" label="模板内容" min-width="250">
          <template slot-scope="scope">
            <el-popover
              placement="top-start"
              width="400"
              trigger="hover"
            >
              <div class="popover-content">{{ formatTemplateContent(scope.row.templateContent) }}</div>
              <div slot="reference" class="template-content text-ellipsis">
                {{ formatTemplateContent(scope.row.templateContent) }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'info'" effect="dark">
              {{ scope.row.enabled ? '已启用' : '已禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="170" />
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          :current-page="queryParams.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.size"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 模板表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="templateForm"
        :model="formData"
        :rules="rules"
        label-width="100px"
        class="detail-form"
      >
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入模板名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板编码" prop="code">
                <el-input v-model="formData.code" placeholder="请输入模板编码" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分组" prop="groupName">
                <el-select v-model="formData.groupName" filterable allow-create default-first-option
                  placeholder="请选择或输入分组名称" @focus="loadGroupNames">
                  <el-option v-for="item in groupOptions" :key="item" :label="item" :value="item">
                    <i class="el-icon-folder"></i>
                    <span style="margin-left: 8px">{{ item }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="第三方编码" prop="thirdTemplateCode">
                <el-input v-model="formData.thirdTemplateCode" placeholder="请输入第三方模板编码" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否启用" prop="enabled">
                <el-switch v-model="formData.enabled" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板类型" prop="templateType">
                <el-radio-group v-model="formData.templateType">
                  <el-radio label="PLAIN">普通文本</el-radio>
                  <el-radio label="IM">站内信</el-radio>
                  <el-radio label="SMTP">邮件</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">模板内容</div>
          
          <!-- 普通文本模板 -->
          <template v-if="formData.templateType === 'PLAIN'">
            <el-form-item prop="templateContent" class="template-content-item">
              <el-input
                type="textarea"
                v-model="formData.templateContent"
                placeholder="请输入模板内容"
                :rows="8"
              />
            </el-form-item>
          </template>
          
          <!-- 站内信模板 -->
          <template v-if="formData.templateType === 'IM'">
            <el-form-item label="正文内容" prop="imContentText" class="content-item">
              <Editor 
                :content="imContent.content" 
                @onContentChange="handleEditorChange"
              />
            </el-form-item>
            <el-form-item label="跳转链接" prop="imContentLink">
              <el-input
                v-model="imContent.link"
                placeholder="请输入跳转链接（选填）"
              />
            </el-form-item>
            <el-form-item label="发送人" prop="imContentSender">
              <el-input
                v-model="imContent.sender"
                placeholder="请输入发送人，支持变量如${user.name}（不填则默认以发送人登录信息为准）"
              />
            </el-form-item>
          </template>
          
          <!-- 邮件模板 -->
          <template v-if="formData.templateType === 'SMTP'">
            <el-form-item label="邮件标题" prop="smtpContentTitle">
              <el-input
                v-model="smtpContent.title"
                placeholder="请输入邮件标题"
              />
            </el-form-item>
            <el-form-item label="邮件内容" prop="smtpContentText" class="content-item">
              <el-input
                type="textarea"
                v-model="smtpContent.content"
                placeholder="请输入邮件内容"
                :rows="6"
              />
            </el-form-item>
          </template>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTemplateList, createTemplate, updateTemplate, deleteTemplate, getTemplateDetail, getTemplateGroupNames } from '@system/api/notice/manager'
import Editor from '@/components/Editor/index.vue'

export default {
  name: 'TemplateManagement',
  components: {
    Editor
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      templates: [],
      providerId: null,
      // 分页相关
      total: 0,
      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      formLoading: false,
      submitLoading: false,
      isEdit: false,
      groupOptions: [], // 分组选项
      queryParams: {
        current: 1,
        size: 10,
        keyword: '',
        groupName: '',
        providerId: ''
      },
      formData: {
        id: '',
        name: '',
        code: '',
        thirdTemplateCode: '',
        templateContent: '',
        enabled: true,
        providerId: '',
        templateType: 'PLAIN', // 新增模板类型字段：PLAIN-普通文本, IM-站内信, SMTP-邮件
        groupName: '' // 新增分组字段
      },
      rules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入模板编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        templateContent: [
          { required: true, message: '请输入模板内容', trigger: 'blur' }
        ]
      },
      // 站内信相关字段
      imContent: {
        content: '',
        link: '',
        sender: '' // 新增发送人字段
      },
      // SMTP邮件相关字段
      smtpContent: {
        title: '',
        content: ''
      }
    }
  },
  created() {
    const id = this.$route.params.id
    if (!id) {
      this.$message.error('供应商ID不能为空')
      this.$router.push('/notice/provider')
      return
    }
    this.providerId = id
    this.queryParams.providerId = id
    this.loadTemplates()
    this.loadGroupNames()
  },
  methods: {
    async loadTemplates() {
      this.loading = true
      try {
        const params = { ...this.queryParams }
        // 过滤掉空值
        Object.keys(params).forEach(key => {
          if (params[key] === '' || params[key] === null || params[key] === undefined) {
            delete params[key]
          }
        })
        const data = await getTemplateList(params)
        this.templates = data.records || []
        this.total = data.total || 0
      } catch (error) {
        console.error('加载模板列表失败:', error)
        this.$message.error('加载模板列表失败')
      } finally {
        this.loading = false
      }
    },
    // 加载分组名称列表
    async loadGroupNames() {
      try {
        const data = await getTemplateGroupNames()
        this.groupOptions = data || []
      } catch (error) {
        console.error('获取分组列表失败:', error)
      }
    },
    formatTemplateContent(content) {
      return content;
      // if (!content) return '';
      // try {
      //   // 尝试解析JSON内容
      //   const template = JSON.parse(content);
        
      //   // 处理站内信格式 (内容+链接)
      //   if (template.content !== undefined && template.link !== undefined) {
      //     return template.content + (template.link ? `\n跳转链接: ${template.link}` : '');
      //   }
        
      //   // 处理SMTP邮件格式 (标题+内容)
      //   if (template.title !== undefined && template.content !== undefined) {
      //     return `标题: ${template.title}\n${template.content}`;
      //   }
        
      //   // 处理其他可能的JSON格式
      //   if (template.content) {
      //     return template.content;
      //   }
        
      //   if (template.templateContent) {
      //     return template.templateContent;
      //   }
        
      //   // 如果JSON中没有找到预期的结构，则返回字符串化的JSON
      //   return JSON.stringify(template, null, 2);
      // } catch (e) {
      //   // 不是JSON格式，直接返回原内容
      //   return content;
      // }
    },
    handleSearch() {
      this.queryParams.current = 1
      this.queryParams.keyword = this.searchKeyword
      this.loadTemplates()
    },
    handleAdd() {
      this.isEdit = false
      this.dialogTitle = '添加模板'
      this.formData = {
        id: '',
        name: '',
        code: '',
        thirdTemplateCode: '',
        templateContent: '',
        enabled: true,
        providerId: this.providerId,
        templateType: 'PLAIN',
        groupName: ''
      }
      // 重置站内信内容
      this.imContent = {
        content: '',
        link: '',
        sender: '' // 重置发送人字段
      }
      // 重置邮件内容
      this.smtpContent = {
        title: '',
        content: ''
      }
      this.loadGroupNames()
      this.dialogVisible = true
    },
    async handleEdit(row) {
      this.isEdit = true
      this.dialogTitle = '编辑模板'
      this.dialogVisible = true
      this.formLoading = true
      try {
        const data = await getTemplateDetail(row.id)
        this.formData = {
          ...data,
          providerId: this.providerId,
          templateType: 'PLAIN', // 默认为普通文本
          groupName: data.groupName || ''
        }
        
        // 重置站内信内容
        this.imContent = {
          content: '',
          link: '',
          sender: '' // 重置发送人字段
        }
        
        // 解析模板内容格式
        this.parseTemplateContent(this.formData.templateContent)
        this.loadGroupNames()
      } catch (error) {
        console.error('获取模板详情失败:', error)
        this.$message.error('获取模板详情失败')
        this.dialogVisible = false
      } finally {
        this.formLoading = false
      }
    },
    
    // 解析模板内容，根据格式设置对应的表单数据
    parseTemplateContent(content) {
      if (!content) return
      
      try {
        const template = JSON.parse(content)
        
        // 检测站内信格式
        if (template.content !== undefined && template.link !== undefined) {
          this.formData.templateType = 'IM'
          this.imContent.content = template.content || ''
          this.imContent.link = template.link || ''
          this.imContent.sender = template.sender || '' // 解析发送人字段
        }
        // 检测SMTP邮件格式
        else if (template.title !== undefined && template.content !== undefined) {
          this.formData.templateType = 'SMTP'
          this.smtpContent.title = template.title
          this.smtpContent.content = template.content
        }
      } catch (e) {
        // 如果不是JSON格式，保持为普通文本类型
        this.formData.templateType = 'PLAIN'
        this.formData.templateContent = content || ''
      }
    },
    
    // 根据模板类型格式化内容
    getFormattedContent() {
      switch (this.formData.templateType) {
        case 'IM':
          return JSON.stringify({
            content: this.imContent.content || '',
            link: this.imContent.link || '',
            sender: this.imContent.sender || '' // 加入发送人字段
          })
        case 'SMTP':
          return JSON.stringify({
            title: this.smtpContent.title,
            content: this.smtpContent.content
          })
        case 'PLAIN':
        default:
          return this.formData.templateContent
      }
    },
    
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该模板吗？', '提示', {
          type: 'warning'
        })
        await deleteTemplate(row.id)
        this.$message.success('删除成功')
        this.loadTemplates()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除模板失败:', error)
          this.$message.error('删除模板失败')
        }
      }
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs.templateForm.validate()
        if (!valid) return
        
        // 校验各类型模板内容
        if (this.formData.templateType === 'IM') {
          if (!this.imContent.content) {
            this.$message.warning('请输入站内信内容')
            return
          }
        }
        
        if (this.formData.templateType === 'SMTP') {
          if (!this.smtpContent.title) {
            this.$message.warning('请输入邮件标题')
            return
          }
          if (!this.smtpContent.content) {
            this.$message.warning('请输入邮件内容')
            return
          }
        }

        this.submitLoading = true
        
        // 根据模板类型格式化内容
        const submitData = {
          ...this.formData,
          templateContent: this.getFormattedContent()
        }
        
        const method = this.isEdit ? updateTemplate : createTemplate
        await method(submitData)
        
        this.$message.success(`${this.isEdit ? '更新' : '添加'}成功`)
        this.dialogVisible = false
        this.loadTemplates()
      } catch (error) {
        console.error(`${this.isEdit ? '更新' : '添加'}模板失败:`, error)
        this.$message.error(`${this.isEdit ? '更新' : '添加'}模板失败`)
      } finally {
        this.submitLoading = false
      }
    },
    handleEditorChange(content) {
      this.imContent.content = content;
    },
    // 分页相关方法
    handleSizeChange(val) {
      this.queryParams.size = val
      this.queryParams.current = 1
      this.loadTemplates()
    },
    handleCurrentChange(val) {
      this.queryParams.current = val
      this.loadTemplates()
    }
  }
}
</script>

<style lang="scss" scoped>
.template-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 10px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        white-space: nowrap;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }

    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
      justify-content: flex-end;

      .unified-search {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: nowrap;

        .search-input {
          width: 220px;

          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;

              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }
          }
        }

        .group-select {
          width: 160px;

          ::v-deep .el-input__inner {
            height: 36px;
            line-height: 36px;
            border-radius: 8px;
            border: 1px solid #e0e5ee;
            background: #f9fafc;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0d0e9;
              background: #f5f7fa;
            }

            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
          }
        }
      }

      .button-group {
        display: flex;
        gap: 8px;

        .el-button {
          padding: 8px 16px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 8px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          height: 36px;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 16px;
          }
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    ::v-deep .el-table {
      flex: 1;
      
      th {
        background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
        font-weight: 600;
        color: #1a1f36;
        height: 44px;
        padding: 8px 0;
        
        .cell {
          font-size: 14px;
          line-height: 28px;
        }
      }

      td {
        padding: 8px 0;
        
        .cell {
          line-height: 1.5;
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;

      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;

        .btn-prev,
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;

          &:not(.disabled):hover {
            border-color: #409EFF;
          }

          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }

        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }

        .el-pagination__jump {
          margin-left: 16px;
        }

        .el-select .el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }

        .el-pagination__editor.el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }
}

::v-deep .el-popover {
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  
  .popover-content {
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    font-size: 14px;
    line-height: 1.6;
    color: #303133;

    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: rgba(144, 147, 153, 0.3);

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 表单样式 */
.detail-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .content-wrapper {
      border: 1px solid #eef1f7;
      border-radius: 12px;
      overflow: hidden;
      background: #fff;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

      .content-header {
        padding: 16px;
        background: linear-gradient(to right, #fcfcfd, #f9fafc);
        border-bottom: 1px solid #eef1f7;
        font-weight: 600;
        color: #1a1f36;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .content-body {
        padding: 16px;
        background: #fcfcfd;
      }
    }

    .template-content-item {
      ::v-deep {
        .el-form-item__content {
          margin-left: 0 !important;
        }
        
        .el-form-item__label {
          display: none;
        }
        
        .el-textarea {
          width: 100%;
        }
      }
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        &.no-margin {
          margin-bottom: 0;
        }

        .el-form-item__label {
          color: #606266;
          font-weight: 500;
          line-height: 38px;
          padding: 0;
          height: 38px;
        }

        .el-form-item__content {
          color: #303133;
          line-height: 38px;
          min-height: 38px;

          .el-input__inner {
            border-radius: 10px;
            height: 38px;
            line-height: 38px;
            background: #fff;
            border: 1px solid #e0e5ee;
            
            &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }

          .el-textarea__inner {
            border-radius: 10px;
            border-color: #e0e5ee;
            background: #fff;
            padding: 12px;
            line-height: 1.6;
            
            &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
        }
      }
    }
  }
}

::v-deep .el-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    color: #606266;
    background: #fff;

    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 2px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    
    .el-button {
      padding: 9px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }

      &--primary {
        background-color: #409EFF;
        border-color: #409EFF;

        &:hover {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}
</style> 