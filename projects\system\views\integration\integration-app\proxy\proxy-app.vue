<template>
  <div class="proxy-detail">
    <!-- 应用详情头部 -->
    <app-detail-header
      :app-detail="appDetail"
      :editable="true"
      @go-back="goBack"
      @update-name="handleUpdateName"
      @update-description="handleUpdateDescription"
      @update-enabled="handleUpdateEnabled"
      @update-group-name="handleUpdateGroupName"
      @update-sort="handleUpdateSort"
    />



    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：Tab导航 -->
      <div class="left-sidebar">
        <div class="tab-navigation">
          <div
            v-for="tab in tabs"
            :key="tab.key"
            :class="['tab-item', { active: activeTab === tab.key }]"
            @click="switchTab(tab.key)"
          >
            <i :class="tab.icon"></i>
            <span>{{ tab.label }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧：内容区域 -->
      <div class="content-area">
        <div class="content-wrapper">
          <!-- 连接器配置 -->
          <div v-show="activeTab === 'connector'" class="tab-content">
            <div class="content-body">
              <connector-selector
                ref="connectorSelector"
                :app-id="appId"
                @config-saved="handleConnectorConfigSaved"
              />
            </div>
          </div>

          <!-- API请求配置 -->
          <div v-show="activeTab === 'api'" class="tab-content">
            <div class="content-body">
              <api-request-config
                ref="apiConfig"
                :app-id="appId"
                @api-change="handleApiChange"
              />
            </div>
          </div>

          <!-- 监控 -->
          <div v-show="activeTab === 'monitor'" class="tab-content">
            <div class="content-body">
              <div class="monitor-placeholder">
                <el-empty description="监控功能开发中...">
                  <el-button type="primary">配置监控</el-button>
                </el-empty>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getIntegrationAppDetail, updateIntegrationApp } from '@system/api/integration/integration-app'
import ConnectorSelector from './components/ConnectorSelector'
import ApiRequestConfig from './components/ApiRequestConfig'
import AppDetailHeader from '../components/AppDetailHeader'

export default {
  name: 'ProxyDetail',
  components: {
    ConnectorSelector,
    ApiRequestConfig,
    AppDetailHeader
  },
  data() {
    return {
      appId: '',
      appDetail: {},
      loading: false,
      saving: false,
      activeTab: 'connector', // 当前激活的tab
      tabs: [
        {
          key: 'connector',
          label: '选择连接',
          icon: 'el-icon-connection'
        },
        {
          key: 'api',
          label: 'API配置',
          icon: 'el-icon-setting'
        },
        {
          key: 'monitor',
          label: '监控',
          icon: 'el-icon-monitor'
        }
      ],
      selectedConnectorIds: [],
      apiList: []
    }
  },
  created() {
    this.appId = this.$route.query.id || this.$route.params.id
    this.loadAppDetail()
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 处理更新应用名称
    async handleUpdateName(newName) {
      this.appDetail.name = newName
      try {
        await updateIntegrationApp({
          ...this.appDetail,
          name: newName
        })
      } catch (error) {
        this.$message.error('应用名称更新失败')
        console.error('更新应用名称失败:', error)
      }
    },

    // 处理更新应用描述
    async handleUpdateDescription(newDescription) {
      this.appDetail.description = newDescription
      try {
        await updateIntegrationApp({
          ...this.appDetail,
          description: newDescription
        })
      } catch (error) {
        this.$message.error('应用描述更新失败')
        console.error('更新应用描述失败:', error)
      }
    },

    // 处理更新启用状态
    async handleUpdateEnabled(newEnabled) {
      this.appDetail.enabled = newEnabled
      try {
        await updateIntegrationApp({
          ...this.appDetail,
          enabled: newEnabled
        })
      } catch (error) {
        this.$message.error('应用状态更新失败')
        console.error('更新应用状态失败:', error)
      }
    },

    // 处理更新分组名称
    async handleUpdateGroupName(newGroupName) {
      this.appDetail.groupName = newGroupName
      try {
        await updateIntegrationApp({
          ...this.appDetail,
          groupName: newGroupName
        })
      } catch (error) {
        this.$message.error('应用分组更新失败')
        console.error('更新应用分组失败:', error)
      }
    },

    // 处理更新排序
    async handleUpdateSort(newSort) {
      this.appDetail.sort = newSort
      try {
        await updateIntegrationApp({
          ...this.appDetail,
          sort: newSort
        })
      } catch (error) {
        this.$message.error('应用排序更新失败')
        console.error('更新应用排序失败:', error)
      }
    },

    // 加载应用详情
    async loadAppDetail() {
      if (!this.appId) return

      this.loading = true
      try {
        // 加载应用详情
        const appDetailResponse = await getIntegrationAppDetail(this.appId)

        // 全局响应拦截器已经把data取出来了，直接使用response
        this.appDetail = appDetailResponse || {}
      } catch (error) {
        console.error('加载应用详情失败:', error)
        this.$message.error('加载应用详情失败')
      } finally {
        this.loading = false
      }
    },

    // 处理连接器配置保存
    handleConnectorConfigSaved(connectorIds) {
      this.selectedConnectorIds = connectorIds
      console.log('连接器配置已保存:', connectorIds)
    },

    // 处理API配置变化
    handleApiChange(apiList) {
      this.apiList = apiList
    },



    // 切换tab
    switchTab(tabKey) {
      this.activeTab = tabKey
    },



    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 获取应用状态类型
    getAppStatusType() {
      return this.appDetail.enabled ? 'success' : 'danger'
    },

    // 获取应用状态文本
    getAppStatusText() {
      return this.appDetail.enabled ? '启用' : '禁用'
    },

    // 获取应用状态样式类
    getAppStatusClass() {
      return this.appDetail.enabled ? 'status-enabled' : 'status-disabled'
    },

    // 获取应用状态图标
    getAppStatusIcon() {
      return this.appDetail.enabled ? 'el-icon-check' : 'el-icon-close'
    }
  }
}
</script>

<style lang="scss" scoped>
.proxy-detail {
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4f8 50%, #f0f9ff 100%);
  display: flex;
  flex-direction: column;

  .page-header {
    background: white;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .back-btn {
        font-size: 14px;
        color: #666;
        
        &:hover {
          color: #1890ff;
        }
      }
      
      .app-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .app-icon {
          width: 48px;
          height: 48px;
          border-radius: 10px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f2f5;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .default-icon {
            font-size: 20px;
            font-weight: 500;
            border-radius: 10px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        
        .app-meta {
          display: flex;
          gap: 24px;
          align-items: flex-start;
          flex: 1;

          .app-basic-info {
            flex: 0 0 auto;

            .app-name-section {
              margin-bottom: 8px;

              .app-name {
                font-size: 20px;
                font-weight: 600;
                color: #262626;
                margin: 0;
                cursor: pointer;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 4px 8px;
                border-radius: 6px;
                transition: all 0.2s ease;

                &:hover {
                  background: #f0f9ff;
                  color: #1890ff;

                  .edit-icon {
                    opacity: 1;
                  }
                }

                .edit-icon {
                  font-size: 14px;
                  opacity: 0;
                  transition: opacity 0.2s ease;
                }
              }

              .name-edit-section {
                display: flex;
                align-items: center;
                gap: 8px;

                .name-input {
                  max-width: 200px;
                }

                .edit-actions {
                  display: flex;
                  gap: 4px;
                }
              }
            }

            .app-tags {
              display: flex;
              gap: 8px;
              flex-wrap: wrap;

              .code-tag {
                cursor: pointer;
                transition: all 0.2s ease;
                display: inline-flex;
                align-items: center;
                gap: 4px;

                &:hover {
                  background: #e6f7ff;
                  border-color: #91d5ff;
                  color: #1890ff;
                  transform: translateY(-1px);
                }

                .copy-icon {
                  font-size: 12px;
                }
              }
            }
          }

          .app-extended-info {
            flex: 1;
            min-width: 0;

            .app-description {
              margin-bottom: 8px;

              .description-text {
                font-size: 13px;
                color: #8c8c8c;
                line-height: 1.4;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }

            .app-extra-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 6px;

              .extra-tag {
                background: #f6f8ff;
                border-color: #d6e4ff;
                color: #1890ff;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;

      .app-status-info {
        display: flex;
        gap: 32px;
        align-items: center;

        .status-item {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 6px;

          &.status-main {
            .status-content {
              align-items: center;
            }
          }

          .status-content {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 6px;
          }

          .status-label {
            font-size: 12px;
            color: #8c8c8c;
            font-weight: 500;
            white-space: nowrap;
          }

          .status-value {
            font-size: 13px;
            color: #262626;
            font-weight: 500;
            white-space: nowrap;
          }

          .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            i {
              font-size: 12px;
            }

            .status-text {
              font-weight: 600;
            }

            &.status-enabled {
              background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
              color: #ffffff;
              box-shadow: 0 2px 12px rgba(82, 196, 26, 0.3);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 16px rgba(82, 196, 26, 0.4);
              }
            }

            &.status-disabled {
              background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
              color: #ffffff;
              box-shadow: 0 2px 12px rgba(255, 77, 79, 0.3);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 16px rgba(255, 77, 79, 0.4);
              }
            }
          }
        }
      }
    }
  }
  
  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    padding: 0 16px 16px 0;

    // 左侧导航栏 - Mac风格
    .left-sidebar {
      width: 180px;
      background: linear-gradient(180deg, #fafbfc 0%, #f5f7fa 100%);
      border-right: none;
      flex-shrink: 0;
      margin: 16px 0 16px 16px;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      backdrop-filter: blur(10px);

      .tab-navigation {
        padding: 20px 12px;

        .tab-item {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border-radius: 12px;
          position: relative;
          margin-bottom: 4px;

          i {
            font-size: 16px;
            margin-right: 12px;
            color: #8c8c8c;
            transition: all 0.3s ease;
          }

          span {
            font-size: 14px;
            font-weight: 500;
            color: #595959;
            transition: all 0.3s ease;
          }

          &:hover:not(.active) {
            background: rgba(24, 144, 255, 0.08);
            transform: translateX(2px);

            i {
              color: #1890ff;
            }

            span {
              color: #262626;
            }
          }

          &.active {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            transform: translateX(4px);

            i {
              color: #ffffff;
            }

            span {
              color: #ffffff;
              font-weight: 600;
            }

            &::before {
              content: '';
              position: absolute;
              left: -4px;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 20px;
              background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
              border-radius: 2px;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
            }
          }
        }
      }
    }

    // 右侧内容区域 - Mac风格
    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .content-wrapper {
        flex: 1;
        padding: 16px 16px 16px 16px;
        overflow-y: auto;
        background: transparent;

        .tab-content {
          height: 100%;
          display: flex;
          flex-direction: column;

          .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 8px 12px;
            background: rgba(248, 250, 252, 0.6);
            border-radius: 8px;
            border: 1px solid rgba(148, 163, 184, 0.1);

            .header-desc {
              font-size: 13px;
              color: #64748b;
              line-height: 1.4;
            }

            .el-button {
              border-radius: 6px;
              padding: 6px 12px;
              font-size: 12px;
              font-weight: 500;
              box-shadow: 0 1px 4px rgba(24, 144, 255, 0.2);
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
              }

              i {
                margin-right: 4px;
                font-size: 12px;
              }
            }
          }

          .content-body {
            flex: 1;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 24px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);



            .monitor-placeholder {
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}

// MAC风格按钮样式
.mac-style-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  &:active {
    transform: translateY(0);
  }

  &.save-config-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }
  }

  &:disabled {
    background: #e0e6ed;
    color: #8c8c8c;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;

    &:hover {
      background: #e0e6ed;
      transform: none;
      box-shadow: none;
    }
  }
}

</style>
