<template>
  <div class="chat-history">
    <div v-for="(message, index) in messages" :key="index" :class="['message-wrapper', message.type]">
      <div class="message">
        <div class="avatar">
          <img :src="message.type === 'user' ? userAvatar : aiAvatar" :alt="message.type === 'user' ? '用户' : 'AI'">
        </div>
        <div class="content">
          <div class="role">
            <span class="time">{{ formatTime(message.timestamp) }}</span>
            <div v-if="message.type === 'user'" class="resend-controls">
              <button class="resend-button" @click="handleResend(message.content)" :disabled="loading">
                <i class="el-icon-refresh"></i>
              </button>
            </div>
            <div v-if="supportsSpeech && message.type === 'ai' && message.content && !loading" class="voice-controls">
              <button class="speak-button" @click="speakText(message.content, index)"
                :disabled="isSpeaking && currentSpeakingIndex !== index">
                <i :class="['speaker-icon', { 'speaking': isSpeaking && currentSpeakingIndex === index }]"></i>
              </button>
            </div>
          </div>
          <div class="text" v-if="message.type === 'user'">{{ message.content }}</div>
          <div class="text markdown-body" v-else>
            <div v-if="loading && index === messages.length - 1" class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div v-else v-html="renderMarkdown(message.content)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import userAvatar from '../../assets/user-avatar.png'
import aiAvatar from '../../assets/ai-avatar.png'

// 配置 marked 选项
const renderer = new marked.Renderer()

// 自定义代码块渲染
renderer.code = (code) => {
  try {
    const validLanguage = code.lang || 'plaintext'
    const highlightedCode = hljs.highlight(code.text, { language: validLanguage }).value

    return `<div class="marked-code-block">
              <div class="marked-code-header">
                <span class="marked-code-language">${validLanguage}</span>
                <button class="marked-copy-button" onclick="copyCode(this)">
                  <span class="marked-copy-icon"></span>
                  <span>复制代码</span>
                </button>
              </div>
              <pre class="marked-code-pre"><code class="hljs ${validLanguage}">${highlightedCode}</code></pre>
            </div>`
  } catch (error) {
    console.error('代码高亮失败：', error)
    return `<pre><code>${code.text}</code></pre>`
  }
}

import { Message } from 'element-ui';

window.copyCode = async (button) => {
  const pre = button.closest('.marked-code-block').querySelector('pre')
  const code = pre.textContent
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(code)
    } else {
      const textarea = document.createElement('textarea')
      textarea.value = code
      textarea.style.position = 'fixed'
      textarea.style.opacity = '0'
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
    }
    Message.success('复制成功');
  } catch (err) {
    Message.error('复制失败');
  }
}

marked.setOptions({
  renderer,
  breaks: true,
  gfm: true,
  headerIds: false,
  mangle: false,
  highlight: function (code, lang) {
    try {

      const language = hljs.getLanguage(lang) ? lang : 'plaintext'
      return hljs.highlight(code, { language }).value
    } catch (error) {
      console.error('代码高亮失败：', error)
      return code
    }
  }
})

export default {
  name: 'ChatHistory',
  props: {
    messages: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      userAvatar,
      aiAvatar,
      isSpeaking: false,
      currentSpeakingIndex: null,
      speechSynthesis: window.speechSynthesis,
      speechUtterance: null,
      selectedVoice: '',
      voiceRate: 1,
      voicePitch: 1,
      supportsSpeech: false
    }
  },
  methods: {
    formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    renderMarkdown(content) {
      try {
        return marked(content || '')
      } catch (error) {
        console.error('Markdown 渲染失败：', error)
        return content
      }
    },
    updateVoiceSettings() {
      localStorage.setItem('selectedVoice', this.selectedVoice)
      localStorage.setItem('voiceRate', this.voiceRate)
    },

    speakText(text, index) {
      if (!this.supportsSpeech) {
        console.warn('浏览器不支持语音合成');
        return;
      }

      if (this.isSpeaking && this.currentSpeakingIndex === index) {
        this.stopSpeaking();
        return;
      }

      if (this.isSpeaking) {
        this.stopSpeaking();
      }

      const plainText = text.replace(/```[\s\S]*?```/g, '')
        .replace(/`.*?`/g, '')
        .replace(/\[.*?\]\(.*?\)/g, '')
        .replace(/[#*_~]/g, '')
        .trim();

      this.speechUtterance = new SpeechSynthesisUtterance(plainText);

      const voices = window.speechSynthesis.getVoices();
      const defaultVoice = voices.find(voice =>
        voice.name === 'Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)' ||
        voice.name.includes('Xiaoxiao')
      );

      if (defaultVoice) {
        this.speechUtterance.voice = defaultVoice;
      }

      this.speechUtterance.lang = 'zh-CN';
      this.speechUtterance.rate = 1;
      this.speechUtterance.pitch = 1;

      this.speechUtterance.onend = () => {
        this.isSpeaking = false;
        this.currentSpeakingIndex = null;
      };

      this.speechUtterance.onerror = (event) => {
        console.error('语音合成错误:', event);
        this.isSpeaking = false;
        this.currentSpeakingIndex = null;
      };

      this.isSpeaking = true;
      this.currentSpeakingIndex = index;
      this.speechSynthesis.speak(this.speechUtterance);
    },
    stopSpeaking() {
      if (this.speechSynthesis) {
        this.speechSynthesis.cancel();
      }
      this.isSpeaking = false;
      this.currentSpeakingIndex = null;
    },
    handleResend(content) {
      this.$emit('resend', content);
    }
  },
  beforeDestroy() {
    this.stopSpeaking();
  },
  mounted() {
    this.$nextTick(() => {
      document.querySelectorAll('pre code').forEach((block) => {
        hljs.highlightBlock(block)
      })
    })

    this.selectedVoice = localStorage.getItem('selectedVoice') || ''
    this.voiceRate = parseFloat(localStorage.getItem('voiceRate')) || 1

    this.supportsSpeech = 'speechSynthesis' in window &&
      'SpeechSynthesisUtterance' in window &&
      window.speechSynthesis.getVoices().length > 0;

    if (!this.supportsSpeech && 'speechSynthesis' in window) {
      window.speechSynthesis.onvoiceschanged = () => {
        this.supportsSpeech = window.speechSynthesis.getVoices().length > 0;
      };
    }
  },
  updated() {
    this.$nextTick(() => {
      document.querySelectorAll('pre code').forEach((block) => {
        hljs.highlightBlock(block)
      })
    })
  }
}
</script>
<style lang="css">
.marked-code-block {
  margin-top: -56px;
}

.marked-code-pre {
  margin-top: -46px;
}

.marked-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #30313c;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.marked-code-language {
  font-size: 16px;
  color: white;
}

.marked-copy-button {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #a2a2a4;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  height: 30px;
}

.marked-copy-button:hover {
  color: white;
}

.marked-copy-icon {
  width: 20px;
  height: 20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23f8f8f2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  padding-right: 10px;
}
</style>

<style scoped>
.chat-history {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.message-wrapper {
  display: flex;
  justify-content: flex-start;
  padding: 12px 16px;
  transition: background-color 0.2s ease;
}

.message-wrapper.user {
  justify-content: flex-end;
  background-color: transparent;
}

.message {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.user .message {
  flex-direction: row-reverse;
}

.avatar {
  flex-shrink: 0;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.avatar:hover {
  transform: scale(1.05);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.role {
  font-size: 13px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 4px;
  margin-bottom: 2px;
}

.user .role {
  justify-content: flex-end;
}

.text {
  font-size: 15px;
  line-height: 1.6;
  color: #1f2937;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f3f4f6;
  padding: 12px 16px;
  border-radius: 16px;
  border-top-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.user .text {
  background: #2563eb;
  color: white;
  border-radius: 16px;
  border-top-right-radius: 4px;
}

/* AI回复的样式优化 */
.markdown-body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  line-height: 1.6;
  background: #ffffff;
  padding: 14px 18px;
  border-radius: 16px;
  border-top-left-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.markdown-body pre {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 14px;
  margin: 12px 0;
  border: 1px solid #e2e8f0;
  overflow-x: auto;
}

.markdown-body code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background: #f3f4f6;
  border-radius: 4px;
}

.markdown-body pre code {
  padding: 0;
  background: transparent;
  border-radius: 0;
}

/* 添加消息气泡的小尾巴 */
.text::before {
  content: '';
  position: absolute;
  top: 0;
  width: 12px;
  height: 12px;
}

.user .text::before {
  right: -6px;
  background: radial-gradient(circle at top left, transparent 70%, #2563eb 72%);
  transform: rotate(45deg);
}

.markdown-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: -6px;
  width: 12px;
  height: 12px;
  background: radial-gradient(circle at top right, transparent 70%, #e5e7eb 72%);
  transform: rotate(-45deg);
  display: none;
}

/* 移动端适配优化 */
@media (max-width: 640px) {
  .message {
    max-width: 90%;
  }

  .message-wrapper {
    padding: 8px 12px;
  }

  .text,
  .markdown-body {
    font-size: 14px;
    padding: 10px 14px;
  }

  .markdown-body pre {
    padding: 12px;
    margin: 8px 0;
  }

  .role .time {
    font-size: 12px;
  }
}

/* 语音播放按钮样式 */
.voice-controls {
  margin-left: 8px;
}

.speak-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  opacity: 0.6;
}

.speak-button:hover:not(:disabled) {
  background: #e5e7eb;
  opacity: 1;
}

.speaker-icon {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.speaker-icon.speaking {
  animation: pulse 1s infinite;
  opacity: 1;
  color: #2563eb;
}

/* Loading 动画优化 */
.loading-dots {
  display: flex;
  gap: 4px;
  padding: 4px;
  justify-content: center;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background-color: #94a3b8;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1.0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

@media screen and (max-width: 768px) {
  .message {
    max-width: 95%;
  }

  .text,
  .markdown-body {
    padding: 10px 12px;
    font-size: 14px;
  }

  .avatar {
    width: 32px;
    height: 32px;
  }

  .markdown-body pre {
    padding: 10px;
    margin: 8px 0;
  }

  .markdown-body code {
    font-size: 12px;
  }

  .voice-controls {
    margin-left: 4px;
  }

  .speaker-icon {
    width: 16px;
    height: 16px;
  }

  /* 调整 loading 动画大小 */
  .loading-dots {
    gap: 6px;
    padding: 8px;
  }

  .loading-dots span {
    width: 8px;
    height: 8px;
  }

  /* 调整动画效果 */
  .loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
  }

  .loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
  }

  .loading-dots span:nth-child(3) {
    animation-delay: 0;
  }
}

/* 添加重发按钮样式 */
.resend-controls {
  margin-left: 8px;
}

.resend-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  opacity: 0.6;
}

.resend-button:hover:not(:disabled) {
  background: #e5e7eb;
  opacity: 1;
  transform: rotate(180deg);
}

.resend-button:disabled {
  cursor: not-allowed;
  opacity: 0.3;
}

.resend-button i {
  font-size: 16px;
  color: #4a5568;
  transition: transform 0.3s ease;
}

@media screen and (max-width: 768px) {
  .resend-button {
    padding: 6px;
  }

  .resend-button i {
    font-size: 14px;
  }
}
</style>