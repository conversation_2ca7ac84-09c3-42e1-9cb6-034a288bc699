/**
 * 多项目
 *
 * 启动单个或多个项目：npm run serve -- --project=项目名,项目名2,项目名3，访问地址：http://localhost:端口
 * 启动全部项目：npm run serve，访问地址：http://localhost:端口/项目名
 *
 * 单个打包支持lib.config.js配置 {
 *   libs: [
 *       'lib库',
 *       'lib库2',
 *       'lib库3',
 *  ]
 *}
 * 打包单个项目选择多个lib：npm run build 项目名 -- --libs=lib库,lib库2,lib库3
 * 打包单个或多个项目：npm run build 项目名
 * 打包多个项目：npm run build:all -- --project=项目名,项目名2,项目名3
 * 打包全部项目：npm run build:all
*/
const { defineConfig } = require("@vue/cli-service")
const { ProvidePlugin } = require('webpack');

const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const MonacoLocalesPlugin = require('monaco-editor-locales-plugin');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const directorys = []

try {
  let files = require('fs').readdirSync(resolve('./projects'), { withFileTypes: true })
  console.log('\n')
  console.log('projects一级非空文件夹:')
  files && files.filter(file => file.isDirectory && file.isDirectory()
    && require('fs').readdirSync(resolve(`./projects/${file.name}`)).length > 0)
    .forEach(subDir => {
      directorys.push(subDir.name)
      console.log('\t', subDir.name)
    })
  console.log('\n')
} catch (err) {
  console.error('无法读取文件夹:', err)
  throw `无法读取文件夹\n`
}


const project = require('./config/project.js')

const cmd = process.argv.slice(2)[0]

const getAdditionalData = function () {

  try {
    let files = require('glob').sync('./projects/*/module.scss.config.js')
    let scss = require(files.find(f => f.indexOf(project.name) > -1))
    return [...scss].join('')
  } catch (error) {
    return ''
  }

}


const checkFilesInDirectory = function (directoryPath, extensions) {
  try {
    // 读取目录中的文件列表
    const files = require('fs').readdirSync(directoryPath)

    // 检查目录中是否有具有指定扩展名的文件
    const hasFileWithExtension = files.some(file => {
      const fileExtension = path.extname(file).toLowerCase();
      return extensions.includes(fileExtension);
    })

    return hasFileWithExtension
  } catch (err) {
    return false
  }
}



module.exports = defineConfig({
  outputDir: process.env.NODE_ENV == 'production' ? `dist/${project.name}` : 'dist',
  publicPath: process.env.NODE_ENV == 'production' ? `/${project.name}` : '/',
  pages: project.pages(),
  lintOnSave: false,
  productionSourceMap: false,
  transpileDependencies: [/jyjh-common/],
  // transpileDependencies: true,
  devServer: {
    host: '0.0.0.0',
    port: 8081,
    open: true,
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    client: {
      //关闭错误提示页
      overlay: false
    },
    proxy: require('./proxy.config.js'),
  },

  configureWebpack: {
    entry: {
      'editor.worker': 'monaco-editor/esm/vs/editor/editor.worker.js'
    },

    resolve: {
      alias: Object.assign({
        '@': resolve('projects/basics'),
      }, directorys.reduce((acc, curr) => {
        // acc[`@${curr}`] = resolve(`projects/${curr}`)
        // acc[`@${curr}`] = (cmd !== 'build' || curr === project.name) ? resolve(`projects/${curr}`) : project.projectLibs.find(f => f === curr) !== undefined ? resolve(`projects/${curr}`) : false
        acc[`@${curr}`] = curr === project.name ? resolve(`projects/${curr}`) : project.projectLibs.find(f => f === curr) !== undefined ? resolve(`projects/${curr}`) : false
        return acc
      }, {})),

      fallback: {
        path: require.resolve("path-browserify")
      }
    },

    plugins: [
      // new BundleAnalyzerPlugin(),
      new MonacoWebpackPlugin({
        languages: ["json", "xml", "yaml", "python", 'java'],
        features: ["coreCommands", "find", "format", "folding", 'smartSelect', 'snippets', 'suggest', 'hover']
      }),
      new MonacoLocalesPlugin({
        //设置支持的语言
        languages: ["es", "zh-cn"],
        //默认语言
        defaultLanguage: "zh-cn",
        //打印不匹配的文本
        logUnmatched: false,
        //自定义文本翻译
        mapLanguages: { "zh-cn": { "Peek References": "查找引用", "Go to Symbol...": "跳到变量位置", "Command Palette": "命令面板" } }
      }),
      new ProvidePlugin({
        plugin_loadView: resolve('./loadview.js'),
        plugin_filefilter: [resolve('config/filefilter.js'), 'default'],
        CONSTANT: [resolve('config/global.js'), 'default']
      }),
      {
        apply(compiler) {
          compiler.hooks.beforeCompile.tap('CreateFilePlugin', () => {
            const prentDir = path.resolve(__dirname, './projects')
            if (project.name) {
              const str = [project.name, ...project.projectLibs].map(m => `if (view.startsWith('${m}/views/')) {
        return import(\`@${m}/views/\${view.replace('${m}/views/', '') }\`)
    }`).join('\n\n\t\t')
              const fileContent = `export default function (view) {

                ${str}
                
    return null
}`
              const filePath = path.resolve(prentDir, `./${project.name}/loadview.config.js`);
              const fs = require('fs')
              if (!fs.existsSync(filePath)) {
                fs.writeFileSync(filePath, fileContent, 'utf8');
              }
            }

          })

          compiler.hooks.environment.tap('CreateFilePlugin', () => {
            try {
              const template = require("./tsconfig.js")
              const dirs = ['basics', project.name, ...project.projectLibs].filter(f => checkFilesInDirectory(resolve(`projects/${f}`), ['.ts', '.tsx', '.vue']))

              const include = dirs.filter(f => f)
                .map(m => [`projects/${m}/**/*.ts`, `projects/${m}/**/*.tsx`, `projects/${m}/**/*.vue`])
              template.include = require('lodash').flatten(include)

              template.compilerOptions.paths = Object.assign({
                "@/*": [
                  "projects/basics/*"
                ]
              }, dirs.reduce((acc, curr) => {
                acc[`@${curr}/*`] = [`projects/${curr}/*`]
                return acc
              }, {}))

              const fPath = resolve('./tsconfig.json')

              const fs = require('fs')
              fs.writeFileSync(fPath, JSON.stringify(template), 'utf8')
            } catch (error) {
              console.log('tsconfig.json生成失败', error)
            }
          })
        }
      }
    ]

    //禁用旧版本浏览器的兼容代码app-legacy.js
    // output: {
    //   filename: 'js/[name].js',
    //   chunkFilename: 'js/[name].js',
    // },
  },


  chainWebpack: config => {
    config.plugin('copy').use(require('copy-webpack-plugin'), [
      {
        patterns: [
          {
            from: resolve('public'),
            to: resolve('dist')
          },
          ...directorys.filter(name => name === project.name && require('fs').existsSync(resolve(`projects/${name}/public`)))
            .map(name => {
              return {
                from: resolve(`projects/${name}/public`),
                to: resolve(`dist/${name}`)
              }
            })
        ]
      }
    ]);

    config.plugin('define').tap((args) => {
      args[0]['process.env']['SERVE_ALL'] = !project.name && process.argv[2] == 'serve'
      args[0]['process.env']['PROJECTS_DIR'] = JSON.stringify(directorys)
      args[0]['process.env']['PROJECT_NAME'] = JSON.stringify(project.name)
      args[0]['process.env']['PROJECT_LIBS'] = JSON.stringify(project.projectLibs)
      return args;
    });

    config.optimization.minimizer('terser').tap(args => {
      // args[0].terserOptions.compress.drop_debugger = false;
      // args[0].terserOptions.compress.drop_console = true;
      args[0].terserOptions.compress = {
        drop_debugger: false,
        // drop_console: true,
        pure_funcs: ['console.log', 'console.debug', 'console.info']
      }
      return args;
    });

    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .merge(directorys.map(dir => resolve(`projects/${dir}/icons`)))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .merge(directorys.map(dir => resolve(`projects/${dir}/icons`)))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()


    config.module.rule('path')
      .include
      .add(resolve("src"))
      .add(resolve("projects"))
      .end()
      .exclude.add(resolve("node_modules"))
      .end()
      .test(/\.(css|less|scss|sass|js|vue)$/)
      .use('root-path-loader')
      .loader('root-path-loader')
      .options({
        patterns: directorys.map(dir => {
          return { root: `/@${dir}/`, newRoot: `/${dir}/` }
        })

      })
      .end()


    config.module.rule('vw-vh')
      .include.add(resolve("src"))
      .add(resolve("projects"))
      .end()
      .exclude.add(resolve("node_modules"))
      .end()
      .test(/\.(vue)$/)
      .use('style-vw-vh-loader')
      .loader('style-vw-vh-loader')
      .options({ matchTemplate: true, viewport: 1920, unit: 'vw', fixed: 5 })
      .tap((options) => {
        try {
          let files = require('glob').sync('./projects/*/postcss-px-to-plugin.config.js')
          let pxToConfig = require(files.find(f => f.indexOf(project.name) > -1))
          if (pxToConfig.toViewport === false) {
            options.enabled = false
          } else {
            options.enabled = true
            options.unit = pxToConfig.toViewportConfig?.viewportUnit || 'vw'
            options.viewport = pxToConfig.toViewportConfig.viewportWidth || 1920
            options.propList = pxToConfig.toViewportConfig.propList || ['*']
          }

        } catch (error) {
        }
        return options
      })
      .end()

    config.module.rule('rem')
      .include.add(resolve("src"))
      .add(resolve("projects"))
      .end()
      .exclude.add(resolve("node_modules"))
      .end()
      .test(/\.(vue)$/)
      .use('style-rem-loader')
      .loader('style-rem-loader')
      .options({ matchTemplate: true, remUnit: 19.2, remFixed: 5, enabled: false })
      .tap((options) => {
        try {
          let files = require('glob').sync('./projects/*/postcss-px-to-plugin.config.js')
          let pxToConfig = require(files.find(f => f.indexOf(project.name) > -1))
          if (pxToConfig.toRem === true) {
            options.enabled = true
            options.remUnit = pxToConfig.toRemConfig.rootValue || 19.2
            options.propList = pxToConfig.toRemConfig.propList || ['*']
          } else {
            options.enabled = false
          }

        } catch (error) {
        }
        return options
      })
      .end()

    // 移除 prefetch 插件
    config.plugins.delete('prefetch-index')
    // 移除 preload 插件，避免加载多余的资源
    config.plugins.delete('preload-index')


    config.optimization.splitChunks({
      chunks: 'all',
      maxSize: 800 * 1024,
      cacheGroups: {
        libs: {
          test: /[\\/]node_modules[\\/]/,
          priority: 1,
          chunks: 'initial',
          name(module) {
            return module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)?.[1];
          }
        },
        echarts: {
          name: 'chunk-echarts',
          test: /[\\/]node_modules[\\/]_?echarts(.*)/,
          priority: 20,
        },
        highcharts: {
          name: 'chunk-highcharts',
          test: /[\\/]node_modules[\\/]_?highcharts(.*)/,
          priority: 20,
        },
        elementUI: {
          name: 'chunk-elementUI',
          test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
          priority: 20
        },
        threejs: {
          name: 'chunk-threejs',
          test: /[\\/]node_modules[\\/]_?three(.*)/,
          priority: 20,
        },
        // common: {
        //   name: 'chunk-common',
        //   test: resolve('src/components'),
        //   minChunks: 3,
        //   priority: 5,
        //   reuseExistingChunk: true
        // }
      }
    })

  },

  css: {
    loaderOptions: {
      css: {
        url: { filter: url => url[0] !== '/' }
      },
      //     sass: {
      //       implementation: require('sass'),
      //     },
      scss: {
        //不支持直接嵌套导入其他文件或样式表。它只能用于简单的字符串或变量
        // additionalData: `@import "@bpm-oa-web/assets/theme.module.scss";`
        additionalData: getAdditionalData()
      }
    }
  }
})
