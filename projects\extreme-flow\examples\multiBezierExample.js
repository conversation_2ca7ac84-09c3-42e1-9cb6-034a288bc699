/**
 * MultiBezierEdge 使用示例
 * 展示如何使用N次贝塞尔曲线组件的各种功能
 */

// 基础使用示例
export const basicExample = {
  // 创建一个5次贝塞尔曲线连接
  edges: [
    {
      id: 'edge1',
      type: 'multiBezier',
      sourceNodeId: 'node1',
      targetNodeId: 'node2',
      properties: {
        degree: 5,                    // 5次贝塞尔曲线
        useAdvancedAlgorithm: true,   // 使用De Casteljau算法
        smoothness: 100,              // 渲染平滑度
        stroke: '#1890ff',           // 线条颜色
        strokeWidth: 2,              // 线条宽度
        strokeDasharray: '5,5'       // 虚线样式
      }
    }
  ]
}

// 高级功能示例
export const advancedExample = {
  // 创建多条不同次数的贝塞尔曲线
  edges: [
    {
      id: 'edge1',
      type: 'multiBezier',
      sourceNodeId: 'node1',
      targetNodeId: 'node2',
      properties: {
        degree: 3,
        useAdvancedAlgorithm: false,
        stroke: '#ff4d4f'
      }
    },
    {
      id: 'edge2', 
      type: 'multiBezier',
      sourceNodeId: 'node2',
      targetNodeId: 'node3',
      properties: {
        degree: 7,
        useAdvancedAlgorithm: true,
        smoothness: 200,
        stroke: '#52c41a'
      }
    },
    {
      id: 'edge3',
      type: 'multiBezier', 
      sourceNodeId: 'node3',
      targetNodeId: 'node4',
      properties: {
        degree: 10,
        useAdvancedAlgorithm: true,
        smoothness: 50,
        stroke: '#722ed1'
      }
    }
  ]
}

// 动态操作示例
export function dynamicOperationExample(lf) {
  // 获取边模型
  const edgeModel = lf.getEdgeModelById('edge1')
  
  if (edgeModel && edgeModel.type === 'multiBezier') {
    // 1. 动态调整曲线度数
    console.log('调整为8次贝塞尔曲线')
    edgeModel.setDegree(8)
    
    // 2. 优化控制点位置
    console.log('优化控制点位置')
    edgeModel.optimizeControlPoints()
    
    // 3. 添加新的控制点
    console.log('添加控制点')
    edgeModel.addControlPoint(300, 200)
    
    // 4. 切换算法
    console.log('切换到高级算法')
    edgeModel.toggleAlgorithm()
    
    // 5. 调整平滑度
    console.log('设置平滑度为150')
    edgeModel.setSmoothness(150)
    
    // 6. 获取曲线信息
    const length = edgeModel.getCurveLength()
    const boundingBox = edgeModel.getBoundingBox()
    console.log(`曲线长度: ${length.toFixed(2)}px`)
    console.log('边界框:', boundingBox)
    
    // 7. 检测自相交
    const intersections = edgeModel.detectSelfIntersection()
    if (intersections.length > 0) {
      console.log('检测到自相交点:', intersections)
    }
    
    // 8. 导出曲线数据
    const curveData = edgeModel.exportCurveData()
    console.log('曲线数据:', curveData)
    
    // 9. 获取调试信息
    const debugInfo = edgeModel.getDebugInfo()
    console.log('调试信息:', debugInfo)
  }
}

// 性能测试示例
export function performanceTestExample(lf) {
  console.log('开始性能测试...')
  
  const edgeModel = lf.getEdgeModelById('edge1')
  if (!edgeModel) return
  
  // 测试不同算法的性能
  const testCases = [
    { algorithm: false, degree: 5, name: '标准算法-5次' },
    { algorithm: true, degree: 5, name: 'De Casteljau算法-5次' },
    { algorithm: false, degree: 10, name: '标准算法-10次' },
    { algorithm: true, degree: 10, name: 'De Casteljau算法-10次' }
  ]
  
  testCases.forEach(testCase => {
    edgeModel.useAdvancedAlgorithm = testCase.algorithm
    edgeModel.setDegree(testCase.degree)
    
    const startTime = performance.now()
    
    // 执行1000次计算
    for (let i = 0; i < 1000; i++) {
      const t = i / 1000
      edgeModel.safeGetBezierPoint(t)
    }
    
    const endTime = performance.now()
    console.log(`${testCase.name}: ${(endTime - startTime).toFixed(2)}ms`)
  })
}

// 交互功能示例
export function interactionExample(lf) {
  // 监听边的选中事件
  lf.on('edge:click', ({ data }) => {
    if (data.type === 'multiBezier') {
      console.log('点击了多次贝塞尔曲线:', data)
      
      // 获取边模型
      const edgeModel = lf.getEdgeModelById(data.id)
      
      // 显示控制点（通过选中状态）
      lf.selectElementById(data.id)
      
      // 显示曲线信息
      const info = {
        度数: edgeModel.degree,
        控制点数量: edgeModel.controlPoints.length,
        算法: edgeModel.useAdvancedAlgorithm ? 'De Casteljau' : '标准',
        长度: edgeModel.getCurveLength().toFixed(2) + 'px'
      }
      
      console.table(info)
    }
  })
  
  // 监听边的双击事件 - 优化曲线
  lf.on('edge:dblclick', ({ data }) => {
    if (data.type === 'multiBezier') {
      const edgeModel = lf.getEdgeModelById(data.id)
      edgeModel.optimizeControlPoints()
      console.log('已优化曲线控制点')
    }
  })
  
  // 监听右键菜单 - 添加自定义操作
  lf.extension.menu.addMenuConfig({
    edgeMenu: [
      {
        text: '增加控制点',
        callback: (edge) => {
          if (edge.type === 'multiBezier') {
            const edgeModel = lf.getEdgeModelById(edge.id)
            const currentDegree = edgeModel.degree
            if (currentDegree < 10) {
              edgeModel.setDegree(currentDegree + 1)
              console.log(`已增加控制点，当前度数: ${currentDegree + 1}`)
            }
          }
        }
      },
      {
        text: '减少控制点',
        callback: (edge) => {
          if (edge.type === 'multiBezier') {
            const edgeModel = lf.getEdgeModelById(edge.id)
            const currentDegree = edgeModel.degree
            if (currentDegree > 2) {
              edgeModel.setDegree(currentDegree - 1)
              console.log(`已减少控制点，当前度数: ${currentDegree - 1}`)
            }
          }
        }
      },

      {
        text: '平滑曲线',
        callback: (edge) => {
          if (edge.type === 'multiBezier') {
            const edgeModel = lf.getEdgeModelById(edge.id)
            edgeModel.smoothCurve()
            console.log('已平滑曲线')
          }
        }
      }
    ]
  })
}

// 批量操作示例
export function batchOperationExample(lf) {
  // 获取所有多次贝塞尔曲线
  const allEdges = lf.getGraphRawData().edges
  const multiBezierEdges = allEdges.filter(edge => edge.type === 'multiBezier')
  
  console.log(`找到 ${multiBezierEdges.length} 条多次贝塞尔曲线`)
  
  // 批量优化所有曲线
  multiBezierEdges.forEach(edge => {
    const edgeModel = lf.getEdgeModelById(edge.id)
    if (edgeModel) {
      // 自适应调整控制点数量
      edgeModel.adaptControlPoints(5)
      // 优化控制点位置
      edgeModel.optimizeControlPoints()
      // 平滑曲线
      edgeModel.smoothCurve()
    }
  })
  
  console.log('批量优化完成')
}

// 数据导入导出示例
export function dataImportExportExample(lf) {
  const edgeModel = lf.getEdgeModelById('edge1')
  if (!edgeModel || edgeModel.type !== 'multiBezier') return
  
  // 导出曲线数据
  const curveData = edgeModel.exportCurveData()
  console.log('导出的曲线数据:', curveData)
  
  // 保存到本地存储
  localStorage.setItem('savedCurve', JSON.stringify(curveData))
  
  // 从本地存储读取
  const savedData = localStorage.getItem('savedCurve')
  if (savedData) {
    const parsedData = JSON.parse(savedData)
    
    // 创建新的边并导入数据
    const newEdge = {
      id: 'imported_edge',
      type: 'multiBezier',
      sourceNodeId: 'node1',
      targetNodeId: 'node3'
    }
    
    lf.addEdge(newEdge)
    const newEdgeModel = lf.getEdgeModelById('imported_edge')
    newEdgeModel.importCurveData(parsedData)
    
    console.log('已导入曲线数据')
  }
}

// 使用说明
export const usage = `
MultiBezierEdge 使用指南:

1. 基础使用:
   - 设置 type: 'multiBezier'
   - 通过 properties.degree 设置曲线度数 (2-10)
   - 通过 properties.useAdvancedAlgorithm 选择算法

2. 高级功能:
   - 动态调整控制点: setDegree(), addControlPoint(), removeControlPoint()
   - 曲线优化: optimizeControlPoints(), smoothCurve()
   - 性能监控: getDebugInfo(), measurePerformance()
   - 数据导入导出: exportCurveData(), importCurveData()

3. 交互功能:
   - 选中边时显示控制点和控制线
   - 拖拽控制点实时调整曲线形状
   - 右键菜单提供快捷操作

4. 性能优化:
   - 内置缓存机制减少重复计算
   - 支持批量更新控制点
   - 提供性能监控和警告

5. 算法支持:
   - 标准贝塞尔算法: 计算速度快
   - De Casteljau算法: 数值稳定性好，支持曲线分割

更多详细信息请查看源码注释。
`

console.log(usage)