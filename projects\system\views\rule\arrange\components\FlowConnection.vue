<template>
  <svg class="flow-connection">
    <path
        :d="pathData"
        :stroke="color"
        :stroke-width="strokeWidth"
        fill="none"
        :class="{'connection-selected': selected}"
    ></path>
    <path
        :d="pathData"
        stroke="transparent"
        stroke-width="10"
        fill="none"
        @click="handleClick"
        class="connection-hitbox"
    ></path>
    <polygon
        :points="arrowPoints"
        :fill="color"
        :transform="arrowTransform"
    ></polygon>
  </svg>
</template>

<script>
export default {
  name: 'FlowConnection',

  props: {
    source: {
      type: Object,
      required: true
    },
    target: {
      type: Object,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: '#409eff'
    },
    strokeWidth: {
      type: Number,
      default: 2
    }
  },

  computed: {
    sourcePosition() {
      return {
        x: this.source.position.x + 180, // 节点宽度
        y: this.source.position.y + 40   // 节点高度的一半
      }
    },

    targetPosition() {
      return {
        x: this.target.position.x,
        y: this.target.position.y + 40   // 节点高度的一半
      }
    },

    pathData() {
      const { x: x1, y: y1 } = this.sourcePosition
      const { x: x2, y: y2 } = this.targetPosition

      const distance = Math.abs(x2 - x1)
      const dx = Math.min(distance * 0.5, 150)

      const controlPoint1 = { x: x1 + dx, y: y1 }
      const controlPoint2 = { x: x2 - dx, y: y2 }

      return `M ${x1} ${y1} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${x2} ${y2}`
    },

    arrowPoints() {
      return '0,-5 10,0 0,5'
    },

    arrowTransform() {
      const { x: x2, y: y2 } = this.targetPosition
      const dx = this.targetPosition.x - this.sourcePosition.x
      const dy = this.targetPosition.y - this.sourcePosition.y
      const angle = Math.atan2(dy, dx) * 180 / Math.PI

      return `translate(${x2}, ${y2}) rotate(${angle})`
    }
  },

  methods: {
    handleClick() {
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-connection {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  path {
    transition: stroke-width 0.2s;

    &.connection-selected {
      stroke-width: 3;
    }

    &.connection-hitbox {
      pointer-events: auto;
      cursor: pointer;
      opacity: 0;
    }
  }
}
</style>