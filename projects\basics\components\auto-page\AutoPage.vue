<template>
    <div class="auto_page">
        <div class="search" v-if="showSearch">
            <form-create v-model="fApiSearch" :rule="searhList" :option="formOptionSearch"></form-create>
            <div class="searchB">
                <el-button type="primary" icon="el-icon-search" size="large" @click="handleQuery">搜索</el-button>
                <el-button v-if="showResetBtn" icon="el-icon-refresh" size="large" @click="resetQuery">重置</el-button>
            </div>
        </div>

        <el-row :gutter="10" class="mb8">
            <el-col v-hasPermi="[pers + ':post']" :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col v-hasPermi="[pers + ':delete']" :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col v-hasPermi="[pers + ':exports']" :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
            </el-col>
            <el-col v-hasPermi="[pers + ':imports']" :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini"
                    @click="handleExportTemplate">导入模板</el-button>
            </el-col>
            <el-col v-hasPermi="[pers + ':imports']" :span="1.5" style="overflow: hidden; position: relative;">
                <el-upload class="upload_file" accept=".xls,.xlsx" :action="baseUrl" :on-change="handleChange"
                    :file-list="uploadfileList" :on-success="handleSuccess">
                    <el-button type="warning" plain icon="el-icon-download" size="mini">导入</el-button>
                </el-upload>
            </el-col>
        </el-row>

        <el-table :data="tableData" @selection-change="handleSelectionChange" :stripe="tableOption.stripe || false"
            :border="tableOption.border || false" :fit="tableOption.fit || true"
            :show-header="tableOption.showHeader || true" row-key="newsId" lazy :load="loadTable"
            @row-click="clickTableRow" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            v-if="tableColumnList && tableColumnList.length">
            <el-table-column type="selection" align="center" />
            <el-table-column v-for="(its, ind) in tableColumnList" :key="ind" :label="its.title"
                :align="ind == 0 && isHasChildren ? 'left' : 'center'" :prop="its.field">
                <template slot-scope="scope">
                    <div v-if="its.isScope">
                        <slot :name="'table' + its.field" :row="scope.row"></slot>
                    </div>
                    <div v-else-if="its.isSvgIcon" style="display: flex;align-items: center;color: currentColor">
                        <svg-icon :icon-class="scope.row[its.field]" style="height: 30px;width: 16px;" />
                        <span v-text="scope.row[its.field]" style="font-size: 16px;padding-left: 5px;"></span>
                    </div>
                    <div v-else-if="its.options"
                        v-html="getOptionsLabel(its.options, scope.row[its.field], its.props.labelName, its.props.valueName)">
                    </div>
                    <div v-else-if="its.type == 'switch'" v-html="scope.row[its.field] ? '是' : '否'"></div>
                    <div v-else v-html="scope.row[its.field]"></div>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="240">
                <template slot-scope="scope">
                    <el-button v-if="isShowHistoryBtn" size="mini" type="text"
                        @click="handleDealHistory(scope.row)">审批</el-button>
                    <el-button v-if="isShowHistoryBtn" size="mini" type="text"
                        @click="handleHistory(scope.row)">审批记录</el-button>
                    <el-button size="mini" type="text" :icon="hasUpdate ? 'el-icon-edit' : 'el-icon-view'"
                        @click="handleUpdateRow(scope.row)">
                        {{ hasUpdate ? '修改' : '查看' }}
                    </el-button>
                    <el-button v-hasPermi="[pers + ':delete']" size="mini" type="text" icon="el-icon-delete"
                        @click="handleDeleteRow(scope.row)">删除</el-button>
                    <slot name="tablesss" :row="scope.row"></slot>
                    <el-button v-for="(ib, ibIndex)  in tableButton" :key="ibIndex" v-hasPermi="ib.permi" size="mini"
                        type="text" :icon="ib.icon" @click="handleToRoute(scope.row, ib)">{{ ib.label }}</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div v-if="pages > 1" class="pagin">
            <el-pagination :current-page.sync="currentPage" :page-size.sync="pageSize" 
                layout="total, prev, pager, next, jumper" :total="pageTotal" @current-change="handleCurrentChange" />
        </div>

        <el-drawer :visible.sync="showDrawerHistory" append-to-body :with-header="drawer.withHeader || true"
            :direction="drawer.direction || 'rtl'" :size="drawer.size || '30%'">
            <div class="drawer_history" style="padding-top: 10px;">
                <div v-if="hisType">
                    <el-button type="success" plain size="mini"
                        @click="showConfirmHistory = true, historyObj.content = null, historyObj.isApprovalPass = true">同意</el-button>
                    <el-button type="danger" plain size="mini"
                        @click="showConfirmHistory = true, historyObj.content = null, historyObj.isApprovalPass = false">拒绝</el-button>
                    <FormInfo :objRule="objRule"></FormInfo>
                </div>

                <TimeLineHistory :historyList="historyList"></TimeLineHistory>
            </div>
        </el-drawer>

        <el-dialog :visible.sync="showConfirmHistory" title="审批意见" width="30%" center>
            <el-input type="textarea" :rows="5" placeholder="请输入意见" v-model="historyObj.content">
            </el-input>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showConfirmHistory = false">取消</el-button>
                    <el-button type="primary" @click="confirmHistory">确认</el-button>
                </span>
            </template>
        </el-dialog>

        <el-drawer :visible.sync="showDrawer" append-to-body :with-header="drawer.withHeader || true"
            :direction="drawer.direction || 'rtl'" :size="drawer.size || '30%'" :wrapperClosable="false">
            <form-create v-if="!drawer.isScope" v-model="fApi" :rule="formRuleDrawerNes" :option="formOption"
                @change="onFormChange"></form-create>
            <div v-if="drawer.isScope">
                <slot name="el_drawer"></slot>
            </div>
        </el-drawer>

        <el-dialog :visible.sync="showConfirm" title="提示" width="30%" center>
            <span>是否确认删除{{ dialogCon }}</span>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showConfirm = false">取消</el-button>
                    <el-button type="primary" @click="showConfirm = false, deleteRows()">确认</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { list, save, update, remove, exports, importsTemplate, treeList, history, action, approval } from './api-sample'
import TimeLineHistory from './TimeLineHistory.vue'
import FormInfo from './FormInfo.vue'
export default {
    name: 'AutoPage',
    components: { TimeLineHistory, FormInfo },
    props: {
        defaultQuery: {
            type: Object,
            default: function name(params) {
                return {}
            }
        },
        options: {
            type: Object,
            default: function name(params) {
                return {}
            }
        },
        pageSize: {
            type: Number,
            default: 10
        },
        formRule: {
            type: Array,
            default: function name(params) {
                return []
            }
        },
        drawer: {
            type: Object,
            default: function name(params) {
                return {
                    direction: 'rtl',
                    size: '30%',
                    withHeader: true,
                    isScope: false,
                }
            }
        },
        collectionName: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            tableData: [],
            fApiSearch: {},
            formOptionSearch: { resetBtn: false, submitBtn: false },

            searchQuery: {},
            currentPage: 1,
            pageTotal: 1,

            tableCheckedRows: [],
            multiple: true,

            showDrawer: false,
            showConfirm: false,
            dialogCon: '',
            drawerType: '',
            editRows: [],
            deleRows: [],

            drawerTitle: '',
            alertBox: {},


            fApi: {},
            formOption: {
                // 显示重置表单按扭
                resetBtn: false,

                // 表单提交按扭事件
                onSubmit: formData => {
                    this.getFormModeValue(formData)
                }
            },

            formRuleDrawerNes: [],
            formRuleCo: null,

            uploadfileList: [],
            baseUrl: '',

            showDrawerHistory: false,
            historyList: [],
            hisType: null,

            showConfirmHistory: false,
            historyObj: {
                isApprovalPass: false,
                content: null,
                processInstanceId: null,
            },
            isShowHistoryBtn: false,

            objRule: {},
        }
    },
    computed: {
        tableButton() {
            if (this.options && this.options.table && this.options.table.button) {
                return this.options.table.button
            } else {
                return []
            }
        },
        newRequest() {
            if (this.options && this.options.newRequest) {
                return this.options.newRequest
            } else {
                return ''
            }
        },
        isHasChildren() {
            if (this.options && this.options.table && this.options.table.isHasChildren) {
                return true
            } else {
                return false
            }
        },
        pers() {
            var api = this.options.api
            const formdata = 'form/data/'
            if (api.startsWith(formdata)) {
                return api.replace(formdata, '').replaceAll('_', ':')
            }
            return api.replaceAll('/', ':')
        },
        hasUpdate() {
            return this.hasPermi([this.pers + ':put'])
        },
        pages() {
            return Math.ceil(this.pageTotal / this.pageSize)
        },
        showSearch() {
            if (this.options && this.options.search && this.options.search.isShow && this.searhList.length) {
                return true
            } else {
                return false
            }
        },
        searhList() {
            if (this.formRuleCo && this.formRuleCo.length) {
                let ars = this.formRuleCo.filter(i => typeof i?.props?.isSearch === 'boolean' ? i.props.isSearch : i.isSearch)
                let fins = this.deepClone2(ars)
                fins = fins.map(is => {
                    if (is.isSearchValidate) {
                        is.validate = is.isSearchValidate
                    }
                    if (is.isSearchCol) {
                        is.col = is.isSearchCol
                    }
                    if (is.isSearchProps) {
                        is.props = is.isSearchProps
                    }
                    return is
                })
                return fins
            } else {
                return []
            }
        },
        formRuleDrawer: {

            set(value) { },

            get() {
                if (this.formRuleCo && this.formRuleCo.length) {
                    let fins = this.deepClone2(this.formRuleCo)
                    let ars = fins.filter(i => typeof i?.props?.isHidden === 'boolean' ? !i.props.isHidden : !i.isHidden)
                    ars.filter(is => is.type == 'json' || is.type == 'uploader' || is.type == 'svgIcon' || is.type == 'gmap' || is.type == 'glist').map(i => {
                        if (i.props) {
                            i.props.value = i.value
                        } else {
                            i.props = {
                                value: i.value
                            }
                        }
                        return i
                    })
                    ars.filter(is => is.type == 'treeSelect' || is.type == 'cascader2').map(i => {
                        if (i.props) {
                            i.props.value = i.value
                            i.props.options = i.options
                        } else {
                            i.props = {
                                value: i.value,
                                options: i.options
                            }
                        }
                        return i
                    })
                    return ars
                } else {
                    return []
                }
            }
        },
        tableColumnList() {
            if (this.formRuleCo && this.formRuleCo.length) {
                let ars = this.formRuleCo.filter(i => typeof i?.props?.isTable === 'boolean' ? i.props.isTable : i.isTable)
                return ars
            } else {
                return []
            }
        },
        showResetBtn() {
            if (this.options && this.options.search && this.options.search.showReset) {
                return true
            } else {
                return false
            }
        },
        showAddBtn() {
            if (this.options && this.options.showAddBtn) {
                return true
            } else {
                return false
            }
        },
        showDeleteBtn() {
            if (this.options && this.options.showDeleteBtn) {
                return true
            } else {
                return false
            }
        },
        showExportBtn() {
            if (this.options && this.options.showExportBtn) {
                return true
            } else {
                return false
            }
        },
        tableOption() {
            if (this.options && this.options.table) {
                return this.options.table
            } else {
                return {}
            }
        }
    },
    watch: {
        formRule: {
            handler(val) {
                let treeList = []
                let as = this.formRule.map((i, ind) => {
                    let d_obj = {
                        type: "input",
                        isTable: true,  //默认显示table
                        isSearchValidate: [],
                        isSearchCol: {
                            md: { span: 6 }
                        },
                        props: {
                            placeholder: `请${i.type == 'input' ? '输入' : '选择'}`,
                            disabled: false,
                            readonly: false,
                            clearable: true
                        },
                        validate: [
                            {
                                required: true,
                                message: `${i.title ? i.title : ''}不能为空`
                            }
                        ],
                        col: {
                            md: { span: 24 }
                        }
                    }

                    let is = Object.assign(d_obj, i)
                    if (!i.validate) {
                        is.validate = []
                    }

                    if (i.type == 'treeSelect' && i.props.action) {
                        treeList.push({ url: i.props.action, ind: ind })
                    }
                    return is
                })

                this.getTreeListOption(treeList, as)
            },
            deep: true,
            immediate: true
        },
        tableData: {
            handler(val) {
                this.tableCheckedRows = []
            },
            deep: true
        },
        searhList: {
            handler(val) {
                let obj = {}
                if (this.searhList && this.searhList.length) {
                    var itsArr = this.searhList.map(i => i.field)
                    itsArr.forEach(element => {
                        obj[element] = this.getListValue(this.searhList, element)
                    })
                }
                this.searchQuery = obj
                this.getList()
            },
            immediate: true,
            deep: true
        },
        tableCheckedRows: {
            handler(val) {
                if (val.length) {
                    this.multiple = false
                } else {
                    this.multiple = true
                }
            },
            deep: true
        },
        collectionName(val) {
            if (this.collectionName) {
                this.getAproval()
            }
        }
    },
    mounted() {
        this.baseUrl = (this.newRequest || CONSTANT.SYSTEM) + '/' + this.options.api + '/import'
        if (this.collectionName) {
            this.getAproval()
        }
    },
    methods: {
        getAproval() {
            approval({ formName: this.collectionName }, this.newRequest).then(res => {
                if (res && res.pluginConfig && res.pluginConfig.approval) {
                    this.isShowHistoryBtn = true
                } else {
                    this.isShowHistoryBtn = false
                }
            })
        },
        toAction() {
            let obj = this.historyObj
            action(obj, this.newRequest, this.collectionName).then(res => {
                if (res) {
                    this.$message({
                        message: '操作成功',
                        type: 'success'
                    })
                    this.showConfirmHistory = false
                    this.showDrawerHistory = false
                    this.getList()
                }
            })
        },
        confirmHistory() {
            if (this.historyObj.content) {
                this.toAction()
            } else {
                this.$message({
                    message: '请输入审批意见',
                    type: 'warning'
                })
            }
        },
        handleHistory(row) {
            this.hisType = null
            this.historyObj.processInstanceId = null
            this.getHistoryList(row.id)
        },
        handleDealHistory(row) {
            this.setFormValue(row)
            let formObj = this.deepClone2(this.formRuleDrawer)
            this.objRule = formObj.map(i => {
                if (i.props) {
                    i.props.disabled = true
                } else {
                    i.props = {
                        disabled: true
                    }
                }
                return i
            })

            this.hisType = true
            this.historyObj.processInstanceId = row.id
            this.getHistoryList(row.id)
        },
        getHistoryList(dataId) {
            let obj = {
                current: 1,
                size: -1,
                process_instance_id: dataId
            }
            history(obj, this.newRequest).then(res => {
                if (this.hisType) {
                    this.historyList = res.records
                    this.showDrawerHistory = true
                } else {
                    if (res && res.records.length) {
                        this.historyList = res.records
                        this.showDrawerHistory = true
                    } else {
                        this.$message({
                            message: '暂无审批记录'
                        })
                    }
                }
            })
        },
        async getTreeListOption(treeList, as) {
            let ass = as
            for (let is = 0; is < treeList.length; is++) {
                ass[treeList[is].ind].options = await this.getTreeList(treeList[is].url)
                if (ass[treeList[is].ind].props) {
                    ass[treeList[is].ind].props.options = ass[treeList[is].ind].options
                } else {
                    ass[treeList[is].ind].props = {
                        options: ass[treeList[is].ind].options
                    }
                }
            }
            this.formRuleCo = ass
        },
        getTreeList(url) {
            let obj = {
                current: 1,
                size: -1
            }
            return new Promise(((resolve, reject) => {
                treeList(url, obj).then(res => {
                    let resu = []
                    if (res && typeof res == 'object') {
                        if (typeof res.length == 'number') {
                            resu = res
                        } else {
                            resu = res.records
                        }
                    } else {
                        resu = []
                    }
                    resolve(resu)
                })
            }))
        },
        handleToRoute(row, cur) {
            let re = /#.*/g
            let path = '/'
            if (cur.route.split('?').length > 1) {
                path = cur.route.split('?')[0] + '?' + cur.route.split('?')[1].split('&').map(is => {
                    return is.replace(re, row[is.match(re)[0].replace('#', '')])
                }).join('&')
            } else {
                path = cur
            }

            this.$router.push({ path })
        },
        handleSuccess() {
            this.uploadfileList = []
            this.getList()
        },
        handleChange(file, fileList) {
            //清空上传列表中的数据 保证列表中只有一个文件
            this.uploadfileList = []
            var extName = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
            if (extName === '.xls' || extName === '.xlsx') {

            } else {
                this.$message.error("只能上传xls或者xlsx文件");
                return false;
            }
        },
        clickTableRow(row, event, column) {
            this.$emit('clickTableRow', row, event, column)
        },
        onFormChange(field, value, rule, api, setFlag) {
            this.$emit('formChange', field, value, rule, api, setFlag)
        },
        getOptionsLabel(arr, value, lName, vName) {

            let lNames = lName || 'label'
            let vNames = vName || 'value'
            let lis = arr.filter(is => is[vNames] == value)
            if (lis.length) {
                return lis[0][lNames]
            } else {
                for (let i = 0; i < arr.length; i++) {
                    if (arr[i].children && arr[i].children.length > 0) {
                        let lab = this.getOptionsLabel(arr[i].children, value, lNames, vNames);
                        return lab
                    }
                }
            }
        },
        async loadTable(tree, treeNode, resolve) {
            let resTable = await this.getDataPromise('treeTable', tree.id)
            let datas = resTable.records.map(is => {
                is.hasChildren = this.isHasChildren
                is.newsId = 'p_' + new Date().toString() + '_' + is.id
                return is
            })
            resolve(datas)
        },
        getListValue(list, field) {
            let lis = list.filter(is => is.field == field)
            if (lis.length) {
                return lis[0].value
            } else {
                return null
            }
        },
        removeList(data) {
            let ids = data.map(i => i.id)
            remove(this.options.api, ids, this.newRequest).then(res => {
                this.$message({
                    message: '删除成功！',
                    type: 'success'
                })
                this.currentPage = 1
                this.getList()
            })
        },
        updateList(old, news) {
            var newsKeys = Object.keys(news)
            newsKeys.forEach(os => {
                old[0][os] = news[os]
            })
            update(this.options.api, old[0], this.newRequest).then(res => {
                this.$message({
                    message: '修改成功！',
                    type: 'success'
                })
                this.showDrawer = false
                this.getList()
            })
        },
        subFormDrawer(data) {
            return new Promise((resolve, reject) => {
                if (!this.$parent.subFormDrawer) {
                    resolve()
                } else {
                    this.$emit('subFormDrawer', data, resolve)
                }
            })
        },
        saveList(data) {
            // data.password = '123456'
            let querys = {}
            if (this.defaultQuery) {
                querys = this.deepClone2(this.defaultQuery)
            }
            let datas = Object.assign(querys, data)
            save(this.options.api, datas, this.newRequest).then(res => {
                this.$message({
                    message: '添加成功！',
                    type: 'success'
                })
                this.showDrawer = false
                this.getList()
            })
        },
        getDataPromise(type, parentId) {
            let querys = {}
            if (this.defaultQuery) {
                querys = this.deepClone2(this.defaultQuery)
            }
            let enums = this.searhList.filter(is => (is.type == 'select' || is.type == 'treeSelect') && is.props && is.props.multiple)
            let ranges = this.searhList.filter(is => is.type == 'DatePicker' && is.props && is.props.type == 'datetimerange')
            let other = this.searhList.map(is => {
                if (is.type == 'select' || is.type == 'treeSelect' || is.type == 'DatePicker') {
                    if (is.props && is.props.multiple) {
                        return null
                    } else if (is.props && is.props.type == 'datetimerange') {
                        return null
                    } else {
                        return is
                    }
                } else {
                    return is
                }
            }).filter(i => i)

            querys.enums = {}
            enums.forEach(is => {
                querys.enums[is.field] = this.searchQuery[is.field]
            })

            querys.ranges = {}
            ranges.forEach(is => {
                querys.ranges[is.field] = this.searchQuery[is.field]
            })

            let egnorArr = ['', [], null]
            other.forEach(is => {
                querys[is.field] = egnorArr.includes(this.searchQuery[is.field]) ? null : this.searchQuery[is.field]
            })

            let size = this.pageSize
            let current = this.currentPage

            if (type && type == 'treeTable') {
                querys.parentId = parentId
                size = -1
                current = 1
            }

            return new Promise((resolve, reject) => {
                list(this.options.api, {
                    ...querys,
                    size,
                    current,
                }, this.newRequest, this.options.scope).then(res => {
                    resolve(res)
                })
            })
        },
        async getList() {
            let res = await this.getDataPromise()

            this.tableData = res.records.map(i => {
                i.hasChildren = this.isHasChildren
                i.newsId = 'p_' + new Date().toString() + '_' + i.id
                return i
            })
            this.pageTotal = res.total
            this.currentPage = res.current
        },
        // 通过js的内置对象JSON来进行数组对象的深拷贝
        deepClone2(obj) {
            var _obj = JSON.stringify(obj)
            var objClone = JSON.parse(_obj)
            return objClone
        },
        async addData(newData) {  //新增
            await this.subFormDrawer({ newData })
            this.saveList(newData)
        },
        async editData(oldData, newData) {    //修改
            await this.subFormDrawer({ oldData, newData })
            this.updateList(oldData, newData)
        },
        deleteData(data) {  //删除
            this.removeList(data)
        },
        selectData() {  //查询
            this.getList()
        },
        importsTemplate() {  //导入模板
            importsTemplate(this.options.api, { fieldNames: null }, this.newRequest)
        },
        exports() {  //导出
            exports(this.options.api, { fieldNames: null }, this.newRequest)
        },
        handleAdd() {  //新增
            // this.drawerTitle = '新增' + this.drawer.title

            this.clearFormVlaue()
            this.formRuleDrawerNes = this.deepClone2(this.formRuleDrawer)
            this.showDrawer = true
            this.drawerType = 'add'
            this.$emit('addRow', this.drawerType)
        },
        handleExport() {
            this.exports()
        },
        handleExportTemplate() {
            this.importsTemplate()
        },
        deleteRows() {
            this.deleteData(this.deleRows)
        },
        handleDelete() {  //删除
            // let label = this.tableColumnList[0].title
            // let prop = this.tableColumnList[0].field
            // let propValus = this.tableCheckedRows.map(i => i[prop])
            this.dialogCon = '是否删除所选数据项？'
            this.showConfirm = true
            this.deleRows = this.tableCheckedRows
        },
        handleSelectionChange(e) {
            this.tableCheckedRows = e
        },
        handleCurrentChange(e) { //分页 current
            this.currentPage = e
            this.selectData()
        },
        async handleUpdateRow(e) {
            this.setFormValue(e)
            if (this.hasUpdate) {
                this.drawerType = 'edit'
                this.editRows = [e]
                this.formRuleDrawer = this.formRuleDrawer.map((i, ind) => {
                    if (i.props) {
                        i.props.disabled = false
                    } else {
                        i.props = {
                            disabled: false
                        }
                    }
                    return i
                })
            } else {
                this.drawerType = 'look'
                this.formRuleDrawer = this.formRuleDrawer.map(i => {
                    if (i.props) {
                        i.props.disabled = true
                    } else {
                        i.props = {
                            disabled: true
                        }
                    }
                    return i
                })
                this.formOption.submitBtn = false
            }
            this.formRuleDrawerNes = this.deepClone2(this.formRuleDrawer)
            this.showDrawer = true
            this.$emit('updateRow', this.drawerType, e)
        },
        getFormModeValue(formData) {
            let specLis = this.formRuleDrawer.filter(i => i.type == 'treeSelect')
            if (specLis && specLis.length) {
                specLis.forEach(is => {
                    formData[is.field] = formData[is.field] ? formData[is.field].toString() : ''
                })
            }

            let keys = Object.keys(formData)
            keys.forEach(i => {
                if (formData[i] == undefined) {
                    formData[i] = null
                }
            })

            // return ;
            if (this.drawerType == 'add') {
                this.addData(formData)
            } else if (this.drawerType == 'edit') {
                this.editData(this.editRows, formData)
            } else if (this.drawerType == 'look') {
                this.showDrawer = false
            }
            // this.showDrawer = false
        },
        clearFormVlaue() {
            let formRuleDrawer = this.formRuleDrawer
            formRuleDrawer = formRuleDrawer.map(is => {
                is.value = null
                if (is.type == 'json' || is.type == 'uploader' || is.type == 'guploader' || is.type == 'treeSelect' || is.type == 'cascader2' || is.type == 'svgIcon' || is.type == 'gmap' || is.type == 'glist') is.props.value = null
                return is
            })
            this.formRuleDrawer = formRuleDrawer
        },
        setFormValue(e) {
            let keys = Object.keys(e)
            let formRuleDrawer = this.formRuleDrawer
            let specLis = formRuleDrawer.filter(i => i.type == 'json' || i.type == 'uploader' || i.type == 'guploader' || i.type == 'treeSelect' || i.type == 'cascader2' || i.type == 'svgIcon' || i.type == 'gmap' || i.type == 'glist').map(is => is.field)
            formRuleDrawer = formRuleDrawer.map(is => {
                if (specLis.includes(is.field)) {
                    is.props.value = e[is.field]
                } else if (keys.includes(is.field)) {
                    is.value = e[is.field]
                } else {
                    is.value = null
                }
                return is
            })
            this.formRuleDrawer = formRuleDrawer
        },
        handleDeleteRow(e) {  //表格 删除row
            let label = this.tableColumnList[0].title
            let prop = this.tableColumnList[0].field
            let propValus = e[prop]
            this.dialogCon = label + propValus.toString() + '的数据项'
            this.deleRows = [e]

            this.showConfirm = true
        },
        handleQuery() {  //搜索
            this.fApiSearch.submit((formData, fApi) => {
                this.searchQuery = formData
                this.currentPage = 1
                this.pageTotal = 1
                this.selectData()
            })
        },
        resetQuery() {  //重置
            // let keysArr = Object.keys(this.searchQuery)
            // keysArr.forEach(is => {
            //     this.sel[is] = null
            // })
            this.searhList = this.searhList.map(i => {
                i.value = null
                return i
            })
            // this.fApiSearch.resetFields()
            this.handleQuery()
        }
    }
}
</script>

<style scoped>
::v-deep .el-table .is-left .cell {
    justify-content: flex-start;
}

::v-deep .el-select {
    width: 100%;
}

::v-deep .el-table th.el-table__cell {
    background: #eeeeee;
    padding: 0;
}

::v-deep .el-table--group::after,
::v-deep .el-table--border::after,
::v-deep .el-table::before {
    background-color: #ffffff;
}

::v-deep .el-table .cell {
    display: flex;
    justify-content: center;
}

/* .el-table__row .el-table__cell:nth-of-type(2) .cell {
    justify-content: flex-start;
    padding-left: 30px;
} */
</style>

<style lang="scss" scoped>
.drawer_history {
    padding-left: 30px;
}

::v-deep .el-form-item .el-input {
    width: 100%;
}

::v-deep .el-drawer__body .form-create .el-form-item {
    display: flex;
    flex-direction: column;
    padding-left: 40px;

    .el-form-item__label {
        width: 100% !important;
        text-align: left;
    }

    .el-form-item__content {
        margin-left: 0 !important;
    }
}

.search {
    display: flex;
    flex-direction: row;

    .searchB {
        margin-left: 50px;
        width: 200px;
        flex-shrink: 0;
    }
}

.auto_page {
    background: #fff;
    border-top: 1px solid #ccc;
    padding-left: 20px;
    padding: 20px 18px 20px 25px;
}

.pagin {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.mb8 {
    margin-bottom: 25px;
}
</style>
