import request from '@/utils/request'

const sysDeptApi = CONSTANT.SYSTEM + '/sys/dept'
const sysAccountApi = CONSTANT.SYSTEM + '/sys/account'

// 获取部门树列表
export function getDeptTreeList() {
  return request({
    url: `${sysDeptApi}/treeList`,
    method: 'get'
  })
}

// 获取用户列表(支持部门过滤)
export function getUserList(params) {
  return request({
    url: `${sysAccountApi}/list`,
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `${sysAccountApi}/${id}`,
    method: 'get'
  })
}

// 更新用户信息
export function updateUser(data) {
  return request({
    url: `${sysAccountApi}`,
    method: 'put',
    data,
    // 不显示加载提示
    loadingDisabled: true
  })
}

// 新增用户
export function createUser(data) {
  return request({
    url: `${sysAccountApi}`,
    method: 'post',
    data,
    // 不显示加载提示
    loadingDisabled: true
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `${sysAccountApi}/${id}`,
    method: 'delete'
  })
}

// 批量删除用户
export function batchDeleteUsers(ids) {
  return request({
    url: `${sysAccountApi}/${ids.join(',')}`,
    method: 'delete'
  })
}

// 解锁账户
export function unlockAccount(username) {
  return request({
    url: `${sysAccountApi}/unlock/${username}`,
    method: 'post'
  })
}

// 批量解锁账户
export function batchUnlockAccounts(usernames) {
  return Promise.all(usernames.map(username => unlockAccount(username)))
}

// 重置用户密码
export function resetUserPassword(id) {
  return request({
    url: `${sysAccountApi}/${id}/reset-password`,
    method: 'post'
  })
}

// 批量重置用户密码
export function batchResetPasswords(ids) {
  return request({
    url: `${sysAccountApi}/resetPassword`,
    method: 'put',
    params: {
      accountId: ids.join(',')
    }
  })
}

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: `${CONSTANT.SYSTEM}/sys/role/list`,
    method: 'get',
    params
  })
}

// 获取职位列表
export function getPostList() {
  return request({
    url: `${CONSTANT.SYSTEM}/sys/post/list`,
    method: 'get',
    params: {
      current: 1,
      size: -1,
      enabled: true
    }
  })
}

// 导出用户数据
export function exportUser(params) {
  return request({
    url: `${sysAccountApi}/export`,
    method: 'get',
    params,
    responseType: 'blob'
  }).then(data => {
    const blob = new Blob([data.data], {
      type: 'application/vnd.ms-excel'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '系统账号.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
  })
}

// 下载导入模板
export function downloadTemplate() {
  return request({
    url: `${sysAccountApi}/template`,
    method: 'get',
    responseType: 'blob'
  }).then(data => {
    const blob = new Blob([data.data], {
      type: 'application/vnd.ms-excel'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '系统账号导入模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
  })
}

// 导入用户数据
export function importUser(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: `${sysAccountApi}/import`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量切换用户状态（启用/停用）
export function toggleAccountStatus(accountIds, status) {
  return request({
    url: `${sysAccountApi}/toggleStatus`,
    method: 'put',
    params: {
      accountIds: accountIds.join(','),
      status
    }
  })
}

export function getConfigByKey(key) {
  return request({
    url: `${CONSTANT.SYSTEM}/sys/config/key`,
    method: 'get',
    params: { key }
  }).catch(() => {
    // 如果请求失败，返回null而不是抛出错误
    return null;
  })
}