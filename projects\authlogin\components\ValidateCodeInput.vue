<template>
    <el-input :maxlength="$attrs.maxlength || 6" :placeholder="$attrs.placeholder || '请输入验证码'"  :prefix-icon="$attrs['prefix-icon'] || 'el-icon-message'" v-bind="$attrs" v-on="$listeners">
        <div slot="suffix" class="sms">
            <el-button :disabled="$attrs.countdown > 0" @click.stop="$listeners.send()"
                v-text="$attrs.countdown > 0 ? `${$attrs.countdown}s后重试` : '获取验证码'">
            </el-button>
        </div>
    </el-input>
</template>
<script>
export default {

}
</script>
<style lang="scss" scoped>
.sms {
    height: 100%;
    display: flex;
    align-items: center;

    .el-button:not(:disabled) {
        background-color: rgb(0, 122, 255);
        color: #fff;
        border: none;

        &:hover {
            background-color: rgb(0, 86, 179);
        }
    }

    .el-button:disabled {
        border: none;
    }
}
</style>
