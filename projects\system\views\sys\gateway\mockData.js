// 模拟路由数据
export const mockRoutes = [
  {
    id: 1,
    name: 'system-service',
    uri: 'lb://extreme-system',
    pattern: '/system/**',
    metadata: {
      version: '1.0.0',
      description: '系统管理服务',
      maintainer: 'admin'
    }
  },
  {
    id: 2,
    name: 'auth-service',
    uri: 'lb://extreme-auth',
    pattern: '/auth/**',
    metadata: {
      version: '1.0.0',
      description: '认证授权服务'
    }
  },
  {
    id: 3,
    name: 'file-service',
    uri: 'lb://extreme-file',
    pattern: '/file/**',
    metadata: {
      version: '1.0.0',
      description: '文件存储服务'
    }
  },
  {
    id: 4,
    name: 'monitor-service',
    uri: 'lb://extreme-monitor',
    pattern: '/monitor/**',
    metadata: {
      version: '1.0.0',
      description: '监控服务'
    }
  },
  {
    id: 5,
    name: 'gateway-service',
    uri: 'lb://extreme-gateway',
    pattern: '/gateway/**',
    metadata: {
      version: '1.0.0',
      description: '网关配置服务'
    }
  },
  {
    id: 5,
    name: 'gateway-service',
    uri: 'lb://extreme-gateway',
    pattern: '/gateway/**',
    metadata: {
      version: '1.0.0',
      description: '网关配置服务'
    }
  }
]

// 模拟断言数据
export const mockPredicates = [
  {
    id: 1,
    routeId: 1,
    name: 'Path',
    arg: {
      pattern: '/system/**'
    }
  },
  {
    id: 2,
    routeId: 1,
    name: 'Method',
    arg: {
      method: 'GET'
    }
  },
  {
    id: 3,
    routeId: 2,
    name: 'Path',
    arg: {
      pattern: '/auth/**'
    }
  },
  {
    id: 4,
    routeId: 2,
    name: 'Method',
    arg: {
      method: 'POST'
    }
  },
  {
    id: 5,
    routeId: 3,
    name: 'Path',
    arg: {
      pattern: '/file/**'
    }
  },
  {
    id: 6,
    routeId: 4,
    name: 'Path',
    arg: {
      pattern: '/monitor/**'
    }
  },
  {
    id: 7,
    routeId: 5,
    name: 'Path',
    arg: {
      pattern: '/gateway/**'
    }
  },
  {
    id: 8,
    routeId: 1,
    name: 'Header',
    arg: {
      header: 'X-Request-Id',
      regexp: '\\d+'
    }
  }
]

// 模拟过滤器数据
export const mockFilters = [
  {
    id: 1,
    routeId: 1,
    name: 'StripPrefix',
    arg: {
      parts: 1
    }
  },
  {
    id: 2,
    routeId: 1,
    name: 'AddRequestHeader',
    arg: {
      name: 'X-System-Request',
      value: 'true'
    }
  },
  {
    id: 3,
    routeId: 2,
    name: 'StripPrefix',
    arg: {
      parts: 1
    }
  },
  {
    id: 4,
    routeId: 2,
    name: 'AddResponseHeader',
    arg: {
      name: 'X-Auth-Response',
      value: 'processed'
    }
  },
  {
    id: 5,
    routeId: 3,
    name: 'StripPrefix',
    arg: {
      parts: 1
    }
  },
  {
    id: 6,
    routeId: 4,
    name: 'StripPrefix',
    arg: {
      parts: 1
    }
  },
  {
    id: 7,
    routeId: 5,
    name: 'StripPrefix',
    arg: {
      parts: 1
    }
  },
  {
    id: 8,
    routeId: 1,
    name: 'RequestRateLimiter',
    arg: {
      'redis-rate-limiter.replenishRate': '10',
      'redis-rate-limiter.burstCapacity': '20'
    }
  }
]

// 模拟安全配置数据
export const mockSecurities = [
  {
    id: 1,
    routeId: 1,
    uri: '/system/admin/**',
    method: 'GET',
    matcher: 'hasRole',
    arg: ['ADMIN', 'SYSTEM_MANAGER']
  },
  {
    id: 2,
    routeId: 1,
    uri: '/system/user/add',
    method: 'POST',
    matcher: 'hasAuthority',
    arg: ['system:user:add']
  },
  {
    id: 3,
    routeId: 2,
    uri: '/auth/login',
    method: '',
    matcher: 'permitAll',
    arg: []
  },
  {
    id: 4,
    routeId: 2,
    uri: '/auth/token',
    method: 'POST',
    matcher: 'authenticated',
    arg: []
  },
  {
    id: 5,
    routeId: 3,
    uri: '/file/upload',
    method: 'POST',
    matcher: 'authenticated',
    arg: []
  },
  {
    id: 6,
    routeId: 3,
    uri: '/file/admin/**',
    method: '',
    matcher: 'hasRole',
    arg: ['ADMIN']
  },
  {
    id: 7,
    routeId: 4,
    uri: '/monitor/metrics',
    method: 'GET',
    matcher: 'hasRole',
    arg: ['ADMIN', 'MONITOR_ADMIN']
  },
  {
    id: 8,
    routeId: 5,
    uri: '/gateway/config/**',
    method: '',
    matcher: 'hasRole',
    arg: ['ADMIN']
  },
  {
    id: 9,
    routeId: 1,
    uri: '/system/internal/**',
    method: '',
    matcher: 'hasIpAddress',
    arg: ['127.0.0.1', '***********/24']
  }
] 