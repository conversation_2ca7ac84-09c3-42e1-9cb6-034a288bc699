<template>
  <div
      class="sql-composer-popup"
      ref="composer"
      @mousedown="handleMouseDown"
  >
    <div
        class="composer-header"
        @mousedown.stop="startDrag"
    >
      <div class="title">
        <i class="el-icon-magic-stick"></i>
        <span>AI 辅助编写 SQL</span>
      </div>
      <div class="header-actions">
        <el-button
            type="text"
            class="action-btn"
            title="重置位置"
            @click="resetPosition"
        >
          <i class="el-icon-refresh"></i>
        </el-button>
        <el-button
            type="text"
            class="action-btn"
            title="关闭"
            @click="$emit('close')"
        >
          <i class="el-icon-close"></i>
        </el-button>
      </div>
    </div>
    <div class="composer-content">
      <div class="section-title">
        <i class="el-icon-connection"></i>
        <span>选择数据表</span>
      </div>
      <div class="context-selector">
        <el-dropdown
            @command="handleAddContext"
            trigger="click"
            class="add-context-btn"
        >
          <el-button type="text">
            <i class="el-icon-plus"></i>
            添加上下文
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-input
                v-model="tableSearchKeyword"
                placeholder="搜索表名"
                prefix-icon="el-icon-search"
                clearable
                class="table-search"
                @input="handleTableSearch"
            />
            <el-dropdown-item
                v-for="table in filteredTables"
                :key="table.name"
                :command="table"
                class="table-item"
                :class="{ 'selected': isTableSelected(table) }"
            >
              <div class="table-info">
                <span class="table-name">
                  <i class="el-icon-tickets"></i>
                  {{ table.name }}
                </span>
                <span class="table-comment" v-if="table.comment">
                  {{ table.comment }}
                </span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <div class="selected-contexts">
          <el-tag
              v-for="table in selectedTables"
              :key="table.name"
              closable
              @close="removeContext(table)"
              size="small"
              class="context-tag"
              effect="dark"
          >
            {{ table.name }}
          </el-tag>
          <div v-if="!selectedTables.length" class="empty-tip">
            请选择需要查询的数据表
          </div>
        </div>
      </div>

      <div class="section-title">
        <i class="el-icon-chat-line-square"></i>
        <span>描述查询需求</span>
      </div>
      <div class="prompt-input">
        <el-input
            type="textarea"
            v-model="naturalLanguageInput"
            :rows="5"
            placeholder="例如：查询最近7天内的订单数量和总金额，按照日期分组"
            @input="handlePromptInput"
        />
        <div class="prompt-footer">
          <div class="prompt-tips">
            <i class="el-icon-info"></i>
            <span>提示：描述越详细，生成的SQL越准确</span>
          </div>
          <el-button
              type="primary"
              size="small"
              @click="generateSQL"
              :loading="generating"
          >
            <i class="el-icon-magic-stick"></i>
            生成 SQL
          </el-button>
        </div>
      </div>

      <!-- AI 生成结果预览区域 -->
      <div v-if="sqlContent" class="preview-section">
        <div class="section-title">
          <i class="el-icon-document"></i>
          <span>生成结果</span>
        </div>
        <div class="preview-content">
          <div class="markdown-preview" v-html="renderedContent"></div>
        </div>
      </div>

      <div class="action-bar">
        <div class="left-actions">
          <el-button
              size="small"
              type="info"
              plain
              @click="resetPosition"
          >
            <i class="el-icon-refresh"></i>
            重置位置
          </el-button>
        </div>
        <div class="right-actions">
          <el-button
              size="small"
              @click="$emit('close')"
          >
            取消
          </el-button>
          <el-button
              type="primary"
              size="small"
              @click="handleApply"
          >
            <i class="el-icon-check"></i>
            应用
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getTableList } from '@system/api/data-manger/table'
import { generateSQL } from '@system/api/data-manger/ai'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/vs.css'

const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return '<pre class="hljs"><code>' +
            hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
            '</code></pre>'
      } catch (__) {}
    }
    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
  }
})

export default {
  name: 'SqlComposer',

  props: {
    dataSourceIds: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      naturalLanguageInput: '',
      generating: false,
      selectedTables: [],
      availableTables: [],
      allTableIds: [],
      thinkingContent: '',
      sqlContent: '',
      isThinking: false,
      isDragging: false,
      dragOffset: {
        x: 0,
        y: 0
      },
      position: {
        x: 0,
        y: 0
      },
      defaultPosition: {
        x: window.innerWidth - 624, // 根据新宽度调整默认位置
        y: 100 // 距离顶部100px
      },
      tableSearchKeyword: '',
      filteredTables: [],
      collapsedThoughts: new Set(),
      copyBtnVisible: false
    }
  },

  computed: {
    canGenerate() {
      return this.naturalLanguageInput && this.selectedTables.length > 0 && !this.generating
    },
    renderedContent() {
      if (!this.sqlContent) return ''

      // 先处理思维链，保存到数组中
      const thoughts = []
      let content = this.sqlContent.replace(/<think>([\s\S]*?)<\/think>/g, (match, p1, index) => {
        thoughts.push({
          id: `thought-${index}`,
          content: p1.trim()
        })
        return `<!--thought-${index}-->`
      })

      // 渲染 Markdown
      content = md.render(content)

      // 替换回思维链组件
      thoughts.forEach(thought => {
        content = content.replace(
            `<!--${thought.id}-->`,
            `<div class="thinking-chain" data-thought-id="${thought.id}">
            <div class="thinking-header">
              <i class="el-icon-cpu"></i>
              <span>思维链</span>
              <i class="el-icon-arrow-down thinking-toggle"></i>
            </div>
            <div class="thinking-content">
              ${md.render(thought.content)}
            </div>
          </div>`
        )
      })

      // 为代码块添加复制按钮
      content = content.replace(
          /<pre><code class="language-(\w+)">/g,
          '<pre class="code-block"><button class="copy-btn">复制</button><code class="language-$1">'
      )

      return content
    }
  },

  watch: {
    dataSourceIds: {
      immediate: true,
      handler(newIds) {
        if (newIds && newIds.length > 0) {
          this.loadTables()
        }
      }
    },
    availableTables: {
      immediate: true,
      handler(tables) {
        this.filteredTables = tables
      }
    }
  },

  mounted() {
    const savedPosition = localStorage.getItem('sqlComposerPosition')
    if (savedPosition) {
      try {
        const { x, y } = JSON.parse(savedPosition)
        this.position = { x, y }
      } catch (e) {
        console.warn('Failed to restore composer position:', e)
      }
    }
    this.$nextTick(() => {
      this.updatePosition()
    })
    // 初始化时设置默认位置
    this.position = { ...this.defaultPosition }

    // 添加事件委托
    document.addEventListener('click', this.handleGlobalClick)
  },

  beforeDestroy() {
    // 清理事件监听
    document.removeEventListener('click', this.handleGlobalClick)
  },

  methods: {
    async loadTables() {
      try {
        const tables = []
        const allIds = []
        for (const datasourceId of this.dataSourceIds) {
          const res = await getTableList({ datasourceId })
          if (res.data && res.data.records) {
            tables.push(...res.data.records.map(table => ({
              id: table.id,
              name: table.name,
              comment: table.comment,
              datasourceId: table.datasourceId
            })))
            allIds.push(...res.data.records.map(table => table.id))
          }
        }
        this.availableTables = tables
        this.allTableIds = allIds
        this.filteredTables = tables
      } catch (error) {
        this.$message.error('加载表列表失败：' + error.message)
      }
    },

    handleAddContext(table) {
      if (!this.selectedTables.find(t => t.name === table.name)) {
        this.selectedTables.push(table)
      }
    },

    removeContext(table) {
      const index = this.selectedTables.findIndex(t => t.name === table.name)
      if (index > -1) {
        this.selectedTables.splice(index, 1)
      }
    },

    handlePromptInput(value) {
      // 处理输入变化
    },

    async generateSQL() {
      if (!this.canGenerate) return

      this.generating = true
      this.sqlContent = ''  // 重置内容

      try {
        const tableIds = this.selectedTables.length > 0
            ? this.selectedTables.map(table => table.id)
            : this.allTableIds

        await generateSQL({
          prompt: this.naturalLanguageInput,
          params: {
            tableIds: tableIds
          }
        }, {
          onMessage: (data) => {
            const content = data.choices[0]?.delta?.content
            if (!content) return

            // 直接添加内容到预览区
            this.sqlContent += content
          },
          onError: (error) => {
            console.error('生成SQL失败:', error)
            this.$message.error('生成SQL失败: ' + error.message)
          },
          onComplete: () => {
            this.generating = false
          }
        })
      } catch (error) {
        console.error('生成SQL失败:', error)
        this.$message.error('生成SQL失败: ' + error.message)
      } finally {
        this.generating = false
      }
    },

    // 处理应用按钮点击
    handleApply() {
      // 只提取 ```sql 标签中的内容
      let finalSQL = ''
      const sqlRegex = /```sql\n([\s\S]*?)```/g
      const matches = this.sqlContent.match(sqlRegex)

      if (matches && matches.length > 0) {
        // 提取第一个SQL代码块的内容
        finalSQL = matches[0]
            .replace(/```sql\n/, '')  // 移除开始标记
            .replace(/```$/, '')      // 移除结束标记
            .trim()
      }

      this.$emit('sql-generated', finalSQL)
      this.$emit('close')
      this.$message.success('SQL已应用')
    },

    startDrag(e) {
      if (e.target.closest('.action-bar') || e.target.closest('.el-button')) return

      this.isDragging = true
      this.dragOffset = {
        x: e.clientX - this.position.x,
        y: e.clientY - this.position.y
      }

      document.addEventListener('mousemove', this.handleDrag)
      document.addEventListener('mouseup', this.stopDrag)

      document.body.classList.add('no-select')
    },

    handleDrag(e) {
      if (!this.isDragging) return

      const newX = e.clientX - this.dragOffset.x
      const newY = e.clientY - this.dragOffset.y

      this.position = { x: newX, y: newY }
      this.updatePosition()
    },

    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.stopDrag)
      document.body.classList.remove('no-select')

      localStorage.setItem('sqlComposerPosition', JSON.stringify(this.position))
    },

    updatePosition() {
      if (this.$refs.composer) {
        const composer = this.$refs.composer
        const rect = composer.getBoundingClientRect()
        const maxX = window.innerWidth - rect.width
        const maxY = window.innerHeight - rect.height

        this.position.x = Math.min(Math.max(0, this.position.x), maxX)
        this.position.y = Math.min(Math.max(0, this.position.y), maxY)

        composer.style.transform = `translate3d(${this.position.x}px, ${this.position.y}px, 0)`
      }
    },

    handleMouseDown(e) {
      if (this.isDragging) {
        e.preventDefault()
      }
    },

    resetPosition() {
      this.position = { ...this.defaultPosition }
      this.$nextTick(() => {
        this.updatePosition()
      })
      this.$message.success('位置已重置')
    },

    handleTableSearch(keyword) {
      if (!keyword) {
        this.filteredTables = this.availableTables
        return
      }

      const searchText = keyword.toLowerCase()
      this.filteredTables = this.availableTables.filter(table =>
          table.name.toLowerCase().includes(searchText) ||
          (table.comment && table.comment.toLowerCase().includes(searchText))
      )
    },

    isTableSelected(table) {
      return this.selectedTables.some(t => t.name === table.name)
    },

    handleGlobalClick(e) {
      // 处理思维链折叠
      const header = e.target.closest('.thinking-header')
      if (header) {
        const chain = header.closest('.thinking-chain')
        if (chain) {
          chain.classList.toggle('is-collapsed')
        }
      }

      // 处理代码复制
      const copyBtn = e.target.closest('.copy-btn')
      if (copyBtn) {
        const pre = copyBtn.closest('pre')
        const code = pre.querySelector('code')
        if (code) {
          navigator.clipboard.writeText(code.textContent).then(() => {
            this.$message.success('复制成功')
            copyBtn.textContent = '已复制!'
            setTimeout(() => {
              copyBtn.textContent = '复制'
            }, 2000)
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sql-composer-popup {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  max-block-size: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  position: fixed;
  inline-size: 600px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  user-select: none;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  left: 0;
  top: 0;

  .composer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #409EFF, #3a8ee6);
    flex-shrink: 0;
    cursor: move;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 1px;
      background: rgba(255, 255, 255, 0.1);
    }

    .title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #fff;

      i {
        font-size: 16px;
      }
    }

    .header-actions {
      display: flex;
      gap: 4px;
    }

    .action-btn {
      padding: 8px;
      font-size: 16px;
      color: #fff;
      opacity: 0.7;
      transition: all 0.3s;

      &:hover {
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }

  .composer-content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    background: rgba(255, 255, 255, 0.95);

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      color: #606266;
      font-size: 13px;
      font-weight: 500;

      i {
        font-size: 14px;
        color: #909399;
      }
    }

    .context-selector {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 20px;

      .add-context-btn {
        .el-button {
          color: #606266;
          padding: 8px 16px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          background: #fff;
          transition: all 0.3s;
          font-weight: 500;

          &:hover {
            color: #409EFF;
            border-color: #409EFF;
            background: #f5f7fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          }

          i {
            margin-right: 6px;
            transition: all 0.3s;
          }

          &:hover i {
            transform: rotate(90deg);
            color: #409EFF;
          }
        }
      }

      .selected-contexts {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        min-height: 32px;
        padding: 8px 12px;
        background: rgba(245, 247, 250, 0.8);
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        transition: all 0.3s;

        &:hover {
          background: rgba(245, 247, 250, 0.95);
          border-color: #c0c4cc;
        }

        .context-tag {
          margin: 2px;
        }

        .empty-tip {
          color: #909399;
          font-size: 13px;
          padding: 4px 0;
        }
      }
    }

    .prompt-input {
      margin-bottom: 20px;

      .el-textarea {
        ::v-deep .el-textarea__inner {
          resize: none;
          font-size: 14px;
          line-height: 1.6;
          padding: 12px;
          min-height: 120px;
          background: rgba(245, 247, 250, 0.8);
          border-color: #e4e7ed;
          transition: all 0.3s;

          &:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
          }
        }
      }

      .prompt-footer {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .prompt-tips {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #909399;
        font-size: 12px;

        i {
          font-size: 14px;
        }
      }
    }

    .action-bar {
      display: flex;
      justify-content: space-between;
      gap: 12px;
      padding-top: 16px;
      margin-top: 16px;
      border-top: 1px solid #e4e7ed;

      .left-actions, .right-actions {
        display: flex;
        gap: 8px;
      }

      .el-button {
        padding: 8px 16px;
        transition: all 0.3s;
        font-weight: 500;

        &:not(.el-button--primary) {
          border-color: #dcdfe6;

          &:hover {
            color: #409EFF;
            border-color: #409EFF;
            background: #f5f7fa;
          }
        }

        &.el-button--primary {
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
          }
        }

        i {
          margin-right: 6px;
          transition: all 0.3s;
        }

        &:hover i {
          &.el-icon-refresh {
            transform: rotate(-180deg);
          }
          &.el-icon-check {
            transform: scale(1.2);
          }
        }
      }
    }

    .preview-section {
      margin-bottom: 20px;

      .preview-content {
        background: rgba(248, 249, 250, 0.8);
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 16px;
        margin-top: 12px;
        transition: all 0.3s;

        &:hover {
          background: rgba(248, 249, 250, 0.95);
          border-color: #c0c4cc;
        }

        .markdown-preview {
          font-size: 13px;
          line-height: 1.5;
          margin: 0;
          padding: 0;
          color: #1a1a1a;
          user-select: text;

          :deep(h3) {
            font-size: 14px;
            font-weight: 500;
            margin: 16px 0 8px;
            color: #303133;
          }

          :deep(p) {
            margin: 8px 0;
            line-height: 1.6;
          }

          :deep(pre.hljs) {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 12px;
            margin: 12px 0;
            border: 1px solid #e4e7ed;
            position: relative;

            code {
              font-family: Monaco, Menlo, Consolas, monospace;
              font-size: 12px;
              line-height: 1.5;
            }

            &:hover::after {
              content: '可以选择复制';
              position: absolute;
              right: 8px;
              top: 8px;
              font-size: 12px;
              color: #909399;
              background: rgba(255, 255, 255, 0.9);
              padding: 2px 6px;
              border-radius: 3px;
              pointer-events: none;
            }
          }

          :deep(.thinking-chain) {
            margin: 16px -16px;
            background: #f8f9fa;
            border-top: 1px solid #e4e7ed;
            border-bottom: 1px solid #e4e7ed;

            .thinking-header {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px 16px;
              border-bottom: 1px solid #e4e7ed;
              background: rgba(64, 158, 255, 0.04);
              cursor: pointer;
              user-select: none;
              transition: all 0.3s;

              &:hover {
                background: rgba(64, 158, 255, 0.08);
              }

              i {
                font-size: 14px;
                color: #409EFF;
              }

              span {
                font-size: 12px;
                font-weight: 500;
                color: #409EFF;
                flex: 1;
              }

              .thinking-toggle {
                font-size: 12px;
                transition: transform 0.3s;
              }
            }

            .thinking-content {
              padding: 12px 16px;
              color: #606266;
              font-size: 12px;
              line-height: 1.6;
              max-height: 500px;
              overflow: hidden;
              transition: all 0.3s ease-in-out;
            }

            &.is-collapsed {
              .thinking-toggle {
                transform: rotate(-90deg);
              }

              .thinking-content {
                max-height: 0;
                padding: 0 16px;
                opacity: 0;
              }
            }
          }

          :deep(.code-block) {
            position: relative;
            margin: 16px 0;

            .copy-btn {
              position: absolute;
              right: 8px;
              top: 8px;
              padding: 4px 8px;
              font-size: 12px;
              color: #606266;
              background: #fff;
              border: 1px solid #dcdfe6;
              border-radius: 3px;
              cursor: pointer;
              opacity: 0;
              transition: all 0.3s;
              z-index: 2;

              &:hover {
                color: #409EFF;
                border-color: #409EFF;
                background: #ecf5ff;
              }
            }

            &:hover .copy-btn {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}

::v-deep .el-dropdown-menu {
  min-width: 280px;
  max-height: 400px;
  overflow-y: auto;

  .table-search {
    position: sticky;
    top: 0;
    z-index: 1;
    margin: 8px;

    .el-input__inner {
      border-radius: 4px;
      height: 32px;
    }
  }

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.4;

    .table-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .table-name {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #303133;
      font-weight: 500;

      i {
        font-size: 14px;
        color: #409EFF;
      }
    }

    .table-comment {
      font-size: 12px;
      color: #909399;
      padding-left: 20px;
      line-height: 1.3;
    }

    &:hover {
      background-color: #ecf5ff;
      .table-name {
        color: #409EFF;

        i {
          color: #66b1ff;
        }
      }

      .table-comment {
        color: #66b1ff;
      }
    }

    &.selected {
      background-color: #ecf5ff;

      .table-name {
        color: #409EFF;
      }

      .table-comment {
        color: #66b1ff;
      }
    }
  }
}

:global(.no-select) {
  user-select: none !important;
  cursor: move !important;

  * {
    user-select: none !important;
  }
}
</style> 