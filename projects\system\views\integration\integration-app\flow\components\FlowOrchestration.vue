<template>
  <div class="flow-orchestration">
    <div class="orchestration-container">
      <!-- 左侧工具栏 -->
      <div class="tool-panel">
        <div class="tool-section">
          <h4>基础节点</h4>
          <div class="tool-group">
            <div
              v-for="tool in basicTools"
              :key="tool.type"
              class="tool-item"
              :draggable="true"
              @dragstart="handleDragStart($event, tool)"
            >
              <i :class="tool.icon"></i>
              <span>{{ tool.name }}</span>
            </div>
          </div>
        </div>

        <div class="tool-section">
          <h4>数据处理</h4>
          <div class="tool-group">
            <div
              v-for="tool in dataTools"
              :key="tool.type"
              class="tool-item"
              :draggable="true"
              @dragstart="handleDragStart($event, tool)"
            >
              <i :class="tool.icon"></i>
              <span>{{ tool.name }}</span>
            </div>
          </div>
        </div>

        <div class="tool-section">
          <h4>连接器</h4>
          <div class="tool-group">
            <div
              v-for="tool in connectorTools"
              :key="tool.type"
              class="tool-item"
              :draggable="true"
              @dragstart="handleDragStart($event, tool)"
            >
              <i :class="tool.icon"></i>
              <span>{{ tool.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-area">
        <div class="canvas-header">
          <div class="canvas-title">
            <h3>{{ currentFlow?.name || '流程编排' }}</h3>
            <span class="flow-status" :class="getFlowStatusClass()">
              {{ getFlowStatusText() }}
            </span>
          </div>
          <div class="canvas-actions">
            <el-button size="small" icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
            <el-button size="small" icon="el-icon-zoom-out" @click="handleZoomOut">
              缩小
            </el-button>
            <el-button size="small" icon="el-icon-zoom-in" @click="handleZoomIn">
              放大
            </el-button>
          </div>
        </div>

        <div
          class="canvas"
          ref="canvas"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="handleCanvasClick"
        >
          <!-- 网格背景 -->
          <div class="canvas-grid"></div>

          <!-- 流程节点 -->
          <div
            v-for="node in nodes"
            :key="node.id"
            class="flow-node"
            :class="{ active: selectedNode?.id === node.id }"
            :style="getNodeStyle(node)"
            @click.stop="handleNodeClick(node)"
            @mousedown="handleNodeMouseDown($event, node)"
          >
            <div class="node-header">
              <i :class="node.icon"></i>
              <span class="node-title">{{ node.name }}</span>
              <el-button
                type="text"
                icon="el-icon-close"
                size="mini"
                class="node-delete"
                @click.stop="handleDeleteNode(node)"
              />
            </div>
            <div class="node-body">
              <div class="node-description">{{ node.description }}</div>
            </div>
            <!-- 连接点 -->
            <div class="connection-points">
              <div class="input-point" @click.stop="handleConnectionPoint(node, 'input')"></div>
              <div class="output-point" @click.stop="handleConnectionPoint(node, 'output')"></div>
            </div>
          </div>

          <!-- 连接线 -->
          <svg class="connections-layer" v-if="connections.length > 0">
            <path
              v-for="connection in connections"
              :key="connection.id"
              :d="getConnectionPath(connection)"
              class="connection-line"
              @click.stop="handleConnectionClick(connection)"
            />
          </svg>

          <!-- 临时连接线 -->
          <svg class="temp-connection-layer" v-if="tempConnection">
            <path
              :d="getTempConnectionPath()"
              class="temp-connection-line"
            />
          </svg>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel" v-if="selectedNode">
        <div class="panel-header">
          <h4>节点属性</h4>
          <el-button
            type="text"
            icon="el-icon-close"
            size="mini"
            @click="selectedNode = null"
          />
        </div>
        <div class="panel-body">
          <el-form :model="selectedNode" label-width="80px" size="small">
            <el-form-item label="节点名称">
              <el-input v-model="selectedNode.name" />
            </el-form-item>
            <el-form-item label="描述">
              <el-input
                v-model="selectedNode.description"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item label="配置">
              <el-input
                v-model="selectedNode.config"
                type="textarea"
                :rows="5"
                placeholder="JSON配置"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlowOrchestration',
  props: {
    appId: {
      type: [String, Number],
      required: true
    },
    currentFlow: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 工具栏配置
      basicTools: [
        { type: 'start', name: '开始', icon: 'el-icon-video-play' },
        { type: 'end', name: '结束', icon: 'el-icon-video-pause' },
        { type: 'condition', name: '条件', icon: 'el-icon-share' },
        { type: 'loop', name: '循环', icon: 'el-icon-refresh' }
      ],
      dataTools: [
        { type: 'transform', name: '数据转换', icon: 'el-icon-sort' },
        { type: 'filter', name: '数据过滤', icon: 'el-icon-search' },
        { type: 'aggregate', name: '数据聚合', icon: 'el-icon-pie-chart' },
        { type: 'validate', name: '数据验证', icon: 'el-icon-circle-check' }
      ],
      connectorTools: [
        { type: 'http', name: 'HTTP请求', icon: 'el-icon-link' },
        { type: 'database', name: '数据库', icon: 'el-icon-coin' },
        { type: 'file', name: '文件操作', icon: 'el-icon-folder' },
        { type: 'message', name: '消息队列', icon: 'el-icon-message' }
      ],

      // 画布数据
      nodes: [],
      connections: [],
      selectedNode: null,
      draggedTool: null,
      
      // 连接相关
      tempConnection: null,
      connectionStart: null,
      
      // 画布状态
      canvasScale: 1,
      canvasOffset: { x: 0, y: 0 },
      
      // 拖拽状态
      isDragging: false,
      dragStart: { x: 0, y: 0 },
      nodeStart: { x: 0, y: 0 }
    }
  },
  mounted() {
    this.initCanvas()
  },
  methods: {
    // 初始化画布
    initCanvas() {
      // 添加一些示例节点
      this.nodes = [
        {
          id: 'start-1',
          type: 'start',
          name: '开始',
          description: '流程开始节点',
          icon: 'el-icon-video-play',
          x: 100,
          y: 100,
          config: '{}'
        },
        {
          id: 'http-1',
          type: 'http',
          name: 'API调用',
          description: '调用外部API',
          icon: 'el-icon-link',
          x: 300,
          y: 200,
          config: '{"url": "", "method": "GET"}'
        }
      ]
      
      this.connections = [
        {
          id: 'conn-1',
          from: 'start-1',
          to: 'http-1'
        }
      ]
    },

    // 工具拖拽开始
    handleDragStart(event, tool) {
      this.draggedTool = tool
      event.dataTransfer.effectAllowed = 'copy'
    },

    // 画布拖拽悬停
    handleDragOver(event) {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    },

    // 画布放置
    handleDrop(event) {
      event.preventDefault()
      if (!this.draggedTool) return

      const rect = this.$refs.canvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      const newNode = {
        id: `${this.draggedTool.type}-${Date.now()}`,
        type: this.draggedTool.type,
        name: this.draggedTool.name,
        description: `${this.draggedTool.name}节点`,
        icon: this.draggedTool.icon,
        x: x - 75, // 节点宽度的一半
        y: y - 40, // 节点高度的一半
        config: '{}'
      }

      this.nodes.push(newNode)
      this.draggedTool = null
    },

    // 节点点击
    handleNodeClick(node) {
      this.selectedNode = node
    },

    // 画布点击
    handleCanvasClick() {
      this.selectedNode = null
    },

    // 节点鼠标按下（开始拖拽）
    handleNodeMouseDown(event, node) {
      this.isDragging = true
      this.selectedNode = node
      this.dragStart = { x: event.clientX, y: event.clientY }
      this.nodeStart = { x: node.x, y: node.y }

      const handleMouseMove = (e) => {
        if (!this.isDragging) return
        
        const deltaX = e.clientX - this.dragStart.x
        const deltaY = e.clientY - this.dragStart.y
        
        node.x = this.nodeStart.x + deltaX
        node.y = this.nodeStart.y + deltaY
      }

      const handleMouseUp = () => {
        this.isDragging = false
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }

      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    },

    // 删除节点
    handleDeleteNode(node) {
      const index = this.nodes.findIndex(n => n.id === node.id)
      if (index > -1) {
        this.nodes.splice(index, 1)
        // 删除相关连接
        this.connections = this.connections.filter(
          conn => conn.from !== node.id && conn.to !== node.id
        )
        if (this.selectedNode?.id === node.id) {
          this.selectedNode = null
        }
      }
    },

    // 连接点点击
    handleConnectionPoint(node, type) {
      if (type === 'output') {
        this.connectionStart = node
      } else if (type === 'input' && this.connectionStart) {
        // 创建连接
        const newConnection = {
          id: `conn-${Date.now()}`,
          from: this.connectionStart.id,
          to: node.id
        }
        this.connections.push(newConnection)
        this.connectionStart = null
      }
    },

    // 获取节点样式
    getNodeStyle(node) {
      return {
        left: `${node.x}px`,
        top: `${node.y}px`,
        transform: `scale(${this.canvasScale})`
      }
    },

    // 获取连接线路径
    getConnectionPath(connection) {
      const fromNode = this.nodes.find(n => n.id === connection.from)
      const toNode = this.nodes.find(n => n.id === connection.to)
      
      if (!fromNode || !toNode) return ''

      const startX = fromNode.x + 150 // 节点宽度
      const startY = fromNode.y + 40  // 节点高度的一半
      const endX = toNode.x
      const endY = toNode.y + 40

      const midX = (startX + endX) / 2

      return `M ${startX} ${startY} C ${midX} ${startY} ${midX} ${endY} ${endX} ${endY}`
    },

    // 画布操作
    handleReset() {
      this.canvasScale = 1
      this.canvasOffset = { x: 0, y: 0 }
    },

    handleZoomIn() {
      this.canvasScale = Math.min(this.canvasScale + 0.1, 2)
    },

    handleZoomOut() {
      this.canvasScale = Math.max(this.canvasScale - 0.1, 0.5)
    },

    // 获取流程状态
    getFlowStatusClass() {
      return this.currentFlow?.status === 'active' ? 'status-active' : 'status-draft'
    },

    getFlowStatusText() {
      const statusMap = {
        active: '运行中',
        draft: '草稿',
        error: '错误'
      }
      return statusMap[this.currentFlow?.status] || '草稿'
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-orchestration {
  height: 100%;
  display: flex;
  flex-direction: column;

  .orchestration-container {
    display: flex;
    height: 100%;
    gap: 0;

    // 左侧工具栏
    .tool-panel {
      width: 240px;
      background: #fafbfc;
      border-right: 1px solid #e8e8e8;
      overflow-y: auto;
      flex-shrink: 0;

      .tool-section {
        padding: 16px 12px;
        border-bottom: 1px solid #f0f0f0;

        h4 {
          font-size: 13px;
          font-weight: 600;
          color: #595959;
          margin: 0 0 12px 0;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .tool-group {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .tool-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            cursor: grab;
            transition: all 0.2s ease;
            font-size: 13px;

            &:hover {
              border-color: #1890ff;
              background: #f6f8ff;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
            }

            &:active {
              cursor: grabbing;
            }

            i {
              font-size: 14px;
              color: #1890ff;
            }

            span {
              color: #262626;
              font-weight: 500;
            }
          }
        }
      }
    }

    // 中间画布区域
    .canvas-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: #f8f9fa;

      .canvas-header {
        background: white;
        padding: 12px 20px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .canvas-title {
          display: flex;
          align-items: center;
          gap: 12px;

          h3 {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin: 0;
          }

          .flow-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;

            &.status-active {
              background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
              color: #ffffff;
            }

            &.status-draft {
              background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
              color: #ffffff;
            }
          }
        }

        .canvas-actions {
          display: flex;
          gap: 8px;
        }
      }

      .canvas {
        flex: 1;
        position: relative;
        overflow: hidden;
        background: #ffffff;

        .canvas-grid {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            radial-gradient(circle, #d9d9d9 1px, transparent 1px);
          background-size: 20px 20px;
          opacity: 0.3;
        }

        .flow-node {
          position: absolute;
          width: 150px;
          min-height: 80px;
          background: white;
          border: 2px solid #e8e8e8;
          border-radius: 8px;
          cursor: move;
          transition: all 0.2s ease;
          z-index: 10;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
          }

          &.active {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }

          .node-header {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #e8e8e8;
            border-radius: 6px 6px 0 0;

            i {
              font-size: 14px;
              color: #1890ff;
              margin-right: 8px;
            }

            .node-title {
              flex: 1;
              font-size: 13px;
              font-weight: 600;
              color: #262626;
            }

            .node-delete {
              opacity: 0;
              transition: opacity 0.2s ease;
            }
          }

          &:hover .node-delete {
            opacity: 1;
          }

          .node-body {
            padding: 12px;

            .node-description {
              font-size: 12px;
              color: #8c8c8c;
              line-height: 1.4;
            }
          }

          .connection-points {
            .input-point,
            .output-point {
              position: absolute;
              width: 8px;
              height: 8px;
              background: #1890ff;
              border: 2px solid white;
              border-radius: 50%;
              cursor: crosshair;
              transition: all 0.2s ease;

              &:hover {
                width: 12px;
                height: 12px;
                margin: -2px;
              }
            }

            .input-point {
              left: -6px;
              top: 50%;
              transform: translateY(-50%);
            }

            .output-point {
              right: -6px;
              top: 50%;
              transform: translateY(-50%);
            }
          }
        }

        .connections-layer,
        .temp-connection-layer {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          z-index: 5;

          .connection-line {
            fill: none;
            stroke: #1890ff;
            stroke-width: 2;
            pointer-events: stroke;
            cursor: pointer;

            &:hover {
              stroke-width: 3;
              stroke: #40a9ff;
            }
          }

          .temp-connection-line {
            fill: none;
            stroke: #d9d9d9;
            stroke-width: 2;
            stroke-dasharray: 5,5;
          }
        }
      }
    }

    // 右侧属性面板
    .property-panel {
      width: 300px;
      background: white;
      border-left: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;

      .panel-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
          font-size: 14px;
          font-weight: 600;
          color: #262626;
          margin: 0;
        }
      }

      .panel-body {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
      }
    }
  }
}
</style>
