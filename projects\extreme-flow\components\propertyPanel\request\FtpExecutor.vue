<template>
  <el-form :model="form" size="small" :rules="rules" ref="form" label-position="top">
      <el-form-item label="数据源" prop="connector">
        <el-select v-model="form.connector" style="width: 100%" >
          <el-option v-for="item in connectorList" :key="item.id" :label="item.name" :value="item.id">
            <div style="display: flex;justify-content: space-between;">
              <div>{{item.name}}</div>
              <div v-if="item.host">{{`${item.host}:${item.port}`}}</div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <div v-if="form.connectionInfo">
        <div>
          <label class="el-form-item__label">连接信息</label>
          <el-row :gutter="10">
            <el-col :span="18">
              <el-form-item prop="connectionInfo.host" :rules="{required: true, message: '请输入主机名', trigger: 'blur'}">
                <el-input v-model="form.connectionInfo.host" placeholder="127.0.0.1"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="connectionInfo.port" :rules="{required: true, message: '请输入端口号', trigger: 'blur'}">
                <el-input v-model.number="form.connectionInfo.port" type="number" placeholder="端口号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-form-item label="用户名" prop="connectionInfo.username" :rules="{required: true, message: '请输入用户名', trigger: 'blur'}">
          <el-input v-model="form.connectionInfo.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="connectionInfo.password" :rules="{required: true, message: '请输入密码', trigger: 'blur'}">
          <el-input v-model="form.connectionInfo.password" placeholder="请输入密码" type="password"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="操作" prop="command">
        <el-select v-model="form.command" style="width: 100%" >
          <el-option v-for="item in commands" :key="item.value" :label="item.label" :value="item.value">
              <div>{{item.describe}}</div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="远程工作目录" prop="remoteDirectory">
        <el-input v-model="form.remoteDirectory" ></el-input>
      </el-form-item>
      <el-form-item label="本地存储目录" prop="localDirectory" v-if="['put', 'get', 'mput', 'mget'].includes(form.command)"
        :rules="{required: true, message: '请输入本地存储目录', trigger: 'blur'}">
        <el-input v-model="form.localDirectory" ></el-input>
      </el-form-item>
      
      <div style="text-align: center;">
        <el-link type="primary" :underline="false" @click="() => higherOption = !higherOption">高级选项<i :class="higherOption ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" /></el-link>
      </div>
      <div v-show="higherOption">
      <el-form-item label="文件匹配模式" prop="filePattern">
        <el-input v-model="form.filePattern" ></el-input>
      </el-form-item>
      <el-form-item label="命令选项" prop="commandOptions">
        <el-input v-model="form.commandOptions" placeholder="-P 保留时间戳, -D 下载后删除, -R 递归，可组合使用" ></el-input>
      </el-form-item>
      <el-form-item label="文件编码" prop="controlEncoding">
        <el-select v-model="form.controlEncoding" style="width: 100%" allow-create filterable>
          <el-option label="UTF-8" value="UTF-8"></el-option>
          <el-option label="GBK" value="GBK"></el-option>
          <el-option label="ISO-8859-1" value="ISO-8859-1"></el-option>
          <el-option label="UTF-16" value="UTF-16"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="本地文件存在时的处理策略" prop="fileExistsMode" >
        <el-select v-model="form.fileExistsMode"  style="width: 100%" >
          <el-option v-for="item in fileExistsModes" :key="item.value" :label="item.label" :value="item.value">
              <div>{{item.describe}}</div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="自动创建本地文件夹" prop="autoCreateLocalDirectory">
        <el-switch v-model="form.autoCreateLocalDirectory" active-text="是" inactive-text="否"></el-switch>
      </el-form-item>
      </div>
  </el-form>
</template>
<script>
import {getIntegrationConnectorList} from '@extreme-flow/api/integration'
import { isEqual } from 'element-ui/src/utils/util';
export default {
  data() {
    return {
      higherOption: false,
      form: {
        connector: 'custom',
        filePattern: '*.*',
        controlEncoding: 'UTF-8',
        command: 'get',
        autoCreateLocalDirectory: true
      },
      rules: {
        'connector': [
          { required: true, message: '请选择连接器', trigger: 'change' }
        ],
        'command': [
          { required: true, message: '请选择操作', trigger: 'change' }
        ],
        'remoteDirectory': [
          { required: true, message: '请输入远程工作目录', trigger: 'blur' }
        ],
      },
      connectorList: [],
      fileExistsModes:[
        { label: '追加', value: 'APPEND', describe: '每次追加后都会关闭文件' },
        { label: '追加不刷新', value: 'APPEND_NO_FLUSH', describe: '每次追加后不立即刷新/关闭文件(高性能连续追加写入)' },
        { label: '失败', value: 'FAIL', describe: '如果要写入的文件已存在，则失败' },
        { label: '跳过', value: 'IGNORE', describe: '如果要写入的文件已存在，则跳过' },
        { label: '替换', value: 'REPLACE', describe: '如果要写入的文件已存在，则替换' },
        { label: '有条件替换', value: 'REPLACE_IF_MODIFIED', describe: '只有当最后修改时间不同时才替换文件' }
      ],
      commands: [
        { label: '文件下载', value: 'get', describe: '下载指定文件到本地' },
        { label: '多文件下载', value: 'mget', describe: '批量下载多个文件' },
        { label: '查看文件列表', value: 'ls', describe: '列出远程服务器上的文件和目录详细信息' },
        { label: '查看文件名称列表', value: 'nlst', describe: '仅列出远程文件的名称' },
        { label: '上传', value: 'put', describe: '将本地文件上传到远程系统'},
        { label: '多文件上传', value: 'mput', describe: '将多个本地文件上传到远程系统' },
        { label: '删除文件', value: 'rm', describe: '删除远程文件（支持通配符路径）' },
        { label: '移动/重命名', value: 'mv', describe: '移动（重命名）远程文件' }
      ],
      editor: null
    }
  },
  mounted() {
    this.getConnectors()
  },

  methods: {
    getConnectors() {
      this.connectorList.push({
        id: 'custom',
        name: '自定义'
      })
      getIntegrationConnectorList({size: -1, type: 'FTP', enabled:true}).then(({records})=>{
        this.connectorList = records
        this.connectorList.push({
          id: 'custom',
          name: '自定义'
        })
      })
    },
    getProperties() {
      return this.form
    },
    async validate() {
      return new Promise(resolve => {
          this.$refs.form.validate(valid => {
              resolve(valid)
          })
      })
    }
  },
  computed: {
    sql() {
      return this.form.query || this.form.update || ''
    }
  },
  props: {
    properties: {
        type: Object,
        default: () => ({})
    },
    node: {
        type: Object,
        default: () => ({})
    }
  },
  watch: {
    "form.connector": {
      handler(val) {
        if (val === 'custom') {
          this.form.connectionInfo = {
            port: 21
          }
        } else {
          delete this.form.connectionInfo
        }
      },
      immediate: true
    },
    properties: {
        handler(newVal) {
            if (!isEqual(newVal, this.form)) {
                this.form = { ...this.form, ...newVal }
            }
        },
        immediate: true,
        deep: true
    },
  },
}
</script>