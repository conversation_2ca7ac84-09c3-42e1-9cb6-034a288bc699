export default {
  api:'sys/position',
  search: {
    isShow: true,
    showReset: true
  },
  table: {},
  formRule:[
    {
      type: "input",
      field: "code",
      className: "code-dom",
      title: "标识",
      isSearch:true,
      isSearchCol:{md:{span:7}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 18}},
      props:{
        placeholder:"请输入标识",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"标识不能为空"
        }
      ]
    },
    {
      type: "input",
      field: "name",
      className: "name-dom",
      title: "角色名",
      isSearch:true,
      isSearchCol:{md:{span:7}},
      isSearchValidate: [],
      isTable:true,
      col:{md:{span: 18}},
      props:{
        placeholder:"请输入角色名",
        readonly: false,
        clearable: true,
        disabled: false
      },
      validate:[
        {
          trigger:"blur",
          required:true,
          message:"角色名字不能为空"
        }
      ]
    },
    {
      type:"tree",
      title:"菜单权限",
      field:"resourceIds",
      isTable: false,
      value:null,
      props:{
        showCheckbox:true,
        checkStrictly:true,
        data:[],
        props: {
          "label": "name"
        }
      }
    },
    {
      type: "select",
      field: "valid",
      className: "isValid-dom",
      title: "是否有效",
      isSearch: true,
      isSearchValidate: [],
      isSearchCol: {md: {span: 7}},
      options: [
        {value: true, label: "正常", disabled: false},
        {value: false, label: "停用", disabled: false}
      ],
      isTable: true,
      col: {md: {span: 18}},
      props: {
        multiple: false,
        placeholder: "请选择是否有效",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {
          trigger: 'change',
          required: true,
          message: "请选择是否有效"
        }
      ],
    },
    {
      type: "input",
      field: "remark",
      className: "remark-dom",
      title: "备注",
      isSearch:false,
      isTable:false,
      col:{md:{span: 18}},
      props:{
        type: "textarea",
        placeholder:"请输入备注",
        readonly: false,
        clearable: true,
        disabled: false
      },
    },

  ]
}
