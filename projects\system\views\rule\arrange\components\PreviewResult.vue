<template>
  <div class="preview-result-container">
    <div class="result-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-section">
            <div class="info-item">
              <span class="label">编排链ID:</span>
              <span class="value">{{ result.chainId }}</span>
            </div>
            <div class="info-item">
              <span class="label">执行状态:</span>
              <span class="value" :class="getStatusClass(result.executionContext.status)">
                {{ result.executionContext.status }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">开始时间:</span>
              <span class="value">{{ formatTime(result.executionContext.startTime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">结束时间:</span>
              <span class="value">{{ formatTime(result.executionContext.endTime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">执行时长:</span>
              <span class="value">{{ getExecutionDuration() }}ms</span>
            </div>
            <div v-if="result.executionContext.errorMessage" class="info-item error-message">
              <span class="label">错误信息:</span>
              <span class="value">{{ result.executionContext.errorMessage }}</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 输入输出 -->
        <el-tab-pane label="输入/输出" name="io">
          <div class="io-container">
            <div class="io-section">
              <div class="section-title">输入参数</div>
              <div class="code-block">
                <pre>{{ formatJson(result.inputParams) }}</pre>
              </div>
            </div>
            <div class="io-section">
              <div class="section-title">输出结果</div>
              <div class="code-block">
                <pre>{{ formatJson(result.executionContext.outputData) }}</pre>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 执行详情（合并执行步骤和节点日志） -->
        <el-tab-pane label="执行详情" name="execution">
          <div class="execution-container">
            <template v-if="result.executionContext.executionSteps && result.executionContext.executionSteps.length > 0">
              <el-timeline>
                <el-timeline-item
                  v-for="(step, index) in result.executionContext.executionSteps"
                  :key="index"
                  :type="getStepType(step)"
                  :color="getStepColor(step)"
                  :timestamp="formatTime(step.startTime)"
                >
                  <div class="step-content">
                    <div class="step-header">
                      <span class="step-name">{{ step.nodeName || `节点: ${step.nodeId}` }}</span>
                      <el-tag size="mini" :type="step.success ? 'success' : 'danger'">
                        {{ step.success ? '成功' : '失败' }}
                      </el-tag>
                    </div>
                    
                    <div class="step-details">
                      <div class="step-detail-item">
                        <span class="detail-label">节点ID:</span>
                        <span class="detail-value">{{ step.nodeId }}</span>
                      </div>
                      <div class="step-detail-item">
                        <span class="detail-label">开始时间:</span>
                        <span class="detail-value">{{ step.startTime }}</span>
                      </div>
                      <div class="step-detail-item">
                        <span class="detail-label">结束时间:</span>
                        <span class="detail-value">{{ step.endTime }}</span>
                      </div>
                      <div class="step-detail-item">
                        <span class="detail-label">执行耗时:</span>
                        <span class="detail-value">{{ step.timeSpent }}ms</span>
                      </div>
                      <div class="step-detail-item">
                        <span class="detail-label">执行线程:</span>
                        <span class="detail-value">{{ step.threadName }}</span>
                      </div>
                      <div v-if="step.stepData" class="step-detail-item step-data">
                        <span class="detail-label">步骤数据:</span>
                        <div class="detail-value code-block">
                          <pre>{{ formatJson(step.stepData) }}</pre>
                        </div>
                      </div>
                      
                      <!-- 节点日志部分 -->
                      <div class="step-detail-item step-logs" v-if="hasNodeLogs(step.nodeId)">
                        <div class="logs-header">
                          <span class="detail-label">节点日志:</span>
                          <el-button 
                            type="text" 
                            size="mini" 
                            @click="toggleNodeLogs(step.nodeId)"
                          >
                            {{ isNodeLogsExpanded(step.nodeId) ? '收起' : '展开' }}
                            <i :class="isNodeLogsExpanded(step.nodeId) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                          </el-button>
                        </div>
                        <div v-show="isNodeLogsExpanded(step.nodeId)" class="logs-content">
                          <div 
                            v-for="(log, logIndex) in getNodeLogs(step.nodeId)" 
                            :key="logIndex" 
                            class="log-item" 
                            :class="getLogClass(log)"
                          >
                            {{ log }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </template>
            <div v-else class="empty-execution">
              <i class="el-icon-warning-outline"></i>
              <span>暂无执行步骤记录</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 全局变量 -->
        <el-tab-pane label="全局变量" name="variables">
          <div class="variables-container">
            <div v-if="Object.keys(result.executionContext.globalVariables).length > 0" class="code-block">
              <pre>{{ formatJson(result.executionContext.globalVariables) }}</pre>
            </div>
            <div v-else class="empty-variables">
              <i class="el-icon-warning-outline"></i>
              <span>暂无全局变量</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 原始数据 -->
        <el-tab-pane label="原始数据" name="raw">
          <div class="raw-container">
            <div class="code-block">
              <pre>{{ formatJson(result) }}</pre>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PreviewResult',
  
  props: {
    result: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      activeTab: 'basic',
      expandedNodeLogs: {} // 用于跟踪每个节点日志的展开状态
    }
  },
  
  methods: {
    formatJson(json) {
      try {
        return JSON.stringify(json, null, 2);
      } catch (e) {
        return JSON.stringify(json);
      }
    },
    
    formatTime(timestamp) {
      if (!timestamp) return '无';
      
      const date = new Date(timestamp);
      return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}.${String(date.getMilliseconds()).padStart(3, '0')}`;
    },
    
    getExecutionDuration() {
      const { startTime, endTime } = this.result.executionContext;
      if (!startTime || !endTime) return '未完成';
      return endTime - startTime;
    },
    
    getStatusClass(status) {
      if (!status) return '';
      
      switch (status.toUpperCase()) {
        case 'RUNNING':
          return 'status-running';
        case 'COMPLETED':
          return 'status-completed';
        case 'FAILED':
          return 'status-failed';
        default:
          return '';
      }
    },
    
    getLogClass(log) {
      if (log.includes('[ERROR]')) return 'log-error';
      if (log.includes('[WARN]')) return 'log-warn';
      if (log.includes('[INFO]')) return 'log-info';
      if (log.includes('[DEBUG]')) return 'log-debug';
      return '';
    },
    
    getStepType(step) {
      if (step.status === 'FAILED' || step.success === false) return 'danger';
      if (step.status === 'COMPLETED' || step.success === true) return 'success';
      return 'primary';
    },
    
    getStepColor(step) {
      if (step.status === 'FAILED' || step.success === false) return '#F56C6C';
      if (step.status === 'COMPLETED' || step.success === true) return '#67C23A';
      return '#409EFF';
    },
    
    // 检查节点是否有日志
    hasNodeLogs(nodeId) {
      return this.result.executionContext.nodeLogs && 
             this.result.executionContext.nodeLogs[nodeId] && 
             this.result.executionContext.nodeLogs[nodeId].length > 0;
    },
    
    // 获取节点日志
    getNodeLogs(nodeId) {
      return this.result.executionContext.nodeLogs[nodeId] || [];
    },
    
    // 切换节点日志展开/折叠状态
    toggleNodeLogs(nodeId) {
      this.$set(this.expandedNodeLogs, nodeId, !this.expandedNodeLogs[nodeId]);
    },
    
    // 检查节点日志是否展开
    isNodeLogsExpanded(nodeId) {
      return !!this.expandedNodeLogs[nodeId];
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-result-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .result-content {
    flex: 1;
    overflow: auto;
    padding: 0;
    height: 100%;
    
    ::v-deep .el-tabs {
      height: 100%;
      
      .el-tabs__content {
        padding: 20px;
        height: calc(100% - 40px);
        overflow: auto;
      }
    }
  }
  
  .info-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .info-item {
      display: flex;
      align-items: flex-start;
      
      .label {
        width: 100px;
        color: #606266;
        font-weight: 500;
      }
      
      .value {
        flex: 1;
        color: #303133;
        
        &.status-running {
          color: #E6A23C;
          font-weight: 500;
        }
        
        &.status-completed {
          color: #67C23A;
          font-weight: 500;
        }
        
        &.status-failed {
          color: #F56C6C;
          font-weight: 500;
        }
      }
      
      &.error-message {
        .value {
          color: #F56C6C;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
    }
  }
  
  .io-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    
    .io-section {
      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        margin-bottom: 8px;
      }
    }
  }
  
  .code-block {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 12px;
    overflow: auto;
    max-height: 300px;
    
    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
      font-size: 13px;
      line-height: 1.5;
      color: #303133;
    }
  }
  
  .execution-container {
    .step-content {
      padding: 8px 0;
      
      .step-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .step-name {
          font-weight: 500;
          font-size: 15px;
          color: #303133;
        }
      }
      
      .step-details {
        background-color: #f8f8f8;
        border-radius: 4px;
        padding: 10px;
        
        .step-detail-item {
          display: flex;
          margin-bottom: 6px;
          font-size: 13px;
          
          .detail-label {
            width: 80px;
            color: #606266;
            font-weight: 500;
          }
          
          .detail-value {
            flex: 1;
            color: #303133;
          }
          
          &.step-data,
          &.step-logs {
            flex-direction: column;
            
            .detail-label {
              margin-bottom: 6px;
            }
            
            .detail-value {
              margin-left: 0;
            }
          }
          
          &.step-logs {
            margin-top: 12px;
            border-top: 1px dashed #e0e0e0;
            padding-top: 12px;
            
            .logs-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
              
              .detail-label {
                width: auto;
                margin-bottom: 0;
              }
            }
            
            .logs-content {
              background-color: #f0f0f0;
              border-radius: 4px;
              padding: 8px;
              max-height: 200px;
              overflow: auto;
              
              .log-item {
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
                font-size: 12px;
                padding: 3px 0;
                white-space: pre-wrap;
                word-break: break-word;
                
                &.log-error {
                  color: #F56C6C;
                }
                
                &.log-warn {
                  color: #E6A23C;
                }
                
                &.log-info {
                  color: #409EFF;
                }
                
                &.log-debug {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }
    
    .empty-execution {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      color: #909399;
      
      i {
        font-size: 32px;
        margin-bottom: 16px;
      }
    }
  }
  
  .empty-variables {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #909399;
    
    i {
      font-size: 32px;
      margin-bottom: 16px;
    }
  }
}
</style> 