<template>
    <div class="editor" style="border: 1px solid #ccc;">
        <div ref="toolbar" class="toolbar">
        </div>
        <div ref="editor" class="text">
        </div>
    </div>
</template>

<script>
import E from 'wangeditor'
import { getToken } from '@/utils/auth'
export default {
    props: {
        content: {
            type: String,
            required: true,
            default: ''
        },
        disable: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            editor: null,
        }
    },
    watch: {

        disable: {
            handler: function (n) {
                if (n) {
                    this.editor && this.editor.disable(); // 禁用编辑器
                } else {
                    this.editor && this.editor.enable(); // 启用编辑器
                }
            },
            immediate: true,
            deep: true
        },
        content(value) {
            if (value !== this.editor.txt.html()) {
                this.editor.txt.html(value)
                this.$emit("onContentChange", value)
            }
        }
    },
    mounted() {
        this.createEditor()
        this.editor.txt.html(this.content)
    },
    beforeDestroy() {
        this.editor && this.editor.destroy()
        this.editor = null
    },
    methods: {

        createEditor() {
            this.editor = new E(this.$refs.toolbar, this.$refs.editor)
            this.editor.config.menuTooltipPosition = 'down'
            // this.editor.config.uploadImgShowBase64 = false // base 64 存储图片
            this.editor.config.uploadFileName = 'file' // 后端接受上传文件的参数名
            // this.editor.config.uploadImgMaxSize = 10 * 1024 * 1024 // 将图片大小限制为 2M
            // this.editor.config.uploadImgMaxLength = 1 // 限制一次最多上传 3 张图片
            this.editor.config.uploadImgTimeout = 3 * 60 * 1000 // 设置超时时间
            // this.editor.config.uploadImgHeaders = {
            //     Authorization: localStorage.getItem('Authorization') // 设置请求头
            // }
            // 配置菜单
            this.editor.config.menus = this.editor.config.menus.filter(f => !['fullScreen', 'video'].includes(f))
            // this.editor.config.menus = [
            //     'head', // 标题
            //     'bold', // 粗体
            //     'fontSize', // 字号
            //     'fontName', // 字体
            //     'italic', // 斜体
            //     'underline', // 下划线
            //     'strikeThrough', // 删除线
            //     'foreColor', // 文字颜色
            //     'backColor', // 背景颜色
            //     'link', // 插入链接
            //     'list', // 列表
            //     'justify', // 对齐方式
            //     'quote', // 引用
            //     'emoticon', // 表情
            //     'image', // 插入图片
            //     'table', // 表格
            //     //'video', // 插入视频
            //     'code', // 插入代码
            //     'undo', // 撤销
            //     'redo', // 重复
            //     // 'fullScreen' // 全屏
            // ]
            this.editor.config.customAlert = function () {
            }
            this.editor.config.customUploadImg = function (files, insertImgFn) {

                const axios = require("axios");
                const FormData = require("form-data");
                for (let i = 0; i < files.length; i++) {
                    const data = new FormData();
                    const file = files[i];
                    data.append('file', file);
                    const config = {
                        method: "post",
                        url: process.env.VUE_APP_BASE_API + '/file/minio/uploadFile', //上传图片地址
                        headers: {
                            "Content-Type": "multipart/form-data",
                            'authorization': getToken(),
                        },
                        data: data
                    };
                    axios(config).then(function (res) {
                        let url = process.env.VUE_APP_FILE_URL + res.data.data //拼接成可浏览的图片地址
                        insertImgFn(url, url.substring(url.lastIndexOf('/') + 1), url) //插入图片
                    }).catch(function (error) {
                        console.log(error);
                    });
                }

            }

            this.editor.config.onchange = (html) => {
                this.$emit('onContentChange', html) // 将内容同步到父组件中
            }
            // 创建富文本编辑器
            this.editor.create()
        }
    }
}
</script>
<style lang="css" scoped>
.toolbar {
    border-bottom: 1px solid #ccc;
}

.text {
    height: 400px;
}
</style>
