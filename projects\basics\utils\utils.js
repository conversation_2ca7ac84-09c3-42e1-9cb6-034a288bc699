/**
 * 查找当前路由信息
 * 
 */
import store from '@/store'
export const findCurrentRouteInfo = (id, list = store.state.permission.sidebarRouters, type) => {
    let res = list.filter((item) => item.name && item.name == id && (item.type == 'MENU' || type == 'MENU'));
    if (res && res.length) {
        return res
    } else {
        for (let i = 0; i < list.length; i++) {
            if (list[i].children && list[i].children.length > 0) {
                res = findCurrentRouteInfo(id, list[i].children, list[i].type);
                if (res) return res;
            }
        }
    }
}

/**
 * 时间格式化，
 * 当前时间getBeforeDate(0)
 * 前七天getBeforeDate(7)
*/
export const getBeforeDate = (n, type) => {
    var n = n
    var d = new Date()
    var year = d.getFullYear()
    var mon = d.getMonth() + 1
    var day = d.getDate()
    if (day <= n) {
        if (mon > 1) {
            mon = mon - 1
        } else {
            year = year - 1
            mon = 12
        }
    }
    d.setDate(d.getDate() - n)
    year = d.getFullYear()
    mon = d.getMonth() + 1
    day = d.getDate()
    var s = year + "-" + (mon < 10 ? ('0' + mon) : mon) + "-" + (day < 10 ? ('0' + day) : day)
    if (type === 'date') {
        return s
    } else {
        return s += ' 00:00:00'
    }
}

/**
 * 获取当前时间 yyy-mm-dd hh:mm:ss
*/
export const getCurrentTime = () => {
    var gettime = ''
    var yy = new Date().getFullYear()
    var mm = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
    var dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
    var hh = new Date().getHours()
    var mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
    var ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
    gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss
    return gettime
}

export function getFileUrl(uri) {
    if (uri) {
        uri = uri.indexOf('/') === 0 ? uri : '/' + uri
        return process.env.VUE_APP_FILE_URL + uri
    } else {
        return ''
    }
}

//月份转成中文
export function NoToChineseMonth(mon) {
    var AA = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九')
    var num = mon.toString().split('')
    if (num.length > 1) {
        var arr = ''
        if (num[1] == 0) {
            arr = ''
        } else {
            arr = AA[num[1]]
        }
        return '十' + arr
    } else {
        return AA[num[0]]
    }
}

//阿拉伯数字转中文数字
export function NoToChinese(num) {
    if (!/^\d*(\.\d*)?$/.test(num)) {
        return ''
    }
    var AA = new Array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九')
    var BB = new Array('', '十', '百', '千', '万', '亿', '点', '')
    var a = ('' + num).replace(/(^0*)/g, '').split('.')
    var k = 0
    var re = ''
    for (var i = a[0].length - 1; i >= 0; i--) {
        switch (k) {
            case 0:
                re = BB[7] + re;
                break;
            case 4:
                if (!new RegExp("0{4}\\d{" + (a[0].length - i - 1) + "}$").test(a[0]))
                    re = BB[4] + re;
                break;
            case 8:
                re = BB[5] + re;
                BB[7] = BB[5];
                k = 0;
                break;
        }
        if (k % 4 == 2 && a[0].charAt(i + 2) != 0 && a[0].charAt(i + 1) == 0) re = AA[0] + re;
        if (a[0].charAt(i) != 0) re = AA[a[0].charAt(i)] + BB[k % 4] + re;
        k++;
    }
    if (a.length > 1) //加上小数部分(如果有小数部分)
    {
        re += BB[6];
        for (var i = 0; i < a[1].length; i++) re += AA[a[1].charAt(i)];
    }
    return re;
}


export function chunk(array, size) {
    //获取数组的长度，如果你传入的不是数组，那么获取到的就是undefined
    const length = array.length
    //判断不是数组，或者size没有设置，size小于1，就返回空数组
    if (!length || !size || size < 1) {
        return []
    }
    //核心部分
    let index = 0 //用来表示切割元素的范围start
    let resIndex = 0 //用来递增表示输出数组的下标

    //根据length和size算出输出数组的长度，并且创建它。
    let result = new Array(Math.ceil(length / size))
    //进行循环
    while (index < length) {
        //循环过程中设置result[0]和result[1]的值。该值根据array.slice切割得到。
        result[resIndex++] = array.slice(index, (index += size))
    }
    //输出新数组
    return result
}
