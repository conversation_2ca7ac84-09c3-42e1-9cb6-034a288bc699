import store from '@/store'

const all_permission = "*:*:*";

export default {
  inserted(el, binding, vnode) {
    const { value } = binding

    if (!hasPermi(value)) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
}

export function hasPermi(value) {
  if (value && value instanceof Array && value.length > 0) {
    const permissionFlag = value
    const roles = store.getters && store.getters.roles
    if (roles.includes('admin')) return true
    const permissions = store.getters && store.getters.permissions
    var hasPermissions = permissions.some(permission => {
      return all_permission === permission || permissionFlag.includes(permission)
    })
    // hasPermissions = true
    return hasPermissions
  } else {
    throw new Error(`请设置操作权限标签值`)
  }
}
