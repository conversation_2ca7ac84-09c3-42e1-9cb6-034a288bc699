<template>
  <div id="app">
    <!-- <nav class="nav-bar">
      <router-link to="/chat">AI问答</router-link> |
      <router-link to="/knowledge">知识库管理</router-link> |
      <router-link to="/kb-chat">知识库对话</router-link>
    </nav> -->
    <!-- <div class="main-container"> -->
      <router-view></router-view>
    <!-- </div> -->
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
#app {
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.nav-bar {
  padding: 20px;
  background-color: #f8f9fa;
  text-align: center;
}

.nav-bar a {
  color: #2c3e50;
  text-decoration: none;
  margin: 0 10px;
}

.nav-bar a.router-link-active {
  color: #409eff;
  font-weight: bold;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

@media screen and (max-width: 768px) {
  .main-container {
    margin: 0 auto;
    padding: 0;
    max-width: unset;
  }
}
</style>
