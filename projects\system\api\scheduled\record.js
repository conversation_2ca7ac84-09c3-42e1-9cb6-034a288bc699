import request from '@/utils/request'

const api = '/scheduled/scheduled/record'

export function list(params) {
    return request({
        url: `${api}/list`,
        params
    })
}
export function save(data) {
    return request({
        url: `${api}`,
        method: 'post',
        data
    })
}

export function update(data) {
    return request({
        url: `${api}`,
        method: 'put',
        data
    })
}

export function remove(ids) {
    return request({
        url: `${api}/${ids}`,
        method: 'delete',
        data
    })
}
export function groupNames(params) {
    return request({
        url: `${api}/groupNames`,
        params
    })
}
export function count(params) {
    return request({
        url: `${api}/count`,
        params
    })
}

export default {
    list,
    save,
    update,
    remove,
    groupNames,
    count
}