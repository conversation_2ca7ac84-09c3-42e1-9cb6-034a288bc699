<template>
    <div id="default-print" class="print default-print">
        <h2>秭归县职业教育中心差旅审批单</h2>
        <div class="tit">(第二联:报销)</div>
        <div class="header">
            <div style="padding-left:0.6rem">前往单位：{{ instance.formData[formItems.find(m=>m.title == '前往单位')?.id] }}</div>
            <!-- <div>{{ instance.startTime.split(' ')[0] }}</div> -->
        </div>
        <img class="result" v-if="status.img" :src="status.img" />
        <div class="content">
            <table border="0">
                <tr>
                    <th>出差事由</th>
                    <td :colspan="3">{{instance.formData[formItems.find(m=>m.title == '出差事由')?.id]}}</td>
                </tr>
                <tr>
                    <th :rowspan="peoples.length + 1">出差人员</th>
                    <td style=" text-align: center;">姓名</td>
                    <td :colspan="2" style=" text-align: center;">职务</td>
                </tr>
                <tr v-for="(item,index) in peoples" :key="index">
                    <td>{{item.name}}</td>
                    <td :colspan="2">{{item.name?'无':''}}</td>
                </tr>
                <tr>
                    <th>预计天数</th>
                    <td style=" text-align: center;" :colspan="3">{{day}}天</td>
                </tr>
                <tr>
                    <th>行程安排</th>
                    <td :colspan="3">
                        <div>出发时间：{{instance?.formData[formItems.find((m) => m.title == '出差时间')?.id][0].split(" ")[0] || ''}}</div>
                        <div style="padding:10px 0">返回时间：{{instance?.formData[formItems.find((m) => m.title == '出差时间')?.id][1].split(" ")[0] || ''}}</div>
                        <div>{{instance?.formData[formItems.find((m) => m.title == '行程安排')?.id] || ''}}</div>
                    </td>
                </tr>
                <tr>
                    <th>出行方式</th>
                    <td :colspan="3">
                        <!-- <el-checkbox-group v-model="instance.formData[formItems.find(m=>m.title == '出行方式')?.id]" class="print-checkbox" disabled>
              <el-checkbox v-for="(item,index) in formItems.find(m=>m.title == '出行方式')?.props.options" :key="index" :label="item"></el-checkbox>
                        </el-checkbox-group>-->
                        <div class="flex flex-wrap">
                            <div v-for="(item,index) in formItems.find(m=>m.title == '出行方式')?.props.options" :key="index" class="goWay">
                                <div class="kuang">
                                    <div :class="instance.formData[formItems.find(m=>m.title == '出行方式')?.id] == item?'act':''"></div>
                                </div>
                                {{item}}
                            </div>
                        </div>

                        <!-- {{instance.formData[formItems.find(m=>m.title == '出行方式')?.id]?.join() || []}} -->
                    </td>
                </tr>
                <tr>
                    <th>部门负责人审核</th>
                    <td style="width:37%">
                        <div class="audit-info">
                            <div>签字：</div>
                            <div>{{progress[0]?.comment[0]?.text }}</div>
                            <div>盖章：</div>
                            <!-- <div>{{progressName('派出部门负责人')?.finishTime}}</div> -->
                            <div>{{instance.formData[formItems.find(m=>m.title == '部门负责人审核时间')?.id] ||'' }}</div>
                            <img class="sign" v-if="progress[0]?.signature" :src="progress[0]?.signature" />
                        </div>
                    </td>

                    <th>单位领导审批</th>
                    <td>
                        <div class="audit-info">
                            <div>签字：</div>
                            <div>{{progress[progress.length-1]?.comment[0]?.text == '自动处理：任务去重自动通过'?progress[progress.length-1]?.comment[0]?.text :progress[progress.length-1]?.comment[0]?.text }}</div>
                            <div>盖章：</div>
                            <div>{{instance?.formData[formItems.find((m) => m.title == '单位领导审批时间')?.id] || ''}}</div>
                            <img class="sign" v-if="progress[progress.length-1]?.signature" :src="progress[progress.length-1]?.signature" />
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <!-- <div class="footer">
      <div>打印人：{{ loginUser.name }}</div>
      <div>打印时间：{{ moment().format('yyyy-MM-DD HH:mm:ss') }}</div>
        </div>-->
    </div>
</template>

<script>
import moment from 'moment'
import DefaultPrintItem from './DefaultPrintFormItem'
import QRCode from 'qrcodejs2'

export default {
    name: 'DefaultPrinterBusinessTrip',
    components: { DefaultPrintItem },
    props: {
        instance: {
            required: true,
        },
        status: {
            required: true,
        },
    },
    computed: {
        loginUser() {
            return this.$store.state.bpm.loginUser
        },
        process() {
            let userNodes = this.instance.progress.filter((p) => p.nodeType === 'APPROVAL')
            let userTask = []
            userNodes.forEach((un) => {
                if (Array.isArray(un.users)) {
                    userTask.push(...un.users.map((u) => this.getTask(u)))
                } else {
                    userTask.push(this.getTask(un))
                }
            })
            return userTask
        },
        formItems() {
            let result = []
            this.getItems(this.instance.formItems, result)
            console.log('formItems', result)
            return result
        },
        peoples() {
            let array = this.instance?.formData[this.formItems.find((m) => m.title == '出差人员')?.id] || []
            if (array.length < 10) {
                // 计算需要补全的数量
                const fillCount = 10 - array.length

                // 使用 Array.prototype.concat() 方法来补全数组
                array = array.concat(Array(fillCount).fill({ name: null }))
            }
            console.log('array', array)
            return array
        },
    },
    data() {
        return {
            moment,
            day: null,
            progress: [],
        }
    },
    mounted() {
        console.log('instance', this.instance)
        // this.showQrCode()
        this.getDay()
        this.progress = this.instance.progress.filter((m) => m.nodeType == 'APPROVAL')
        this.progress = this.progress.reduce((acc, current) => {
            if (!acc.some((item) => item.user.id === current.user.id)) {
                acc.push(current)
            }
            return acc
        }, [])
    },
    methods: {
        getDay() {
            let data = this.instance?.formData[this.formItems.find((m) => m.title == '出差时间')?.id] || ['', '']
            // const date1 = new Date(data[0])
            // const date2 = new Date(data[1])
            const [startDate, endDate] = data.map((dateStr) => new Date(dateStr))
            // 去掉时分秒
            startDate.setHours(0, 0, 0, 0)
            endDate.setHours(0, 0, 0, 0)
            const diffTime = endDate - startDate
            const diffDays = diffTime / (1000 * 3600 * 24)

            this.day = Math.max(1, diffDays + 1);
        },
        showQrCode() {
            new QRCode(this.$refs.qrCode, {
                text: `${window.location.origin}${process.env.BASE_URL}mbInstance?instanceId=${this.instance.instanceId}`,
                width: 90,
                height: 90,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.L,
            })
        },
        getItems(items, collects) {
            items.forEach((item) => {
                if (item.name === 'SpanLayout') {
                    this.getItems(item.props.items, collects)
                } else if (item.props.enablePrint) {
                    collects.push(item)
                }
            })
        },
        getTask(item) {
            return {
                userName: item.user.name,
                result: this.getResult(item.result),
                signature: item.signature,
                finishTime: item.finishTime ? item.finishTime.substring(0, 16) : '----',
                comment: item.comment.length > 0 ? item.comment[0].text : '',
            }
        },
        getResult(res) {
            switch (res) {
                case 'complete':
                    return '已办理'
                case 'agree':
                    return '已同意'
                case 'refuse':
                    return '已拒绝'
                case 'recall':
                    return '已退回'
                default:
                    return '--'
            }
        },
    },
}
</script>

<style lang="scss" scoped>
@import '../../../default-print.scss';
@media print {
    * {
        letter-spacing: normal !important;
        font-size: 7px !important;
    }

    .print {
        font-family: 宋体;
        position: relative;

        .result {
            position: absolute;
            width: 100px;
            height: 100px;
            right: 30px;
            top: 10px;
        }

        .qr-code {
            position: absolute;
            text-align: center;
            top: 0;
            right: 0;
        }
    }

    h2 {
        text-align: center;
        font-size: 13px !important;
        text-shadow: 0.15pt 0px 0px black, 0.25pt 0px 0px black, 0.35pt 0px 0px black, -0.25pt 0px 0px black, 0px 0.25pt 0px black,
            0px -0.25pt 0px black;
    }

    .tit {
        text-align: center;
        margin-top: -10px;
        font-size: 5px !important;
    }

    .header {
        // display: flex;
        // justify-content: space-between;
        font-size: 8px !important;
        margin-top: 15px;

        div {
            padding-left: 40px;
        }
    }

    table {
        font-size: 10px !important;
        width: 100%;
        border-collapse: collapse;
        padding: 2px;

        th {
            width: 13%;
        }
    }

    table tr th,
    table tr td {
        text-align: left;
        border: 0.1px solid #464648;
        padding: 1px 5px;
        height: 20px;
        font-size: 7px !important;
    }

    .footer {
        font-size: 8px !important;
        margin-top: 10px;

        div {
            display: inline-block;
            width: 50%;
        }

        div:last-child {
            text-align: right;
        }
    }

    .process-list {
        .base-info {
            position: relative;

            & > div {
                position: absolute;
            }

            div:nth-child(3) {
                right: 0;
            }

            div:nth-child(2) {
                right: 200px;
            }

            .sign {
                position: absolute;
                left: 50px;
                width: 110px;
                height: 50px;
            }
        }

        .comment {
            margin-left: 10px;
            margin-top: 40px;
        }
    }

    .audit-info {
        height: 60px;
        position: relative;

        & > div {
            position: absolute;
        }

        div:nth-child(3) {
            bottom: 0;
            left: 0;
        }

        div:nth-child(2) {
            left: 20px;
            top: 10px;
        }

        div:nth-child(4) {
            bottom: 10px;
            right: 0;
            font-size: 7px !important;
        }

        img {
            position: absolute;
            left: 30px;
            width: 110px;
            height: 50px;
        }
    }
    ::v-deep .print-checkbox .el-checkbox .el-checkbox__label {
        font-size: 7px !important;
    }

    ::v-deep.el-checkbox .el-checkbox__input .el-checkbox__inner {
        width: 7px !important;
        height: 7px !important;
        margin-left: 20px;
    }

    .goWay {
        display: flex;
        margin-right: 15px;
        .kuang {
            width: 7px;
            height: 7px;
            border: 1px solid #464648;
            position: relative;
            margin-right: 5px;
        }

        .act {
            position: absolute;
            width: 3px;
            height: 5px;
            border-right: 1px solid #464648;
            border-bottom: 1px solid #464648;
            transform: rotate(40deg);
            left: 1px;
        }
    }
}
</style>
