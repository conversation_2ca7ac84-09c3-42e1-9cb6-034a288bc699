section.app-main>.auto_page,
section.app-main>*>.auto_page,
section.app-main>.app-container {
    margin: 24px;
    // padding: 0 !important;
    border: none;
    background: white;
    border-radius: 10px;

    >.el-header {
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
    }

    >.el-main {
        border-radius: 10px !important;
    }

    >.el-header+.el-main {
        border-top-right-radius: 0px !important;
        border-top-left-radius: 0px !important;
    }

    >.el-main:has(+ .el-footer) {
        border-bottom-right-radius: 0px !important;
        border-bottom-left-radius: 0px !important;
    }

    >.el-footer {
        border-bottom-right-radius: 10px;
        border-bottom-left-radius: 10px;
    }

    .el-container.is-vertical {
        background: white;
        border-radius: 10px;
    }


    .el-container {


        .el-header {
            border-top-right-radius: 10px;
            border-top-left-radius: 10px;
        }

        .el-aside,
        .el-main {
            border-radius: 10px !important;
        }

        .el-footer {
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
        }
    }
}

.el-table.el-table--fit.el-table--border {
    border-width: 1px;

    .el-table__fixed-right::before {
        display: none;
    }

    thead {
        tr {
            th:last-child {
                border-right: none;
            }
        }
    }
}

.sidebar-logo{
    color: #79b5fa !important;
}