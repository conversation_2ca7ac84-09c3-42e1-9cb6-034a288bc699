<template>
  <div class="notice-center">
    <el-page-header @back="goBack()" content="消息中心">
    </el-page-header>
    <!-- 页面头部 -->
    <div class="page-header" style="margin-top: 20px;">
      <div class="header-title">
        <span class="header-subtitle">查看和管理您的消息通知</span>
      </div>
    </div>

    <div class="notice-container">
      <!-- 左侧菜单栏 -->
      <div class="sidebar">
        <div class="menu-list">
          <div 
            class="menu-item" 
            :class="{ active: activeMenu === 'notice' }" 
            @click="handleChangeMenu('notice')"
          >
            <i class="el-icon-bell"></i>
            <span>通知中心</span>
            <el-badge v-if="unreadCount > 0" :value="unreadCount" class="menu-badge" />
          </div>
          <div class="menu-divider"></div>
          <div 
            class="menu-item" 
            :class="{ active: activeMenu === 'settings' }" 
            @click="handleChangeMenu('settings')"
          >
            <i class="el-icon-setting"></i>
            <span>消息设置</span>
          </div>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="content-area">
        <!-- 加载中状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton style="width: 100%" animated>
            <template slot="template">
              <el-skeleton-item variant="image" style="width: 100%; height: 60px; margin-bottom: 16px" />
              <el-skeleton-item variant="p" style="width: 100%; height: 16px; margin-bottom: 12px" />
              <el-skeleton-item variant="text" style="width: 90%; height: 12px; margin-bottom: 8px" />
              <el-skeleton-item variant="text" style="width: 80%; height: 12px; margin-bottom: 8px" />
              <el-skeleton-item variant="text" style="width: 70%; height: 12px; margin-bottom: 24px" />
              
              <el-skeleton-item variant="p" style="width: 100%; height: 16px; margin-bottom: 12px" />
              <el-skeleton-item variant="text" style="width: 90%; height: 12px; margin-bottom: 8px" />
              <el-skeleton-item variant="text" style="width: 80%; height: 12px; margin-bottom: 8px" />
              <el-skeleton-item variant="text" style="width: 70%; height: 12px; margin-bottom: 24px" />
            </template>
          </el-skeleton>
        </div>
        
        <!-- 通知中心内容 -->
        <div v-else-if="activeMenu !== 'settings'" class="notice-center-content">
          <!-- 消息列表组件 -->
          <notice-list-view 
            @unread-count-change="handleUnreadCountChange"
          />
        </div>
        
        <!-- 消息设置 -->
        <notice-settings-view 
          v-else-if="activeMenu === 'settings'"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { stack } from '@/router'
import NoticeSettingsView from './components/NoticeSettingsView.vue'
import NoticeListView from './components/NoticeListView.vue'

export default {
  name: 'NoticeCenter',
  components: {
    NoticeSettingsView,
    NoticeListView
  },
  data() {
    return {
      activeMenu: 'notice', // 默认打开通知中心
      // 加载状态
      loading: false,
      // 未读消息计数
      unreadCount: 0
    }
  },
  created() {
    // 初始化通知中心
    this.initNoticeCenter()
  },
  methods: {
    // 返回上一页
    goBack() {
      const stacks = stack()
      if (stacks.length > 0) {
        this.$router.go(-1)
      } else {
        this.$router.push('/')
      }
    },
    
    // 初始化通知中心
    async initNoticeCenter() {
      this.loading = true
      
      try {
        // 简单的延迟，让加载动画显示一会儿
        setTimeout(() => {
          this.loading = false
        }, 500)
      } catch (error) {
        this.$message.error('初始化通知中心失败')
        this.loading = false
      }
    },
    
    // 处理左侧菜单切换
    handleChangeMenu(menu) {
      this.activeMenu = menu
    },
    
    // 处理未读消息数量变化
    handleUnreadCountChange(count) {
      this.unreadCount = count
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-center {
  margin: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;

  .page-header {
    margin-bottom: 20px;

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 500;
        color: #303133;
      }

      .header-subtitle {
        font-size: 14px;
        color: #909399;
      }
    }
  }

  .notice-container {
    display: flex;
    margin-top: 24px;
    height: 760px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;

    // 左侧菜单栏
    .sidebar {
      width: 240px;
      background-color: #f8f9fb;
      border-right: 1px solid #e6e6e6;
      padding: 16px 0;
      flex-shrink: 0;

      .menu-list {
        .menu-item {
          padding: 16px 24px;
          display: flex;
          align-items: center;
          cursor: pointer;
          position: relative;
          color: #606266;
          transition: all 0.3s;

          i {
            font-size: 18px;
            margin-right: 12px;
            color: #909399;
            transition: all 0.3s;
          }

          span {
            flex: 1;
            font-size: 14px;
          }

          .menu-badge {
            margin-left: auto;
          }

          &:hover {
            background-color: #ecf5ff;
            color: #409EFF;

            i {
              color: #409EFF;
            }
          }

          &.active {
            background-color: #fff;
            color: #409EFF;
            font-weight: 500;
            
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 4px;
              background-color: #409EFF;
            }

            i {
              color: #409EFF;
            }
          }
        }

        .menu-divider {
          height: 1px;
          background-color: #ebeef5;
          margin: 12px 0;
        }
      }
    }

    // 右侧内容区
    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      overflow: hidden;

      .loading-container {
        padding: 20px;
        flex: 1;
      }

      .notice-center-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
}

::v-deep .el-badge__content {
  background-color: #F56C6C;
}

::v-deep .el-page-header__content {
  color: #303133;
}
</style>
