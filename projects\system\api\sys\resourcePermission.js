import request from '@/utils/request'

const sysRoleApi = CONSTANT.SYSTEM + '/sys/role'
const sysPermissionApi = CONSTANT.SYSTEM + '/sys/permission'

// 获取角色拥有的资源权限
export function getRoleResources(roleId) {
  return request({
    url: `${sysRoleApi}/resources`,
    method: 'get',
    params: { roleId }
  })
}

// 保存角色的资源权限
export function saveRoleResources(data) {
  return request({
    url: `${sysRoleApi}/resources`,
    method: 'post',
    data
  })
}

// 获取所有微服务模块
export function getAllModules() {
  return request({
    url: `${sysPermissionApi}/allModules`,
    method: 'get'
  })
}

// 获取网关路由列表
export function getGatewayRoutes() {
  return request({
    url:`gateway/route/list`,
    method: 'get',
    params: { size: -1 },
  })
}

// 获取模块的接口权限列表
export function getModulePermissions(modules, params) {
  return request({
    url: `${sysPermissionApi}/scan`,
    method: 'get',
    params: {
      modules,
      ...params
    },
    hideExceptionPrompt: true
  })
}

// 保存角色的接口权限
export function saveRolePermissions(data) {
  return request({
    url: `${sysRoleApi}/permissions`,
    method: 'post',
    data
  })
}
