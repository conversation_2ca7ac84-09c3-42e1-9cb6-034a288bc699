<template>
  <div>
    <el-form-item label="提示文字">
      <el-input size="small" v-model="value.placeholder" placeholder="请设置提示语"/>
    </el-form-item>
    <el-form-item label="选项模式">
      <el-radio-group v-model="value.fixed">
        <el-radio :label="true">固定选项</el-radio>
        <el-radio :label="false">远程加载</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="选项设置" class="options" v-if="value.fixed">
      <div slot="label">
        <span>选项设置（鼠标拖拽排序）</span>
        <el-button icon="el-icon-plus" type="text" size="mini"
                   @click="value.options.push({name: '', value: ''})">新增选项
        </el-button>
      </div>
      <draggable :list="value.options" group="option" handler=".el-icon-rank" :options="{animation: 300, sort: true}">
        <div v-for="(op, index) in value.options" :key="index" class="option-item">
          <el-input v-model="op.value" style="width: 100px;" size="small" placeholder="选项value值" clearable/>
          ~
          <el-input size="small" v-model="op.name" placeholder="选项名称" style="width: 130px;"/>
          <i class="el-icon-delete del-btn" @click="value.options.splice(index, 1)"></i>
        </div>
      </draggable>

    </el-form-item>

    <el-form-item label="配置数据源" v-else>
      <el-button icon="el-icon-link" size="small" @click="visible = true">编辑http数据源</el-button>
    </el-form-item>
    <el-form-item label="选项展开">
      <el-switch v-model="value.expanding"></el-switch>
    </el-form-item>
    <el-form-item label="多选模式" @change="modeChange">
      <el-switch v-model="value.multiple"></el-switch>
    </el-form-item>
    <w-dialog title="配置http数据源请求" closeFree width="600px" v-model="visible" @opened="loadHttp" @ok="httpOk">
      <http-req ref="http" :runtime="false" :show-tip="false" v-model="tempHttp">
        <template slot="aft">
          <el-radio-group v-model="tempHttp.aftHandler.isJs" style="margin-bottom: 10px">
            <el-radio :label="false">规则解析</el-radio>
            <el-radio :label="true">JS解析(小程序端不支持)</el-radio>
          </el-radio-group>
          <div v-if="tempHttp.aftHandler.rule && !tempHttp.aftHandler.isJs">
            <el-input size="small" style="width: 200px;" v-model="tempHttp.aftHandler.rule.source"><template slot="prepend">返回值.</template></el-input>
            取数据
            <el-input size="small" style="width: 150px;" v-model="tempHttp.aftHandler.rule.name"><template slot="prepend">名称</template></el-input>
            <el-input size="small" style="width: 150px;" v-model="tempHttp.aftHandler.rule.value"><template slot="prepend">值</template></el-input>
          </div>
          <code-editor v-else height="200px" ref="aftCode" v-model="tempHttp.aftHandler.js" :default-value="`function aftHandler(rsp){\n\t//获取到结果后做一些处理\n\t\n}`"/>
        </template>
      </http-req>
    </w-dialog>
  </div>
</template>

<script>
import HttpReq from "@bpm-oa-web/components/common/HttpReq.vue";
import draggable from "vuedraggable";
import {ValueType} from "../ComponentsConfigExport";
import CodeEditor from '@bpm-oa-web/components/common/CodeEditor.vue'
export default {
  name: "ScoreConfig",
  components: {draggable, HttpReq, CodeEditor},
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      visible: false,
      tempHttp: {
        url: '',
        method: 'GET',
        headers: [],
        contentType: 'JSON',
        params: [],
        data: '',
        preHandler: null,
        aftHandler: {
          isJs: false,
          js: '',
          rule: {
            source: '',
            name: '',
            value: ''
          }
        }
      }
    }
  },
  beforeMount() {
    this.loadHttp()
  },
  methods: {
    loadHttp(){
      this.tempHttp = this.$deepCopy(this.value.http)
      //处理旧数据兼容
      const aftHandler = {
        isJs: true,
        js: '',
        rule: {
          source: 'data',
          name: 'name',
          value: 'value'
        }
      }
      if (typeof(this.tempHttp.aftHandler) === 'string'){
        aftHandler.js = this.tempHttp.aftHandler
        this.$set(this.tempHttp, 'aftHandler', aftHandler)
      } else if (!this.tempHttp.aftHandler){
        this.$set(this.tempHttp, 'aftHandler', aftHandler)
      }
      if (this.$refs.aftCode){
        this.$refs.aftCode.reloadCode()
      }
    },
    httpOk(){
      this.$refs.http.validate((valid, err) => {
        if (valid){
          this.value.http = this.$deepCopy(this.tempHttp)
          this.visible = false
        }else {
          this.$message.warning(err)
        }
      })
    },
    modeChange(val){
       this.$store.state.bpm.selectFormItem.valueType = val ? ValueType.array:ValueType.string
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .options {
  .el-form-item__label {
    display: block;
    width: 100% !important;
    text-align: left;
  }

  .el-button{
    float: right;
  }

  .el-form-item__content{
    margin-left: 0 !important;
    margin-top: 40px;
  }

  .option-item{
    margin: 2px 0;
  }

  .del-btn{
    cursor: pointer;
    margin-left: 5px;
    padding: 5px;
    border-radius: 50%;
    &:hover{
      background: #DDDFE5;
    }
  }
}

</style>
