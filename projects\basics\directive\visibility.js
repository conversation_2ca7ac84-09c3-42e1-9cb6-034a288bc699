import Vue from 'vue';

Vue.use({
    install(Vue) {
        Vue.directive('visibility', {
            bind(el, binding) {
                if (binding.value === false) {
                    el.style.visibility = 'hidden';
                } else if (binding.value === true) {
                    el.style.visibility = 'visible';
                } else {
                    el.style.visibility = 'visible';
                }
            },
            update(el, binding) {
                if (binding.value === false) {
                    el.style.visibility = 'hidden';
                } else if (binding.value === true) {
                    el.style.visibility = 'visible';
                } else {
                    el.style.visibility = 'visible';
                }
            }
        })
    }
})