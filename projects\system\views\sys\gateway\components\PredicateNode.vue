<template>
  <div class="predicate-node" :class="{ 'is-active': isActive,disabled: predicate.enable < 1 }" @click="$emit('click', predicate)">
    <div class="predicate-header">
      <div class="predicate-name">
        <i class="el-icon-fork-spoon"></i>
        <span>{{ predicate.name }}</span>
      </div>
      <div class="predicate-actions">
        <el-switch v-model="predicate.enable" style="transform: scale(0.7)" @change="$emit('enableChange')"></el-switch>
        <el-tooltip content="编辑断言" placement="top">
          <el-button type="text" icon="el-icon-edit" @click.stop="$emit('edit')"></el-button>
        </el-tooltip>
        <el-tooltip content="删除断言" placement="top">
          <el-button type="text" icon="el-icon-delete" @click.stop="$emit('delete')"></el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="predicate-content">
      <div v-if="predicate.arg && Object.keys(predicate.arg).length" class="arg-list">
        <div v-for="(value, key) in predicate.arg" :key="key" class="arg-item">
          <span class="arg-key">{{ key }}:</span>
          <span class="arg-value">{{ value }}</span>
        </div>
      </div>
      <div v-else class="no-args">无参数</div>
    </div>
    <div class="predicate-description">
      <span>{{ getPredicateDescription() }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PredicateNode',
  props: {
    predicate: {
      type: Object,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      predicateDescriptions: {
        Path: '路径匹配',
        Method: 'HTTP方法匹配',
        Host: '主机名匹配',
        Header: '请求头匹配',
        Cookie: 'Cookie匹配',
        Query: '查询参数匹配',
        After: '在特定时间之后',
        Before: '在特定时间之前',
        Between: '在特定时间段之间',
        RemoteAddr: '远程地址匹配',
        Weight: '权重路由'
      }
    }
  },
  methods: {
    getPredicateDescription() {
      // 根据断言类型返回描述
      if (this.predicateDescriptions[this.predicate.name]) {
        return this.predicateDescriptions[this.predicate.name];
      }
      
      return `${this.predicate.name} 断言`;
    },
    
    formatPredicateValue(predicate) {
      if (!predicate.arg) return '';
      
      // 格式化不同类型断言的值显示
      switch (predicate.name) {
        case 'Path':
          return predicate.arg.pattern || '';
        case 'Method':
          return predicate.arg.method || '';
        case 'Host':
          return predicate.arg.pattern || '';
        case 'Header':
          return `${predicate.arg.header}=${predicate.arg.regexp || ''}`;
        case 'Cookie':
          return `${predicate.arg.name}=${predicate.arg.regexp || ''}`;
        case 'Query':
          return `${predicate.arg.param}=${predicate.arg.regexp || ''}`;
        case 'RemoteAddr':
          return predicate.arg.sources || '';
        default:
          return Object.values(predicate.arg).join(', ');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.disabled {
  opacity: 0.4;
}
.predicate-node {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px;
  margin-bottom: 12px;
  border-left: 3px solid #67C23A;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  &.is-active {
    background-color: #F5FAEF;
    
    &:before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      border-width: 0 18px 18px 0;
      border-style: solid;
      border-color: #67C23A #fff;
    }
  }
  
  .predicate-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .predicate-name {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 6px;
        color: #67C23A;
      }
    }
    
    .predicate-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .el-button {
        font-size: 16px;
        padding: 4px;
        
        &:hover {
          color: #409EFF;
        }
        
        &:last-child:hover {
          color: #F56C6C;
        }
      }
    }
  }
  
  .predicate-content {
    margin-bottom: 8px;
    
    .arg-list {
      .arg-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 13px;
        
        .arg-key {
          color: #909399;
          min-width: 70px;
          margin-right: 8px;
        }
        
        .arg-value {
          color: #303133;
          word-break: break-all;
        }
      }
    }
    
    .no-args {
      font-size: 13px;
      color: #909399;
      font-style: italic;
    }
  }
  
  .predicate-description {
    font-size: 12px;
    color: #909399;
    padding-top: 6px;
    border-top: 1px dashed #EBEEF5;
  }
}
</style> 