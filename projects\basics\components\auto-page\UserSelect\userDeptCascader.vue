<template>
    <el-cascader ref="cascader" v-bind="$attrs" v-on="$listeners" :props="$attrs.props || oprops" :options="options">
        <template slot-scope="{ node, data }">
            <i v-if="data.leafType === 'account'" class="el-icon-user" style="margin-right: 5px;" />
            <span>{{ node.label }}</span>
        </template>
    </el-cascader>
</template>
<script>
import { isEqual } from 'element-ui/src/utils/util';
import { getDeptList, getAccountList } from './api'
export default {
    props: {
        type: {
            type: String,
            default: 'dept'//['dept','account']
        },
        deptId: {
            type: [String, Number],
            default: null
        }
    },
    data() {
        return {
            options: [],
            oprops: {
                value: 'id',
                label: 'name',
                multiple: true,
                emitPath: false,
                checkStrictly: true,
            },
        }
    },

    mounted() {
        this.load(0, this.deptId, {}, this.callback)
    },

    methods: {
        getSelectItems(e) {

            let list = this.$refs.cascader.getCheckedNodes().map(node => {
                return {
                    id: node.value,
                    name: node.label,
                    type: node.data.leafType
                }
            })
        },

        callback(level, data, list) {
            if (level == 0) {
                list.forEach(f => {
                    this.load(1, f.id, f, this.callback)
                })
                this.options = list
            } else {
                if (list.length > 0) {
                    data.children = list
                    list.forEach(f => {
                        this.load(level + 1, f.id, f, this.callback)
                    })
                }
            }
        },

        load(level, value, data, resolve) {
            this.getDeptList(level, value, data)
                .then((list) => {
                    resolve(level, data, [...list[0].map(m => {
                        m.leafType = 'dept'
                        m.children = []
                        return m
                    }), ...list[1].map(m => {
                        m.leafType = 'account'
                        m.leaf = 'leaf'
                        return m
                    })].map(m => {
                        if (this.$listeners.disabledMethod) {
                            m.disabled = this.$listeners.disabledMethod(m)
                        }
                        return m
                    }))
                })
        },



        getDeptList(level, value, data) {
            const { leafType } = data || {}

            return Promise.all([new Promise(resolve => {

                if (leafType === 'account') {
                    resolve([])
                } else {
                    getDeptList({ parentId: level === 0 && !value ? 0 : value }).then(data => {
                        resolve(data?.records || [])
                    }).catch(() => {
                        resolve([])
                    })
                }
            }), new Promise(resolve => {
                if (leafType === 'account' || this.type === 'dept') {
                    resolve([])
                } else {
                    getAccountList({ deptId: value, 'nulls[deptId]': value || value === 0 ? null : true }).then(data => {
                        resolve(data?.records || [])
                    }).catch(() => {
                        resolve([])
                    })
                }
            })])
        }
    }
}
</script>
<style lang="scss"></style>