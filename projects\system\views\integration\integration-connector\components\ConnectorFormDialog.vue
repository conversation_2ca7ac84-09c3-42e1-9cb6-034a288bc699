<template>
  <el-dialog :visible.sync="visible" :title="editMode ? '编辑连接器' : '创建连接器'" width="90vw" top="5vh"
    :close-on-click-modal="false" :close-on-press-escape="false" custom-class="create-connector-dialog"
    @close="handleClose">
    <div class="create-connector-container">
      <!-- 左侧：连接器类型选择 -->
      <div class="connector-type-section">
        <div class="section-header">
          <h3>{{ editMode ? '连接器类型' : '选择连接器类型' }}</h3>
          <p>{{ editMode ? '当前连接器类型（不可修改）' : '选择您要创建的连接器类型' }}</p>
        </div>



        <!-- 连接器类型选择 -->
        <div class="connector-type-selection">
          <!-- 可用连接器 -->
          <div class="type-category">
            <h4 class="category-title">
              <i class="category-icon available"></i>
              可用连接器
            </h4>
            <div class="type-list">
              <div class="type-item" :class="{ active: connectorForm.type === 'HTTP', disabled: editMode }"
                @click="!editMode && selectConnectorType('HTTP')">
                <div class="type-icon http-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="currentColor"
                      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="currentColor"
                      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </div>
                <div class="type-content">
                  <h5>HTTP连接器</h5>
                  <p>RESTful API连接器</p>
                </div>
                <div class="type-status available">可用</div>
              </div>

              <div class="type-item" :class="{ active: connectorForm.type === 'JDBC', disabled: editMode }"
                @click="!editMode && selectConnectorType('JDBC')">
                <div class="type-icon jdbc-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z" fill="currentColor" />
                    <circle cx="6" cy="7" r="1" fill="white" />
                    <circle cx="6" cy="12" r="1" fill="white" />
                    <circle cx="6" cy="17" r="1" fill="white" />
                  </svg>
                </div>
                <div class="type-content">
                  <h5>JDBC连接器</h5>
                  <p>数据库连接器</p>
                </div>
                <div class="type-status available">可用</div>
              </div>

              <div class="type-item" :class="{ active: connectorForm.type === 'FTP', disabled: editMode }"
                @click="!editMode && selectConnectorType('FTP')">
                <div class="type-icon ftp-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor"
                      stroke-width="2" />
                    <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" />
                  </svg>
                </div>
                <div class="type-content">
                  <h5>FTP连接器</h5>
                  <p>文件传输连接器</p>
                </div>
                <div class="type-status available">可用</div>
              </div>
            </div>
          </div>

          <!-- 即将上线连接器 -->
          <div class="type-category">
            <h4 class="category-title">
              <i class="category-icon coming-soon"></i>
              即将上线
            </h4>
            <div class="type-list">
              <div class="type-item disabled" @click="handleDisabledClick('MQ')">
                <div class="type-icon mq-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"
                      stroke="currentColor" stroke-width="2" />
                  </svg>
                </div>
                <div class="type-content">
                  <h5>MQ连接器</h5>
                  <p>消息队列连接器</p>
                </div>
                <div class="type-status coming">即将上线</div>
              </div>

              <!-- <div class="type-item disabled" @click="handleDisabledClick('SFTP')">
                <div class="type-icon sftp-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="currentColor" stroke-width="2" />
                    <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" />
                  </svg>
                </div>
                <div class="type-content">
                  <h5>SFTP连接器</h5>
                  <p>安全文件传输连接器</p>
                </div>
                <div class="type-status coming">即将上线</div>
              </div> -->

              <div class="type-item disabled" @click="handleDisabledClick('REDIS')">
                <div class="type-icon redis-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="3" width="20" height="14" rx="2" stroke="currentColor" stroke-width="2" />
                    <circle cx="8" cy="10" r="2" stroke="currentColor" stroke-width="2" />
                    <circle cx="16" cy="10" r="2" stroke="currentColor" stroke-width="2" />
                  </svg>
                </div>
                <div class="type-content">
                  <h5>Redis连接器</h5>
                  <p>缓存数据库连接器</p>
                </div>
                <div class="type-status coming">即将上线</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：连接器配置表单 -->
      <div class="connector-form-section">

        <!-- 连接器配置表单 -->
        <div v-if="connectorForm.type" class="form-container">
          <connector-base-form
            v-model="connectorForm"
            ref="baseForm"
            :show-test-connection="true"
          />
        </div>

        <!-- 空状态提示 -->
        <div v-else class="empty-form-state">
          <div class="empty-content">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#d9d9d9" stroke-width="2" stroke-linejoin="round" />
              <path d="M2 17L12 22L22 17" stroke="#d9d9d9" stroke-width="2" stroke-linejoin="round" />
              <path d="M2 12L12 17L22 12" stroke="#d9d9d9" stroke-width="2" stroke-linejoin="round" />
            </svg>
            <h3>请先选择连接器类型</h3>
            <p>从左侧选择一个连接器类型开始配置</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button size="large" class="cancel-btn" @click="handleClose">
        取消
      </el-button>
      <el-button type="primary" size="large" class="create-btn" :disabled="!canCreate" @click="handleCreate"
        :loading="creating">
        <i :class="editMode ? 'el-icon-edit' : 'el-icon-plus'" v-if="!creating"></i>
        {{ creating ? (editMode ? '保存中...' : '创建中...') : (editMode ? '保存连接器' : '创建连接器') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createIntegrationConnector, updateIntegrationConnector } from '@system/api/integration/integration-connetor'
import ConnectorBaseForm from './ConnectorBaseForm'

export default {
  name: 'CreateConnectorDialog',
  components: {
    ConnectorBaseForm
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editMode: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      creating: false,
      connectorForm: {
        name: '',
        code: '',
        type: '',
        description: '',
        protocol: '',
        host: '',
        port: null,
        urlAppend: '',
        authActuator: '',
        enabled: true,
        username: '',
        password: '',
        config: {
          type: ''
        }
      },
      formRules: {
        name: [
          { required: true, message: '请输入连接器名称', trigger: 'blur' },
          { min: 2, max: 50, message: '连接器名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入连接器编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '连接器编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
          { min: 2, max: 30, message: '连接器编码长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择连接器类型', trigger: 'change' }
        ],
        protocol: [
          { required: true, message: '请输入协议', trigger: 'blur' },
          { min: 2, max: 20, message: '协议长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        host: [
          { required: true, message: '请输入主机名', trigger: 'blur' },
          { min: 2, max: 100, message: '主机名长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' },
          { type: 'number', min: 1, max: 65535, message: '端口号必须在 1 到 65535 之间', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 1, max: 50, message: '用户名长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 1, max: 100, message: '密码长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initDialog()
        })
      }
    },
    editData: {
      handler(newVal) {
        if (this.editMode && newVal && Object.keys(newVal).length > 0) {
          this.loadEditData(newVal)
        }
      },
      immediate: true,
      deep: true
    },
    'connectorForm.type'(newVal) {
      if (newVal) {
        // 清空之前的认证配置
        this.connectorForm.authActuator = ''
        this.connectorForm.username = ''
        this.connectorForm.password = ''
        this.connectorForm.config = {
          type: newVal
        }

        // 重新加载认证执行器由ConnectorBaseForm处理

        // 设置默认协议
        if (newVal === 'HTTP') {
          this.connectorForm.protocol = 'http'
          this.connectorForm.port = 80
        } else if (newVal === 'JDBC') {
          this.connectorForm.protocol = 'jdbc:mysql'
          this.connectorForm.port = 3306
        } else if (newVal === 'FTP') {
          this.connectorForm.protocol = 'ftp'
          this.connectorForm.port = 21
        }
      } else {
        // 清空认证执行器列表
        this.authActuators = []
      }
    }
  },
  computed: {
    canCreate() {
      const basicInfo = this.connectorForm.name && this.connectorForm.code && this.connectorForm.type
      const connectionInfo = this.connectorForm.protocol && this.connectorForm.host && this.connectorForm.port
      const authInfo = !this.connectorForm.authActuator ||
        this.connectorForm.authActuator === 'CHAIN_AUTH' ||
        (this.connectorForm.authActuator === 'USER_PASSWORD' &&
          this.connectorForm.username && this.connectorForm.password)
      return basicInfo && connectionInfo && authInfo
    },

  },
  methods: {
    initDialog() {
      if (this.editMode && this.editData && Object.keys(this.editData).length > 0) {
        // 编辑模式，加载编辑数据
        this.loadEditData(this.editData)
      } else {
        // 创建模式，初始化空表单
        this.connectorForm = {
          name: '',
          code: '',
          type: '', // 初始为空，让用户选择
          description: '',
          protocol: '',
          host: '',
          port: null,
          urlAppend: '',
          authActuator: '',
          enabled: true,
          username: '',
          password: '',
          config: {
            type: ''
          }
        }
        this.creating = false
        // 认证执行器由ConnectorBaseForm处理
      }
    },

    // 加载编辑数据
    loadEditData(data) {
      this.connectorForm = {
        name: data.name || '',
        code: data.code || '',
        type: data.type || '',
        description: data.description || '',
        protocol: data.protocol || '',
        host: data.host || '',
        port: data.port || null,
        urlAppend: data.urlAppend || '',
        authActuator: data.authActuator || '',
        enabled: data.enabled !== undefined ? data.enabled : true,
        username: data.username || '',
        password: data.password || '',
        config: {
          type: data.type || ''
        }
      }
      this.creating = false
      this.testResult = null

      // 认证执行器由ConnectorBaseForm处理
    },

    selectConnectorType(type) {
      console.log('选择连接器类型:', type)
      this.connectorForm.type = type
    },

    // 清除选择
    clearSelection() {
      this.connectorForm.type = ''
      this.connectorForm.protocol = ''
      this.connectorForm.port = null
      this.connectorForm.authActuator = ''
      this.connectorForm.username = ''
      this.connectorForm.password = ''
      this.connectorForm.config = {
        type: ''
      }
      this.testResult = null
    },

    // 处理禁用连接器类型的点击
    handleDisabledClick(type) {
      this.$message.info(`${this.getConnectorTypeName(type)} 即将上线，敬请期待！`)
    },



    getConnectorTypeName(type = this.connectorForm.type) {
      const typeNames = {
        'HTTP': 'HTTP连接器',
        'JDBC': 'JDBC连接器',
        'FTP': 'FTP连接器',
        'MQ': 'MQ连接器',
        'SFTP': 'SFTP连接器',
        'REDIS': 'Redis连接器'
      }
      return typeNames[type] || '未知连接器'
    },

    getConnectorTypeDescription(type = this.connectorForm.type) {
      const descriptions = {
        'HTTP': '基于HTTP协议的RESTful API连接器',
        'JDBC': '基于JDBC协议的数据库连接器',
        'FTP': '基于FTP协议的文件传输连接器',
        'MQ': '基于消息队列的异步通信连接器',
        'SFTP': '基于SFTP协议的安全文件传输连接器',
        'REDIS': '基于Redis的缓存数据库连接器'
      }
      return descriptions[type] || '连接器类型描述'
    },

    getKeyFeatures() {
      const features = {
        'HTTP': [
          '支持RESTful API调用',
          '多种认证方式（Basic、Bearer Token等）',
          '支持JSON、XML、Form等数据格式',
          '自动重试和错误处理'
        ],
        'JDBC': [
          '支持多种主流数据库',
          '智能连接池管理',
          '支持复杂SQL查询',
          '事务管理和批量操作'
        ]
      }
      return features[this.connectorForm.type] || []
    },

    getUrlAppendPlaceholder() {
      const placeholders = {
        'HTTP': '如: /api/v1/users 或 ?timeout=30&format=json',
        'JDBC': '如: useUnicode=true&characterEncoding=UTF-8&serverTimezone=UTC'
      }
      return placeholders[this.connectorForm.type] || '请输入URL附加参数'
    },





    async handleCreate() {
      if (!this.connectorForm.type) {
        this.$message.warning('请先选择连接器类型')
        return
      }

      const valid = await this.$refs.baseForm.validate()
      if (valid) {
          this.creating = true

          try {
            // 构建提交数据
            const submitData = {
              name: this.connectorForm.name,
              code: this.connectorForm.code,
              type: this.connectorForm.type,
              description: this.connectorForm.description || '',
              protocol: this.connectorForm.protocol,
              host: this.connectorForm.host,
              port: this.connectorForm.port,
              urlAppend: this.connectorForm.urlAppend || '',
              authActuator: this.connectorForm.authActuator || null,
              enabled: this.connectorForm.enabled,
              username: this.connectorForm.username,
              password: this.connectorForm.password,
              config: {
                type: this.connectorForm.type
              },
              remark: this.connectorForm.description || ''
            }

            // 编辑模式需要添加ID
            if (this.editMode && this.editData.id) {
              submitData.id = this.editData.id
              submitData.createBy = this.editData.createBy
              submitData.createTime = this.editData.createTime
            } else {
              submitData.createBy = null
              submitData.updateBy = null
              submitData.createTime = null
              submitData.updateTime = null
              submitData.id = null
            }

            // 根据认证类型添加特殊配置
            if (this.connectorForm.authActuator === 'CHAIN_AUTH') {
              submitData.config.authExpression = '${login.data.accessToken}'
              submitData.config.authLocation = 'HEADER'
              submitData.config.authParamName = 'Authorization'
            }

            // 调用创建或更新接口
            let response
            if (this.editMode) {  
              // 编辑模式使用PUT请求
              response = await updateIntegrationConnector(submitData)
              this.$emit('updated', submitData)
            } else {
              // 创建模式使用POST请求
              response = await createIntegrationConnector([submitData])
              this.$emit('created', {
                ...this.connectorForm,
                id: response?.id
              })
            }
            this.handleClose()
          } finally {
            this.creating = false
          }
      } else {
        this.$message.warning('请完善必填信息')
      }
    },



    handleClose() {
      this.resetForm()
      this.creating = false
      this.$emit('update:visible', false)
    },

    resetForm() {
      this.connectorForm = {
        name: '',
        code: '',
        type: '',
        description: '',
        protocol: '',
        host: '',
        port: null,
        urlAppend: '',
        authActuator: '',
        enabled: true,
        username: '',
        password: '',
        config: {
          type: ''
        }
      }
      if (this.$refs.baseForm) {
        this.$refs.baseForm.resetForm()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 修改蒙版颜色
::v-deep .el-dialog__wrapper {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(4px);
}

::v-deep .create-connector-dialog {
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  margin-bottom: 2.5vh !important;

  .el-dialog {
    border-radius: 16px !important;
    margin: 0 !important;
  }

  .el-dialog__header {
    padding: 24px 24px 0;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}

.create-connector-container {
  display: flex;
  height: 80vh;
  min-height: 600px;
}

.connector-type-section {
  flex: 1;
  padding: 24px;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
  background: #fafbfc;

  .section-header {
    margin-bottom: 24px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 6px 0;
    }

    p {
      font-size: 13px;
      color: #8c8c8c;
      margin: 0;
    }
  }



  .connector-type-selection {
    .type-category {
      margin-bottom: 24px;

      .category-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 12px 0;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        .category-icon {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.available {
            background: #52c41a;
          }

          &.coming-soon {
            background: #fa8c16;
          }
        }
      }

      .type-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }

    .type-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:hover:not(.disabled) {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      }

      &.active {
        border-color: #1890ff;
        background: linear-gradient(135deg, #f6f8ff, #e6f7ff);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);

        &::after {
          content: '✓';
          position: absolute;
          top: 8px;
          right: 8px;
          width: 16px;
          height: 16px;
          background: #1890ff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          font-weight: bold;
        }
      }

      &.disabled {
        background: #f9f9f9;
        border-color: #e8e8e8;
        cursor: not-allowed;
        opacity: 0.7;

        &:hover {
          box-shadow: none;
          border-color: #e8e8e8;
        }

        .type-icon {
          opacity: 0.6;
        }

        .type-content {

          h5,
          p {
            color: #8c8c8c;
          }
        }

        &.active {
          background: #f0f0f0;
          border-color: #d9d9d9;

          &::after {
            background: #8c8c8c;
          }
        }
      }

      .type-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        svg {
          width: 18px;
          height: 18px;
          color: white;
        }

        &.http-icon {
          background: linear-gradient(135deg, #1890ff, #40a9ff);
        }

        &.jdbc-icon {
          background: linear-gradient(135deg, #722ed1, #9254de);
        }

        &.ftp-icon {
          background: linear-gradient(135deg, #52c41a, #73d13d);
        }

        &.mq-icon {
          background: linear-gradient(135deg, #fa8c16, #ffa940);
        }

        &.sftp-icon {
          background: linear-gradient(135deg, #13c2c2, #36cfc9);
        }

        &.redis-icon {
          background: linear-gradient(135deg, #f5222d, #ff4d4f);
        }
      }

      .type-content {
        flex: 1;

        h5 {
          font-size: 14px;
          font-weight: 600;
          color: #262626;
          margin: 0 0 2px 0;
        }

        p {
          font-size: 12px;
          color: #8c8c8c;
          margin: 0;
          line-height: 1.3;
        }
      }

      .type-status {
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 500;
        flex-shrink: 0;

        &.available {
          background: #f6ffed;
          color: #52c41a;
          border: 1px solid #b7eb8f;
        }

        &.coming {
          background: #fff7e6;
          color: #fa8c16;
          border: 1px solid #ffd591;
        }
      }

    }
  }
}

.connector-form-section {
  flex: 2;
  padding: 16px 24px;
  background: white;
  overflow-y: auto;

  .form-container {
    background: #fafbfc;
    border-radius: 8px;
    padding: 20px;
  }

  .empty-form-state {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .empty-content {
      text-align: center;
      color: #8c8c8c;

      svg {
        margin-bottom: 16px;
      }

      h3 {
        font-size: 16px;
        font-weight: 500;
        color: #595959;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 14px;
        margin: 0;
      }
    }
  }

  .connector-form {
    .form-row {
      display: flex;
      gap: 16px;

      .form-item-half {
        flex: 1;
      }

      .form-item-quarter {
        flex: 0 0 120px;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0;

        .form-item-quarter {
          flex: 1;
        }
      }
    }

    .el-form-item {
      margin-bottom: 20px;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #262626;
      font-size: 14px;
    }

    .el-input,
    .el-select,
    .el-textarea {

      .el-input__inner,
      .el-textarea__inner {
        border-radius: 8px;
        border: 1px solid #e8e8e8;
        transition: all 0.2s ease;
        font-size: 14px;

        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }
    }

    // 连接信息和认证配置样式
    .connection-section,
    .auth-section {
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
    }

    .url-preview-section {
      margin-top: 12px;
    }

    .url-preview {
      padding: 8px 12px;
      background: #fafafa;
      border-radius: 6px;
      border: 1px solid #e8e8e8;

      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;

        label {
          font-size: 13px;
          font-weight: 500;
          color: #595959;
          margin: 0;
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: 6px;

          .test-btn-inline {
            font-size: 12px;
            padding: 2px 6px;
            height: auto;
            line-height: 1;
            color: #1890ff;
            border-radius: 3px;
            transition: all 0.2s ease;

            &:hover {
              color: #40a9ff;
              background: rgba(24, 144, 255, 0.05);
            }

            i {
              margin-right: 2px;
              font-size: 11px;
            }
          }

          .test-result-tag {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;

            &.success {
              color: #52c41a;
              background: rgba(82, 196, 26, 0.08);
              border: 1px solid rgba(82, 196, 26, 0.2);
            }

            &.error {
              color: #ff4d4f;
              background: rgba(255, 77, 79, 0.08);
              border: 1px solid rgba(255, 77, 79, 0.2);
            }

            i {
              font-size: 11px;
            }
          }
        }
      }

      .preview-url {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        color: #096dd9;
        background: transparent;
        padding: 0;
        border: none;
        word-break: break-all;
        line-height: 1.4;
      }
    }

    .auth-config {
      margin-top: 16px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 8px;
      border: 1px solid #e8e8e8;
    }

    .chain-auth-tip {
      margin-top: 16px;

      ::v-deep .el-alert {
        border-radius: 8px;

        .el-alert__title {
          font-size: 14px;
          font-weight: 500;
        }

        .el-alert__description {
          font-size: 13px;
          margin-top: 4px;
        }
      }
    }

    .auth-option {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .auth-name {
        font-size: 14px;
        color: #262626;
        font-weight: 500;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  @media (max-width: 768px) {
    flex-direction: column-reverse;
    gap: 8px;

    .el-button {
      width: 100%;
      margin: 0;
    }
  }

  .el-button {
    min-width: 100px;
    height: 40px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.cancel-btn {
      background: #fff;
      border: 1px solid #d9d9d9;
      color: #595959;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }

    &.create-btn {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border: none;
      color: white;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #40a9ff, #69c0ff);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        transform: translateY(-1px);
      }

      &:disabled {
        background: #f5f5f5;
        color: #bfbfbf;
        box-shadow: none;
        cursor: not-allowed;
      }

      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
