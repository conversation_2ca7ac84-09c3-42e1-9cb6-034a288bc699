import uniqueId from '@form-create/utils/lib/unique';
import { localeProps } from '@form-create/designer-zrw/src/utils';

const label = '地图';
const name = 'gmap';

export default {
    menu: 'custom',
    icon: 'icon-editor',
    label,
    name,
    event: ['change'],
    validate: ['string'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            $required: false,
            props: {},
        };
    },
    props(_, { t }) {
        return localeProps(t, 'custom.props', [
            {
                type: 'gmap',
                field: 'coordinates',
                title: '地图中心位置',
                props: {
                    type: 'Point',
                    mode: 0
                }
            },
            {
                type: 'inputNumber',
                field: 'zoom',
                value: 11,
                title: '地图缩放比',
            },
            {
                type: 'radio',
                field: 'type',
                value: 'Point',
                title: '地图数据类型',
                options: [{ label: 'Point', value: 'Point' },
                { label: 'Polygon', value: 'Polygon' }]
            },
            {
                type: 'switch',
                field: 'disabled',
                title: t('custom.props.disabled')
            }]);
    }
};
