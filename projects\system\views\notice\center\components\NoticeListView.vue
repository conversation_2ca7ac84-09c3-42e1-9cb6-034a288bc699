<template>
  <div class="notice-list-view">
    <!-- 顶部操作栏 -->
    <div class="top-bar">
      <div class="left-actions">
        <el-input
          placeholder="搜索消息"
          prefix-icon="el-icon-search"
          v-model="searchKeyword"
          clearable
          @keyup.enter.native="handleSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
        </el-input>
      </div>
      <div class="right-actions">
        <div class="select-wrapper">
          <i class="prefix-icon" :class="selectedGroup ? 'el-icon-folder' : 'el-icon-folder-opened'"></i>
          <el-select
            v-model="selectedGroup"
            placeholder="选择分组"
            clearable
            class="group-select"
            @change="handleGroupChange"
            popper-class="group-select-dropdown"
          >
            <el-option
              v-for="(count, groupName) in groupStats"
              :key="groupName"
              :label="groupName"
              :value="groupName"
            >
              <div class="group-option">
                <div class="group-info">
                  <i class="el-icon-folder" style="color: #409EFF;"></i>
                  <span class="group-name">{{ groupName }}</span>
                </div>
                <span v-if="count" class="count-badge">{{ count }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
        <el-button 
          v-if="exportUrl && selectedGroup" 
          type="primary" 
          plain 
          icon="el-icon-download" 
          @click="handleExport" 
          class="export-btn"
        >
          导出
        </el-button>
        <el-button type="primary" plain @click="handleReadAll" v-if="unreadCount > 0" class="all-read-btn">
          <i class="el-icon-check"></i> 全部已读
        </el-button>
      </div>
    </div>

    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" class="notification-tabs" @tab-click="handleTabClick">
      <el-tab-pane name="group">
        <span slot="label" class="tab-label">
          系统
          <el-badge v-if="unreadGroup > 0" :value="unreadGroup" class="tab-badge" type="danger"></el-badge>
        </span>
      </el-tab-pane>
      <el-tab-pane name="system">
        <span slot="label" class="tab-label">
          公告
          <el-badge v-if="unreadSystem > 0" :value="unreadSystem" class="tab-badge" type="danger"></el-badge>
        </span>
      </el-tab-pane>
      <el-tab-pane name="admin">
        <span slot="label" class="tab-label">
          私信
          <el-badge v-if="unreadAdmin > 0" :value="unreadAdmin" class="tab-badge" type="danger"></el-badge>
        </span>
      </el-tab-pane>
    </el-tabs>
  
    <!-- 消息列表 -->
    <div class="notification-list" ref="notificationList">
      <transition-group name="message-fade" tag="div">
        <div v-if="currentMessages.length > 0" key="message-list" class="message-list-container">
          <div v-for="(msg, index) in currentMessages" :key="msg.id" class="notification-item" 
            :class="{ unread: !msg.read }"
            :data-message-id="msg.id">
            <div class="user-avatar" v-if="msg.disPlayPicUrl">
              <img :src="getFileUrl(msg.disPlayPicUrl)" alt="头像">
            </div>
            <div class="item-icon" :class="getIconClass(msg.type)" v-else>
              <i :class="getIconByType(msg.type)"></i>
            </div>
            <div class="item-content" @click="handleReadMessage(msg)">
              <div class="item-main">
                <div class="item-header">
                  <div class="item-title">
                    {{ msg.displayName || msg.title }}
                    <el-tag size="mini" type="success" class="group-tag" v-if="msg.groupName">
                      {{ msg.groupName }}
                    </el-tag>
                  </div>
                </div>
                <div class="item-desc" :class="{'rich-content': true}">
                  <template v-if="msg.type === 'group' && msg.groupSpeakerDisplayName">
                    <div class="item-desc rich-content" v-html="msg.groupSpeakerDisplayName + ': ' + msg.content"></div>
                  </template>
                  <template v-else>
                    <span v-html="msg.content"></span>
                  </template>
                </div>
                <div class="item-meta">
                  <span class="time">{{ formatTime(msg.time) }}</span>
                </div>
              </div>
              <div class="item-right">
                <div class="item-actions" v-if="msg.link">
                  <el-button type="primary" size="mini" round @click.stop="handleProcess(msg)">处理</el-button>
                </div>
                <div class="item-status">
                  <el-tag size="mini" :type="msg.read ? 'info' : 'danger'" effect="plain">
                    {{ msg.read ? '已读' : '未读' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else key="empty-state" class="empty-state">
          <i class="el-icon-chat-dot-square"></i>
          <p>{{ getEmptyText() }}</p>
        </div>
      </transition-group>
    </div>
    
    <!-- 分页器 -->
    <div class="pagination-container" v-if="currentMessages.length > 0">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="pagination.current"
        :page-size="pagination.size"
        layout="prev, pager, next, jumper"
        :total="pagination.total"
        :disabled="pagination.loading"
      >
      </el-pagination>
    </div>
    
    <!-- 加载状态 -->
    <transition name="fade">
      <div v-if="pagination.loading" class="loading-overlay">
        <div class="loading-spinner">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { getHistoryMessages, readMessage, readAllMessages, getUnreadMessageCount, getCurrentSubscriptions, getMessageGroupStats, getMessagesByGroup, getSystemConfig, exportMessages } from '@system/api/notice/chat.js'
import { formatTime } from '@system/api/notice/noticeService.js'
import chatSocketService from '@system/api/notice/chatSocket.js'

export default {
  name: 'NoticeListView',
  data() {
    return {
      activeTab: 'group', // 默认打开系统标签页
      searchKeyword: '',
      currentMessages: [],
      selectedGroup: '', // 选中的分组
      groupStats: {}, // 分组统计数据
      exportUrl: '', // 导出接口地址
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        loading: false
      },
      fileBaseUrl: process.env.VUE_APP_FILE_URL || '',
      // 未读消息计数
      unreadCounts: {
        broadcast: 0,
        private: 0,
        group: 0
      },
      // 服务是否可用
      serviceAvailable: true
    }
  },
  computed: {
    chatType() {
      switch (this.activeTab) {
        case 'system': return 'BROADCAST'
        case 'admin': return 'PRIVATE'
        case 'group': return 'GROUP'
        default: return 'BROADCAST'
      }
    },
    unreadCount() {
      return this.unreadCounts.broadcast + this.unreadCounts.private + this.unreadCounts.group
    },
    unreadSystem() {
      return this.unreadCounts.broadcast
    },
    unreadAdmin() {
      return this.unreadCounts.private
    },
    unreadGroup() {
      return this.unreadCounts.group
    }
  },
  watch: {
    activeTab: {
      immediate: true,
      handler() {
        this.pagination.current = 1
        this.selectedGroup = '' // 切换标签页时重置分组选择
        this.fetchGroupStats() // 获取分组统计
        this.fetchMessages()
      }
    }
  },
  created() {
    const { messageId, messageType, activeTab } = this.$route.query
    if (messageId && messageType) {
      this.activeTab = messageType
    } else if (activeTab) {
      this.activeTab = activeTab
    }
  },
  mounted() {
    // 挂载时初始化WebSocket连接和消息处理
    this.initWebSocket()
    
    // 检查导出功能是否可用
    this.checkExportAvailability()
    
    // 处理路由参数,聚焦到指定消息或设置标签页
    const { messageId, messageType, activeTab } = this.$route.query
    if (messageId && messageType) {
      // 设置正确的标签页
      this.activeTab = messageType
      // 延迟执行以确保数据加载完成
      this.$nextTick(() => {
        this.focusMessage(messageId)
      })
    } else if (activeTab) {
      // 如果只是设置标签页，直接设置
      this.activeTab = activeTab
    }
  },
  beforeDestroy() {
    // 清理资源
    this.$root.$off('new-message', this.handleMessage)
  },
  methods: {
    // 初始化WebSocket连接
    async initWebSocket() {
      try {
        // 先获取订阅信息
        const response = await getCurrentSubscriptions()
        const subscriptions = response || {}
        
        // 检查是否有任何可用的订阅
        if (!subscriptions.broadcastTopics?.length && 
            !subscriptions.groupTopics?.length && 
            !subscriptions.privateQueue) {
          this.serviceAvailable = false
          return
        }

        // 先建立一个连接
        await chatSocketService.connect()
        
        // 订阅收集所有需要订阅的主题
        const allTopics = [
          ...(subscriptions.broadcastTopics || []),
          ...(subscriptions.groupTopics || [])
        ]
        
        if (subscriptions.privateQueue) {
          allTopics.push(subscriptions.privateQueue)
        }
        
        // 批量订阅所有主题
        for (const topic of allTopics) {
          try {
            await chatSocketService.subscribe(topic, this.handleMessage)
          } catch (error) {
            // 捕获错误但不打印
          }
        }
        
        this.serviceAvailable = true
        
        // 订阅完成后获取历史消息和未读计数
        this.fetchUnreadCounts()
        this.fetchGroupStats()
        this.fetchMessages()
      } catch (error) {
        this.serviceAvailable = false
      }
    },
    
    // 处理消息回调
    handleMessage(message) {
      if (!message) return
      
      const notification = chatSocketService.handleNewMessage(message)
      
      // 处理撤回消息
      if (message.recall) {
        // 根据消息类型找到对应的消息列表
        let targetList = this.currentMessages
        
        // 找到并处理被撤回的消息
        const index = targetList.findIndex(msg => msg.id === message.id)
        if (index !== -1) {
          const targetMessage = targetList[index]
          // 如果消息未读，更新未读计数
          if (!targetMessage.read) {
            switch (message.chatType) {
              case 'BROADCAST':
                this.unreadCounts.broadcast = Math.max(0, this.unreadCounts.broadcast - 1)
                break
              case 'PRIVATE':
                this.unreadCounts.private = Math.max(0, this.unreadCounts.private - 1)
                break
              case 'GROUP':
                this.unreadCounts.group = Math.max(0, this.unreadCounts.group - 1)
                break
            }
            // 通知父组件未读计数变化
            this.$emit('unread-count-change', this.unreadCount)
          }
          // 移除被撤回的消息
          targetList.splice(index, 1)
          this.pagination.total = Math.max(0, this.pagination.total - 1)
        }
        return
      }
      
      // 根据消息类型更新未读计数和添加到对应列表
      switch (notification.type) {
        case 'system':
          this.unreadCounts.broadcast++
          // 如果当前是公告标签页，添加消息到列表顶部
          if (this.activeTab === 'system') {
            this.currentMessages.unshift(notification)
            this.pagination.total += 1
          }
          break
        case 'admin':
          this.unreadCounts.private++
          // 如果当前是私信标签页，添加消息到列表顶部
          if (this.activeTab === 'admin') {
            this.currentMessages.unshift(notification)
            this.pagination.total += 1
          }
          break
        case 'group':
          this.unreadCounts.group++
          // 如果当前是系统标签页，添加消息到列表顶部
          if (this.activeTab === 'group') {
            this.currentMessages.unshift(notification)
            this.pagination.total += 1
          }
          break
      }
      
      // 通知父组件未读计数变化
      this.$emit('unread-count-change', this.unreadCount)
      
      // 如果当前是对应的标签页，高亮显示新消息
      if ((notification.type === 'system' && this.activeTab === 'system') ||
          (notification.type === 'admin' && this.activeTab === 'admin') ||
          (notification.type === 'group' && this.activeTab === 'group')) {
        this.$nextTick(() => {
          this.focusNewMessage(notification)
        })
      }
    },
    
    // 获取未读消息数量
    async fetchUnreadCounts() {
      try {
        const response = await getUnreadMessageCount()
        if (response) {
          this.unreadCounts = {
            broadcast: response.broadcastUnReadCount || 0,
            private: response.privateUnReadCount || 0,
            group: response.groupUnReadCount || 0
          }
        }
      } catch (error) {
        // 错误处理
      }
    },
    
    // 获取分组统计数据
    async fetchGroupStats() {
      try {
        const response = await getMessageGroupStats(this.chatType)
        this.groupStats = response || {}
      } catch (error) {
        console.error('获取分组统计失败:', error)
        this.groupStats = {}
      }
    },
    
    async fetchMessages() {
      this.pagination.loading = true;
      
      try {
        let response;
        
        if (this.selectedGroup) {
          // 如果选择了分组，使用分组消息接口
          response = await getMessagesByGroup(
            this.chatType,
            this.selectedGroup,
            this.pagination.current,
            this.pagination.size
          );
        } else {
          // 否则使用常规历史消息接口，并传入搜索关键词
          response = await getHistoryMessages(
            this.chatType, 
            this.pagination.current, 
            this.pagination.size,
            this.searchKeyword || undefined // 当searchKeyword为空字符串时传undefined
          );
        }
        
        if (response && response.records) {
          // 记住之前的第一条消息ID，用于检测是否有新消息
          const previousFirstMessageId = this.currentMessages.length > 0 ? this.currentMessages[0].id : null;
          
          // 格式化消息数据
          this.currentMessages = response.records.map(msg => {
            const defaultTitle = this.activeTab === 'system' ? '公告' : 
                              (this.activeTab === 'admin' ? '管理员' : '系统');
            return {
              id: msg.id,
              title: msg.title || '',
              content: msg.content || '',
              time: msg.sendTime || new Date().toISOString(),
              read: msg.read || false,
              type: this.activeTab,
              sender: msg.fromUsername || defaultTitle,
              link: msg.link || '',
              displayName: msg.displayName || msg.fromUsername || defaultTitle,
              disPlayPicUrl: msg.disPlayPicUrl || '',
              groupSpeakerDisplayName: msg.groupSpeakerDisplayName || '',
              groupName: msg.groupName || '' // 添加分组名称字段
            };
          });
          
          // 更新分页信息
          this.pagination.total = response.total || 0;
          
          // 检查是否需要聚焦到特定消息
          const { messageId } = this.$route.query;
          if (messageId && this.currentMessages.some(msg => msg.id === messageId)) {
            this.$nextTick(() => {
              this.focusMessage(messageId);
            });
          }
          // 检查是否有新消息，如果有则聚焦到新消息
          else if (previousFirstMessageId && 
              this.currentMessages.length > 0 && 
              previousFirstMessageId !== this.currentMessages[0].id) {
            this.$nextTick(() => {
              // 聚焦到新消息
              this.focusNewMessage(this.currentMessages[0]);
            });
          }
        } else {
          this.currentMessages = [];
          this.pagination.total = 0;
        }
      } catch (error) {
        this.$message.error('获取消息列表失败');
        this.currentMessages = [];
      } finally {
        this.pagination.loading = false;
      }
    },
    
    handleCurrentChange(page) {
      this.pagination.current = page
      this.fetchMessages()
    },
    
    handleSearch() {
      this.pagination.current = 1;
      // 如果有选择分组，但需要搜索时，清除分组选择，使用搜索关键词过滤
      if (this.searchKeyword && this.selectedGroup) {
        this.selectedGroup = ''; // 清除分组选择，以便使用关键词搜索
      }
      this.fetchMessages();
    },
    
    // 处理标签页切换
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.selectedGroup = '' // 切换标签页时重置分组选择
      this.fetchGroupStats() // 获取分组统计
      // 通知父组件标签页已切换
      this.$emit('tab-change', this.activeTab)
    },
    
    async handleReadMessage(message) {
      if (!message.read) {
        try {
          await readMessage(message.id)
          message.read = true
          
          // 更新未读计数
          if (message.type === 'system') {
            this.unreadCounts.broadcast = Math.max(0, this.unreadCounts.broadcast - 1)
          } else if (message.type === 'admin') {
            this.unreadCounts.private = Math.max(0, this.unreadCounts.private - 1)
          } else if (message.type === 'group') {
            this.unreadCounts.group = Math.max(0, this.unreadCounts.group - 1)
          }
          
          // 通知父组件消息已读
          this.$emit('message-read', message)
        } catch (error) {
          // 错误处理
        }
      }
      
      // 如果有链接，则导航到该链接
      if (message.link) {
        this.handleProcess(message)
      }
    },
    
    handleProcess(message) {
      // 确保消息已读
      if (!message.read) {
        this.handleReadMessage(message)
      }
      
      // 如果有处理链接，则导航到该链接
      if (message.link) {
        // 检查是否为协议开头的链接 (http://, https://, ftp://, mailto:等)
        if (/^[a-zA-Z]+:\/\//.test(message.link) || message.link.startsWith('mailto:') || message.link.startsWith('tel:')) {
          // 协议开头，执行硬跳转
          window.open(message.link, '_blank')
        } else {
          // 非协议开头，执行路由跳转
          this.$router.push(message.link)
        }
      }
    },
    
    async handleReadAll() {
      try {
        await readAllMessages(this.chatType)
        
        // 更新UI中的消息状态
        this.currentMessages.forEach(msg => { msg.read = true })
        
        // 更新未读计数
        await this.fetchUnreadCounts()
        
        this.$message.success('已将所有消息标记为已读')
        this.$emit('all-read')
      } catch (error) {
        this.$message.error('设置所有消息已读失败')
      }
    },
    
    // 高亮显示新消息
    focusNewMessage(message) {
      if (!message) return
      
      // 确保打开对应的标签页
      this.activeTab = message.type
      
      // 等待DOM更新
      this.$nextTick(() => {
        // 获取消息列表DOM
        const listRef = this.$refs.notificationList
        if (!listRef) {
          return
        }
        
        // 由于新消息是unshift到数组前面，所以第一个元素就是新消息
        const firstMessageElement = listRef.querySelector('.notification-item')
        if (firstMessageElement) {
          // 滚动到该元素，添加平滑滚动
          listRef.scrollTo({
            top: 0,
            behavior: 'smooth'
          })
          
          // 添加高亮效果
          firstMessageElement.classList.add('highlight-new-message')
          
          // 3秒后移除高亮效果
          setTimeout(() => {
            firstMessageElement.classList.remove('highlight-new-message')
          }, 3000)
        }
      })
    },
    
    // 获取当前活动标签页的消息列表DOM引用
    getCurrentList() {
      return this.$refs.notificationList
    },
    
    // 获取图标类名
    getIconClass(type) {
      switch (type) {
        case 'system':
          return 'system'
        case 'admin':
          return 'admin'
        case 'group':
          return 'group'
        default:
          return 'system'
      }
    },
    
    // 根据类型获取图标
    getIconByType(type) {
      switch (type) {
        case 'system':
          return 'el-icon-bell'
        case 'admin':
          return 'el-icon-user'
        case 'group':
          return 'el-icon-user-solid'
        default:
          return 'el-icon-message'
      }
    },
    
    // 获取文件URL
    getFileUrl(picUrl) {
      if (!picUrl) return ''
      return this.fileBaseUrl + picUrl
    },
    
    // 格式化时间
    formatTime(time) {
      return formatTime(time)
    },
    
    // 获取空状态文本
    getEmptyText() {
      switch (this.activeTab) {
        case 'system':
          return '暂无公告通知'
        case 'admin':
          return '暂无私信消息'
        case 'group':
          return '暂无系统消息'
        default:
          return '暂无消息通知'
      }
    },
    
    // 聚焦到指定消息
    focusMessage(messageId) {
      if (!messageId) return
      
      // 等待DOM更新
      this.$nextTick(() => {
        // 查找消息元素
        const messageElement = this.$el.querySelector(`[data-message-id="${messageId}"]`)
        if (messageElement) {
          // 滚动到该元素
          messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
          
          // 添加高亮效果
          messageElement.classList.add('highlight-new-message')
          
          // 3秒后移除高亮效果
          setTimeout(() => {
            messageElement.classList.remove('highlight-new-message')
          }, 3000)
        }
      })
    },
    
    // 处理分组选择变化
    handleGroupChange() {
      this.pagination.current = 1
      this.fetchMessages()
    },
    
    // 检查导出功能是否可用
    async checkExportAvailability() {
      try {
        const response = await getSystemConfig('builtin.notice.im.export')
        if (response && response !== '') {
          this.exportUrl = response
        }
      } catch (error) {
        // 静默失败，不显示导出按钮
        this.exportUrl = ''
      }
    },
    
    // 处理导出
    async handleExport() {
      if (!this.exportUrl || !this.selectedGroup) {
        this.$message.warning('请先选择一个分组再导出');
        return;
      }
      
      try {
        this.$message({
          message: '正在导出消息，请稍候...',
          type: 'info'
        });
        
        // 构建查询参数，必须包含分组名称
        const params = {
          chatType: this.chatType,
          groupName: this.selectedGroup
        };
        
        // 调用导出API
        const result = await exportMessages(this.exportUrl, params);
        
        if (result === '导出成功') {
          this.$message({
            message: '导出成功',
            type: 'success'
          });
        }
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败，请稍后再试');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-list-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background-color: #fafbfc;
  border-radius: 8px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
  
  // 顶部操作栏
  .top-bar {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

    .left-actions {
      width: 320px;

      ::v-deep .el-input__inner {
        border-radius: 20px 0 0 20px;
        padding-left: 40px;
        transition: all 0.3s;

        &:focus {
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      ::v-deep .el-input__prefix {
        left: 12px;
      }
      
      ::v-deep .el-input-group__append {
        background-color: #409EFF;
        border-color: #409EFF;
        color: white;
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px;
        padding: 0 15px;
        
        .el-button {
          border: none;
          background: transparent;
          color: white;
          font-size: 14px;
          padding: 0;
          margin: 0;
          
          &:hover {
            color: white;
            opacity: 0.9;
          }
          
          &:focus, &:active {
            color: white;
            background: transparent;
          }
        }
        
        &:hover {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }

    .right-actions {
      display: flex;
      gap: 12px;
      align-items: center;
      
      .select-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        
        .prefix-icon {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;
          font-size: 16px;
          
          &.el-icon-folder {
            color: #409EFF;
          }
          
          &.el-icon-folder-opened {
            color: #909399;
          }
        }
        
        .group-select {
          width: 180px;
          
          ::v-deep .el-input__inner {
            border-radius: 20px;
            transition: all 0.3s;
            height: 36px;
            line-height: 36px;
            padding-left: 35px; /* 增加左侧内边距以适应图标 */

            &:focus {
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }
      }

      .export-btn {
        padding: 9px 20px;
        border-radius: 20px;
        transition: all 0.3s;
        font-weight: 500;
        background-color: #ecf5ff;
        border-color: #d9ecff;
        color: #409EFF;
        
        &:hover {
          background-color: #409EFF;
          border-color: #409EFF;
          color: white;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
        
        i {
          margin-right: 4px;
          font-size: 14px;
        }
      }

      .el-button {
        padding: 9px 20px;
        border-radius: 20px;
        transition: all 0.3s;
        font-weight: 500;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
      
      .all-read-btn {
        position: relative;
        overflow: hidden;
        
        &::after {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: rgba(255, 255, 255, 0.1);
          transform: rotate(45deg);
          animation: btn-shine 3s ease-in-out infinite;
        }
      }
    }
  }
  
  // 标签页样式
  ::v-deep .notification-tabs {
    .el-tabs__header {
      margin: 0;
      padding: 0 24px;
      background-color: #fff;
      border-bottom: 1px solid #ebeef5;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    .el-tabs__nav-wrap::after {
      height: 1px;
    }

    .el-tabs__item {
      height: 48px;
      line-height: 48px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;
      padding: 0 24px;
      
      &.is-active {
        color: #409EFF;
      }
      
      &:hover {
        color: #409EFF;
      }
    }

    .el-tabs__active-bar {
      height: 3px;
      border-radius: 3px;
      background-color: #409EFF;
      bottom: 0px;
    }

    .el-tabs__content {
      padding: 0;
    }
  }

  // 消息列表
  .notification-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    padding-bottom: 0;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    will-change: transform;
    transform: translateZ(0);
    
    // 细滚动条样式
    scrollbar-width: thin; /* Firefox */
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background-color: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
    
    // 消息项
    .notification-item {
      padding: 16px;
      display: flex;
      align-items: flex-start;
      gap: 16px;
      cursor: pointer;
      transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
      position: relative;
      border-radius: 12px;
      margin-bottom: 10px;
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
      transform: translateZ(0);
      backface-visibility: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background-color: #f9faff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        transform: translateY(-1px);
      }

      &.unread {
        background-color: #f0f7ff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);

        &:hover {
          background-color: #e6f1ff;
        }

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 40%;
          background: #409EFF;
          border-radius: 0 4px 4px 0;
        }
      }
      
      // 用户头像
      .user-avatar {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        border: 2px solid #fff;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      // 图标样式
      .item-icon {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);

        i {
          font-size: 20px;
          color: #fff;
        }

        &.system {
          background: linear-gradient(140deg, #409EFF, #007AFF);
        }

        &.admin {
          background: linear-gradient(140deg, #67C23A, #4CAF50);
        }

        &.group {
          background: linear-gradient(140deg, #E6A23C, #FF9800);
        }
      }
      
      // 消息内容
      .item-content {
        flex: 1;
        min-width: 0;
        cursor: pointer;
        display: flex;
        
        .item-main {
          flex: 1;
          min-width: 0;
        }
        
        .item-right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          margin-left: 16px;
          min-width: 70px;
        }

        .item-header {
          margin-bottom: 8px;
        }

        .item-title {
          font-size: 15px;
          color: #303133;
          font-weight: 600;
          line-height: 1.4;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 8px;
          
          .group-tag {
            font-size: 11px;
            height: 20px;
            line-height: 18px;
            background-color: #f0f9eb;
            border-color: #e1f3d8;
            border-radius: 10px;
            padding: 0 8px;
            font-weight: 500;
          }
        }
        
        .item-actions {
          margin-bottom: 6px;
          text-align: center;
          width: 100%;
          
          .el-button {
            padding: 8px 14px;
            border-radius: 20px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            font-weight: 500;
            transition: all 0.3s;
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            }
          }
        }
        
        .item-status {
          display: flex;
          justify-content: center;
          width: 100%;
          
          .el-tag {
            text-align: center;
            padding: 0 10px;
            height: 24px;
            line-height: 22px;
            font-weight: 500;
            border-radius: 12px;
            transition: all 0.3s;
            
            &.el-tag--danger {
              background-color: #fff0f0;
              color: #f56c6c;
              border-color: #fcd3d3;
            }
            
            &.el-tag--info {
              background-color: #f4f6f8;
              color: #909399;
              border-color: #e9ecef;
            }
          }
        }

        .item-desc {
          font-size: 14px;
          color: #606266;
          margin-bottom: 10px;
          line-height: 1.5;
          white-space: pre-wrap;
          word-break: break-word;
        }

        .item-meta {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 13px;
          color: #909399;

          .time {
            color: #909399;
            background-color: #f5f7fa;
            padding: 2px 8px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 12px;
          }
        }
      }
    }
    
    // 空状态
    .empty-state {
      padding: 60px 0;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      i {
        font-size: 64px;
        color: #cbd5e1;
        margin-bottom: 20px;
        display: block;
      }

      p {
        color: #94a3b8;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  // 分页器
  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 56px;
    margin: 0;
    padding: 0;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
    flex-shrink: 0;
    width: 100%;
    
    ::v-deep .el-pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: 4px;
      
      .btn-prev, 
      .btn-next,
      .el-pager li {
        background: #f5f7fa;
        border-radius: 4px;
        margin: 0 4px;
        
        &:hover {
          color: #409EFF;
        }
        
        &.is-active {
          background-color: #409EFF;
          color: #fff;
          font-weight: 600;
        }
      }

      .el-pagination__jump {
        margin-left: 12px;
      }
    }
  }
  
  // 加载状态
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    backdrop-filter: blur(2px);
    
    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      i {
        font-size: 32px;
        color: #409EFF;
        margin-bottom: 10px;
      }
      
      span {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

// 分组选择下拉选项样式
.group-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 7px 0;
  
  .group-info {
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      font-size: 16px;
    }
    
    .group-name {
      font-size: 14px;
    }
  }
  
  .count-badge {
    background-color: #f56c6c;
    color: #fff;
    border-radius: 10px;
    min-width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    padding: 0 6px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.35);
  }
}

::v-deep .group-select-dropdown {
  .el-select-dropdown__item {
    padding: 0 15px;
    height: 40px;
    
    &.selected {
      color: #409EFF;
      font-weight: 600;
    }
  }
}

.tab-label {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.speaker-name {
  font-weight: 600;
  color: #303133;
}

.highlight-new-message {
  animation: highlight-pulse 3s cubic-bezier(0.22, 0.61, 0.36, 1);
}

@keyframes highlight-pulse {
  0% {
    background-color: rgba(64, 158, 255, 0.25);
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.15);
  }
  50% {
    background-color: rgba(64, 158, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }
  100% {
    background-color: #f9faff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

// 全局富文本样式
::v-deep .rich-content {
  line-height: 1.8;
  color: #303133;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;

  * {
    max-width: 100%;
  }
  
  img {
    max-width: 100%;
    border-radius: 6px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  }
  
  a {
    color: #409EFF;
    text-decoration: none;
    transition: all 0.2s;
    font-weight: 500;
    
    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }
  
  p {
    margin: 10px 0;
  }
  
  ul, ol {
    padding-left: 20px;
    margin: 10px 0;
  }
  
  code {
    background-color: #f5f7fa;
    color: #e6a23c;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: monospace;
  }
  
  pre {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 12px 0;
  }
}

::v-deep .tab-badge {
  margin-left: 6px;
  transform: scale(0.8);
  transform-origin: right center;
  
  .el-badge__content {
    border: none;
    box-shadow: 0 2px 6px rgba(245, 108, 108, 0.35);
  }
}

// 动画效果
.message-fade-enter-active, .message-fade-leave-active {
  transition: all 0.5s;
}
.message-fade-enter, .message-fade-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

@keyframes btn-shine {
  0% {
    left: -100%;
    opacity: 0;
  }
  20% {
    left: 100%;
    opacity: 0.6;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}
</style> 