<template>
    <div>
        <div style="margin-bottom: 5px;padding-top: 5px;line-height: 35px;"
            class="el-cascader-panel-popper-class-user-tags">
            <el-tag style="margin-left: 5px;;margin-right: 5px;" size="small" closable
                v-for="(item, index) in tempItems" :key="index" @close="tagClose(item, index)">{{ item.name }}</el-tag>
        </div>
        <div style="overflow-x: scroll;">
            <el-cascader-panel ref="cascader" v-model="selectValues" :props="oprops"
                class="el-cascader-panel-popper-class-user-select" clearable @change="change" :options="options">
                <template slot-scope="{ node, data }">
                    <i v-if="data.leafType === 'account'" class="el-icon-user" style="margin-right: 5px;" />
                    <span>{{ node.label }}</span>
                </template>
            </el-cascader-panel>
        </div>
        <!-- <cascaderEx ref="cascader" class="el-cascader-panel-popper-class-user-select" v-model="selectValues"
            :props="oprops" clearable @change="change" :options="options">
            <template slot-scope="{ node, data }">
                <i v-if="data.leafType === 'account'" class="el-icon-user" style="margin-right: 5px;" />
                <span>{{ node.label }}</span>
            </template>
        </cascaderEx> -->
    </div>

</template>
<script>
import { isEqual } from 'element-ui/src/utils/util';
import { getDeptList, getAccountList } from './api'
// import cascaderEx from './cascaderEx.vue'
export default {
    // components: { cascaderEx },
    props: {
        type: {
            type: String,
            default: 'dept'
        },
        values: {
            type: Array,
            default: function () {
                return []
            }
        }
    },
    data() {
        return {
            selectValues: [],
            tempItems: this.values,
            options: [],
            oprops: {
                value: 'id',
                label: 'name',
                multiple: true,
                emitPath: false,
                checkStrictly: true,
                // lazy: true,
                // lazyLoad: this.lazyLoad,
            },
        }
    },

    mounted() {
        this.selectValues = this.values.map(m => { return m.id })
        this.load(0, null, {}, this.callback)
    },
    methods: {
        tagClose(item, index) {
            this.tempItems.splice(index, 1)
            this.selectValues = this.selectValues.filter(n => !isEqual(n, item.id));
        },

        change(e) {

            let list = this.$refs.cascader.getCheckedNodes().map(node => {
                return {
                    id: node.value,
                    name: node.label,
                    type: node.data.leafType
                }
            })
            this.tempItems = list
            this.$emit('change', list)
        },

        callback(level, data, list) {
            if (level == 0) {
                list.forEach(f => {
                    this.load(1, f.id, f, this.callback)
                })
                this.options = list
            } else {
                if (list.length > 0) {
                    data.children = list
                    list.forEach(f => {
                        this.load(level + 1, f.id, f, this.callback)
                    })
                }
            }
        },

        load(level, value, data, resolve) {
            this.getDeptList(level, value, data)
                .then((list) => {
                    resolve(level, data, [...list[0].map(m => {
                        m.leafType = 'dept'
                        m.children = []
                        return m
                    }), ...list[1].map(m => {
                        m.leafType = 'account'
                        m.leaf = 'leaf'
                        return m
                    })])
                })
        },

        // lazyLoad(node, resolve) {
        //     const { level, value, data } = node
        //     this.getDeptList(level, value, data)
        //         .then((list) => {
        //             if (level === 0) {
        //                 setTimeout(() => {
        //                     this.selectValues = this.values.map(m => { return m.id })
        //                 }, 200)
        //             } else {
        //                 list.forEach(e => {
        //                     let it = this.tempItems.find(f => f.id == e.id)
        //                     if (it && this.selectValues.indexOf(e.id) == -1) {
        //                         this.selectValues.push(e.id)
        //                     }
        //                 });
        //             }
        //             resolve([...list[0].map(m => {
        //                 m.leafType = 'dept'
        //                 return m
        //             }), ...list[1].map(m => {
        //                 m.leafType = 'account'
        //                 m.leaf = 'leaf'
        //                 return m
        //             })])
        //         })
        // },

        getDeptList(level, value, data) {
            const { leafType } = data || {}

            return Promise.all([new Promise(resolve => {

                if (leafType === 'account') {
                    resolve([])
                } else {
                    getDeptList({ parentId: level === 0 ? 0 : value }).then(data => {
                        resolve(data?.records || [])
                    }).catch(() => {
                        resolve([])
                    })
                }
            }), new Promise(resolve => {
                if (leafType === 'account' || this.type === 'dept') {
                    resolve([])
                } else {
                    getAccountList({ deptId: value, 'nulls[deptId]': value || value === 0 ? null : true }).then(data => {
                        resolve(data?.records || [])
                    }).catch(() => {
                        resolve([])
                    })
                }
            })])
        }
    }
}
</script>
<style lang="scss">
.el-cascader-panel-popper-class-user-tags {
    border-radius: 0.20833vw;
    border: 1px solid #ddd;
    width: 100%;
    margin-top: 5px;
    padding-bottom: 5px;
    max-height: 160px;
    min-height: 40px;
    overflow-y: scroll;

    &:hover {
        border-color: #409EFF;
    }
}

.el-cascader-panel-popper-class-user-select {
    width: 100%;
    min-height: 200px;

    ::v-deep .el-cascader-menu__wrap {
        height: fit-content !important;
        max-height: 500px;
    }

    ::v-deep .el-cascader-menu__empty-text {
        display: none !important;
    }
}
</style>