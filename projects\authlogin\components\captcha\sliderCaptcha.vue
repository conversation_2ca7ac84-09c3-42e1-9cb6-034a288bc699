<template>
  <el-dialog :visible.sync="dialogVisible" :width="`${dialogWidth}px`" title="安全验证" top="30vh" custom-class="captcha-dialog">
    <div class="captcha-container">
      <div class="slider-captcha">
        <el-image v-loading="loading" ref="background" id="background" :src="codeResult.base64 | base64ToImage" @load="imageLoad" @dragstart.prevent style="min-height: 100px; min-width: 280px;" />
        <el-image id="mask" style="position: absolute; cursor: pointer;" v-if="codeResult.mask" :src="codeResult.mask | base64ToImage" :style="position" @mousedown="maskMove = true" @mouseup="maskMove = false" @dragstart.prevent />
        <el-slider :disabled="loading" :value="value" :max="sliderLength" @change="sliderChange" :show-tooltip="false" class="slider" @input="val => $emit('change', val)" ref="slider"/>
        <i class="el-icon-refresh-right" style="position: absolute; right: 10px; top: 10px; cursor: pointer; color: white" @click="refresh" />
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "textCaptcha",
  data() {
    return {
      sliderLength: 100,
      maskMove: false,
      dialogVisible: false,
      dialogWidth: 350,
      callback: undefined,
      rules: [
        { trigger: 'blur',validator: (rule, value, callback) => {
          this.dialogVisible = true
          this.refresh()
          this.callback = callback
        }}
      ],
      loading: false
    }
  },
  props: {
    codeResult: {
      type: Object,
      default: () => { 
        return {}
      }
    },
    value: {
      type: Number,
      default: 0
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  mounted() {
    document.addEventListener('mousemove', this.mousemove)
    document.addEventListener('mouseup', this.mouseup)
    this.$emit('mounted')
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.mousemove)
    document.addEventListener('mouseup', this.mouseup)
  },
  computed: {
    position() {
      return {
        top: `${this.codeResult?.position?.y || 0}px`, 
        left: `${this.value || this.codeResult?.position?.x || 0}px`
      }
    }
  },
  filters: {
    base64ToImage(base64) {
      return `data:image/png;base64,${base64}`
    }
  },
  methods: {
    imageLoad(e) {
      this.sliderLength = e.target.width - document.getElementById('mask').width
      this.dialogWidth = e.target.width + 60
      this.loading = false
    },
    refresh() {
      this.loading = true
      this.$emit('refresh')
      this.$emit('change', 0)
    },
    sliderChange() {
    },
    mousemove({movementX}) {
      let p = this.value + movementX * 1.3
      if (this.maskMove && p <= this.sliderLength && p >= 0) {
        this.$emit('change', p)
      }
    },
    mouseup(){
      this.maskMove = false
      this.callback && this.callback()
      delete this.callback
    },
    onError() {
      this.dialogVisible = false
    },
    onSuccess() {
      var el = this.$refs.slider.$el
      var sliderButton = el.querySelector('.el-slider__button')
      sliderButton.style.setProperty('--before-content', "'✔'");
      sliderButton.style.setProperty('--before-color', '#67C23A');
      var sliderBar = el.querySelector('.el-slider__bar')
      sliderBar.style.background = "linear-gradient(to right,#409EFF, #67C23A)" ;
    }
  }
}

</script>

<style scoped lang="scss">
::v-deep .captcha-dialog{
  border-radius: 10px;
  overflow: hidden;
}

.captcha-container {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

::v-deep #background{
  border-radius: 10px;
  width: auto;
  height: auto;
  max-width: 100%;
}

.slider-captcha {
  line-height: 15px;
  position: relative;
  display: inline-block;
  overflow: hidden;
  border-radius: 10px;
}

.slider {
  height: 40px;
  margin-top: 5px;
  width: 100%;
  overflow: hidden;
  ::v-deep .el-slider__runway::after {
    content: "向右滑动";
    margin: 0 auto;
    display: block;
    text-align: center;
    color: #999999;
  }
  ::v-deep .el-slider__runway {
    display: flex;
    align-items: center;
    justify-items: center;
    height: 100%;
    margin: 0;
    border-radius: 20px;
    overflow: hidden;
    background-color: #f5f5f5;
    border: 1px solid #e4e7ed;
    .el-slider__bar {
      height: 100%;
      border-radius: 20px 0 0 20px;
    }
    .el-slider__button-wrapper {
      height: 100%;
      top: 0;
      .el-slider__button::before {
        content: var(--before-content, '➟');
        font-weight: bolder;
        color: var(--before-color, #409EFF);
      }
      .el-slider__button {
        height: 36px;
        width: 36px;
        // border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #409EFF;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }
    }
  }
}
</style>