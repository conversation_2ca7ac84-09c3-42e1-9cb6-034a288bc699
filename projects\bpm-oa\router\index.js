// import Vue from "vue";
// import Router from "vue-router";

// Vue.use(Router);

// const viewport = {
//   content: "width=device-width, initial-scale=1.0, user-scalable=no"
// }

// const router = new Router({
//   mode: 'history',
//   routes: [
//     {
//       path: '/',
//       redirect: '/workspace'
//     },
//     {
//       path: "/workspace",
//       name: "workspace",
//       component: () => import("@bpm-oa-web/views/workspace/WorkSpace.vue"),
//       children: [
//         {
//           path: "forms",
//           name: "forms",
//           component: () => import("@bpm-oa-web/views/workspace/oa/FromsApp.vue"),
//           meta: { title: 'bpm-ui| OA审批', viewport: viewport },
//         },
//         {
//           path: "submit",
//           name: "submit",
//           component: () => import("@bpm-oa-web/views/workspace/oa/MySubmit.vue"),
//           meta: { title: 'bpm-ui| 我发起的', viewport: viewport },
//         },
//         {
//           path: "cc",
//           name: "cc",
//           component: () => import("@bpm-oa-web/views/workspace/oa/CcMe.vue"),
//           meta: { title: 'bpm-ui | 抄送我的', viewport: viewport },
//         },
//         {
//           path: "unfinished",
//           name: "unfinished",
//           component: () => import("@bpm-oa-web/views/workspace/oa/UnFinished.vue"),
//           meta: { title: 'bpm-ui| 未完成的', viewport: viewport },
//         },
//         {
//           path: "finished",
//           name: "finished",
//           component: () => import("@bpm-oa-web/views/workspace/oa/Finished.vue"),
//           meta: { title: 'bpm-ui| 未完成的', viewport: viewport },
//         },
//         {
//           path: "instances",
//           name: "instances",
//           component: () => import("@bpm-oa-web/views/admin/ProcessInstanceManage.vue"),
//           meta: { title: 'bpm-ui| 数据管理', viewport: viewport }
//         },
//         {
//           path: "formsPanel",
//           name: "formsPanel",
//           component: () => import("@bpm-oa-web/views/admin/FormsPanel.vue"),
//           meta: { title: 'bpm-ui| 表单列表', viewport: viewport }
//         },
//         {
//           path: "subProcess",
//           name: "subProcess",
//           component: () => import("@bpm-oa-web/views/admin/SubProcess.vue"),
//           meta: { title: 'bpm-ui| 子流程管理', viewport: viewport }
//         },
//       ]
//     },
//     {
//       path: "/mbinitiate",
//       name: "mbinitiate",
//       component: () => import("@bpm-oa-web/views/workspace/MbInitiateProcess.vue"),
//       meta: { title: 'bpm-ui| 发起审批', viewport: viewport }
//     },
//     {
//       path: "/mbInstance",
//       name: "mbInstance",
//       component: () => import("@bpm-oa-web/views/workspace/MbInstanceViewer.vue"),
//       meta: { title: 'bpm-ui| 流程详情', viewport: viewport }
//     },
//     {
//       path: "/admin/design",
//       name: "design",
//       component: () => import("@bpm-oa-web/views/admin/FormProcessDesign.vue"),
//       meta: { title: 'bpm-ui| 表单流程设计', viewport: viewport }
//     }, {
//       path: "/admin/design/subProcess",
//       name: "subProcess",
//       component: () => import("@bpm-oa-web/views/admin/subProcess/SubProcessDesign.vue"),
//       meta: { title: 'bpm-ui| 子流程设计', viewport: viewport }
//     },
//     {
//       path: "/testForm",
//       name: "testForm",
//       component: () => import("@bpm-oa-web/views/common/form/TestForm.vue"),
//       meta: { title: 'bpm-ui| 表单演示', viewport: viewport }
//     }
//   ]
// })

// router.beforeEach((to, from, next) => {
//   if (to.meta.title) {
//     document.title = to.meta.title
//   }
//   if (to.meta.content) {
//     let head = document.getElementByTagName('head')
//     let meta = document.createElemnet('meta')
//     meta.name = 'viewport'
//     meta.content = "width=device-width, initial-scale=1.0, user-scalable=no"
//     head[0].appendChild(meta)
//   }
//   next();
//   sessionStorage.setItem('router-path', to.path)
// })

// export default router;
