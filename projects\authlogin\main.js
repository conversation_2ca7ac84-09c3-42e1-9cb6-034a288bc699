import Vue from 'vue'
import Router from 'vue-router'
import App from './App'
Vue.use(Router)

import ElementUI from 'element-ui'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n
import '@/styles/element-ui/theme/index.css'
Vue.use(ElementUI, {
    locale
})

const route = new Router({
    base: process.env.BASE_URL,
    mode: process.env.SERVE_ALL ? 'hash' : "history",
    scrollBehavior: () => ({ y: 0 }),
    routes: [
        {
            path: '/',
            component: () => import('./views/index.vue'),
            hidden: true
        },
    ]
})


new Vue({
    el: '#app',
    router: route,
    render: h => h(App)
})
