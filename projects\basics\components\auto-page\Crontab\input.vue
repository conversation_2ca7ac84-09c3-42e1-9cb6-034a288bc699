<template>
  <div>
    <el-input v-model="cronExpression" placeholder="请输入cron执行表达式">
      <template slot="append">
        <el-button type="primary" @click="handleShowCron">
          生成表达式
          <i class="el-icon-time el-icon--right"></i>
        </el-button>
      </template>
    </el-input>
    <el-dialog
      title="Cron表达式生成器"
      :visible.sync="openCron"
      append-to-body
      destroy-on-close
      class="scrollbar"
    >
      <Crontab
        @hide="openCron = false"
        @fill="crontabFill"
        :expression="expression"
      ></Crontab>
    </el-dialog>
  </div>
</template>

<script>
import Crontab from "./index.vue";
import { isEqual } from "element-ui/src/utils/util";

export default {
  components: {
    Crontab,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      openCron: false,
      cronExpression: "",
      expression: "",
    };
  },
  methods: {
    handleShowCron() {
      this.expression = this.cronExpression;
      this.openCron = true;
    },
    crontabFill(expression) {
      this.cronExpression = expression;
      this.openCron = false;
    },
  },

  watch: {
    value: {
      handler(val) {
        if (!isEqual(val, this.cronExpression)) {
          this.cronExpression = val;
          this.$emit("value", val);
        }
      },
      immediate: true,
    },
    cronExpression(val) {
      if (!isEqual(val, this.value)) {
        this.$emit("value", val);
      }
    },
  },
};
</script>

<style></style>
