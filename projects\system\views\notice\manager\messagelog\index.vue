<template>
  <div class="message-log-container">
    <div class="page-header">
      <div class="header-title">
        <h2>推送记录</h2>
      </div>
      
      <div class="header-tools">
        <div class="unified-search">
          <el-input v-model="queryParams.keyword" placeholder="请输入接收对象搜索" prefix-icon="el-icon-search" clearable
            class="search-input" @keyup.enter.native="handleQuery" @clear="handleQuery" />

          <el-select v-model="queryParams.providerId" placeholder="服务提供商" clearable class="provider-select">
            <el-option v-for="provider in providers" :key="provider.id" :label="provider.name"
              :value="provider.id" />
          </el-select>

          <el-select v-model="queryParams.sendStatus" placeholder="发送状态" clearable class="status-select">
            <el-option label="未发送" value="NOT_SENT" />
            <el-option label="发送完成" value="SENT" />
            <el-option label="发送失败" value="FAILED" />
            <el-option label="调用超时" value="TIMEOUT" />
          </el-select>

          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="handleDateRangeChange"
            class="date-range-picker" />
        </div>

        <div class="button-group">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button type="primary" icon="el-icon-refresh" @click="refreshList">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <el-table v-loading="loading" :data="messageList" border stripe fit style="width: 100%" highlight-current-row>
        <el-table-column prop="providerName" label="服务提供商" :width=rpx(120) />
        <el-table-column prop="messageContent" label="消息内容" min-width="250">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="400" trigger="hover">
              <div class="popover-content">{{ scope.row.messageContent }}</div>
              <div slot="reference" class="message-content-cell text-ellipsis">
                {{ scope.row.messageContent }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="noticeTargetDisplay" label="接收对象名称" width="150" show-overflow-tooltip />
        <el-table-column prop="noticeTargetNum" label="接收对象" width="150" show-overflow-tooltip />
        <el-table-column prop="sendStatus" label="发送状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.sendStatus === 'SENT' ? 'success' : 'danger'" effect="dark">
              {{ scope.row.sendStatus === 'SENT' ? '已发送' : '发送失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="responseData" label="响应消息" min-width="200">
          <template slot-scope="scope">
            <el-popover placement="top-start" width="400" trigger="hover">
              <div class="popover-content">{{ scope.row.responseData || scope.row.responseMessage || '暂无消息' }}</div>
              <div slot="reference" class="message-content-cell text-ellipsis">
                {{ scope.row.responseData || scope.row.responseMessage || '暂无消息' }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="sendDateTime" label="发送时间" width="180" />
        <el-table-column label="操作" min-width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewDetail(scope.row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination :current-page="queryParams.current" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.size"
          :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog title="消息详情" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false" append-to-body
      destroy-on-close>
      <el-form class="detail-form" label-width="100px">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="批次ID">
                <span>{{ detailData.batchId }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务提供商">
                <span>{{ detailData.providerName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="插件名称">
                <span>{{ detailData.pluginName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发送状态">
                <el-tag :type="detailData.sendStatus === 'SENT' ? 'success' :
                  detailData.sendStatus === 'FAILED' ? 'danger' :
                    detailData.sendStatus === 'TIMEOUT' ? 'warning' : 'info'" effect="dark">
                  {{ detailData.sendStatus === 'SENT' ? '发送完成' :
                    detailData.sendStatus === 'FAILED' ? '发送失败' :
                      detailData.sendStatus === 'TIMEOUT' ? '调用超时' : '未发送' }}
                </el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发送者">
                <span>{{ detailData.sender }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="接收对象名称">
                <span>{{ detailData.noticeTargetDisplay }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="接收对象">
                <span>{{ detailData.noticeTargetNum }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">消息内容</div>
          <div class="content-wrapper">
            <div class="content-header">
              <span>消息内容</span>
            </div>
            <div class="content-body">
              <pre class="json-viewer" v-html="formatJson(detailData.messageContent)"></pre>
            </div>
          </div>

          <div class="content-wrapper mt-16">
            <div class="content-header">
              <span>模板参数</span>
            </div>
            <div class="content-body">
              <pre class="json-viewer"
                v-html="formatJson(detailData.renderParams ? JSON.stringify(detailData.renderParams, null, 2) : '{}')"></pre>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="section-title">响应信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="响应代码">
                <span>{{ detailData.responseCode }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="响应消息">
                <span>{{ detailData.responseMessage }}</span>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="content-wrapper">
            <div class="content-header">
              <span>响应数据</span>
            </div>
            <div class="content-body">
              <pre class="json-viewer" v-html="formatJson(detailData.responseData)"></pre>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="section-title">时间信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="创建时间">
                <span>{{ detailData.createTime }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发送时间">
                <span>{{ detailData.sendDateTime }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间">
                <span>{{ detailData.updateTime }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMessageLogList, getProviderList } from '@system/api/notice/manager'
import Pagination from '@/components/Pagination'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import json from 'highlight.js/lib/languages/json'

// 注册json语言
hljs.registerLanguage('json', json)

export default {
  name: 'NoticeMessageLog',
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        sendStatus: '',
        keyword: '',
        ranges: {},
        providerId: ''
      },
      providers: [],
      // 日期范围
      dateRange: [],
      // 消息列表数据
      messageList: [],
      // 总记录数
      total: 0,
      // 详情对话框
      dialogVisible: false,
      // 详情数据
      detailData: {}
    }
  },
  async created() {
    await this.loadProviders()
    this.getList()
  },
  methods: {
    /** 加载服务提供商列表 */
    async loadProviders() {
      try {
        const params = { size: -1 } // 查询所有服务提供商
        const response = await getProviderList(params)
        this.providers = response.records || []
      } catch (error) {
        console.error('加载服务提供商失败:', error)
        this.$message.error('加载服务提供商失败')
      }
    },
    /** 获取缩略的响应数据 */
    getAbbreviatedResponseData(row) {
      if (row.responseData) {
        // 如果响应数据超过50个字符，则截取前50个字符并添加省略号
        return row.responseData.length > 50
          ? row.responseData.substring(0, 50) + '...'
          : row.responseData
      } else if (row.responseMessage) {
        return row.responseMessage
      } else {
        return '暂无消息'
      }
    },
    /** 查询消息日志列表 */
    async getList() {
      this.loading = true
      const response = await getMessageLogList(this.queryParams)
      this.messageList = response.records
      this.total = response.total
      this.loading = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1
      this.getList()
    },
    /** 刷新列表 */
    refreshList() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        current: 1,
        size: 10,
        sendStatus: '',
        keyword: '',
        ranges: {},
        providerId: ''
      }
      this.getList()
    },
    /** 查看详情按钮操作 */
    handleViewDetail(row) {
      this.detailData = row
      this.dialogVisible = true
    },
    /** 处理日期范围变化 */
    handleDateRangeChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.queryParams.ranges = {
          ...this.queryParams.ranges,
          sendDateTime: `${dateRange[0]},${dateRange[1]}`
        }
      } else {
        if (this.queryParams.ranges) {
          delete this.queryParams.ranges.sendDateTime
        }
      }
    },
    handleSizeChange(val) {
      this.queryParams.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.current = val
      this.getList()
    },
    // 格式化JSON并高亮显示
    formatJson(value) {
      if (!value) return '';
      try {
        // 如果是对象，先格式化为字符串
        if (typeof value === 'object') {
          value = JSON.stringify(value, null, 2);
        }
        // 使用highlight.js进行语法高亮
        return hljs.highlight('json', value).value;
      } catch (error) {
        return value;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.message-log-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 10px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        white-space: nowrap;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }
    
    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
      justify-content: flex-end;
      
      .unified-search {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: nowrap;

        .search-input {
          width: 180px;

          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;

              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }
          }
        }

        .provider-select,
        .status-select {
          width: 130px;
        }

        .date-range-picker {
          width: 260px;
        }

        ::v-deep .el-select .el-input__inner,
        ::v-deep .el-date-editor.el-input__inner,
        ::v-deep .el-range-editor.el-input__inner {
          height: 36px;
          line-height: 36px;
          border-radius: 8px;
          border: 1px solid #e0e5ee;
          background: #f9fafc;
          transition: all 0.3s ease;

          &:hover {
            border-color: #c0d0e9;
            background: #f5f7fa;
          }

          &:focus {
            background: #fff;
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
        }

        ::v-deep .el-range-separator {
          line-height: 24px;
        }

        ::v-deep .el-range-input {
          background: transparent;
        }
      }
      
      .button-group {
        display: flex;
        gap: 8px;

        .el-button {
          padding: 8px 16px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 8px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          height: 36px;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 16px;
          }
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;

        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;

          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        td {
          padding: 8px 0;

          .cell {
            line-height: 1.5;
          }
        }
      }

      tr {
        transition: all 0.3s;

        &:hover {
          background: #f7f9fc !important;
        }

        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }

      .text-ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
      }

      .message-content-cell {
        cursor: pointer;
      }

      .no-message {
        color: #909399;
        font-style: italic;
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;

      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;

        .btn-prev,
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;

          &:not(.disabled):hover {
            border-color: #409EFF;
          }

          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }

        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }

        .el-pagination__jump {
          margin-left: 16px;
        }

        .el-select .el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }

        .el-pagination__editor.el-input {
          margin: 0 8px;

          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }
}

::v-deep .el-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    color: #606266;
    background: #fff;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .detail-form {
      .form-section {
        background-color: #f8f9fb;
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #f5f7fa;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          margin-bottom: 24px;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .el-form-item__label {
            color: #606266;
            font-weight: 500;
            line-height: 1.5;
            padding-top: 4px;
            vertical-align: top;
          }

          .el-form-item__content {
            color: #303133;
            line-height: 1.5;
            display: flex;
            align-items: flex-start;
            min-height: 32px;
          }
        }

        .content-wrapper {
          border: 1px solid #eef1f7;
          border-radius: 12px;
          overflow: hidden;
          background: #fff;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

          &.mt-16 {
            margin-top: 16px;
          }

          .content-header {
            padding: 16px;
            background: linear-gradient(to right, #fcfcfd, #f9fafc);
            border-bottom: 1px solid #eef1f7;
            font-weight: 600;
            color: #1a1f36;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .content-body {
            padding: 16px;
            max-height: 300px;
            overflow: auto;
            background: #fcfcfd;

            &::-webkit-scrollbar {
              width: 4px;
              height: 4px;
            }

            &::-webkit-scrollbar-thumb {
              border-radius: 4px;
              background: rgba(144, 147, 153, 0.3);

              &:hover {
                background: rgba(144, 147, 153, 0.5);
              }
            }

            &::-webkit-scrollbar-track {
              background: transparent;
            }

            .json-viewer {
              margin: 0;
              white-space: pre-wrap;
              word-break: break-all;
              font-family: Monaco, Menlo, Consolas, monospace;
              font-size: 13px;
              line-height: 1.6;
              color: #303133;
            }
          }
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-button {
      padding: 9px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

::v-deep .el-popover {
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-width: 400px;

  .popover-content {
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    font-size: 14px;
    line-height: 1.6;
    color: #303133;
  }
}

::v-deep .el-form-item__label {
  line-height: 32px;
}

::v-deep .el-form-item__content {
  line-height: 32px;
}
</style>