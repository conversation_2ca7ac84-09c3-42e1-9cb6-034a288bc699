import { constantRoutes } from '@/router'
import Layout from '@/layout/index'
import ParentView from '@/components/ParentView';
import InnerLink from '@/layout/components/InnerLink'
/**
 * 根据从后台获取的资源列表与动态路由列表比对，判断是否有权限
 * @param resources
 * @param route
 */
function hasPermission(resources, route) {
  return resources.some(resource => resource.resUrl === route.path)
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRouterMap
 * @param resources
 */
export function filterAsyncRoutes(routes, resources) { // 通过角色来已经设置的动态路由表过滤
  const res = []

  routes.forEach(route => { // 循环设置好的动态路由
    const tmp = { ...route }
    if (hasPermission(resources, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, resources)
      }
      res.push(tmp)
    }
  })

  return res
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false, level = 0) {
  return asyncRouterMap.filter(route => {
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }

    if (level !== 0 && (route.type == 'MENU' && route.json) || (route.type == 'MENU' && route.meta && route.meta.form)) {
      route.component = () => import("../../views/default/DefaultPage")
    } else if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === 'Layout') {
        route.component = Layout
        if (route.type === 'MENU' && route.children) {
          route.children.forEach(clRoute => {
            clRoute.type = 'MENU'
          })
        }
      } else if (route.component === 'ParentView') {
        route.component = ParentView
      } else if (route.component === 'InnerLink') {
        route.component = InnerLink
      } else {
        route.component = loadView(route.component, route)
      }
    }

    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type, level + 1)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === 'ParentView' && !lastRouter) {
        el.path = el.path.replace(/^\//, '')
        el.children.forEach(c => {
          c.path = (el.path + '/' + c.path).replace(/\/{2,}/g, "/")
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c))
            return
          }
          if (c.path)
            children.push(c)
        })
        return
      }
    }
    if (lastRouter) {
      el.path = (lastRouter.path + '/' + el.path).replace(/\/{2,}/g, "/")
    }
    if (el.path) {
      children = children.concat(el)
    }
  })
  return children
}

const state = {
  routes: [],
  addRoutes: [],
  defaultRoutes: [],
  topbarRouters: [],
  sidebarRouters: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  SET_DEFAULT_ROUTES: (state, routes) => {
    state.defaultRoutes = constantRoutes.concat(routes)
  },
  SET_TOPBAR_ROUTES: (state, routes) => {
    state.topbarRouters = routes;
  },
  SET_SIDEBAR_ROUTERS: (state, routes) => {
    state.sidebarRouters = routes
  },
}

const actions = {

  // 生成路由
  generateRoutes({ commit }, resources) {
    return new Promise(resolve => {
      const sdata = JSON.parse(JSON.stringify(resources))
      const rdata = JSON.parse(JSON.stringify(resources))
      const sidebarRoutes = filterAsyncRouter(sdata)
      const rewriteRoutes = filterAsyncRouter(rdata, false, true)
      rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })
      commit('SET_ROUTES', rewriteRoutes)
      commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(sidebarRoutes))
      commit('SET_DEFAULT_ROUTES', sidebarRoutes)
      commit('SET_TOPBAR_ROUTES', sidebarRoutes)
      resolve(rewriteRoutes)
    })
  },
}

// 路由懒加载
export const loadView = (view, route) => {

  return () => {
    try {
      return plugin_loadView.default(view).then((res) => {
        if (route && route.name) {
          res.default.name = route.name
        }
        return res
      }).catch((error) => {
        console.error('组件地址不存在___' + view + '___: ' + error);
        return import('@/views/404')
      })
    } catch (error) {
      console.error('组件地址不存在___' + view + '___: ' + error);
      return import('@/views/404')
    }
  }
}

export default {
  // namespaced: true,
  state,
  mutations,
  actions
}
