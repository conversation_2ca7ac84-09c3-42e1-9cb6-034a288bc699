<template>
  <div class="living-container">
    <div class="page-header">
      <div class="header-title">
        <h2>在线用户</h2>
      </div>
      
      <!-- 将工具栏移到标题行 -->
      <div class="header-tools">
        <div class="unified-search">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入用户名/姓名/IP搜索"
            prefix-icon="el-icon-search"
            clearable
            class="search-input"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>
        </div>
        
        <div class="group-toggle">
          <el-switch
            v-model="enableGrouping"
            active-text="按用户名分组"
            inactive-text="不分组"
            @change="handleGroupingChange"
          ></el-switch>
        </div>
        
        <el-button type="primary" icon="el-icon-refresh" @click="refreshList">
          刷新列表
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-card-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stats-card total-users">
            <div class="icon-container">
              <i class="el-icon-user"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">在线用户</div>
              <div class="stats-value">{{ userList.length }}</div>
              <div class="stats-desc">当前系统中的在线用户数量</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card active-users">
            <div class="icon-container">
              <i class="el-icon-s-custom"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">活跃用户</div>
              <div class="stats-value">{{ getActiveUsers }}</div>
              <div class="stats-desc">最近30分钟有活动的用户</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card unique-ips">
            <div class="icon-container">
              <i class="el-icon-location"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">独立IP数</div>
              <div class="stats-value">{{ uniqueIps.length }}</div>
              <div class="stats-desc">不同IP地址的在线用户数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card admin-users">
            <div class="icon-container">
              <i class="el-icon-s-tools"></i>
            </div>
            <div class="stats-info">
              <div class="stats-title">管理员用户</div>
              <div class="stats-value">{{ adminUsers.length }}</div>
              <div class="stats-desc">当前在线的管理员用户数</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="table-wrapper">
      <div v-if="selectedUsers.length > 0" class="batch-actions-bar">
        <div class="selection-info">
          已选择 <span class="count">{{ selectedUsers.length }}</span> 项
          <el-button type="text" @click="$refs.userTable.clearSelection()">清空选择</el-button>
        </div>
        <div class="batch-buttons">
          <el-button-group class="operation-group">
            <el-button 
              type="danger" 
              icon="el-icon-close" 
              size="small"
              @click="handleBatchOffline"
            >强制下线</el-button>
          </el-button-group>
        </div>
      </div>

      <el-table 
        ref="userTable"
        v-loading="loading" 
        :data="filteredUserList" 
        border 
        stripe
        fit
        style="width: 100%"
        highlight-current-row
        @selection-change="handleSelectionChange"
        :row-key="getRowKey"
        :span-method="enableGrouping ? spanMethod : null"
      >
        <el-table-column 
          type="selection" 
          :width=rpx(50)
          align="center"
          :selectable="row => !isCurrentUser(row.token)"
        />
        <el-table-column prop="username" label="账号" min-width="120">
          <template slot-scope="scope">
            <div class="user-cell">
              <el-avatar :size="24" icon="el-icon-user"></el-avatar>
              <span>{{ scope.row.username }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="姓名" min-width="120" />
        
        <el-table-column prop="host" label="登录IP" min-width="120" />

        <el-table-column prop="loginTime" label="登录时间" min-width="160" />
        
        <el-table-column prop="lastLoginTime" label="上次登录时间" min-width="160" />

        <el-table-column label="角色" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="role-tags">
              <el-tag 
                v-for="(role, index) in scope.row.roles" 
                :key="index" 
                size="small"
                type="info"
                effect="plain"
                class="role-tag"
              >
                {{ role }}
              </el-tag>
              <span v-if="!scope.row.roles || scope.row.roles.length === 0" class="no-roles">暂无角色</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" :width="rpx(120)" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleOffline(scope.row)"
              :disabled="isCurrentUser(scope.row.token)"
            >
              强制下线
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getOnlineUsers, forceOffline } from '@system/api/sys/living'

export default {
  name: 'OnlineUserManagement',
  data() {
    return {
      loading: false,
      userList: [],
      queryParams: {
        keyword: ''
      },
      selectedUsers: [],
      selectedUserItems: [],
      enableGrouping: false,
      spanColumns: ['username'] // 需要合并的列
    }
  },
  computed: {
    filteredUserList() {
      let result = [];
      
      if (!this.queryParams.keyword) {
        result = [...this.userList];
      } else {
        const keyword = this.queryParams.keyword.toLowerCase();
        result = this.userList.filter(user => {
          return (
            (user.username && user.username.toLowerCase().includes(keyword)) ||
            (user.name && user.name.toLowerCase().includes(keyword)) ||
            (user.host && user.host.toLowerCase().includes(keyword))
          );
        });
      }
      
      // 如果启用分组，处理分组数据
      if (this.enableGrouping) {
        return this.processUserList(result);
      }
      
      return result;
    },
    currentUserToken() {
      return this.$store.getters.token || ''
    },
    // 新增计算属性
    uniqueIps() {
      const ips = new Set()
      this.userList.forEach(user => {
        if (user.host) {
          ips.add(user.host)
        }
      })
      return Array.from(ips)
    },
    adminUsers() {
      return this.userList.filter(user => 
        user.roles && user.roles.some(role => 
          role.toLowerCase().includes('admin') || role.includes('管理员')
        )
      )
    },
    getActiveUsers() {
      // 计算30分钟内有活动的用户数量
      const now = new Date()
      const thirtyMinutesAgo = new Date(now - 30 * 60 * 1000)
      
      return this.userList.filter(user => {
        if (!user.lastLoginTime) return false
        const lastActiveTime = new Date(user.lastLoginTime)
        return lastActiveTime >= thirtyMinutesAgo
      }).length
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const data  = await getOnlineUsers()
        this.userList = data || []
      } catch (error) {
        console.error('获取在线用户列表失败:', error)
        this.$message.error('获取在线用户列表失败')
      }
      this.loading = false
    },
    // 处理用户列表，添加分组索引
    processUserList(list) {
      if (!list || list.length === 0) {
        return [];
      }
      
      try {
        // 按用户名分组并排序
        const sortedList = [...list].sort((a, b) => {
          if (!a.username) return 1;
          if (!b.username) return -1;
          return String(a.username).localeCompare(String(b.username));
        });
        
        // 添加分组索引
        let currentUsername = null;
        let groupStart = 0;
        
        // 先标记分组
        const processedList = sortedList.map((item, index) => {
          const newItem = { ...item };
          
          if (index === 0 || newItem.username !== currentUsername) {
            // 处理上一组的span
            if (index > 0) {
              const groupSpan = index - groupStart;
              for (let i = groupStart; i < index; i++) {
                sortedList[i]._groupSpan = groupSpan;
              }
            }
            
            // 开始新的一组
            currentUsername = newItem.username;
            groupStart = index;
          }
          
          return newItem;
        });
        
        // 处理最后一组
        const lastGroupSpan = sortedList.length - groupStart;
        for (let i = groupStart; i < sortedList.length; i++) {
          sortedList[i]._groupSpan = lastGroupSpan;
        }
        
        return sortedList;
      } catch (error) {
        console.error('处理用户分组数据出错:', error);
        return list; // 出错时返回原始列表
      }
    },
    // 表格行合并方法
    spanMethod({ row, column, rowIndex }) {
      if (!this.enableGrouping) return;
      if (!row || !column) return;
      
      // 获取当前列名
      const columnProperty = column.property;
      
      // 只对指定列进行合并
      if (this.spanColumns.includes(columnProperty)) {
        // 安全检查
        if (rowIndex === 0) {
          // 第一行显示
          return { rowspan: row._groupSpan || 1, colspan: 1 };
        } else if (rowIndex > 0 && this.filteredUserList[rowIndex - 1]) {
          const prevRow = this.filteredUserList[rowIndex - 1];
          
          // 如果当前行与前一行用户名相同，则不显示
          if (row.username && prevRow.username && row.username === prevRow.username) {
            return { rowspan: 0, colspan: 0 };
          } else {
            // 显示并合并
            return { rowspan: row._groupSpan || 1, colspan: 1 };
          }
        }
      }
    },
    // 获取行的唯一标识
    getRowKey(row) {
      return row.token || (row.username + '-' + row.host);
    },
    // 处理分组切换
    handleGroupingChange() {
      // 清除选择
      if (this.$refs.userTable) {
        this.$refs.userTable.clearSelection();
      }
    },
    isCurrentUser(token) {
      return token === this.currentUserToken
    },
    async handleOffline(row) {
      try {
        await this.$confirm('确认要强制下线该用户吗？', '提示', {
          type: 'warning'
        })
        await forceOffline(row.token)
        this.$message.success('强制下线成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('强制下线用户失败:', error)
          this.$message.error(error.message || '操作失败')
        }
      }
    },
    handleSearch() {
      // 前端搜索，不需要调用API
    },
    refreshList() {
      this.getList()
    },
    handleSelectionChange(selection) {
      this.selectedUsers = selection.map(item => item.token)
      this.selectedUserItems = selection
    },
    async handleBatchOffline() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请至少选择一个用户')
        return
      }
      
      // 检查是否包含当前用户
      const hasCurrentUser = this.selectedUserItems.some(user => this.isCurrentUser(user.token))
      if (hasCurrentUser) {
        this.$message.warning('不能强制下线当前用户')
        return
      }
      
      try {
        await this.$confirm(`确认要强制下线这 ${this.selectedUsers.length} 个用户吗？`, '提示', {
          type: 'warning'
        })
        
        const offlinePromises = this.selectedUserItems.map(user => {
          return forceOffline(user.token)
        })
        
        await Promise.all(offlinePromises)
        this.$message.success('批量强制下线成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量强制下线失败:', error)
          this.$message.error(error.message || '操作失败')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.living-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }
    
    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .unified-search {
        .search-input {
          width: 280px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;
              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;

              .el-button {
                margin: 0;
                height: 34px;
                border: none;
                padding: 0 16px;
                border-radius: 0 8px 8px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
          }
        }
      }
      
      .group-toggle {
        margin: 0 8px;
        
        ::v-deep {
          .el-switch__label {
            color: #606266;
            font-size: 13px;
            padding: 0 4px;
          }
          
          .el-switch__label.is-active {
            color: #409EFF;
            font-weight: 500;
          }
          
          .el-switch__core {
            width: 46px !important;
            height: 22px;
            border-radius: 11px;
            
            &:after {
              width: 18px;
              height: 18px;
              top: 1px;
            }
          }
          
          .el-switch.is-checked .el-switch__core::after {
            margin-left: -19px;
          }
        }
      }
      
      .el-button {
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        border-radius: 8px;
        background-color: #409EFF;
        border-color: #409EFF;
        overflow: hidden;
        z-index: 1;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        height: 36px;
        font-size: 14px;

        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
          z-index: -1;
        }

        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }

        i {
          margin-right: 6px;
          font-size: 14px;
        }
      }
    }
  }

  // 统计卡片样式
  .stats-card-container {
    margin-bottom: 24px;
    
    .stats-card {
      height: 120px;
      border-radius: 12px;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
      padding: 20px;
      display: flex;
      overflow: hidden;
      position: relative;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
      }
      
      .icon-container {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        i {
          font-size: 32px;
          color: #fff;
        }
      }
      
      .stats-info {
        flex: 1;
        
        .stats-title {
          font-size: 16px;
          color: #909399;
          margin-bottom: 8px;
        }
        
        .stats-value {
          font-size: 30px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        
        .stats-desc {
          font-size: 12px;
          color: #909399;
        }
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 50%;
        transform: translate(30%, 30%);
      }
      
      &.total-users {
        background: linear-gradient(135deg, #3E8FF7, #2C5CF6);
        color: #fff;
        
        .stats-info {
          .stats-title, .stats-desc {
            color: rgba(255, 255, 255, 0.8);
          }
          
          .stats-value {
            color: #fff;
          }
        }
        
        .icon-container {
          background: rgba(255, 255, 255, 0.15);
        }
      }
      
      &.active-users {
        background: linear-gradient(135deg, #11C3D0, #18A0EC);
        color: #fff;
        
        .stats-info {
          .stats-title, .stats-desc {
            color: rgba(255, 255, 255, 0.8);
          }
          
          .stats-value {
            color: #fff;
          }
        }
        
        .icon-container {
          background: rgba(255, 255, 255, 0.15);
        }
      }
      
      &.unique-ips {
        background: linear-gradient(135deg, #FFB822, #F9774B);
        color: #fff;
        
        .stats-info {
          .stats-title, .stats-desc {
            color: rgba(255, 255, 255, 0.8);
          }
          
          .stats-value {
            color: #fff;
          }
        }
        
        .icon-container {
          background: rgba(255, 255, 255, 0.15);
        }
      }
      
      &.admin-users {
        background: linear-gradient(135deg, #8C54FF, #573AD1);
        color: #fff;
        
        .stats-info {
          .stats-title, .stats-desc {
            color: rgba(255, 255, 255, 0.8);
          }
          
          .stats-value {
            color: #fff;
          }
        }
        
        .icon-container {
          background: rgba(255, 255, 255, 0.15);
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    .batch-actions-bar {
      background: linear-gradient(to right, #f5f7fa, #f9fafc);
      padding: 14px 20px;
      margin: 0;
      border-radius: 0;
      border-left: none;
      border-right: none;
      border-top: none;
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      animation: fadeIn 0.3s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid #eef1f7;
      margin-bottom: 12px;

      .selection-info {
        color: #606266;
        font-size: 14px;
        display: flex;
        align-items: center;

        .count {
          color: #409EFF;
          font-weight: 600;
          margin: 0 4px;
          display: inline-block;
          min-width: 24px;
          height: 24px;
          line-height: 24px;
          background: rgba(64, 158, 255, 0.1);
          border-radius: 12px;
          padding: 0 8px;
          text-align: center;
        }

        .el-button {
          margin-left: 12px;
          padding: 0;
          color: #909399;
          
          &:hover {
            color: #409EFF;
          }
        }
      }
      
      .batch-buttons {
        .operation-group {
          .el-button {
            padding: 6px 12px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            margin-left: 8px;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            }
          }
        }
      }
    }

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }

      .user-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .el-avatar {
          background-color: #f0f2f5;
          color: #909399;
        }
        
        span {
          color: #1a1f36;
          font-weight: 500;
        }
      }

      .role-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        
        .role-tag {
          margin: 0;
        }
        
        .no-roles {
          color: #909399;
          font-style: italic;
        }
      }

      .delete-btn {
        color: #F56C6C;
        
        &:hover {
          color: #f78989;
        }
        
        &[disabled] {
          color: #C0C4CC;
          cursor: not-allowed;
        }
      }

      .grouped-row {
        background-color: #f7f9fc;
      }
      
      // 添加分组样式
      .el-table__row.grouped-first-row td {
        border-top: 2px solid #e0e5ee;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
