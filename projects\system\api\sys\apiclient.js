import request from '@/utils/request'
import { method } from 'lodash';

const api = CONSTANT.SYSTEM + "/api/client";

export function list(params) {
  return request({
    url: `${api}/list`,
    params
  })
}
export function get(params) {
  return request({
    url: `${api}`,
    params
  })
}
export function save(data) {
  return request({
    url: `${api}`,
    method: 'post',
    data
  })
}
export function update(data) {
  return request({
    url: `${api}`,
    method: 'put',
    data
  })
}
export function remove(ids) {
  return request({
    url: `${api}/${ids}`,
    method: 'delete',
  })
}

export function secretReset(id) {
  return request({
    url: `${api}/secret/reset/${id}`,
    method: 'put'
  })
}