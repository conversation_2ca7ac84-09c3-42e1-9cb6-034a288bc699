<template>
    <div class="flow-panel">

        <NodePanel v-if="lf" :lf="lf"></NodePanel>
        <!-- <EdgeStylePanel v-if="lf" :lf="lf"></EdgeStylePanel> -->
        <div ref="container" class="flow-container"></div>
        <NodeDrawer v-if="lf" :visible.sync="nodeDrawerVisible" :node="clickNode" :lf="lf"></NodeDrawer>
    </div>
</template>
<script>
import LogicFlow from "@logicflow/core"
import { Dagre } from "@logicflow/layout"
import { Control, MiniMap, SelectionSelect, Menu } from "@logicflow/extension"
import { register } from '@logicflow/vue-node-registry'
import { BaseVueNodeView, BaseVueNodeModel } from './components/core/base.js'
import line from './components/core/line.js'
import polyline from './components/core/polyline.js'
import bezier from './components/core/bezier.js'
import multiBezier from './components/core/multiBezier.js'

import "@logicflow/core/lib/style/index.css"
import "@logicflow/extension/lib/style/index.css"

import NodeDrawer from './components/panel/NodeDrawer.vue'
import NodePanel from './components/panel/NodePanel.vue'
import EdgeStylePanel from './components/panel/EdgeStylePanel.vue'
import config from './components/panel/config.js'
import basicsNode from "./components/node/BasicsNode.vue"

import nodeEvent from './components/event/node'
// import { interactionExample } from '@extreme-flow/examples/multiBezierExample.js'

export default {
    components: {
        NodePanel,
        NodeDrawer,
        EdgeStylePanel
    },
    data() {
        return {
            lf: null,
            nodeDrawerVisible: false,
            clickNode: null,
        };
    },
    mounted() {
        this.initFlow();
    },
    methods: {
        initFlow() {
            this.lf = new LogicFlow({
                container: this.$refs.container,
                plugins: [Dagre, Control, MiniMap, SelectionSelect, Menu],
                grid: {
                    size: 20, // 点的密集程度
                    visible: true,
                    type: 'dot', // 'dot' | 'mesh'
                    config: {
                        color: '#e2e4ed', // 点的颜色
                        thickness: 1, // 点的大小
                    },
                },
                nodeTextEdit: false,
                edgeSelectedOutline: false,
                nodeSelectedOutline: false,
                hoverOutline: false,
                pluginsOptions: {
                    miniMap: {
                        width: 200,
                        height: 100,
                        showEdge: true,
                        rightPosition: 5,
                        bottomPosition: 5,
                    },
                },
            })

            this.setTheme()
            this.setExtensionConfig()

            this.lf.batchRegister([line, polyline, bezier, multiBezier])

            config().then(list => {
                list.forEach(item => {
                    if (item.nodes && item.nodes.length) {
                        item.nodes.forEach(node => {
                            register({
                                type: node.type,
                                component: node.component || basicsNode,
                                view: BaseVueNodeView,
                                model: BaseVueNodeModel,
                            }, this.lf)
                        })
                    }
                })
                this.$emit('init')
            })

            this.lf.render({
                nodes: [],
                edges: [],
            })

            nodeEvent.call(this)

            this.lf.extension.miniMap.show()

        },

        render(data) {
            this.lf && this.lf.render(data)
        },
        renderRawData(data) {
            this.lf && this.lf.renderRawData(data)
        },
        resize() {
            this.lf && this.lf.resize()
        },
        getFlowJson() {
            let rData = this.lf.getGraphRawData()
            return JSON.stringify(rData)
        },

        setTheme() {
            this.lf.setTheme({
                baseEdge: {
                    stroke: "#c2c5cf",
                },
            });
            this.lf.setTheme({
                arrow: {
                    offset: 6,
                    verticalLength: 3,
                },
            });
        },

        setExtensionConfig() {
            this.lf.setDefaultEdgeType('bezier')

            this.lf.extension.menu.setMenuConfig({
                nodeMenu: [
                    {
                        text: '删除',
                        icon: true,
                        className: 'menu-delete',
                        callback: (node) => {
                            this.lf.deleteNode(node.id)
                        },
                    },
                    {
                        text: '复制',
                        icon: true,
                        className: 'menu-copy',
                        callback: (node) => {
                            this.lf.cloneNode(node.id)
                        },
                    },
                ],
                edgeMenu: [
                    {
                        text: '删除',
                        icon: true,
                        className: 'menu-delete',
                        callback: (edge) => {
                            this.lf.deleteEdge(edge.id)
                        },
                    },
                    {
                        text: '编辑文本',
                        icon: true,
                        className: 'menu-edit-text',
                        callback: (edge) => {
                            this.lf.graphModel.editText(edge.id)
                        },
                    },
                    {
                        text: '直线',
                        icon: true,
                        className: 'menu-line',
                        callback: (edge) => {
                            this.lf.changeEdgeType(edge.id, 'line')
                        }
                    },
                    {
                        text: '折线',
                        icon: true,
                        className: 'menu-polyline',
                        callback: (edge) => {
                            this.lf.changeEdgeType(edge.id, 'polyline')
                        }
                    },
                    {
                        text: '曲线',
                        icon: true,
                        className: 'menu-bezier',
                        callback: (edge) => {
                            this.lf.changeEdgeType(edge.id, 'bezier')
                        }
                    },
                    // {
                    //     text: '多次曲线',
                    //     icon: true,
                    //     className: 'menu-multi-bezier',
                    //     callback: (edge) => {
                    //         this.lf.changeEdgeType(edge.id, 'multiBezier')
                    //     }
                    // },
                ],

            })

            // interactionExample(this.lf)


            this.lf.extension.control.addItem({
                key: 'fitView',
                iconClass: 'lf-control-fit',
                title: '自适应流程尺寸',
                text: '自适应',
                onClick: () => {
                    this.lf.fitView(400, 100);
                },
            })

            this.lf.extension.control.addItem({
                key: 'beautify',
                iconClass: 'lf-control-beautify',
                title: '美化流程布局',
                text: '美化',
                onClick: () => {
                    this.lf.extension.dagre && this.lf.extension.dagre.layout({
                        nodesep: 2,
                        ranksep: 20,
                        begin: [400, 100],
                    })
                    this.lf.extension.miniMap.hide()
                    setTimeout(() => {
                        this.lf.extension.miniMap.show()
                    }, 0)
                },
            })
            this.lf.extension.control.addItem({
                key: 'selection',
                iconClass: 'lf-control-selection',
                title: '框选布局',
                text: '单次框选',
                onClick: () => {
                    this.lf.extension.selectionSelect.openSelectionSelect()
                    this.lf.once('selection:selected', () => {
                        this.lf.extension.selectionSelect.closeSelectionSelect()
                    });
                },
            })

            this.lf.extension.control.addItem({
                key: 'export',
                iconClass: 'lf-control-export',
                title: '导出流程数据',
                text: '导出',
                onClick: () => {
                    const data = this.lf.getGraphRawData()
                    const blob = new Blob([JSON.stringify(data)], { type: 'application/json' })
                    const url = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = 'flow-data.json'
                    document.body.appendChild(a)
                    a.click()
                    document.body.removeChild(a)
                    URL.revokeObjectURL(url)
                },
            })

            this.lf.extension.control.addItem({
                key: 'testExecuteStatus',
                iconClass: 'lf-control-test',
                title: '测试执行状态',
                text: '测试状态',
                onClick: () => {
                    this.testExecuteStatus()
                },
            })
        },

        testExecuteStatus() {
            const nodes = this.lf.getGraphData().nodes
            if (nodes.length === 0) {
                alert('请先添加一些节点到画布中')
                return
            }

            // 随机设置节点的执行状态
            nodes.forEach((node, index) => {
                const statuses = ['success', 'error']
                const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]

                this.lf.setProperties(node.id, {
                    executeStatus: randomStatus
                })
            })
        }
    }
};
</script>
<style lang="scss" scoped>
.flow-panel,
.flow-container {
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
<style lang="scss">
[class^="lf"] {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.flow-container {
    .custom-anchor {
        stroke: #2961ef;
        stroke-width: 1;
        fill: #fff;
        cursor: crosshair;
        rx: 50%;
        ry: 50%;
    }

    .custom-anchor:hover {
        stroke: #2961ef;
        stroke-width: 1;
        fill: #fff;
    }

    .lf-node-not-allow .custom-anchor:hover {
        stroke: #999;
        fill: #d9d9d9;
        cursor: not-allowed;
    }

    .incomming-anchor {
        stroke: #2961ef;
        cursor: default;
    }

    .outgoing-anchor {
        stroke: #2961ef;
    }
}


.lf-dnd-item {
    margin: 0;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: start;
}

.lf-dnd-shape {
    background-size: 30px;
    margin: 0 10px 0 0;
}

.lf-control {
    right: 0px;
    box-shadow: none;
    filter: drop-shadow(2px 2px 6px rgba(0, 0, 0, 0.1));
}

.lf-menu {
    width: 120px;
    border-radius: 5px;
    box-shadow: none;
    filter: drop-shadow(2px 2px 6px rgba(0, 0, 0, 0.1));

    .lf-menu-item-text {
        padding-left: 24px;
    }

    .lf-menu-item-icon+.lf-menu-item-text {
        position: absolute;
        left: 40px;
        padding-left: 0px;
    }
}

.lf-node:hover {
    filter: drop-shadow(0px 10px 10px rgba(0, 0, 0, 0.2));
}

.lf-node {
    filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.2));
}

.lf-graph {
    background: #f2f4f7;
}

// .lf-edge-append {
//     pointer-events: none;
// }

.lf-mini-map {
    background: #ebedf1;
    border: none;
}

.lf-minimap-viewport {
    background: rgba(48, 48, 48, 0.1);
}

/* 自定义控制按钮图标样式 */
.lf-control-beautify::before {
    content: "✨";
    font-size: 16px;
    position: absolute;
}

.lf-control-selection::before {
    content: "⬚";
    font-size: 16px;
    position: absolute;
}

.lf-control-export::before {
    content: "📤";
    font-size: 16px;
    position: absolute;
}

.lf-control-test::before {
    content: "🧪";
    font-size: 16px;
    position: absolute;
}

/* 菜单图标样式 */
.menu-delete .lf-menu-item-icon::before {
    content: "✕";
    font-size: 14px;
    font-weight: bold;
    color: #f56c6c;
}

.menu-copy .lf-menu-item-icon::before {
    content: "⧉";
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
}

.menu-edit-text .lf-menu-item-icon::before {
    content: "✎";
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
}

.menu-line .lf-menu-item-icon::before {
    content: "─";
    font-size: 14px;
    font-weight: bold;
}

.menu-polyline .lf-menu-item-icon::before {
    content: "└┐";
    font-size: 8px;
    font-weight: bold;
}

.menu-bezier .lf-menu-item-icon::before {
    content: "〰️";
    font-size: 12px;
    font-weight: bold;
}

.menu-multi-bezier .lf-menu-item-icon::before {
    content: "〰〰";
    font-size: 10px;
    font-weight: bold;
    color: #67c23a;
}
</style>