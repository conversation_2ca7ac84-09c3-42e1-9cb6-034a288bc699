// import {rem2px} from '@/utils'

export const baseSeriesOption = () => ({
  label: { show: true, color: '#fff', distance: 12, fontSize: '12px' },
  emphasis: { focus: 'series' },
})

export const baseOption = () => ({
  grid: {
    top: '0%',
    left: '0%',
    right: '0%',
    bottom: '0%',
    containLabel: true,
  },
  // tooltip: {
  //   trigger: 'axis',
  //   axisPointer: { // 坐标轴指示器，坐标轴触发有效
  //     type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
  //   }
  // },
  // toolbox: {
  //   feature: {
  //     saveAsImage: {},
  //   },
  // },
  title: {
    left: 'center',
    // text: 'Title',
    textStyle: {
      color: 'green',
    },
  },
  // legend: {
  //   // itemWidth: rem2px(1.6, false),
  //   // itemHeight: rem2px(1.4, false),
  //   // itemGap: rem2px(3.2, false),
  //   textStyle: {
  //     color: 'black',
  //     fontSize: '12px',
  //   },
  // },
  // axis: {
  //   axisLabel: {
  //     fontSize: '10px',
  //     color: '#ccc',
  //   },
  //   axisTick: { // y轴刻度线
  //     show: false
  //   },
  //   axisLine: { //y轴
  //     show: false
  //   },
  //   splitLine: {
  //     lineStyle: {
  //       color: '#333',
  //     }
  //   },
  // },
  xAxis: {
    // nameGap: rem2px(2),
    nameLocation: 'end',
    nameTextStyle: {
      fontSize: '12px',
    },
    type: 'value',
    // axisLabel: {
    //   fontSize: '12px',
    //   fontWeight: 'bold',
    //   color: '#ccc',
    //   interval: 0,
    //   // margin: rem2px(2, false),
    // },
    // axisTick: { // 轴刻度线
    //   show: false
    // },
    // axisLine: { // 轴
    //   show: true,
    //   lineStyle: {
    //     color: '#C3EEFF',
    //     shadowColor: '#0091FF',
    //   },
    // },
    // splitLine: {
    //   lineStyle: {
    //     color: '#031B3F',
    //   }
    // },
    // splitArea: {
    //   show: false,
    // }
  },
  yAxis: {
    type: 'value',
    // axisLabel: {
    //   fontSize: '12px',
    //   fontWeight: 'bold',
    //   color: '#fff',
    //   interval: 0,
    //   // margin: rem2px(5, false),
    // },
    // axisTick: { // y轴刻度线
    //   show: false
    // },
    // axisLine: { // y轴
    //   show: false,
    //   lineStyle: {
    //     color: '#0658A899',
    //   },
    // },
    // // splitNumber: 10,
    // splitLine: {
    //   lineStyle: {
    //     color: '#031B3F88',
    //   },
    // },
  },
  textStyle: {
    fontSize: '13px',
    color: 'rgba(31, 38, 62, 1)',
  },
  itemStyle: {
    label: {
      show: true,
      position: 'top',
      textStyle: {
        fontSize: '12px',
        color: 'black'
      }
    }
  },
})
