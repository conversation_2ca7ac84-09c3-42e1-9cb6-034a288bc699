<template>
  <div class="role-container">
    <div class="page-header">
      <div class="header-title">
        <h2>角色管理</h2>
      </div>
      
      <div class="header-tools">
        <div class="unified-search">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入角色名称/编码搜索"
            prefix-icon="el-icon-search"
            clearable
            class="search-input"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>
        </div>

        <div class="button-group">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
            新增角色
          </el-button>
          <el-button type="primary" icon="el-icon-refresh" @click="refreshList">
            刷新列表
          </el-button>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <div v-if="selectedRoles.length > 0" class="batch-actions-bar">
        <div class="selection-info">
          已选择 <span class="count">{{ selectedRoles.length }}</span> 项
          <el-button type="text" @click="$refs.roleTable.clearSelection()">清空选择</el-button>
        </div>
        <div class="batch-buttons">
          <el-button-group class="operation-group">
            <el-button 
              type="primary" 
              icon="el-icon-check" 
              size="small"
              @click="handleBatchEnable"
            >启用</el-button>
            <el-button 
              type="warning" 
              icon="el-icon-close" 
              size="small"
              @click="handleBatchDisable"
            >禁用</el-button>
            <el-button 
              type="danger" 
              icon="el-icon-delete" 
              size="small"
              @click="handleBatchDelete"
            >删除</el-button>
          </el-button-group>
        </div>
      </div>

      <el-table 
        ref="roleTable"
        v-loading="loading" 
        :data="roleList" 
        border 
        stripe
        fit
        style="width: 100%"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" :width=rpx(50) align="center" />
        <el-table-column prop="name" label="角色名称" min-width="120">
          <template slot-scope="scope">
            <div class="role-cell">
              <i class="el-icon-user" />
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="code" label="角色编码" :width="rpx(120)">
          <template slot-scope="scope">
            <el-tag size="medium" type="info" effect="plain" class="code-tag">
              {{ scope.row.code }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="valid" label="状态" min-width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.valid ? 'success' : 'danger'" effect="dark">
              {{ scope.row.valid ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="权限引用" min-width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.inheritedRoleIds && scope.row.inheritedRoleIds.length > 0" class="role-relation-tags">
              <el-tag 
                type="primary" 
                effect="plain" 
                class="clickable-tag"
                @click="handleViewDetails(scope.row)"
              >
                <i class="el-icon-connection"></i>
                引用 {{ scope.row.inheritedRoleIds.length }} 个角色
              </el-tag>
            </div>
            <el-tag v-else type="info" effect="plain">未引用其他角色</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="备注" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.remark && scope.row.remark.trim()">{{ scope.row.remark }}</span>
            <span v-else class="no-remark">暂无备注</span>
          </template>
        </el-table-column>

        <el-table-column prop="updateTime" label="更新时间" min-width="160" />

        <el-table-column label="操作" min-width="220" align="center">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              type="text" 
              icon="el-icon-view"
              @click="handleViewDetails(scope.row)"
            >
              详情
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              class="delete-btn"
              @click="handleDelete(scope.row)"
              :disabled="scope.row.code === 'admin'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          :current-page="queryParams.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.size"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="roleForm"
        :model="roleForm"
        :rules="rules"
        label-width="80px"
        class="role-form"
      >
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="角色名称" prop="name">
                <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色编码" prop="code">
                <el-input v-model="roleForm.code" placeholder="请输入角色编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-switch
                  v-model="roleForm.valid"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input
                  v-model="roleForm.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <div class="form-section">
          <div class="section-title">权限引用</div>
          <el-row>
            <el-col :span="24">
              <el-form-item label="引用角色">
                <div class="role-relation-selector">
                  <div class="role-search-box">
                    <el-input
                      v-model="roleSearchKeyword"
                      prefix-icon="el-icon-search"
                      placeholder="搜索角色名称/编码"
                      clearable
                      @input="filterRoleOptions"
                    />
                  </div>
                  <div class="role-relation-header">
                    <div class="relation-title">选择要引用权限的角色</div>
                    <el-checkbox 
                      :indeterminate="isIndeterminate"
                      v-model="checkAll"
                      @change="handleCheckAllChange"
                    >全选</el-checkbox>
                  </div>
                  <div class="role-tree-container">
                    <div class="role-list">
                      <el-checkbox-group v-model="roleForm.inheritedRoleIds" @change="handleRolesChange">
                        <div 
                          v-for="role in filteredRoleOptions" 
                          :key="role.id"
                          class="role-item"
                          :class="{'disabled': role.id === roleForm.id}"
                        >
                          <el-checkbox 
                            :label="role.id" 
                            :disabled="role.id === roleForm.id"
                            class="role-checkbox"
                          >
                            <div class="role-info">
                              <div class="role-name">
                                <i class="el-icon-user"></i>
                                <span>{{ role.name }}</span>
                              </div>
                              <span class="role-code">({{ role.code }})</span>
                            </div>
                          </el-checkbox>
                        </div>
                      </el-checkbox-group>
                    </div>
                  </div>
                  <div class="form-tips">
                    <i class="el-icon-info"></i>
                    <span>该角色将获得所选角色的全部权限和资源</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 角色详情对话框 -->
    <el-dialog 
      title="角色详情" 
      :visible.sync="detailDialogVisible"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <div v-loading="detailLoading">
        <div class="detail-card">
          <div class="detail-header">
            <h3>基本信息</h3>
          </div>
          <div class="detail-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="角色名称">{{ currentRole.name }}</el-descriptions-item>
              <el-descriptions-item label="角色编码">{{ currentRole.code }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="currentRole.valid ? 'success' : 'danger'" effect="dark">
                  {{ currentRole.valid ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ currentRole.updateTime }}</el-descriptions-item>
              <el-descriptions-item :span="2" label="备注">
                <span v-if="currentRole.remark && currentRole.remark.trim()">{{ currentRole.remark }}</span>
                <span v-else class="no-remark">暂无备注</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
        
        <div class="detail-card">
          <div class="detail-header">
            <h3>权限引用关系</h3>
            <div class="header-actions">
              <el-switch
                v-model="showAllInheritance"
                active-text="显示所有引用角色"
                inactive-text="仅直接引用角色"
                @change="toggleInheritanceView"
              ></el-switch>
            </div>
          </div>
          <div class="detail-content">
            <div v-if="(showAllInheritance ? allInheritedRoles : inheritedRoles).length > 0" class="role-relation-chart">
              <div v-if="showAllInheritance" class="inheritance-notice">
                <el-alert
                  title="当前显示所有层级的权限引用关系，包括直接和间接引用的角色"
                  type="info"
                  :closable="false"
                  show-icon
                ></el-alert>
              </div>
              <div class="role-relation-list">
                <div class="role-relation-item" 
                  v-for="role in (showAllInheritance ? allInheritedRoles : inheritedRoles)" 
                  :key="role.id"
                  :class="{'indirect-inheritance': role.isIndirect}"
                >
                  <div class="role-relation-card">
                    <div class="role-info">
                      <i class="el-icon-user"></i>
                      <span class="role-name">{{ role.name }}</span>
                      <span class="role-code">({{ role.code }})</span>
                      <el-tag v-if="role.isIndirect" size="mini" type="info" class="indirect-tag">间接引用</el-tag>
                    </div>
                    <div class="role-relation-arrow">
                      <i class="el-icon-arrow-down"></i>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="current-role-node">
                <div class="role-card">
                  <div class="role-avatar">
                    <i class="el-icon-user"></i>
                  </div>
                  <div class="role-detail">
                    <h4>{{ currentRole.name }}</h4>
                    <span>{{ currentRole.code }}</span>
                  </div>
                </div>
              </div>
            </div>
            <el-empty 
              v-else 
              :description="showAllInheritance ? '没有直接或间接引用的角色' : '未设置权限引用'" 
              :image-size="60"
            ></el-empty>
          </div>
        </div>
        
        <div class="detail-card">
          <div class="detail-header">
            <div class="header-with-tabs">
              <h3>权限与资源</h3>
            </div>
          </div>
          <div class="detail-content">
            <div class="permission-jump">
              <div class="permission-summary">
                <div class="summary-item">
                  <div class="summary-label">菜单资源：</div>
                  <div class="summary-value">
                    <el-tag type="primary" effect="plain">
                      {{ currentRole.resourceIds ? currentRole.resourceIds.length : 0 }} 项资源
                    </el-tag>
                  </div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">接口权限：</div>
                  <div class="summary-value">
                    <el-tag type="success" effect="plain">
                      {{ currentRole.permissions ? currentRole.permissions.length : 0 }} 项权限
                    </el-tag>
                  </div>
                </div>
              </div>
              <el-button 
                type="primary" 
                class="permission-link-btn"
                @click="goToPermissionPage"
              >
                <i class="el-icon-view"></i>
                查看完整权限配置
              </el-button>
              <div class="permission-tip">
                <i class="el-icon-info"></i>
                点击按钮进入权限管理页面，可查看和配置该角色的详细权限
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRoleList, addRole, updateRole, deleteRole, setRoleInheritance, getRolePermissions, getRoleResources } from '@system/api/sys/role'

export default {
  name: 'RoleManagement',
  components: {
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      detailLoading: false,
      roleList: [],
      total: 0,
      queryParams: {
        keyword: '',
        current: 1,
        size: 10
      },
      dialogVisible: false,
      dialogTitle: '',
      roleForm: {
        id: null,
        name: '',
        code: '',
        valid: true,
        remark: '',
        inheritedRoleIds: []
      },
      rules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入角色编码', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ]
      },
      selectedRoles: [],
      selectedRoleItems: [],
      inheritableRoles: [], // 可继承的角色列表
      
      // 详情相关
      detailDialogVisible: false,
      currentRole: {},
      inheritedRoles: [],
      activeResourceTab: 'resources',
      roleSearchKeyword: '',
      isIndeterminate: false,
      checkAll: false,
      filteredRoleOptions: [],
      showAllInheritance: false,
      allInheritedRoles: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const params = {}
        Object.keys(this.queryParams).forEach(key => {
          if (this.queryParams[key] !== '' &&
            this.queryParams[key] !== null &&
            this.queryParams[key] !== undefined) {
            params[key] = this.queryParams[key]
          }
        })

        const data = await getRoleList(params)
        this.roleList = data?.records || []
        
        // 处理角色列表数据，确保每个角色对象都有inheritedRoleIds数组
        this.roleList = this.roleList.map(role => {
          return {
            ...role,
            inheritedRoleIds: role.inheritedRoleIds || []
          }
        })
        
        this.total = data.total || 0
      } catch (error) {
        console.error('获取角色列表失败:', error)
        this.$message.error('获取角色列表失败')
      }
      this.loading = false
    },
    handleAdd() {
      this.dialogTitle = '新增角色'
      this.roleForm = {
        id: null,
        name: '',
        code: '',
        valid: true,
        remark: '',
        inheritedRoleIds: []
      }
      this.dialogVisible = true
      this.getInheritableRoles()
      // 重置搜索关键字和全选状态
      this.roleSearchKeyword = ''
      this.checkAll = false
      this.isIndeterminate = false
    },
    handleEdit(row) {
      this.dialogTitle = '编辑角色'
      this.roleForm = {
        id: row.id,
        name: row.name,
        code: row.code,
        valid: row.valid,
        remark: row.remark,
        inheritedRoleIds: row.inheritedRoleIds || []
      }
      this.dialogVisible = true
      this.getInheritableRoles()
      // 重置搜索关键字
      this.roleSearchKeyword = ''
    },
    async handleDelete(row) {
        await this.$confirm('确认要删除该角色吗？', '提示', {
          type: 'warning'
        })
        await deleteRole(row.id)
        this.$message.success('删除成功')
        this.getList()
    },
    async submitForm() {
      try {
        await this.$refs.roleForm.validate()
        
        this.submitLoading = true
        
        if (this.roleForm.id === null) {
          // 新增角色
          const result = await addRole(this.roleForm)
          this.$message.success('新增角色成功')
        } else {
          // 更新角色
          await updateRole(this.roleForm)
          this.$message.success('更新角色信息成功')
        }
        
        this.dialogVisible = false
        this.getList()
      } catch (error) {
        if (error !== false) {
          console.error('提交角色表单失败:', error)
          this.$message.error(error.message || '操作失败')
        }
      }
      this.submitLoading = false
    },
    handleSizeChange(val) {
      this.queryParams.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.current = val
      this.getList()
    },
    refreshList() {
      this.getList()
    },
    handleSearch() {
      this.queryParams.current = 1
      this.getList()
    },
    handleSelectionChange(selection) {
      this.selectedRoles = selection.map(item => item.id)
      this.selectedRoleItems = selection
    },
    async handleBatchEnable() {
      if (this.selectedRoles.length === 0) {
        this.$message.warning('请至少选择一个角色')
        return
      }
      
      try {
        await this.$confirm('确认要启用所选角色吗？', '提示', {
          type: 'warning'
        })
        
        // 构建批量更新的请求体
        const updatePromises = this.selectedRoleItems.map(role => {
          return updateRole({
            ...role,
            valid: true
          })
        })
        
        await Promise.all(updatePromises)
        this.$message.success('批量启用成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量启用失败:', error)
          this.$message.error(error.message || '操作失败')
        }
      }
    },
    async handleBatchDisable() {
      if (this.selectedRoles.length === 0) {
        this.$message.warning('请至少选择一个角色')
        return
      }
      
      // 检查是否包含管理员角色
      const hasAdmin = this.selectedRoleItems.some(role => role.code === 'admin')
      if (hasAdmin) {
        this.$message.warning('管理员角色不能被禁用')
        return
      }
      
      try {
        await this.$confirm('确认要禁用所选角色吗？', '提示', {
          type: 'warning'
        })
        
        // 构建批量更新的请求体
        const updatePromises = this.selectedRoleItems.map(role => {
          return updateRole({
            ...role,
            valid: false
          })
        })
        
        await Promise.all(updatePromises)
        this.$message.success('批量禁用成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量禁用失败:', error)
          this.$message.error(error.message || '操作失败')
        }
      }
    },
    async handleBatchDelete() {
      if (this.selectedRoles.length === 0) {
        this.$message.warning('请至少选择一个角色')
        return
      }
      
      // 检查是否包含管理员角色
      const hasAdmin = this.selectedRoleItems.some(role => role.code === 'admin')
      if (hasAdmin) {
        this.$message.warning('管理员角色不能被删除')
        return
      }
      
      try {
        await this.$confirm(`确认要删除这 ${this.selectedRoles.length} 个角色吗？`, '提示', {
          type: 'warning'
        })
        
        await deleteRole(this.selectedRoles.join(','))
        this.$message.success('批量删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error(error.message || '操作失败')
        }
      }
    },
    filterRoleOptions() {
      // 过滤角色选项
      if (!this.roleSearchKeyword) {
        this.filteredRoleOptions = [...this.inheritableRoles];
        return;
      }
      
      const keyword = this.roleSearchKeyword.toLowerCase();
      this.filteredRoleOptions = this.inheritableRoles.filter(role => 
        role.name.toLowerCase().includes(keyword) || 
        role.code.toLowerCase().includes(keyword)
      );
    },
    
    handleCheckAllChange(val) {
      // 全选/取消全选
      // 过滤掉当前角色，不能自我继承
      const selectableRoles = this.inheritableRoles.filter(role => 
        role.id !== this.roleForm.id
      );
      
      this.roleForm.inheritedRoleIds = val ? selectableRoles.map(role => role.id) : [];
      this.isIndeterminate = false;
    },
    
    handleRolesChange(value) {
      // 更新全选和半选状态
      // 过滤掉当前角色，不能自我继承
      const selectableRoles = this.inheritableRoles.filter(role => 
        role.id !== this.roleForm.id
      );
      
      const checkedCount = value.length;
      this.checkAll = checkedCount === selectableRoles.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < selectableRoles.length;
    },

    // 获取可继承的角色列表
    async getInheritableRoles() {
      try {
        // 获取所有有效角色作为可继承角色
        const params = { valid: true, size: 100 }
        const data = await getRoleList(params)
        this.inheritableRoles = (data?.records || []).filter(role => {
          // 过滤掉当前角色，防止自我继承
          return this.roleForm.id === null || role.id !== this.roleForm.id
        })
        
        // 初始化过滤后的角色列表
        this.filteredRoleOptions = [...this.inheritableRoles]
        
        // 初始化全选/半选状态
        this.updateCheckAllStatus()
      } catch (error) {
        console.error('获取可继承角色失败:', error)
        this.$message.error('获取可继承角色列表失败')
      }
    },
    
    updateCheckAllStatus() {
      // 过滤掉当前角色，不能自我继承
      const selectableRoles = this.inheritableRoles.filter(role => 
        role.id !== this.roleForm.id
      );
      
      const checkedCount = this.roleForm.inheritedRoleIds.length;
      this.checkAll = selectableRoles.length > 0 && checkedCount === selectableRoles.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < selectableRoles.length;
    },
    // 查看角色详情
    async handleViewDetails(role) {
      this.detailDialogVisible = true
      this.currentRole = { ...role }
      this.detailLoading = true
      this.showAllInheritance = false // 默认显示直接继承
      
      try {
        // 获取继承角色信息
        if (role.inheritedRoleIds && role.inheritedRoleIds.length > 0) {
          // 从角色列表中匹配继承的角色
          this.inheritedRoles = this.roleList
            .filter(item => role.inheritedRoleIds.includes(item.id))
            .sort((a, b) => a.name.localeCompare(b.name)) // 按名称排序，使显示更有序
            
          // 如果有些角色ID在当前列表中找不到，可能需要重新加载完整列表
          if (this.inheritedRoles.length < role.inheritedRoleIds.length) {
            try {
              // 获取所有角色
              const params = { size: 500 } // 设置较大的size确保获取所有角色
              const data = await getRoleList(params)
              const allRoles = data?.records || []
              
              // 更新角色列表，避免重复角色
              const existingIds = this.roleList.map(r => r.id)
              const newRoles = allRoles.filter(r => !existingIds.includes(r.id))
              if (newRoles.length > 0) {
                this.roleList = [...this.roleList, ...newRoles]
              }
              
              // 重新匹配继承的角色
              this.inheritedRoles = this.roleList
                .filter(item => role.inheritedRoleIds.includes(item.id))
                .sort((a, b) => a.name.localeCompare(b.name))
            } catch (error) {
              console.error('获取完整角色列表失败:', error)
            }
          }
        } else {
          this.inheritedRoles = []
        }
        
        // 初始化所有继承角色列表
        this.allInheritedRoles = [...this.inheritedRoles]
      } catch (error) {
        console.error('获取角色详情数据失败:', error)
        this.$message.error('获取角色详情数据失败')
      }
      
      this.detailLoading = false
    },
    
    // 切换权限/资源标签页
    handleResourceTabChange() {
    },
    goToPermissionPage() {
      // 关闭当前对话框
      this.detailDialogVisible = false
      
      // 存储当前选中的角色ID到本地存储，以便在权限页面进行预选
      localStorage.setItem('preSelectedRoleId', this.currentRole.id)
      
      // 跳转到权限管理页面
      this.$router.push({
        path: '/sys/permission',
        query: {
          roleId: this.currentRole.id
        }
      })
    },
    // 根据ID获取角色名称
    getRoleName(roleId) {
      const role = this.roleList.find(r => r.id === roleId);
      return role ? role.name : '未知角色';
    },
    
    // 根据ID获取角色编码
    getRoleCode(roleId) {
      const role = this.roleList.find(r => r.id === roleId);
      return role ? role.code : '';
    },
    toggleInheritanceView() {
      if (this.showAllInheritance) {
        // 切换到显示所有继承关系
        this.loadAllInheritedRoles()
      }
    },
    async loadAllInheritedRoles() {
      if (!this.currentRole || !this.currentRole.id) return
      
      this.detailLoading = true
      try {
        // 检查是否有allInheritedRoleIds字段
        if (this.currentRole.allInheritedRoleIds && Array.isArray(this.currentRole.allInheritedRoleIds)) {
          // 直接使用后端返回的所有继承角色ID
          const directRoleIds = this.currentRole.inheritedRoleIds || []
          
          // 从角色列表中找到对应的角色详情
          const allRoles = this.roleList.filter(role => this.currentRole.allInheritedRoleIds.includes(role.id))
          
          // 标记直接和间接继承
          this.allInheritedRoles = allRoles.map(role => ({
            ...role,
            isIndirect: !directRoleIds.includes(role.id)
          })).sort((a, b) => {
            // 先按直接/间接排序，再按名称排序
            if (a.isIndirect && !b.isIndirect) return 1
            if (!a.isIndirect && b.isIndirect) return -1
            return a.name.localeCompare(b.name)
          })
          
          // 如果有些角色ID在当前列表中找不到，可能需要重新加载完整列表
          if (this.allInheritedRoles.length < this.currentRole.allInheritedRoleIds.length) {
            try {
              // 获取所有角色
              const params = { size: 500 } // 设置较大的size确保获取所有角色
              const data = await getRoleList(params)
              const allRoleList = data?.records || []
              
              // 更新角色列表，避免重复角色
              const existingIds = this.roleList.map(r => r.id)
              const newRoles = allRoleList.filter(r => !existingIds.includes(r.id))
              if (newRoles.length > 0) {
                this.roleList = [...this.roleList, ...newRoles]
              }
              
              // 重新匹配所有继承的角色
              const updatedAllRoles = this.roleList.filter(role => 
                this.currentRole.allInheritedRoleIds.includes(role.id)
              )
              
              // 标记直接和间接继承
              this.allInheritedRoles = updatedAllRoles.map(role => ({
                ...role,
                isIndirect: !directRoleIds.includes(role.id)
              })).sort((a, b) => {
                // 先按直接/间接排序，再按名称排序
                if (a.isIndirect && !b.isIndirect) return 1
                if (!a.isIndirect && b.isIndirect) return -1
                return a.name.localeCompare(b.name)
              })
            } catch (error) {
              console.error('获取完整角色列表失败:', error)
            }
          }
        } else {
          // 如果后端没有提供allInheritedRoleIds字段，则调用接口获取
          const result = await getRolePermissions(this.currentRole.id, true) // 假设第二个参数表示包含间接继承
          
          if (result && Array.isArray(result)) {
            // 处理返回的数据，标记直接和间接继承
            const directRoleIds = this.currentRole.inheritedRoleIds || []
            
            // 从角色列表中找到对应的角色详情
            const allRoles = this.roleList.filter(role => result.includes(role.id))
            
            // 标记直接和间接继承
            this.allInheritedRoles = allRoles.map(role => ({
              ...role,
              isIndirect: !directRoleIds.includes(role.id)
            })).sort((a, b) => {
              // 先按直接/间接排序，再按名称排序
              if (a.isIndirect && !b.isIndirect) return 1
              if (!a.isIndirect && b.isIndirect) return -1
              return a.name.localeCompare(b.name)
            })
          } else {
            // 如果接口不支持或返回错误，回退到只显示直接继承
            this.allInheritedRoles = [...this.inheritedRoles].map(role => ({
              ...role,
              isIndirect: false
            }))
            this.$message.warning('获取间接引用角色失败，仅显示直接引用角色')
          }
        }
      } catch (error) {
        console.error('获取所有引用角色失败:', error)
        this.$message.error('获取所有引用角色失败')
        // 回退到只显示直接继承
        this.allInheritedRoles = [...this.inheritedRoles].map(role => ({
          ...role,
          isIndirect: false
        }))
      } finally {
        this.detailLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.role-container {
  margin: 24px;
  padding: 24px;
  background: inherit;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }
    
    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .unified-search {
        .search-input {
          width: 280px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;
              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;

              .el-button {
                margin: 0;
                height: 34px;
                border: none;
                padding: 0 16px;
                border-radius: 0 8px 8px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
          }
        }
      }

      .button-group {
        display: flex;
        gap: 12px;
        
        .el-button {
          padding: 8px 16px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 8px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          height: 36px;
          font-size: 14px;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    .batch-actions-bar {
      background: linear-gradient(to right, #f5f7fa, #f9fafc);
      padding: 14px 20px;
      margin: 0;
      border-radius: 0;
      border-left: none;
      border-right: none;
      border-top: none;
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      animation: fadeIn 0.3s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid #eef1f7;
      margin-bottom: 12px;

      .selection-info {
        color: #606266;
        font-size: 14px;
        display: flex;
        align-items: center;

        .count {
          color: #409EFF;
          font-weight: 600;
          margin: 0 4px;
          display: inline-block;
          min-width: 24px;
          height: 24px;
          line-height: 24px;
          background: rgba(64, 158, 255, 0.1);
          border-radius: 12px;
          padding: 0 8px;
          text-align: center;
        }

        .el-button {
          margin-left: 12px;
          padding: 0;
          color: #909399;
          
          &:hover {
            color: #409EFF;
          }
        }
      }
      
      .batch-buttons {
        .operation-group {
          .el-button {
            padding: 6px 12px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            margin-left: 8px;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            }
          }
        }
      }
    }

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }

      .role-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        
        i {
          color: #409EFF;
          font-size: 16px;
        }
        
        span {
          color: #1a1f36;
          font-weight: 500;
        }
      }

      .no-remark {
        color: #909399;
        font-style: italic;
      }

      .delete-btn {
        color: #F56C6C;
        
        &:hover {
          color: #f78989;
        }
        
        &[disabled] {
          color: #C0C4CC;
          cursor: not-allowed;
        }
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;
      
      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;
        
        .btn-prev, 
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;
          
          &:not(.disabled):hover {
            border-color: #409EFF;
          }
          
          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }
        
        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }
        
        .el-pagination__jump {
          margin-left: 16px;
        }
        
        .el-select .el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }
        
        .el-pagination__editor.el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }
}

::v-deep .el-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
    
    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      
      .el-dialog__close {
        font-size: 18px;
        color: #909399;
        font-weight: bold;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    background: #f8f9fb;

    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    
    .el-button {
      padding: 9px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      & + .el-button {
        margin-left: 12px;
      }

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);
        
        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

.role-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        .el-input,
        .el-select,
        .el-cascader {
          width: 100%;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-select,
        .el-cascader {
          .el-input {
            width: 100%;
          }
        }
      }
    }
  }
}

.form-tips {
  margin-top: 6px;
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 4px;
    color: #E6A23C;
  }
}

.role-code {
  color: #909399;
  font-size: 13px;
  margin-left: 8px;
}

.clickable-tag {
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

.detail-card {
  background: #f8f9fb;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);
  
  &:hover {
    background: #f5f7fa;
  }
  
  .detail-header {
    padding: 16px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      color: #1a1f36;
      font-weight: 600;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
    
    .header-with-tabs {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .detail-content {
    padding: 16px;
  }
}

.inheritance-list, .permission-list, .resource-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  
  .role-tag, .permission-tag, .resource-tag {
    margin-bottom: 8px;
    padding: 6px 10px;
    border-radius: 6px;
  }
}

.resource-tabs {
  ::v-deep .el-tabs__header {
    margin: 0;
  }
  
  ::v-deep .el-tabs__nav {
    padding: 0;
  }
}

.resource-permission-wrapper {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fff;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    border-radius: 3px;
    
    &:hover {
      background: rgba(144, 147, 153, 0.5);
    }
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.permission-jump {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .permission-summary {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-around;
    width: 100%;
    
    .summary-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .summary-label {
        font-size: 14px;
        font-weight: 600;
        color: #1a1f36;
      }
      
      .summary-value {
        margin-top: 4px;
        font-size: 12px;
        color: #606266;
      }
    }
  }
  
  .permission-link-btn {
    margin-top: 16px;
    padding: 8px 16px;
    font-weight: 500;
    border-radius: 8px;
    background-color: #409EFF;
    border-color: #409EFF;
    color: #fff;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    
    &:hover {
      transform: translateY(-2px);
      background-color: #5aacff;
      border-color: #5aacff;
      box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
    }
  }
  
  .permission-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 4px;
      color: #E6A23C;
    }
  }
}

.role-relation-selector {
  width: 100%;
  display: flex;
  flex-direction: column;
  
  .role-search-box {
    margin-bottom: 16px;
    
    ::v-deep .el-input__inner {
      border-radius: 8px;
      height: 36px;
      background: #f7f9fc;
      border: 1px solid #e0e5ee;
      
      &:focus {
        background: #fff;
        border-color: #409EFF;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
      }
      
      &:hover {
        border-color: #c0d0e9;
      }
    }
  }
  
  .role-relation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 8px;
    
    .relation-title {
      font-size: 14px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 10px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 2px;
      }
    }
  }
  
  .role-tree-container {
    border: 1px solid #e0e5ee;
    border-radius: 8px;
    background: #fff;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: inset 0 0 6px rgba(31, 45, 61, 0.05);
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .role-list {
      .role-item {
        padding: 8px 10px;
        margin: 4px 0;
        border-radius: 6px;
        transition: all 0.2s ease;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        &.disabled {
          opacity: 0.6;
          background-color: #f5f7fa;
          cursor: not-allowed;
        }
        
        .role-checkbox {
          width: 100%;
          margin-right: 0;
          
          ::v-deep .el-checkbox__label {
            width: calc(100% - 24px); /* 减去checkbox本身的宽度 */
          }
        }
        
        .role-info {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .role-name {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            
            i {
              color: #409EFF;
              font-size: 14px;
            }
            
            span {
              color: #303133;
            }
          }
          
          .role-code {
            color: #909399;
            font-size: 13px;
            margin-left: auto;
          }
        }
      }
    }
  }
  
  .form-tips {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f0f9eb;
    border-radius: 6px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #67C23A;
      font-size: 16px;
    }
    
    span {
      color: #606266;
      font-size: 13px;
    }
  }
}

.role-relation-tags {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  
  .clickable-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 10px;
    font-weight: 500;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    cursor: pointer;
    
    i {
      font-size: 12px;
    }
    
    &:hover {
      box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
    }
  }
}

.role-relation-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  
  .inheritance-notice {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .role-relation-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    margin-bottom: 20px;
    width: 100%;
    
    .role-relation-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      &.indirect-inheritance .role-relation-card {
        background: linear-gradient(to bottom, #f5f7fa, #eef2f8);
        border: 1px solid #e0e5ee;
        opacity: 0.85;
      }
      
      .role-relation-card {
        background: linear-gradient(to bottom, #f6f9fe, #ecf5ff);
        border: 1px solid #d1e9ff;
        border-radius: 8px;
        padding: 12px 16px;
        min-width: 180px;
        box-shadow: 0 4px 10px rgba(31, 45, 61, 0.07);
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
        position: relative;
        
        &:hover {
          box-shadow: 0 6px 12px rgba(64, 158, 255, 0.15);
        }
        
        .role-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-wrap: wrap;
          
          i {
            color: #409EFF;
            font-size: 16px;
          }
          
          .role-name {
            font-weight: 600;
            color: #303133;
          }
          
          .role-code {
            color: #909399;
            font-size: 13px;
            margin-left: 4px;
          }
          
          .indirect-tag {
            margin-left: auto;
            font-size: 10px;
            padding: 0 5px;
            height: 18px;
            line-height: 16px;
            border-radius: 9px;
          }
        }
        
        .role-relation-arrow {
          text-align: center;
          margin-top: 12px;
          height: 20px;
          
          i {
            color: #409EFF;
            font-size: 20px;
            opacity: 0.6;
          }
        }
      }
    }
  }
  
  .current-role-node {
    background-color: #ecf5ff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(64, 158, 255, 0.15);
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      top: -20px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 12px solid transparent;
      border-right: 12px solid transparent;
      border-bottom: 20px solid #ecf5ff;
    }
    
    .role-card {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .role-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #409EFF, #64B5F6);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
        
        i {
          color: #fff;
          font-size: 24px;
        }
      }
      
      .role-detail {
        h4 {
          margin: 0 0 4px;
          font-size: 16px;
          color: #303133;
          font-weight: 600;
        }
        
        span {
          color: #606266;
          font-size: 13px;
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.code-tag {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 4px;
  
  &.el-tag--info {
    background-color: #f5f7fa;
    border-color: #e0e5ee;
    color: #606266;
    
    &:hover {
      background-color: #eef1f6;
    }
  }
}
</style>
