const nodes = [
    {
        id: 'trigger',
        name: '触发器',
        nodes: [
            { type: 'HTTP_TRIGGER', label: 'http入站', icon: 'el-icon-monitor', propertyPanel: () => import('../propertyPanel/trigger/HttpInboundTrigger.vue') },
            { type: 'SCHEDULED_TRIGGER', inAnchor: false, label: '定时任务', icon: 'el-icon-alarm-clock', propertyPanel: () => import('../propertyPanel/trigger/ScheduledTrigger.vue'), properties: { triggerType: 'fixRate', fixRate: 5000 } }
        ]
    },
    {
        id: 'request',
        name: '通用请求',
        nodes: [
            { type: 'HTTP_REQUEST', label: 'HTTP', icon: 'el-icon-connection', propertyPanel: () => import('../propertyPanel/request/HttpRequest.vue') },
            { type: 'JDBC_REQUEST', label: 'JDBC', icon: 'el-icon-coin', propertyPanel: () => import('../propertyPanel/request/JdbcRequest.vue') },
            { type: 'FTP_EXECUTOR', label: 'FTP', icon: 'el-icon-files', propertyPanel: () => import('../propertyPanel/request/FtpExecutor.vue') }
        ]
    },
    {
        id: 'script',
        name: '脚本',
        nodes: [
            { type: 'GROOVY_SCRIPT', label: 'Groovy', icon: 'el-icon-document', propertyPanel: () => import('../propertyPanel/script/index.vue') },
            { type: 'PYTHON_SCRIPT', label: 'Python', icon: 'el-icon-document', propertyPanel: () => import('../propertyPanel/script/index.vue') },
            { type: 'JS_SCRIPT', label: 'JS', icon: 'el-icon-document', propertyPanel: () => import('../propertyPanel/script/index.vue') }
        ]
    },
    {
        id: 'flow',
        name: '流程控制',
        nodes: [
            { type: 'BRANCH_FLOW', label: '分支', icon: 'el-icon-back', propertyPanel: () => import('../propertyPanel/flow/BranchFlow.vue')},
            { type: 'AGGREGATOR_FLOW', label: '聚合', icon: 'el-icon-right', propertyPanel: () => import('../propertyPanel/flow/AggregatorFlow.vue')},
            { type: 'LOOP_FLOW', label: '循环', icon: 'el-icon-refresh'}
        ]
    },
    {
        id: 'store',
        name: '数据存储',
        nodes: [
            { type: 'STORE_PUT', label: '缓存设置', icon: 'el-icon-folder-opened', propertyPanel: () => import('../propertyPanel/store/StorePut.vue'), properties: { store: {} } },
        ]
    }
]

const getNodes = function () {
    return new Promise(resolve => {
        //todo 动态获取节点
        resolve([...nodes])
    })
}

export async function getNodeByType(type) {
    const list = await getNodes()
    return list.flatMap(category =>
        category?.nodes?.filter(n => n.type === type)
    )[0]
}

export default getNodes