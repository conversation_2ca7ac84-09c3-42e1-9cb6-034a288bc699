import uniqueId from '@form-create/utils/lib/unique';
import { localeProps } from '@form-create/designer-zrw/src/utils';

const label = 'g文件上传';
const name = 'guploader';

export default {
    menu: 'custom',
    icon: 'icon-upload',
    label,
    name,
    event: ['change', 'remove'],
    validate: ['array'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            $required: false,
            props: {},
        };
    },
    props(_, { t }) {

        return localeProps(t, 'custom.props', [
            {
                type: 'input',
                field: 'accept',
                value: 'image/*',
                title: '文件类型',
            },
            {
                type: 'inputNumber',
                field: 'limit',
                value: 3,
                title: '文件个数限制',
            },
            {
                type: 'switch',
                field: 'disabled',
                title: t('custom.props.disabled')
            }]);
    }
};
