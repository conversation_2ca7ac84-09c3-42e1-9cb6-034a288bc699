<template>
  <div class="top-bar">
    <div class="left-section">
      <div class="logo">
        <svg-icon
            v-if="isSvgIcon"
            :icon-class="systemIcon"
            class="logo-svg"
        />
        <img
            v-else-if="isImageLink"
            :src="resolvedSystemIcon"
            alt="logo"
            class="logo-img"
        />
        <img
            v-else
            :src="defaultLogo"
            alt="logo"
            class="logo-img"
        />
        <span class="logo-text">{{ systemName }}</span>
      </div>
    </div>
    <div class="header-right">
      <notice-icon/>
      <el-dropdown trigger="hover" @command="handleCommand">
        <div class="avatar-wrapper">
          <el-avatar :size="rpx(40)" :src="picUrl" :style="{ backgroundColor: '#409EFF' }">
            {{ $store.state.user.name?.charAt(0) }}
          </el-avatar>
          <span class="username">{{ $store.state.user.name }}</span>
          <i class="el-icon-arrow-down"></i>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown-menu">
          <el-dropdown-item command="profile">
            <i class="el-icon-user"></i> 个人信息
          </el-dropdown-item>
          <el-dropdown-item command="notice">
            <i class="el-icon-bell"></i> 消息中心
            <el-badge v-if="noticeCount" :value="noticeCount" class="notice-badge"/>
          </el-dropdown-item>
          <!-- <el-dropdown-item command="settings">
            <i class="el-icon-setting"></i> 系统设置
          </el-dropdown-item> -->
          <el-dropdown-item divided command="logout">
            <i class="el-icon-switch-button"></i> 退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import {getConfigByKey} from '@system/api/sys/config'
import NoticeIcon from '@/layout/components/Notice/NoticeIcon.vue'

export default {
  name: 'TopBar',
  components: {
    NoticeIcon
  },
  props: {
    currentModuleTitle: {
      type: String,
      default: ''
    },
    currentMenuTitle: {
      type: String,
      default: ''
    },
    isModuleSelect: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      url: process.env.VUE_APP_FILE_URL,
      systemName: 'ExtremePlus', // 默认系统名称
      systemIcon: '', // 系统图标
      userInfo: {
        name: '',
        role: ' ',
        picUrl: '',
        employeeId: ' ',
        department: ' ',
        email: ' ',
        phone: ' ',
        joinDate: ' '
      }
    }
  },
  computed: {
    picUrl() {
      return this.url + this.$store.state.user.info.picUrl
    },
    noticeCount() {
      // 获取消息组件中的未读消息数
      return this.$store.state.user.unreadNoticeCount || 0
    },
    isSvgIcon() {
      return typeof this.systemIcon === 'string' &&
          this.systemIcon.trim() !== '' &&
          !/^https?:\/\//.test(this.systemIcon) &&
          !this.systemIcon.includes('/');
    },
    isImageLink() {
      return typeof this.systemIcon === 'string' &&
          this.systemIcon.trim() !== '' &&
          (/^https?:\/\//.test(this.systemIcon) || this.systemIcon.includes('/'));
    },
    resolvedSystemIcon() {
      if (!this.systemIcon || typeof this.systemIcon !== 'string') return '';
      return /^https?:\/\//.test(this.systemIcon)
          ? this.systemIcon
          : `${process.env.VUE_APP_FILE_URL}/${this.systemIcon.replace(process.env.VUE_APP_FILE_URL + '/', '')}`;
    },
    defaultLogo() {
      return require('../../assets/logo.svg');
    }
  },
  created() {
    this.getSystemConfig()
  },
  methods: {
    // 获取系统配置
    getSystemConfig() {
      // 获取系统名称
      getConfigByKey('builtin.topBar.name').then(res => {
        const value = this.extractValueFromResponse(res);
        if (value) {
          this.systemName = value;
        }
      }).catch((err) => {
        // 获取失败时使用默认名称
      })

      // 获取系统图标
      getConfigByKey('builtin.topBar.icon').then(res => {
        const value = this.extractValueFromResponse(res);
        if (value) {
          this.systemIcon = value;
        }
      }).catch((err) => {
        // 获取失败时使用默认图标
      })
    },

    // 辅助函数：从各种可能的响应结构中提取值
    extractValueFromResponse(res) {
      // 直接是字符串
      if (typeof res === 'string' && res.trim() !== '') {
        return res;
      }

      // 直接是值类型（非对象）
      if (res !== null && typeof res !== 'object') {
        return String(res);
      }

      // 是对象，检查可能的属性
      if (res && typeof res === 'object') {
        // 检查 value 属性
        if ('value' in res && res.value !== null && res.value !== undefined && String(res.value).trim() !== '') {
          return String(res.value);
        }

        // 检查 data 属性
        if ('data' in res) {
          // data 是字符串
          if (typeof res.data === 'string' && res.data.trim() !== '') {
            return res.data;
          }

          // data 是对象且有 value 属性
          if (res.data && typeof res.data === 'object' && 'value' in res.data &&
              res.data.value !== null && res.data.value !== undefined &&
              String(res.data.value).trim() !== '') {
            return String(res.data.value);
          }
        }

        // 检查 code 和 msg 等常见API返回模式，可能值在其他地方
        if ('code' in res && res.code === 0 || res.code === 200) {
          // 成功响应，检查常见值存放位置
          const possibleValueKeys = ['result', 'data', 'obj', 'object', 'item', 'info'];
          for (const key of possibleValueKeys) {
            if (key in res && res[key] !== null && res[key] !== undefined) {
              if (typeof res[key] === 'string' && res[key].trim() !== '') {
                return res[key];
              } else if (typeof res[key] === 'object' && 'value' in res[key]) {
                return String(res[key].value);
              }
            }
          }
        }
      }

      // 未找到有效值
      return null;
    },
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push({path: "/profile/index"});
          break;
        case 'notice':
          this.$router.push({path: "/notice/center"});
          break;
        case 'settings':

          break
        case 'logout':
          this.$confirm('确认退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            localStorage.removeItem('recentModules')
            this.$router.push('/logout')
            this.$message.success('退出登录成功')
          }).catch(() => {
          })
          break
      }
    },
    handleChangePassword() {
      this.$prompt('请输入新密码', '修改密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputPattern: /^.{6,20}$/,
        inputErrorMessage: '密码长度在6-20个字符之间'
      }).then(({value}) => {
        // TODO: 调用修改密码接口
        this.$message.success('密码修改成功')
      }).catch(() => {
      })
    },
    handleUpdateAvatar() {
      this.$message.info('头像更新功能开发中...')
    },
    handleWechatBind() {
      if (this.userInfo.wechatBound) {
        this.$confirm('确定要解除微信绑定吗？', '提示', {
          type: 'warning'
        }).then(() => {
          // TODO: 调用解除绑定接口
          this.$message.success('解除绑定成功')
        }).catch(() => {
        })
      } else {
        this.$message.info('微信绑定功能开发中...')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.top-bar {
  height: 64px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;

  .left-section {
    display: flex;
    align-items: center;

    .logo {
      height: 64px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      margin-right: 40px;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 4px;

      &:hover {
        background-color: #f5f7fa;

        .logo-img, .logo-svg {
          transform: scale(1.1);
        }
      }

      .logo-img, .logo-svg {
        height: 32px;
        margin-right: 10px;
        transition: transform 0.3s;
      }

      .logo-svg {
        font-size: 32px;
        color: #409EFF;
      }

      .logo-text {
        font-size: 18px;
        font-weight: 500;
        background: linear-gradient(120deg, #409EFF, #007AFF);
        -webkit-background-clip: text;
        color: transparent;
      }
    }

    .el-breadcrumb {
      ::v-deep .el-breadcrumb__inner {
        color: #909399;
        font-weight: normal;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;

        &.is-link {
          &:hover {
            color: #409EFF;
          }
        }

        .el-icon-s-home {
          margin-right: 5px;
          font-size: 16px;
          color: #606266;
        }
      }

      ::v-deep .el-breadcrumb__item:last-child .el-breadcrumb__inner {
        color: #303133;
        cursor: default;
        font-weight: 500;

        &:hover {
          color: #303133;
        }
      }

      ::v-deep .el-breadcrumb__separator {
        color: #C0C4CC;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 100%;

    .avatar-wrapper {
      height: 40px;
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      padding-right: 12px;
      border-radius: 20px;
      transition: all 0.3s;
      background-color: #f5f7fa;
      border: 1px solid #e4e7ed;

      &:hover {
        background-color: #ecf5ff;
        border-color: #d9ecff;

        .username {
          color: #409EFF;
        }
      }

      .el-avatar {
        margin-left: -1px;
        width: 40px;
        height: 40px;
        border: 2px solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .username {
        margin: 0 8px;
        font-size: 14px;
        color: #606266;
        transition: color 0.3s;
      }

      .el-icon-arrow-down {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

@keyframes shake {
  10%,
  90% {
    transform: rotate(-2deg);
  }

  20%,
  80% {
    transform: rotate(2deg);
  }

  30%,
  50%,
  70% {
    transform: rotate(-1deg);
  }

  40%,
  60% {
    transform: rotate(1deg);
  }
}

.profile-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .profile-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .profile-header {
      text-align: center;
      padding: 32px 20px;
      background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);

      .avatar-wrapper {
        position: relative;
        display: inline-block;
        margin-bottom: 16px;

        .el-avatar {
          border: 4px solid rgba(255, 255, 255, 0.8);
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .avatar-actions {
          margin-top: 8px;
        }
      }

      h2 {
        margin: 16px 0 8px;
        font-size: 22px;
        color: #303133;
        font-weight: 600;
      }

      .role-tag {
        display: inline-block;
        padding: 4px 16px;
        background: linear-gradient(45deg, #409EFF, #007AFF);
        color: white;
        border-radius: 20px;
        font-size: 13px;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }

    .profile-info {
      flex: 1;
      padding: 24px;
      overflow-y: auto;

      .info-section {
        & + .info-section {
          margin-top: 24px;
        }

        h3 {
          font-size: 15px;
          font-weight: 500;
          color: #303133;
          margin: 0 0 12px;
        }

        .info-group {
          background-color: #f8f9fb;
          border-radius: 8px;
          padding: 12px;
        }

        .info-item {
          display: flex;
          align-items: center;
          padding: 12px;

          & + .info-item {
            border-top: 1px solid #ebeef5;
          }

          i {
            font-size: 18px;
            color: #409EFF;
            margin-right: 12px;
          }

          label {
            width: 40px;
            color: #909399;
            font-size: 14px;
          }

          span {
            flex: 1;
            margin-left: 12px;
            color: #303133;
            font-size: 14px;
          }
        }

        .security-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px;

          & + .security-item {
            border-top: 1px solid #ebeef5;
          }

          .security-info {
            display: flex;
            align-items: center;

            i {
              font-size: 18px;
              color: #409EFF;
              margin-right: 12px;
            }

            span {
              font-size: 14px;
              color: #303133;
            }
          }
        }
      }
    }

    .profile-footer {
      padding: 16px 24px;
      border-top: 1px solid #ebeef5;
      text-align: center;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>

<style lang="scss">
.user-dropdown-menu {
  min-width: 150px !important;
}

.notice-badge {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
</style>