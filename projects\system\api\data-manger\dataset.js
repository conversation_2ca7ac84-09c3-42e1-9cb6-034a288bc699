import request from '@/utils/request'

const api = CONSTANT.META


// 获取数据集树形列表
export function getDatasetTree(params) {
  return request({
    url: api + '/folder/tree',
    method: 'get',
    params
  })
}

// 新增数据集文件夹
export function createFolder(data) {
  return request({
    url: api + '/folder',
    method: 'post',
    data
  })
}

// 新增数据集
export function createDataset(data) {
  return request({
    url: api + '/dynamic/mapper',
    method: 'post',
    data: [data]  // 包装成数组
  })
}

// 获取数据集详情
export function getDatasetDetail(id) {
  return request({
    url: api + '/dynamic/mapper',
    method: 'get',
    params: { id }
  })
}

// 更新数据集
export function updateDataset(data) {
  return request({
    url: api + '/dynamic/mapper',
    method: 'put',
    data
  })
}

// 执行SQL预览
export function previewSQL(data) {
  return request({
    url: api + '/dataset/preview',
    method: 'post',
    data
  })
}

// 添加执行SQL查询接口
export function executeSQL(namespace, mappedId, params) {
  return request({
    url: api + `/dynamic/mapper/execute/${namespace}/${mappedId}`,
    method: 'get',
    params // 添加查询参数
  })
}

// 删除文件夹
export function deleteFolder(ids) {
  return request({
    url: api + `/folder/${ids}`,
    method: 'delete'
  })
}

// 删除数据集
export function deleteDataset(ids) {
  return request({
    url: api + `/dynamic/mapper/${ids}`,
    method: 'delete'
  })
}

// 更新文件夹
export function updateFolder(data) {
  return request({
    url: api + '/folder',
    method: 'put',
    data
  })
}

// 获取文件夹详情
export function getFolderDetail(id) {
  return request({
    url: api + `/folder/`,
    method: 'get',
    params: { id }
  })
}

// 获取文件夹列表
export function getFolderList() {
  return request({
    url: api + '/folder/list',
    method: 'get'
  })
} 

// 获取数据集列表
export function getDatasetList(params) {
  return request({
    url: api + '/dynamic/mapper/list',
    method: 'get',
    params: {
      current: 1,
      size: -1,
      ...params
    }
  })
}

// 获取缓存策略列表
export function getCacheStrategies() {
  return request({
    url: api + '/cache/strategy/list',
    method: 'get'
  })
}