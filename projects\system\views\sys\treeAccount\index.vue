<template>
  <div class="account-management">
    <!-- 左侧组织树 -->
    <div class="department-tree">
      <div class="tree-header">
        <div class="header-title">
          <span>{{ viewMode === 'dept' ? '组织架构' : '角色列表' }}</span>
          <!-- 视角切换按钮移到标题右边，使用更紧凑的下拉菜单 -->
          <div class="view-switcher">
            <el-tooltip :content="viewMode === 'dept' ? '切换到角色视角' : '切换到部门视角'" placement="top">
              <el-button 
                type="text" 
                class="switch-btn" 
                @click="handleQuickViewSwitch">
                <i :class="viewMode === 'dept' ? 'el-icon-user' : 'el-icon-office-building'"></i>
                <span>{{ viewMode === 'dept' ? '角色视角' : '部门视角' }}</span>
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="search-box">
          <el-input
            v-model="deptSearchKeyword"
            :placeholder="viewMode === 'dept' ? '搜索部门名称' : '搜索角色名称'"
            prefix-icon="el-icon-search"
            clearable
            size="small"
            @clear="handleDeptSearchClear"
          />
        </div>
      </div>
      <div class="tree-container">
        <!-- 部门视角树 -->
        <el-tree
          v-if="viewMode === 'dept'"
          ref="departmentTree"
          :data="treeData"
          :props="defaultProps"
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ node }" class="custom-tree-node">
            <div class="node-content">
              <i class="el-icon-office-building"></i>
              <span :class="{ 'highlight': isHighlighted(node, deptSearchKeyword) }">
                {{ node.label }}
              </span>
            </div>
          </span>
        </el-tree>
        
        <!-- 角色视角树 -->
        <el-tree
          v-else
          ref="roleTree"
          :data="roleTreeData"
          :props="roleProps"
          :filter-node-method="filterRoleNode"
          node-key="id"
          highlight-current
          @node-click="handleRoleNodeClick"
        >
          <span slot-scope="{ node }" class="custom-tree-node">
            <div class="node-content">
              <i class="el-icon-user"></i>
              <span :class="{ 'highlight': isHighlighted(node, deptSearchKeyword) }">
                {{ node.label }}
              </span>
            </div>
          </span>
        </el-tree>
      </div>
    </div>

    <!-- 右侧用户列表 -->
    <div class="user-content-container">
      <div class="user-card">
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="left-section">
            <div class="title">
              <span>账号管理</span>
              <span class="dept-name" v-if="currentDept">- {{ currentDept.name }}</span>
              <span class="dept-name" v-if="currentRole">- {{ currentRole.name }}</span>
            </div>
            <div class="unified-search">
              <el-input
                v-model="searchForm.keyword"
                placeholder="输入用户名/姓名/手机号搜索"
                prefix-icon="el-icon-search"
                clearable
                class="search-input"
                @keyup.enter.native="handleSearch"
                @clear="resetSearch"
              >
                <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
              </el-input>
              <el-select
                v-model="searchForm.valid"
                placeholder="状态"
                class="status-select"
                clearable
                @change="handleSearch"
              >
                <el-option label="启用" :value="true" />
                <el-option label="禁用" :value="false" />
              </el-select>
            </div>
          </div>
          <div class="right-section">
            <el-button-group class="import-export-group">
              <el-button type="primary" icon="el-icon-plus" @click="handleAddUser">新增用户</el-button>
              <!-- 批量赋权按钮 - 仅在角色视角下显示 -->
              <el-button 
                v-if="viewMode === 'role' && currentRole && currentRole.id !== 'ALL_ROLES'" 
                type="primary" 
                icon="el-icon-connection" 
                @click="handleBatchRoleAuth">
                批量赋权
              </el-button>
              <el-button type="primary" icon="el-icon-download" @click="handleExport">导出</el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 批量操作栏 -->
        <div class="batch-actions-bar" v-show="selectedUsers.length">
          <div class="selection-info">
            已选择 <span class="count">{{ selectedUsers.length }}</span> 项
            <el-button type="text" @click="$refs.userTable.clearSelection()">清空选择</el-button>
          </div>
          <div class="batch-buttons">
            <el-button-group class="operation-group">
              <el-button 
                type="danger" 
                icon="el-icon-delete" 
                size="small"
                @click="handleBatchDelete">删除</el-button>
              <el-button 
                type="warning" 
                icon="el-icon-unlock" 
                size="small"
                :disabled="!hasLockedUsers"
                @click="handleBatchUnlock">解锁</el-button>
              <el-button 
                type="info" 
                icon="el-icon-refresh" 
                size="small"
                @click="handleBatchResetPwd">重置密码</el-button>
              <el-button 
                type="danger" 
                plain
                icon="el-icon-close" 
                size="small"
                @click="handleBatchDisable">停用</el-button>
              <el-button 
                type="success" 
                icon="el-icon-check" 
                size="small"
                @click="handleBatchEnable">启用</el-button>
            </el-button-group>
          </div>
        </div>


        <!-- 表格区域 -->
        <div class="table-wrapper">
          <el-table
            ref="userTable"
            :data="userList"
            v-loading="loading"
            border
            stripe
            fit
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" :width=rpx(50) align="center" />
            <el-table-column label="头像" width="70" align="center">
              <template slot-scope="{ row }">
                <el-avatar 
                  :size="40" 
                  :src="row.picUrl ? url + '/' + row.picUrl : ''"
                  class="user-avatar"
                >
                  {{ (row.nickName || row.name)?.substring(0, 1) || '?' }}
                </el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="username" label="用户名" min-width="100">
              <template slot-scope="{ row }">
                <span class="username">{{ row.username }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" min-width="80">
              <template slot-scope="{ row }">
                <span class="name">{{ row.nickName || row.name || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" min-width="120">
              <template slot-scope="{ row }">
                <el-link type="primary" :underline="false" v-if="row.phone">{{ row.phone }}</el-link>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="email" label="邮箱" min-width="120">
              <template slot-scope="{ row }">
                <el-link type="primary" :underline="false" v-if="row.email">{{ row.email }}</el-link>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="lastLoginTime" label="最后登录" min-width="120">
              <template slot-scope="{ row }">
                <span>{{ row.lastLoginTime || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="70" align="center">
              <template slot-scope="{ row }">
                <el-tag
                  :type="row.valid === true ? 'success' :  'warning' "
                  size="small"
                  effect="dark"
                >
                  {{ row.valid === true ? '启用'  : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="锁定状态" prop="lockedTime" min-width="80" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.lockedTime ? 'danger' : 'success'" effect="plain">
                  {{ scope.row.lockedTime ? '已锁定' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="80" align="center">
              <template slot-scope="{ row }">
                <el-dropdown trigger="click" @command="(command) => handleCommand(command, row)">
                  <el-button type="text" class="action-button">
                    操作<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="view">
                      <i class="el-icon-view"></i> 查看
                    </el-dropdown-item>
                    <el-dropdown-item command="edit">
                      <i class="el-icon-edit"></i> 编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="resetPwd" divided>
                      <i class="el-icon-refresh"></i> 重置密码
                    </el-dropdown-item>
                    <el-dropdown-item command="unlock" v-if="row.lockedTime">
                      <i class="el-icon-unlock"></i> 解锁
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided class="danger">
                      <i class="el-icon-delete"></i> 删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              :current-page="page.current"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="page.size"
              :total="page.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="userForm"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
        class="user-form"
      >
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input v-model="userForm.username" :disabled="dialogType === 'edit' || dialogType === 'view'" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="password" v-if="dialogType === 'add'">
                <el-input v-model="userForm.password" type="password" show-password placeholder="默认是123456,请输入密码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="姓名" prop="nickName">
                <el-input 
                  v-model="userForm.nickName" 
                  :disabled="dialogType === 'view'" 
                  placeholder="请输入姓名"
                  @input="handleNameInput" 
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="userForm.phone" :disabled="dialogType === 'view'" placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="userForm.email" :disabled="dialogType === 'view'" placeholder="请输入邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="valid">
                <el-switch
                  v-model="userForm.valid"
                  :disabled="dialogType === 'view'"
                  :active-value="true"
                  :inactive-value="false"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">组织信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属部门" prop="deptId">
                <el-cascader
                  v-model="userForm.deptId"
                  :options="dialogTreeData"
                  :props="{
                    checkStrictly: true,
                    label: 'name',
                    value: 'id',
                    emitPath: false
                  }"
                  placeholder="请选择所属部门"
                  clearable
                  class="custom-cascader"
                  :disabled="dialogType === 'view'"
                >
                  <template slot-scope="{ node, data }">
                    <i class="el-icon-office-building"></i>
                    <span>{{ data.name }}</span>
                    <span class="dept-code" v-if="data.code">({{ data.code }})</span>
                  </template>
                </el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属职位" prop="postId">
                <el-select v-model="userForm.postId" :disabled="dialogType === 'view'" placeholder="请选择职位" clearable>
                  <el-option
                    v-for="item in postOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色" prop="roleIds">
                <el-select v-model="userForm.roleIds" :disabled="dialogType === 'view'" multiple placeholder="请选择角色" clearable>
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据权限" prop="scope">
                <el-select v-model="userForm.scope" :disabled="dialogType === 'view'" placeholder="请选择数据权限" clearable>
                  <el-option
                    v-for="option in scopeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 自定义数据权限部门选择器 -->
          <el-row v-if="userForm.scope === 'CUSTOM'" class="custom-dept-row">
            <el-col :span="24">
              <el-form-item label="权限部门" prop="dataPermDeptIds" :rules="customScopeRules" class="data-perm-depts-item">
                <el-cascader
                  v-model="userForm.dataPermDeptIds"
                  :options="dialogTreeData"
                  :props="{
                    checkStrictly: true,
                    label: 'name',
                    value: 'id',
                    multiple: true,
                    emitPath: false
                  }"
                  :disabled="dialogType === 'view'"
                  clearable
                  placeholder="请选择数据权限部门"
                  style="width: 100%"
                >
                  <template slot-scope="{ node, data }">
                    <i class="el-icon-office-building"></i>
                    <span>{{ data.name }}</span>
                    <span class="dept-code" v-if="data.code">({{ data.code }})</span>
                  </template>
                </el-cascader>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">其他信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="头像" prop="picUrl">
                <el-upload
                  v-if="dialogType !== 'view'"
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                  :http-request="handleAvatarUpload"
                  :on-success="handleAvatarSuccess"
                  :on-error="handleAvatarError"
                  accept="image/png,image/jpeg"
                >
                  <el-avatar :size="100" :src="userForm.picUrl ? url + '/' + userForm.picUrl : ''">
                    <div style="background-color: #c0c4cc;">{{ userForm.name ? userForm.name.charAt(0).toUpperCase() : 'U' }}</div>
                  </el-avatar>
                </el-upload>
                <el-avatar v-else :size="100" :src="userForm.picUrl ? url + '/' + userForm.picUrl : ''">
                  <div style="background-color: #c0c4cc;">{{ userForm.name ? userForm.name.charAt(0).toUpperCase() : 'U' }}</div>
                </el-avatar>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Host" prop="host">
                <el-input v-model="userForm.host" :disabled="dialogType === 'view'" placeholder="请输入host" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ dialogType === 'view' ? '关闭' : '取 消' }}</el-button>
        <el-button v-if="dialogType !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">
          确 定
        </el-button>
      </div>
    </el-dialog>
    
    <!-- 批量赋权对话框 -->
    <batch-role-auth
      v-if="currentRole !== null && currentRole !== undefined"
      v-model="batchRoleAuthVisible"
      :role="currentRole"
      @success="handleBatchRoleAuthSuccess"
    />
  </div>
</template>

<script>
import { getDeptTreeList, getUserList, updateUser,  getRoleList, getPostList, createUser, exportUser, importUser, downloadTemplate, batchDeleteUsers, unlockAccount, batchUnlockAccounts, batchResetPasswords, toggleAccountStatus, getConfigByKey } from '@system/api/sys/treeAccount'
import { getUsersByRoleId } from '@system/api/sys/role'
import UploadHelper from '@/components/auto-page/fileUpload/uploadHelper.js'
import BatchRoleAuth from './components/BatchRoleAuth.vue'

export default {
  name: 'TreeAccount',
  components: {
    BatchRoleAuth
  },
  data() {
    // 密码验证
    const validatePassword = (rule, value, callback) => {
      if (this.dialogType === 'add' && !value) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }

    return {
      // 部门树相关
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      deptSearchKeyword: '',
      currentDept: null,
      
      // 角色树相关
      viewMode: 'dept', // 默认为部门视角
      roleTreeData: [],
      roleProps: {
        children: 'children',
        label: 'name'
      },
      currentRole: null,
      
      // 批量赋权相关
      batchRoleAuthVisible: false,

      // 搜索相关
      searchForm: {
        keyword: '',
        valid: null
      },

      // 用户列表相关
      loading: false,
      userList: [],
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      
      // 对话框相关
      dialogVisible: false,
      dialogType: 'add', // add or edit
      submitLoading: false,

      // 表单相关
      uploadUrl: process.env.VUE_APP_BASE_API + '/system/file/upload', // 上传地址
      postOptions: [], // 职位选项
      roleOptions: [], // 角色选项
      userForm: {
        id: '',
        username: '',
        password: '123456',
        name: '',
        nickName: '',
        phone: '',
        email: '',
        valid: true,
        deptId: '',
        postId: '',
        roleIds: [],
        scope: 'SELF',
        dataPermDeptIds: [],
        picUrl: '',
        host: ''
      },
      userRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          // { min: 2, max: 20, message: '长度在2-20个字符', trigger: 'blur' },
          // { pattern: /^[a-zA-Z]/, message: '用户名须以字母开头', trigger: 'blur' },
          // { pattern: /^[a-zA-Z0-9_\-.]+$/, message: '不允许有空格，仅允许字母、数字、_、-、.', trigger: 'blur' }
        ],
        password: [
          { validator: validatePassword, trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '姓名不能包含空格', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '手机号不能包含空格', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
          { pattern: /^[^\s]+$/, message: '邮箱不能包含空格', trigger: 'blur' }
        ],
        deptId: [
          { required: true, message: '请选择所属部门', trigger: 'change' }
        ],
        roleIds: [
          // 移除必填校验，允许角色为空
          { type: 'array', message: '角色格式错误', trigger: 'change' }
        ],
        scope: [
          { required: true, message: '请选择数据权限', trigger: 'change' }
        ]
      },
      customScopeRules: [
        { required: true, message: '请选择权限部门', trigger: 'change' },
        { type: 'array', min: 1, message: '请至少选择一个部门', trigger: 'change' }
      ],
      selectedUsers: [], // 新增选中用户数组
      // 添加数据权限选项配置
      scopeOptions: [
        { label: '全部数据', value: 'ALL' },
        { label: '本部门及以下', value: 'DEPTS' },
        { label: '本部门', value: 'DEPT' },
        { label: '自定义', value: 'CUSTOM' },
        { label: '仅本人', value: 'SELF' }
      ],
    }
  },
  computed: {
    dialogTitle() {
      const type = this.dialogType
      const titles = {
        add: '新增用户',
        edit: '编辑用户',
        view: '用户详情'
      }
      return titles[type] || ''
    },
    url() {
      return process.env.VUE_APP_FILE_URL
    },
    // 对话框中的部门树数据（不包含全部账号节点）
    dialogTreeData() {
      return this.treeData.filter(item => item.id !== 'ALL')
    },
    // 判断是否有被锁定的用户
    hasLockedUsers() {
      return this.selectedUsers.some(user => user.lockedTime);
    }
  },
  watch: {
    deptSearchKeyword(val) {
      if (this.viewMode === 'dept') {
        this.$refs.departmentTree.filter(val)
      } else {
        this.$refs.roleTree.filter(val)
      }
    },
    'userForm.scope'(val) {
      if (val !== 'CUSTOM') {
        this.userForm.dataPermDeptIds = [];
      }
    }
  },
  created() {
    this.getTreeData()
    this.getRoleOptions()
    this.getPostOptions()
    this.getScopeOptions() // 添加获取数据权限配置的调用
  },
  methods: {
    // 获取部门树数据
    async getTreeData() {
      try {
        const data = await getDeptTreeList()
        // 添加固定的全部账号节点
        this.treeData = [{
          id: 'ALL',
          name: '全部账号',
          children: []
        }, ...data]
        // 默认选中第一个节点
        if (this.treeData.length > 0) {
          this.$nextTick(() => {
            const firstNode = this.treeData[0]
            this.$refs.departmentTree.setCurrentKey(firstNode.id)
            this.handleNodeClick(firstNode)
          })
        }
      } catch (error) {
        this.$message.error('获取组织架构失败')
      }
    },
    
    // 视角切换处理
    async handleViewModeChange(mode) {
      this.currentDept = null
      this.currentRole = null
      this.deptSearchKeyword = ''
      
      if (mode === 'role') {
        await this.getRoleTreeData()
      } else {
        // 切换回部门视角，选中第一个节点
        this.$nextTick(() => {
          if (this.treeData.length > 0) {
            const firstNode = this.treeData[0]
            this.$refs.departmentTree.setCurrentKey(firstNode.id)
            this.handleNodeClick(firstNode)
          }
        })
      }
    },
    
    // 快速切换视图
    async handleQuickViewSwitch() {
      // 切换视图模式
      const newMode = this.viewMode === 'dept' ? 'role' : 'dept'
      this.viewMode = newMode
      await this.handleViewModeChange(newMode)
    },
    
    // 获取角色树数据
    async getRoleTreeData() {
      this.loading = true
      try {
        const result = await getRoleList({ valid: true, size: -1 })
        const roles = result.records || []
        
        // 添加全部角色节点
        this.roleTreeData = [{
          id: 'ALL_ROLES',
          name: '全部账号',
          children: []
        }, ...roles.map(role => ({
          id: role.id,
          name: role.name,
          code: role.code
        }))]
        
        this.loading = false
        
        // 默认选中第一个节点
        this.$nextTick(() => {
          if (this.roleTreeData.length > 0) {
            const firstRoleNode = this.roleTreeData[0]
            this.$refs.roleTree.setCurrentKey(firstRoleNode.id)
            this.handleRoleNodeClick(firstRoleNode)
          }
        })
      } catch (error) {
        this.$message.error('获取角色列表失败')
        this.loading = false
      }
    },
    
    // 角色树节点过滤
    filterRoleNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase())
    },
    
    // 角色树节点点击
    async handleRoleNodeClick(role) {
      this.currentDept = null
      this.currentRole = role
      this.page.current = 1
      
      this.loading = true
      try {
        if (role.id === 'ALL_ROLES') {
          // 全部角色，显示所有用户
          await this.getUserList()
        } else {
          // 获取特定角色的用户
          await this.getUsersByRole(role.id)
        }
      } finally {
        this.loading = false
      }
    },
    
    // 获取角色下的用户
    async getUsersByRole(roleId) {
      try {
        // 确保roleId是有效值
        if (roleId === null || roleId === undefined) {
          this.$message.error('角色ID不能为空');
          return;
        }
        
            // 不转换roleId为数字类型，保持字符串以防止精度丢失
    const params = {
      current: this.page.current,
      size: this.page.size
    };
    
    // 添加查询关键词（如果有）
    if (this.searchForm.keyword) {
      params.keyword = this.searchForm.keyword;
    }
    
    // 添加状态过滤（如果有）
    if (this.searchForm.valid !== null) {
      params.valid = this.searchForm.valid;
    }
    
    console.log('获取角色用户，roleId:', roleId, '类型:', typeof roleId);
    
    const { records, total } = await getUsersByRoleId({
      roleId,
      ...params
    });
        
        console.log('获取角色用户成功，数量:', records?.length || 0);
        
        this.userList = (records || []).map(item => ({
          ...item,
          picUrl: item.picUrl || ''
        }));
        this.page.total = total || 0;
      } catch (error) {
        console.error('获取角色用户列表失败', error);
        this.$message.error('获取角色用户列表失败');
        this.userList = [];
        this.page.total = 0;
      }
    },

    // 部门树节点过滤
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase())
    },

    // 清除部门搜索
    handleDeptSearchClear() {
      if (this.viewMode === 'dept') {
        this.$refs.departmentTree.filter('')
      } else {
        this.$refs.roleTree.filter('')
      }
    },

    // 判断节点是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false
      return node.label.toLowerCase().includes(keyword.toLowerCase())
    },

    // 点击部门树节点
    handleNodeClick(data) {
      this.currentDept = data
      this.currentRole = null
      this.page.current = 1
      this.getUserList()
    },

    // 获取用户列表
    async getUserList() {
      this.loading = true
      try {
        const params = {
          keyword: this.searchForm.keyword,
          current: this.page.current,
          size: this.page.size
        }
        // 添加调试日志
        console.log('searchForm.valid:', this.searchForm.valid)
   
        // 如果valid不是null，则添加到参数中（包括false值）
        if (this.searchForm.valid !== null) {
          params.valid = this.searchForm.valid
        }
        console.log('params:', params)
        // 如果不是"全部账号"节点,则添加deptId参数
        if (this.currentDept && this.currentDept.id !== 'ALL') {
          params.deptId = this.currentDept.id
        }
        const { records, total } = await getUserList(params)
        this.userList = (records || []).map(item => ({
          ...item,
          picUrl: item.picUrl || ''
        }))
        this.page.total = total || 0
      } catch (error) {
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取角色列表
    async getRoleOptions() {
      try {
        const data =await getRoleList({ valid: true, size: -1 })
        this.roleOptions = data.records.map(item => ({
          id: item.id,
          name: item.name,
          code: item.code
        }))
      } catch (error) {
        console.error('获取角色列表失败', error)
        this.$message.error('获取角色列表失败')
      }
    },

    // 获取职位列表
    async getPostOptions() {
      try {
        const data  = await getPostList()
        this.postOptions = data.records.map(item => ({
          id: item.id,
          name: item.name,
          code: item.code
        }))
      } catch (error) {
        console.error('获取职位列表失败', error)
        this.$message.error('获取职位列表失败')
      }
    },

    // 头像上传前的处理
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
        return false;
      }
      return true;
    },

    // 头像上传处理
    handleAvatarUpload({ file, onProgress, onSuccess, onError }) {
      new UploadHelper().upload(
        file,
        (progress) => {
          onProgress({ percent: progress });
        },
        (url) => {
          onSuccess({ url });
        },
        onError
      );
    },

    // 头像上传成功
    async handleAvatarSuccess(res, file) {
      const picUrl = res.url
      this.$set(this.userForm, 'picUrl', picUrl)
      this.$message.success('上传成功')
    },

    // 头像上传失败
    handleAvatarError(err, file) {
      this.$message.error('上传失败')
    },

    // 新增用户
    handleAddUser() {
      // 修改判断逻辑，允许在角色视图下也能添加用户
      if (this.viewMode === 'dept' && (!this.currentDept || this.currentDept.id === 'ALL')) {
        this.$message.warning('请在左侧部门树中选择具体部门')
        return
      }
      
      this.dialogType = 'add'
      this.userForm = {
        username: '',
        password: '123456',
        name: '',
        nickName: '',
        phone: '',
        email: '',
        valid: true,
        deptId: this.viewMode === 'dept' && this.currentDept ? this.currentDept.id : '',
        postId: '',
        roleIds: this.viewMode === 'role' && this.currentRole && this.currentRole.id !== 'ALL_ROLES' ? [this.currentRole.id] : [],
        scope: 'SELF',
        dataPermDeptIds: [],
        picUrl: '',
        host: ''
      }
      this.dialogVisible = true
      // 获取选项数据
      this.getPostOptions()
      this.getRoleOptions()
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      const actions = {
        view: () => this.handleViewUser(row),
        edit: () => this.handleEditUser(row),
        resetPwd: () => this.handleResetPassword(row),
        unlock: () => this.handleUnlockUser(row),
        delete: () => this.handleDeleteUser(row)
      }
      if (actions[command]) {
        actions[command]()
      }
    },

    // 重置密码
    async handleResetPassword(row) {
      try {
        await this.$confirm('确定要重置该用户的密码吗？', '提示', {
          type: 'warning'
        })
        this.loading = true;
        await batchResetPasswords([row.id]);
        this.$message.success('密码重置成功，默认密码为123456');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('密码重置失败', error);
          this.$message.error('密码重置失败: ' + (error.message || '未知错误'));
        }
      } finally {
        this.loading = false;
      }
    },

    // 查看用户
    handleViewUser(row) {
      this.dialogType = 'view'
      // 处理头像URL
      const picUrl = row.picUrl ? row.picUrl.replace(process.env.VUE_APP_FILE_URL + '/', '') : ''
      this.userForm = {
        ...row,
        picUrl,
        valid: row.valid === true,
        roleIds: row.roleIds || [],
        dataPermDeptIds: row.deptIds || [],
        // 确保显示正确的名称
        nickName: row.nickName || row.name,
        name: row.nickName || row.name
      }
      this.dialogVisible = true
      // 获取选项数据
      this.getPostOptions()
      this.getRoleOptions()
    },

    // 编辑用户
    handleEditUser(row) {
      this.dialogType = 'edit'
      // 处理头像URL，移除baseURL前缀
      const picUrl = row.picUrl ? row.picUrl.replace(process.env.VUE_APP_FILE_URL + '/', '') : ''
      this.userForm = {
        ...row,
        picUrl,
        valid: row.valid === true,
        roleIds: row.roleIds || [],
        dataPermDeptIds: row.deptIds || [],
        // 确保显示正确的名称
        nickName: row.nickName || row.name,
        name: row.nickName || row.name
      }
      this.dialogVisible = true
      // 获取选项数据
      this.getPostOptions()
      this.getRoleOptions()
    },

    // 删除用户
    handleDeleteUser(row) {
      this.$confirm('确定要删除该用户吗？', '提示', {
        type: 'warning'
      }).then(async () => {
          this.loading = true;
          try {
            await batchDeleteUsers([row.id]);
            this.$message.success('删除成功');
            this.getUserList();
          } catch (error) {
            console.error('删除失败', error);
            this.$message.error('删除失败: ' + (error.message || '未知错误'));
          } finally {
            this.loading = false;
          }
      }).catch(() => {})
    },

    // 提交表单
    handleSubmit() {
      this.$refs.userForm.validate(async valid => {
        if (!valid) return

        this.submitLoading = true
        try {
          const data = { ...this.userForm }
          
          // 确保当scope不是CUSTOM时，dataPermDeptIds为空数组
          if (data.scope !== 'CUSTOM') {
            data.dataPermDeptIds = [];
          }
          
          // 将dataPermDeptIds的值赋给deptIds字段
          data.deptIds = data.dataPermDeptIds;
          delete data.dataPermDeptIds; // 删除dataPermDeptIds字段，不提交

          // 确保name和nickName字段都存在且值相同
          data.name = data.nickName;

          if (this.dialogType === 'edit') {
            await updateUser(data)
          } else {
            await createUser(data)
          }
          this.$message.success(this.dialogType === 'add' ? '新增成功' : '更新成功')
          this.dialogVisible = false
          this.getUserList()
        }  finally {
          this.submitLoading = false
        }
      })
    },

    // 搜索
    handleSearch() {
      this.page.current = 1
      if (this.viewMode === 'dept') {
        this.getUserList()
      } else if (this.currentRole) {
        // 修改：当选择全部角色时，使用与部门视角一致的查询逻辑
        if (this.currentRole.id === 'ALL_ROLES') {
          this.getUserList()
        } else {
          this.getUsersByRole(this.currentRole.id)
        }
      }
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        valid: null
      }
      this.handleSearch()
    },

    // 表格选择相关
    handleSelectionChange(selection) {
      this.selectedUsers = selection
    },

    // 分页相关
    handleSizeChange(val) {
      this.page.size = val
      if (this.viewMode === 'dept') {
        this.getUserList()
      } else if (this.currentRole) {
        // 修改：当选择全部角色时，使用与部门视角一致的查询逻辑
        if (this.currentRole.id === 'ALL_ROLES') {
          this.getUserList()
        } else {
          this.getUsersByRole(this.currentRole.id)
        }
      }
    },
    
    handleCurrentChange(val) {
      this.page.current = val
      if (this.viewMode === 'dept') {
        this.getUserList()
      } else if (this.currentRole) {
        // 修改：当选择全部角色时，使用与部门视角一致的查询逻辑
        if (this.currentRole.id === 'ALL_ROLES') {
          this.getUserList()
        } else {
          this.getUsersByRole(this.currentRole.id)
        }
      }
    },

    // 批量操作相关
    handleBatchDelete() {
      this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？`, '提示', {
        type: 'warning'
      }).then(async () => {
        this.loading = true;
        try {
          const ids = this.selectedUsers.map(user => user.id);
          await batchDeleteUsers(ids);
          this.$message.success('批量删除成功');
          this.getUserList();
          this.$refs.userTable.clearSelection();
        } catch (error) {
          console.error('批量删除失败', error);
        } finally {
          this.loading = false;
        }
      }).catch(() => {});
    },

    handleBatchEnable() {
      const ids = this.selectedUsers.map(user => user.id)
      this.$confirm(`确定要启用选中的 ${ids.length} 个用户吗？`, '提示', {
        type: 'warning'
      }).then(async () => {
        this.loading = true;
        try {
          await toggleAccountStatus(ids, true)
          this.$message.success('批量启用成功')
          this.getUserList()
          this.$refs.userTable.clearSelection();
        } catch (error) {
          console.error('批量启用失败', error);
        } finally {
          this.loading = false;
        }
      }).catch(() => {})
    },

    handleBatchDisable() {
      const ids = this.selectedUsers.map(user => user.id)
      this.$confirm(`确定要停用选中的 ${ids.length} 个用户吗？`, '提示', {
        type: 'warning'
      }).then(async () => {
        this.loading = true;
        try {
          await toggleAccountStatus(ids, false)
          this.$message.success('批量停用成功')
          this.getUserList()
          this.$refs.userTable.clearSelection();
        } catch (error) {
          console.error('批量停用失败', error);
        } finally {
          this.loading = false;
        }
      }).catch(() => {})
    },

    handleBatchUnlock() {
      // 筛选出锁定状态的用户
      const lockedUsers = this.selectedUsers.filter(user => user.lockedTime);
      
      if (lockedUsers.length === 0) {
        this.$message.warning('没有选中被锁定的用户');
        return;
      }
      
      this.$confirm(`确定要解锁选中的 ${lockedUsers.length} 个用户吗？`, '提示', {
        type: 'warning'
      }).then(async () => {
        this.loading = true;
        try {
          const usernames = lockedUsers.map(user => user.username);
          await batchUnlockAccounts(usernames);
          this.$message.success('批量解锁成功');
          this.getUserList();
          this.$refs.userTable.clearSelection();
        } catch (error) {
          console.error('批量解锁失败', error);
          this.$message.error('批量解锁失败: ' + (error.message || '未知错误'));
        } finally {
          this.loading = false;
        }
      }).catch(() => {});
    },

    handleBatchResetPwd() {
      this.$confirm(`确定要重置选中的 ${this.selectedUsers.length} 个用户的密码吗？`, '提示', {
        type: 'warning'
      }).then(async () => {
        this.loading = true;
        try {
          const ids = this.selectedUsers.map(user => user.id);
          await batchResetPasswords(ids);
          this.$message.success('密码重置成功，默认密码为123456');
          this.$refs.userTable.clearSelection();
        } catch (error) {
          console.error('批量重置密码失败', error);
          this.$message.error('批量重置密码失败: ' + (error.message || '未知错误'));
        } finally {
          this.loading = false;
        }
      }).catch(() => {});
    },

    handleExport() {
      try {
        // 构造导出参数
        const params = {
          keyword: this.searchForm.keyword
        }
        // 如果valid不是null，则添加到参数中
        if (this.searchForm.valid !== null) {
          params.valid = this.searchForm.valid
        }
        // 如果不是"全部账号"节点,则添加deptId参数
        if (this.currentDept && this.currentDept.id !== 'ALL') {
          params.deptId = this.currentDept.id
        }
        exportUser(params)
      } catch (error) {
        this.$message.error('导出失败')
      }
    },

    // 导入用户数据
    async handleImport() {
      try {
        // 创建文件选择器
        const input = document.createElement('input')
        input.type = 'file'
        input.accept = '.xlsx,.xls'
        
        input.onchange = async (e) => {
          const file = e.target.files[0]
          if (!file) return
          
          try {
            await importUser(file)
            this.$message.success('导入成功')
            this.getUserList()
          } catch (error) {
            this.$message.error('导入失败')
          }
        }
        
        input.click()
      } catch (error) {
        this.$message.error('导入失败')
      }
    },

    // 下载导入模板
    async handleDownloadTemplate() {
      try {
        await downloadTemplate()
        this.$message.success('模板下载成功')
      } catch (error) {
        this.$message.error('模板下载失败')
      }
    },

    // 处理导入相关命令
    handleImportCommand(command) {
      const actions = {
        import: () => this.handleImport(),
        template: () => this.handleDownloadTemplate()
      }
      actions[command]?.()
    },

    // 解锁单个用户
    async handleUnlockUser(row) {
      if (!row.lockedTime) {
        this.$message.warning('该用户未被锁定');
        return;
      }
      
      try {
        await this.$confirm(`确定要解锁用户 ${row.username} 吗？`, '提示', {
          type: 'warning'
        });
        
        this.loading = true;
        await unlockAccount(row.username);
        this.$message.success('账户解锁成功');
        this.getUserList();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('解锁失败', error);
          this.$message.error('解锁失败: ' + (error.message || '未知错误'));
        }
      } finally {
        this.loading = false;
      }
    },

    // 获取数据权限配置
    async getScopeOptions() {
      try {
        const response = await getConfigByKey('builtin.account.permission')
        if (response && Array.isArray(response)) {
          // 构建新的选项数组
          const configuredOptions = response.map(value => {
            const label = {
              'ALL': '全部数据',
              'DEPTS': '本部门及以下',
              'DEPT': '本部门',
              'CUSTOM': '自定义',
              'SELF': '仅本人'
            }[value]
            return { label, value }
          })
          if (configuredOptions.length > 0) {
            this.scopeOptions = configuredOptions
          }
        }
      } catch (error) {
        // 如果获取失败，保持默认选项
        console.warn('Failed to fetch scope options, using defaults')
      }
    },

    // 添加新方法
    handleNameInput(value) {
      // 同步更新name字段
      this.userForm.name = value;
    },
    
    // 批量赋权相关
    handleBatchRoleAuth() {
      // 修改判断条件，确保角色ID为0的情况也能正常处理
      if (this.currentRole === null || this.currentRole === undefined || this.currentRole.id === 'ALL_ROLES') {
        this.$message.warning('请先选择一个具体角色')
        return
      }
      
      // 再次确认currentRole是有效对象
      if (typeof this.currentRole !== 'object') {
        this.$message.warning('角色数据无效，请重新选择角色')
        return
      }
      
      this.batchRoleAuthVisible = true
    },
    
    // 批量赋权成功后的回调
    handleBatchRoleAuthSuccess() {
      // 刷新用户列表以显示最新授权情况
      if (this.currentRole && this.currentRole.id !== 'ALL_ROLES') {
        this.getUsersByRole(this.currentRole.id)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.account-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  gap: 24px;
  overflow: hidden;

  .department-tree {
    width: 280px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);

    &:hover {
      box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    }

    .tree-header {
      padding: 20px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .header-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        span {
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          letter-spacing: 0.5px;
          position: relative;
          padding-left: 12px;
          
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: linear-gradient(to bottom, #409EFF, #64B5F6);
            border-radius: 3px;
          }
        }
      }

      .view-switcher {
        .switch-btn {
          padding: 3px 8px;
          color: #409EFF;
          background: rgba(64, 158, 255, 0.1);
          border: none;
          border-radius: 4px;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(64, 158, 255, 0.2);
            transform: translateY(-1px);
          }
          
          i {
            margin-right: 4px;
            font-size: 14px;
          }
          
          span {
            font-size: 12px;
            font-weight: 500;
          }
        }
      }

      .search-box {
        .el-input {
          width: 100%;
          ::v-deep .el-input__inner {
            border-radius: 10px;
            height: 36px;
            background: #f7f9fc;
            border: 1px solid #e7ebf3;
            
            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
          
          ::v-deep .el-input__prefix {
            left: 10px;
            i {
              color: #8492a6;
            }
          }
        }
      }
    }

    .tree-container {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #fff;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      ::v-deep {
        .el-tree {
          background: transparent;
        }

        .el-tree-node__content {
          height: 32px;
          border-radius: 8px;
          margin: 2px 0;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #f5f7fa;
          }

          .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;
            
            .node-content {
              display: flex;
              align-items: center;
              gap: 8px;

              i {
                font-size: 14px;
                
                &.el-icon-office-building {
                  color: #409EFF;
                }
                
                &.el-icon-user {
                  color: #67C23A;
                }
              }
            }
            
            .node-actions {
              display: none;
              gap: 2px;

              .el-button {
                padding: 1px 2px;
                margin: 0;
                
                i {
                  font-size: 13px;
                  margin: 0;
                }

                &.danger {
                  color: #F56C6C;
                }
              }
            }
          }
          
          .el-tree-node.is-current > .el-tree-node__content {
            background-color: #ecf5ff !important;
            color: #409EFF;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
            
            .custom-tree-node .node-content {
              span {
                color: #409EFF;
              }
            }
            
            .node-actions {
              display: flex;
            }
          }
        }
      }
    }
  }

  .user-content-container {
    flex: 1;
    background: transparent;
    display: flex;
    overflow: hidden;
    padding: 0;
    min-width: 0;

    .user-card {
      background: #fff;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
      transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
      width: 100%;
      display: flex;
      flex-direction: column;
      height: 100%;
      
      &:hover {
        box-shadow: 0 10px 30px rgba(31, 45, 61, 0.1);
      }

      .card-header {
        padding: 20px;
        border-bottom: 1px solid #eef1f7;
        background: linear-gradient(to right, #fcfcfd, #f9fafc);
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .left-section {
          display: flex;
          flex-direction: column;
          gap: 16px;
          max-width: 600px;
          width: 100%;
          
          .title {
            font-size: 20px;
            font-weight: 600;
            color: #1a1f36;
            display: flex;
            align-items: center;
            gap: 8px;
            letter-spacing: 0.5px;
            padding-left: 12px;
            border-left: 3px solid #409EFF;
            
            .dept-name {
              color: #409EFF;
              font-weight: 500;
              font-size: 18px;
              position: relative;
              padding-left: 12px;
              
              &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 2px;
                height: 16px;
                background: #409EFF;
                border-radius: 2px;
              }
            }
          }
          
          .unified-search {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .search-input {
              flex: 1;
              ::v-deep {
                .el-input__inner {
                  height: 42px;
                  line-height: 42px;
                  font-size: 14px;
                  border: 1px solid #e0e5ee;
                  transition: all 0.3s ease;
                  padding-left: 42px;
                  border-top-left-radius: 10px;
                  border-bottom-left-radius: 10px;
                  background: #f9fafc;

                  &:hover {
                    border-color: #c0d0e9;
                    background: #f5f7fa;
                  }

                  &:focus {
                    background: #fff;
                    border-color: #409EFF;
                    box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
                  }
                }

                .el-input__prefix {
                  left: 14px;
                  .el-icon-search {
                    font-size: 18px;
                    line-height: 42px;
                    color: #8492a6;
                  }
                }

                .el-input-group__append {
                  background-color: #f9fafc;
                  border-left: 1px solid #e0e5ee;
                  padding: 0;
                  border-top-right-radius: 10px;
                  border-bottom-right-radius: 10px;

                  .el-button {
                    margin: 0;
                    height: 40px;
                    border: none;
                    padding: 0 20px;
                    border-radius: 0 10px 10px 0;
                    background: transparent;
                    font-weight: 500;
                    color: #409EFF;
                    transition: all 0.3s;

                    &:hover {
                      background-color: #ecf5ff;
                      color: #66b1ff;
                    }

                    i {
                      margin-right: 4px;
                    }
                  }
                }
              }
            }

            .status-select {
              width: 120px;
              ::v-deep {
                .el-input__inner {
                  height: 42px;
                  line-height: 42px;
                  border-radius: 10px;
                  background: #f9fafc;
                  border: 1px solid #e0e5ee;

                  &:hover {
                    border-color: #c0d0e9;
                    background: #f5f7fa;
                  }

                  &:focus {
                    background: #fff;
                    border-color: #409EFF;
                    box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
                  }
                }
              }
            }
          }
        }
        
        .right-section {
          display: flex;
          align-items: flex-start;
          margin-left: 20px;
          
          .import-export-group {
            display: flex;
            
            .el-button {
              padding: 9px 18px;
              font-weight: 500;
              transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
              position: relative;
              border-radius: 10px;
              margin-left: 8px;
              background-color: #409EFF;
              border-color: #409EFF;
              overflow: hidden;
              z-index: 1;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

              &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
                z-index: -1;
              }

              &:hover {
                transform: translateY(-3px);
                background-color: #5aacff;
                border-color: #5aacff;
                box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
              }
              
              &:active {
                transform: translateY(-1px);
              }

              i {
                margin-right: 6px;
                font-size: 16px;
              }
            }
          }
        }
      }
      
      .batch-actions-bar {
        background: linear-gradient(to right, #f5f7fa, #f9fafc);
        padding: 14px 20px;
        margin: 0;
        border-radius: 0;
        border-left: none;
        border-right: none;
        border-top: none;
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        animation: fadeIn 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        border: 1px solid #eef1f7;

        .selection-info {
          color: #606266;
          font-size: 14px;
          display: flex;
          align-items: center;

          .count {
            color: #409EFF;
            font-weight: 600;
            margin: 0 4px;
            display: inline-block;
            min-width: 24px;
            height: 24px;
            line-height: 24px;
            background: rgba(64, 158, 255, 0.1);
            border-radius: 12px;
            padding: 0 8px;
            text-align: center;
          }

          .el-button {
            margin-left: 12px;
            padding: 0;
            color: #909399;
            
            &:hover {
              color: #409EFF;
            }
          }
        }
        
        .batch-buttons {
          .operation-group {
            .el-button {
              padding: 6px 12px;
              font-weight: 500;
              position: relative;
              overflow: hidden;
              margin-left: 8px;
              border-radius: 8px;
              transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
              }
              
              &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
                z-index: -1;
              }
              
              i {
                margin-right: 4px;
                font-size: 14px;
              }
              
              &.el-button--danger {
                background-color: #f56c6c;
                border-color: #f56c6c;
                &:hover {
                  background-color: #fa8c8c;
                  border-color: #fa8c8c;
                }
                &.is-plain {
                  background-color: #fff;
                  color: #f56c6c;
                  &:hover {
                    background-color: #f56c6c;
                    color: #fff;
                  }
                }
              }

              &.el-button--warning {
                background-color: #e6a23c;
                border-color: #e6a23c;
                &:hover {
                  background-color: #ecb45a;
                  border-color: #ecb45a;
                }
              }

              &.el-button--info {
                background-color: #909399;
                border-color: #909399;
                &:hover {
                  background-color: #a0a3a8;
                  border-color: #a0a3a8;
                }
              }

              &.el-button--success {
                background-color: #67c23a;
                border-color: #67c23a;
                &:hover {
                  background-color: #79cf52;
                  border-color: #79cf52;
                }
              }
            }
          }
        }
      }

      .table-header {
        padding: 16px 24px;
        border-bottom: 1px solid #eef1f7;
        background: linear-gradient(to right, #f7f9fc, #f3f6fa);
        flex-shrink: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1a1f36;
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: -1px;
      }

      .table-wrapper {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
        background: #fff;
        
        ::v-deep .el-table {
          flex: 1;
          display: flex;
          flex-direction: column;
          table-layout: auto;
          
          .el-table__inner-wrapper {
            height: 100%;
            display: flex;
            flex-direction: column;
          }
          
          .el-table__header-wrapper {
            flex-shrink: 0;
            
            th {
              background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
              font-weight: 600;
              color: #1a1f36;
              height: 44px;
              padding: 8px 0;
              
              .cell {
                font-size: 14px;
                line-height: 28px;
              }
            }
          }
          
          .el-table__body-wrapper {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            
            &::-webkit-scrollbar {
              width: 6px;
              height: 0;
            }
            
            &::-webkit-scrollbar-thumb {
              border-radius: 3px;
              background: rgba(144, 147, 153, 0.3);
              
              &:hover {
                background: rgba(144, 147, 153, 0.5);
              }
            }
            
            &::-webkit-scrollbar-track {
              background: transparent;
            }
            
            td {
              padding: 8px 0;
              
              .cell {
                line-height: 1.5;
              }
            }
          }
          
          .operation-column {
            .cell {
              padding: 0 12px;
              white-space: nowrap;
              display: flex;
              justify-content: center;
              gap: 4px;
              line-height: 28px;
              height: 28px;
              
              .el-button {
                padding: 4px 8px;
                margin: 0;
                height: 28px;
                line-height: 1;
                display: inline-flex;
                align-items: center;
                
                i {
                  margin-right: 0;
                  font-size: 14px;
                }
                
                &.danger {
                  color: #F56C6C;
                }
              }
            }
          }
        }
        
        .pagination-container {
          margin: 0;
          margin-right: 12px;
          padding: 12px 24px;
          background: #fff;
          border-top: 1px solid #eef1f7;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          flex-shrink: 0;
          position: relative;
          z-index: 1;
          height: 56px;
          box-sizing: border-box;
          
          ::v-deep .el-pagination {
            padding: 0;
            font-weight: normal;
            white-space: nowrap;
            
            .btn-prev, 
            .btn-next,
            .el-pager li {
              margin: 0 4px;
              min-width: 32px;
              border-radius: 4px;
              border: 1px solid #e0e5ee;
              
              &:not(.disabled):hover {
                border-color: #409EFF;
              }
              
              &.active {
                background-color: #409EFF;
                border-color: #409EFF;
                color: #fff;
              }
            }
            
            .el-pagination__total,
            .el-pagination__sizes {
              margin-right: 16px;
            }
            
            .el-pagination__jump {
              margin-left: 16px;
            }
            
            .el-select .el-input {
              margin: 0 8px;
              
              .el-input__inner {
                border-radius: 4px;
                height: 28px;
                line-height: 28px;
                padding-right: 25px;
              }
            }
            
            .el-pagination__editor.el-input {
              margin: 0 8px;
              
              .el-input__inner {
                border-radius: 4px;
                height: 28px;
                line-height: 28px;
              }
            }
          }
        }
      }
    }
  }
}

.user-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);


    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        .el-input,
        .el-select,
        .el-cascader {
          width: 100%;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-select,
        .el-cascader {
          .el-input {
            width: 100%;
          }
        }
      }
    }
  }
}

::v-deep {
  .danger {
    color: #F56C6C;
  }

  .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
    margin-top: 8vh !important;
    max-height: 84vh;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
      padding: 24px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
      
      .el-dialog__headerbtn {
        top: 20px;
        right: 20px;
        
        .el-dialog__close {
          font-size: 18px;
          color: #909399;
          font-weight: bold;
          
          &:hover {
            color: #409EFF;
          }
        }
      }
    }

    .el-dialog__body {
      padding: 30px 24px;
      overflow-y: auto;
      background: #f8f9fb;

      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 3px;
        
        &:hover {
          background: rgba(144, 147, 153, 0.5);
        }
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }

    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #ebeef5;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);
      
      .el-button {
        padding: 9px 20px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

        & + .el-button {
          margin-left: 12px;
        }

        &--default {
          border-color: #dcdfe6;
          background: linear-gradient(to bottom, #fff, #f9fafc);
          
          &:hover {
            border-color: #c0c4cc;
            color: #606266;
            background: #f5f7fa;
          }
        }

        &--primary {
          background: #409EFF;
          border-color: #409EFF;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          
          &:hover {
            transform: translateY(-2px);
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
  }
}

.avatar-uploader {
  ::v-deep .el-upload {
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    width: 100px;
    height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409EFF;
      .el-avatar {
        transform: scale(1.05);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
      }
    }
  }

  ::v-deep .el-avatar {
    transition: all 0.3s;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: transparent;
    border-radius: 4px;
    width: 100%;
    height: 100%;
    line-height: 100px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}

::v-deep .el-avatar {
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: transparent;
  border-radius: 4px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }
}

.highlight {
  color: #409EFF;
  font-weight: 600;
  position: relative;
  z-index: 1;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: rgba(64, 158, 255, 0.2);
    z-index: -1;
    border-radius: 3px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-wrapper {
  ::v-deep .el-table {
    th {
      background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
      font-weight: 600;
      color: #1a1f36;
      height: 44px;
      padding: 8px 0;
      
      .cell {
        font-size: 14px;
      }
    }
    
    td {
      padding: 8px 0;
      
      .cell {
        line-height: 1.5;
      }
    }
    
    tr {
      transition: all 0.3s;
      
      &:hover {
        background: #f7f9fc !important;
      }
      
      &.current-row {
        td {
          background: #ecf5ff !important;
        }
      }
    }

    .user-avatar {
      background: linear-gradient(135deg, #409EFF, #64B5F6);
      color: #fff;
      font-size: 14px;
      border: 2px solid #fff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      transition: all 0.3s ease;
      width: 36px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      object-fit: cover;
      margin: 0 auto;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
      }

      img {
        border-radius: 4px;
      }
    }
    
    .username {
      color: #1a1f36;
      font-weight: 500;
      font-size: 14px;
    }
    
    .name {
      color: #606266;
      font-size: 14px;
    }
    
    .el-tag {
      padding: 0 12px;
      height: 24px;
      line-height: 22px;
      border-radius: 12px;
      font-weight: 500;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      
      &.el-tag--success {
        background-color: rgba(103, 194, 58, 0.1);
        border-color: rgba(103, 194, 58, 0.2);
        color: #67c23a;
      }
      
      &.el-tag--warning {
        background-color: rgba(230, 162, 60, 0.1);
        border-color: rgba(230, 162, 60, 0.2);
        color: #e6a23c;
      }
    }
    
    .action-button {
      font-size: 14px;
      color: #409EFF;
      padding: 4px 12px;
      border-radius: 6px;
      transition: all 0.3s;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.1);
      }
      
      i {
        transition: transform 0.3s ease;
      }
      
      &:hover i {
        transform: rotate(180deg);
      }
    }

    &::before {
      display: none;
    }
  }
}

/* Cascader 下拉菜单样式 */
::v-deep .el-cascader-panel {
  .el-cascader-menu {
    height: 300px;
    padding: 8px;
    border-right: 1px solid #ebeef5;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .el-cascader-menu__wrap {
      margin-right: -8px;
    }
    
    .el-cascader-node {
      padding: 8px 20px;
      display: flex;
      align-items: center;
      border-radius: 6px;
      margin: 2px 0;
      
      &:not(.is-disabled):hover {
        background-color: #f5f7fa;
      }
      
      &.in-active-path,
      &.is-active {
        background-color: #ecf5ff;
        color: #409EFF;
        font-weight: 500;
      }
      
      .el-icon-office-building {
        margin-right: 8px;
        font-size: 14px;
        color: #409EFF;
      }
      
      .dept-code {
        margin-left: 8px;
        color: #909399;
        font-size: 12px;
      }
      
      .el-cascader-node__postfix {
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

/* 自定义数据权限部门多选样式 */
::v-deep .el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

::v-deep .el-cascader {
  width: 100%;
  
  .el-cascader__tags {
    flex-wrap: wrap;
    padding: 3px 0;
    padding-left: 5px;
    max-height: 120px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .el-tag {
      margin: 2px;
      background-color: #ecf5ff;
      color: #409EFF;
      border-color: #d9ecff;
      max-width: calc(100% - 10px);
      display: inline-flex;
      align-items: center;
      
      // 标签内容过长时显示省略号
      .el-tag__content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
      }
      
      .el-tag__close {
        background-color: #409EFF;
        color: #fff;
        
        &:hover {
          background-color: #66b1ff;
        }
      }
    }
  }
}

/* 自定义数据权限部门表单项样式 */
::v-deep .data-perm-depts-item {
  margin-bottom: 25px;
  
  .el-form-item__content {
    min-height: 38px;
    height: auto;
  }
  
  .el-cascader {
    height: auto;
    min-height: 38px;
    
    .el-input.el-input--suffix {
      height: auto;
      min-height: 38px;
      
      .el-input__inner {
        height: auto;
        min-height: 38px;
        padding-top: 5px;
        padding-bottom: 5px;
      }
    }
  }
}

/* 自定义数据权限部门行样式与动画 */
.custom-dept-row {
  animation: fadeIn 0.3s ease-in-out;
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  padding: 15px 10px 5px;
  margin-top: 5px;
  border: 1px dashed rgba(64, 158, 255, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>