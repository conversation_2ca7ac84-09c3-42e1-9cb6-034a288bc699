特性	    ScriptProcessorNode	    AudioWorkletNode
执行环境	    主线程	                   Web Worker（独立线程）
性能	       可能阻塞 UI	               高性能，无 UI 阻塞
复杂度	       简单	                        需要编写 Worklet 脚本
浏览器支持	    较旧	                    较新（现代浏览器支持）


//两种获取音频节点的方式

        async requestAudioPermission() {
            let obj = await checkPermission()
            this.hasPermission = obj.hasPermission
            this.errorMessage = obj.errorMessage || ''
            if (this.hasPermission) {
                getUserMedia()
                    .then((stream) => {
                        let audioContext = newAudioContext()
                        let mediaStreamSource = createMediaStreamSource(audioContext, stream)
                        // let scriptProcessorNode = createScriptProcessor(audioContext)
                        // mediaStreamSource.connect(scriptProcessorNode)
                        // scriptProcessorNode.connect(audioContext.destination)
                        // scriptProcessorNode.onaudioprocess = this.onaudioprocess

                        // 使用 AudioWorkletNode 代替 ScriptProcessorNode
                        createAudioWorkletNode(audioContext)
                            .then((audioWorkletNode) => {
                                audioWorkletNode.onaudioprocess = (e) => {
                                    console.log('AudioProcessor message received:', e)
                                    this.onaudioprocess()
                                }
                                mediaStreamSource.connect(audioWorkletNode)
                                audioWorkletNode.connect(audioContext.destination)
                            })
                            .catch(err => {
                                console.error('Error creating AudioWorkletNode:', err);
                            });
                    })
                    .catch(err => {

                    })
            }
        },