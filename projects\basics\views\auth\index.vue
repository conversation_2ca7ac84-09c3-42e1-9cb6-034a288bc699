<template>
    <div style="position: absolute;left: 0;right: 0;top: 0;bottom: 0;background: #e6ebf5">
        <div class="login-body">
            <el-form class="loginForm" :model="loginForm" :rules="loginRules" ref="loginForm">
                <div class="input-data">
                    <label>用户名</label>
                    <el-form-item prop="username">
                        <el-input placeholder="请输入用户名" v-model="loginForm.username" required></el-input>
                    </el-form-item>
                </div>
                <div class="input-data">
                    <label>密码</label>
                    <el-form-item prop="password">
                        <el-input placeholder="请输入密码" v-model="loginForm.password" @keyup.enter="handleLogin"
                            show-password required></el-input>
                    </el-form-item>
                </div>

                <div class="input-data">
                    <label>验证码</label>
                    <div class="capt">
                        <el-form-item prop="code">
                            <el-input placeholder="请输入验证码" v-model="loginForm.code" @keyup.enter="handleLogin"
                                required />
                        </el-form-item>
                        <el-image :src="base64ToImage" style="width: 130px;height: 40px; border-radius: 3px;"
                            @click="clickPic" />
                    </div>
                </div>
            </el-form>
            <el-button class="pri_btn" type="primary" :loading="loading"
                @click.native.prevent="handleLogin">登录</el-button>
        </div>
    </div>
</template>
<script>
import { code } from "@/api/login/login";
import { getToken } from '@/utils/auth'
export default {
    data() {
        return {
            loginForm: {
                username: "",
                password: ""
            },
            loading: false,
            redirect: undefined,
            img: null,
            loginRules: {
                username: [
                    { required: true, trigger: "blur", message: '请输入用户名' }
                ],
                password: [
                    { required: true, trigger: "blur", message: '密码不能为空' }
                ],
                code: [
                    { required: true, trigger: "blur", message: '验证码不能为空' }
                ]
            },
        }
    },
    computed: {
        base64ToImage() {
            return `data:image/png;base64,${this.img}`;
        }
    },
    watch: {
        $route: {
            handler: function (route) {
                this.redirect = route.query && route.query.redirect;
            },
            immediate: true
        }
    },
    mounted() {
        if (getToken()) {
            this.redirectTo()
        } else {
            this.createCode()
        }
    },
    methods: {
        createCode() {
            code().then(res => {
                this.img = res.base64
                this.loginForm.uuid = res.uuid
            })
        },
        clickPic() {
            this.createCode();
        },

        redirectTo() {
            if (this.redirect?.startsWith('http://') || this.redirect?.startsWith('https://')) {
                window.location.href = `${this.redirect}${this.redirect.indexOf('?') > -1 ? '' : '?'}&Authorization=${getToken()}`
            } else {
                this.$router.push({ path: this.redirect || "/" });
            }
        },
        handleLogin() {
            this.$refs.loginForm.validate(valid => {
                if (valid) {
                    this.loading = true;
                    let passwordValue = this.loginForm.password;
                    let data = JSON.parse(JSON.stringify(this.loginForm));
                    data.password = this.$encruption(passwordValue);
                    this.$store
                        .dispatch("user/login", {data})
                        .then(() => {
                            this.redirectTo()
                            this.loading = false;
                        })
                        .catch((err) => {
                            this.loading = false;
                            this.createCode();
                            console.log("error submit!!", err);
                        });
                } else {
                    console.log("error submit!!");

                    return false;
                }
            });
        },
    }
}
</script>
<style lang="scss" scoped>
.login-body {
    width: 500px;
    height: 700px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(30px);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding-top: 70px;
    padding-left: 70px;
    padding-right: 70px;


    .pri_btn {
        width: 100%;
        height: 50px;
        font-size: 22px;
        margin-top: 70px;
    }

    .el-button--primary {
        background-color: rgba(67, 101, 220, 1);
        border-color: rgba(67, 101, 220, 1);
    }

    .el-button--primary:focus,
    .el-button--primary:hover {
        background: #66b1ff;
        border-color: #66b1ff;
        color: #fff;
    }

    .input-data {
        padding-top: 30px;

        label {
            font-size: 18px;
            font-weight: bold;
            color: rgba(76, 92, 130, 1);
            margin-bottom: 14px;
            display: block;
        }

        .capt {
            display: flex;
            justify-content: space-between;

            .el-input {
                width: 220px;
            }

            .el-image {
                width: 130px;
            }
        }
    }
}

::v-deep {
    .el-form-item {
        margin: 0 !important;
    }
}
</style>