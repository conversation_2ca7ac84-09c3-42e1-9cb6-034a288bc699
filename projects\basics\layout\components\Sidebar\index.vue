<template>
  <div :class="{ 'has-logo': showLogo, 'has-search': true }">
    <!-- <logo v-if="showLogo" :collapse="isCollapse" /> -->
    <component :is="config.logoComponent" v-if="showLogo" :collapse="isCollapse" />
    <div v-show="!isCollapse && searchShow" style="padding-left: 20px;padding-right: 20px;margin-bottom: 10px;">
      <el-input v-model="searchInput" placeholder="请输入内容" clearable prefix-icon="el-icon-search"></el-input>
    </div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="variables.menuBg"
        :text-color="variables.menuText" :unique-opened="false" :active-text-color="variables.menuActiveText"
        :collapse-transition="false" mode="vertical">
        <sidebar-item v-for="(route, index) in searchSliderRouters" :key="index" :item="route"
          :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.module.scss'
import config from '../../layout.config'

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      searchInput: null,
      searchSliderRouters: [],
      config,
    }
  },
  mounted() {
    this.searchSliderRouters = this.sidebarRouters
  },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters([
      'sidebar',
      'routes',
      'sidebarRouters'
    ]),
    // routes() {
    //   return this.$router.options.routes
    // },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    },
    searchShow() {
      let count = 0
      this.sidebarRouters?.forEach(e => {
        count++
        count += e.children?.length || 0
      });

      return count > 80
    }

  },
  watch: {
    sidebarRouters(val) {
      this.searchSliderRouters = val
    },
    searchInput(val) {
      let arr = []
      if (!this.searchInput || this.searchInput.length == 0)
        arr = this.sidebarRouters
      else
        arr = this.findItemById(this.sidebarRouters, this.searchInput)
      this.searchSliderRouters = arr
    },
  },
  methods: {
    findItemById(list, id, parent) {
      let res = list.filter((item) => item.meta && item.meta.title.indexOf(id) > -1);
      if (res && res.length) {
        if (parent) {
          let obj = { ...parent }
          obj.children = res
          return [obj]
        } else
          return res
      } else {
        for (let i = 0; i < list.length; i++) {
          if (list[i].children && list[i].children.length > 0) {
            res = this.findItemById(list[i].children, id, list[i]);
            if (res) return res;
          }
        }
        return null;
      }
    }
  }
}
</script>