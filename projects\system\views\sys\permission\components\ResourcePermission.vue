<template>
  <div class="resource-permission-container">
    <!-- 操作按钮组 - 更紧凑的设计 -->
    <div class="tree-actions-compact">
      <div class="actions-left">
        <el-tooltip content="全选" placement="top">
          <el-button size="mini" type="primary" icon="el-icon-check" circle plain @click="handleCheckAll"></el-button>
        </el-tooltip>
        <el-tooltip content="取消全选" placement="top">
          <el-button size="mini" type="info" icon="el-icon-close" circle plain @click="handleUncheckAll"></el-button>
        </el-tooltip>
      </div>
      
      <div class="actions-right">
        <el-tooltip content="展开所有" placement="top">
          <el-button size="mini" type="success" icon="el-icon-arrow-down" circle plain @click="handleExpandAll"></el-button>
        </el-tooltip>
        <el-tooltip content="收起所有" placement="top">
          <el-button size="mini" type="warning" icon="el-icon-arrow-up" circle plain @click="handleCollapseAll"></el-button>
        </el-tooltip>
        
        <!-- 重新放置勾选模式切换 -->
        <el-tooltip :content="cascadeMode ? '切换到独立勾选' : '切换到联动勾选'" placement="top">
          <el-button 
            size="mini" 
            :type="cascadeMode ? 'primary' : 'info'" 
            :icon="cascadeMode ? 'el-icon-link' : 'el-icon-connection'" 
            circle 
            plain 
            @click="toggleCheckMode">
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 资源树 -->
    <div class="tree-wrapper">
      <el-tree
        ref="resourceTree"
        v-loading="treeLoading"
        :data="resourceTreeData"
        :props="defaultProps"
        node-key="id"
        show-checkbox
        :default-checked-keys="selectedResourceIds"
        :filter-node-method="filterResourceNode"
        highlight-current
        :check-strictly="!cascadeMode"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <svg-icon v-if="data.icon" :icon-class="data.icon" class="node-icon" />
          <i v-else class="el-icon-menu"></i>
          <span :class="{ 'highlight': isHighlighted(node, keyword) }">
            {{ node.label }}
          </span>
          <el-tag size="mini" :type="getTypeTag(data.type)" class="resource-type">
            {{ getTypeLabel(data.type) }}
          </el-tag>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { getResourceTreeList } from '@system/api/sys/treeResource'

export default {
  name: 'ResourcePermission',
  props: {
    selectedResourceIds: {
      type: Array,
      default: () => []
    },
    keyword: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      resourceTreeData: [],
      treeLoading: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      typeLabels: {
        APP: '应用',
        MENU: '菜单',
        BUTTON: '按钮',
        DIR: '目录'
      },
      treeObserver: null,
      cascadeMode: false, // 默认为独立勾选模式
      currentSelectedKeys: [] // 用于存储当前选中的keys
    }
  },
  watch: {
    keyword(val) {
      this.resetResourceFilter()
      this.$refs.resourceTree.filter(val)
    },
    selectedResourceIds(newVal) {
      // 当selectedResourceIds变化时，更新树的选中状态
      this.$nextTick(() => {
        if (this.$refs.resourceTree) {
          // 先存储当前值，避免模式切换时丢失
          this.currentSelectedKeys = newVal || []
          this.$refs.resourceTree.setCheckedKeys(this.currentSelectedKeys)
        }
      })
    }
  },
  created() {
    this.getResources()
  },
  mounted() {
    this.$nextTick(() => {
      this.updateScrollBar()
      
      // 使用MutationObserver监听树结构DOM变化
      this.setupTreeObserver()
    })
    
    // 监听窗口大小变化，更新滚动条状态
    window.addEventListener('resize', this.updateScrollBar)
  },
  beforeDestroy() {
    // 移除事件监听，避免内存泄漏
    window.removeEventListener('resize', this.updateScrollBar)
    
    // 断开MutationObserver连接
    if (this.treeObserver) {
      this.treeObserver.disconnect()
    }
  },
  methods: {
    // 替换原来的handleCheckModeChange方法
    toggleCheckMode() {
      // 切换勾选模式
      this.cascadeMode = !this.cascadeMode;
      
      // 保存当前选中状态
      const currentCheckedKeys = this.$refs.resourceTree.getCheckedKeys();
      
      // 模式切换后，重新设置选中状态
      this.$nextTick(() => {
        this.$refs.resourceTree.setCheckedKeys(currentCheckedKeys);
        
        // 提示用户当前模式
        if (this.cascadeMode) {
          this.$message.info('已切换到联动勾选模式，勾选父节点将自动选中所有子节点');
        } else {
          this.$message.info('已切换到独立勾选模式，可以单独勾选节点');
        }
      });
    },

    // 获取资源树
    async getResources() {
      this.treeLoading = true
      try {
        const data = await getResourceTreeList()
        this.resourceTreeData = data || []
        this.$nextTick(() => {
          this.updateScrollBar()
        })
      } catch (error) {
        console.error('获取资源树失败:', error)
        this.$message.error('获取资源树失败')
      } finally {
        this.treeLoading = false
      }
    },

    // 获取选中的资源ID
    getSelectedResourceIds() {
      // 级联模式和独立模式需要不同处理
      if (this.cascadeMode) {
        // 级联模式下只需要获取当前被勾选的节点(包括了半选状态的节点)
        return this.$refs.resourceTree.getCheckedKeys(false)
      } else {
        // 独立模式下获取所有选中的节点
        return this.$refs.resourceTree.getCheckedKeys()
      }
    },

    // 过滤资源节点
    filterResourceNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase())
    },

    // 重置资源过滤
    resetResourceFilter() {
      if (this.$refs.resourceTree) {
        this.$refs.resourceTree.filter('')
      }
    },

    // 重置选中的节点
    resetCheckedNodes() {
      if (this.$refs.resourceTree) {
        this.$refs.resourceTree.setCheckedKeys([])
      }
    },

    // 判断节点是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false
      return node.label.toLowerCase().includes(keyword.toLowerCase())
    },

    // 获取资源类型标签样式
    getTypeTag(type) {
      const types = {
        APP: 'primary',
        MENU: 'success',
        BUTTON: 'warning'
      }
      return types[type] || 'info'
    },

    // 获取资源类型中文标签
    getTypeLabel(type) {
      return this.typeLabels[type] || type
    },

    // 全选
    handleCheckAll() {
      this.$refs.resourceTree.setCheckedNodes(this.getRecursiveNodes(this.resourceTreeData))
    },

    // 取消全选
    handleUncheckAll() {
      this.$refs.resourceTree.setCheckedKeys([])
    },

    // 展开所有
    handleExpandAll() {
      const nodes = this.getRecursiveNodes(this.resourceTreeData)
      nodes.forEach(node => {
        this.$refs.resourceTree.store.nodesMap[node.id].expanded = true
      })
      this.$nextTick(() => {
        this.updateScrollBar()
      })
    },

    // 收起所有
    handleCollapseAll() {
      const nodes = this.getRecursiveNodes(this.resourceTreeData)
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          this.$refs.resourceTree.store.nodesMap[node.id].expanded = false
        }
      })
      this.$nextTick(() => {
        this.updateScrollBar()
      })
    },

    // 更新滚动条状态
    updateScrollBar() {
      if (!this.$refs.resourceTree) return
      
      this.$nextTick(() => {
        const treeWrapper = this.$el.querySelector('.tree-wrapper')
        const treeEl = this.$refs.resourceTree.$el
        
        if (treeEl && treeWrapper) {
          // 获取实际内容高度和容器高度
          const contentHeight = treeEl.scrollHeight
          const containerHeight = treeWrapper.clientHeight
          
          console.log('内容高度:', contentHeight, '容器高度:', containerHeight)
          
          // 仅当内容高度大于容器高度时才显示滚动条
          if (contentHeight > containerHeight) {
            treeWrapper.style.overflowY = 'scroll' // 改为scroll，强制显示滚动条
            console.log('显示滚动条')
          } else {
            treeWrapper.style.overflowY = 'hidden'
            console.log('隐藏滚动条')
          }
        }
      })
    },

    // 递归获取所有节点
    getRecursiveNodes(nodes) {
      let allNodes = []
      
      const traverse = (nodeList) => {
        if (!nodeList || nodeList.length === 0) return
        
        nodeList.forEach(node => {
          allNodes.push(node)
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }
      
      traverse(nodes)
      return allNodes
    },

    // 下拉菜单命令处理
    handleCommand(command) {
      switch(command) {
        case 'checkAll':
          this.handleCheckAll()
          break
        case 'uncheckAll':
          this.handleUncheckAll()
          break
        case 'expandAll':
          this.handleExpandAll()
          break
        case 'collapseAll':
          this.handleCollapseAll()
          break
      }
    },

    // 设置树结构观察器，监听DOM变化
    setupTreeObserver() {
      if (!this.$refs.resourceTree) return
      
      // 创建MutationObserver实例
      this.treeObserver = new MutationObserver((mutations) => {
        // 当DOM变化时，更新滚动条
        this.updateScrollBar()
      })
      
      // 开始观察
      this.treeObserver.observe(this.$refs.resourceTree.$el, {
        childList: true,  // 监听子节点变化
        subtree: true,    // 监听所有后代节点变化
        attributes: true  // 监听属性变化
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-permission-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  
  .tree-actions-compact {
    display: flex;
    justify-content: space-between;
    padding: 0;
    margin-bottom: 8px;
    margin-top: 0;
    flex-shrink: 0; /* 防止按钮区域被压缩 */
    
    .actions-left, .actions-right {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    
    .actions-right {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    
    ::v-deep .el-button {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
      }
    }
  }
  
  .tree-wrapper {
    flex: 1;
    background-color: transparent;
    min-height: 0; /* 重要：允许flex子项收缩到小于内容高度 */
    position: relative;
    overflow-x: hidden;
    /* 初始状态设置为auto，确保基本的滚动能力 */
    overflow-y: auto;

    
    &::-webkit-scrollbar {
      width: 6px;
      height: 0;
    }
    
    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background: rgba(144, 147, 153, 0.3);
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
  
  ::v-deep .el-tree {
    background-color: transparent;
    height: 100%; /* 确保树填满容器 */
    min-height: 50px; /* 给树一个最小高度，避免完全收缩 */
    
    .el-tree-node__content {
      height: 40px;
      border-radius: 8px;
      margin: 4px 0;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 10px;
        
        .node-icon {
          height: 16px;
          width: 16px;
          color: #409EFF;
        }
        
        i {
          color: #409EFF;
          font-size: 16px;
        }
        
        .resource-type {
          margin-left: auto;
          border-radius: 10px;
          padding: 2px 8px;
          font-size: 12px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 20px;
          line-height: 1;
        }
      }
    }
    
    .is-current > .el-tree-node__content {
      background-color: #ecf5ff !important;
      color: #409EFF;
      font-weight: 500;
    }
  }
}

.highlight {
  color: #409EFF;
  font-weight: 600;
  position: relative;
  z-index: 1;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: rgba(64, 158, 255, 0.2);
    z-index: -1;
    border-radius: 3px;
  }
}
</style>
