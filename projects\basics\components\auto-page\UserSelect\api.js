import request from '@/utils/request'
let api = CONSTANT.SYSTEM

// 获取部门
export function getDeptList(params) {
    return request({
        url: api + `/sys/dept/list`,
        method: 'get',
        loadingDisabled: true,
        params: Object.assign({}, params, { size: -1 })
    })
}

// 获取账号
export function getAccountList(params) {
    return request({
        url: api + `/sys/account/list`,
        method: 'get',
        loadingDisabled: true,
        params: Object.assign({}, params, { size: -1 })
    })
}




