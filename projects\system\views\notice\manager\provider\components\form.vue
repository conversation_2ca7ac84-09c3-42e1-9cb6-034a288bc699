<template>
  <div class="provider-form-container">
    <el-page-header @back="$router.replace('/notice/provider')" :content="`${isEdit ? '编辑' : '添加'}消息供应商`">
    </el-page-header>

    <el-form ref="providerForm" :model="formData" :rules="rules" label-width="120px" v-loading="loading">
      <el-form-item label="供应商名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入供应商名称"></el-input>
      </el-form-item>
      <el-form-item label="供应商编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入供应商编码" :disabled="isEdit"></el-input>
      </el-form-item>
      <el-form-item label="绑定插件" prop="pluginBeanName">
        <el-select v-model="formData.pluginBeanName" placeholder="请选择插件" style="width: 100%">
          <el-option 
            v-for="plugin in plugins" 
            :key="plugin.pluginBeanName" 
            :label="plugin.name" 
            :value="plugin.pluginBeanName">
            <span>{{ plugin.name }}</span>
            <el-tag size="mini" type="success" class="plugin-type-tag">
              {{ plugin.type }}
            </el-tag>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="说明" prop="remark">
        <el-input type="textarea" v-model="formData.remark" placeholder="请输入说明"></el-input>
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-switch v-model="formData.enabled"></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="$router.push('/notice/provider')">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getProviderDetail, createProvider, updateProvider, getPlugins } from '@system/api/notice/manager'

export default {
  name: 'ProviderForm',
  data() {
    return {
      isEdit: false,
      loading: false,
      providerId: null,
      formData: {
        id: '',
        name: '',
        code: '',
        pluginBeanName: '',
        remark: '',
        enabled: true
      },
      plugins: [],
      rules: {
        name: [
          { required: true, message: '请输入供应商名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入供应商编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        pluginBeanName: [
          { required: true, message: '请选择插件', trigger: 'change' }
        ]
      }
    }
  },
  async created() {
    this.isEdit = !!this.$route.params.id
    this.providerId = this.$route.params.id
    
    await this.loadPlugins()
    
    if (this.isEdit) {
      await this.getProviderDetail()
    }
  },
  methods: {
    async loadPlugins() {
      this.loading = true
      const plugins = await getPlugins()
      this.plugins = plugins.filter(plugin => plugin.type === '通知插件')
      this.loading = false
    },
    async getProviderDetail() {
      this.loading = true
      const data = await getProviderDetail(this.providerId)
      this.formData = data
      this.loading = false
    },
    async handleSubmit() {
      const valid = await this.$refs.providerForm.validate()
      if (valid) {
        const method = this.isEdit ? updateProvider : createProvider
        await method(this.formData)
        this.$message.success(`${this.isEdit ? '更新' : '添加'}成功`)
        this.$router.push('/notice/provider')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.provider-form-container {
  margin: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;

  // 表单布局优化
  ::v-deep .el-form {
    max-width: 800px;
    margin: 0 auto;

    .el-form-item {
      margin-bottom: 24px;

      &__label {
        font-weight: 500;
        padding-right: 24px;
      }

      &__content {
        .el-input,
        .el-select {
          width: 100%;
        }
      }
    }
  }

  // 按钮样式
  ::v-deep .el-button {
    padding: 10px 24px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s;

    &--primary {
      background: #409EFF;
      border-color: #409EFF;

      &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
        transform: translateY(-1px);
      }
    }
  }

  .plugin-type-tag {
    float: right;
    margin-left: 10px;
  }
}
</style> 