<template>
  <div class="file-list">
    <!-- 工具栏 -->
    <div class="file-list-header">
      <div class="filter-area">
        <el-input
          v-model="filters.fileName"
          placeholder="按文件名搜索"
          clearable
          prefix-icon="el-icon-search"
          size="small"
          @input="handleFilter"
          style="width: 200px; margin-right: 10px;"
        />
        <el-select
          v-model="filters.fileType"
          placeholder="文件类型"
          clearable
          size="small"
          @change="handleFilter"
          style="width: 120px;"
        >
          <el-option
            v-for="type in fileTypes"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
      </div>
      <el-button 
        type="primary" 
        size="small"
        icon="el-icon-upload2"
        @click="$emit('upload')"
      >
        上传文件
      </el-button>
    </div>

    <!-- 文件列表表格 -->
    <el-table 
      :data="filteredFiles" 
      v-loading="loading"
      class="file-table"
    >
      <el-table-column prop="file_name" label="文件名" min-width="200">
        <template slot-scope="scope">
          <el-button type="text" @click="handleViewDetail(scope.row)">
            {{ scope.row.file_name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="file_ext" label="类型" width="80" />
      <el-table-column prop="docs_count" label="文档数" width="100" align="center" />
      <el-table-column label="源文件" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.in_folder ? 'success' : 'warning'" size="small">
            {{ scope.row.in_folder ? '√' : '×' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="向量库" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.in_db ? 'success' : 'warning'" size="small">
            {{ scope.row.in_db ? '√' : '×' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" width="160">
        <template slot-scope="scope">
          {{ formatDate(scope.row.create_time) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template slot-scope="scope">
          <file-actions 
            :file="scope.row"
            :kb-name="kbName"
            @reload="$emit('reload')"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import FileActions from './FileActions.vue'
import { formatDate } from '../../utils/format'

export default {
  name: 'KnowledgeFileList',
  components: {
    FileActions
  },
  props: {
    files: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    kbName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      filters: {
        fileName: '',
        fileType: ''
      },
      fileTypes: []
    }
  },
  computed: {
    filteredFiles() {
      return this.files.filter(file => {
        const fileNameMatch = !this.filters.fileName || 
          file.file_name.toLowerCase().includes(this.filters.fileName.toLowerCase());
        const fileTypeMatch = !this.filters.fileType || 
          file.file_ext === this.filters.fileType;
        return fileNameMatch && fileTypeMatch;
      });
    }
  },
  watch: {
    files: {
      immediate: true,
      handler(newFiles) {
        // 更新文件类型列表
        this.fileTypes = [...new Set(newFiles.map(file => file.file_ext))].filter(Boolean);
      }
    }
  },
  methods: {
    formatDate,
    handleViewDetail(file) {
      this.$emit('view-detail', file)
    },
    handleFilter() {
      // 可以添加防抖处理
      this.$emit('filter', this.filters)
    }
  }
}
</script>

<style scoped>
.file-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-table {
  margin-top: 12px;
}
</style> 