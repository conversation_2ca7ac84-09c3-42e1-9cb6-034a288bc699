import Vue from 'vue'
Vue.config.productionTip = false
// import App from './App.vue'
// import router from "./router";
// import store from './store'
import { Icon } from '@iconify/vue2'
require('@bpm-oa-web/utils/Injection')

/*import tinymce from 'tinymce'
Vue.prototype.$tinymce = tinymce*/

import 'vant/lib/index.css';

import '@bpm-oa-web/assets/theme/index.css'
import "@bpm-oa-web/assets/global.css";
import "@bpm-oa-web/assets/iconfont/iconfont.css"


import ElementUI from "element-ui";
import 'element-ui/lib/theme-chalk/display.css';
import "element-ui/lib/theme-chalk/index.css";
Vue.use(ElementUI);


import Ellipsis from '@bpm-oa-web/components/common/Ellipsis'
import WDialog from '@bpm-oa-web/components/common/WDialog'
import Tip from '@bpm-oa-web/components/common/Tip'
import Avatar from '@bpm-oa-web/components/common/Avatar'
Vue.use(Ellipsis);
Vue.use(WDialog);
Vue.use(Tip);
Vue.use(Avatar);
//注册iconify图标库
Vue.component('iconify', Icon);


import redirect from '@/views/redirect.vue'
import './root.scss'

import { vue } from '@/main.js'

const viewport = {
  content: "width=device-width, initial-scale=1.0, user-scalable=no"
}

vue.$router.beforeEach((to, from, next) => {
  vue.$store.state.bpm.loginUser = JSON.parse(localStorage.getItem('bpm-loginUser') || '{}')
  next()
})

vue.$router.moduleRoutes([
  {
    path: '/',
    component: redirect
  },
  {
    path: "/mbinitiate",
    name: "mbinitiate",
    component: () => import("@bpm-oa-web/views/workspace/MbInitiateProcess.vue"),
    meta: { title: 'bpm-ui| 发起审批', viewport: viewport }
  },
  {
    path: "/mbInstance",
    name: "mbInstance",
    component: () => import("@bpm-oa-web/views/workspace/MbInstanceViewer.vue"),
    meta: { title: 'bpm-ui| 流程详情', viewport: viewport }
  },
  {
    path: "/admin/design",
    name: "design",
    component: () => import("@bpm-oa-web/views/admin/FormProcessDesign.vue"),
    meta: { title: 'bpm-ui| 表单流程设计', viewport: viewport }
  }, {
    path: "/admin/design/subProcess",
    name: "subProcess",
    component: () => import("@bpm-oa-web/views/admin/subProcess/SubProcessDesign.vue"),
    meta: { title: 'bpm-ui| 子流程设计', viewport: viewport }
  },
  {
    path: "/testForm",
    name: "testForm",
    component: () => import("@bpm-oa-web/views/common/form/TestForm.vue"),
    meta: { title: 'bpm-ui| 表单演示', viewport: viewport }
  }
])