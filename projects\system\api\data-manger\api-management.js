import request from '@/utils/request'

const api = CONSTANT.META

// 获取API详情
export function getApiDetail(id) {
  return request({
    url: api + `/api`,
    method: 'get',
    params: { id }
  })
}

// 创建API
export function createApi(data) {
  return request({
    url: api + '/api',
    method: 'post',
    data
  })
}

// 更新API
export function updateApi(data) {
  return request({
    url: api + '/api',
    method: 'put',
    data
  })
}

// 删除API
export function deleteApi(id) {
  return request({
    url: api +`/api/${id}`,
    method: 'delete'
  })
}



