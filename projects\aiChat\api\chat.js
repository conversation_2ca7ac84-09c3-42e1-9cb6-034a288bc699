import request from '../utils/request'

const API = 'ai'
// const API = 'http://192.168.1.189:8888'

export default {
  // 获取模型列表
  async getModels() {
    const response = await request.get(`${API}/v1/models`)
    return response.data || []
  },

  // 发送消息（使用 OpenAI 标准接口）
  async sendMessage(params, onData, onError, onComplete) {
    try {
      await request({
        url: `${API}/v1/chat/completions`,
        method: 'POST',
        headers: {
          'Accept': 'text/event-stream',
        },
        data: {
          ...params,
          temperature: 0.7,
          max_tokens: 2000,
        },
        timeout: 60 * 1000,
        responseType: 'text',
        transformResponse: [data => data], // 防止自动 JSON 解析
        onDownloadProgress: ({ event }) => {
          const chunk = event.target.responseText
          if (!chunk) return

          // 获取新增的内容
          const lines = chunk.split('\n')
          var content = ''
          for (const line of lines) {
            if (!line.trim()) continue
            
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              if (data === '[DONE]') {
                break
              }
              try {
                const parsed = JSON.parse(data)
                var choice = parsed.choices?.[0]
                if ('stop' === choice?.finish_reason) {
                  break
                }
                content += choice?.delta?.content
              } catch (e) {
                console.warn('解析响应数据失败：', e, 'Raw data:', data)
              }
            }
          }
          console.log(event);
          
          content.length && onData(content)
        }
      })

      onComplete && onComplete()
    } catch (error) {
      console.error('API 调用失败：', error)
      onError && onError(error)
      throw error
    }
  }
} 