module.exports = {
    '/dev-api/': {
      target: 'http://192.168.1.135:8000',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/': ''
      }
    },
    '/dev-api/system': {
      target: 'http://192.168.1.135:8001',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/system': ''
      }
    },
    '/dev-api/file': {
      target: 'http://192.168.1.201:8002',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/file': ''
      }
    },
    '/dev-api/device': {
      target: 'http://192.168.1.135:8003',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/device': ''
      }
    },
    '/dev-api/notice': {
      target: 'http://192.168.1.135:8004',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/notice': ''
      }
    },
    '/dev-api/form': {
      target: 'http://192.168.1.135:8005',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/form': ''
      }
    },
    '/dev-api/meta': {
      target: 'http://192.168.1.135:8006',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/meta': ''
      }
    },
    '/dev-api/integration': {
      target: 'http://192.168.1.135:8007',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/integration': ''
      }
    },
    '/dev-api/scheduled': {
      target: 'http://192.168.1.135:8008',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/scheduled': ''
      }
    },
    '/dev-api/admin': {
      target: 'http://192.168.1.135:8009',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/admin': ''
      }
    },
    '/dev-api/oa': {
      target: 'http://192.168.1.135:8010',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/oa': ''
      }
    },
    '/dev-api/auth': {
      target: 'http://192.168.1.135:8011',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/auth': ''
      }
    },
    '/dev-api/project': {
      target: 'http://192.168.1.135:8012',
      changeOrigin: false,
      pathRewrite: {
        '^/dev-api/project': ''
      }
    }
  }