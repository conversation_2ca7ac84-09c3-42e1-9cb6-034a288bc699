import request from '@/utils/request'

const api = '/es'

// 查询日志数据
export function queryLogs(params) {
  const { startTime, endTime, appNames, traceId, pageSize = 100, from = 0 } = params
  
  // 构建查询条件
  const must = []
  
  // 添加时间范围
  if (startTime && endTime) {
    must.push({
      range: {
        '@timestamp': {
          gte: startTime,
          lte: endTime
        }
      }
    })
  }

  // 添加应用名称过滤
  if (appNames && appNames.length > 0) {
    must.push({
      terms: {
        'app.keyword': appNames
      }
    })
  }

  // 添加 traceId 过滤
  if (traceId) {
    must.push({
      term: {
        'tid.keyword': traceId
      }
    })
  }

  const query = {
    query: {
      bool: {
        must
      }
    },
    sort: [
      {
        '@timestamp': {
          order: 'desc'
        }
      }
    ],
    size: pageSize,
    from: from
  }

  return request({
    url: `${api}/extreme-log-*/_search`,
    method: 'post',
    data: query,
    loadingDisabled: true
  })
}

// 获取所有应用列表
export function getAppList() {
  const query = {
    size: 0,
    aggs: {
      apps: {
        terms: {
          field: 'app.keyword',
          size: 100
        }
      }
    }
  }

  return request({
    url: `${api}/extreme-log-*/_search`,
    method: 'post',
    data: query,
    hideExceptionPrompt: true
  })
} 