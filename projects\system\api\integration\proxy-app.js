import request from '@/utils/request'

const integrationProxyApi = CONSTANT.INTEGRATION + '/integration/proxy/app'

/**
 * 更新或新增认证配置
 * @param {Object} data 认证配置数据，包含proxyAppConfig和authRequests
 * @returns {Promise}
 */
export function updateProxyAuthConfig(data) {
  return request({
    url: `${integrationProxyApi}/auth/config`,
    method: 'post',
    data
  })
}

/**
 * 获取代理应用认证配置
 * @param {string} appId 应用ID
 * @returns {Promise} 返回认证配置数据，包含proxyAppConfig和authRequests
 */
export function getProxyAuthConfig(appId) {
  return request({
    url: `${integrationProxyApi}/auth/config/${appId}`,
    method: 'get'
  })
}

/**
 * 测试认证链执行
 * @param {Object} data 测试数据，包含baseUrl和authRequests
 * @returns {Promise} 返回认证链执行结果
 */
export function testAuthChainExecution(data) {
  return request({
    url: `${integrationProxy<PERSON><PERSON>}/test/chain/exec`,
    method: 'post',
    data
  })
}

/**
 * 测试Token提取
 * @param {Object} data 测试数据，包含baseUrl、authRequests和authExpression
 * @returns {Promise} 返回Token提取结果
 */
export function testTokenExtraction(data) {
  return request({
    url: `${integrationProxyApi}/test/token/extract`,
    method: 'post',
    data
  })
}

/**
 * 获取认证执行器列表
 * @returns {Promise} 返回认证执行器列表
 */
export function getAuthActuators() {
  return request({
    url: `${integrationProxyApi}/auth/actuators`,
    method: 'get'
  })
}

/**
 * 测试网络连接
 * @param {string} url 要测试的URL地址
 * @returns {Promise} 返回连接测试结果
 */
export function testNetworkConnectivity(url) {
  return request({
    url: `${integrationProxyApi}/test/network/connectivity`,
    method: 'get',
    params: { url }
  })
}

// ==================== API 请求管理相关接口 ====================

/**
 * 获取 API 请求列表
 * @param {Object} params 查询参数
 * @param {string} params.accessType 访问类型：WHITELIST(白名单) 或 BLACKLIST(黑名单)
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @param {string} params.integrationAppId 集成应用ID（可选）
 * @returns {Promise} 返回 API 请求列表
 */
export function getApiRequestList(params) {
  return request({
    url: `${CONSTANT.INTEGRATION}/integration/http/request/list`,
    method: 'get',
    params
  })
}

/**
 * 批量新增 API 请求
 * @param {Array} data API 请求数据数组
 * @returns {Promise} 返回新增结果
 */
export function createApiRequests(data) {
  return request({
    url: `${CONSTANT.INTEGRATION}/integration/http/request`,
    method: 'post',
    data: Array.isArray(data) ? data : [data]
  })
}

/**
 * 批量更新 API 请求
 * @param {Array} data API 请求数据数组
 * @returns {Promise} 返回更新结果
 */
export function updateApiRequests(data) {
  return request({
    url: `${CONSTANT.INTEGRATION}/integration/http/request`,
    method: 'put',
    data: Array.isArray(data) ? data : [data]
  })
}

/**
 * 批量删除 API 请求
 * @param {Array|Set} ids 要删除的 ID 集合
 * @returns {Promise} 返回删除结果
 */
export function deleteApiRequests(ids) {
  const idArray = Array.isArray(ids) ? ids : Array.from(ids)
  return request({
    url: `${CONSTANT.INTEGRATION}/integration/http/request/${idArray.join(',')}`,
    method: 'delete'
  })
}

// ==================== 连接器管理相关接口 ====================

/**
 * 获取连接器列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小，-1为全部
 * @returns {Promise} 返回连接器列表
 */
export function getConnectorList(params) {
  return request({
    url: `${CONSTANT.INTEGRATION}/integration/connector/list`,
    method: 'get',
    params
  })
}

/**
 * 选择/更新应用关联的连接器
 * @param {Object} data 连接器选择数据
 * @param {string} data.appId 应用ID
 * @param {Array} data.connectorIds 连接器ID数组
 * @returns {Promise} 返回操作结果
 */
export function updateAppConnectors(data) {
  return request({
    url: `${CONSTANT.INTEGRATION}/integration/app/use/connectors`,
    method: 'post',
    data
  })
}

/**
 * 获取应用关联的连接器ID列表
 * @param {string} appId 应用ID
 * @returns {Promise} 返回连接器ID数组
 */
export function getAppConnectors(appId) {
  return request({
    url: `${CONSTANT.INTEGRATION}/integration/app/${appId}/connectors`,
    method: 'get'
  })
}
