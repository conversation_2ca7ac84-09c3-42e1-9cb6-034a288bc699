<template>
  <div class="flink-app-management">
    <!-- 左侧应用列表 -->
    <div class="app-list" :class="{'is-collapsed': isAppListCollapsed}">
      <!-- 收起状态下的提示文字 -->
      <div class="collapse-hint" v-if="isAppListCollapsed" @click="toggleAppList">
        <span>点击展开应用列表</span>
      </div>
      <div class="list-header" v-show="!isAppListCollapsed">
        <div class="header-title">
          <i class="el-icon-share title-icon"></i>
          <span>应用列表</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="handleCreateApp" class="add-btn">
              <i class="el-icon-plus"></i>
            </el-button>
          </div>
        </div>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索应用名称"
            prefix-icon="el-icon-search"
            clearable
            size="small"
            @clear="handleSearchClear"
          />
        </div>
      </div>
      <div class="tree-container" v-show="!isAppListCollapsed">
        <el-tree
          ref="appTree"
          :data="treeData"
          :props="defaultProps"
          node-key="id"
          :filter-node-method="filterNode"
          highlight-current
          :default-expanded-keys="expandedNodes"
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <!-- 应用节点 -->
            <template v-if="data.type === 'app'">
              <div class="node-content">
                <i class="el-icon-share"></i>
                <span>{{ data.name }}</span>
              </div>
              <div class="node-actions" @click.stop>
                <el-button type="primary" size="mini" circle @click="handleEditApp(data)" title="编辑应用">
                  <i class="el-icon-edit"></i>
                </el-button>
                <el-button type="danger" size="mini" circle @click="handleDeleteApp(data)" title="删除应用">
                  <i class="el-icon-delete"></i>
                </el-button>
              </div>
            </template>
            <!-- 映射节点 -->
            <template v-else>
              <div class="node-content">
                <i class="el-icon-connection"></i>
                <span>{{ data.sourceTable }} → {{ data.sinkTable }}</span>
              </div>
            </template>
          </span>
        </el-tree>
      </div>
      <!-- 折叠按钮 -->
      <div class="collapse-btn" @click.stop="toggleAppList" :title="isAppListCollapsed ? '展开应用列表' : '收起应用列表'">
        <i :class="isAppListCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"></i>
      </div>
    </div>

    <!-- 右侧映射配置 -->
    <div class="mapping-detail" v-if="currentMapping" v-loading="loading">
      <div class="detail-content">
        <!-- 映射信息 -->
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-connection"></i>
            <span>{{ currentMapping.sourceTable }} → {{ currentMapping.sinkTable }}</span>
            <div class="info-actions">
              <el-button 
                type="primary"
                size="small"
                class="action-btn"
                @click="handleAddMapping"
                title="添加下一条映射"
              >
                <i class="el-icon-plus icon-white"></i> 添加下一条
              </el-button>
              <el-button 
                type="danger"
                size="small"
                class="action-btn"
                @click="handleDeleteMapping(currentMappingIndex)"
                title="删除当前映射"
              >
                <i class="el-icon-delete icon-white"></i> 删除映射
              </el-button>
            </div>
          </div>
          <div class="subtitle">
            <span class="info-item">
              <label>源表ID:</label> {{ currentMapping.sourceTableId }}
            </span>
            <span class="info-item">
              <label>目标表ID:</label> {{ currentMapping.sinkTableId }}
            </span>
          </div>
        </div>

        <!-- 映射详情 -->
        <div class="info-section mapping-config">
          <div class="section-title">
            <i class="el-icon-setting"></i>
            <span>映射配置</span>
          </div>

          <div class="mapping-content">
            <div class="mapping-row">
              <!-- 源表选择 -->
              <div class="source-tables">
                <div class="column-header">
                  <span>源表</span>
                  <el-button type="text" size="mini" @click="addSourceTable(currentMapping)">
                    <i class="el-icon-plus"></i> 添加源表
                  </el-button>
                </div>
                <div class="tables-container">
                  <div v-for="(sourceTable, tableIndex) in currentMapping.sourceTables || []" :key="tableIndex" class="table-item">
                    <div class="table-header" @click="toggleSourceTableFields(sourceTable)">
                      <i :class="['el-icon-arrow-right', {'is-expanded': sourceTable.expanded}]"></i>
                      <el-select 
                        v-model="sourceTable.id" 
                        size="small"
                        placeholder="请选择源表"
                        style="width: calc(100% - 36px)"
                        @change="(val) => handleSourceTableChange(val, sourceTable, currentMapping)"
                      >
                        <el-option
                          v-for="table in sourceTables"
                          :key="table.id"
                          :label="table.name"
                          :value="table.id"
                        >
                          <span style="float: left">{{ table.name }}</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">
                            {{ table.comment }}
                          </span>
                        </el-option>
                      </el-select>
                      <span 
                        v-if="sourceTable.id" 
                        class="table-id" 
                        @dblclick.stop="copyToClipboard(sourceTable.id)"
                        title="双击复制表ID"
                      >
                        ({{ sourceTable.id }})
                      </span>
                      <el-button 
                        type="text" 
                        size="mini" 
                        class="delete-btn"
                        @click.stop="removeSourceTable(currentMapping, tableIndex)"
                        v-if="(currentMapping.sourceTables || []).length > 1"
                      >
                        <i class="el-icon-delete"></i>
                      </el-button>
                    </div>
                    
                    <!-- 表字段列表 -->
                    <div class="table-fields" v-show="sourceTable.expanded">
                      <div class="field-search">
                        <el-input
                          v-model="sourceFieldSearch"
                          size="small"
                          placeholder="搜索字段"
                          prefix-icon="el-icon-search"
                          clearable
                        />
                      </div>
                      <div class="field-list">
                        <div 
                          v-for="field in filterFields(sourceTable.fields || [], sourceFieldSearch)" 
                          :key="field.name" 
                          class="field-item"
                        >
                          <span class="field-name">{{ field.name }}</span>
                          <span class="field-type">{{ field.type }}</span>
                          <el-tooltip 
                            v-if="field.comment"
                            :content="field.comment" 
                            placement="top" 
                            effect="light"
                          >
                            <i class="el-icon-info field-comment"></i>
                          </el-tooltip>
                        </div>
                        <div v-if="!(sourceTable.fields || []).length" class="empty-fields">
                          <i class="el-icon-info"></i> 暂无字段信息
                        </div>
                        <div v-else-if="filterFields(sourceTable.fields || [], sourceFieldSearch).length === 0" class="empty-fields">
                          <i class="el-icon-info"></i> 无匹配字段
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- SQL编辑器 -->
              <div class="sql-editor-container">
                <div class="editor-title">
                  <span>数据转换 SQL</span>
                  <div class="editor-actions">
                    <el-button size="small" @click="validateSQL" :loading="validating">
                      <i class="el-icon-check"></i> 校验
                    </el-button>
                    <el-button size="small" @click="saveSQL" :loading="savingSQL" type="primary">
                      <i class="el-icon-upload"></i> 保存
                    </el-button>
                    <el-button size="small" @click="clearSQL" type="danger">
                      <i class="el-icon-delete"></i> 清空
                    </el-button>
                    <el-button size="small" @click="toggleFullscreen">
                      <i :class="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
                      {{ isFullscreen ? '退出全屏' : '全屏编辑' }}
                    </el-button>
                  </div>
                </div>
                <div class="editor-wrapper">
                  <div :id="`editor-container-${currentMappingIndex}`" class="code-editor"></div>
                </div>
              </div>

              <!-- 目标表选择 -->
              <div class="sink-table">
                <div class="column-header">
                  <span>目标表</span>
                </div>
                <div class="table-container">
                  <div class="table-header">
                    <el-select 
                      v-model="currentMapping.sinkTableId" 
                      placeholder="请选择目标表"
                      size="small"
                      style="width: calc(100% - 60px)"
                      @change="(val) => handleSinkTableChange(val, currentMapping)"
                    >
                      <el-option
                        v-for="table in sinkTables"
                        :key="table.id"
                        :label="table.name"
                        :value="table.id"
                      >
                        <span style="float: left">{{ table.name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">
                          {{ table.comment }}
                        </span>
                      </el-option>
                    </el-select>
                    <span 
                      v-if="currentMapping.sinkTableId" 
                      class="table-id" 
                      @dblclick.stop="copyToClipboard(currentMapping.sinkTableId)"
                      title="双击复制表ID"
                    >
                      ({{ currentMapping.sinkTableId }})
                    </span>
                  </div>
                  
                  <!-- 目标表字段列表 -->
                  <div class="table-fields">
                    <div class="field-search">
                      <el-input
                        v-model="sinkFieldSearch"
                        size="small"
                        placeholder="搜索字段"
                        prefix-icon="el-icon-search"
                        clearable
                      />
                    </div>
                    <div class="field-list">
                      <div 
                        v-for="field in filterFields(currentMapping.sinkTableFields || [], sinkFieldSearch)" 
                        :key="field.name" 
                        class="field-item"
                      >
                        <span class="field-name">{{ field.name }}</span>
                        <span class="field-type">{{ field.type }}</span>
                        <el-tooltip 
                          v-if="field.comment"
                          :content="field.comment" 
                          placement="top" 
                          effect="light"
                        >
                          <i class="el-icon-info field-comment"></i>
                        </el-tooltip>
                      </div>
                      <div v-if="!(currentMapping.sinkTableFields || []).length" class="empty-fields">
                        <i class="el-icon-info"></i> 暂无字段信息
                      </div>
                      <div v-else-if="filterFields(currentMapping.sinkTableFields || [], sinkFieldSearch).length === 0" class="empty-fields">
                        <i class="el-icon-info"></i> 无匹配字段
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用配置页 -->
    <div class="mapping-detail" v-else-if="currentApp && !currentMapping" v-loading="loading">
      <div class="detail-content">
        <!-- 应用信息 -->
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-share"></i>
            <span>{{ currentApp.appName }}</span>
            <el-button 
              type="primary"
              size="small"
              class="action-btn primary-action-btn"
              @click="handleAddMapping"
              style="margin-left: auto;"
            >
              <i class="el-icon-plus icon-white"></i> 新增映射
            </el-button>
          </div>
          <div class="subtitle">
            <span class="info-item">
              <label>数据库:</label> {{ currentApp.databaseName }}
            </span>
            <span class="info-item">
              <label>创建时间:</label> {{ currentApp.createTime }}
            </span>
          </div>
        </div>

        <!-- 映射列表 -->
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-connection"></i>
            <span>数据映射</span>
            <span class="mapping-count" v-if="mappings.length">
              共 {{ mappings.length }} 个
            </span>
          </div>

          <el-table
            :data="paginatedMappings"
            style="width: 100%"
            border
            stripe
            highlight-current-row
            height="calc(100% - 110px)"
            class="custom-table"
          >
            <el-table-column prop="sourceTable" label="源表" min-width="120">
              <template slot-scope="{ row }">
                <div class="table-info">
                  <i class="el-icon-tickets"></i>
                  <span>{{ row.sourceTable }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="sinkTable" label="目标表" min-width="120">
              <template slot-scope="{ row }">
                <div class="table-info">
                  <i class="el-icon-tickets"></i>
                  <span>{{ row.sinkTable }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="operationKey" label="操作键" width="100" align="center" />
            <el-table-column label="操作" width="180" align="center" class-name="operation-column">
              <template slot-scope="{ row, $index }">
                <el-button
                  type="text"
                  size="mini"
                  class="view-btn"
                  @click="viewMappingDetail(row, $index)"
                >
                  <i class="el-icon-view"></i> 查看
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  class="delete-btn"
                  @click="handleDeleteMapping($index)"
                >
                  <i class="el-icon-delete"></i> 删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container" v-if="mappings.length > 0">
            <el-pagination
              @current-change="handleTableCurrentChange"
              :current-page.sync="tablePage"
              :page-size="tablePageSize"
              layout="total, prev, pager, next"
              :total="mappings.length"
              background
            >
            </el-pagination>
          </div>
          
          <div class="empty-tip" v-if="mappings.length === 0">
            <i class="el-icon-info"></i>
            <span>暂无映射关系，请点击"新增映射"按钮创建</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <i class="el-icon-share"></i>
      <p>请选择左侧应用或创建新应用</p>
    </div>

    <!-- 新建应用对话框 -->
    <el-dialog
      title="新建 Flink 应用"
      :visible.sync="createAppDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
      class="app-detail-dialog"
    >
      <el-form :model="appForm" ref="appForm" label-width="100px" :rules="appFormRules">
        <el-form-item label="应用名称" prop="appName">
          <el-input v-model="appForm.appName" placeholder="请输入应用名称"></el-input>
        </el-form-item>
        <el-form-item label="所属数据库" prop="databaseId">
          <el-select v-model="appForm.databaseId" placeholder="请选择数据库" style="width: 100%">
            <el-option
              v-for="db in databases"
              :key="db.id"
              :label="db.name"
              :value="db.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button class="cancel-btn" @click="createAppDialogVisible = false">取 消</el-button>
        <el-button 
          type="primary" 
          class="primary-action-btn" 
          @click="submitCreateApp" 
          :loading="creating"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 应用编辑对话框 -->
    <el-dialog
      title="编辑应用"
      :visible.sync="editAppDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
      class="app-detail-dialog"
    >
      <el-form :model="appForm" ref="editAppForm" label-width="100px" :rules="appFormRules">
        <el-form-item label="应用名称" prop="appName">
          <el-input v-model="appForm.appName" placeholder="请输入应用名称"></el-input>
        </el-form-item>
        <el-form-item label="所属数据库" prop="databaseId">
          <el-select v-model="appForm.databaseId" placeholder="请选择数据库" style="width: 100%">
            <el-option
              v-for="db in databases"
              :key="db.id"
              :label="db.name"
              :value="db.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button class="cancel-btn" @click="editAppDialogVisible = false">取 消</el-button>
        <el-button 
          type="primary" 
          class="primary-action-btn" 
          @click="submitEditApp" 
          :loading="updating"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 全屏编辑器 -->
    <div class="fullscreen-mask" v-if="isFullscreen">
      <div class="fullscreen-editor">
        <div class="fullscreen-header">
          <div class="title">SQL全屏编辑</div>
          <el-button size="small" @click="toggleFullscreen" class="close-button">
            <i class="el-icon-close"></i> 退出全屏
          </el-button>
        </div>
        <div class="fullscreen-content">
          <div ref="fullscreenEditor" class="code-editor"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as monaco from 'monaco-editor'
import { getFlinkAppList, getSinkTableList, createFlinkApp, updateFlinkApp, deleteFlinkApp } from '@system/api/data-manger/flink-app'

export default {
  name: 'FlinkAppManagement',
  components: {
  },
  data() {
    return {
      searchKeyword: '',
      loading: false,
      saving: false,
      creating: false,
      updating: false,
      currentApp: null,
      currentMapping: null,
      currentMappingIndex: -1,
      isAppListCollapsed: localStorage.getItem('flinkAppListCollapsed') === 'true',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      apps: [],
      mappings: [],
      activeMapping: [],
      sourceTables: [
        {
          id: 1,
          name: 'user_info',
          comment: '用户基本信息表'
        },
        {
          id: 2,
          name: 'user_address',
          comment: '用户地址表'
        },
        {
          id: 3,
          name: 'order_main',
          comment: '订单主表'
        },
        {
          id: 4,
          name: 'order_detail',
          comment: '订单详情表'
        },
        {
          id: 5,
          name: 'product_info',
          comment: '商品信息表'
        }
      ],
      sinkTables: [
        {
          id: 101,
          name: 'ods_user_info',
          comment: '用户信息ODS层'
        },
        {
          id: 102,
          name: 'ods_user_address',
          comment: '用户地址ODS层'
        },
        {
          id: 103,
          name: 'ods_order_main',
          comment: '订单主表ODS层'
        },
        {
          id: 104,
          name: 'ods_order_detail',
          comment: '订单详情ODS层'
        },
        {
          id: 105,
          name: 'ods_product_info',
          comment: '商品信息ODS层'
        }
      ],
      databases: [
        {
          id: 1,
          name: '用户订单库'
        },
        {
          id: 2,
          name: '商品库'
        }
      ],
      createAppDialogVisible: false,
      editAppDialogVisible: false,
      appForm: {
        id: null,
        appName: '',
        databaseId: null
      },
      appFormRules: {
        appName: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        databaseId: [
          { required: true, message: '请选择数据库', trigger: 'change' }
        ]
      },
      editor: null,
      editors: {}, // 存储所有编辑器实例的映射
      hasChanges: false,
      // 表字段模拟数据
      tableFields: {
        // 源表字段
        1: [
          { name: 'id', type: 'BIGINT', comment: '用户ID' },
          { name: 'username', type: 'VARCHAR(50)', comment: '用户名' },
          { name: 'email', type: 'VARCHAR(100)', comment: '邮箱' },
          { name: 'phone', type: 'VARCHAR(20)', comment: '手机号' },
          { name: 'create_time', type: 'DATETIME', comment: '创建时间' },
          { name: 'update_time', type: 'DATETIME', comment: '更新时间' }
        ],
        2: [
          { name: 'id', type: 'BIGINT', comment: '地址ID' },
          { name: 'user_id', type: 'BIGINT', comment: '用户ID' },
          { name: 'address', type: 'VARCHAR(200)', comment: '详细地址' },
          { name: 'city', type: 'VARCHAR(50)', comment: '城市' },
          { name: 'province', type: 'VARCHAR(50)', comment: '省份' },
          { name: 'is_default', type: 'TINYINT', comment: '是否默认地址' },
          { name: 'create_time', type: 'DATETIME', comment: '创建时间' },
          { name: 'update_time', type: 'DATETIME', comment: '更新时间' }
        ],
        3: [
          { name: 'id', type: 'BIGINT', comment: '订单ID' },
          { name: 'user_id', type: 'BIGINT', comment: '用户ID' },
          { name: 'order_no', type: 'VARCHAR(50)', comment: '订单编号' },
          { name: 'total_amount', type: 'DECIMAL(10,2)', comment: '订单金额' },
          { name: 'status', type: 'TINYINT', comment: '订单状态' },
          { name: 'create_time', type: 'DATETIME', comment: '创建时间' },
          { name: 'pay_time', type: 'DATETIME', comment: '支付时间' }
        ],
        4: [
          { name: 'id', type: 'BIGINT', comment: '详情ID' },
          { name: 'order_id', type: 'BIGINT', comment: '订单ID' },
          { name: 'product_id', type: 'BIGINT', comment: '商品ID' },
          { name: 'product_name', type: 'VARCHAR(100)', comment: '商品名称' },
          { name: 'quantity', type: 'INT', comment: '数量' },
          { name: 'price', type: 'DECIMAL(10,2)', comment: '单价' },
          { name: 'create_time', type: 'DATETIME', comment: '创建时间' }
        ],
        5: [
          { name: 'id', type: 'BIGINT', comment: '商品ID' },
          { name: 'name', type: 'VARCHAR(100)', comment: '商品名称' },
          { name: 'category_id', type: 'BIGINT', comment: '类目ID' },
          { name: 'price', type: 'DECIMAL(10,2)', comment: '价格' },
          { name: 'stock', type: 'INT', comment: '库存' },
          { name: 'status', type: 'TINYINT', comment: '状态' },
          { name: 'create_time', type: 'DATETIME', comment: '创建时间' },
          { name: 'update_time', type: 'DATETIME', comment: '更新时间' }
        ],
        // 目标表字段
        101: [
          { name: 'id', type: 'BIGINT', comment: '用户ID' },
          { name: 'username', type: 'VARCHAR(50)', comment: '用户名' },
          { name: 'email', type: 'VARCHAR(100)', comment: '邮箱' },
          { name: 'phone', type: 'VARCHAR(20)', comment: '手机号' },
          { name: 'created_at', type: 'TIMESTAMP', comment: '创建时间' },
          { name: 'updated_at', type: 'TIMESTAMP', comment: '更新时间' },
          { name: 'etl_time', type: 'TIMESTAMP', comment: 'ETL时间' }
        ],
        102: [
          { name: 'id', type: 'BIGINT', comment: '地址ID' },
          { name: 'user_id', type: 'BIGINT', comment: '用户ID' },
          { name: 'address', type: 'VARCHAR(200)', comment: '详细地址' },
          { name: 'city', type: 'VARCHAR(50)', comment: '城市' },
          { name: 'province', type: 'VARCHAR(50)', comment: '省份' },
          { name: 'is_default', type: 'INT', comment: '是否默认地址' },
          { name: 'created_at', type: 'TIMESTAMP', comment: '创建时间' },
          { name: 'updated_at', type: 'TIMESTAMP', comment: '更新时间' },
          { name: 'etl_time', type: 'TIMESTAMP', comment: 'ETL时间' }
        ],
        103: [
          { name: 'id', type: 'BIGINT', comment: '订单ID' },
          { name: 'user_id', type: 'BIGINT', comment: '用户ID' },
          { name: 'order_no', type: 'VARCHAR(50)', comment: '订单编号' },
          { name: 'total_amount', type: 'DECIMAL(10,2)', comment: '订单金额' },
          { name: 'status', type: 'INT', comment: '订单状态' },
          { name: 'created_at', type: 'TIMESTAMP', comment: '创建时间' },
          { name: 'pay_time', type: 'TIMESTAMP', comment: '支付时间' },
          { name: 'etl_time', type: 'TIMESTAMP', comment: 'ETL时间' }
        ],
        104: [
          { name: 'id', type: 'BIGINT', comment: '详情ID' },
          { name: 'order_id', type: 'BIGINT', comment: '订单ID' },
          { name: 'product_id', type: 'BIGINT', comment: '商品ID' },
          { name: 'product_name', type: 'VARCHAR(100)', comment: '商品名称' },
          { name: 'quantity', type: 'INT', comment: '数量' },
          { name: 'price', type: 'DECIMAL(10,2)', comment: '单价' },
          { name: 'created_at', type: 'TIMESTAMP', comment: '创建时间' },
          { name: 'etl_time', type: 'TIMESTAMP', comment: 'ETL时间' }
        ],
        105: [
          { name: 'id', type: 'BIGINT', comment: '商品ID' },
          { name: 'name', type: 'VARCHAR(100)', comment: '商品名称' },
          { name: 'category_id', type: 'BIGINT', comment: '类目ID' },
          { name: 'price', type: 'DECIMAL(10,2)', comment: '价格' },
          { name: 'stock', type: 'INT', comment: '库存' },
          { name: 'status', type: 'INT', comment: '状态' },
          { name: 'created_at', type: 'TIMESTAMP', comment: '创建时间' },
          { name: 'updated_at', type: 'TIMESTAMP', comment: '更新时间' },
          { name: 'etl_time', type: 'TIMESTAMP', comment: 'ETL时间' }
        ]
      },
      isFullscreen: false,
      fullscreenEditor: null,
      validating: false,
      savingSQL: false,
      sourceFieldSearch: '', // 源表字段搜索关键字
      sinkFieldSearch: '', // 目标表字段搜索关键字
      tablePage: 1,
      tablePageSize: 10,
      expandedNodes: [], // 用于存储展开的节点
      appMappingsLoaded: {}, // 跟踪每个应用的映射是否已加载
    }
  },
  computed: {
    treeData() {
      return this.apps.map(app => ({
        id: app.id,
        type: 'app',
        name: app.appName,
        databaseId: app.databaseId,
        databaseName: app.databaseName,
        createTime: app.createTime,
        children: this.mappings
          .filter(mapping => {
            // 不依赖于 currentApp，而是直接比较 mapping 中的 app ID
            return mapping.appId === app.id;
          })
          .map((mapping, index) => ({
            id: `${app.id}-mapping-${mapping.operationKey}`, // 修改ID生成方式，加入app.id前缀
            type: 'mapping',
            name: `${mapping.sourceTable} → ${mapping.sinkTable}`,
            sourceTable: mapping.sourceTable,
            sinkTable: mapping.sinkTable,
            index: index,
            parentId: app.id
          }))
      }))
    },
    filteredApps() {
      if (!this.searchKeyword) return this.apps
      const keyword = this.searchKeyword.toLowerCase()
      return this.apps.filter(app => 
        app.appName.toLowerCase().includes(keyword)
      )
    },
    paginatedMappings() {
      const start = (this.tablePage - 1) * this.tablePageSize;
      return this.mappings.slice(start, start + this.tablePageSize);
    }
  },
  created() {
    // 从localStorage读取已展开节点状态
    try {
      const savedExpandedNodes = localStorage.getItem('flinkAppExpandedNodes')
      if (savedExpandedNodes) {
        this.expandedNodes = JSON.parse(savedExpandedNodes)
      }
    } catch (e) {
      console.error('读取已展开节点状态失败:', e)
      this.expandedNodes = []
    }
    
    this.initData()
  },
  mounted() {
    this.initEditor()
  },
  beforeDestroy() {
    // 销毁所有编辑器实例
    Object.values(this.editors).forEach(editor => {
      if (editor) {
        editor.dispose();
      }
    });
    this.editors = {};
    
    if (this.fullscreenEditor) {
      this.fullscreenEditor.dispose();
      this.fullscreenEditor = null;
    }
  },
  watch: {
    activeMapping: {
      handler(newVal, oldVal) {
        // 当映射选择项变化时，重新初始化编辑器
        // 需要一点延迟确保DOM已更新
        this.$nextTick(() => {
          this.initEditor();
        });
      },
      deep: true
    },
    searchKeyword(val) {
      this.$refs.appTree && this.$refs.appTree.filter(val)
    },
    // 监听折叠状态并保存到localStorage
    isAppListCollapsed(val) {
      localStorage.setItem('flinkAppListCollapsed', val);
    },
    // 监听节点展开状态并保存到localStorage
    expandedNodes: {
      handler(val) {
        localStorage.setItem('flinkAppExpandedNodes', JSON.stringify(val));
      },
      deep: true
    }
  },
  methods: {
    async initData() {
      this.loading = true
      // 保存当前折叠状态
      const savedCollapsedState = this.isAppListCollapsed
      // 保存已加载应用状态
      const savedAppMappingsLoaded = {...this.appMappingsLoaded}
      // 保存已展开节点状态
      const savedExpandedNodes = [...this.expandedNodes]
      
      try {
        // 获取应用列表
        const appList = await getFlinkAppList({size: -1})
        if (appList && appList.records) {
          this.apps = appList.records.map(app => ({
            id: app.id,
            appName: app.name || `应用${app.id}`,
            databaseId: app.databaseId,
            databaseName: this.databases.find(db => db.id === app.databaseId)?.name,
            createTime: app.createTime
          }))
        }
        
        // 如果有当前选中的应用，重新加载其映射关系
        if (this.currentApp && savedAppMappingsLoaded[this.currentApp.id]) {
          await this.loadAppMappings(this.currentApp.id)
        }

      } catch (error) {
        this.$message.error('加载数据失败：' + error.message)
      } finally {
        this.loading = false
        // 恢复折叠状态
        this.isAppListCollapsed = savedCollapsedState
        // 恢复已加载应用状态
        this.appMappingsLoaded = savedAppMappingsLoaded
        // 恢复展开节点状态
        this.expandedNodes = savedExpandedNodes
      }
    },
    handleSearchClear() {
      this.searchKeyword = ''
    },
    handleCreateApp() {
      this.appForm = {
        appName: '',
        databaseId: null
      }
      this.createAppDialogVisible = true
    },
    async submitCreateApp() {
      this.$refs.appForm.validate(async valid => {
        if (valid) {
          this.creating = true
          try {
            const data = {
              name: this.appForm.appName,
              databaseId: this.appForm.databaseId
            }
            await createFlinkApp(data)
            this.createAppDialogVisible = false
            this.$message.success('创建成功')
            await this.initData()
          } catch (error) {
            this.$message.error('创建失败：' + error.message)
          } finally {
            this.creating = false
          }
        }
      })
    },
    handleAppSelect(app) {
      if (!app) return
      this.currentApp = app
      
      // 设置当前节点为选中状态
      this.$refs.appTree && this.$refs.appTree.setCurrentKey(app.id)
      
      // 确保当前应用节点展开
      if (!this.expandedNodes.includes(app.id)) {
        this.expandedNodes.push(app.id)
      }
      
      // 如果应用映射尚未加载，则加载
      if (!this.appMappingsLoaded[app.id]) {
        this.loadAppMappings(app.id)
        this.appMappingsLoaded[app.id] = true
      }
    },
    async loadAppMappings(appId) {
      this.loading = true
      const savedCollapsedState = this.isAppListCollapsed // 保存当前折叠状态
      try {
        // 获取应用的映射列表
        const response = await getSinkTableList({appId, size: -1})
        
        if (response && response.records) {
          // 保存当前映射，避免直接替换
          const currentMappings = this.mappings.filter(mapping => mapping.appId !== appId);
          
          // 将API返回的数据转换为组件需要的格式
          const newMappings = response.records.map(item => {
            // 解析源表和目标表名称
            // TODO 从transSql中解析
            const sourceTable = item.tableId
            const sinkTable = item.tableId
            
            return {
              id: item.id,
              sourceTableId: null, // 这些值需要通过其他API获取或者从transSql中解析
              sourceTable: sourceTable,
              sinkTableId: item.tableId,
              sinkTable: sinkTable,
              transSql: item.transSql || '',
              operationKey: item.operationKey,
              appId: item.appId,
              sourceTables: [
                {
                  id: null, // 后续可能需要从其他API获取
                  name: sourceTable,
                  expanded: false,
                  fields: [] // 字段信息需要从其他API获取
                }
              ],
              sinkTableFields: [] // 目标表字段信息需要从其他API获取
            }
          });
          
          // 合并映射
          this.mappings = [...currentMappings, ...newMappings];
        }
        
        // 如果有具体的映射，尝试获取它们的字段信息
        for (const mapping of this.mappings.filter(m => m.appId === appId)) {
          if (mapping.sinkTableId) {
            // 这里可以添加获取目标表字段的API调用
            mapping.sinkTableFields = this.tableFields[mapping.sinkTableId] || []
          }
          
          // 源表字段信息可能需要另一个API调用
          if (mapping.sourceTables && mapping.sourceTables.length > 0) {
            const sourceTable = mapping.sourceTables[0]
            if (sourceTable.id) {
              sourceTable.fields = this.tableFields[sourceTable.id] || []
            }
          }
        }
        
        // 确保当前应用节点默认展开 - 保持原有节点的展开状态
        if (!this.expandedNodes.includes(appId)) {
          this.expandedNodes.push(appId)
        }
        
        // 设置当前应用节点为高亮
        this.$nextTick(() => {
          if (this.$refs.appTree) {
            this.$refs.appTree.setCurrentKey(appId);
          }
        });
        
      } catch (error) {
        console.error('加载映射关系失败:', error)
        this.$message.error('加载映射关系失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
        this.isAppListCollapsed = savedCollapsedState // 确保即使出错也恢复折叠状态
      }
    },
    handleAddMapping() {
      const newIndex = this.mappings.length;
      const newMapping = {
        sourceTableId: null,
        sinkTableId: null,
        sourceTable: '',
        sinkTable: '',
        transSql: '-- 请在此输入SQL查询语句',
        operationKey: Date.now(),
        appId: this.currentApp?.id, // 添加 appId 属性
        sourceTables: [
          {
            id: null,
            name: '',
            expanded: false,
            fields: []
          }
        ],
        sinkTableFields: []
      };
      
      this.mappings.push(newMapping);
      
      // 设置当前映射为新添加的
      this.viewMappingDetail(newMapping, newIndex);
    },
    handleDeleteMapping(index) {
      this.$confirm('确认删除该映射关系？', '提示', {
        type: 'warning'
      }).then(() => {
        this.mappings.splice(index, 1)
        // 如果删除的是当前选中的映射，清除当前映射
        if (this.currentMappingIndex === index) {
          this.currentMapping = null
          this.currentMappingIndex = -1
        } else if (this.currentMappingIndex > index) {
          // 如果删除的是当前选中映射之前的映射，需要调整索引
          this.currentMappingIndex--
        }
        this.activeMapping = this.activeMapping.filter(i => i !== index)
      }).catch(() => {})
    },
    async handleSaveApp() {
      if (!this.validateMappings()) return
      
      this.saving = true
      try {
        // Mock保存应用配置
        setTimeout(() => {
          this.$message.success('保存成功')
          this.saving = false
          this.hasChanges = false
        }, 800)
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
      } finally {
        this.saving = false
      }
    },
    validateMappings() {
      let valid = true
      this.mappings.forEach((mapping, index) => {
        if (!mapping.sourceTableId) {
          this.$message.error(`映射 #${index + 1} 未选择源表`)
          valid = false
        }
        if (!mapping.sinkTableId) {
          this.$message.error(`映射 #${index + 1} 未选择目标表`)
          valid = false
        }
        if (!mapping.transSql.trim()) {
          this.$message.error(`映射 #${index + 1} 未填写转换SQL`)
          valid = false
        }
      })
      return valid
    },
    initEditor() {
      // 确保 DOM 已经渲染
      this.$nextTick(() => {
        // 如果当前没有选中映射，则不初始化编辑器
        if (this.currentMappingIndex === -1) return;
        
        const editorId = `editor-container-${this.currentMappingIndex}`;
        const container = document.getElementById(editorId);
        
        if (!container) {
          console.warn(`未找到编辑器容器: ${editorId}`);
          return;
        }

        // 获取当前选中的映射
        const currentMapping = this.mappings[this.currentMappingIndex];
        
        if (!currentMapping) return;
        
        // 如果该映射已经有编辑器实例，先销毁它
        if (this.editors[editorId]) {
          this.editors[editorId].dispose();
          delete this.editors[editorId];
        }

        // 创建新的编辑器实例
        this.editors[editorId] = monaco.editor.create(container, {
          value: currentMapping.transSql || '-- 请在此输入SQL查询语句',
          language: 'sql',
          theme: 'vs-dark',
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 14,
          tabSize: 2
        });

        // 添加监听
        this.editors[editorId].onDidChangeModelContent(() => {
          if (currentMapping) {
            currentMapping.transSql = this.editors[editorId].getValue();
            this.hasChanges = true;
          }
        });

        // 更新编辑器布局
        setTimeout(() => {
          if (this.editors[editorId]) {
            this.editors[editorId].layout();
          }
        }, 100);
      });
    },
    formatSQL() {
      if (this.editor) {
        this.editor.getAction('editor.action.formatDocument').run()
        this.$message.success('SQL格式化成功')
      }
    },
    async toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      
      if (this.isFullscreen) {
        this.$nextTick(() => {
          const container = this.$refs.fullscreenEditor;
          if (!container) return;
          
          // 获取当前选中的映射
          const activeIndex = this.activeMapping[0];
          if (activeIndex === undefined) return;
          
          const currentMapping = this.mappings[activeIndex];
          const editorId = `editor-container-${activeIndex}`;
          
          if (!currentMapping) return;
          
          // 获取当前编辑器内容
          const content = this.editors[editorId] ? this.editors[editorId].getValue() : currentMapping.transSql;
          
          // 创建全屏编辑器
          this.fullscreenEditor = monaco.editor.create(container, {
            value: content,
            language: 'sql',
            theme: 'vs-dark',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            lineNumbers: 'on',
            fontSize: 16,
            tabSize: 2
          });
          
          // 添加快捷键
          this.fullscreenEditor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
            const newContent = this.fullscreenEditor.getValue();
            const editorId = `editor-container-${activeIndex}`;
            
            if (this.editors[editorId]) {
              this.editors[editorId].setValue(newContent);
            }
            
            if (currentMapping) {
              currentMapping.transSql = newContent;
              this.hasChanges = true;
            }
            
            this.$message.success('内容已保存');
          });
        });
      } else {
        // 退出全屏模式，将全屏编辑器内容更新回主编辑器
        if (this.fullscreenEditor) {
          const activeIndex = this.activeMapping[0];
          if (activeIndex !== undefined) {
            const currentMapping = this.mappings[activeIndex];
            const editorId = `editor-container-${activeIndex}`;
            
            if (currentMapping) {
              const newContent = this.fullscreenEditor.getValue();
              currentMapping.transSql = newContent;
              
              if (this.editors[editorId]) {
                this.editors[editorId].setValue(newContent);
              }
              
              this.hasChanges = true;
            }
          }
          
          this.fullscreenEditor.dispose();
          this.fullscreenEditor = null;
        }
      }
    },
    // 添加源表
    addSourceTable(mapping) {
      if (!mapping.sourceTables) {
        mapping.sourceTables = []
      }
      
      mapping.sourceTables.push({
        id: null,
        name: '',
        expanded: false,
        fields: []
      })
    },
    
    // 移除源表
    removeSourceTable(mapping, index) {
      mapping.sourceTables.splice(index, 1)
    },
    
    // 切换源表字段展示
    toggleSourceTableFields(sourceTable) {
      sourceTable.expanded = !sourceTable.expanded
    },
    
    // 源表变更处理
    handleSourceTableChange(tableId, sourceTable, mapping) {
      const selectedTable = this.sourceTables.find(t => t.id === tableId)
      if (selectedTable) {
        sourceTable.name = selectedTable.name
        sourceTable.fields = this.tableFields[tableId] || []
        
        // 更新主源表信息(用于显示在折叠标题)
        if (mapping.sourceTables && mapping.sourceTables.length > 0 && mapping.sourceTables[0].id === tableId) {
          mapping.sourceTableId = tableId
          mapping.sourceTable = selectedTable.name
        }
      }
    },
    
    // 目标表变更处理
    handleSinkTableChange(tableId, mapping) {
      const selectedTable = this.sinkTables.find(t => t.id === tableId)
      if (selectedTable) {
        mapping.sinkTable = selectedTable.name
        mapping.sinkTableFields = this.tableFields[tableId] || []
      }
    },

    // 折叠/展开左侧应用列表
    toggleAppList(e) {
      // 阻止事件冒泡
      if (e) {
        e.stopPropagation();
      }
      // 切换侧边栏折叠状态
      this.isAppListCollapsed = !this.isAppListCollapsed;
      // 保存状态到 localStorage - 现在由 watch 处理
      // localStorage.setItem('flinkAppListCollapsed', this.isAppListCollapsed);
    },

    // 处理编辑应用
    handleEditApp(app) {
      this.appForm = {
        id: app.id,
        appName: app.type === 'app' ? app.name : app.appName,
        databaseId: app.databaseId
      }
      this.editAppDialogVisible = true
    },

    // 提交编辑应用
    async submitEditApp() {
      this.$refs.editAppForm.validate(async valid => {
        if (valid) {
          this.updating = true
          try {
            const data = {
              id: this.appForm.id,
              name: this.appForm.appName,
              databaseId: this.appForm.databaseId
            }
            await updateFlinkApp(data)
            this.editAppDialogVisible = false
            this.$message.success('更新成功')
            await this.initData()
          } catch (error) {
            this.$message.error('更新失败：' + error.message)
          } finally {
            this.updating = false
          }
        }
      })
    },

    // 处理删除应用
    async handleDeleteApp(app) {
      const appName = app.type === 'app' ? app.name : app.appName
      const appId = app.id
      
      try {
        await this.$confirm('确认删除应用 "' + appName + '"？删除后不可恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await deleteFlinkApp(appId)
        this.$message.success('删除成功')
        
        // 如果删除的是当前选中的应用，清除currentApp
        if (this.currentApp && this.currentApp.id === appId) {
          this.currentApp = null
        }
        
        await this.initData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message)
        }
      }
    },

    // 校验 SQL
    async validateSQL() {
      const activeIndex = this.activeMapping[0];
      if (activeIndex === undefined) return;
      
      const editorId = `editor-container-${activeIndex}`;
      const editor = this.editors[editorId];
      if (!editor) return;
      
      const currentMapping = this.mappings[activeIndex];
      if (!currentMapping) return;
      
      const sqlContent = editor.getValue();
      if (!sqlContent.trim()) {
        this.$message.warning('SQL内容不能为空');
        return;
      }
      
      this.validating = true;
      
      try {
        // 这里是SQL验证逻辑，实际项目中应该调用后端API
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // 简单的SQL验证规则
        let valid = true;
        let errorMessage = '';
        
        // 检查是否包含 SELECT 关键字
        if (!sqlContent.toUpperCase().includes('SELECT')) {
          valid = false;
          errorMessage = 'SQL语句必须包含SELECT关键字';
        }
        
        // 检查是否有基本的FROM子句
        if (!sqlContent.toUpperCase().includes('FROM')) {
          valid = false;
          errorMessage = 'SQL语句必须包含FROM子句';
        }
        
        if (valid) {
          this.$message.success('SQL语法验证通过');
        } else {
          this.$message.error(errorMessage);
        }
      } catch (error) {
        this.$message.error('SQL验证失败: ' + error.message);
      } finally {
        this.validating = false;
      }
    },
    
    // 保存 SQL
    async saveSQL() {
      const activeIndex = this.activeMapping[0];
      if (activeIndex === undefined) return;
      
      const editorId = `editor-container-${activeIndex}`;
      const editor = this.editors[editorId];
      if (!editor) return;
      
      const currentMapping = this.mappings[activeIndex];
      if (!currentMapping) return;
      
      const sqlContent = editor.getValue();
      
      this.savingSQL = true;
      
      try {
        // 模拟保存过程
        await new Promise(resolve => setTimeout(resolve, 600));
        
        // 更新当前映射的SQL
        currentMapping.transSql = sqlContent;
        
        // 重置变更标志
        this.hasChanges = false;
        
        this.$message.success('SQL保存成功');
      } catch (error) {
        this.$message.error('保存失败: ' + error.message);
      } finally {
        this.savingSQL = false;
      }
    },
    
    // 清空 SQL
    clearSQL() {
      this.$confirm('确认清空当前SQL内容吗？清空后不可恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const activeIndex = this.activeMapping[0];
        if (activeIndex === undefined) return;
        
        const editorId = `editor-container-${activeIndex}`;
        const editor = this.editors[editorId];
        if (editor) {
          editor.setValue('-- 请在此输入SQL查询语句');
          
          const currentMapping = this.mappings[activeIndex];
          if (currentMapping) {
            currentMapping.transSql = '-- 请在此输入SQL查询语句';
            this.hasChanges = true;
          }
          
          this.$message.success('SQL已清空');
        }
      }).catch(() => {
        // 取消清空操作
      });
    },

    // 复制文本到剪贴板
    copyToClipboard(text) {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
      this.$message.success('复制成功');
    },

    // 过滤字段
    filterFields(fields, keyword) {
      if (!keyword) return fields;
      const lowerKeyword = keyword.toLowerCase();
      return fields.filter(field => 
        field.name.toLowerCase().includes(lowerKeyword) ||
        field.type.toLowerCase().includes(lowerKeyword) ||
        (field.comment && field.comment.toLowerCase().includes(lowerKeyword))
      );
    },
    filterNode(value, data) {
      if (!value) return true
      const searchValue = value.toLowerCase()
      if (data.type === 'app') {
        return data.name.toLowerCase().includes(searchValue)
      } else {
        return data.sourceTable.toLowerCase().includes(searchValue) || 
               data.sinkTable.toLowerCase().includes(searchValue)
      }
    },
    handleNodeClick(data) {
      // 保存当前应用列表折叠状态
      const savedCollapsedState = this.isAppListCollapsed
      
      if (data.type === 'app') {
        try {
          // 获取当前点击应用的记录
          const app = this.apps.find(a => a.id === data.id)
          if (app) {
            // 设置当前应用
            this.currentApp = app
            
            // 如果是应用节点点击，清除当前映射
            this.currentMapping = null
            this.currentMappingIndex = -1
            
            // 设置当前节点为选中状态
            this.$refs.appTree.setCurrentKey(data.id)
            
            // 切换应用节点的展开/折叠状态
            this.toggleNodeExpanded(data.id)
            
            // 检查是否需要加载应用的映射数据 - 懒加载优化
            if (!this.appMappingsLoaded[app.id]) {
              this.loadAppMappings(app.id)
              this.appMappingsLoaded[app.id] = true
            }
          }
        } catch (error) {
          console.error('处理应用节点点击失败:', error)
        }
      } else if (data.type === 'mapping') {
        try {
          // 如果点击的是映射节点
          const [appId, , mappingId] = data.id.split('-') // 解析出appId和mappingId
          
          // 如果已经选中了这个映射节点，不进行操作
          if (this.currentMapping && this.currentMapping.operationKey === mappingId) {
            return
          }
          
          // 设置当前节点为选中状态
          this.$refs.appTree.setCurrentKey(data.id)
          
          // 查看该映射详情
          this.viewMappingDetail(this.mappings[data.index], data.index)
        } catch (error) {
          console.error('处理映射节点点击失败:', error)
        }
      }
      
      // 恢复应用列表折叠状态
      this.isAppListCollapsed = savedCollapsedState
    },
    
    // 切换节点展开/折叠状态
    toggleNodeExpanded(nodeId) {
      const index = this.expandedNodes.indexOf(nodeId)
      if (index > -1) {
        // 如果已经展开，则折叠
        this.expandedNodes.splice(index, 1)
      } else {
        // 如果是折叠状态，则展开
        this.expandedNodes.push(nodeId)
      }
    },
    
    // 查看映射详情
    viewMappingDetail(mapping, index) {
      // 保存当前折叠状态
      const savedCollapsedState = this.isAppListCollapsed
      
      this.currentMapping = mapping
      this.currentMappingIndex = index
      
      // 确保父应用节点展开
      if (mapping.appId && !this.expandedNodes.includes(mapping.appId)) {
        this.expandedNodes.push(mapping.appId)
      }
      
      // 设置当前映射节点为高亮
      const mappingNodeId = `${mapping.appId}-mapping-${mapping.operationKey}` // 修改映射节点ID的生成方式
      if (this.$refs.appTree) {
        this.$refs.appTree.setCurrentKey(mappingNodeId)
      }
      
      // 延迟加载编辑器
      this.$nextTick(() => {
        this.initEditor()
      })
      
      // 恢复折叠状态
      this.isAppListCollapsed = savedCollapsedState
    },
    handleTableCurrentChange(page) {
      this.tablePage = page;
    },

  }
}
</script>

<style lang="scss" scoped>
.flink-app-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  display: flex;
  gap: 24px;
  overflow: hidden;

  .app-list {
    width: 280px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    position: relative;
    transition: all 0.3s ease;

    &.is-collapsed {
      width: 50px;
      
      .collapse-btn {
        position: absolute;
        top: 50%;
        right: -10px;
        transform: translateY(-50%);
        width: 25px;
        height: 25px;
        border-radius: 50%;
        background: linear-gradient(135deg, #409EFF, #64B5F6);
        box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
        z-index: 100;
        
        i {
          color: #fff;
        }
        
        &:hover {
          background: linear-gradient(135deg, #66b1ff, #7fc6ff);
        }
      }

      .collapse-hint {
        position: absolute;
        left: 50%;
        top: 40%;
        transform: translate(-50%, 0);
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #409EFF;
        font-size: 14px;
        line-height: 1.6;
        opacity: 0.9;
        width: 20px;
        
        span {
          writing-mode: vertical-lr;
          letter-spacing: 4px;
          transition: all 0.3s ease;
          cursor: default;
          font-weight: 500;
        }
      }
    }

    .list-header {
      padding: 20px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);
      transition: opacity 0.3s ease;
      
      .header-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        span {
          font-size: 18px;
          font-weight: 600;
          color: #1a1f36;
          letter-spacing: 0.5px;
          position: relative;
          padding-left: 12px;

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: linear-gradient(to bottom, #409EFF, #64B5F6);
            border-radius: 3px;
          }
        }
        
        .title-icon {
          font-size: 18px;
          color: #409EFF;
          margin-right: 6px;
        }
        
        .header-actions {
          margin-left: auto;
          display: flex;
          gap: 10px;
        }
        
        .add-btn {
          flex-shrink: 0;
          width: 36px;
          height: 36px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #409EFF, #64B5F6);
          border: none;
          border-radius: 8px;
          
          i {
            color: #fff;
            font-size: 16px;
            margin-right: 0;
          }
          
          &:hover {
            background: linear-gradient(135deg, #66b1ff, #7fc6ff);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
          }
        }
      }
      
      .search-box {
        .el-input {
          width: 100%;
          ::v-deep .el-input__inner {
            border-radius: 10px;
            height: 36px;
            background: #f7f9fc;
            border: 1px solid #e7ebf3;
            
            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
          
          ::v-deep .el-input__prefix {
            left: 10px;
            i {
              color: #8492a6;
            }
          }
        }
      }
    }

    .tree-container {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #fff;
      transition: opacity 0.3s ease;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      ::v-deep {
        .el-tree {
          background: transparent;
        }

        .el-tree-node__content {
          height: 32px;
          border-radius: 8px;
          margin: 3px 0;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #f5f7fa;
          }

          .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;
            overflow: hidden;
            
            .node-content {
              display: flex;
              align-items: center;
              gap: 6px;
              flex: 1;
              min-width: 0;
              overflow: hidden;

              i {
                font-size: 14px;
                color: #409EFF;
                flex-shrink: 0;
              }

              span {
                color: #606266;
                text-align: left;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .node-actions {
              display: none;
              flex-shrink: 0;
              
              .el-button {
                padding: 5px;
                margin: 0 2px;
                border: none;
                
                i {
                  margin-right: 0;
                  font-size: 12px;
                  color: #fff;
                }
              }
            }
          }
          
          &:hover .node-actions {
            display: flex;
          }
          
          &.is-current {
            background-color: #ecf5ff !important;
            box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
            
            .custom-tree-node {
              .node-content {
                span {
                  color: #409EFF;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
    
    .collapse-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: -12px;
      width: 25px;
      height: 25px;
      background: #fff;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f0f2f5;
      }
      
      i {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .mapping-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;

    .detail-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: 100%;

      .info-section {
        background-color: #fff;
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

        &:hover {
          box-shadow: 0 6px 24px rgba(31, 45, 61, 0.1);
        }

        &:first-child {
          flex-shrink: 0;
        }

        &:last-child {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;

          i {
            margin-right: 8px;
            color: #409EFF;
          }

          .info-actions {
            margin-left: auto;
            display: flex;
            gap: 8px;
            
            .action-btn {
              height: 32px;
              padding: 0 12px;
              border-radius: 6px;
              display: flex;
              align-items: center;
              
              i {
                margin-right: 4px;
                color: #fff;
              }
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
              }
            }
          }

          .mapping-count {
            font-size: 13px;
            color: #909399;
            margin-left: 8px;
            font-weight: normal;
          }
        }

        .subtitle {
          display: flex;
          gap: 16px;
          color: #909399;
          font-size: 14px;

          .info-item {
            label {
              color: #606266;
              margin-right: 8px;
              font-weight: 500;
            }
          }
        }

        .mapping-content {
          flex: 1;
          overflow: auto;
          
          .mapping-row {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
            
            .source-tables, .sink-table, .sql-editor-container {
              border: 1px solid #e7ebf3;
              border-radius: 8px;
              padding: 12px;
              background: #f8f9fb;
            }
            
            .source-tables {
              width: 25%;
              display: flex;
              flex-direction: column;
              
              .column-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                
                span {
                  font-weight: 500;
                  color: #606266;
                }
              }
              
              .tables-container {
                overflow-y: auto;
                flex: 1;
                
                .table-item {
                  margin-bottom: 10px;
                  
                  &:last-child {
                    margin-bottom: 0;
                  }
                  
                  .table-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    
                    i {
                      transition: transform 0.3s;
                      margin-right: 4px;
                      
                      &.is-expanded {
                        transform: rotate(90deg);
                      }
                    }
                    
                    .delete-btn {
                      margin-left: 4px;
                    }
                  }
                  
                  .table-fields {
                    margin-left: 24px;
                    margin-top: 4px;
                    padding: 8px;
                    background: #fff;
                    border-radius: 4px;
                    max-height: 200px;
                    overflow-y: auto;
                    
                    .field-item {
                      display: flex;
                      justify-content: space-between;
                      padding: 4px 0;
                      border-bottom: 1px dashed #ebeef5;
                      
                      &:last-child {
                        border-bottom: none;
                      }
                      
                      .field-name {
                        color: #303133;
                        font-family: monospace;
                      }
                      
                      .field-type {
                        color: #909399;
                        font-size: 12px;
                        font-family: monospace;
                      }
                    }
                    
                    .empty-fields {
                      color: #909399;
                      font-size: 12px;
                      text-align: center;
                      padding: 10px 0;
                      
                      i {
                        margin-right: 4px;
                      }
                    }
                  }
                }
              }
            }
            
            .sql-editor-container {
              flex: 1;
              width: 50%;
              
              .editor-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                
                span {
                  font-size: 14px;
                  color: #606266;
                  font-weight: 500;
                }
                
                .editor-actions {
                  display: flex;
                  gap: 8px;
                  
                  .el-button {
                    padding: 6px 12px;
                    font-size: 12px;
                    
                    i {
                      margin-right: 4px;
                    }
                    
                    &.el-button--primary {
                      background: linear-gradient(135deg, #409EFF, #64B5F6);
                      border: none;
                      
                      &:hover {
                        background: linear-gradient(135deg, #66b1ff, #7fc6ff);
                        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                      }
                    }
                    
                    &.el-button--danger {
                      background: linear-gradient(135deg, #f56c6c, #f78989);
                      border: none;
                      
                      &:hover {
                        background: linear-gradient(135deg, #f78989, #fa7575);
                        transform: translateY(-1px);
                      }
                    }
                  }
                }
              }
              
              .editor-wrapper {
                position: relative;
                height: 600px;
                
                .code-editor {
                  height: 100%;
                  width: 100%;
                  border: 1px solid #3c3f41;
                  border-radius: 8px;
                  overflow: hidden;
                  transition: all 0.3s ease;

                  &:hover {
                    border-color: #4b4d4f;
                  }

                  &:focus-within {
                    border-color: #4f5659;
                    box-shadow: 0 0 0 3px rgba(97, 175, 254, 0.1);
                  }
                }
              }
            }
            
            .sink-table {
              width: 25%;
              display: flex;
              flex-direction: column;
              
              .column-header {
                margin-bottom: 12px;
                font-weight: 500;
                color: #606266;
              }
              
              .table-container {
                display: flex;
                flex-direction: column;
                flex: 1;
                
                .table-header {
                  margin-bottom: 12px;
                }
                
                .table-fields {
                  background: #fff;
                  border-radius: 4px;
                  padding: 8px;
                  flex: 1;
                  overflow-y: auto;
                  margin-top: 4px;
                  
                  .field-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 4px 0;
                    border-bottom: 1px dashed #ebeef5;
                    
                    &:last-child {
                      border-bottom: none;
                    }
                    
                    .field-name {
                      color: #303133;
                      font-family: monospace;
                    }
                    
                    .field-type {
                      color: #909399;
                      font-size: 12px;
                      font-family: monospace;
                    }
                  }
                  
                  .empty-fields {
                    color: #909399;
                    font-size: 12px;
                    text-align: center;
                    padding: 10px 0;
                    
                    i {
                      margin-right: 4px;
                    }
                  }
                }
              }
            }
          }
          
          .mapping-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 16px;
          }
          
          .save-actions {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e7ebf3;
          }
          
          .empty-tip {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30px 0;
            color: #909399;
            
            i {
              margin-right: 8px;
              font-size: 16px;
              color: #e6a23c;
            }
          }
          
          ::v-deep .el-table {
            &.custom-table {
              border-radius: 12px;
              overflow: hidden;
              margin-top: 12px;
              
              &.el-table--border {
                border-radius: 12px;
                overflow: hidden;
              }

              th {
                background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
                font-weight: 600;
                color: #1a1f36;
                height: 40px;
                padding: 8px 0;
              }

              td {
                padding: 6px 0;
              }
              
              .el-table__body-wrapper::-webkit-scrollbar {
                width: 4px;
                height: 4px;
              }
              
              .el-table__body-wrapper::-webkit-scrollbar-thumb {
                background: rgba(144, 147, 153, 0.3);
                border-radius: 4px;
                
                &:hover {
                  background: rgba(144, 147, 153, 0.5);
                }
              }
              
              .el-table__body-wrapper::-webkit-scrollbar-track {
                background: transparent;
              }
              
              .table-info {
                display: flex;
                align-items: center;
                gap: 6px;

                i {
                  color: #409EFF;
                  font-size: 14px;
                }
              }
              
              .operation-column {
                .cell {
                  white-space: nowrap;
                  display: flex;
                  justify-content: center;
                  gap: 4px;

                  .el-button {
                    padding: 2px 4px;
                    font-size: 12px;
                    margin: 0;
                    height: 24px;
                    line-height: 1;
                    display: inline-flex;
                    align-items: center;

                    i {
                      margin-right: 4px;
                      font-size: 13px;
                    }
                    
                    &.view-btn {
                      color: #409EFF;
                      transition: all 0.3s;
                      border-radius: 4px;
                      padding: 3px 10px;
                      
                      &:hover {
                        background-color: rgba(64, 158, 255, 0.1);
                        color: #66b1ff;
                      }
                      
                      i {
                        color: #409EFF;
                        margin-right: 4px;
                      }
                    }
                    
                    &.delete-btn {
                      color: #f56c6c;
                      transition: all 0.3s;
                      border-radius: 4px;
                      padding: 3px 10px;
                      
                      &:hover {
                        background-color: rgba(245, 108, 108, 0.1);
                        color: #f78989;
                      }
                      
                      i {
                        color: #f56c6c;
                        margin-right: 4px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    animation: fadeIn 0.3s ease;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      color: #dcdfe6;
    }

    p {
      font-size: 14px;
      margin: 0;
    }
  }

  .editor-container {
    border: 1px solid #e7ebf3;
    border-radius: 8px;
    overflow: hidden;
    
    .monaco-editor-wrapper {
      width: 100%;
    }
  }

  /* 应用细滚动条样式 */
  :deep(.app-list .list-container),
  :deep(.source-tables .tables-container),
  :deep(.table-fields),
  :deep(.sink-table .table-fields) {
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 4px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
  }

  .sql-editor-container {
    .editor-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      span {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }
      
      .editor-actions {
        display: flex;
        gap: 8px;
        
        .el-button {
          padding: 6px 12px;
          font-size: 12px;
          
          i {
            margin-right: 4px;
          }
          
          &.el-button--primary {
            background: linear-gradient(135deg, #409EFF, #64B5F6);
            border: none;
            
            &:hover {
              background: linear-gradient(135deg, #66b1ff, #7fc6ff);
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            }
          }
          
          &.el-button--danger {
            background: linear-gradient(135deg, #f56c6c, #f78989);
            border: none;
            
            &:hover {
              background: linear-gradient(135deg, #f78989, #fa7575);
              transform: translateY(-1px);
            }
          }
        }
      }
    }

    .editor-wrapper {
      position: relative;
      height: 380px;
      
      .code-editor {
        height: 100%;
        width: 100%;
        border: 1px solid #3c3f41;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
          border-color: #4b4d4f;
        }

        &:focus-within {
          border-color: #4f5659;
          box-shadow: 0 0 0 3px rgba(97, 175, 254, 0.1);
        }
      }
    }
  }
  
  .fullscreen-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 2999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;

    .fullscreen-editor {
      background: #1e1e1e;
      width: 100%;
      height: 100%;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

      .fullscreen-header {
        padding: 12px 20px;
        border-bottom: 1px solid #333;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #2d2d2d;
        border-radius: 8px 8px 0 0;

        .title {
          font-size: 16px;
          font-weight: 500;
          color: #e5e5e5;
        }

        .close-button {
          padding: 6px 12px;
          background: transparent;
          border: 1px solid #444;
          color: #e5e5e5;
          
          &:hover {
            background: #444;
            border-color: #555;
          }
          
          i {
            margin-right: 6px;
          }
        }
      }

      .fullscreen-content {
        flex: 1;
        padding: 0;
        overflow: hidden;

        .code-editor {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}

.app-detail-dialog {
  ::v-deep {
    .el-dialog__header {
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }

    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #ebeef5;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);
    }
  }
}

.primary-action-btn {
  height: 32px;
  padding: 0 16px;
  background: linear-gradient(135deg, #409EFF, #64B5F6);
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
    background: linear-gradient(135deg, #66b1ff, #7fc6ff);
  }
  
  i {
    margin-right: 6px;
  }
}

.cancel-btn {
  background: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
  
  &:hover {
    background: #e9ecf2;
    border-color: #d3d6de;
    color: #333;
  }
}

.icon-white {
  color: #ffffff !important;
}

.field-search {
  padding: 8px;
  background: #f8f9fb;
  border-bottom: 1px solid #ebeef5;
  
  .el-input {
    .el-input__inner {
      height: 28px;
      line-height: 28px;
      font-size: 12px;
    }
  }
}

.field-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 4px 8px;
}

.field-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  
  &:hover {
    background: #f5f7fa;
  }
  
  .field-name {
    flex: 1;
    color: #303133;
    font-family: monospace;
  }
  
  .field-type {
    color: #909399;
    font-size: 12px;
    font-family: monospace;
  }
  
  .field-comment {
    color: #909399;
    font-size: 14px;
    cursor: help;
    
    &:hover {
      color: #409EFF;
    }
  }
}

.table-id {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  cursor: pointer;
  user-select: none;
  
  &:hover {
    color: #409EFF;
  }
}

.empty-fields {
  text-align: center;
  padding: 20px 0;
  color: #909399;
  font-size: 12px;
  
  i {
    margin-right: 4px;
  }
}

.dataset-tag {
  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

    i {
      color: #409EFF;
    }
  }
}

.pagination-container {
  padding: 16px 0 8px;
  display: flex;
  justify-content: center;
}

.empty-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: #909399;
  
  i {
    margin-right: 8px;
    font-size: 16px;
    color: #e6a23c;
  }
}
</style>

<style>
/* 全局样式确保monaco编辑器正确显示 */
.monaco-editor {
  padding: 0 !important;
  width: 100% !important;
}

.monaco-editor .overflow-guard {
  width: 100% !important;
}

.monaco-editor .editor-scrollable {
  width: 100% !important;
}

/* 细滚动条全局样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(144, 147, 153, 0.3) transparent;
}

*::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.5);
}
</style>

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

