import { getToken } from '@/utils/auth'
import { createLoadingInstance } from './loadingFactory.js'

export default [(config) => {
  if (!config.loadingDisabled) {
    window.loadingInstance = createLoadingInstance()
  }

  let token = getToken() == undefined ? null : getToken()

  if (config.headers['Content-Type'] === undefined) {
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'
  }

  if (config.headers['Authorization'] === undefined) {
    config.headers['Authorization'] = token
  }

  return config
}, (error) => {
  window.loadingInstance && window.loadingInstance.delayClose()
  return Promise.reject(error)
}
]