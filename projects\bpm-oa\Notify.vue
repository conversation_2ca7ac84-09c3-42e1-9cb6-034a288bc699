<template>
    <el-popover placement="bottom-end" width="480" trigger="click">
        <div class="notify">
            <el-empty :image-size="50" description="暂无消息 😁" v-if="notify.total === 0"></el-empty>
            <div v-for="msg in notify.records" :key="msg.id" class="notify-item">
                <el-row>
                    <el-col :span="2">
                        <div class="notify-item-type-icon">
                            <i class="el-icon-success" v-if="msg.type === 'SUCCESS'" style="color: #02b068"></i>
                            <i class="el-icon-warning" v-else-if="msg.type === 'WARNING'" style="color: #f78f5f"></i>
                            <i class="el-icon-error" v-else-if="msg.type === 'ERROR'" style="color: #f25643"></i>
                            <i class="el-icon-info" v-else style="color: #8c8c8c"></i>
                        </div>
                    </el-col>
                    <el-col :span="22">
                        <div class="notify-item-title" @click="toNotify(msg)">{{ msg.title }}</div>
                        <ellipsis hoverTip class="notify-item-content" :content="msg.content" />
                    </el-col>
                </el-row>
                <span class="notify-item-time">{{ msg.createTime.substring(5, 16) }}</span>
                <el-button type="text" class="notify-btn" @click="readNotify(msg.id)">已读</el-button>
            </div>
        </div>
        <div class="notify-action" v-show="notify.total > 0">
            <el-button type="text" @click="--params.pageNo; getUserNotify()" :disabled="params.pageNo <= 1">上一页</el-button>
            <el-button type="text" @click="readNotify(null)">本页已读</el-button>
            <el-button type="text" @click="++params.pageNo;getUserNotify()"
                :disabled="notify.total <= params.pageSize * notify.current">
                下一页
            </el-button>
        </div>
        <el-badge class="notify-badge" slot="reference" :hidden="notify.total === 0" :value="notify.total">
            <i class="el-icon-bell"></i>
        </el-badge>
    </el-popover>
</template>
<script>
import { getUserNotify, readNotify } from '@bpm-oa-web/api/notify'
import store from "@/store"
export default {

    data() {
        return {
            params: {
                pageSize: 5,
                pageNo: 1
            },
            notify: {
                records: []
            },
        }
    },
    computed: {
        loginUser() {
            return store.state.bpm.loginUser
        }
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    mounted() {
        if (this.loginUser.id) {
            this.getUserNotify()
            this.timerGetNotify(5)
        }
    },

    methods: {
        getUserNotify() {
            getUserNotify(this.params).then(res => {
                this.notify = res.data
            }).catch(err => {
                if (this.timer) {
                    clearInterval(this.timer)
                }
                this.$err(err, '获取通知消息失败')
            })
        },
        toNotify(msg) {
            if (this.$isNotEmpty(msg.instanceId)) {
                this.selectInstance = msg.instanceId
                this.processVisible = true
                this.readNotify(msg.id)
            }
        },
        readNotify(id) {
            let list = id ? [id] : this.notify.records.map(n => n.id);
            readNotify(list).then(rsp => {
                this.$ok(rsp, '已读成功')
                this.getUserNotify()
            }).catch(err => {
                this.$err(err, '已读失败')
            })
        },
        timerGetNotify(cycle) {
            if (this.timer) {
                clearInterval(this.timer)
            }
            this.timer = setInterval(() => this.getUserNotify(), cycle * 1000)
        },
    }
}
</script>
<style lang="scss" scoped>
.notify {
    max-height: 200px;
    background: $theme-aside-bgc;
    overflow-y: auto;

    .notify-item:last-child {
        border-bottom: 2px solid $theme-aside-bgc;
    }

    .notify-item {
        border-top: 2px solid $theme-aside-bgc;
        padding: 5px;
        background: white;
        position: relative;
        border-radius: 5px;

        .notify-item-title {
            cursor: pointer;
            color: #3b3b3b;

            &:hover {
                color: $theme-primary;
            }
        }

        .notify-item-content {
            color: #8c8c8c;
            padding: 5px 0;
            font-size: smaller;
        }

        .notify-item-time {
            position: absolute;
            right: 45px;
            top: 7px;
            font-size: 12px;
            color: #8c8c8c;
        }

        .notify-btn {
            position: absolute;
            right: 5px;
            top: 8px;
        }

        .notify-item-type-icon {
            font-size: 18px;
        }
    }
}

.notify-action {
    display: flex;
    justify-content: space-between;
}

.notify-badge {
    position: fixed;
    right: 282px;
    top: 32px;
}
</style>