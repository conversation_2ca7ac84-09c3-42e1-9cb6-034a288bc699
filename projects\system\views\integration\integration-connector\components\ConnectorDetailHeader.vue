<template>
  <div class="connector-detail-header">
    <!-- 主要内容区域 -->
    <div class="header-main">
      <!-- 返回按钮 -->
      <div class="header-back">
        <el-button
          type="text"
          icon="el-icon-arrow-left"
          @click="handleGoBack"
          class="back-btn"
        >
          返回
        </el-button>
      </div>

      <!-- 连接器基本信息 - 横向布局 -->
      <div class="connector-basic-info">
        <!-- 连接器图标 -->
        <div class="connector-icon-section">
          <div class="connector-icon">
            <div class="icon-wrapper" :style="iconStyle">
              <i :class="iconClass"></i>
            </div>
          </div>
        </div>

        <!-- 连接器名称 -->
        <div class="connector-name-section">
          <h1 class="connector-name">
            {{ connectorDetail.name || defaultName }}
          </h1>
        </div>

        <!-- 连接器编码 -->
        <div class="connector-code-section">
          <el-tag
            size="small"
            type="info"
            class="code-tag"
            @click="copyCode"
            :title="'点击复制连接器编码'"
          >
            <i class="el-icon-document-copy copy-icon"></i>
            {{ connectorDetail.code || 'CONNECTOR-CODE' }}
          </el-tag>
        </div>

        <!-- 连接器类型 -->
        <div class="connector-type-section">
          <el-tag
            size="small"
            :type="typeTagType"
            class="type-tag"
          >
            {{ typeLabel }}
          </el-tag>
        </div>

        <!-- 连接器元数据 -->
        <div class="connector-meta-section" v-if="metaInfo && metaInfo.length">
          <div
            v-for="(meta, index) in metaInfo"
            :key="index"
            class="meta-item"
          >
            <el-tag
              size="small"
              type="warning"
              class="meta-tag"
            >
              <i :class="meta.icon" v-if="meta.icon"></i>
              {{ meta.label }}: {{ meta.value }}
            </el-tag>
          </div>
        </div>

        <!-- 连接器描述 -->
        <div class="connector-description-section">
          <div class="description-display">
            <span class="description-text">
              {{ connectorDetail.description || '暂无描述' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 右侧：状态信息和操作按钮 -->
      <div class="connector-status-info">
        <div class="status-card">
          <div class="status-main">
            <div class="status-badge" :class="getConnectorStatusClass()">
              <i :class="getConnectorStatusIcon()"></i>
              <span class="status-text">{{ getConnectorStatusText() }}</span>
            </div>
          </div>

          <div class="status-details">
            <div class="status-item" v-if="connectorDetail.createBy">
              <span class="status-label">创建者</span>
              <span class="status-value">{{ connectorDetail.createBy }}</span>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConnectorDetailHeader',
  props: {
    connectorDetail: {
      type: Object,
      default: () => ({})
    },
    // 连接器类型相关
    typeLabel: {
      type: String,
      default: '连接器'
    },
    typeTagType: {
      type: String,
      default: 'success'
    },
    // 图标相关
    iconClass: {
      type: String,
      default: 'el-icon-link'
    },
    iconStyle: {
      type: Object,
      default: () => ({
        background: 'linear-gradient(135deg, #409eff, #67c23a)'
      })
    },
    // 默认名称
    defaultName: {
      type: String,
      default: '未命名连接器'
    },
    // 元数据信息
    metaInfo: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 返回上一页
    handleGoBack() {
      this.$emit('go-back')
    },



    // 复制连接器编码
    copyCode() {
      const code = this.connectorDetail.code || 'CONNECTOR-CODE'
      if (navigator.clipboard) {
        navigator.clipboard.writeText(code).then(() => {
          this.$message.success('连接器编码已复制到剪贴板')
        }).catch(() => {
          this.$message.error('复制失败，请手动复制')
        })
      } else {
        this.$message.error('复制失败，请手动复制')
      }
    },

    // 获取连接器状态样式类
    getConnectorStatusClass() {
      return this.connectorDetail.enabled ? 'status-enabled' : 'status-disabled'
    },

    // 获取连接器状态图标
    getConnectorStatusIcon() {
      return this.connectorDetail.enabled ? 'el-icon-check' : 'el-icon-close'
    },

    // 获取连接器状态文本
    getConnectorStatusText() {
      return this.connectorDetail.enabled ? '启用' : '禁用'
    }
  }
}
</script>

<style lang="scss" scoped>
.connector-detail-header {
  background: white;
  border-radius: 0; // 移除圆角，实现完全贴边
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  margin: 0; // 移除所有外边距，实现完全贴边
  flex-shrink: 0; // 防止头部被压缩
  position: relative;
  z-index: 10; // 确保头部始终在上层
  border-bottom: 1px solid rgba(0, 0, 0, 0.06); // 添加底部边框，增强分离感

  .header-main {
    padding: 12px 16px;
    display: flex;
    gap: 16px;
    align-items: center;
    min-height: 56px;

    // 确保在所有屏幕尺寸下都有适当的内边距
    @media (max-width: 768px) {
      padding: 12px 12px;
      gap: 12px;
    }

    @media (max-width: 480px) {
      padding: 8px 8px;
      gap: 8px;
      min-height: 48px;
    }

    .header-back {
      flex-shrink: 0;

      .back-btn {
        font-size: 13px;
        color: #666;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: #1890ff;
          background: rgba(24, 144, 255, 0.06);
        }
      }
    }

    .connector-basic-info {
      flex: 1;
      display: flex;
      gap: 16px;
      min-width: 0;
      align-items: center;

      .connector-icon-section {
        flex-shrink: 0;

        .connector-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

          .icon-wrapper {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
          }
        }
      }

      .connector-name-section {
        flex-shrink: 0;
        min-width: 120px;

        .connector-name {
          font-size: 18px;
          font-weight: 600;
          color: #262626;
          margin: 0;
          line-height: 1.3;
          white-space: nowrap;
        }
      }

      .connector-code-section {
        flex-shrink: 0;

        .code-tag {
          cursor: pointer;
          transition: all 0.2s ease;
          display: inline-flex;
          align-items: center;
          gap: 4px;
          font-family: 'Monaco', 'Menlo', monospace;
          font-size: 12px;

          &:hover {
            background: #e6f7ff;
            border-color: #91d5ff;
            color: #1890ff;
            transform: translateY(-1px);
          }

          .copy-icon {
            font-size: 11px;
          }
        }
      }

      .connector-type-section {
        flex-shrink: 0;

        .type-tag {
          font-weight: 600;
          font-size: 12px;
        }
      }

      .connector-meta-section {
        flex-shrink: 0;
        display: flex;
        gap: 8px;

        .meta-item {
          .meta-tag {
            background: #fff7e6;
            border-color: #ffd591;
            color: #d46b08;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;

            i {
              font-size: 11px;
            }
          }
        }
      }

      .connector-description-section {
        flex: 1;
        min-width: 0;
        margin-left: 8px;

        .description-display {
          .description-text {
            font-size: 13px;
            color: #595959;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 200px;
          }
        }
      }
    }

    .connector-status-info {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 12px;

      .status-card {
        display: flex;
        align-items: center;
        gap: 12px;
        background: #fafbfc;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        padding: 8px 12px;

        .status-main {
          .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            white-space: nowrap;

            i {
              font-size: 12px;
            }

            .status-text {
              font-weight: 600;
            }

            &.status-enabled {
              background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
              color: #ffffff;
              box-shadow: 0 1px 6px rgba(82, 196, 26, 0.2);
            }

            &.status-disabled {
              background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
              color: #ffffff;
              box-shadow: 0 1px 6px rgba(255, 77, 79, 0.2);
            }
          }
        }

        .status-details {
          display: flex;
          gap: 16px;

          .status-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            white-space: nowrap;

            .status-label {
              font-size: 11px;
              color: #8c8c8c;
              font-weight: 500;
            }

            .status-value {
              font-size: 12px;
              color: #262626;
              font-weight: 500;
            }
          }
        }
      }


    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .header-main {
      flex-wrap: wrap;
      gap: 12px;

      .connector-basic-info {
        .connector-description-section {
          display: none;
        }
      }

      .connector-status-info {
        .status-card .status-details {
          gap: 12px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .header-main {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .connector-basic-info {
        flex-wrap: wrap;
        gap: 12px;

        .connector-name-section {
          min-width: auto;
          flex: 1;
        }

        .connector-description-section {
          display: block;
          flex: 1 100%;
          margin-left: 0;
          margin-top: 8px;
        }
      }

      .connector-status-info {
        justify-content: center;
        flex-direction: column;
        gap: 8px;

        .status-card {
          flex-direction: column;
          gap: 8px;
          text-align: center;

          .status-details {
            flex-direction: row;
            justify-content: center;
            gap: 16px;
          }
        }


      }
    }
  }
}
</style>
