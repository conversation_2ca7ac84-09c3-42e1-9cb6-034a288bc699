<template>
  <div class="gateway-audit-container">
    <el-row :gutter="24">
      <!-- 左侧流量链路 -->
      <el-col :span="6">
        <div class="card-container flow-panel">
          <div class="card-body flow-list">
            <FlowVisualizer 
              :predicates="allPredicates"
              :filters="allFilters"
              :securities="allSecurities"
              :selectedRoute="currentRoute"
              :editable="false"
              @route-selected="selectRoute"
            />
          </div>
        </div>
      </el-col>

      <!-- 右侧审计面板 -->
      <el-col :span="18">
        <div class="card-container audit-panel">
          <div class="card-body">
            <audit-index :route-id="currentRoute?.id" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import FlowVisualizer from './components/FlowVisualizer'
import AuditIndex from './audit/index.vue'

export default {
  name: 'GatewayAudit',
  components: {
    FlowVisualizer,
    AuditIndex
  },
  data() {
    return {
      // 路由相关数据
      currentRoute: null,
      allPredicates: [],
      allFilters: [],
      allSecurities: []
    }
  },
  methods: {
    selectRoute(route) {
      this.currentRoute = route
    }
  }
}
</script>

<style lang="scss" scoped>
.gateway-audit-container {
  height: calc(100vh - 75px);
  padding: 12px;
  margin: 12px;
  overflow: hidden;
  background: inherit;

  .el-row {
    height: 100%;
    width: 100%;
    margin: 0 !important;
    
    .el-col {
      height: 100%;
    }
  }

  .card-container {
    height: 100%;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(31, 45, 61, 0.1);
    }
  }

  .card-body {
    flex: 1;
    overflow: hidden;
    padding: 20px;
    height: 0;
    min-height: 0;
    display: flex;
    flex-direction: column;
    background-color: #fff;
  }

  .flow-panel {
    .flow-list {
      padding: 10px;
      height: 100%;
      overflow-y: auto;
      margin-right: 2px;
      
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 4px;
        border: 2px solid #fff;
        background-clip: padding-box;
        
        &:hover {
          background: rgba(144, 147, 153, 0.5);
          border: 2px solid #fff;
          background-clip: padding-box;
        }
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
      }
    }
  }

  .audit-panel {
    .card-body {
      padding: 0;
    }
  }
}
</style>
