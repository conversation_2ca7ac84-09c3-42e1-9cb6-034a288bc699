<template>
  <div id="groovy-editor" :style="{ 'height': `${height}px` }"></div>
</template>

<script>
import * as monaco from 'monaco-editor'
const LANGUAGE = 'groovy'
if (!monaco.languages.getLanguages().find(lang => lang.id === LANGUAGE)) { 
  monaco.languages.register({ id: LANGUAGE });
  monaco.languages.setLanguageConfiguration(LANGUAGE, {
    comments: {
      lineComment: '//',
      blockComment: ['/*', '*/']
    },
    brackets: [['{', '}'], ['[', ']'], ['(', ')']],
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"' },
      { open: "'", close: "'" }
    ]
  });

  monaco.languages.setMonarchTokensProvider(LANGUAGE, {
    tokenizer: {
      root: [
        [/[a-z_$][\w$]*/, { cases: { '@keywords': 'keyword', '@default': 'identifier' } }],
        [/[{}()\[\]]/, '@brackets'],
        [/@\s*[a-zA-Z_\$][\w\$]*/, 'annotation'],
        [/".*?"/, 'string'],
        [/\d+/, 'number']
      ]
    },
    keywords: ['def', 'class', 'if', 'else', 'while', 'return', 'println']
  });
}
export default {
  name: 'GroovyEditor',
  props: {
    height: {
      type: Number,
      default: 550
    },
    data: {
      type: String,
      default: ''
    }
  },
  model: {
    prop: 'data',
    event: 'change'
  },
  data() {
    return {
      editor: null,
    }
  },
  beforeDestroy() {
    // 销毁 Monaco 编辑器
    if (this.editor) {
      this.editor.dispose()
    }
  },
  mounted() { 
    console.log(monaco.languages);
    
    this.$nextTick(() => {
        const container = document.getElementById('groovy-editor')
        if (!container) return

        // 销毁已存在的编辑器
        if (this.editor) {
          this.editor.dispose()
        }

        // 创建新的编辑器实例
        this.editor = monaco.editor.create(container, {
          value: this.data || '',
          language: LANGUAGE,
          theme: 'vs', // 使用白色主题
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          lineNumbers: 'on',
          fontSize: 13,
          tabSize: 2,
          wordWrap: 'on',
          folding: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3
        })

        // 监听内容变化
        this.editor.onDidChangeModelContent((val) => {
          this.$emit('change', this.editor.getValue())
        })
      })
  },
}
</script>