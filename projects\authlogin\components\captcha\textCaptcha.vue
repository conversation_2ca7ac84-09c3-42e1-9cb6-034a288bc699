<template>
  <el-input maxlength="4" placeholder="请输入验证码" prefix-icon="el-icon-key" :value="value"
    @input="val => $emit('change', val)" @keyup.enter="handleLogin">
    <div slot="suffix" class="captcha-img" @click.stop="refresh">
      <el-image :src="codeResult.base64 | base64ToImage" fit="scale-down" style="height: 100%;" />
    </div>
  </el-input>
</template>

<script>
export default {
  name: "textCaptcha",
  data() {
    return {
      rules: [
        {
          trigger: "blur", validator: (rule, value, callback) => {
            if (this.value) {
              callback()
            } else {
              callback(new Error('请输入验证码'))
            }
          }
        }
      ]
    };
  },
  props: {
    codeResult: {
      type: Object,
      default: () => {
        return {}
      }
    },
    value: {
      type: String,
      default: ''
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  mounted() {
    this.refresh()
    this.$emit('mounted')
  },
  methods: {
    refresh() {
      this.$emit('refresh')
    },
    onError() {
      this.refresh()
    }
  },
  filters: {
    base64ToImage(base64) {
      return `data:image/png;base64,${base64 || ''}`
    }
  }
}
</script>

<style scoped>
.captcha-img {
  height: 100%;
}
</style>