<template>
  <el-form :model="form" size="small" ref="form" label-width="80px" style="height: 100%; display: flex; flex-direction: column; justify-content: space-between;">
    <el-form-item prop="type" label="聚合类型">
      <el-select v-model="type" style="width: 100%;">
        <el-option label="Groovy" value="GROOVY"></el-option>
        <el-option label="Python" value="PYTHON"></el-option>
        <el-option label="JavaScript" value="JS"></el-option>
      </el-select>
    </el-form-item>
    <component :is="editor.component" v-model="form.content" style="height: 100%;" />
  </el-form>
</template>

<script>
import { isEqual } from 'element-ui/src/utils/util';
import scriptEditor from '../script/index.js'

export default {
  name: 'AggregatorFlow',
  data() {
    return {
      form: {
        type: 'GROOVY'
      },
    }
  },
  computed: { 
    type: {
      get() {
        return this.form?.type?.toUpperCase() || 'GROOVY'
      },
      set(val) {
        this.form.type = val
      }
    },
    editor() {
      return scriptEditor[this.type]
    }
  },
  methods: {
    getProperties() {
      return this.form
    },
    async validate() {
      if (!this.form.content?.trim()) {
        this.$message.error('请填写脚本内容')
        return Promise.resolve(false)
      }
      return Promise.resolve(true)
    }
  },
  props: {
    properties: {
      type: Object,
      default: () => ({})
    },
    node: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    properties: {
      handler(newVal) {
        if (!isEqual(newVal, this.form)) {
          this.form = { ...this.form, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
  },
}
</script>