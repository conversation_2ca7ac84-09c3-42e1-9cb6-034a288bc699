# 快速开始

node版本：v14.21.3
下载地址：https://nodejs.org/en/download/prebuilt-installer

```shell
npm config set registry http://192.168.2.100:4873/
```

```bash
git submodule update --init --recursive
```

```bash
git submodule update --remote
```

```bash
git submodule foreach 'git checkout master; git pull origin master'
```

```bash
npm i 
```

```bash
npm run serve -- --project=项目
```



**postcss-px-to-plugin.config.js模版**
```
module.exports = {
    toRem: false,
    toViewport: true,
    toViewportConfig: {
        viewportWidth: 1920,
        propList: ['*', '!font-size', '!--txt_*'],
    },
    toRemConfig: {
        propList: ['font-size', '--txt_*'],
    },
};
```
**lib.config.js模版**
```
module.exports = {
    libs: ['system', 'commonlib']
}
```
**module.store.js模版(vuex分module)**
```
import bpm from './bpm.js'//子module

export default {
    bpm
}

```

**proxy.config.js模版**
```
module.exports = {
    '/dev-api/file': {
       target: `http://192.168.1.65:8002`,
        changeOrigin: false,
        pathRewrite: {
            '^/dev-api/file': ''
        }
    }
}

```

**配置title、logo等**
```
vue.$store.dispatch('settings/changeSetting', {
    key: 'title',
    value: '智慧园区管理平台'
})
vue.$store.dispatch('settings/changeSetting', {
    key: 'logo',
    value: require('@/assets/logo.png')
})

```
**配置tsconfig.js、tsconfig.json**
```
tsconfig.js配置模版、修改模版的配置,然后动态生成tsconfig.json配置文件
```
