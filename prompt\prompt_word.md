# 连接器详情页面

首先调用接口，调用接口时，请注意以下规则
1. 发请求的时候，我全局响应已经把data取出来了，不要多取一层
2. 不要太多的异常捕获，我请求工具里面已经做了全局的异常捕获
3. 要导入的时候，类似这种格式 @system/api/notice/chat.js

## 连接器详情接口
```bash
curl --location --request GET 'http://localhost:8007/integration/http/connector/auth/detail/1' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive'
```

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "createBy": 1001,
    "updateBy": null,
    "createTime": "2025-08-01 10:00:00",
    "updateTime": null,
    "id": 1,
    "remark": "微信支付官方API连接器",
    "code": "wechat_pay_connector",
    "name": "微信支付连接器111",
    "type": "HTTP",
    "description": "用于处理微信支付相关的API调用，包括统一下单、查询订单、退款等功能",
    "enabled": true,
    "authActuator": "wechatTokenExtractor",
    "tokenCacheSeconds": 7200,
    "config": {
      "type": "HTTP",
      "baseUrl": "http://localhost:8007",
      "headers": {},
      "authExpression": "${getAccessToken.access_token}",
      "authLocation": "HEADER",
      "authParamName": "Authorization"
    },
    "authRequests": [
      {
        "createBy": null,
        "updateBy": null,
        "createTime": "2025-08-02 16:12:52",
        "updateTime": null,
        "id": "1951461376889229314",
        "remark": null,
        "code": "getAccessToken",
        "name": "获取访问令牌",
        "description": null,
        "contentType": null,
        "url": "/mock/oauth/token",
        "method": "POST",
        "headers": {
          "Content-Type": "application/json"
        },
        "body": {
          "grant_type": "client_credentials",
          "client_id": "test_client_id",
          "client_secret": "test_client_secret"
        },
        "connectorId": 1,
        "type": "AUTH",
        "enabled": true
      },
      {
        "createBy": null,
        "updateBy": null,
        "createTime": "2025-08-02 16:12:52",
        "updateTime": null,
        "id": "1951461376889229315",
        "remark": null,
        "code": "refreshToken",
        "name": "刷新访问令牌",
        "description": null,
        "contentType": null,
        "url": "/mock/oauth/refresh",
        "method": "POST",
        "headers": {
          "Content-Type": "application/json",
          "Authorization": "Bearer ${getAccessToken.access_token}"
        },
        "body": {
          "refresh_token": "${getAccessToken.refresh_token}"
        },
        "connectorId": 1,
        "type": "AUTH",
        "enabled": true
      },
      {
        "createBy": null,
        "updateBy": null,
        "createTime": "2025-08-02 16:12:52",
        "updateTime": null,
        "id": "1951461376889229316",
        "remark": null,
        "code": "getUserInfo",
        "name": "获取用户信息",
        "description": null,
        "contentType": null,
        "url": "/mock/user/info",
        "method": "GET",
        "headers": {
          "Authorization": "Bearer ${refreshToken.access_token}",
          "Accept": "application/json"
        },
        "body": null,
        "connectorId": 1,
        "type": "AUTH",
        "enabled": true
      }
    ]
  },
  "success": true
}
```

## 获取所有认证执行器
> 在选择表单认证执行器的时候使用，获取可以使用的认证执行器
```bash
curl --location --request GET 'http://localhost:8007/integration/connector/auth/actuators' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive'
```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "code": "CHAIN_AUTH",
            "name": "链式认证",
            "description": "支持多步骤链式认证，通过执行一系列HTTP请求获取最终认证Token"
        }
    ],
    "success": true
}
```

## 测试网络连通性接口

```bash
curl --location --request POST 'http://localhost:8007/integration/http/connector/test/connection?url=http://localhost:8007' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive'
```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true,
    "success": true
}
```

## 测试链式执行

```bash
curl --location --request POST 'http://localhost:8007/integration/http/connector/test/auth/execute' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive' \
--data-raw '{
    "connectorConfig": {
        "type": "HTTP",
        "baseUrl": "http://localhost:8007",
        "headers": {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "WeChatPay-Integration/1.0"
        },
        "authExpression": "${getAccessToken.access_token}",
        "authLocation": "HEADER",
        "authParamName": "Authorization"
    },
    "authRequests":[
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229314",
                "remark": null,
                "code": "getAccessToken",
                "name": "获取访问令牌",
                "description": null,
                "contentType": null,
                "url": "/mock/oauth/token",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "grant_type": "client_credentials",
                    "client_id": "test_client_id",
                    "client_secret": "test_client_secret"
                },
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            },
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229315",
                "remark": null,
                "code": "refreshToken",
                "name": "刷新访问令牌",
                "description": null,
                "contentType": null,
                "url": "/mock/oauth/refresh",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer ${getAccessToken.access_token}"
                },
                "body": {
                    "refresh_token": "${getAccessToken.refresh_token}"
                },
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            },
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229316",
                "remark": null,
                "code": "getUserInfo",
                "name": "获取用户信息",
                "description": null,
                "contentType": null,
                "url": "/mock/user/info",
                "method": "GET",
                "headers": {
                    "Authorization": "Bearer ${refreshToken.access_token}",
                    "Accept": "application/json"
                },
                "body": null,
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            }
        ]
}'
```


## 测试提取

```bash
curl --location --request POST 'http://localhost:8007/integration/http/connector/test/auth/token/extract' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive' \
--data-raw '{
    "connectorConfig": {
        "type": "HTTP",
        "baseUrl": "http://localhost:8007",
        "headers": {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "WeChatPay-Integration/1.0"
        },
        "authExpression": "${getAccessToken.access_token}",
        "authLocation": "HEADER",
        "authParamName": "Authorization"
    },
    "authRequests":[
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229314",
                "remark": null,
                "code": "getAccessToken",
                "name": "获取访问令牌",
                "description": null,
                "contentType": null,
                "url": "/mock/oauth/token",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "grant_type": "client_credentials",
                    "client_id": "test_client_id",
                    "client_secret": "test_client_secret"
                },
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            },
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229315",
                "remark": null,
                "code": "refreshToken",
                "name": "刷新访问令牌",
                "description": null,
                "contentType": null,
                "url": "/mock/oauth/refresh",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer ${getAccessToken.access_token}"
                },
                "body": {
                    "refresh_token": "${getAccessToken.refresh_token}"
                },
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            },
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229316",
                "remark": null,
                "code": "getUserInfo",
                "name": "获取用户信息",
                "description": null,
                "contentType": null,
                "url": "/mock/user/info",
                "method": "GET",
                "headers": {
                    "Authorization": "Bearer ${refreshToken.access_token}",
                    "Accept": "application/json"
                },
                "body": null,
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            }
        ]
}'
```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "chainExecutionResults": {
            "getAccessToken": {
                "access_token": "mock_access_token_431396bb",
                "refresh_token": "mock_refresh_token_b40210e4",
                "scope": "read write",
                "token_type": "Bearer",
                "expires_in": 3600,
                "issued_at": "2025-08-02T11:37:53.5207372"
            },
            "refreshToken": {
                "access_token": "mock_new_access_token_6f10a70f",
                "refresh_token": "mock_new_refresh_token_d7bf09d1",
                "refreshed_at": "2025-08-02T11:37:53.533678",
                "scope": "read write",
                "token_type": "Bearer",
                "expires_in": 3600
            },
            "getUserInfo": {
                "full_name": "Test User",
                "user_id": "user_12345",
                "last_login": "2025-08-02T11:37:53.5387501",
                "roles": [
                    "user",
                    "tester"
                ],
                "created_at": "2024-01-01T00:00:00",
                "avatar": "https://example.com/avatar.jpg",
                "email": "<EMAIL>",
                "username": "test_user"
            }
        },
        "authExpression": "${getAccessToken.access_token}",
        "extractedToken": "mock_access_token_431396bb",
        "success": true,
        "errorMessage": null,
        "executionTime": "2025-08-02 11:37:53",
        "executionDurationMs": 24,
        "executedRequestCount": 3
    },
    "success": true
}
```

## 保存或更新认证配置

```bash
curl --location --request POST 'http://localhost:8007/integration/http/connector/saveOrUpdate' \
--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
--header 'Content-Type: application/json' \
--header 'Accept: */*' \
--header 'Host: localhost:8007' \
--header 'Connection: keep-alive' \
--data-raw '{
        "createBy": 1001,
        "updateBy": null,
        "createTime": "2025-08-01 10:00:00",
        "updateTime": null,
        "id": 1,
        "remark": "微信支付官方API连接器",
        "code": "wechat_pay_connector",
        "name": "微信支付连接器",
        "type": "HTTP",
        "description": "用于处理微信支付相关的API调用，包括统一下单、查询订单、退款等功能",
        "enabled": true,
        "authActuator": "wechatTokenExtractor",
        "tokenCacheSeconds": 7200,
        "config": {
            "type": "HTTP",
            "baseUrl": "https://api.mch.weixin.qq.com",
            "authExpression": "${getAccessToken.access_token}",
            "authLocation": "HEADER",
            "authParamName": "Authorization"
        },
        "authRequests": [
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229314",
                "remark": null,
                "code": "getAccessToken",
                "name": "获取访问令牌",
                "description": null,
                "contentType": null,
                "url": "/mock/oauth/token",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "grant_type": "client_credentials",
                    "client_id": "test_client_id",
                    "client_secret": "test_client_secret"
                },
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            },
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229315",
                "remark": null,
                "code": "refreshToken",
                "name": "刷新访问令牌",
                "description": null,
                "contentType": null,
                "url": "/mock/oauth/refresh",
                "method": "POST",
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer ${getAccessToken.access_token}"
                },
                "body": {
                    "refresh_token": "${getAccessToken.refresh_token}"
                },
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            },
            {
                "createBy": null,
                "updateBy": null,
                "createTime": "2025-08-02 10:08:02",
                "updateTime": null,
                "id": "1951461376889229316",
                "remark": null,
                "code": "getUserInfo",
                "name": "获取用户信息",
                "description": null,
                "contentType": null,
                "url": "/mock/user/info",
                "method": "GET",
                "headers": {
                    "Authorization": "Bearer ${refreshToken.access_token}",
                    "Accept": "application/json"
                },
                "body": null,
                "connectorId": 1,
                "type": "AUTH",
                "enabled": true
            }
        ]
    }'

```

```json
{
    "code": 200,
    "msg": "操作成功",
    "data": true,
    "success": true
}
```