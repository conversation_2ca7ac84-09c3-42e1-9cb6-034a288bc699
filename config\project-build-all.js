const projects = require('./project').targetNames || []

const { execSync } = require('child_process');

console.log(`打包项目：${projects.join(',')}`)
for (const module of projects) {
  console.log(`\n************开始打包项目: ${module} ************`)
  let cmd = ''
  try {
    let libConfig = require(`../projects/${module}/lib.config.js`)
    console.log(`项目libs配置读取: `, libConfig)
    let libs = libConfig.libs
    if (libs && libs.length > 0) {
      cmd = `-- --libs=${libs.join(',')}`
    }
  } catch (err) {
    console.warn(`项目未配置lib.config.js文件`)
  }
  execSync(`npm run build ${module} ${cmd}`)
  console.log(`************项目打包完成: ${module} ************`)
}
