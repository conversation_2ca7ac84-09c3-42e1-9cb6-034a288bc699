import SparkMD5 from "spark-md5";
import { fileSuffixTypeUtil } from "./FileUtil";
import axios from "axios";

// * @param {Function} checkUpload 检查上传函数
// * @param {Function} initUpload 初始化上传函数
// * @param {Function} mergeUpload 合并上传函数
import { checkUpload, initUpload, mergeUpload } from "./upload";

/**
 * 单个文件上传助手类
 * 处理单个文件的上传，并提供进度回调
 */
class UploadHelper {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     * @param {Number} chunkSize 分片大小（字节）
     * @param {Number} simultaneousUploads 允许并发数
     * @param {Number} maxSize 最大文件大小（字节）
     */
    constructor(options = {}) {
        this.chunkSize = options.chunkSize || 10 * 1024 * 1024;
        this.simultaneousUploads = options.simultaneousUploads || 3;
        this.maxSize = options.maxSize || 50 * 1024 * 1024;

        // 验证必要的函数是否提供
        if (!checkUpload || !initUpload || !mergeUpload) {
            throw new Error('必须提供 checkUpload, initUpload 和 mergeUpload 函数');
        }
    }

    /**
     * 上传文件
     * @param {File} file 要上传的文件
     * @param {Function} onProgress 进度回调函数 (progress, url) => {}
     * @param {Function} onSuccess 成功回调函数 (url) => {}
     * @param {Function} onError 错误回调函数 (error) => {}
     */
    upload(file, onProgress, onSuccess, onError) {
        // 验证文件
        if (!this._validateFile(file, onError)) {
            return;
        }

        // 开始上传流程
        this._processFile(file, onProgress, onSuccess, onError);
    }

    /**
     * 验证文件
     * @private
     * @param {File} file 文件对象
     * @param {Function} onError 错误回调
     * @returns {Boolean} 是否验证通过
     */
    _validateFile(file, onError) {
        // 验证文件大小
        if (file.size > this.maxSize) {
            const error = new Error(`文件大小超过限制 ${this._formatSize(this.maxSize)}`);
            onError(error);
            return false;
        }
        return true;
    }

    /**
     * 处理文件上传
     * @private
     * @param {File} file 文件对象
     * @param {Function} onProgress 进度回调
     * @param {Function} onSuccess 成功回调
     * @param {Function} onError 错误回调
     */
    async _processFile(file, onProgress, onSuccess, onError) {
        try {
            // 2. 计算文件MD5
            this.sumChunks = 0;
            onProgress && onProgress(0);
            const fileMD5 = await this._calculateFileMd5(file, (maxChunks) => {
                this.sumChunks = maxChunks;
            }).then((md5) => {
                return md5;
            }).catch((err) => {
                console.error("Error calculating MD5:", err); // 捕获并处理错误
            });
            onProgress && onProgress(5);
            // 3. 校验文件是否已存在 若存在则直接返回
            const verifyHasResult = await this._verifyFileHasMD5(fileMD5, file.name);
            // 4.根据后台返回，设置分片大小
            this.chunkSize = verifyHasResult.data.chunkSize
                ? verifyHasResult.data.chunkSize * 1024 * 1024
                : 10 * 1024 * 1024;
            // 结束  code: 1已上传 直接返回url code:2上传中【断点续传】 code: 3未上传
            if (verifyHasResult.code === 1) {
                // console.log("上传成功文件访问地址+秒传：" + verifyHasResult.data.url);
                onProgress && onProgress(100);
                onSuccess && onSuccess(verifyHasResult.data.url, { fileName: file.name });
                return
            }
            if (verifyHasResult.code === 2) {
                //断点续传
            }
            // 4. 获取上传url
            // console.log("this.sumChunks", this.sumChunks);
            const initResult = await this._initMultipartUpload(file, fileMD5);
            const uploadId = initResult.uploadId;
            // 5. 分片上传
            const chunks = this._createFileChunks(file);
            const chunkList = chunks.map((chunk, index) => ({
                chunk: chunk,
                chunkNumber: index + 1,
                uploadUrl: initResult.urlList[index],
                progress: 0,
            }));
            file.chunkUploadedList = [];
            // 分片上传
            let result = await this._uploadChunks(chunkList, (progress) => {
                onProgress && onProgress(progress * 0.9);
            })
            if (uploadId === "SingleFileUpload") {
                onProgress && onProgress(100);
                onSuccess && onSuccess(initResult.url, { fileName: file.name })
            } else {
                // 6. 合并文件
                let mergeResult = await this._mergeChunkRequest(
                    initResult.uploadId,
                    initResult.urlList.length,
                    file.name,
                    fileMD5
                );
                // 7.重置分块属性
                this.sumChunks = 0;
                // 8. 返回文件路径
                onProgress && onProgress(100)
                onSuccess && onSuccess(mergeResult, { fileName: file.name })
                return
            }
        } catch (error) {
            console.error('文件上传失败:', error);
            onError && onError(error);
        }
    }

    /**
     * 计算文件MD5
     * @private
     * @param {File} file 文件对象
     * @param {Function} onProgress 进度回调
     * @returns {Promise<String>} MD5值
     */
    async _calculateFileMd5(file, callback) {
        const blobSlice =
            File.prototype.slice ||
            File.prototype.mozSlice ||
            File.prototype.webkitSlice;
        let currChunkSize = this.chunkSize;
        return new Promise((resolve, reject) => {
            const spark = new SparkMD5.ArrayBuffer();
            const reader = new FileReader();
            let maxChunks = Math.ceil(file.size / currChunkSize) || 0;
            callback(maxChunks);
            let currentChunk = 0;
            reader.onload = (e) => {
                spark.append(e.target.result);
                currentChunk++;
                if (currentChunk < maxChunks) {
                    loadNext();
                } else {
                    resolve(spark.end());
                }
            };

            reader.onerror = () => {
                console.log("tag", "读取Md5失败，文件读取错误");
                reject;
            };

            function loadNext() {
                const start = currentChunk * currChunkSize;
                const end =
                    start + currChunkSize >= file.size
                        ? file.size
                        : start + currChunkSize;
                // 注意这里的 fileRaw
                reader.readAsArrayBuffer(blobSlice.call(file, start, end));
            }
            loadNext();
        });
    }

    // ====校验文件是否已上传
    async _verifyFileHasMD5(fileMD5, fileName) {
        try {
            const params = {
                fileName: fileName,
            };
            const result = await checkUpload(fileMD5, params);
            return result;
        } catch (error) {
            console.error("校验文件失败:", error);
            throw error;
        }
    }

    // ====初始化分片上传
    async _initMultipartUpload(file, fileMD5) {
        try {
            let type = fileSuffixTypeUtil(file.name);
            let data = {
                fileName: file.name,
                fileSize: file.size,
                chunkNum: this.sumChunks,
                fileMd5: fileMD5,
                contentType: "application/octet-stream",
                fileType: type,
                chunkUploadedList: file.chunkUploadedList,
                // chunkUploadedList: file.chunkUploadedList, //已上传的分片索引+1
            };
            const result = await initUpload(data);
            return result;
        } catch (error) {
            console.error("初始化上传失败:", error);
            throw error;
        }
    }

    // 文件分片
    _createFileChunks(file, size = this.chunkSize) {
        const fileChunkList = [];
        let count = 0;
        while (count < file.size) {
            fileChunkList.push({
                file: file.slice(count, count + size),
            });
            count += size;
        }
        return fileChunkList;
    }

    /**
     * 上传分片
     * @private
     * @param {Blob} chunk 分片数据
     * @returns {Promise} 上传结果
     */
    // 上传分片
    async _uploadChunks(chunkList, onProgress = () => { }) {
        let successCount = 0;
        let lastResponse = null;
        const totalChunks = chunkList.length;
        let chunkProgress = new Array(totalChunks).fill(0); // 每个分片的进度
        return new Promise((resolve, reject) => {
            const uploadHandler = () => {
                if (chunkList.length) {
                    const chunkItem = chunkList.shift();
                    axios
                        .put(chunkItem.uploadUrl, chunkItem.chunk.file, {
                            headers: {
                                "Content-Type": "application/octet-stream",
                            },
                            onUploadProgress: (progressEvent) => {
                                chunkProgress[chunkItem.chunkNumber - 1] = (progressEvent.loaded / progressEvent.total) * 100;
                                let totalProgress = chunkProgress.reduce((acc, curr) => acc + curr, 0) / totalChunks;
                                onProgress(parseInt(totalProgress));
                            },
                        })
                        .then((response) => {
                            if (response.status === 200) {
                                successCount++;
                                if (successCount >= totalChunks) {
                                    lastResponse = response;
                                    resolve(lastResponse);
                                } else {
                                    // 继续上传下一个分片
                                    uploadHandler();
                                }
                            } else {
                                // console.error(
                                //     `分片 ${chunkItem.chunkNumber} 上传失败:`,
                                //     response.statusText
                                // );
                                // 重试该分片
                                chunkList.push(chunkItem);
                                uploadHandler();
                            }
                        })
                        .catch((error) => {
                            console.error(`分片 ${chunkItem.chunkNumber} 上传出错:`, error);
                            // 重试该分片
                            // chunkList.push(chunkItem);
                            // uploadHandler();
                            reject(error)
                        });
                }

                if (successCount >= totalChunks) {
                    console.log("所有分片上传成功");
                    onProgress(100);
                }
            };
            // 启动并发上传
            for (let i = 0; i < this.simultaneousUploads; i++) {
                uploadHandler();
            }
        });
    }

    // ====合并分片
    async _mergeChunkRequest(uploadId, length, fileName, fileMD5) {
        try {
            let type = fileSuffixTypeUtil(fileName);
            const data = {
                uploadId: uploadId,
                fileName: fileName,
                fileMd5: fileMD5,
                fileType: type,
                chunkNum: length,
            };
            const result = await mergeUpload(data);
            return result;
        } catch (error) {
            console.error("合并文件失败:", error);
            throw error;
        }
    }

    /**
     * 格式化文件大小
     * @private
     * @param {Number} size 文件大小（字节）
     * @returns {String} 格式化后的大小
     */
    _formatSize(size) {
        if (!size) return '0 B';

        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let index = 0;
        while (size >= 1024 && index < units.length - 1) {
            size /= 1024;
            index++;
        }

        return `${size.toFixed(2)} ${units[index]}`;
    }
}

export default UploadHelper;