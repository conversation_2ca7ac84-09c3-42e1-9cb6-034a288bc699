export default {
  api:'sys/dept',
  search: {
    isShow: true,
    showReset: true
  },
  table: {
    isHasChildren: true
  },
  formRule: [
    {
      type: "hidden",
      field: "id",
      className: "id-dom",
      title: "id",
      value: null,
      isSearch: false,
      isTable: false,
      isScope: false
    },
    {
      type: "input",
      field: "name",
      className: "name-dom",
      title: "部门名称",
      value: '',
      isSearch: true,
      isSearchValidate: [],
      isSearchCol:{md:{span:20}},
      isTable: true,
      isScope: false,

      props: {
        placeholder: "请输入部门名称",
        disabled: false,
        readonly: false,
        clearable: true
      },
      validate: [
        {trigger: 'blur',required: true,message: '部门名称不能为空'}
      ],
      col: {md: {span: 18}}
    },
    {
      type: "treeSelect",
      field: "parentId",
      className: "parentId-dom",
      title: "上级部门",
      value: 0,
      isSearch: false,
      isTable: false,
      isScope: false,
      isSearchValidate: [],
      isSearchCol: {
        md: { span: 10 }
      },
      options: [],
      props: {
        multiple: false,
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true
      },
      col: {
        md: { span: 18 }
      }
    },
    {
      type: "select",
      field: "leaderId",
      className: "leaderId-dom", // 设置组件的class属性
      title: "负责人", // 组件的名称, 选填
      isSearch: false,
      isTable: false,
      props: {
        placeholder: "请选择",
        disabled: false,
        readonly: false,
        clearable: true
      },
      options: [],
      isScope: false,
      col: {md: {span: 18}}
    },
    {
      type: "input",
      field: "orderNum",
      className: "orderNum-dom", // 设置组件的class属性
      title: "排序", // 组件的名称, 选填
      isSearch: false,
      isTable: true,
      props: {
        placeholder: "请输入排序",
        disabled: false,
        readonly: false,
        clearable: true
      },
      isScope: false,
      col: {md: {span: 18}}
    },

    {
      type: "radio",
      field: "valid",
      title: "状态",
      isSearch: false,
      isTable: true,
      col: {md: {span: 18}},
      props: {
        disabled: false,
        readonly: false,
      },
      options: [
        {
          value: true,
          label: "正常",
          disabled: false
        },
        {
          value: false,
          label: "停用",
          disabled: false
        }
      ],
      value: null
    },
    // {
    //   type: "select",
    //   field: "valid",
    //   className: "valid-dom",
    //   title: "部门状态",
    //   isSearch: true,
    //   isSearchValidate: [],
    //   isSearchCol: {md: {span: 20}},
    //   options: [
    //     {value: true, label: "正常", disabled: false},
    //     {value: false, label: "停用", disabled: false}
    //   ],
    //   isTable: true,
    //   col: {md: {span: 18}},
    //   props: {
    //     placeholder: "请选择部门状态",
    //     activeValue:1,
    //     inactiveValue:0,
    //     clearable: true
    //   },
    //   validate: [
    //     {
    //       trigger: 'change',
    //       required: true,
    //       message: "请选择是否有效"
    //     }
    //   ],
    // }
    {
      type: "input",
      field: "remark",
      className: "remark-dom",
      title: "备注",
      isSearch: false,
      isTable: false,
      col: {md: {span: 18}},
      props: {
        type: "textarea",
        placeholder: "请输入备注",
        readonly: false,
        clearable: true,
        disabled: false
      },
    }
  ]
}
