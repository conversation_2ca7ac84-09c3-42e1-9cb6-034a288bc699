<template>
    <div>
        <div :class="['msg-wrapper', item.type === 'user' ? 'user' : '']" v-for="(item, index) in messages"
            :key="index">
            <template v-if="item.type === 'user'">
                <div class="user-content" v-html="renderMarkdown(item.content)"></div>
            </template>
            <template v-if="item.type === 'ai'">
                <img class=" ai-avatar" src="./icon-ai.gif">
                <div class="ai-content" v-loading="!item.content">
                    <div v-html="renderMarkdown(item.content)"></div>


                    <div class="retriever" v-if="item?.retriever_resources?.length > 0">
                        <div class="t">
                            <div>引用</div>
                            <div class="line"></div>
                        </div>
                        <div class="retriever_resources">

                            <el-popover v-for="(res_item, res_index) in retrieverGroupBy(item.retriever_resources)"
                                :key="`${index}-${res_index}`" placement="top" trigger="click">
                                <div slot="reference" class="retriever_resources_item">
                                    <div class="document_name" v-text="res_item[0].document_name"></div>
                                </div>
                                <div class="retriever_resources_content">
                                    <div v-for="(res_item_item, res_index_index) in res_item"
                                        :key="`${index}-${res_index}-${res_index_index}`">
                                        <div>
                                            <div class="num"><span>#</span>{{ res_index_index + 1 }}</div>
                                        </div>
                                        <div class="hc" v-html="res_item_item.content"></div>
                                        <div v-if="res_index_index < res_item.length - 1" class="line"></div>
                                    </div>
                                </div>
                            </el-popover>
                        </div>
                    </div>

                    <el-button v-if="index == messages.length - 1 && item.content" class="ai-button" round
                        v-text="item.task_id ? '停止生成' : '重新生成'" @click="$emit('ai-event')"></el-button>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
import lodash from 'lodash'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// 配置 marked 选项
const renderer = new marked.Renderer()

// 自定义代码块渲染
renderer.code = (code) => {
    try {
        const validLanguage = code.lang || 'plaintext'
        const highlightedCode = hljs.highlight(code.text, { language: validLanguage }).value

        return `<div class="marked-code-block">
              <div class="marked-code-header">
                <span class="marked-code-language">${validLanguage}</span>
                <button class="marked-copy-button" onclick="copyCode(this)">
                  <span class="marked-copy-icon"></span>
                  <span>复制代码</span>
                </button>
              </div>
              <pre class="marked-code-pre"><code class="hljs ${validLanguage}">${highlightedCode}</code></pre>
            </div>`
    } catch (error) {
        console.error('代码高亮失败：', error)
        return `<pre><code>${code.text}</code></pre>`
    }
}

import { Message } from 'element-ui';

window.copyCode = async (button) => {
    const pre = button.closest('.marked-code-block').querySelector('pre')
    const code = pre.textContent
    try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            await navigator.clipboard.writeText(code)
        } else {
            const textarea = document.createElement('textarea')
            textarea.value = code
            textarea.style.position = 'fixed'
            textarea.style.opacity = '0'
            document.body.appendChild(textarea)
            textarea.select()
            document.execCommand('copy')
            document.body.removeChild(textarea)
        }
        Message.success('复制成功');
    } catch (err) {
        Message.error('复制失败');
    }
}

marked.setOptions({
    renderer,
    breaks: true,
    gfm: true,
    headerIds: false,
    mangle: false,
    highlight: function (code, lang) {
        try {

            const language = hljs.getLanguage(lang) ? lang : 'plaintext'
            return hljs.highlight(code, { language }).value
        } catch (error) {
            console.error('代码高亮失败：', error)
            return code
        }
    }
})

export default {
    props: {
        messages: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            msgs: []
        }
    },
    methods: {
        renderMarkdown(content) {
            try {
                return marked(content || '')
            } catch (error) {
                console.error('Markdown 渲染失败：', error)
                return content
            }
        },

        retrieverGroupBy(resources) {
            return lodash.groupBy(resources, 'document_id')
        }
    }
}
</script>
<style lang="scss" scoped>
.msg-wrapper {
    display: flex;
    justify-content: flex-start;
    padding: 8px 16px;

    &.user {
        justify-content: flex-end;
    }

    .ai-avatar {
        width: 20px;
        height: 20px;
    }

    .ai-content {
        margin-left: 7px;
        padding: 10px;
        border-radius: 6px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(230, 222, 220, 1);
        max-width: 270px;
        font-size: 14px;
        color: rgba(56, 56, 56, 1);
        transition: all 0.2s ease;

        .ai-button {
            color: rgba(250, 100, 0, 1);
            font-size: 12px;
            padding: 6px 10px;
            border-color: rgba(255, 105, 108, 1);
        }

        ::v-deep .el-loading-mask {
            background-color: transparent;

            .el-loading-spinner {
                margin-top: 0px;
                transform: translateY(-50%);

                .circular {
                    width: 12px;
                    height: 12px;
                }
            }
        }

        .retriever {
            margin-bottom: 10px;

            .t {
                font-size: 12px;
                color: rgb(102, 112, 133);
                display: flex;
                align-items: center;

                .line {
                    background-color: rgba(0, 0, 0, 0.05);
                    height: 1px;
                    margin-left: 8px;
                    flex: 1;
                }
            }

            .retriever_resources {
                display: flex;
                flex-wrap: wrap;
                margin-top: 8px;

                .retriever_resources_item {
                    padding: 3px 6px;
                    background-color: rgb(240, 248, 255);
                    margin: 2px;
                    border-radius: 4px;
                }

                .document_name {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 250px;
                    color: rgb(52, 64, 84);
                    font-size: 12px;
                }
            }
        }

    }

    .user-content {
        margin-right: 24px;
        padding: 10px;
        background: linear-gradient(270deg, rgba(255, 103, 108, 1) 0%, rgba(255, 188, 101, 1) 100%);
        clip-path: inset(0% 0% 0% 0% round 20px 20px 0% 20px);
        max-width: 270px;
        font-size: 14px;
        color: white;

        ::v-deep p {
            margin-block-start: 0;
            margin-block-end: 0;
        }
    }
}
</style>
<style lang="scss">
.marked-code-block {
    margin-top: 0px;
}

.marked-code-pre {
    margin-top: 0px;
}

.marked-code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: #30313c;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.marked-code-language {
    font-size: 16px;
    color: white;
}

.marked-copy-button {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #a2a2a4;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 30px;
}

.marked-copy-button:hover {
    color: white;
}

.marked-copy-icon {
    width: 20px;
    height: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23f8f8f2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z'%3E%3C/path%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    padding-right: 10px;
}

.retriever_resources_content {
    max-width: 270px;
    max-height: 400px;
    overflow-y: scroll;

    .num {
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, .05);
        display: inline-block;
        padding: 0px 6px;
        margin-top: 12px;
        color: rgb(0, 0, 0);
        font-size: 13px;
        line-height: 16px;

        span {
            color: rgb(152, 162, 179);
            font-size: 14px;
            padding-right: 2px;
        }
    }

    .hc {
        font-size: 13px;
        color: rgb(29, 41, 57);
        line-height: 18px;
        margin-top: 12px;
    }

    .line {
        background-color: rgba(0, 0, 0, .05);
        height: 1px;
        margin-top: 12px;
    }
}
</style>