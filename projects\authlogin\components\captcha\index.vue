<template>
  <component :is="captcha.component" @refresh="refresh" :codeResult="codeResult" v-model="value" ref="captcha" @mounted="bindFormItem" />
</template>

<script>
import captcha from "./captcha.js"
import { code } from "@/api/login/login";
export default {
  name: "Captcha",
  data() {
    return {
      codeResult: {},
      value: undefined
    }
  },
  props: {
    type: {
      type: String,
      default: "text",
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  watch: {
    type(val) {
      val && this.$nextTick(this.bindFormItem)
    }
  },
  computed: {
    captcha() {
      return captcha[this.type]
    },
    result() {
      return `${this.codeResult.uuid}:${this.value}`
    }
  },
  methods: {
    bindFormItem() {
      let elFormItemt = this.$parent
      let captcha = this.$refs.captcha
      if ("el-form-item" === elFormItemt.$options._componentTag && captcha?.rules) {
        elFormItemt.elForm.rules[elFormItemt.prop] = captcha.rules
      }
    },
    refresh() { 
      code(this.type).then(res => { 
        this.codeResult = res
      })
    },
    complete(success, message) {
      let captcha = this.$refs.captcha
      if (success) {
        captcha.onSuccess && captcha.onSuccess()
      } else {
        captcha.onError && captcha.onError(message)
      }
    }
  }
}
</script>