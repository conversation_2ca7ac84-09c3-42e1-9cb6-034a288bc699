import request from '@/utils/request'

const integrationHttpConnectorApi = CONSTANT.INTEGRATION + '/integration/http/connector'
const integrationConnectorApi = CONSTANT.INTEGRATION + '/integration/connector'
const integrationHttpRequestApi = CONSTANT.INTEGRATION + '/integration/http/request'

// ------------------- HTTP连接器专用接口 -------------------

// 获取HTTP连接器认证详情（包含HTTP请求配置）
export function getHttpConnectorAuthDetail(id) {
  return request({
    url: `${integrationHttpConnectorApi}/auth/detail/${id}`,
    method: 'get'
  })
}

// 获取所有认证执行器
export function getAuthActuators() {
  return request({
    url: `${integrationConnectorApi}/auth/actuators`,
    method: 'get'
  })
}

// 测试HTTP连接器网络连通性
export function testHttpConnectorConnection(url) {
  return request({
    url: `${integrationHttpConnectorApi}/test/connection`,
    method: 'post',
    params: { url }
  })
}

// 测试HTTP连接器链式执行
export function testHttpConnectorAuthChain(data) {
  return request({
    url: `${integrationHttpConnectorApi}/test/auth/execute`,
    method: 'post',
    data
  })
}

// 测试HTTP连接器Token提取
export function testHttpConnectorTokenExtraction(data) {
  return request({
    url: `${integrationHttpConnectorApi}/test/auth/token/extract`,
    method: 'post',
    data
  })
}

// 保存或更新HTTP连接器配置
export function saveOrUpdateHttpConnector(data) {
  return request({
    url: `${integrationHttpConnectorApi}/saveOrUpdate`,
    method: 'post',
    data
  })
}

// 生成雪花算法ID
export function generateSnowflakeId() {
  return request({
    url: `${integrationHttpRequestApi}/snowflake/id`,
    method: 'get'
  })
}

// ------------------- HTTP请求接口管理 -------------------

// 获取HTTP请求列表（分页）
export function getHttpRequestList(params) {
  return request({
    url: `${integrationHttpRequestApi}/list`,
    method: 'get',
    params
  })
}

// 获取单个HTTP请求详情
export function getHttpRequestDetail(id) {
  return request({
    url: `${integrationHttpRequestApi}`,
    method: 'get',
    params: { id }
  })
}

// 创建HTTP请求
export function createHttpRequest(data) {
  return request({
    url: `${integrationHttpRequestApi}`,
    method: 'post',
    data
  })
}

// 更新HTTP请求
export function updateHttpRequest(data) {
  return request({
    url: `${integrationHttpRequestApi}`,
    method: 'put',
    data
  })
}

// 删除HTTP请求
export function deleteHttpRequest(id) {
  return request({
    url: `${integrationHttpRequestApi}/${id}`,
    method: 'delete'
  })
}

// 批量删除HTTP请求
export function batchDeleteHttpRequest(ids) {
  return request({
    url: `${integrationHttpRequestApi}/${ids.join(',')}`, 
    method: 'delete'
  })
}
// 测试HTTP请求
export function testHttpRequest(data) {
  return request({
    url: `${integrationHttpRequestApi}/test`,
    method: 'post',
    data
  })
}

// 执行HTTP请求测试
export function executeHttpRequestTest(data) {
  return request({
    url: `${integrationHttpRequestApi}/test/exec`,
    method: 'post',
    data,
    responseType: 'blob', // 支持二进制响应
    timeout: 30000 // 30秒超时
  })
}

