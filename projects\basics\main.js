import 'core-js/stable'
import 'regenerator-runtime/runtime'
import './modernizr-build.js'

import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import directive from './directive/permission' // directive

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n
// import '@/styles/element-ui/theme/index.css'
// set ElementUI lang to EN
Vue.use(ElementUI, { locale })

import './utils/Dict'

import '@/styles/index.scss' // global css
import { parseTime, resetForm } from "@/utils/index"
import { hasPermi } from './directive/permission/hasPermi'

import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // fixme permission control
import JsEncrypt from 'jsencrypt'
// 头部标签组件
import pagination from '@/components/Pagination'

import directives from './directive' // directive
Vue.use(directives)

import './directive/echarts.js'
import './directive/resize-detector.js'
import './directive/enter.js'
import './directive/visibility.js'
import version from './directive/version'

Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.hasPermi = hasPermi
Vue.prototype.version = version

// 全局方法挂载
Vue.prototype.msgSuccess = function (msg) {
  this.$message({ showClose: true, message: msg, type: "success" });
}

Vue.prototype.msgError = function (msg) {
  this.$message({ showClose: true, message: msg, type: "error" });
}

Vue.prototype.msgInfo = function (msg) {
  this.$message.info(msg);
}

Vue.prototype.confirm = function (msg) {
  return ElementUI.MessageBox.confirm(msg, "系统提示", {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: "warning",
  })
}

// 全局组件挂载
Vue.component('pagination', pagination)
Vue.use(directive)

import AutoPage from './components/auto-page'
Vue.use(AutoPage)


import VueAMap from '@vuemap/vue-amap';
import '@vuemap/vue-amap/dist/style.css'
Vue.use(VueAMap)
VueAMap.initAMapApiLoader({
  key: 'bc2abd9089c81e6b563adbe99cd8b89a',
  securityJsCode: "fb5ea6b6af49efe805a73e771cccf8cf",
})

import UnitConvert from "@/utils/UnitConvert.js"
UnitConvert.viewportUnit = 'vw'
UnitConvert.viewportWidth = 1920
window.px2vwh = UnitConvert.px2vwh
window.vwh2px = UnitConvert.vwh2px
window.rpx = UnitConvert.rpx
Vue.prototype.px2vwh = UnitConvert.px2vwh
Vue.prototype.vwh2px = UnitConvert.vwh2px
Vue.prototype.rpx = UnitConvert.rpx

import { findCurrentRouteInfo } from '@/utils/utils'
Vue.prototype.findCurrentRouteInfo = findCurrentRouteInfo


/**
 * 配置全局的加密方法
 * @param obj 需要加密的字符串
 */
Vue.prototype.$encruption = function (obj) {
  let encrypt = new JsEncrypt()
  // 公钥
  encrypt.setPublicKey(
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDSUi13ttnt7W2IwdE/5vtm6Yup9mEXop42iPn7DSYTZUlb16wLMLdp4nISrwCAliPwoHWBJ67tDGKZY+MCuaddfr7Mc1f1SKtBzuTdxqjnxagpzEFAyP3VerbPz/vdp7WQCbxmox0N/WUSUTfIm2g8yo3TogSJB48gLgbyRgwShwIDAQAB'

  )
  return encrypt.encrypt(obj)
}

Vue.config.productionTip = false

import tab from '@/utils/tab.js'
Vue.prototype.$tab = tab

const vue = new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})



export { vue }

