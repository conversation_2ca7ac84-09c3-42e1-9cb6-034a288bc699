import { BaseEdge, BaseEdgeModel, h } from '@logicflow/core'
import { action, observable } from 'mobx'

/**
 * N次贝塞尔曲线边视图类
 * 基于BaseEdge重新开发，支持任意次数的贝塞尔曲线
 */
export class MultiBezierEdge extends BaseEdge {

  getLastTwoPoints() {
    const { model } = this.props
    const { controlPoints = [] } = model.properties
    if (Array.isArray(controlPoints)) {
      const arr = controlPoints.slice(-2)
      if (arr.length === 2) {
        return arr
      }
    }
    return [null, null]
  }
  /**
   * 获取边的形状
   * 支持N次贝塞尔曲线渲染
   */
  getEdge() {
    const { model } = this.props
    const { path, isAnimation, arrowConfig } = model
    const style = model.getEdgeStyle()
    const animationStyle = model.getEdgeAnimationStyle()
    const {
      strokeDasharray,
      stroke,
      strokeDashoffset,
      animationName,
      animationDuration,
      animationIterationCount,
      animationTimingFunction,
      animationDirection,
    } = animationStyle

    const edgeProps = {
      d: path,
      ...style,
      fill: 'none',
      className: 'lf-edge-path',
      ...arrowConfig,
      ...(isAnimation
        ? {
          strokeDasharray,
          stroke,
          style: {
            strokeDashoffset,
            animationName,
            animationDuration,
            animationIterationCount,
            animationTimingFunction,
            animationDirection,
          },
        }
        : {})
    }



    const edgeShape = h('path', edgeProps)

    const elements = [edgeShape]


    // 添加控制线
    const controlLines = this.getControlLines()
    if (controlLines) {
      elements.push(...controlLines)
    }

    // 添加控制点可视化
    const controlPoints = this.getControlPoints()
    if (controlPoints) {
      elements.push(...controlPoints)
    }

    // 添加悬浮时的加号图标
    const plusIcon = this.getPlusIcon()
    if (plusIcon) {
      elements.push(plusIcon)
    }

    return elements
  }

  /**
   * 获取控制点
   */
  getControlPoints() {
    const { model } = this.props
    const { controlPoints, isSelected, startPoint, endPoint } = model

    // 只有在选中时才显示控制点
    if (!isSelected) return null

    if (!controlPoints || controlPoints.length === 0) return null

    // 显示所有控制点，包括起点和终点
    return controlPoints.map((point, index) => {
      // 为起点和终点使用不同的样式
      const isEndpoint = index === 0 || index === controlPoints.length - 1

      // 对于端点，使用实时的startPoint和endPoint坐标
      let actualPoint = point
      if (index === 0 && startPoint) {
        actualPoint = startPoint
      } else if (index === controlPoints.length - 1 && endPoint) {
        actualPoint = endPoint
      }

      return h('circle', {
        cx: actualPoint.x,
        cy: actualPoint.y,
        r: isEndpoint ? 3 : 4,
        fill: isEndpoint ? '#ff4d4f' : '#1890ff',
        stroke: '#fff',
        strokeWidth: 2,
        className: isEndpoint ? 'lf-bezier-endpoint' : 'lf-bezier-control-point',
        style: {
          cursor: isEndpoint ? 'default' : 'move'
        },
        onMouseDown: (e) => {
          if (!isEndpoint) {
            this.onControlPointMouseDown(e, index)
          }
        },
        onMouseEnter: (e) => {
          this.onControlPointMouseEnter(e)
        },
        onMouseLeave: (e) => {
          this.onControlPointMouseLeave(e)
        },
        onContextMenu: (e) => {
          if (!isEndpoint) {
            this.onControlPointRightClick(e, index)
          }
        }
      })
    })
  }

  /**
   * 获取控制点连接线
   */
  getControlLines() {
    const { model } = this.props
    const { controlPoints, isSelected, startPoint, endPoint } = model

    // 只有在选中时才显示控制线
    if (!isSelected || controlPoints.length < 3) return null

    const lines = []

    // 确保使用最新的起点和终点位置
    const actualStartPoint = startPoint || controlPoints[0]
    const actualEndPoint = endPoint || controlPoints[controlPoints.length - 1]

    // 绘制从起点到第一个控制点的连接线
    if (controlPoints.length > 2) {
      lines.push(h('line', {
        x1: actualStartPoint.x,
        y1: actualStartPoint.y,
        x2: controlPoints[1].x,
        y2: controlPoints[1].y,
        stroke: '#1890ff',
        strokeWidth: 2,
        strokeDasharray: '3,3',
        className: 'lf-bezier-control-line',
        'data-line-index': 0,
        onMouseEnter: (e) => this.onControlLineMouseEnter(e),
        onMouseLeave: (e) => this.onControlLineMouseLeave(e),
      }))
    }

    // 绘制控制点之间的连接线
    for (let i = 1; i < controlPoints.length - 2; i++) {
      lines.push(h('line', {
        x1: controlPoints[i].x,
        y1: controlPoints[i].y,
        x2: controlPoints[i + 1].x,
        y2: controlPoints[i + 1].y,
        stroke: '#1890ff',
        strokeWidth: 2,
        strokeDasharray: '3,3',
        className: 'lf-bezier-control-line',
        'data-line-index': i,
        onMouseEnter: (e) => this.onControlLineMouseEnter(e),
        onMouseLeave: (e) => this.onControlLineMouseLeave(e),
      }))
    }

    // 绘制从最后一个控制点到终点的连接线
    if (controlPoints.length > 2) {
      const lastControlIndex = controlPoints.length - 2
      lines.push(h('line', {
        x1: controlPoints[lastControlIndex].x,
        y1: controlPoints[lastControlIndex].y,
        x2: actualEndPoint.x,
        y2: actualEndPoint.y,
        stroke: '#1890ff',
        strokeWidth: 2,
        strokeDasharray: '3,3',
        className: 'lf-bezier-control-line',
        'data-line-index': lastControlIndex,
        onMouseEnter: (e) => this.onControlLineMouseEnter(e),
        onMouseLeave: (e) => this.onControlLineMouseLeave(e)
      }))
    }

    return lines
  }

  /**
   * 获取悬浮时显示的加号图标
   */
  getPlusIcon() {
    const { model } = this.props
    const { hoveredLineIndex, hoveredLinePosition, isSelected } = model

    if (!isSelected || hoveredLineIndex === -1) {
      if (!isSelected && hoveredLineIndex !== -1) {
        model.setHoveredLine(-1)
      }
      return null
    }

    const { x, y } = hoveredLinePosition
    const iconSize = 12
    const strokeWidth = 2

    // 创建加号图标组
    return h('g', {
      className: 'lf-bezier-plus-icon',
      style: {
        cursor: 'pointer'
      },
      onMouseEnter: () => model.setControlHovered(true),
      onMouseLeave: () => model.setControlHovered(false),
      onClick: (e) => this.onPlusIconClick(e, hoveredLineIndex, x, y)
    }, [
      h('circle', {
        cx: x,
        cy: y,
        r: iconSize / 2 + 2,
        fill: '#fff',
        stroke: '#1890ff',
        strokeWidth: 1,
        opacity: 0.9
      }),
      h('line', {
        x1: x - iconSize / 3,
        y1: y,
        x2: x + iconSize / 3,
        y2: y,
        stroke: '#1890ff',
        strokeWidth: strokeWidth,
        strokeLinecap: 'round'
      }),
      h('line', {
        x1: x,
        y1: y - iconSize / 3,
        x2: x,
        y2: y + iconSize / 3,
        stroke: '#1890ff',
        strokeWidth: strokeWidth,
        strokeLinecap: 'round'
      })
    ])
  }

  /**
   * 加号图标点击事件
   */
  onPlusIconClick(e, lineIndex, x, y) {
    e.stopPropagation()
    const { model } = this.props
    // 在点击位置添加控制点
    model.addControlPoint(x, y, lineIndex + 1)

    model.setHoveredLine(-1)
  }

  onControlLineMouseEnter(e) {
    const { model } = this.props
    const line = e.target

    // 获取线段索引
    const lineIndex = parseInt(line.getAttribute('data-line-index') || '0')

    // 计算线段中点位置
    const x1 = parseFloat(line.getAttribute('x1'))
    const y1 = parseFloat(line.getAttribute('y1'))
    const x2 = parseFloat(line.getAttribute('x2'))
    const y2 = parseFloat(line.getAttribute('y2'))
    const midX = (x1 + x2) / 2
    const midY = (y1 + y2) / 2

    model.setControlHovered(true)
    model.setHoveredLine(lineIndex, { x: midX, y: midY })

    if (line) {
      line.setAttribute('stroke-width', '3')
    }
  }

  onControlLineMouseLeave(e) {
    const { model } = this.props
    model.setControlHovered(false)

    const line = e.target
    if (line) {
      line.setAttribute('stroke-width', '2')
    }
  }

  /**
   * 控制点鼠标按下事件
   */
  onControlPointMouseDown(e, index) {
    const { model, graphModel } = this.props

    e.preventDefault()
    e.stopPropagation()

    const startX = e.clientX
    const startY = e.clientY
    const startPoint = { ...model.controlPoints[index] }

    const onMouseMove = (moveEvent) => {
      const deltaX = moveEvent.clientX - startX
      const deltaY = moveEvent.clientY - startY

      model.setHoveredLine(-1)

      const newPoint = {
        x: startPoint.x + deltaX,
        y: startPoint.y + deltaY
      }

      // 更新控制点位置
      model.controlPoints[index] = newPoint
      model.updatePath()

      // 触发重新渲染
      graphModel.eventCenter.emit('graph:updated', {
        type: 'edge:update',
        data: model.getData()
      })
    }

    const onMouseUp = () => {
      document.removeEventListener('mousemove', onMouseMove)
      document.removeEventListener('mouseup', onMouseUp)
    }

    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
  }

  /**
   * 控制点鼠标进入事件
   */
  onControlPointMouseEnter(e) {
    e.stopPropagation()

    const { model } = this.props

    model.setControlHovered(true)

    // 可以在这里添加视觉反馈，比如改变控制点样式
    const controlPoint = e.target
    if (controlPoint) {
      controlPoint.setAttribute('r', '6') // 增大半径
      controlPoint.setAttribute('fill', '#40a9ff') // 改变颜色
    }
  }

  /**
   * 控制点鼠标离开事件
   */
  onControlPointMouseLeave(e) {
    e.stopPropagation()

    const { model } = this.props

    model.setControlHovered(false)
    // 恢复控制点原始样式
    const controlPoint = e.target
    if (controlPoint) {
      controlPoint.setAttribute('r', '4') // 恢复原始半径
      controlPoint.setAttribute('fill', '#1890ff') // 恢复原始颜色
    }
  }

  /**
   * 控制点右键点击事件
   */
  onControlPointRightClick(e, index) {
    e.preventDefault()
    e.stopPropagation()

    const { model } = this.props

    // 确保不是端点（起点和终点不能删除）
    if (index === 0 || index === model.controlPoints.length - 1) {
      return
    }

    // 确保至少保留4个控制点（起点、终点和至少2个中间控制点）
    if (model.controlPoints.length <= 4) {
      console.warn('无法删除控制点：至少需要保留4个控制点')
      return
    }

    model.removeControlPoint(index)
  }

  /**
   * 获取边的选择区域
   */
  getAppendWidth() {
    const { model } = this.props
    const { path } = model
    return h('path', {
      d: path,
      strokeWidth: 15,
      stroke: 'transparent',
      fill: 'none',
      className: 'lf-edge-append-width'
    })
  }

  /**
   * 自定义控制点的渲染样式
   */
  getAdjustPointShape(x, y, model) {
    const { graphModel } = model
    const { theme } = graphModel
    const { edgeAdjust } = theme

    return h('circle', {
      cx: x,
      cy: y,
      r: edgeAdjust.r || 4,
      fill: edgeAdjust.fill || '#1e6fff',
      stroke: edgeAdjust.stroke || '#ffffff',
      strokeWidth: edgeAdjust.strokeWidth || 2,
      cursor: 'move',
      className: 'lf-edge-adjust-point'
    })
  }
}

/**
 * N次贝塞尔曲线边模型类
 * 基于BaseEdgeModel重新开发，支持任意次数的贝塞尔曲线
 * 功能比源码中的BezierEdgeModel更强大
 * 
 * 功能特性：
 * - 支持2次到10次贝塞尔曲线
 * - 提供标准算法和De Casteljau高级算法
 * - 支持控制点的动态添加、删除和优化
 * - 内置性能优化和缓存机制
 * - 支持曲线分割、长度计算、边界框检测
 * - 提供曲线平滑、自相交检测等高级功能
 * 
 * 使用示例：
 * ```javascript
 * // 创建5次贝塞尔曲线
 * const edgeModel = new MultiBezierEdgeModel({
 *   sourceNodeId: 'node1',
 *   targetNodeId: 'node2',
 *   properties: {
 *     degree: 5,
 *     useAdvancedAlgorithm: true,
 *     smoothness: 100
 *   }
 * }, graphModel)
 * 
 * // 动态调整控制点
 * edgeModel.setDegree(7)
 * edgeModel.optimizeControlPoints()
 * 
 * // 性能监控
 * const debugInfo = edgeModel.getDebugInfo()
 * console.log('曲线信息:', debugInfo)
 * 
 * // 添加控制点
 * edgeModel.addControlPoint(100, 200)
 * 
 * // 导出曲线数据
 * const curveData = edgeModel.exportCurveData()
 * ```
 * 
 * API 方法：
 * - setDegree(degree): 设置曲线度数
 * - setSmoothness(smoothness): 设置渲染平滑度
 * - addControlPoint(x, y): 添加控制点
 * - removeControlPoint(index): 删除控制点
 * - optimizeControlPoints(): 自动优化控制点位置
 * - getCurveLength(): 计算曲线长度
 * - getBoundingBox(): 获取边界框
 * - splitCurve(t): 分割曲线
 * - detectSelfIntersection(): 检测自相交
 * - exportCurveData(): 导出曲线数据
 * - importCurveData(data): 导入曲线数据
 */
export class MultiBezierEdgeModel extends BaseEdgeModel {

  @observable isHighlight = false
  @observable isControlHovered = false
  @observable hoveredLineIndex = -1
  @observable hoveredLinePosition = { x: 0, y: 0 }


  constructor(data, graphModel) {
    // 预先初始化关键属性，避免在 super() 调用时出现 undefined
    const tempControlPoints = data.properties?.controlPoints || []

    // 必须先调用 super() 才能访问 this
    super(data, graphModel)

    // N次贝塞尔曲线的度数（控制点数量 = 度数 + 1）
    this.degree = data.properties?.degree || 3
    // 控制点数量
    this.controlPointsCount = this.degree + 1
    // 控制点数组，包含起点、控制点、终点
    this.controlPoints = tempControlPoints
    // 曲线路径
    this.path = ''
    // 是否启用高级算法
    this.useAdvancedAlgorithm = data.properties?.useAdvancedAlgorithm !== false
    // 曲线平滑度（用于渲染精度）
    this.smoothness = data.properties?.smoothness || 100
    // 自动更新标志
    this._autoUpdate = true


    // 初始化缓存
    this.initCache()

    // 确保控制点已正确初始化
    if (!this.controlPoints || this.controlPoints.length === 0) {
      this.initializeControlPoints()
    } else {
      // 更新控制点数量以匹配实际数组长度
      this.controlPointsCount = this.controlPoints.length
      this.degree = this.controlPointsCount - 1
      this.updatePath()
    }
  }

  /**
   * 初始化控制点
   */
  initializeControlPoints() {
    // 确保 controlPoints 数组存在
    if (!this.controlPoints) {
      this.controlPoints = []
    }

    if (!this.controlPoints || this.controlPoints.length === 0) {
      this.generateDefaultControlPoints()
    }
    this.updatePath()
  }

  /**
   * 二项式系数计算（组合数）
   */
  binomialCoefficient(n, k) {
    if (k > n) return 0
    if (k === 0 || k === n) return 1

    let result = 1
    for (let i = 1; i <= k; i++) {
      result = result * (n - i + 1) / i
    }
    return result
  }

  /**
   * 计算贝塞尔基函数
   */
  bernsteinBasis(n, i, t) {
    return this.binomialCoefficient(n, i) * Math.pow(t, i) * Math.pow(1 - t, n - i)
  }

  /**
   * 计算N次贝塞尔曲线上的点
   * @param {number} t 参数 t ∈ [0, 1]
   * @returns {Object} {x, y} 坐标
   */
  getBezierPoint(t) {
    if (!this.controlPoints || this.controlPoints.length === 0) {
      return { x: 0, y: 0 }
    }

    const { controlPoints } = this
    const n = controlPoints.length - 1
    let x = 0, y = 0

    for (let i = 0; i <= n; i++) {
      const point = controlPoints[i]
      if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
        continue
      }
      const basis = this.bernsteinBasis(n, i, t)
      x += basis * point.x
      y += basis * point.y
    }

    return { x, y }
  }

  /**
   * 使用De Casteljau算法计算贝塞尔曲线点（更稳定的算法）
   */
  getBezierPointDeCasteljau(t) {
    if (!this.controlPoints || this.controlPoints.length === 0) {
      return { x: 0, y: 0 }
    }

    const { controlPoints } = this
    // 验证所有控制点的有效性
    const validPoints = controlPoints.filter(point =>
      point && typeof point.x === 'number' && typeof point.y === 'number'
    )

    if (validPoints.length === 0) {
      return { x: 0, y: 0 }
    }

    let points = [...validPoints]

    for (let level = 1; level < validPoints.length; level++) {
      for (let i = 0; i < validPoints.length - level; i++) {
        if (points[i] && points[i + 1]) {
          points[i] = {
            x: (1 - t) * points[i].x + t * points[i + 1].x,
            y: (1 - t) * points[i].y + t * points[i + 1].y
          }
        }
      }
    }

    return points[0] || { x: 0, y: 0 }
  }

  /**
   * 计算贝塞尔曲线的导数（用于计算切线）
   */
  getBezierDerivative(t) {
    const { controlPoints } = this
    if (!controlPoints || controlPoints.length < 2) {
      return { dx: 0, dy: 0 }
    }

    const n = controlPoints.length - 1
    let dx = 0, dy = 0

    for (let i = 0; i < n; i++) {
      const basis = this.bernsteinBasis(n - 1, i, t)
      dx += basis * n * (controlPoints[i + 1].x - controlPoints[i].x)
      dy += basis * n * (controlPoints[i + 1].y - controlPoints[i].y)
    }

    return { dx, dy }
  }


  /**
   * 生成SVG路径字符串
   */
  generatePath() {
    // 构建完整的贝塞尔曲线控制点数组（起点 + 控制点 + 终点）
    const allPoints = []

    // 添加起点
    if (this.startPoint) {
      allPoints.push(this.startPoint)
    }

    // 添加中间控制点
    if (this.controlPoints && Array.isArray(this.controlPoints)) {
      allPoints.push(...this.controlPoints)
    }

    // 添加终点
    if (this.endPoint) {
      allPoints.push(this.endPoint)
    }

    // 确保至少有两个点
    if (allPoints.length < 2) {
      return ''
    }

    const { smoothness, useAdvancedAlgorithm } = this
    const pathCommands = []

    // 起点
    const startPoint = allPoints[0]
    if (!startPoint || typeof startPoint.x !== 'number' || typeof startPoint.y !== 'number') {
      return ''
    }
    pathCommands.push(`M ${startPoint.x} ${startPoint.y}`)

    // 如果只有两个点，绘制直线
    if (allPoints.length === 2) {
      const endPoint = allPoints[1]
      if (!endPoint || typeof endPoint.x !== 'number' || typeof endPoint.y !== 'number') {
        return pathCommands.join(' ')
      }
      pathCommands.push(`L ${endPoint.x} ${endPoint.y}`)
      return pathCommands.join(' ')
    }

    // 临时保存原始控制点，用于贝塞尔计算
    const originalControlPoints = this.controlPoints
    this.controlPoints = allPoints

    // 生成贝塞尔曲线路径
    const safeSmootness = Math.max(1, smoothness || 100)
    for (let i = 1; i <= safeSmootness; i++) {
      const t = i / safeSmootness
      try {
        const point = useAdvancedAlgorithm
          ? this.getBezierPointDeCasteljau(t)
          : this.getBezierPoint(t)
        if (point && typeof point.x === 'number' && typeof point.y === 'number') {
          pathCommands.push(`L ${point.x} ${point.y}`)
        }
      } catch (error) {
        console.warn('Error generating bezier point:', error)
        break
      }
    }

    // 恢复原始控制点
    this.controlPoints = originalControlPoints

    return pathCommands.join(' ')
  }

  /**
   * 更新路径
   */
  updatePath() {
    if (this._autoUpdate !== false) {
      this.clearCache()
    }

    this.path = this.generatePath()

  }

  /**
   * 重写initPoints方法
   */
  initPoints() {
    // 确保 controlPoints 数组存在
    if (!this.controlPoints) {
      this.controlPoints = []
    }

    // 确保控制点已初始化
    if (!this.controlPoints || this.controlPoints.length === 0) {
      this.initializeControlPoints()
    } else {
      this.updatePath()
    }
  }

  /**
   * 重写updateStartPoint方法
   */
  updateStartPoint(point) {
    this.startPoint = { ...point }

    // 确保控制点数组存在
    if (!this.controlPoints) {
      this.controlPoints = []
    }

    // 如果没有控制点，初始化它们
    if (this.controlPoints.length === 0) {
      this.initializeControlPoints()
    } else {
      // 更新第一个控制点（起点）
      this.controlPoints[0] = { ...point }
      // 清除缓存以确保重新计算
      this.clearCache()
      this.updatePath()
      // 触发视图更新
      this.setAttributes()
    }
  }

  /**
   * 重写updateEndPoint方法
   */
  updateEndPoint(point) {
    this.endPoint = { ...point }

    // 确保控制点数组存在
    if (!this.controlPoints) {
      this.controlPoints = []
    }

    // 如果没有控制点，初始化它们
    if (this.controlPoints.length === 0) {
      this.initializeControlPoints()
    } else {
      // 更新最后一个控制点（终点）
      this.controlPoints[this.controlPoints.length - 1] = { ...point }
      // 清除缓存以确保重新计算
      this.clearCache()
      this.updatePath()
      // 触发视图更新
      this.setAttributes()
    }
  }

  setAttributes() {
    super.setAttributes()
    const { executeStatus, controlPointsCount, controlPoints } = this.properties || {}
    this.isAnimation = executeStatus === 'success'

    if (controlPointsCount !== undefined) {
      this.controlPointsCount = controlPointsCount
    }

    if (controlPoints && controlPoints.length > 0) {
      this.controlPoints = controlPoints
      this.updatePath()
    } else {
      // 如果没有预设控制点，则自动生成
      this.initializeControlPoints()
    }
  }

  /**
   * 生成默认控制点
   * 根据起点和终点自动计算控制点位置
   */
  generateDefaultControlPoints() {
    let startPoint = this.startPoint
    let endPoint = this.endPoint

    const controlPoints = []

    // 添加起点
    controlPoints.push({ x: startPoint.x, y: startPoint.y })

    // 计算起点和终点之间的距离
    const dx = endPoint.x - startPoint.x
    const dy = endPoint.y - startPoint.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    // 生成中间控制点（度数-1个）
    for (let i = 1; i < this.degree; i++) {
      const ratio = i / this.degree
      const offsetX = distance * 0.2 * Math.sin(i * Math.PI / this.degree)
      const offsetY = distance * 0.2 * Math.cos(i * Math.PI / this.degree)

      controlPoints.push({
        x: startPoint.x + dx * ratio + offsetX,
        y: startPoint.y + dy * ratio + offsetY
      })
    }

    // 添加终点
    controlPoints.push({ x: endPoint.x, y: endPoint.y })

    this.controlPoints = controlPoints
    this.controlPointsCount = controlPoints.length
    this.updatePath()
    return controlPoints
  }

  /**
   * 获取边的路径
   * 使用多个控制点生成复杂的贝塞尔曲线
   * @param points 可选的点数组，兼容父类调用
   */
  getPath(points) {
    // 如果传入了 points 参数，使用父类的逻辑
    if (points && Array.isArray(points) && points.length >= 4) {
      return super.getPath(points)
    }

    const { startPoint, endPoint } = this
    const controlPoints = this.controlPoints || []

    if (controlPoints.length === 0) {
      // 如果没有控制点，创建一个简单的直线路径
      return `M ${startPoint.x} ${startPoint.y} L ${endPoint.x} ${endPoint.y}`
    }

    // 构建多段贝塞尔曲线路径
    let path = `M ${startPoint.x} ${startPoint.y}`

    if (controlPoints && controlPoints.length === 1) {
      // 单个控制点，使用二次贝塞尔曲线
      path += ` Q ${controlPoints[0].x} ${controlPoints[0].y} ${endPoint.x} ${endPoint.y}`
    } else if (controlPoints && controlPoints.length === 2) {
      // 两个控制点，使用三次贝塞尔曲线
      path += ` C ${controlPoints[0].x} ${controlPoints[0].y} ${controlPoints[1].x} ${controlPoints[1].y} ${endPoint.x} ${endPoint.y}`
    } else if (controlPoints && controlPoints.length > 2) {
      // 多个控制点，分段处理
      const segments = this.createMultiSegmentPath(startPoint, endPoint, controlPoints)
      path += segments
    }

    return path
  }

  /**
   * 创建多段路径
   * 将多个控制点分组，每组创建一段贝塞尔曲线
   */
  createMultiSegmentPath(start, end, controlPoints) {
    let path = ''
    const allPoints = [start, ...controlPoints, end]

    // 每3个点创建一段三次贝塞尔曲线
    for (let i = 0; i < allPoints.length - 1; i += 2) {
      if (i + 3 < allPoints.length) {
        // 完整的三次贝塞尔曲线段
        const p0 = allPoints[i]
        const p1 = allPoints[i + 1]
        const p2 = allPoints[i + 2]
        const p3 = allPoints[i + 3]

        if (i === 0) {
          path += ` C ${p1.x} ${p1.y} ${p2.x} ${p2.y} ${p3.x} ${p3.y}`
        } else {
          path += ` S ${p2.x} ${p2.y} ${p3.x} ${p3.y}`
        }
      } else if (i + 2 < allPoints.length) {
        // 二次贝塞尔曲线段
        const p1 = allPoints[i + 1]
        const p2 = allPoints[i + 2]
        path += ` Q ${p1.x} ${p1.y} ${p2.x} ${p2.y}`
      } else {
        // 直线段
        const p1 = allPoints[i + 1]
        path += ` L ${p1.x} ${p1.y}`
      }
    }

    return path
  }

  /**
   * 获取调整点（控制点）
   * 支持N次贝塞尔曲线的所有控制点
   */
  getAdjustPoints() {
    const adjustPoints = []

    // 为每个控制点创建调整点（排除起点和终点）
    if (this.controlPoints && this.controlPoints.length > 2) {
      for (let i = 1; i < this.controlPoints.length - 1; i++) {
        const point = this.controlPoints[i]
        adjustPoints.push({
          x: point.x,
          y: point.y,
          id: `control-point-${i}`,
          type: 'control-point',
          index: i,
          onDragStart: this.onControlPointDragStart.bind(this),
          onDragging: this.onControlPointDragging.bind(this),
          onDragEnd: this.onControlPointDragEnd.bind(this)
        })
      }
    }

    return adjustPoints
  }

  /**
   * 获取文本位置（曲线中点）
   */
  getTextPosition() {
    // 安全检查 controlPoints
    if (!this.controlPoints || this.controlPoints.length === 0) {
      // 如果没有控制点，使用起点和终点的中点
      if (this.startPoint && this.endPoint) {
        return {
          x: (this.startPoint.x + this.endPoint.x) / 2,
          y: (this.startPoint.y + this.endPoint.y) / 2
        }
      }
      // 如果连起点终点都没有，返回默认位置
      return { x: 0, y: 0 }
    }

    // 计算曲线中点（t=0.5）
    const midPoint = this.useAdvancedAlgorithm
      ? this.getBezierPointDeCasteljau(0.5)
      : this.getBezierPoint(0.5)

    return midPoint || { x: 0, y: 0 }
  }

  /**
   * 获取边样式
   */
  getEdgeStyle() {
    const style = super.getEdgeStyle()
    const { properties } = this

    return {
      ...style,
      stroke: properties.stroke || style.stroke,
      strokeWidth: properties.strokeWidth || style.strokeWidth,
      strokeDasharray: properties.strokeDasharray || style.strokeDasharray,
      ...properties.style
    }
  }

  /**
   * 获取数据（包含N次贝塞尔曲线特有属性）
   */
  getData() {
    const data = super.getData()
    return {
      ...data,
      properties: {
        ...data.properties,
        degree: this.degree,
        controlPoints: this.controlPoints,
        useAdvancedAlgorithm: this.useAdvancedAlgorithm,
        smoothness: this.smoothness
      }
    }
  }

  /**
   * 设置度数（控制点数量）
   */
  setDegree(degree) {
    if (degree < 1) degree = 1
    if (degree > 10) degree = 10 // 限制最大度数

    const oldDegree = this.degree
    const newControlPointsCount = degree + 1

    this.degree = degree
    this.controlPointsCount = newControlPointsCount

    // 智能调整控制点数量，保持现有控制点位置
    this.adjustControlPointsForDegree(newControlPointsCount)

    // 更新属性以触发视图更新
    this.setProperties({
      ...(this.properties || {}),
      degree: this.degree,
      controlPointsCount: this.controlPointsCount,
      controlPoints: [...this.controlPoints]
    })

    this.updatePath()

    // 强制触发视图更新
    if (this.graphModel) {
      this.graphModel.eventCenter.emit('model:update', { model: this })
    }
  }

  /**
   * 智能调整控制点数量，保持现有控制点位置
   */
  adjustControlPointsForDegree(targetCount) {
    const currentCount = this.controlPoints.length

    if (currentCount === targetCount) {
      return // 数量已经正确，无需调整
    }

    if (currentCount === 0) {
      // 如果当前没有控制点，生成默认控制点
      this.generateDefaultControlPoints()
      return
    }

    if (targetCount > currentCount) {
      // 需要增加控制点，在现有控制点之间插入新点
      const pointsToAdd = targetCount - currentCount

      for (let i = 0; i < pointsToAdd; i++) {
        // 在中间位置插入新控制点
        const insertIndex = Math.floor((currentCount + i) / 2)

        // 计算插入位置的坐标（在相邻两点之间）
        let newX, newY
        if (insertIndex === 0) {
          // 在起点和第一个控制点之间
          newX = (this.controlPoints[0].x + this.controlPoints[1].x) / 2
          newY = (this.controlPoints[0].y + this.controlPoints[1].y) / 2
        } else if (insertIndex >= this.controlPoints.length) {
          // 在最后一个控制点和终点之间
          const lastIndex = this.controlPoints.length - 1
          newX = (this.controlPoints[lastIndex - 1].x + this.controlPoints[lastIndex].x) / 2
          newY = (this.controlPoints[lastIndex - 1].y + this.controlPoints[lastIndex].y) / 2
        } else {
          // 在中间位置
          newX = (this.controlPoints[insertIndex - 1].x + this.controlPoints[insertIndex].x) / 2
          newY = (this.controlPoints[insertIndex - 1].y + this.controlPoints[insertIndex].y) / 2
        }

        // 添加一些随机偏移，避免控制点重叠
        newX += (Math.random() - 0.5) * 20
        newY += (Math.random() - 0.5) * 20

        this.controlPoints.splice(insertIndex, 0, { x: newX, y: newY })
      }
    } else {
      // 需要减少控制点，移除多余的中间控制点，保留起点和终点
      const pointsToRemove = currentCount - targetCount

      // 优先移除中间的控制点，保留起点和终点
      for (let i = 0; i < pointsToRemove; i++) {
        if (this.controlPoints.length > 2) {
          // 移除中间位置的控制点
          const removeIndex = Math.floor(this.controlPoints.length / 2)
          this.controlPoints.splice(removeIndex, 1)
        }
      }
    }

    // 确保控制点数量正确
    while (this.controlPoints.length > targetCount) {
      this.controlPoints.pop()
    }
    while (this.controlPoints.length < targetCount) {
      const lastPoint = this.controlPoints[this.controlPoints.length - 1]
      this.controlPoints.push({
        x: lastPoint.x + 50,
        y: lastPoint.y
      })
    }
  }

  /**
   * 设置平滑度
   */
  setSmoothness(smoothness) {
    if (smoothness < 10) smoothness = 10
    if (smoothness > 1000) smoothness = 1000

    this.smoothness = smoothness
    this.updatePath()
  }

  /**
   * 切换算法
   */
  toggleAlgorithm() {
    this.useAdvancedAlgorithm = !this.useAdvancedAlgorithm
    this.updatePath()
  }

  /**
   * 曲线分割（De Casteljau算法的应用）
   * @param {number} t 分割点参数
   * @returns {Array} [leftCurve, rightCurve] 分割后的两条曲线
   */
  splitCurve(t) {
    const { controlPoints } = this
    if (!controlPoints || controlPoints.length < 2) {
      return [[], []]
    }

    const n = controlPoints.length - 1

    // 使用De Casteljau算法进行分割
    const leftPoints = []
    const rightPoints = []

    let points = [...controlPoints]
    leftPoints.push(points[0])
    rightPoints.unshift(points[n])

    for (let level = 1; level <= n; level++) {
      for (let i = 0; i <= n - level; i++) {
        if (points[i] && points[i + 1]) {
          points[i] = {
            x: (1 - t) * points[i].x + t * points[i + 1].x,
            y: (1 - t) * points[i].y + t * points[i + 1].y
          }
        }
      }
      if (points[0]) leftPoints.push(points[0])
      if (points[n - level]) rightPoints.unshift(points[n - level])
    }

    return [leftPoints, rightPoints]
  }

  /**
   * 获取曲线上最近的点
   * @param {Object} targetPoint 目标点 {x, y}
   * @returns {Object} {point, t, distance} 最近点信息
   */
  getClosestPoint(targetPoint) {
    if (!this.controlPoints || this.controlPoints.length < 2) {
      return {
        point: { x: 0, y: 0 },
        t: 0,
        distance: Infinity
      }
    }

    let minDistance = Infinity
    let closestPoint = null
    let closestT = 0

    // 使用二分法查找最近点
    const samples = 1000
    for (let i = 0; i <= samples; i++) {
      const t = i / samples
      const point = this.useAdvancedAlgorithm
        ? this.getBezierPointDeCasteljau(t)
        : this.getBezierPoint(t)

      const distance = Math.sqrt(
        Math.pow(point.x - targetPoint.x, 2) +
        Math.pow(point.y - targetPoint.y, 2)
      )

      if (distance < minDistance) {
        minDistance = distance
        closestPoint = point
        closestT = t
      }
    }

    return {
      point: closestPoint,
      t: closestT,
      distance: minDistance
    }
  }

  /**
   * 添加控制点到指定位置
   * @param {number} x X坐标
   * @param {number} y Y坐标
   * @param {number} index 插入位置索引（可选，默认插入到中间）
   */
  addControlPoint(x, y, index) {
    if (!this.controlPoints) {
      this.controlPoints = []
    }

    const newPoint = { x, y }

    if (typeof index === 'number') {
      // 在指定位置插入
      if (index < 0 || index > this.controlPoints.length) {
        console.warn('Invalid index for control point insertion')
        return
      }
      this.controlPoints.splice(index, 0, newPoint)
    } else {
      // 计算最佳插入位置
      const insertIndex = this.findBestInsertPosition(x, y)
      this.controlPoints.splice(insertIndex, 0, newPoint)
    }

    this.degree = this.controlPoints.length - 1
    this.controlPointsCount = this.controlPoints.length

    // 更新属性以触发视图更新
    this.setProperties({
      ...(this.properties || {}),
      controlPoints: [...this.controlPoints],
      controlPointsCount: this.controlPointsCount,
      degree: this.degree
    })

    this.updatePath()

    // 强制触发视图更新
    if (this.graphModel) {
      this.graphModel.eventCenter.emit('model:update', { model: this })
    }
  }

  /**
   * 在指定位置插入新的控制点
   * @param {number} t 插入位置参数
   */
  insertControlPoint(t) {
    if (t <= 0 || t >= 1) return

    // 计算插入点
    const insertPoint = this.useAdvancedAlgorithm
      ? this.getBezierPointDeCasteljau(t)
      : this.getBezierPoint(t)

    // 使用曲线分割来插入控制点
    const [leftCurve, rightCurve] = this.splitCurve(t)

    // 重新构建控制点数组
    this.controlPoints = [...leftCurve.slice(0, -1), ...rightCurve]
    this.degree = this.controlPoints.length - 1
    this.updatePath()
  }

  /**
   * 移除指定索引的控制点
   * @param {number} index 控制点索引
   */
  removeControlPoint(index) {
    if (!this.controlPoints || index <= 0 || index >= this.controlPoints.length - 1) return
    if (this.controlPoints.length <= 3) return // 至少保留3个点

    this.controlPoints.splice(index, 1)
    this.degree = this.controlPoints.length - 1
    this.updatePath()
  }

  /**
   * 计算曲线长度（近似）
   */
  getCurveLength() {
    if (!this.controlPoints || this.controlPoints.length === 0) {
      return 0
    }

    let length = 0
    const samples = 1000
    let prevPoint = this.controlPoints[0]

    for (let i = 1; i <= samples; i++) {
      const t = i / samples
      const currentPoint = this.useAdvancedAlgorithm
        ? this.getBezierPointDeCasteljau(t)
        : this.getBezierPoint(t)

      length += Math.sqrt(
        Math.pow(currentPoint.x - prevPoint.x, 2) +
        Math.pow(currentPoint.y - prevPoint.y, 2)
      )

      prevPoint = currentPoint
    }

    return length
  }

  /**
   * 获取曲线边界框
   */
  getBoundingBox() {
    if (!this.controlPoints || this.controlPoints.length === 0) return null

    let minX = Infinity, minY = Infinity
    let maxX = -Infinity, maxY = -Infinity

    // 检查控制点
    if (this.controlPoints && this.controlPoints.length > 0) {
      this.controlPoints.forEach(point => {
        minX = Math.min(minX, point.x)
        minY = Math.min(minY, point.y)
        maxX = Math.max(maxX, point.x)
        maxY = Math.max(maxY, point.y)
      })
    }

    // 检查曲线上的采样点
    const samples = 100
    for (let i = 0; i <= samples; i++) {
      const t = i / samples
      const point = this.useAdvancedAlgorithm
        ? this.getBezierPointDeCasteljau(t)
        : this.getBezierPoint(t)

      minX = Math.min(minX, point.x)
      minY = Math.min(minY, point.y)
      maxX = Math.max(maxX, point.x)
      maxY = Math.max(maxY, point.y)
    }

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    }
  }

  /**
   * 自动优化控制点位置
   * 使用最小二乘法拟合
   */
  optimizeControlPoints() {
    if (!this.controlPoints || this.controlPoints.length < 2) {
      return
    }

    const { startPoint, endPoint } = this
    const distance = Math.sqrt(
      Math.pow(endPoint.x - startPoint.x, 2) +
      Math.pow(endPoint.y - startPoint.y, 2)
    )

    // 计算方向向量
    const direction = {
      x: (endPoint.x - startPoint.x) / distance,
      y: (endPoint.y - startPoint.y) / distance
    }

    // 计算垂直向量
    const perpendicular = {
      x: -direction.y,
      y: direction.x
    }

    // 重新分布控制点
    const newControlPoints = [startPoint]

    for (let i = 1; i < this.controlPoints.length - 1; i++) {
      const t = i / (this.controlPoints.length - 1)

      // 基础位置（直线插值）
      const baseX = startPoint.x + t * (endPoint.x - startPoint.x)
      const baseY = startPoint.y + t * (endPoint.y - startPoint.y)

      // 添加曲率偏移
      const curvature = Math.sin(t * Math.PI) * distance * 0.2

      newControlPoints.push({
        x: baseX + perpendicular.x * curvature,
        y: baseY + perpendicular.y * curvature
      })
    }

    newControlPoints.push(endPoint)
    this.controlPoints = newControlPoints
    this.updatePath()
  }

  /**
   * 自适应调整控制点数量
   * @param {number} targetComplexity 目标复杂度（1-10）
   */
  adaptControlPoints(targetComplexity = 5) {
    if (!this.controlPoints) {
      this.controlPoints = []
    }

    const minPoints = 3
    const maxPoints = 10
    const targetPoints = Math.max(minPoints, Math.min(maxPoints,
      Math.floor(minPoints + (maxPoints - minPoints) * (targetComplexity - 1) / 9)
    ))

    const currentPoints = this.controlPoints.length

    if (targetPoints > currentPoints) {
      // 增加控制点
      const pointsToAdd = targetPoints - currentPoints
      for (let i = 0; i < pointsToAdd; i++) {
        const t = (i + 1) / (pointsToAdd + 1)
        this.insertControlPoint(t)
      }
    } else if (targetPoints < currentPoints) {
      // 减少控制点（从中间开始移除）
      const pointsToRemove = currentPoints - targetPoints
      for (let i = 0; i < pointsToRemove; i++) {
        const middleIndex = Math.floor(this.controlPoints.length / 2)
        this.removeControlPoint(middleIndex)
      }
    }
  }

  /**
   * 平滑曲线（减少锯齿）
   */
  smoothCurve() {
    if (!this.controlPoints || this.controlPoints.length < 4) return

    const smoothedPoints = [this.controlPoints[0]]

    // 使用移动平均平滑中间控制点
    for (let i = 1; i < this.controlPoints.length - 1; i++) {
      const prev = this.controlPoints[i - 1]
      const curr = this.controlPoints[i]
      const next = this.controlPoints[i + 1]

      const smoothed = {
        x: (prev.x + 2 * curr.x + next.x) / 4,
        y: (prev.y + 2 * curr.y + next.y) / 4
      }

      smoothedPoints.push(smoothed)
    }

    smoothedPoints.push(this.controlPoints[this.controlPoints.length - 1])
    this.controlPoints = smoothedPoints
    this.updatePath()
  }

  /**
   * 检测曲线自相交
   */
  detectSelfIntersection() {
    const samples = 100
    const points = []

    // 采样曲线上的点
    for (let i = 0; i <= samples; i++) {
      const t = i / samples
      points.push(this.useAdvancedAlgorithm
        ? this.getBezierPointDeCasteljau(t)
        : this.getBezierPoint(t)
      )
    }

    // 检测线段相交
    const intersections = []
    for (let i = 0; i < points.length - 1; i++) {
      for (let j = i + 2; j < points.length - 1; j++) {
        const intersection = this.lineIntersection(
          points[i], points[i + 1],
          points[j], points[j + 1]
        )
        if (intersection) {
          intersections.push(intersection)
        }
      }
    }

    return intersections
  }

  /**
   * 计算两条线段的交点
   */
  lineIntersection(p1, p2, p3, p4) {
    const x1 = p1.x, y1 = p1.y
    const x2 = p2.x, y2 = p2.y
    const x3 = p3.x, y3 = p3.y
    const x4 = p4.x, y4 = p4.y

    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
    if (Math.abs(denom) < 1e-10) return null // 平行线

    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom

    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
      return {
        x: x1 + t * (x2 - x1),
        y: y1 + t * (y2 - y1)
      }
    }

    return null
  }

  /**
   * 导出曲线数据
   */
  exportCurveData() {
    return {
      type: 'MultiBezierCurve',
      degree: this.degree,
      controlPoints: this.controlPoints,
      useAdvancedAlgorithm: this.useAdvancedAlgorithm,
      smoothness: this.smoothness,
      path: this.path,
      length: this.getCurveLength(),
      boundingBox: this.getBoundingBox()
    }
  }

  /**
   * 从数据导入曲线
   */
  importCurveData(data) {
    if (data.type !== 'MultiBezierCurve') {
      throw new Error('Invalid curve data type')
    }

    this.degree = data.degree || this.degree
    this.controlPoints = data.controlPoints || this.controlPoints
    this.useAdvancedAlgorithm = data.useAdvancedAlgorithm ?? this.useAdvancedAlgorithm
    this.smoothness = data.smoothness || this.smoothness

    this.updatePath()
  }

  /**
   * 性能优化：缓存计算结果
   */
  initCache() {
    this._cache = {
      path: null,
      length: null,
      boundingBox: null,
      lastUpdateTime: 0
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    if (this._cache) {
      this._cache.path = null
      this._cache.length = null
      this._cache.boundingBox = null
      this._cache.lastUpdateTime = Date.now()
    }
  }

  /**
   * 获取缓存的路径
   */
  getCachedPath() {
    if (!this._cache) this.initCache()

    if (!this._cache.path) {
      this._cache.path = this.generatePath()
    }

    return this._cache.path
  }

  /**
   * 获取缓存的长度
   */
  getCachedLength() {
    if (!this._cache) this.initCache()

    if (this._cache.length === null) {
      this._cache.length = this.getCurveLength()
    }

    return this._cache.length
  }

  /**
   * 获取缓存的边界框
   */
  getCachedBoundingBox() {
    if (!this._cache) this.initCache()

    if (!this._cache.boundingBox) {
      this._cache.boundingBox = this.getBoundingBox()
    }

    return this._cache.boundingBox
  }

  /**
   * 验证控制点数据
   */
  validateControlPoints(points) {
    if (!Array.isArray(points)) {
      throw new Error('Control points must be an array')
    }

    if (points.length < 2) {
      throw new Error('At least 2 control points are required')
    }

    for (let i = 0; i < points.length; i++) {
      const point = points[i]
      if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
        throw new Error(`Invalid control point at index ${i}: must have numeric x and y properties`)
      }

      if (!isFinite(point.x) || !isFinite(point.y)) {
        throw new Error(`Invalid control point at index ${i}: coordinates must be finite numbers`)
      }
    }

    return true
  }

  /**
   * 安全的贝塞尔点计算（带错误处理）
   */
  safeGetBezierPoint(t) {
    try {
      if (typeof t !== 'number' || !isFinite(t)) {
        throw new Error('Parameter t must be a finite number')
      }

      if (t < 0 || t > 1) {
        console.warn(`Parameter t (${t}) is outside [0,1] range, clamping to valid range`)
        t = Math.max(0, Math.min(1, t))
      }

      return this.useAdvancedAlgorithm
        ? this.getBezierPointDeCasteljau(t)
        : this.getBezierPoint(t)
    } catch (error) {
      console.error('Error calculating Bezier point:', error)
      // 返回起点作为fallback
      return this.controlPoints && this.controlPoints[0]
        ? { ...this.controlPoints[0] }
        : { x: 0, y: 0 }
    }
  }

  /**
   * 性能监控
   */
  measurePerformance(operation, fn) {
    const startTime = performance.now()
    const result = fn()
    const endTime = performance.now()

    if (endTime - startTime > 16) { // 超过一帧的时间
      console.warn(`Performance warning: ${operation} took ${(endTime - startTime).toFixed(2)}ms`)
    }

    return result
  }

  /**
   * 批量更新控制点（性能优化）
   */
  batchUpdateControlPoints(updates) {
    if (!Array.isArray(updates)) {
      throw new Error('Updates must be an array')
    }

    // 暂停自动更新
    const autoUpdate = this._autoUpdate
    this._autoUpdate = false

    try {
      updates.forEach(({ index, point }) => {
        if (index >= 0 && index < this.controlPoints.length) {
          this.controlPoints[index] = { ...point }
        }
      })

      // 手动触发一次更新
      this.clearCache()
      this.updatePath()
    } finally {
      // 恢复自动更新
      this._autoUpdate = autoUpdate
    }
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      type: 'MultiBezierEdgeModel',
      degree: this.degree,
      controlPointsCount: this.controlPoints ? this.controlPoints.length : 0,
      useAdvancedAlgorithm: this.useAdvancedAlgorithm,
      smoothness: this.smoothness,
      pathLength: this.path ? this.path.length : 0,
      cacheStatus: this._cache ? {
        hasPath: !!this._cache.path,
        hasLength: this._cache.length !== null,
        hasBoundingBox: !!this._cache.boundingBox,
        lastUpdate: this._cache.lastUpdateTime
      } : null,
      memoryUsage: {
        controlPoints: this.controlPoints ? JSON.stringify(this.controlPoints).length : 0,
        path: this.path ? this.path.length : 0
      }
    }
  }

  /**
   * 控制点拖拽开始
   */
  onControlPointDragStart(index) {
    // 记录拖拽开始时的状态
    this.dragStartState = {
      index,
      originalPoint: { ...this.controlPoints[index] }
    }
  }

  /**
   * 控制点拖拽中
   */
  onControlPointDragging(index, deltaX, deltaY) {
    if (!this.controlPoints || !Array.isArray(this.controlPoints) || index < 0 || index >= this.controlPoints.length) {
      return
    }

    // 更新控制点位置
    if (this.controlPoints[index]) {
      this.controlPoints[index].x += deltaX
      this.controlPoints[index].y += deltaY
    }

    // 更新属性以触发重新渲染
    this.setProperties({
      ...(this.properties || {}),
      controlPoints: [...this.controlPoints]
    })
  }

  /**
   * 控制点拖拽结束
   */
  onControlPointDragEnd(index) {
    // 清理拖拽状态
    this.dragStartState = null

    // 触发图形更新事件
    this.graphModel.eventCenter.emit('graph:updated', {
      data: this
    })
  }

  /**
   * 更新控制点位置
   */
  updateControlPoint(pointId, deltaX, deltaY) {
    if (!this.controlPoints || !Array.isArray(this.controlPoints) || !pointId || typeof pointId !== 'string') {
      return
    }

    const parts = pointId.split('-')
    if (parts.length < 3) {
      return
    }
    const index = parseInt(parts[2])
    if (index >= 0 && index < this.controlPoints.length && this.controlPoints[index]) {
      this.controlPoints[index].x += deltaX
      this.controlPoints[index].y += deltaY

      // 更新属性
      this.setProperties({
        ...(this.properties || {}),
        controlPoints: [...this.controlPoints]
      })
    }
  }

  /**
   * 添加控制点
   * @param x 控制点 x 坐标
   * @param y 控制点 y 坐标
   */


  /**
   * 找到新控制点的最佳插入位置
   * @param x 新控制点的 x 坐标
   * @param y 新控制点的 y 坐标
   * @returns 插入位置的索引
   */
  findBestInsertPosition(x, y) {
    if (!this.controlPoints || this.controlPoints.length === 0) {
      return 0
    }

    // 创建包含起点、控制点、终点的完整点列表
    const allPoints = [
      this.startPoint,
      ...this.controlPoints,
      this.endPoint
    ]

    let minDistance = Infinity
    let bestIndex = 0

    // 计算新点到每条线段的距离，找到最近的线段
    for (let i = 0; i < allPoints.length - 1; i++) {
      const p1 = allPoints[i]
      const p2 = allPoints[i + 1]

      // 计算点到线段的距离
      const distance = this.pointToLineDistance(x, y, p1.x, p1.y, p2.x, p2.y)

      if (distance < minDistance) {
        minDistance = distance
        bestIndex = i // 在第 i 个控制点之后插入（如果 i=0，则在第一个控制点之前插入）
      }
    }

    return bestIndex
  }

  /**
   * 计算点到线段的距离
   */
  pointToLineDistance(px, py, x1, y1, x2, y2) {
    const A = px - x1
    const B = py - y1
    const C = x2 - x1
    const D = y2 - y1

    const dot = A * C + B * D
    const lenSq = C * C + D * D

    if (lenSq === 0) {
      // 线段退化为点
      return Math.sqrt(A * A + B * B)
    }

    let param = dot / lenSq

    let xx, yy

    if (param < 0) {
      xx = x1
      yy = y1
    } else if (param > 1) {
      xx = x2
      yy = y2
    } else {
      xx = x1 + param * C
      yy = y1 + param * D
    }

    const dx = px - xx
    const dy = py - yy

    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 删除控制点
   */
  removeControlPoint(index) {
    if (!this.controlPoints || !Array.isArray(this.controlPoints)) {
      return
    }

    if (index >= 0 && index < this.controlPoints.length && this.controlPoints[index]) {
      this.controlPoints.splice(index, 1)
      this.controlPointsCount = this.controlPoints ? this.controlPoints.length : 0

      this.setProperties({
        ...(this.properties || {}),
        controlPoints: [...this.controlPoints],
        controlPointsCount: this.controlPointsCount
      })
    }
  }

  @action
  setControlHovered(flag = true) {
    this.isControlHovered = flag
  }

  @action
  setHoveredLine(lineIndex = -1, position = { x: 0, y: 0 }) {
    this.hoveredLineIndex = lineIndex
    this.hoveredLinePosition = position
  }

  @action
  setHighlight(isHighlight = true) {
    this.isHighlight = isHighlight
  }

  getEdgeStyle() {
    const style = super.getEdgeStyle()
    if (this.isHighlight) {
      style.stroke = '#5eb15e'
      style.strokeWidth = 3
    } else if (this.isHovered && !this.isControlHovered) {
      style.stroke = '#1e6fff'
      style.strokeWidth = 3
    }
    return style
  }

  getEdgeAnimationStyle() {
    const style = super.getEdgeAnimationStyle()
    style.stroke = '#84cf65'
    if (this.isHighlight) {
      style.strokeWidth = 3
    } else if (this.isHovered && !this.isControlHovered) {
      style.strokeWidth = 3
    }
    return style
  }

  /**
   * 获取边的数据，包含控制点信息
   */
  getData() {
    const data = super.getData()
    return {
      ...data,
      properties: {
        ...data.properties,
        controlPoints: this.controlPoints || [],
        controlPointsCount: this.controlPointsCount || 0
      }
    }
  }
}



export default {
  type: 'multiBezier',
  view: MultiBezierEdge,
  model: MultiBezierEdgeModel
}