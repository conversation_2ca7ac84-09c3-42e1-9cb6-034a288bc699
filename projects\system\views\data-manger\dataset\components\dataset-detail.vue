<template>
  <div class="dataset-detail">
    <!-- 数据集选择区域 -->
    <div class="dataset-select-section">
      <el-select
        v-model="selectedDatasetId"
        filterable
        remote
        reserve-keyword
        placeholder="请选择数据集"
        :remote-method="remoteSearch"
        :loading="loading"
        @change="handleDatasetChange"
        style="width: 100%"
      >
        <el-option
          v-for="item in datasetOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
          <span>{{ item.name }}</span>
          <span v-if="isDuplicateName(item)" class="dataset-namespace">
            ({{ item.namespace }}.{{ item.mappedId }})
          </span>
        </el-option>
      </el-select>
    </div>

    <!-- 数据集预览信息 -->
    <div v-loading="loading" class="dataset-preview">
      <template v-if="detail.id">
        <el-form label-width="100px">
          <el-form-item label="名称">
            <span>{{ detail.name }}</span>
          </el-form-item>
          <el-form-item label="命名空间">
            <span>{{ detail.namespace }}</span>
          </el-form-item>
          <el-form-item label="方法ID">
            <span>{{ detail.mappedId }}</span>
          </el-form-item>
          <el-form-item label="SQL">
            <pre class="sql-content">{{ detail.mappedContent }}</pre>
          </el-form-item>
        </el-form>
      </template>
      <div v-else class="no-dataset-selected">
        <i class="el-icon-connection"></i>
        <p>请选择要绑定的数据集</p>
      </div>
    </div>

    <!-- 更改绑定按钮 -->
    <div class="actions-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button 
        type="primary" 
        @click="handleUpdateBinding"
        :loading="updating"
        :disabled="!selectedDatasetId || selectedDatasetId === datasetId"
      >
        更改绑定
      </el-button>
    </div>
  </div>
</template>

<script>
import { getDatasetDetail, getDatasetList } from '@system/api/data-manger/dataset'
import { updateApi } from '@system/api/data-manger/api-management'

export default {
  name: 'DatasetDetail',
  props: {
    apiId: {
      type: [String, Number],
      required: true
    },
    datasetId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      updating: false,
      detail: {},
      selectedDatasetId: '',
      datasetOptions: [],
      datasetSearchTimer: null,
      nameCountMap: {}
    }
  },
  created() {
    this.selectedDatasetId = this.datasetId
    if (this.datasetId) {
      this.fetchDetail()
    }
    this.loadDatasetOptions()
  },
  methods: {
    // 获取数据集详情
    async fetchDetail() {
      if (!this.selectedDatasetId) return
      
      this.loading = true
      try {
        const  data  = await getDatasetDetail(this.selectedDatasetId)
        this.detail = data || {}
      } catch (error) {
        console.error('获取数据集详情失败:', error)
        this.$message.error('获取数据集详情失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 远程搜索数据集
    async remoteSearch(query) {
      if (this.datasetSearchTimer) {
        clearTimeout(this.datasetSearchTimer)
      }

      this.datasetSearchTimer = setTimeout(async () => {
        this.loading = true
        try {
          const data = await getDatasetList({
            current: 1,
            size: 10,
            name: query || undefined
          })
          
          if (data && data.records) {
            this.processDatasetOptions(data.records)
          }
        } catch (error) {
          console.error('搜索数据集失败:', error)
          this.$message.error('搜索数据集失败：' + error.message)
        } finally {
          this.loading = false
        }
      }, 300)
    },

    // 加载初始数据集选项
    async loadDatasetOptions() {
      this.loading = true
      try {
        const data = await getDatasetList({
          current: 1,
          size: 10
        })

        if (data && data.records) {
          this.processDatasetOptions(data.records)
        }
      } catch (error) {
        console.error('加载数据集列表失败:', error)
        this.$message.error('加载数据集列表失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 处理数据集选项并统计重名
    processDatasetOptions(records) {
      this.datasetOptions = records
      
      // 统计名称出现次数
      this.nameCountMap = records.reduce((acc, curr) => {
        acc[curr.name] = (acc[curr.name] || 0) + 1
        return acc
      }, {})
    },

    // 判断是否需要显示命名空间（名称重复时）
    isDuplicateName(dataset) {
      return this.nameCountMap[dataset.name] > 1
    },

    // 处理数据集选择变化
    handleDatasetChange(value) {
      this.selectedDatasetId = value
      this.fetchDetail()
    },

    // 更新绑定关系
    async handleUpdateBinding() {
      if (!this.selectedDatasetId || this.selectedDatasetId === this.datasetId) {
        return
      }

      this.updating = true
      try {
        await updateApi({
          id: this.apiId,
          datasetId: this.selectedDatasetId
        })
        
        this.$message.success('更新绑定关系成功')
        this.$emit('close')
        this.$emit('refresh')
      } catch (error) {
        console.error('更新绑定关系失败:', error)
        this.$message.error('更新绑定关系失败：' + error.message)
      } finally {
        this.updating = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dataset-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
}

.dataset-select-section {
  margin-bottom: 20px;
}

.dataset-preview {
  flex: 1;
  min-height: 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;

  .sql-content {
    margin: 0;
    padding: 16px;
    background: #fff;
    border-radius: 4px;
    font-family: Monaco, Menlo, Consolas, monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
  }
}

.no-dataset-selected {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  padding: 40px 0;

  i {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

.actions-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.dataset-namespace {
  float: right;
  color: #909399;
  font-size: 13px;
}

::v-deep .el-select-dropdown__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .el-form-item__label {
    color: #606266;
    font-weight: 500;
  }
}
</style> 