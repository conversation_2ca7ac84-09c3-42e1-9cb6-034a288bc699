<template>
    <div style="display: flex; flex-direction: column; align-items: center;">

        <h3 class="qr-title" v-text="connector?.name || ''"></h3>

        <template v-if="connector?.pluginBeanName == 'WeChatOfficialAuthPlugin'">
            <div class="qr-code-wrapper">
                <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="二维码" class="qr-code" :class="{
                    'scanned': isScanned,
                    'expired': qrCodeExpired
                }">
                <div v-if="qrCodeExpired" class="qr-expired" @click="refreshQrCode">
                    <i class="el-icon-refresh"></i>
                    <p>二维码已失效</p>
                    <span>点击刷新</span>
                </div>
                <div v-else-if="isScanned" class="checkmark">
                    <i class="el-icon-check"></i>
                    <span>扫码成功</span>
                </div>
            </div>
            <div v-if="remainingTime > 0 && !qrCodeExpired" class="countdown">
                剩余时间: {{ remainingTime }} 秒
            </div>
        </template>
        <template v-else-if="connector?.pluginBeanName == 'DingTalkAuthPlugin'">
            <iframe id="ddQrCode" :src="authorizationUrl" frameborder="0" scrolling="no" width="365"
                height="400"></iframe>
        </template>

        <div style="text-align: center;">
            <div class="back-to-login" @click="$emit('goBack')">
                <i class="el-icon-switch-button"></i>
                <span>返回账号密码登录</span>
            </div>
        </div>
    </div>
</template>
<script>
import { getQrCode, pollLoginStatus } from '../api/auth'
import { setToken } from '@/utils/auth'

export default {
    props: {
        connector: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            qrCodeUrl: '',
            isScanned: false,
            qrCodeExpired: false,
            remainingTime: 120,
            qrCodeKey: '',
            authorizationUrl: ''
        }
    },
    destroyed() {
        this.countdownInterval && clearInterval(this.countdownInterval)
        this.pollingInterval && clearInterval(this.pollingInterval)
    },
    mounted() {
        this.getQrCode()
    },
    methods: {
        getQrCode() {
            getQrCode(this.connector.id).then(res => {
                if (res.plugin == 'WeChatOfficialAuthPlugin') {
                    this.qrCodeUrl = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${encodeURIComponent(res.ticket)}`
                    this.qrCodeKey = res.qrCodeKey
                    this.remainingTime = res.expire_seconds || 120
                    this.startCountdown()
                    this.startPolling()
                } else if (data.plugin == 'DingTalkAuthPlugin') {
                    this.authorizationUrl = res.authorizationUrl
                }
            }).catch((error) => {
                this.$message.error('获取二维码失败：' + error);
            })
        },
        startCountdown() {
            this.countdownInterval = setInterval(() => {
                if (this.remainingTime > 0) {
                    this.remainingTime--;
                } else {
                    this.countdownInterval && clearInterval(this.countdownInterval);
                }
            }, 1000);
        },
        // 开始轮询
        async startPolling() {
            this.pollingInterval && clearInterval(this.pollingInterval)

            this.pollingInterval = setInterval(async () => {
                try {
                    const data = await pollLoginStatus(this.qrCodeKey);

                    // 无论什么情况，只要返回了 accessToken，就停止轮询并处理登录
                    if (data.accessToken) {
                        this.pollingInterval && clearInterval(this.pollingInterval)
                        this.isScanned = true
                        // 存储token并跳转
                        setToken(data.accessToken)
                        this.$emit('loginComplete', data.accessToken)
                        return
                    }

                    switch (data.status) {
                        case 'SCAN':
                            // 已扫码但未授权，继续轮询
                            this.isScanned = true;
                            break;

                        case 'NOT_SCAN':
                            // 未扫码，继续轮询
                            break;
                        case 'SCAN_BUT_NOT_BIND':
                            this.$router.replace({ path: '/exception', query: { msg: '未绑定账号' } })
                            break;
                        case 'TIMEOUT':
                            // 二维码过期，停止轮询
                            this.pollingInterval && clearInterval(this.pollingInterval);
                            this.remainingTime = 0;
                            this.qrCodeExpired = true;
                            break;
                    }
                } catch (error) {
                    console.error('轮询失败:', error);
                    this.pollingInterval && clearInterval(this.pollingInterval);
                    this.$message.error('轮询失败：' + error);
                }
            }, 2000);
        },
        refreshQrCode() {
            this.qrCodeExpired = false;
            this.isScanned = false;
            this.remainingTime = 120;
            this.getQrCode()
        }
    }
}
</script>
<style lang="scss" scoped>
.qr-title {
    font-size: 20px;
    font-weight: 500;
    color: #303133;
    margin-top: 40px;
}

.back-to-login {
    display: inline-block;
    color: #909399;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin-top: 20px;

    &:hover {
        color: #409EFF;
        background: #f5f7fa;
    }

    i {
        margin-right: 6px;
        font-size: 16px;
    }
}

.qr-code-wrapper {
    position: relative;
    width: 220px;
    height: 220px;
    margin-bottom: 16px;
    padding: 10px;
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .qr-code {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        transition: opacity 0.3s ease;

        &.expired {
            opacity: 0.8;
            filter: grayscale(100%);
        }
    }

    .qr-code.scanned {
        opacity: 0.5;
    }

    .checkmark {
        font-size: 24px;
        color: #4caf50;
        width: 200px;
        text-align: center;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .countdown {
        margin-top: 20px;
        font-size: 16px;
        color: #2c3e50;
        font-weight: bold;
    }

    .qr-expired {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: rgba(255, 255, 255, 0.95);
        }

        i {
            font-size: 24px;
            color: #409EFF;
            margin-bottom: 8px;
        }

        p {
            color: #606266;
            font-size: 14px;
        }

        span {
            color: #409EFF;
            font-size: 14px;
        }
    }
}
</style>