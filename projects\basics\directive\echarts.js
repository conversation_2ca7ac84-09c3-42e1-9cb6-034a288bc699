import Vue from 'vue'
import * as echarts from 'echarts';
import elementResizeDetectorMaker from 'element-resize-detector';
const erd = elementResizeDetectorMaker()

const debounce = function (func, delay = 1000 / 60) {
    let timeoutId;
    return function () {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, arguments);
        }, delay);
    };
}
Vue.use({
    install(Vue) {
        Vue.directive('echarts', {
            inserted(el, binding, vnode) {

                const obj = binding.modifiers.svg ? { renderer: 'svg' } : null
                const myChart = echarts.init(el, vnode.context.currentTheme, obj)
                const option = binding.value;
                vnode.context.$emit('initEcharts', myChart)
                vnode.context.initEcharts && vnode.context.initEcharts(myChart)
                myChart.setOption(option, true)
                el.debounce = debounce(function () {
                    myChart.resize()
                })
                window.addEventListener('resize', el.debounce)
                myChart.on('finished', function () {
                    if (el.finished !== true) {
                        el.finished = true
                        erd.listenTo(el, el.debounce)
                    }
                })
            },
            update(el, binding) {
                const myChart = echarts.getInstanceByDom(el)
                const option = binding.value;
                const force = binding.modifiers.force && true
                myChart.setOption(option, force);
            },
            unbind(el) {
                try {
                    if (el.debounce instanceof Function) {
                        window.removeEventListener('resize', el.debounce)
                        el.debounce = null
                    }
                    const myChart = echarts.getInstanceByDom(el)
                    myChart.dispose()

                    erd.uninstall(el)
                } catch (error) {
                }
            }
        })
    },
})