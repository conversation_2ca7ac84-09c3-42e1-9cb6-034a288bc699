
export default class UnitConvert {

    static viewportUnit = 'vw'
    // 设计稿的视口宽度
    static viewportWidth = 1920
    static viewportHeight = 1080
    static unitPrecision = 5
    static remUnit = 19.2
    static remOpen = false

    static px2vwh(px, suffix = UnitConvert.viewportUnit) {
        let width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth || UnitConvert.viewportWidth
        let height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight || UnitConvert.viewportHeight
        let ruler = suffix == 'vh' ? height : width
        let ruler2 = suffix == 'vh' ? UnitConvert.viewportHeight : UnitConvert.viewportWidth
        let scale = ruler / ruler2
        let vwh = ((px * scale) / (ruler / 100)).toFixed(UnitConvert.unitPrecision)
        return ['vw', 'vh'].includes(suffix) ? vwh + suffix : vwh
    }

    static vwh2px(vwh, suffix = 'px') {
        let width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth || UnitConvert.viewportWidth
        let height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight || UnitConvert.viewportHeight
        let ruler = UnitConvert.viewportUnit == 'vh' ? height : width
        let scale = ruler / 100.0
        let px = vwh * scale
        return ['px'].includes(suffix) ? px + suffix : px
    }

    static rem2px(rem, suffix = 'px') {
        let htmlFontSize = parseFloat(window.getComputedStyle(document.documentElement).fontSize)
        return suffix ? `${rem * htmlFontSize}${suffix}` : rem * htmlFontSize
    }

    static px2rem(px, suffix = 'rem') {
        let rem = (px / UnitConvert.remUnit).toFixed(UnitConvert.unitPrecision)
        return suffix ? `${rem}${suffix}` : rem
    }

    static rpx(px, suffix = false) {
        if (UnitConvert.viewportUnit == 'px') {
            px = px || 0
        } else if (UnitConvert.remOpen) {
            px = UnitConvert.rem2px(UnitConvert.px2rem(px, false), false)
        } else {
            px = UnitConvert.vwh2px(UnitConvert.px2vwh(px, false), false)
        }
        return suffix ? px + 'px' : px
    }
}

