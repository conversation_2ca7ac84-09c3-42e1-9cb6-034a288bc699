<template>
    <div style="position: relative;">
        <div v-if="loading" class="empty_state_box_loading">
        </div>
        <div v-else-if="empty" class="empty_state_box_ui">
        </div>
        
        <slot v-else></slot>
    </div>
</template>
<script>

export default {
    name: 'UiBox',
    props: {
        empty: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        },

    }
}
</script>