.empty_state_box_ui {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;

    &::after {
        content: "";
        position: absolute;
        width: 220px;
        height: 146px;
        background: url(./empty.png);
        background-size: 100% 100%;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
    }
}

.empty_state_box_loading {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;

    &::after {
        content: "";
        position: absolute;
        width: 220px;
        height: 118px;
        background: url(./empty2.png);
        background-size: 100% 100%;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        animation: jump 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
        transform-origin: bottom;
    }

    &::before {
        content: "加载中...";
        position: absolute;
        top: calc(50% + 70px);
        left: 50%;
        transform: translateX(-50%) translateY(-50%);
        font-size: 16px;
        color: transparent !important;
        background: linear-gradient(90deg, rgb(88, 151, 222), #00ffff, rgb(88, 151, 222));
        background-size: 200% 100%;
        -webkit-background-clip: text;
        background-clip: text;
        animation: shine 2s linear infinite;
    }
}

@keyframes shine {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}

@keyframes jump {

    0%,
    100% {
        transform: translateX(-50%) translateY(-50%) scale(1);
    }

    50% {
        transform: translateX(-50%) translateY(calc(-50% - 10px)) scale(1.05);
    }
}