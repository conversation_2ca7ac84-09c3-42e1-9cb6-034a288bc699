<template>
  <div class="flow-visualizer">
    <div class="card-header">
      <div class="header-title" style="display: flex; align-items: center; justify-content: space-between;">
        <h2 style="display: flex; align-items: center;">
          <span style="width: 4px; height: 20px; background: #409EFF; margin-right: 8px; border-radius: 2px;"></span>
          网关路由
        </h2>
        <div class="action-bar" v-if="editable">
          <el-button type="primary" size="small" icon="el-icon-plus" @click="$emit('add-route')">添加路由</el-button>
          <el-tooltip content="应用配置" placement="top">
            <el-button type="success" size="small" icon="el-icon-s-promotion" @click="$emit('apply-config')">应用配置</el-button>
          </el-tooltip>
        </div>
      </div>
      <div class="config-tip" v-if="editable">
        <el-alert
          title="温馨提示：配置修改后，需点击应用配置按钮才会实时生效"
          type="warning"
          show-icon
          :closable="false">
        </el-alert>
      </div>
    </div>
    
    <div class="flow-container" ref="flowContainer">
      <div v-if="filteredRoutes.length === 0 && searchKeyword" class="empty-search">
        <i class="el-icon-search"></i>
        <p>未找到匹配的路由</p>
      </div>
      
      <div v-else-if="routes.length === 0" class="empty-data">
        <i class="el-icon-guide"></i>
        <p>暂无路由配置</p>
        <el-button size="small" type="primary" @click="$emit('add-route')" v-if="editable">添加路由</el-button>
      </div>
      
      <template v-else>
        <div 
          class="route-item" 
          :class="{ active: selectedRoute && selectedRoute.id === route.id, disabled: route.enable < 1 }" 
          v-for="route in filteredRoutes" 
          :key="route.id" 
          @click="selectRoute(route)"
        >
          <div class="route-header">
            <i class="el-icon-guide"></i>
            <span class="route-name">{{ route.name }}</span>
          </div>
          <div class="route-uri">{{ `${route.pattern} → ${route.uri}` }}</div>
          <!-- <div class="route-stats">
            <div class="stat-item predicate">
              <i class="el-icon-fork-spoon"></i>
              <span>{{ getPredicatesCount(route.id) }}</span>
            </div>
            <div class="stat-item filter">
              <i class="el-icon-magic-stick"></i>
              <span>{{ getFiltersCount(route.id) }}</span>
            </div>
            <div class="stat-item security">
              <i class="el-icon-lock"></i>
              <span>{{ getSecurityCount(route.id) }}</span>
            </div>
          </div> -->
          <div class="route-actions" v-if="editable">
            <span @click.stop>
              <el-switch v-model="route.enable" style="transform: scale(0.6)" @change="$emit('enableChange', route)"></el-switch>
            </span>
            <el-tooltip content="编辑路由" placement="top">
              <el-button type="text" icon="el-icon-edit" size="mini" @click.stop="$emit('edit-route', route)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除路由" placement="top">
              <el-button type="text" icon="el-icon-delete" size="mini" @click.stop="$emit('delete-route', route)"></el-button>
            </el-tooltip>
          </div>
        </div>
      </template>
    </div>
    
    <!-- <div class="flow-legend" v-if="routes.length > 0">
      <div class="legend-item">
        <i class="el-icon-fork-spoon" style="color: #67C23A;"></i>
        <span>断言</span>
      </div>
      <div class="legend-item">
        <i class="el-icon-magic-stick" style="color: #E6A23C;"></i>
        <span>过滤器</span>
      </div>
      <div class="legend-item">
        <i class="el-icon-lock" style="color: #F56C6C;"></i>
        <span>安全配置</span>
      </div>
    </div> -->
  </div>
</template>

<script>
import { getRouteList } from '@system/api/sys/gateway'

export default {
  name: 'FlowVisualizer',
  props: {
    predicates: {
      type: Array,
      required: true
    },
    filters: {
      type: Array,
      required: true
    },
    securities: {
      type: Array,
      required: true
    },
    selectedRoute: {
      type: Object,
      default: null
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchKeyword: '',
      filteredRoutes: [],
      routes: []
    }
  },
  created() {
    this.fetchRoutes()
  },
  methods: {
    async fetchRoutes() {
      try {
        const response = await getRouteList({ size: -1 })
        this.routes = response.records || []
        this.filteredRoutes = [...this.routes]
        if (this.routes.length) {
          this.selectRoute(this.routes[0])
        }
      } catch (error) {
        console.error('获取路由列表失败:', error)
      }
    },
    selectRoute(route) {
      this.$emit('route-selected', route)
    },
    filterRoutes() {
      if (!this.searchKeyword) {
        this.filteredRoutes = [...this.routes]
        return
      }
      
      const keyword = this.searchKeyword.toLowerCase()
      this.filteredRoutes = this.routes.filter(route => 
        route.name.toLowerCase().includes(keyword) || 
        route.uri.toLowerCase().includes(keyword) ||
        (route.pattern && route.pattern.toLowerCase().includes(keyword))
      )
    },
    getPredicatesCount(routeId) {
      return this.predicates.filter(p => p.routeId === routeId).length;
    },
    getFiltersCount(routeId) {
      return this.filters.filter(f => f.routeId === routeId).length;
    },
    getSecurityCount(routeId) {
      return this.securities.filter(s => s.routeId === routeId).length;
    },
    editRoute(route) {
      this.$emit('edit-route', route);
    },
    deleteRoute(route) {
      this.$emit('delete-route', route);
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-visualizer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .card-header {
    padding: 16px;
    border-bottom: 1px solid #EBEEF5;

    .header-title {
      margin-bottom: 12px;

      h2 {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }
    }

    .config-tip {
      margin-top: 12px;
    }
  }

  .disabled {
    opacity: 0.4;
  }
  
  .flow-container {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    .empty-search,
    .empty-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      color: #909399;
      text-align: center;
      
      i {
        font-size: 40px;
        margin-bottom: 16px;
        color: #c0c4cc;
      }
      
      p {
        margin: 0 0 16px;
        font-size: 14px;
      }
    }
    
    .route-item {
      background-color: #fff;
      border-radius: 10px;
      padding: 15px;
      border: 1px solid #ebeef5;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
        
        .route-actions {
          opacity: 1;
        }
      }
      
      &.active {
        background-color: #ecf5ff;
        border-color: #c6e2ff;
        box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.1);
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 4px;
          background: linear-gradient(to bottom, #409EFF, #a0cfff);
          border-radius: 4px 0 0 4px;
        }
      }
      
      .route-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding-right: 25px;
        
        i {
          font-size: 18px;
          color: #409EFF;
          margin-right: 8px;
        }
        
        .route-name {
          font-size: 15px;
          font-weight: 600;
          color: #303133;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .route-uri {
        color: #606266;
        font-size: 13px;
        margin-bottom: 12px;
        padding-left: 26px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .route-stats {
        display: flex;
        justify-content: space-between;
        border-top: 1px dashed #ebeef5;
        padding-top: 10px;
        
        .stat-item {
          display: flex;
          align-items: center;
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 10px;
          color: #fff;
          
          i {
            margin-right: 4px;
            font-size: 12px;
          }
          
          &.predicate {
            background-color: #67C23A;
          }
          
          &.filter {
            background-color: #E6A23C;
          }
          
          &.security {
            background-color: #F56C6C;
          }
        }
      }
      
      .route-actions {
        position: absolute;
        top: 10px;
        right: 10px;
        opacity: 0;
        transition: opacity 0.3s;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .el-button {
          padding: 2px 4px;
          
          &:first-child {
            color: #409EFF;
          }
          
          &:last-child {
            color: #F56C6C;
          }
        }
      }
    }
  }
  
  .flow-legend {
    display: flex;
    justify-content: center;
    padding: 10px 0;
    margin-top: 10px;
    border-top: 1px solid #ebeef5;
    
    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 8px;
      font-size: 12px;
      color: #909399;
      
      i {
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }
}
</style> 