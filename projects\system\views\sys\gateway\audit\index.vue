<template>
  <div class="audit-containers">
    <div class="card-header">
      <el-radio-group v-model="activeTab" size="medium" class="tab-switch">
        <el-radio-button label="log">操作记录</el-radio-button>
        <el-radio-button label="config">配置</el-radio-button>
      </el-radio-group>
    </div>
    <div v-show="activeTab === 'log'" class="tab-content">
      <log :route-id="routeId" class="tab-content-item" />
    </div>
    <div v-show="activeTab === 'config'" class="tab-content">
      <log-config :route-id="routeId" class="tab-content-item" />
    </div>
  </div>
</template>

<script>
import log from './log.vue';
import logConfig from './logConfig.vue';
export default {
  name: 'AuditIndex',
  components: {
    log,
    logConfig
  },
  props: {
    routeId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      activeTab: 'log'
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-content-item {
  height: 82vh;
  overflow-y: auto;
}
.audit-containers {
  width: 100%;
  background: #fff;
  background: inherit;
  padding: 24px;
  .card-header {
    padding: 0px 0 20px;
    .tab-switch {
      margin-left: 8px;
          font-size: 16px;
    }
  }
  .tab-content {
    padding: 0 8px;
  }
}
</style> 
