import uniqueId from '@form-create/utils/lib/unique';
import { localeOptions, localeProps, makeTreeOptions, makeTreeOptionsRule } from '@form-create/designer-zrw/src/utils';

const label = '级联选择器2';
const name = 'cascader2';

export default {
    menu: 'custom',
    icon: 'icon-cascader',
    label,
    name,
    event: ['change', 'expandChange', 'blur', 'focus', 'visibleChange', 'removeTag'],
    validate: ['string', 'number', 'array'],
    rule({ t }) {
        return {
            type: name,
            field: uniqueId(),
            title: label,
            info: '',
            effect: {
                fetch: ''
            },
            $required: false,
            props: {
                options: makeTreeOptions(t('props.option'), { label: 'label', value: 'value' }, 3)
            }
        };
    },
    props(_, { t }) {
        return localeProps(t, 'cascader.props', [
            makeTreeOptionsRule({ t, to: 'props.options' }),
            ...[

                {
                    type: 'switch',
                    field: 'disabled'
                },
                {
                    type: 'switch',
                    field: 'clearable'
                },
                {
                    type: 'input',
                    field: 'placeholder'
                },
                {
                    type: 'Object',
                    field: 'props',
                    props: {
                        rule: localeProps(t, name + '.propsOpt', [{
                            type: 'switch',
                            field: 'multiple'
                        }, {
                            type: 'select',
                            field: 'expandTrigger',
                            options: localeOptions(t, [{ label: 'click', value: 'click' }, {
                                label: 'hover',
                                value: 'hover'
                            }])
                        }, {
                            type: 'switch',
                            field: 'checkStrictly'
                        }, {
                            type: 'switch',
                            field: 'emitPath',
                            value: true
                        }, {
                            type: 'input',
                            field: 'value',
                            value: 'value'
                        }, {
                            type: 'input',
                            field: 'label',
                            value: 'label'
                        }, {
                            type: 'input',
                            field: 'children',
                            value: 'children'
                        }, {
                            type: 'input',
                            field: 'disabled',
                            value: 'disabled'
                        }, { type: 'input', field: 'leaf' }])
                    }
                },
                {
                    type: 'switch',
                    field: 'showAllLevels',
                    value: true
                },
                {
                    type: 'switch',
                    field: 'collapseTags'
                },
                {
                    type: 'input',
                    field: 'separator'
                },
                {
                    type: 'switch',
                    field: 'filterable'
                },
            ]
        ]);
    }
};
