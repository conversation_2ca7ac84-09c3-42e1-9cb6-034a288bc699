<template>
  <div class="http-connector-detail">
    <!-- HTTP连接器详情头部 -->
    <connector-detail-header
      :connector-detail="connectorDetail"
      type-label="HTTP连接器"
      type-tag-type="success"
      icon-class="el-icon-link"
      :icon-style="httpIconStyle"
      default-name="未命名HTTP连接器"
      :meta-info="httpMetaInfo"
      @go-back="goBack"
    />

    <!-- 使用公共的Tab导航组件 -->
    <connector-tab-navigation
      :connector-detail="connectorDetail"
      :custom-tabs="httpTabs"
      :default-active-tab="activeTab"
      :saving="saving"
      @tab-change="handleTabChange"
      @save-basic-config="handleSaveBasicConfig"
    >
      <!-- 认证配置 -->
      <template #auth="{ connectorDetail }">
        <div class="content-header">
          <span class="header-desc">配置HTTP连接器的认证方式、参数和认证链路</span>
          <el-button
            type="primary"
            size="small"
            :loading="saving"
            @click="handleSaveConfig"
            class="mac-style-btn save-config-btn"
          >
            <i class="el-icon-check"></i>
            保存配置
          </el-button>
        </div>

        <div class="content-body">
          <!-- 基础认证配置 -->
          <http-connector-auth-config
            :connector-id="connectorId"
            :config="connectorDetail.config || {}"
            :auth-requests="connectorDetail.authRequests || []"
            @config-change="handleAuthConfigChange"
            @auth-requests-change="handleAuthRequestsChange"
          />

          <!-- 认证链路配置 -->
          <http-connector-auth-chain
            :connector-id="connectorId"
            :config="connectorDetail.config || {}"
            :auth-requests="connectorDetail.authRequests || []"
            @auth-requests-change="handleAuthRequestsChange"
            @save-config="handleSaveConfig"
          />
        </div>
      </template>

      <!-- 接口管理 -->
      <template #api="{ connectorDetail }">
        <div class="content-body">
          <http-connector-api-management
            ref="apiManagement"
            :connector-id="connectorId"
            :connector-detail="connectorDetail"
          />
        </div>
      </template>

      <!-- 监控 -->
      <template #monitor>
        <div class="content-header">
          <span class="header-desc">监控HTTP连接器的运行状态和性能指标</span>
        </div>

        <div class="content-body">
          <div class="monitor-placeholder">
            <el-empty description="监控功能开发中...">
              <el-button type="primary">配置监控</el-button>
            </el-empty>
          </div>
        </div>
      </template>
    </connector-tab-navigation>
  </div>
</template>

<script>
import { getHttpConnectorAuthDetail, saveOrUpdateHttpConnector } from '@system/api/integration/http-connector'
import HttpConnectorAuthConfig from './components/HttpConnectorAuthConfig'
import HttpConnectorAuthChain from './components/HttpConnectorAuthChain'
import HttpConnectorApiManagement from './components/HttpConnectorApiManagement'
import ConnectorDetailHeader from '../components/ConnectorDetailHeader'
import ConnectorTabNavigation from '../components/ConnectorTabNavigation'

export default {
  name: 'HttpConnectorDetail',
  components: {
    HttpConnectorAuthConfig,
    HttpConnectorAuthChain,
    HttpConnectorApiManagement,
    ConnectorDetailHeader,
    ConnectorTabNavigation
  },
  data() {
    return {
      connectorId: '',
      connectorDetail: {},
      loading: false,
      saving: false,
      activeTab: 'basic', // 当前激活的tab，默认为基础配置
      // HTTP连接器特有的tabs（除了基础配置外）
      httpTabs: [
        {
          key: 'auth',
          label: '认证配置',
          icon: 'el-icon-key'
        },
        {
          key: 'api',
          label: '接口管理',
          icon: 'el-icon-connection'
        },
        {
          key: 'monitor',
          label: '监控',
          icon: 'el-icon-monitor'
        }
      ]
    }
  },
  computed: {
    // HTTP连接器图标样式
    httpIconStyle() {
      return {
        background: 'linear-gradient(135deg, #409eff, #67c23a)'
      }
    },

    // HTTP连接器元数据信息
    httpMetaInfo() {
      const metaInfo = []

      if (this.connectorDetail.baseUrl) {
        metaInfo.push({
          icon: 'el-icon-connection',
          label: '基础URL',
          value: this.connectorDetail.baseUrl
        })
      }

      return metaInfo
    },


  },
  created() {
    this.connectorId = this.$route.query.id || this.$route.params.id
    this.loadConnectorDetail()
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 处理更新连接器名称
    async handleUpdateName(newName) {
      // 更新本地数据
      this.connectorDetail.name = newName
      // 可以在这里添加额外的业务逻辑，比如重新加载数据
      await this.loadConnectorDetail()
    },

    // 处理更新连接器描述
    async handleUpdateDescription(newDescription) {
      // 更新本地数据
      this.connectorDetail.description = newDescription
      // 可以在这里添加额外的业务逻辑，比如重新加载数据
      await this.loadConnectorDetail()
    },

    // 处理更新连接器状态
    async handleUpdateEnabled(enabled) {
      // 更新本地数据
      this.connectorDetail.enabled = enabled
      // 可以在这里添加额外的业务逻辑，比如重新加载数据
      await this.loadConnectorDetail()
    },

    // 处理操作按钮点击
    handleActionClick(action) {
      switch (action) {
        case 'export':
          this.handleExport()
          break
        case 'test':
          this.handleTest()
          break
        default:
          console.warn('未知的操作:', action)
      }
    },

    // 处理导出
    handleExport() {
      this.$message.info('导出功能开发中...')
    },

    // 处理测试
    handleTest() {
      this.$message.info('测试功能开发中...')
    },



    // 处理tab切换
    handleTabChange(tabKey) {
      this.activeTab = tabKey
    },

    // 处理保存基础配置
    handleSaveBasicConfig(basicConfig) {
      // 更新连接器详情
      this.connectorDetail = { ...this.connectorDetail, ...basicConfig }
      // 调用保存方法
      this.handleSaveConfig()
    },

    // 加载HTTP连接器详情
    async loadConnectorDetail() {
      if (!this.connectorId) return

      this.loading = true
      try {
        const response = await getHttpConnectorAuthDetail(this.connectorId)
        this.connectorDetail = response || {}

        // 确保 config 对象存在
        if (!this.connectorDetail.config) {
          this.connectorDetail.config = {}
        }

        // 如果顶层有 authActuator 但 config 中没有，则同步到 config 中
        if (this.connectorDetail.authActuator && !this.connectorDetail.config.authActuator) {
          this.connectorDetail.config.authActuator = this.connectorDetail.authActuator
        }
        if (this.connectorDetail.tokenCacheSeconds && !this.connectorDetail.config.tokenCacheSeconds) {
          this.connectorDetail.config.tokenCacheSeconds = this.connectorDetail.tokenCacheSeconds
        }
      } catch (error) {
        console.error('加载HTTP连接器详情失败:', error)
        this.$message.error('加载HTTP连接器详情失败')
      } finally {
        this.loading = false
      }
    },

    // 处理认证配置变化
    handleAuthConfigChange(config) {

      // 更新 config 对象
      this.connectorDetail.config = { ...this.connectorDetail.config, ...config }

      // 特别处理 authActuator 字段，确保它也被更新到顶层
      if (config.authActuator) {
        this.connectorDetail.authActuator = config.authActuator
      }
      if (config.tokenCacheSeconds) {
        this.connectorDetail.tokenCacheSeconds = config.tokenCacheSeconds
      }

    },

    // 处理认证请求变化
    handleAuthRequestsChange(authRequests) {
      this.connectorDetail.authRequests = authRequests
    },

    // 处理保存配置
    async handleSaveConfig() {
      this.saving = true
      try {
        // 通用配置字段铺到外层
        if (this.connectorDetail.config && this.connectorDetail.config.authActuator) {
          this.connectorDetail.authActuator = this.connectorDetail.config.authActuator
          this.connectorDetail.tokenCacheSeconds = this.connectorDetail.config.tokenCacheSeconds
        }

        await saveOrUpdateHttpConnector(this.connectorDetail)
        this.$message.success('HTTP连接器配置保存成功')
        // 重新加载数据
        await this.loadConnectorDetail()
      } catch (error) {
        console.error('保存HTTP连接器配置失败:', error)
        this.$message.error('保存HTTP连接器配置失败')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.http-connector-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4f8 50%, #f0f9ff 100%);
  display: flex;
  flex-direction: column;
  // 移除所有padding，让头部完全贴边
  padding: 0;

  // 内容区域样式
  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: rgba(248, 250, 252, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(148, 163, 184, 0.1);

    .header-desc {
      font-size: 13px;
      color: #64748b;
      line-height: 1.4;
    }

    .el-button {
      border-radius: 6px;
      padding: 6px 12px;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 1px 4px rgba(24, 144, 255, 0.2);
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      }

      i {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }

  .content-body {
    flex: 1;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 24px;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .monitor-placeholder {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// MAC风格按钮样式
.mac-style-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  &:active {
    transform: translateY(0);
  }

  &.save-config-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }
  }



  &:disabled {
    background: #e0e6ed;
    color: #8c8c8c;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;

    &:hover {
      background: #e0e6ed;
      transform: none;
      box-shadow: none;
    }
  }
}
</style>
