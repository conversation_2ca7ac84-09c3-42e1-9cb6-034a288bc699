const axios = require('axios')
const FormData = require('form-data')
import { getToken } from '@/utils/auth'

export default function uploadFile({ file, onProgress, onSuccess, onError }) {
    const data = new FormData()
    data.append('file', file)
    const config = {
        method: 'post',
        url: process.env.VUE_APP_BASE_API + 'file/minio/uploadFile',
        headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken()
        },
        data: data,
        onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(percentCompleted, file);
        },
    }
    axios(config)
        .then(function (res) {
            onSuccess(res.data, file);
        })
        .catch(function (error) {
            onError(error, file);
        })
}