<template>
  <div class="profile-page">
    <el-page-header @back="goBack()" content="个人中心">
    </el-page-header>
    <!-- 页面头部 -->
    <div class="page-header" style="margin-top: 20px;">
      <div class="header-title">
        <span class="header-subtitle">查看和管理您的个人信息</span>
      </div>
    </div>

    <div class="profile-container">
      <!-- 顶部个人信息卡片 -->
      <div class="profile-overview">
        <div class="overview-content">
          <div class="user-avatar">
            <el-upload action="#" :show-file-list="false" accept="image/png,image/jpeg"
              :before-upload="beforeAvatarUpload" :http-request="handleAvatarUpload" :on-success="handleImgSuccess"
              :on-error="handleImgError">
              <el-avatar :size="rpx(100)" :src="picUrl">
                <div style="background-color: #c0c4cc;"> {{ userInfo.nickName ? userInfo.nickName.charAt(0).toUpperCase() : (userInfo.name ? userInfo.name.charAt(0).toUpperCase() : 'U')
                  }}</div>
              </el-avatar>
            </el-upload>
          </div>
          <div class="user-info">
            <el-input v-if="isEditName" ref="inputName" style="margin-bottom: 10px;" v-model="tempName"
              @blur="handleSaveName"></el-input>
            <template v-else>
              <h2 style="display: inline-block; max-width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;vertical-align: middle;
">{{ userInfo.nickName || userInfo.name || '未设置' }}</h2>
              <i style="display: inline-block;vertical-align: middle;margin-left: 20px;cursor: pointer;"
                class="el-icon-edit" @click="handleEditName()"></i>
            </template>
            <div class="info-tags">
              <el-tag size="medium" type="primary" effect="plain">
                <i class="el-icon-user-solid"></i>
                {{ userInfo.roles && userInfo.roles.length ? userInfo.roles.join(', ') : '暂无角色' }}
              </el-tag>
              <el-tag size="medium" type="success" effect="plain">
                <i class="el-icon-office-building"></i>
                {{ userInfo.deptName || '暂无部门' }}
              </el-tag>
            </div>
            <div class="quick-info">
              <div class="info-item" v-if="userInfo.email">
                <i class="el-icon-message"></i>
                <span>{{ userInfo.email }}</span>
              </div>
              <div class="info-item" v-if="userInfo.phone">
                <i class="el-icon-mobile-phone"></i>
                <span>{{ userInfo.phone }}</span>
              </div>
              <div class="info-item" v-if="!userInfo.email && !userInfo.phone">
                <span class="text-muted">暂无联系方式</span>
              </div>
            </div>
          </div>
        </div>
        <div class="screen-saver">
          <div class="title">锁屏背景</div>
          <el-upload class="screen-saver-uploader" action="#" accept="image/png,image/jpeg" :show-file-list="false"
            :http-request="handleAvatarUpload2" :on-error="handleImgError">
            <img v-if="screenSaverimageUrl.length > 0" :src="url + screenSaverimageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <div class="del">
              <i class="el-icon-delete"
                @click.stop="screenSaverimageUrl = ''; $store.dispatch('user/putAccountConfig', { screenSaverimageUrl: '' })"></i>
            </div>
          </el-upload>
        </div>
      </div>

      <div class="profile-layout">
        <!-- 左侧内容区 -->
        <div class="left-section">
          <div class="profile-section">
            <div class="card-header">
              <div class="header-left">
                <h3>账号信息</h3>
                <span class="subtitle">账号的基本信息</span>
              </div>
            </div>
            <div class="card-body">
              <el-form label-width="100px" class="profile-form">
                <el-form-item label="用户名称">
                  <span>{{ userInfo.nickName || userInfo.name || '未设置' }}</span>
                  <el-button v-if="userInfo.nickName || userInfo.name" type="text" class="copy-btn" @click="handleCopy(userInfo.nickName || userInfo.name)">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                </el-form-item>
                <el-form-item label="用户名">
                  <span>{{ userInfo.username || '未设置' }}</span>
                  <el-button v-if="userInfo.username" type="text" class="copy-btn"
                    @click="handleCopy(userInfo.username)">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                </el-form-item>
                <el-form-item label="手机号码">
                  <span>{{ userInfo.phone || '未设置' }}</span>
                  <el-button v-if="userInfo.phone" type="text" class="copy-btn" @click="handleCopy(userInfo.phone)">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                </el-form-item>
                <el-form-item label="用户邮箱">
                  <span>{{ userInfo.email || '未设置' }}</span>
                  <el-button v-if="userInfo.email" type="text" class="copy-btn" @click="handleCopy(userInfo.email)">
                    <i class="el-icon-document-copy"></i>
                  </el-button>
                </el-form-item>
                <el-form-item label="所属部门">
                  <span>
                    <el-tag size="medium" type="success" effect="plain">
                      <i class="el-icon-office-building"></i>
                      {{ userInfo.deptName || '暂无部门' }}
                    </el-tag>
                  </span>
                </el-form-item>
                <el-form-item label="所属角色">
                  <span>
                    <el-tag size="medium" type="primary" effect="plain">
                      <i class="el-icon-user-solid"></i>
                      {{ userInfo.roles && userInfo.roles.length ? userInfo.roles.join(', ') : '暂无角色' }}
                    </el-tag>
                  </span>
                </el-form-item>
                <el-form-item label="创建日期">
                  <span>{{ userInfo.createTime || '未知' }}</span>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <!-- <div class="profile-section">
            <div class="card-header">
              <div class="header-content">
                <div class="header-left">
                  <h3>个性化配置</h3>
                  <span class="subtitle">自定义您的使用偏好</span>
                </div>
                <div class="header-right">
                  <el-button type="primary" size="small" @click="handleSaveConfig">保存配置</el-button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <el-form ref="configForm" :model="configForm" label-width="100px">
                <el-form-item label="主题设置">
                  <el-select v-model="configForm.theme">
                    <el-option label="明亮" value="light"></el-option>
                    <el-option label="暗黑" value="dark"></el-option>
                    <el-option label="自定义" value="custom"></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </div> -->
        </div>

        <!-- 右侧自助服务 -->
        <div class="right-section">
          <div class="profile-section">
            <div class="card-header">
              <div class="header-left">
                <h3>自助服务</h3>
                <span class="subtitle">快捷的自助服务功能</span>
              </div>
            </div>
            <div class="card-body">
              <div class="service-grid">
                <div class="service-item" @click="handlePasswordChange">
                  <div class="icon-wrapper"
                    :style="{ background: `linear-gradient(135deg, ${iconColorMap.password}, #E6A23C)` }">
                    <i class="el-icon-lock"></i>
                  </div>
                  <div class="service-info">
                    <span class="title">修改密码</span>
                    <p class="desc">修改您的登录密码</p>
                  </div>
                  <i class="el-icon-arrow-right go-icon"></i>
                </div>

                <!-- 动态渲染可绑定应用 -->
                <div v-for="app in bindableApps" :key="app.code" class="service-item" @click="handleAppBinding(app)">
                  <div class="icon-wrapper" :style="{
                    background: `linear-gradient(135deg, ${getIconColor(app.pluginBeanName)}, ${getIconColor(app.pluginBeanName)}CC)`
                  }">
                    <i :class="app.icon"></i>
                  </div>
                  <div class="service-info">
                    <span class="title">{{ app.name }}</span>
                    <p class="desc">{{ isAppBound(app.code) ? '已绑定，点击解绑' : app.description }}</p>
                  </div>
                  <div class="status-icon">
                    <el-tag :type="isAppBound(app.code) ? 'success' : 'info'" size="small">
                      {{ isAppBound(app.code) ? '已绑定' : '未绑定' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="passwordDialogVisible" width="500px" append-to-body
      custom-class="password-dialog">
      <el-form ref="passwordForm" :model="passwordForm" :rules="passwordRules" label-width="100px"
        class="password-form">
        <div class="form-section">
          <div class="section-title">
            <i class="el-icon-lock"></i>
            <span>修改登录密码</span>
          </div>
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input maxlength='20' v-model="passwordForm.currentPassword" type="password" show-password
              class="custom-input" placeholder="请输入当前密码">
              <i slot="prefix" class="el-icon-key"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input maxlength='20' v-model="passwordForm.newPassword" type="password" show-password
              class="custom-input" placeholder="请输入新密码">
              <i slot="prefix" class="el-icon-lock"></i>
            </el-input>
            <div class="form-tip" style="line-height: 20px;">密码长度在 8 到 20 个字符，包含字母、数字和特殊字符@$!%*#?&.</div>
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input maxlength='20' v-model="passwordForm.confirmPassword" type="password" show-password
              class="custom-input" placeholder="请再次输入新密码">
              <i slot="prefix" class="el-icon-lock"></i>
            </el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPasswordChange">
          <i class="el-icon-check"></i>
          确认修改
        </el-button>
      </div>
    </el-dialog>

    <!-- 密保问题对话框 -->
    <el-dialog :title="userInfo.securityQuestion ? '修改密保问题' : '设置密保问题'" :visible.sync="securityQuestionDialogVisible"
      width="500px" append-to-body>
      <el-form ref="securityQuestionForm" :model="securityQuestionForm" :rules="securityQuestionRules"
        label-width="100px">
        <el-form-item label="密保问题" prop="question">
          <el-select v-model="securityQuestionForm.question" placeholder="请选择密保问题">
            <el-option v-for="item in securityQuestions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="答案" prop="answer">
          <el-input v-model="securityQuestionForm.answer" type="password" show-password placeholder="请输入密保答案" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="securityQuestionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitSecurityQuestion">确认</el-button>
      </div>
    </el-dialog>

    <!-- 添加绑定二维码对话框 -->
    <el-dialog :title="`绑定${currentConnector?.name || ''}`" :visible.sync="bindDialogVisible" width="400px"
      @closed="handleBindDialogClose" append-to-body>
      <div class="qr-code-container">
        <div class="qr-code-wrapper" v-loading="!qrCodeUrl">
          <img v-if="qrCodeUrl && !qrCodeExpired" :src="qrCodeUrl" class="qr-code" :class="{ expired: qrCodeExpired }">
          <div v-if="qrCodeExpired" class="qr-expired" @click="refreshBindQrCode">
            <i class="el-icon-refresh"></i>
            <p>二维码已过期</p>
            <span>点击刷新</span>
          </div>
        </div>
        <div class="qr-tip">
          <i class="el-icon-mobile-phone"></i>
          <span>请使用微信扫码绑定</span>
        </div>
        <div class="countdown" v-if="!qrCodeExpired">
          剩余 {{ remainingTime }} 秒
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserInfo, putProfile } from '@system/api/sys/registration'
import { getConfigList, updateConfig } from '@system/api/sys/config'
import { getDeptInfo } from '@system/api/sys/dept'
import { getBindableApps, getUserBindApps } from '@system/api/sys/connector'
import { changePassword, unbindAccount, getBindQrCode, pollBindStatus, getWechatQrCode } from '@system/api/sys/auth'

import UploadHelper from '@/components/auto-page/fileUpload/uploadHelper.js'

import { stack } from '@/router'


export default {
  data() {
    // 密码验证规则
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else if (value.length < 8) {
        callback(new Error('密码长度不能小于8位'))
      } else if (!/^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&.])[A-Za-z\d@$!%*#?&.]{8,20}$/.test(value)) {
        callback(new Error('密码必须包含字母、数字和特殊字符@$!%*#?&.'))
      } else {
        callback()
      }
    }

    // 确认密码验证
    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    return {
      url: process.env.VUE_APP_FILE_URL,
      screenSaverimageUrl: '',
      isEditName: false,
      tempName: '',
      userInfo: {
        username: '',
        name: '',
        phone: '',
        email: '',
        deptName: '',
        deptId: '',
        roles: [],
        createTime: '',
        picUrl: this.$store.state.user.info.picUrl
      },
      configForm: {
        theme: 'light',
        layout: 'grid',
        notifications: {
          email: false,
          sms: false
        }
      },
      passwordDialogVisible: false,
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' },
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      securityQuestionDialogVisible: false,
      securityQuestionForm: {
        question: '',
        answer: ''
      },
      securityQuestionRules: {
        question: [
          { required: true, message: '请选择密保问题', trigger: 'change' }
        ],
        answer: [
          { required: true, message: '请输入密保答案', trigger: 'blur' },
          { min: 3, message: '答案长度不能小于3个字符', trigger: 'blur' }
        ]
      },
      securityQuestions: [
        { value: 'pet', label: '您的第一只宠物叫什么名字？' },
        { value: 'school', label: '您的母校是哪所学校？' },
        { value: 'city', label: '您的出生地是哪个城市？' }
      ],
      bindableApps: [], // 可绑定的应用列表
      userBindApps: [], // 用户已绑定的应用ID列表
      connectorMap: {}, // 存储连接器code和id的映射关系
      iconColorMap: {
        'WeChatOfficialAuthPlugin': '#07C160', // 微信绿
        'DingTalkAuthPlugin': '#1890FF', // 钉钉蓝
        'WeChatMiniAuthPlugin': '#07C160', // 微信小程序绿
        'OAuthPlugin': '#07C160', // OAuth 紫
        'AlipayAuthPlugin': '#1677FF', // 支付宝蓝
        'WeiboAuthPlugin': '#FF6B6B', // 微博红
        'QQAuthPlugin': '#12B7F5', // QQ蓝
        'password': '#F56C6C', // 密码修改红色
      },
      bindDialogVisible: false,
      currentConnector: null,
      qrCodeUrl: '',
      qrCodeKey: '',
      qrCodeExpired: false,
      remainingTime: 120,
      pollingInterval: null,
    }
  },
  computed: {
    picUrl() {
      return this.userInfo.picUrl ? this.url + this.userInfo.picUrl : null
    }
  },
  watch: {
    '$store.state.user.userExtraInfo': {
      handler(val) {
        let config = val?.accountConfig?.config || {}
        this.screenSaverimageUrl = config.screenSaverimageUrl || ''
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.fetchUserInfo()
    this.fetchUserConfig()
    this.fetchBindableApps()
    this.fetchUserBindApps()
  },
  methods: {
    async fetchUserInfo() {
      try {
        const data = await getUserInfo()
        this.userInfo = {
          ...this.userInfo,
          ...data,
        }
        this.$set(this.userInfo, 'picUrl', data.picUrl)

        // 如果有部门ID,获取部门信息
        if (this.userInfo.deptId) {
          this.fetchDeptInfo(this.userInfo.deptId)
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$message.error('获取用户信息失败')
      }
    },
    async fetchDeptInfo(deptId) {
      try {
        const data = await getDeptInfo(deptId)
        this.userInfo.deptName = data.name || '暂无部门'
      } catch (error) {
        console.error('获取部门信息失败:', error)
        this.userInfo.deptName = '暂无部门'
      }
    },
    async fetchUserConfig() {
      try {
        const data = await getConfigList()
        const userConfig = data?.records?.find(config => config.type === 'frontend') || []
        if (userConfig) {
          this.configForm = { ...userConfig.config }
        }
      } catch (error) {
        console.error('获取配置信息失败:', error)
        this.$message.error('获取配置信息失败')
      }
    },
    async handleSaveConfig() {
      try {
        await updateConfig({
          type: 'frontend',
          config: this.configForm
        })
        this.$message.success('配置更新成功')
      } catch (error) {
        console.error('更新配置失败:', error)
        this.$message.error('更新配置失败')
      }
    },
    handlePasswordChange() {
      this.passwordDialogVisible = true
    },
    handleWechatBind() {
      this.$message.info('微信绑定功能开发中...')
    },
    handleDingTalkBind() {
      this.$message.info('钉钉绑定功能开发中...')
    },
    async submitPasswordChange() {
      this.$refs.passwordForm.validate(async valid => {
        if (valid) {
          try {
            // 对密码进行加密
            const data = {
              oldPassword: this.$encruption(this.passwordForm.currentPassword),
              newPassword: this.$encruption(this.passwordForm.newPassword),
              confirmNewPassword: this.$encruption(this.passwordForm.confirmPassword)
            }

            await changePassword(data)
            this.$message.success('密码修改成功')
            this.passwordDialogVisible = false

            // 重置表单
            this.$refs.passwordForm.resetFields()

          } catch (error) {
            console.error('修改密码失败:', error)
            this.$message.error(error.message || '修改密码失败')
          }
        }
      })
    },
    handleSecurityQuestion() {
      this.securityQuestionDialogVisible = true
    },
    submitSecurityQuestion() {
      this.$refs.securityQuestionForm.validate(valid => {
        if (valid) {
          this.userInfo.securityQuestion = this.securityQuestionForm.question
          this.$message.success('密保问题设置成功')
          this.securityQuestionDialogVisible = false
        }
      })
    },
    handleCopy(text) {
      if (!text) return;
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
      this.$message.success('复制成功');
    },
    // 获取可绑定应用列表
    async fetchBindableApps() {
      try {
        const data = await getBindableApps()
        this.bindableApps = data
        // 构建连接器code和id的映射关系
        this.connectorMap = data.reduce((map, app, index) => {
          map[app.code] = index + 1 // 假设ID是从1开始的连续数字
          return map
        }, {})
      } catch (error) {
        console.error('获取可绑定应用列表失败:', error)
      }
    },
    // 获取用户已绑定应用
    async fetchUserBindApps() {
      try {
        const data = await getUserBindApps()
        this.userBindApps = data  // 存储连接器ID列表
      } catch (error) {
        console.error('获取用户已绑定应用失败:', error)
      }
    },
    // 检查应用是否已绑定
    isAppBound(appCode) {
      const connectorId = this.connectorMap[appCode]
      return this.userBindApps.includes(connectorId)
    },
    // 处理应用绑定/解绑
    async handleAppBinding(app) {
      const connectorId = this.connectorMap[app.code]
      if (this.isAppBound(app.code)) {
        // 处理解绑逻辑
        this.$confirm(`确定要解除${app.name}绑定吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            const data = {
              connectorId: connectorId,
              accountId: this.userInfo.id // 从用户信息中获取账号ID
            }
            await unbindAccount(data)
            await this.fetchUserBindApps() // 刷新绑定状态
            this.$message.success('解绑成功')
          } catch (error) {
            console.error('解绑失败:', error)
            this.$message.error(error.message || '解绑失败')
          }
        }).catch(() => {
          // 取消解绑操作
        })
      } else {
        // 处理绑定逻辑
        this.currentConnector = app
        try {
          const data = await getBindQrCode(connectorId, `${window.location.origin}${process.env.BASE_URL}`)

          switch (data.plugin) {
            case 'WeChatOfficialAuthPlugin':
              // 微信公众号的处理逻辑保持不变
              this.bindDialogVisible = true
              this.qrCodeUrl = getWechatQrCode(data.ticket)
              this.qrCodeKey = data.qrCodeKey
              this.remainingTime = data.expire_seconds || 120
              this.qrCodeExpired = false
              this.startBindCountdown()
              this.startBindPolling()
              break

            case 'DingTalkAuthPlugin':
            case 'OAuthPlugin':
              // 钉钉和微信开放平台直接跳转到授权页面
              if (data.authorizationUrl) {
                window.location.href = data.authorizationUrl
              } else {
                this.$message.error('获取授权地址失败')
              }
              break

            default:
              this.$message.info(`暂不支持绑定${app.name}`)
          }
        } catch (error) {
          console.error('获取绑定信息失败:', error)
          this.$message.error('获取绑定信息失败')
        }
      }
    },
    getIconColor(pluginBeanName) {
      return this.iconColorMap[pluginBeanName] || '#409EFF'
    },
    // 获取绑定二维码
    async getBindQrCode(connectorId) {
      try {
        const data = await getBindQrCode(connectorId, `${window.location.origin}${process.env.BASE_URL}`)
        if (data.plugin === 'WeChatOfficialAuthPlugin') {
          this.qrCodeUrl = getWechatQrCode(data.ticket)
          this.qrCodeKey = data.qrCodeKey
          this.remainingTime = data.expire_seconds || 120
          this.qrCodeExpired = false
          this.startBindCountdown()
          this.startBindPolling()
        }
      } catch (error) {
        console.error('获取绑定二维码失败:', error)
        this.$message.error('获取绑定二维码失败')
      }
    },
    // 开始绑定倒计时
    startBindCountdown() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }
      this.countdownTimer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--
        } else {
          clearInterval(this.countdownTimer)
          this.qrCodeExpired = true
        }
      }, 1000)
    },
    // 开始轮询绑定状态
    startBindPolling() {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
      }

      this.pollingInterval = setInterval(async () => {
        try {
          const data = await pollBindStatus(this.qrCodeKey)


          switch (data.status) {
            case 'SCAN':
              // 绑定成功
              clearInterval(this.pollingInterval)
              this.$message.success('绑定成功')

              this.bindDialogVisible = false
              await this.fetchUserBindApps() // 刷新绑定状态
              return
            case 'NOT_SCAN':
              // 未扫码，继续轮询
              break
            case 'TIMEOUT':
              // 二维码过期，停止轮询
              clearInterval(this.pollingInterval)
              this.qrCodeExpired = true
              break
          }
        } catch (error) {
          console.error('轮询失败:', error)
          clearInterval(this.pollingInterval)
        }
      }, 2000)
    },
    // 刷新绑定二维码
    refreshBindQrCode() {
      if (this.currentConnector) {
        const connectorId = this.connectorMap[this.currentConnector.code]
        this.getBindQrCode(connectorId)
      }
    },
    // 处理绑定对话框关闭
    handleBindDialogClose() {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
      }
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }
      this.qrCodeUrl = ''
      this.qrCodeKey = ''
      this.currentConnector = null
      this.qrCodeExpired = false
    },

    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2 MB!')
        return false;
      }
      return true;

    },
    handleAvatarUpload({ file, onProgress, onSuccess, onError }) {
      new UploadHelper().upload(
        file,
        (progress) => {
          onProgress({ percent: progress });
        },
        (url) => {
          onSuccess({ url });
        },
        onError
      );
    },
    async handleImgSuccess(res, file) {
      this.$set(this.userInfo, 'picUrl', res.url)
      try {
        await putProfile({ picUrl: res.url })
        this.$message.success('修改成功');
      } catch (error) {
        this.$message.error('上传失败');
      }
    },
    handleImgError(err, file) {
      this.$message.error('上传失败');
    },

    handleAvatarUpload2({ file, onProgress, onSuccess, onError }) {
      new UploadHelper().upload(
        file,
        (progress) => {
          onProgress({ percent: progress });
        },
        (url) => {
          this.screenSaverimageUrl = url
          this.$store.dispatch('user/putAccountConfig', { screenSaverimageUrl: url })
        },
        onError
      );
    },

    goBack() {
      const stacks = stack()
      if (stacks.length > 0) {
        this.$router.go(-1)
      } else {
        this.$router.push('/')
      }
    },
    handleEditName() {
      this.tempName = this.userInfo.nickName || this.userInfo.name;
      this.isEditName = true,
        this.$nextTick(() => {
          this.$refs.inputName.focus();
        });
    },
    async handleSaveName() {
      try {
        this.isEditName = false
        if (this.tempName == (this.userInfo.nickName || this.userInfo.name)) {
          return;
        }

        this.userInfo.nickName = this.tempName;
        this.userInfo.name = this.tempName;
        await putProfile({
          nickName: this.tempName,
          name: this.tempName
        })
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      } catch (e) {
        this.$message.error('修改失败');
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  margin: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;

  .page-header {
    margin-bottom: 20px;

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 500;
        color: #303133;
      }

      .header-subtitle {
        font-size: 14px;
        color: #909399;
      }
    }
  }

  .profile-overview {
    margin-bottom: 24px;
    background-color: #f8f9fb;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    justify-content: space-between;

    .screen-saver {
      margin-left: 20px;
      min-width: 200px;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .screen-saver-uploader {
        margin-top: 20px;

        ::v-deep .el-upload {
          border: 1px dashed #409eff;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;

          &:hover {
            .del {
              display: block;

              .el-icon-delete {
                font-size: 20px;
                color: #409EFF;
              }
            }
          }
        }

        .del {
          display: none;
          position: absolute;
          height: 30px;
          width: 30px;
          background-color: white;
          top: 0;
          right: 0;
          text-align: center;
          line-height: 30px;
          border-bottom-left-radius: 6px;
          border-top-right-radius: 6px;
          cursor: pointer;
        }
      }

      .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 100px;
        line-height: 100px;
        text-align: center;
      }

      .avatar {
        width: 178px;
        height: 100px;
        display: block;
      }
    }

    .overview-content {
      display: flex;
      align-items: center;
      gap: 32px;
      overflow: auto;

      .user-avatar {
        .el-avatar {
          border-radius: 4px;
          font-size: 18px;
          background-color: transparent;
        }
      }

      .user-info {
        h2 {
          margin: 0 0 16px;
          font-size: 24px;
          font-weight: 600;
          color: #303133;
        }

        .info-tags {
          display: flex;
          gap: 12px;
          margin-bottom: 16px;
        }

        .quick-info {
          display: flex;
          gap: 24px;

          .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #606266;

            i {
              font-size: 16px;
              color: #909399;
            }
          }
        }
      }
    }
  }

  .profile-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;

    .left-section {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .right-section {
      .profile-section {
        height: 100%;
      }
    }
  }


  .profile-section {
    border-radius: 8px;
    border: 1px solid #e6e6e6;
    background: white;
    overflow: hidden;

    .card-header {
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;
      background-color: #f8f9fb;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .header-left {
        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 500;
          color: #303133;
        }

        .subtitle {
          font-size: 13px;
          color: #909399;
          margin-top: 4px;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
      }
    }

    .card-body {
      padding: 24px;
    }

    .form-header {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-bottom: 24px;
      background: linear-gradient(to right, #f5f7fa, #fff);
      border-radius: 8px;

      .el-avatar {
        border: 4px solid rgba(64, 158, 255, 0.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .user-info {
        h2 {
          margin: 0 0 12px;
          font-size: 20px;
          color: #303133;
        }

        .tags {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .service-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .service-item {
      display: grid;
      grid-template-columns: auto 1fr auto;
      align-items: center;
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;

      .icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        i {
          font-size: 24px;
          color: white;
        }
      }

      .service-info {
        margin-left: 16px;

        .title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .desc {
          font-size: 13px;
          color: #909399;
          margin: 0;
        }
      }

      .go-icon {
        font-size: 16px;
        color: #909399;
        transition: all 0.3s;
      }

      .status-icon {
        margin-left: auto;
        padding-left: 16px;
      }

      &:hover {
        background-color: #ecf5ff;
        transform: translateY(-2px);

        .icon-wrapper {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        }

        .go-icon {
          color: #409EFF;
          transform: translateX(4px);
        }

        .status-icon {
          .el-tag--success {
            background-color: #f0f9eb;
          }

          .el-tag--info {
            background-color: #f4f4f5;
          }
        }
      }
    }
  }

  .profile-form {
    max-width: 100%;

    .el-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      ::v-deep .el-form-item__label {
        color: #909399;
      }

      ::v-deep .el-form-item__content {
        color: #303133;
      }
    }

    .copy-btn {
      margin-left: 8px;
      padding: 2px 4px;

      &:hover {
        color: #409EFF;
      }

      i {
        font-size: 14px;
      }
    }
  }
}

::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

::v-deep .el-tabs__header {
  margin: 0;
  padding: 0 24px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

::v-deep .el-tabs__item {
  height: 48px;
  line-height: 48px;
  font-size: 14px;

  &.is-active {
    font-weight: 500;
  }
}

// 覆盖 el-card 的默认样式
::v-deep .el-card {
  border: none;

  .el-card__body {
    padding: 24px;
  }
}

// 密码修改对话框样式
.password-dialog {
  ::v-deep .el-dialog__body {
    padding: 30px;
  }

  .password-form {
    .form-section {
      background-color: #f8f9fb;
      border-radius: 8px;
      padding: 24px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;
        padding-left: 10px;
        border-left: 3px solid #409EFF;

        i {
          font-size: 18px;
          color: #409EFF;
        }

        span {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        padding-left: 20px;
      }
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 22px;

        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          font-weight: normal;
          color: #606266;
        }
      }

      .custom-input {
        .el-input__inner {
          padding-left: 35px;
          height: 40px;
          border-radius: 4px;

          &:hover {
            border-color: #c0c4cc;
          }

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }

        .el-input__prefix {
          left: 10px;
          color: #909399;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;

  .el-button {
    padding: 12px 20px;

    i {
      margin-right: 4px;
    }
  }
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;

  .qr-code-wrapper {
    width: 200px;
    height: 200px;
    margin-bottom: 20px;
    position: relative;

    .qr-code {
      width: 100%;
      height: 100%;

      &.expired {
        opacity: 0.6;
        filter: grayscale(100%);
      }
    }

    .qr-expired {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      i {
        font-size: 24px;
        color: #409EFF;
        margin-bottom: 8px;
      }

      p {
        margin: 0;
        color: #606266;
      }

      span {
        color: #409EFF;
        margin-top: 4px;
      }
    }
  }

  .qr-tip {
    display: flex;
    align-items: center;
    color: #606266;
    margin-bottom: 12px;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }

  .countdown {
    color: #909399;
    font-size: 14px;
  }
}

::v-deep .el-page-header__content {
  color: #303133;
}
</style>