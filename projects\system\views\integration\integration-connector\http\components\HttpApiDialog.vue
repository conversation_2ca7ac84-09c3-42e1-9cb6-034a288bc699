<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="70%"
    top="3vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    custom-class="modern-api-dialog"
    @close="handleClose"
  >
    <div class="api-dialog-content">
      <el-form
        ref="apiForm"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="small"
        class="api-form"
        :disabled="mode === 'view'"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-header">
            <h4 class="section-title">基础信息</h4>
          </div>
          <div class="section-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="接口名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入接口名称"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="接口编码" prop="code">
                  <el-input
                    v-model="formData.code"
                    placeholder="请输入接口编码"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="接口类型" prop="type">
                  <el-select
                    v-model="formData.type"
                    placeholder="请选择接口类型"
                    style="width: 100%"
                  >
                    <el-option label="认证接口" value="AUTH" />
                    <el-option label="业务接口" value="API" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="enabled">
                  <el-switch
                    v-model="formData.enabled"
                    active-text="启用"
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="接口描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="2"
                placeholder="请输入接口描述"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 请求配置 -->
        <div class="form-section">
          <div class="section-header">
            <h4 class="section-title">请求配置</h4>
          </div>
          <div class="section-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="请求方法" prop="method">
                  <el-select
                    v-model="formData.method"
                    placeholder="请选择请求方法"
                    style="width: 100%"
                  >
                    <el-option label="GET" value="GET" />
                    <el-option label="POST" value="POST" />
                    <el-option label="PUT" value="PUT" />
                    <el-option label="DELETE" value="DELETE" />
                    <el-option label="PATCH" value="PATCH" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="请求路径" prop="url">
                  <el-input
                    v-model="formData.url"
                    placeholder="如：/api/users 或 /topapi/v2/user/get"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="Content-Type" prop="contentType">
              <el-select
                v-model="formData.contentType"
                placeholder="请选择Content-Type"
                style="width: 100%"
                allow-create
                filterable
                clearable
              >
                <!-- 常用类型 -->
                <el-option-group label="常用类型">
                  <el-option label="application/json" value="application/json" />
                  <el-option label="application/x-www-form-urlencoded" value="application/x-www-form-urlencoded" />
                  <el-option label="multipart/form-data" value="multipart/form-data" />
                  <el-option label="text/plain" value="text/plain" />
                </el-option-group>

                <!-- 文档类型 -->
                <el-option-group label="文档类型">
                  <el-option label="application/xml" value="application/xml" />
                  <el-option label="text/xml" value="text/xml" />
                  <el-option label="text/html" value="text/html" />
                  <el-option label="text/csv" value="text/csv" />
                  <el-option label="application/pdf" value="application/pdf" />
                  <el-option label="application/rtf" value="application/rtf" />
                </el-option-group>

                <!-- 图片类型 -->
                <el-option-group label="图片类型">
                  <el-option label="image/jpeg" value="image/jpeg" />
                  <el-option label="image/png" value="image/png" />
                  <el-option label="image/gif" value="image/gif" />
                  <el-option label="image/webp" value="image/webp" />
                  <el-option label="image/svg+xml" value="image/svg+xml" />
                </el-option-group>

                <!-- 音视频类型 -->
                <el-option-group label="音视频类型">
                  <el-option label="audio/mpeg" value="audio/mpeg" />
                  <el-option label="audio/wav" value="audio/wav" />
                  <el-option label="video/mp4" value="video/mp4" />
                  <el-option label="video/avi" value="video/avi" />
                </el-option-group>

                <!-- 压缩文件 -->
                <el-option-group label="压缩文件">
                  <el-option label="application/zip" value="application/zip" />
                  <el-option label="application/x-rar-compressed" value="application/x-rar-compressed" />
                  <el-option label="application/x-7z-compressed" value="application/x-7z-compressed" />
                  <el-option label="application/gzip" value="application/gzip" />
                </el-option-group>

                <!-- Office文档 -->
                <el-option-group label="Office文档">
                  <el-option label="application/msword" value="application/msword" />
                  <el-option label="application/vnd.openxmlformats-officedocument.wordprocessingml.document" value="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
                  <el-option label="application/vnd.ms-excel" value="application/vnd.ms-excel" />
                  <el-option label="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" value="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
                  <el-option label="application/vnd.ms-powerpoint" value="application/vnd.ms-powerpoint" />
                  <el-option label="application/vnd.openxmlformats-officedocument.presentationml.presentation" value="application/vnd.openxmlformats-officedocument.presentationml.presentation" />
                </el-option-group>

                <!-- 其他类型 -->
                <el-option-group label="其他类型">
                  <el-option label="application/octet-stream" value="application/octet-stream" />
                  <el-option label="application/javascript" value="application/javascript" />
                  <el-option label="text/css" value="text/css" />
                  <el-option label="application/font-woff" value="application/font-woff" />
                  <el-option label="application/font-woff2" value="application/font-woff2" />
                </el-option-group>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 请求头 -->
        <div class="form-section">
          <div class="section-header">
            <h4 class="section-title">请求头</h4>
            <el-button
              v-if="mode !== 'view'"
              type="text"
              size="mini"
              icon="el-icon-plus"
              @click="addHeader"
              class="add-btn"
            >
              添加请求头
            </el-button>
          </div>
          <div class="section-content">
            <div class="headers-editor">
              <div
                v-for="(header, index) in headersList"
                :key="index"
                class="header-item"
              >
                <el-input
                  v-model="header.key"
                  placeholder="Header名称"
                  size="mini"
                  class="header-key"
                />
                <el-input
                  v-model="header.value"
                  placeholder="Header值"
                  size="mini"
                  class="header-value"
                />
                <el-button
                  v-if="mode !== 'view'"
                  type="text"
                  size="mini"
                  icon="el-icon-delete"
                  @click="removeHeader(index)"
                  class="remove-btn"
                />
              </div>
              <div v-if="headersList.length === 0" class="empty-headers">
                <span class="empty-text">暂无请求头配置</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 请求体 -->
        <div class="form-section">
          <div class="section-header">
            <h4 class="section-title">请求体</h4>
            <div class="header-actions">
              <el-button
                v-if="mode !== 'view'"
                type="text"
                size="mini"
                icon="el-icon-document"
                @click="formatJson"
                class="format-btn"
              >
                格式化JSON
              </el-button>
            </div>
          </div>
          <div class="section-content">
            <el-input
              v-model="bodyText"
              type="textarea"
              :rows="8"
              placeholder="请输入请求体内容，支持JSON格式"
              class="body-editor"
            />
            <div class="body-help">
              <i class="el-icon-info"></i>
              支持使用变量，如：{"userId": "${userId}", "name": "${userName}"}
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button
        @click="handleClose"
        class="mac-close-btn"
        size="medium"
      >
        {{ mode === 'view' ? '关闭' : '取消' }}
      </el-button>
      <el-button
        v-if="mode !== 'view'"
        type="primary"
        @click="handleSave"
        :loading="saving"
        class="mac-save-btn"
        size="medium"
      >
        {{ mode === 'create' ? '创建' : '保存' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { 
  createHttpRequest, 
  updateHttpRequest, 
  generateSnowflakeId 
} from '@system/api/integration/http-connector'

export default {
  name: 'HttpApiDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'create', // create, edit, view
      validator: value => ['create', 'edit', 'view'].includes(value)
    },
    apiData: {
      type: Object,
      default: null
    },
    connectorId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      saving: false,
      formData: {
        name: '',
        code: '',
        description: '',
        type: 'API',
        method: 'POST',
        url: '',
        contentType: 'application/json',
        enabled: true
      },
      headersList: [],
      bodyText: '',
      formRules: {
        name: [
          { required: true, message: '请输入接口名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入接口编码', trigger: 'blur' },
          { 
            pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, 
            message: '编码必须以字母开头，只能包含字母、数字和下划线', 
            trigger: 'blur' 
          }
        ],
        type: [
          { required: true, message: '请选择接口类型', trigger: 'change' }
        ],
        method: [
          { required: true, message: '请选择请求方法', trigger: 'change' }
        ],
        url: [
          { required: true, message: '请输入请求路径', trigger: 'blur' }
        ],
        contentType: [
          { required: true, message: '请选择Content-Type', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    dialogTitle() {
      const titleMap = {
        create: '新建接口',
        edit: '编辑接口',
        view: '查看接口'
      }
      return titleMap[this.mode] || '接口配置'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    },
    apiData: {
      handler() {
        if (this.visible) {
          this.initFormData()
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单数据
    async initFormData() {
      if (this.mode === 'create') {
        // 创建模式：重置表单并生成新ID
        this.resetForm()
        try {
          const id = await generateSnowflakeId()
          this.formData.id = id
        } catch (error) {
          console.error('生成ID失败:', error)
        }
      } else if (this.apiData) {
        // 编辑/查看模式：填充现有数据
        this.formData = { ...this.apiData }
        this.parseHeaders()
        this.parseBody()
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        name: '',
        code: '',
        description: '',
        type: 'API',
        method: 'POST',
        url: '',
        contentType: 'application/json',
        enabled: true,
        connectorId: this.connectorId
      }
      this.headersList = []
      this.bodyText = ''
      this.$nextTick(() => {
        this.$refs.apiForm?.clearValidate()
      })
    },

    // 解析请求头
    parseHeaders() {
      this.headersList = []
      if (this.formData.headers && typeof this.formData.headers === 'object') {
        Object.entries(this.formData.headers).forEach(([key, value]) => {
          this.headersList.push({ key, value })
        })
      }
    },

    // 解析请求体
    parseBody() {
      if (this.formData.body) {
        if (typeof this.formData.body === 'object') {
          this.bodyText = JSON.stringify(this.formData.body, null, 2)
        } else {
          this.bodyText = this.formData.body
        }
      } else {
        this.bodyText = ''
      }
    },

    // 添加请求头
    addHeader() {
      this.headersList.push({ key: '', value: '' })
    },

    // 删除请求头
    removeHeader(index) {
      this.headersList.splice(index, 1)
    },

    // 格式化JSON
    formatJson() {
      try {
        if (this.bodyText.trim()) {
          const parsed = JSON.parse(this.bodyText)
          this.bodyText = JSON.stringify(parsed, null, 2)
          this.$message.success('JSON格式化成功')
        }
      } catch (error) {
        this.$message.error('JSON格式不正确，无法格式化')
      }
    },

    // 保存
    async handleSave() {
      try {
        await this.$refs.apiForm.validate()
        
        this.saving = true
        
        // 构建保存数据
        const saveData = { ...this.formData }
        
        // 处理请求头
        const headers = {}
        this.headersList.forEach(header => {
          if (header.key && header.value) {
            headers[header.key] = header.value
          }
        })
        saveData.headers = headers
        
        // 处理请求体
        if (this.bodyText.trim()) {
          try {
            saveData.body = JSON.parse(this.bodyText)
          } catch (error) {
            saveData.body = this.bodyText
          }
        } else {
          saveData.body = null
        }
        
        // 调用API
        if (this.mode === 'create') {
          await createHttpRequest(saveData)
          this.$message.success('创建成功')
        } else {
          await updateHttpRequest(saveData)
          this.$message.success('保存成功')
        }
        
        this.$emit('success')
        this.handleClose()
      } catch (error) {
        if (error.message) {
          console.error('保存失败:', error)
          this.$message.error('保存失败: ' + error.message)
        }
      } finally {
        this.saving = false
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    }
  }
}
</script>

<style lang="scss" scoped>
.api-dialog-content {
  max-height: 75vh;
  overflow-y: auto;
  padding: 24px !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;

  .api-form {
    .form-section {
      margin-bottom: 20px !important;
      background: white !important;
      border-radius: 16px !important;
      padding: 24px !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
      border: 1px solid rgba(148, 163, 184, 0.1) !important;
      transition: all 0.3s ease !important;

      &:hover {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
        transform: translateY(-2px) !important;
      }

      .section-header {
        margin-bottom: 20px !important;
        padding-bottom: 12px !important;
        border-bottom: 2px solid #f1f5f9 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        position: relative !important;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 60px;
          height: 2px;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          border-radius: 1px;
        }

        .section-title {
          font-size: 16px !important;
          font-weight: 600 !important;
          color: #1e293b !important;
          margin: 0 !important;
          display: flex !important;
          align-items: center !important;
          gap: 8px !important;

          &::before {
            content: '';
            width: 4px;
            height: 16px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 2px;
          }
        }

        .header-actions {
          display: flex;
          gap: 8px;
        }

        .add-btn,
        .format-btn {
          color: #3b82f6;
          font-size: 12px;
          padding: 6px 12px;
          border-radius: 8px;
          transition: all 0.3s ease;
          background: rgba(59, 130, 246, 0.1);
          border: 1px solid rgba(59, 130, 246, 0.2);

          &:hover {
            color: white;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-color: transparent;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
          }
        }
      }

      .section-content {
        padding: 0;
      }
    }

    .headers-editor {
      .header-item {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .header-key {
          flex: 1;
        }

        .header-value {
          flex: 2;
        }

        .remove-btn {
          color: #ef4444;
          padding: 4px;

          &:hover {
            color: #dc2626;
            background: rgba(239, 68, 68, 0.1);
          }
        }
      }

      .empty-headers {
        text-align: center;
        padding: 20px;
        color: #8c8c8c;
        font-size: 13px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px dashed #d1d5db;

        .empty-text {
          font-style: italic;
        }
      }
    }

    .body-editor {
      ::v-deep .el-textarea__inner {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.6;
        border-radius: 8px;
        border: 1px solid rgba(148, 163, 184, 0.2);

        &:focus {
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }
    }

    .body-help {
      margin-top: 8px;
      font-size: 12px;
      color: #64748b;
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        font-size: 14px;
        color: #3b82f6;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    min-width: 100px;
    height: 40px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .mac-close-btn {
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border: 2px solid #e2e8f0;
    color: #64748b;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3b82f6;
      color: #3b82f6;
      background: linear-gradient(135deg, #f8fafc, #f1f5f9);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15);
    }
  }

  .mac-save-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #1d4ed8, #1e40af);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
      transform: translateY(-2px);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    &:disabled {
      background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
      color: #94a3b8;
      box-shadow: none;
      cursor: not-allowed;
      transform: none;
    }
  }
}
</style>

<!-- 全局样式 -->
<style lang="scss">
// 修改蒙版颜色
::v-deep .el-dialog__wrapper {
  background: rgba(15, 23, 42, 0.4) !important;
  backdrop-filter: blur(8px) !important;
}

::v-deep .modern-api-dialog {
  border-radius: 20px !important;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2) !important;
  margin-bottom: 3vh !important;
  animation: dialogSlideIn 0.3s ease-out;

  .el-dialog {
    border-radius: 20px !important;
    margin: 0 !important;
    border: 1px solid rgba(148, 163, 184, 0.1) !important;
  }

  .el-dialog__header {
    padding: 24px 32px 16px !important;
    border-bottom: 2px solid #f1f5f9 !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    position: relative !important;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 32px;
      right: 32px;
      height: 2px;
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      border-radius: 1px;
    }

    .el-dialog__title {
      font-size: 20px !important;
      font-weight: 700 !important;
      color: #1e293b !important;
      display: flex !important;
      align-items: center !important;
      gap: 12px !important;

      &::before {
        content: '⚙️';
        font-size: 18px;
      }
    }

    .el-dialog__headerbtn {
      top: 20px !important;
      right: 24px !important;
      width: 32px !important;
      height: 32px !important;
      background: rgba(239, 68, 68, 0.1) !important;
      border-radius: 8px !important;
      transition: all 0.3s ease !important;

      &:hover {
        background: rgba(239, 68, 68, 0.2) !important;
        transform: scale(1.1) !important;
      }

      .el-dialog__close {
        color: #ef4444 !important;
        font-size: 16px !important;
        font-weight: bold !important;
      }
    }
  }

  .el-dialog__body {
    padding: 0 !important;
    background: white !important;
  }

  .el-dialog__footer {
    padding: 20px 32px 28px !important;
    border-top: 2px solid #f1f5f9 !important;
    text-align: right !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  }

  // 表单样式
  .el-form-item {
    margin-bottom: 20px !important;

    .el-form-item__label {
      font-weight: 500 !important;
      color: #262626 !important;
      font-size: 14px !important;
    }

    .el-input, .el-select, .el-textarea {
      .el-input__inner, .el-textarea__inner {
        border-radius: 8px !important;
        border: 1px solid #e8e8e8 !important;
        transition: all 0.2s ease !important;
        font-size: 14px !important;

        &:focus {
          border-color: #1890ff !important;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
        }
      }
    }

    .el-switch {
      .el-switch__core {
        border-radius: 12px !important;
        transition: all 0.2s ease !important;
      }

      &.is-checked {
        .el-switch__core {
          background-color: #1890ff !important;
        }
      }
    }
  }
}

// 动画效果
@keyframes dialogSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  ::v-deep .modern-api-dialog {
    width: 85% !important;
  }
}

@media (max-width: 768px) {
  ::v-deep .modern-api-dialog {
    width: 95% !important;
    margin: 20px !important;

    .el-dialog__header {
      padding: 20px 20px 12px !important;

      .el-dialog__title {
        font-size: 18px !important;
      }
    }

    .api-dialog-content {
      padding: 16px !important;

      .form-section {
        padding: 16px !important;
        margin-bottom: 16px !important;
      }
    }

    .el-dialog__footer {
      padding: 16px 20px 20px !important;

      .el-button {
        min-width: 80px !important;
        height: 36px !important;
      }
    }
  }
}
</style>
