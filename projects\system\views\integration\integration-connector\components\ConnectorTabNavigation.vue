<template>
  <div class="connector-tab-navigation">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：Tab导航 -->
      <div class="left-sidebar">
        <div class="tab-navigation">
          <div
            v-for="tab in allTabs"
            :key="tab.key"
            :class="['tab-item', { active: activeTab === tab.key }]"
            @click="switchTab(tab.key)"
          >
            <i :class="tab.icon"></i>
            <span>{{ tab.label }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧：内容区域 -->
      <div class="content-area">
        <div class="content-wrapper">
          <!-- 基础配置 -->
          <div v-show="activeTab === 'basic'" class="tab-content">
            <div class="content-header">
              <span class="header-desc">配置连接器的基础信息和连接参数</span>
              <el-button
                type="primary"
                size="small"
                :loading="saving"
                @click="handleSaveBasicConfig"
                class="mac-style-btn save-config-btn"
              >
                <i class="el-icon-check"></i>
                保存配置
              </el-button>
            </div>

            <div class="content-body">
              <!-- 使用公共表单组件 -->
              <connector-base-form
                v-model="formData"
                ref="baseForm"
                :show-test-connection="true"
              />
            </div>
          </div>

          <!-- 动态插槽内容 -->
          <div v-for="tab in customTabs" :key="tab.key" v-show="activeTab === tab.key" class="tab-content">
            <slot :name="tab.key" :tab="tab" :connector-detail="connectorDetail"></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ConnectorBaseForm from './ConnectorBaseForm'

export default {
  name: 'ConnectorTabNavigation',
  components: {
    ConnectorBaseForm
  },
  props: {
    // 连接器详情数据
    connectorDetail: {
      type: Object,
      default: () => ({})
    },
    // 自定义tabs（除了基础配置外的其他tabs）
    customTabs: {
      type: Array,
      default: () => []
    },
    // 默认激活的tab
    defaultActiveTab: {
      type: String,
      default: 'basic'
    },
    // 保存状态
    saving: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: this.defaultActiveTab,
      formData: {}
    }
  },
  computed: {
    // 基础配置tab（所有连接器都有）
    basicTab() {
      return {
        key: 'basic',
        label: '基础配置',
        icon: 'el-icon-setting'
      }
    },
    // 所有tabs（基础配置 + 自定义tabs）
    allTabs() {
      return [this.basicTab, ...this.customTabs]
    }
  },
  watch: {
    defaultActiveTab(newVal) {
      this.activeTab = newVal
    },
    // 监听connectorDetail变化，同步到formData
    connectorDetail: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.formData = { ...newVal }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 切换tab
    switchTab(tabKey) {
      this.activeTab = tabKey
      this.$emit('tab-change', tabKey)
    },

    // 处理保存基础配置
    async handleSaveBasicConfig() {
      const valid = await this.$refs.baseForm.validate()
      if (valid) {
        this.$emit('save-basic-config', this.formData)
      } else {
        this.$message.warning('请完善必填信息')
      }
    },


  }
}
</script>

<style lang="scss" scoped>
.connector-tab-navigation {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许flex子项收缩
  margin-top: 16px; // 为头部和内容之间添加间距

  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    padding: 0 16px 16px 16px; // 添加左边距，与头部内容对齐
    min-height: 0; // 确保flex子项可以收缩

    @media (max-width: 768px) {
      flex-direction: column;
      padding: 0 8px 8px 8px; // 移动端也保持左右对称
    }

    // 左侧导航栏 - Mac风格
    .left-sidebar {
      width: 180px;
      background: linear-gradient(180deg, #fafbfc 0%, #f5f7fa 100%);
      border-right: none;
      flex-shrink: 0;
      margin: 0 0 16px 0; // 移除左边距，由父容器padding控制
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      backdrop-filter: blur(10px);

      @media (max-width: 768px) {
        width: 100%;
        margin: 0 0 16px 0; // 移动端也移除左右边距
      }

      .tab-navigation {
        padding: 20px 12px;

        .tab-item {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border-radius: 12px;
          position: relative;
          margin-bottom: 4px;

          i {
            font-size: 16px;
            margin-right: 12px;
            color: #8c8c8c;
            transition: all 0.3s ease;
          }

          span {
            font-size: 14px;
            font-weight: 500;
            color: #595959;
            transition: all 0.3s ease;
          }

          &:hover:not(.active) {
            background: rgba(24, 144, 255, 0.08);
            transform: translateX(2px);

            i {
              color: #1890ff;
            }

            span {
              color: #262626;
            }
          }

          &.active {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            transform: translateX(4px);

            i {
              color: #ffffff;
            }

            span {
              color: #ffffff;
              font-weight: 600;
            }

            &::before {
              content: '';
              position: absolute;
              left: -4px;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 20px;
              background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
              border-radius: 2px;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
            }
          }
        }
      }
    }

    // 右侧内容区域 - Mac风格
    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      min-width: 0; // 防止内容撑开

      @media (max-width: 768px) {
        margin: 0; // 移动端移除额外边距，由父容器控制
      }

      .content-wrapper {
        flex: 1;
        padding: 16px 16px 16px 16px;
        overflow-y: auto;
        background: transparent;
        min-height: 0; // 确保可以正确滚动

        .tab-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          min-height: 0; // 防止内容撑开影响布局

          .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 8px 12px;
            background: rgba(248, 250, 252, 0.6);
            border-radius: 8px;
            border: 1px solid rgba(148, 163, 184, 0.1);

            .header-desc {
              font-size: 13px;
              color: #64748b;
              line-height: 1.4;
            }

            .el-button {
              border-radius: 6px;
              padding: 6px 12px;
              font-size: 12px;
              font-weight: 500;
              box-shadow: 0 1px 4px rgba(24, 144, 255, 0.2);
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
              }

              i {
                margin-right: 4px;
                font-size: 12px;
              }
            }
          }

          .content-body {
            flex: 1;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 24px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
  }
}



// MAC风格按钮样式
.mac-style-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }

  &:active {
    transform: translateY(0);
  }

  &.save-config-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }
  }

  &:disabled {
    background: #e0e6ed;
    color: #8c8c8c;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;

    &:hover {
      background: #e0e6ed;
      transform: none;
      box-shadow: none;
    }
  }
}
</style>
