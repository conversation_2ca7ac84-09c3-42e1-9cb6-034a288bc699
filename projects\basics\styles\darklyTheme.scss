// 根据设计图 修改的 dialog / uploader图片 / 弹框中的树结构
#app .main-container {
    background-color: #16191C;
}

/* 全局背景 */
.app-container {
    background-color: #16191C;
}

.app-main {
    background-color: #16191C !important;
}

/*顶部navbar 面包屑*/
.navbar {
    color: #fff !important;
    background-color: #16191C !important;
    border-bottom: 1px solid #374148;
}

/*顶部 面包屑标题*/
.el-breadcrumb__inner a {
    color: #fff !important;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner span {
    color: #fff !important;
}

.navbar .vue-treeselect .vue-treeselect__control {
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid #020202;
}

.navbar .vue-treeselect__single-value {
    color: #fff;
}

.navbar .vue-treeselect__menu {
    background-color: #16191C;
}

.navbar .vue-treeselect__menu--transition {
    background-color: red;
}

.navbar .vue-treeselect__menu--container {
    background-color: red;
}

.navbar .vue-treeselect__menu--placeholder {
    background-color: red;
}

// 布局样式
.el-header {
    background-color: #374148;
    color: #333;
    border-bottom: 1px solid #16191C;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.el-header .el-input__inner {
    background-color: #020202;
    border: 1px solid #020202;
}


.el-footer {
    background-color: #374148;
    color: #333;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.el-footer .pagination-container {
    background-color: #374148 !important;
    color: #333;
}

.el-footer .pagination-container .el-pagination__total {
    color: #fff;
}

.el-footer .pagination-container .el-pagination__jump {
    color: #fff;
}

.el-footer .pagination-container .el-input__inner {
    background-color: #374148;
    border: 1px solid #647480;
    color: #fff;
}

.el-footer .el-pagination .btn-prev {
    background-color: #374148;
    border: 1px solid 647480;
}

.el-footer .el-pagination .btn-next {
    background-color: #374148;
    border: 1px solid 647480;
}

.el-footer .el-pagination.is-background .el-pager .active {
    background-color: #374148 !important;
    border: 1px solid 647480;
}

.el-main {
    background-color: #374148;
    color: #333;
}


.el-aside {
    // background-color: #ffffff;
    // color: #333;
}

/* 表格样式 */
.el-table {
    background-color: #374148;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

/*表头的样式：*/
.el-table th {
    background-color: #647480;
    padding: 5px 0px;
    color: #fff;

}

.el-table td {
    padding: 5px 0px;
}

/*每行的样式：*/
.el-table .el-table__row {
    background-color: #374148;
    color: rgb(255, 255, 255);

}

/*鼠标选中每行的样式：*/
.el-table .el-table__body tr.current-row>td {
    background-color: #16191C !important;
}

/*鼠标hover每行的样式：*/
.el-table .el-table__body tr:hover>td {
    background-color: #16191C !important;
}

/*修改表格每行边框的样式：*/
.el-table td,
.el-table th.is-leaf {
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    //   border-right:1px solid #4e73ac;
}

.el-table--border::after,
.el-table--group::after {
    width: 0;
}

.el-table::before {
    height: 0;
}

//表格显示 全部字段 tip 控制宽度
.el-tooltip__popper {}



// div {
//     background-color: #374148;
// }

/* 设计图dialog样式 */
.el-dialog__header {
    border-bottom: 1px solid #e9e9e9;
}

// 添加单张图片样式
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
}

.avatar-uploader-icon {
    color: #8c939d;
}

.pagination-container {}


//表单label
.el-form-item__label {}

.el-form-item {}

.el-form-item__label {}
