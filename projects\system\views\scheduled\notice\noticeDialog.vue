<template>
  <el-dialog :title="(value?.id ? '修改' : '新增') + '通知'" :visible.sync="visible" :before-close="handleClose" :close-on-click-modal="false" width="600px">
    <el-form :model="value" label-width="80px" :rules="rules" ref="formRef" v-if="value">
      <el-form-item label="名称" prop="name">
        <el-input v-model="value.name" />
      </el-form-item>
      <el-row :gitter="20">
        <el-col :span="12">
          <el-form-item label="分组" prop="groupName">
            <el-select v-model="value.groupName" style="width: 100%;" >
              <el-option v-for="(group, index) in groups" :index="index" :label="group.name" :value="group.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否开启" prop="enable">
            <el-switch v-model="value.enable" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gitter="20">
        <el-col :span="16">
          <el-form-item label="时间间隔" prop="intervalTime">
            <el-input-number v-model.number="value.intervalTime" :min="0" style="width:100%" :step="1" step-strictly/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="intervalUnit" label-width="0">
            <el-select v-model="value.intervalUnit" style="width:100%">
              <el-option v-for="entry in Object.entries(timeUnit)" :label="entry[1]" :value="entry[0]" :key="entry[0]"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="通知预设" prop="noticeId">
        <el-select v-model="value.noticeId" style="width:100%" filterable>
          <el-option v-for="item in presetTaskList" :key="item.id" :value="item.id" :label="item.name" />
        </el-select>
      </el-form-item>
      <el-card>
        <div slot="header" style="display:flex;justify-content:space-between">
          <label>通知预设信息</label>
          <el-button type="text" icon="el-icon-search" @click="handlePresetTaskDetail">查看详情</el-button>
        </div>
        <el-descriptions v-if="presetTask">
          <el-descriptions-item label="名称">{{ presetTask.name }}</el-descriptions-item>
          <el-descriptions-item label="模板编号">{{ presetTask.templateCode }}</el-descriptions-item>
          <el-descriptions-item label="渠道编码">{{ presetTask.providerCode }}</el-descriptions-item>
          <el-descriptions-item label="发送类型">{{ presetTask.sendType === 'NOW' ? '立即发送' : '延迟发送' }}</el-descriptions-item>
          <el-descriptions-item label="接收类型" :span="2">{{ receiverTypeMap[presetTask.receiverType] }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="3">{{ presetTask.description }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-form>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handlerSubmit" :loading="loading">提 交</el-button>
    </span>

  </el-dialog>
</template>

<script>
import notice from '@system/api/scheduled/notice'
import {getPresetTaskList} from '@system/api/notice/manager'
import dict from './dict'
export default {
  name: 'noticeDialog',
  components: {
  },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        groupName: [
          { required: true, message: '请选择分组', trigger: 'change' }
        ],
        noticeId: [
          { required: true, message: '请选择通知预设', trigger: 'change' }
        ]
      },
      loading: false,
      presetTaskList: []
    };
  },
  mounted() {
    getPresetTaskList({size: -1}).then(res => { 
      this.presetTaskList = res.records
    })
  },
  watch: {
  },
  computed: {
    receiverTypeMap() {
      return dict.receiverType
    },
    visible() {
      return typeof (this.value) !== 'undefined'
    },
    presetTask() {
      return this.presetTaskList.find(task => task.id === this.value.noticeId)
    },
    timeUnit() {
      return dict.timeUnit
    }
  },
  props: {
    types: {
      type: Array,
      default: () => {
        return []
      }
    },
    value: {
      type: Object
    },
    groups: {
      type: Array,
      default: () => {
        return []
      }
    },
    statuses: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  methods: {
    handlePresetTaskDetail() {
      let base = process.env.BASE_URL.at(-1) === '/' ? process.env.BASE_URL.slice(0, -1) : process.env.BASE_URL
      window.open(`${base}/notice/preset/task`, '_blank')
    },
    handleClose() {
      this.$emit('change', undefined)
    },
    handlerSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          const result = this.value.id ? notice.update(this.value) : notice.save(this.value)
          result.then(res => {
            this.$message.success('操作成功')
            this.loading = false
            var data = this.value
            if (!data.id) {
              data.id = res[0].id
            }
            this.$emit('after-submit', data)
            this.handleClose()
          }).catch(e => {
            this.loading = false
          })
        }
      })
    }
  }
};
</script>

<style lang="scss" scoped> 
.label-group {
  border-left: 5px solid #409EFF; 
  padding-left: 15px;
  margin-bottom: 20px;
  font-weight: bolder;
}
.tips {
  line-height: 20px;
  color: #909399;
}
</style>