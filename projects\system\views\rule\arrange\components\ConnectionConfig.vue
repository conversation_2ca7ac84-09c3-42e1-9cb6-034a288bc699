<template>
  <div class="connection-config">
    <el-form
        ref="form"
        :model="formData"
        label-width="80px"
        size="small"
    >
      <el-form-item label="连线类型">
        <el-select v-model="formData.type" @change="handleChange">
          <el-option label="默认" value="default"></el-option>
          <el-option label="条件" value="condition"></el-option>
          <el-option label="循环" value="loop"></el-option>
        </el-select>
      </el-form-item>

      <template v-if="formData.type === 'condition'">
        <el-form-item label="条件表达式">
          <el-input
              type="textarea"
              v-model="formData.condition"
              placeholder="请输入条件表达式"
              :rows="3"
              @change="handleChange"
          ></el-input>
        </el-form-item>
      </template>

      <template v-if="formData.type === 'loop'">
        <el-form-item label="循环次数">
          <el-input-number
              v-model="formData.loopTimes"
              :min="1"
              placeholder="循环次数"
              @change="handleChange"
          ></el-input-number>
        </el-form-item>
      </template>

      <el-form-item label="优先级">
        <el-input-number
            v-model="formData.priority"
            :min="1"
            :max="10"
            @change="handleChange"
        ></el-input-number>
      </el-form-item>

      <el-form-item>
        <el-button type="danger" size="small" @click="handleDelete">删除连线</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ConnectionConfig',

  props: {
    connection: {
      type: Object,
      required: true
    },
    sourceNode: {
      type: Object,
      required: true
    },
    targetNode: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      formData: {
        type: 'default',
        condition: '',
        loopTimes: 1,
        priority: 1
      }
    }
  },

  watch: {
    connection: {
      immediate: true,
      handler(connection) {
        this.formData = {
          type: connection.type || 'default',
          condition: connection.condition || '',
          loopTimes: connection.loopTimes || 1,
          priority: connection.priority || 1
        }
      }
    }
  },

  methods: {
    handleChange() {
      this.$emit('update', {
        ...this.connection,
        ...this.formData
      })
    },

    handleDelete() {
      this.$confirm('确认删除此连线?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete')
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.connection-config {
  padding: 10px 0;
}
</style> 