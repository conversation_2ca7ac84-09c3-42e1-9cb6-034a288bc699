<template>
  <div class="audit-container">
    <div class="table-toolbar">
      <div class="filter-container">
        <el-input
          v-model="queryParams.uri"
          placeholder="请输入接口URI搜索"
          prefix-icon="el-icon-search"
          clearable
          class="search-input"
          @keyup.enter.native="handleSearch"
          @clear="handleSearch"
        >
        </el-input>
        <el-select v-model="queryParams.method" placeholder="请选择请求方法" clearable @change="handleSearch">
          <el-option label="GET" value="GET" />
          <el-option label="POST" value="POST" />
          <el-option label="PUT" value="PUT" />
          <el-option label="DELETE" value="DELETE" />
        </el-select>
        <el-select v-model="queryParams.statusCode" placeholder="请选择响应状态" clearable @change="handleSearch">
          <el-option label="成功" value="200" />
          <el-option label="未授权" value="401" />
          <el-option label="禁止访问" value="403" />
          <el-option label="未找到" value="404" />
          <el-option label="服务器错误" value="500" />
        </el-select>
        <el-date-picker
          v-model="queryParams.ranges.createTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleSearch"
          @clear="handleSearch">
        </el-date-picker>
      </div>

      <div class="button-group">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshList">
          刷新列表
        </el-button>
      </div>
    </div>

    <div class="table-wrapper">
      <el-table 
        ref="auditTable"
        v-loading="loading" 
        :data="auditList" 
        border 
        stripe
        fit
        style="width: 100%"
        highlight-current-row
        class="audit-table"
      >
        <el-table-column prop="uri" label="接口URI" min-width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="uri-cell">
              <el-tag size="mini" v-if="scope.row.protocol">{{ scope.row.protocol }}</el-tag>
              <el-button type="text" size="mini" @click="handleViewDetail(scope.row)">{{ scope.row.uri }}</el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="method" label="请求方法" :width=rpx(100) align="center">
          <template slot-scope="scope">
            <el-tag :type="getMethodTagType(scope.row.method)">
              {{ scope.row.method }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="statusCode" label="响应状态" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.statusCode)">
              {{ scope.row.statusCode }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="userName" label="用户信息" min-width="140">
          <template slot-scope="scope">
            <div class="user-info" v-if="scope.row.userName">
              <span class="username">{{ scope.row.userName }}</span>
              <el-divider direction="vertical"></el-divider>
              <span class="nickname">{{ scope.row.nickName }}</span>
            </div>
            <div v-else>
              <el-tag type="info" size="small">未登录</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="host" label="主机地址" min-width="150" show-overflow-tooltip />

        <el-table-column prop="duration" label="耗时(ms)" min-width="100" show-overflow-tooltip />

        <el-table-column :formatter="formatRelativeTime" prop="createTime" label="请求时间" min-width="180" show-overflow-tooltip />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          :current-page="queryParams.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.size"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog 
      title="接口审计详情" 
      :visible.sync="dialogVisible"
      width="900px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
    >
      <div v-if="dialogVisible" class="audit-detail">
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item :label="detailData.protocol">
              <div class="copyable-content">
                {{ detailData.uri }}
                <el-button type="text" icon="el-icon-document-copy" @click="copyContent(detailData.uri)" />
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="请求方法">
              <el-tag :type="getMethodTagType(detailData.method)">{{ detailData.method }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="响应状态">
              <el-tag :type="getStatusTagType(detailData.statusCode)">{{ detailData.statusCode }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="主机地址">
              <div class="copyable-content">
                {{ detailData.host }}
                <el-button type="text" icon="el-icon-document-copy" @click="copyContent(detailData.host)" />
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="链路ID" v-if="detailData.traceId">
              <div class="copyable-content">
                {{ detailData.traceId }}
                <el-button type="text" icon="el-icon-document-copy" @click="copyContent(detailData.traceId)" />
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="Token" v-if="detailData.token">
              <div class="copyable-content">
                {{ detailData.token }}
                <el-button type="text" icon="el-icon-document-copy" @click="copyContent(detailData.token)" />
              </div>
            </el-descriptions-item>
            <el-descriptions-item v-if="detailData.userName" label="用户名">{{ detailData.userName }}</el-descriptions-item>
            <el-descriptions-item label="耗时(ms)">{{ detailData.duration }}</el-descriptions-item>
            <el-descriptions-item v-if="detailData.nickName" label="昵称">{{ detailData.nickName }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="form-section">
          <div class="section-title">
            <span>请求信息</span>
            <el-button type="text" class="copy-curl-btn" @click="copyCurl">
              <i class="el-icon-document-copy" />
              复制为CURL
            </el-button>
          </div>
          <el-tabs type="border-card" class="custom-tabs">
            <el-tab-pane lazy label="请求头">
              <el-button type="text" class="copy-btn" @click="copyContent(JSON.stringify(detailData.headers, null, 2))">
                <i class="el-icon-document-copy" />
                复制
              </el-button>
              <pre v-if="detailData.headers" class="json-viewer" v-html="formatJsonMemo(detailData.headers)"></pre>
              <div v-else class="empty-data">暂无数据</div>
            </el-tab-pane>
            <el-tab-pane lazy label="请求参数" v-if="Object.keys(detailData.params).length">
              <el-button type="text" class="copy-btn" @click="copyContent(JSON.stringify(detailData.params, null, 2))">
                <i class="el-icon-document-copy" />
                复制
              </el-button>
              <pre v-if="detailData.params" class="json-viewer" v-html="formatJsonMemo(detailData.params)"></pre>
              <div v-else class="empty-data">暂无数据</div>
            </el-tab-pane>
            <el-tab-pane lazy label="请求体" v-if="detailData.requestBody">
              <el-button type="text" class="copy-btn" @click="copyContent(detailData.requestBody)">
                <i class="el-icon-document-copy" />
                复制
              </el-button>
              <pre v-if="detailData.requestBody" class="json-viewer" v-html="formatJsonMemo(detailData.requestBody)"></pre>
              <div v-else class="empty-data">暂无数据</div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div class="form-section">
          <div class="section-title">
            <span>响应信息</span>
          </div>
          <div class="code-block">
            <el-button type="text" class="copy-btn" @click="copyContent(detailData.responseBody)">
              <i class="el-icon-document-copy" />
              复制
            </el-button>
            <pre v-if="detailData.responseBody" class="json-viewer" v-html="formatJsonMemo(detailData.responseBody)"></pre>
            <div v-else class="empty-data">暂无数据</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { getAuditList } from '@system/api/sys/audit'
import hljs from 'highlight.js/lib/core'
import json from 'highlight.js/lib/languages/json'
import 'highlight.js/styles/github.css'
import {getLogList} from '@system/api/sys/gateway'

// 注册json语言
hljs.registerLanguage('json', json)

// 创建一个简单的缓存对象
const cache = new Map()

export default {
  name: 'ApiAudit',
  props: {
    routeId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      auditList: [],
      total: 3,
      queryParams: {
        current: 1,
        size: 10,
        routeId: null,
        ranges:{}
      },
      dialogVisible: false,
      detailData: {}
    }
  },
  watch: {
    routeId: {
      handler(val) {
        this.queryParams.routeId = val
        this.getList()
      },
      immediate: true
    }
  },
  created() {
    // 初始化时不需要调用getList，由watch处理
  },
  beforeDestroy() {
    // 清空缓存
    cache.clear()
  },
  methods: {
    formatRelativeTime(row, column, time, index) {
      if (!time) return '';
      const date = new Date(time);
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      
      // 转换为秒
      const seconds = Math.floor(diff / 1000);
      
      // 小于1分钟
      if (seconds < 60) {
        return `${seconds}秒前`;
      }
      
      // 小于1小时
      const minutes = Math.floor(seconds / 60);
      if (minutes < 60) {
        return `${minutes}分钟前`;
      }
      
      // 小于1天
      const hours = Math.floor(minutes / 60);
      if (hours < 24) {
        return `${hours}小时前`;
      }
      
      // 小于30天
      const days = Math.floor(hours / 24);
      if (days < 30) {
        return `${days}天前`;
      }
      
      // 超过30天
      return '30天前';
    },
    async getList() {
      this.loading = true
      getLogList(this.queryParams).then(({total, records})=> {
        this.auditList = records
        this.total = total
        this.loading = false
      }).catch(e=>{
        this.loading = false
      })
    },
    handleViewDetail(row) {
      // 清空之前的缓存
      cache.clear()
      this.detailData = { ...row }
      this.dialogVisible = true
    },
    handleSizeChange(val) {
      this.queryParams.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.current = val
      this.getList()
    },
    refreshList() {
      this.getList()
    },
    handleSearch() {
      this.queryParams.current = 1
      this.getList()
    },
    getMethodTagType(method) {
      const types = {
        GET: 'success',
        POST: 'primary',
        PUT: 'warning',
        DELETE: 'danger'
      }
      return types[method] || 'info'
    },
    getStatusTagType(status) {
      if (status >= 200 && status < 300) return 'success'
      if (status >= 400 && status < 500) return 'warning'
      if (status >= 500) return 'danger'
      return 'info'
    },
    // 带缓存的 JSON 格式化方法
    formatJsonMemo(value) {
      if (!value) return ''
      
      try {
        // 生成缓存key
        const cacheKey = typeof value === 'object' ? JSON.stringify(value) : value
        
        // 检查缓存
        if (cache.has(cacheKey)) {
          return cache.get(cacheKey)
        }

        // 处理对象类型
        let jsonString = typeof value === 'object' ? JSON.stringify(value, null, 2) : value

        // 尝试解析字符串为JSON并格式化
        try {
          const parsed = JSON.parse(jsonString)
          jsonString = JSON.stringify(parsed, null, 2)
        } catch (e) {
          // 如果解析失败，保持原样
        }

        // 高亮处理
        const highlighted = hljs.highlight(jsonString, { language: 'json' }).value

        // 存入缓存
        cache.set(cacheKey, highlighted)
        
        return highlighted
      } catch (error) {
        console.warn('JSON格式化失败:', error)
        return String(value)
      }
    },
    // 复制内容到剪贴板
    copyContent(content) {
      if (!content) return
      const textarea = document.createElement('textarea')
      textarea.value = content
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('复制成功')
    },

    // 生成并复制curl命令
    copyCurl() {
      const { method, uri, headers, params, requestBody } = this.detailData
      let url = uri
      
      // 添加查询参数
      if (params && Object.keys(params).length > 0) {
        const query = new URLSearchParams(params).toString()
        url = `${url}?${query}`
      }

      // 构建curl命令
      let curl = `curl --location --request ${method} '${url}'`

      // 添加请求头
      if (headers) {
        Object.entries(headers).forEach(([key, value]) => {
          curl += ` \\\n--header '${key}: ${value}'`
        })
      }

      // 添加请求体
      if (requestBody) {
        curl += ` \\\n--data-raw '${requestBody}'`
      }

      this.copyContent(curl)
    }
  }
}
</script>

<style lang="scss" scoped>
.audit-container {
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 16px;
    border-bottom: 1px solid #EBEEF5;
    flex-shrink: 0;

    .header-title {
      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .table-toolbar {
    margin-bottom: 24px;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .filter-container {
      display: flex;
      align-items: center;
      gap: 16px;
        
        .search-input {
          width: 200px;
          ::v-deep {
            .el-input__inner {
              height: 42px;
              line-height: 42px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 42px;
              border-top-left-radius: 10px;
              border-bottom-left-radius: 10px;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 14px;
              .el-icon-search {
                font-size: 18px;
                line-height: 42px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 10px;
              border-bottom-right-radius: 10px;

              .el-button {
                margin: 0;
                height: 40px;
                border: none;
                padding: 0 20px;
                border-radius: 0 10px 10px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
        }
      }

      .el-select {
        width: 160px;
        ::v-deep {
          .el-input__inner {
            height: 42px;
            line-height: 42px;
            border-radius: 10px;
            border: 1px solid #e0e5ee;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0d0e9;
              background: #f5f7fa;
            }

            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
          }
        }
      }
    }

    .button-group {
      display: flex;
      gap: 12px;
      
      .el-button {
        padding: 9px 18px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        border-radius: 10px;
        background-color: #409EFF;
        border-color: #409EFF;
        overflow: hidden;
        z-index: 1;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
          z-index: -1;
        }

        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }

        i {
          margin-right: 6px;
          font-size: 16px;
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;
    height: 75vh;
    
    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;
      
      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;
        
        .btn-prev, 
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;
          
          &:not(.disabled):hover {
            border-color: #409EFF;
          }
          
          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }
        
        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }
        
        .el-pagination__jump {
          margin-left: 16px;
        }
        
        .el-select .el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }
        
        .el-pagination__editor.el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }
    }
  }
}

.audit-detail {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .copy-curl-btn {
        font-size: 13px;
        color: #409EFF;
        padding: 4px 12px;
        height: auto;
        margin-right: 8px;
        border: 1px solid #e1e8f5;
        border-radius: 4px;
        transition: all 0.3s ease;

        i {
          margin-right: 4px;
        }

        &:hover {
          color: #66b1ff;
          background: #ecf5ff;
          border-color: #b3d8ff;
        }
      }
    }

    .custom-tabs {
      ::v-deep {
        .el-tabs__header {
          margin: 0;
          border-bottom: none;
          padding: 4px;
          background: #f7f9fc;

          .el-tabs__nav {
            border: none;
          }

          .el-tabs__item {
            height: 36px;
            line-height: 36px;
            font-size: 13px;
            color: #606266;
            border: none;
            margin: 0 4px;
            padding: 0 16px;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
              color: #409EFF;
            }

            &.is-active {
              color: #409EFF;
              background: #fff;
              font-weight: 500;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
            }
          }
        }

        .el-tabs__content {
          padding: 20px;
          background: #fff;
          position: relative;

          .copy-btn {
            position: absolute;
            top: 12px;
            right: 12px;
            font-size: 13px;
            color: #409EFF;
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #e1e8f5;
            border-radius: 4px;
            z-index: 1;
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);

            i {
              margin-right: 4px;
            }

            &:hover {
              color: #66b1ff;
              background: #ecf5ff;
              border-color: #b3d8ff;
            }
          }

          &:hover .copy-btn {
            opacity: 1;
          }

          pre.json-viewer {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #1a1f36;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-word;

            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            
            &::-webkit-scrollbar-thumb {
              border-radius: 3px;
              background: rgba(144, 147, 153, 0.3);
              
              &:hover {
                background: rgba(144, 147, 153, 0.5);
              }
            }
            
            &::-webkit-scrollbar-track {
              background: transparent;
            }

            ::v-deep {
              .hljs {
                background: transparent;
                padding: 0;
              }
              .hljs-attr { color: #e3116c; }
              .hljs-string { color: #0b7fc7; }
              .hljs-number { color: #2a9292; }
              .hljs-literal { color: #2a9292; }
              .hljs-punctuation { color: #393a34; }
            }
          }

          .empty-data {
            text-align: center;
            color: #909399;
            padding: 24px;
            font-style: italic;
          }
        }
      }
    }

    .code-block {
      position: relative;
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .copy-btn {
        position: absolute;
        top: 12px;
        right: 12px;
        font-size: 13px;
        color: #409EFF;
        padding: 4px 12px;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #e1e8f5;
        border-radius: 4px;
        z-index: 1;
        opacity: 0;
        transition: all 0.3s ease;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);

        i {
          margin-right: 4px;
        }

        &:hover {
          color: #66b1ff;
          background: #ecf5ff;
          border-color: #b3d8ff;
        }
      }

      &:hover .copy-btn {
        opacity: 1;
      }

      pre {
        margin-top: 0;
      }
    }
  }
}

.copyable-content {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-button {
    padding: 2px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s;

    &:hover {
      color: #66b1ff;
    }
  }

  &:hover .el-button {
    opacity: 1;
  }
}

::v-deep {
  .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
    margin-top: 8vh !important;
    max-height: 84vh;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
      padding: 24px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
    }

    .el-dialog__body {
      padding: 30px 24px;
      overflow-y: auto;
      background: #f8f9fb;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        border-radius: 3px;
        background: rgba(144, 147, 153, 0.3);
        
        &:hover {
          background: rgba(144, 147, 153, 0.5);
        }
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .audit-detail {
        .form-section {
          .content-body {
            padding: 16px;
            max-height: 300px;
            overflow: auto;
            background: #fcfcfd;

            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            
            &::-webkit-scrollbar-thumb {
              border-radius: 3px;
              background: rgba(144, 147, 153, 0.3);
              
              &:hover {
                background: rgba(144, 147, 153, 0.5);
              }
            }
            
            &::-webkit-scrollbar-track {
              background: transparent;
            }
          }

          pre {
            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }
            
            &::-webkit-scrollbar-thumb {
              border-radius: 3px;
              background: rgba(144, 147, 153, 0.3);
              
              &:hover {
                background: rgba(144, 147, 153, 0.5);
              }
            }
            
            &::-webkit-scrollbar-track {
              background: transparent;
            }
          }
        }
      }
    }
  }

  .el-descriptions {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    
    .el-descriptions-item__label {
      background-color: #f7f9fc;
      color: #606266;
      font-weight: 500;
    }
    
    .el-descriptions-item__content {
      color: #1a1f36;
    }
  }

  .el-tabs--border-card {
    background: #fff;
    border: none;
    box-shadow: none;
    
    > .el-tabs__header {
      background-color: #f7f9fc;
      border: none;
      margin: 0;
      
      .el-tabs__item {
        border: none;
        
        &.is-active {
          background-color: #fff;
          color: #409EFF;
        }
      }
    }
    
    > .el-tabs__content {
      padding: 16px;
    }
  }
}
</style> 