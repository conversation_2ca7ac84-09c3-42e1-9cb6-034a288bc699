<template>
  <div class="registration-container">
    <div class="page-header">
      <div class="header-title">
        <h2>用户注册审核</h2>
      </div>
      
      <div class="header-tools">
        <div class="unified-search">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入用户名/手机号搜索"
            prefix-icon="el-icon-search"
            clearable
            class="search-input"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          </el-input>
          <el-select 
            v-model="queryParams.status" 
            placeholder="审核状态" 
            clearable 
            class="status-select"
            @change="handleSearch"
          >
            <el-option label="待审核" value="PENDING">
              <i class="el-icon-time" style="color: #E6A23C" />
              <span style="margin-left: 8px">待审核</span>
            </el-option>
            <el-option label="已通过" value="PASSED">
              <i class="el-icon-check" style="color: #67C23A" />
              <span style="margin-left: 8px">已通过</span>
            </el-option>
            <el-option label="已拒绝" value="FAILED">
              <i class="el-icon-close" style="color: #F56C6C" />
              <span style="margin-left: 8px">已拒绝</span>
            </el-option>
          </el-select>
        </div>

        <el-button type="primary" icon="el-icon-refresh" @click="refreshList">
          刷新列表
        </el-button>
      </div>
    </div>

    <div class="table-wrapper">
      <el-table 
        v-loading="loading" 
        :data="registrationList" 
        border 
        stripe
        fit
        style="width: 100%"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
         <el-table-column type="selection" :width=rpx(50) align="center" />
        <el-table-column prop="username" label="用户名" min-width="120">
          <template slot-scope="scope">
            <div class="user-cell">
              <i class="el-icon-user" />
              <span>{{ scope.row.username }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="姓名" min-width="80" />
        <el-table-column prop="verifyType" label="注册方式" min-width="70"
          :formatter="(row) => getVerifyType(row.verifyType)" />

        <el-table-column prop="phone" label="手机号" min-width="110">
          <template slot-scope="scope">
            <div class="phone-cell">
              <i class="el-icon-mobile-phone" />
              <span>{{ scope.row.phone }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="deptPath" label="部门" min-width="200">
          <template slot-scope="scope">
            <span>{{ getDeptPath(scope.row.deptId) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="email" label="邮箱" min-width="180">
          <template slot-scope="scope">
            <div class="email-cell">
              <i class="iconfont ali-icon-youxiang" />
              <span>{{ scope.row.email }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="picUrl" label="头像" min-width="70" align="center">
          <template slot-scope="scope">
            <el-avatar :size="40"
              :src="scope.row?.picUrl?.length > 1 ? url + scope.row.picUrl : require('./default-avatar.svg')"
              icon="el-icon-user" />
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="申请时间" min-width="130" />

        <el-table-column prop="updateTime" label="审核时间" min-width="130">
          <template slot-scope="scope">
            <span v-if="scope.row.status !== 'PENDING'">
              {{ scope.row.updateTime }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" effect="dark" size="medium">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="80" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === 'PENDING'" size="mini" type="text" icon="el-icon-edit"
              @click="handleViewDetail(scope.row)">
              审核
            </el-button>
            <el-button v-else size="mini" type="text" icon="el-icon-view" @click="handleViewDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          :current-page="queryParams.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.size"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog :title="detailForm.status === 'PENDING' ? '注册申请审核' : '注册申请详情'" :visible.sync="detailDialogVisible"
      width="800px" :close-on-click-modal="false" append-to-body destroy-on-close>
      <el-form
        ref="detailForm"
        :model="detailForm"
        label-width="100px"
        class="user-form"
      >
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名">
                <el-input v-model="detailForm.username" disabled placeholder="用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="姓名">
                <el-input v-model="detailForm.name" disabled placeholder="姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号">
                <el-input v-model="detailForm.phone" disabled placeholder="手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱">
                <el-input v-model="detailForm.email" disabled placeholder="邮箱" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="头像">
                <el-avatar :size="100"
                  :src="detailForm?.picUrl?.length > 1 ? url + detailForm.picUrl : require('./default-avatar.svg')"
                  icon="el-icon-user" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请时间">
                <el-input v-model="detailForm.createTime" disabled placeholder="申请时间" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div v-if="detailForm.status !== 'PENDING'" class="form-section">
          <div class="section-title">审核信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="审核状态">
                <el-tag :type="getStatusType(detailForm.status)" effect="dark" size="medium">
                  {{ getStatusText(detailForm.status) }}
                </el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核时间">
                <el-input v-model="detailForm.updateTime" disabled placeholder="审核时间" />
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.status === 'FAILED'">
              <el-form-item label="拒绝理由">
                <el-input v-model="detailForm.failedReason" type="textarea" :rows="3" disabled />
              </el-form-item>
            </el-col>
            
            <el-col :span="12" v-if="detailForm.status === 'PASSED'">
              <el-form-item label="主部门">
                <el-input :value="getDeptPath(detailForm.deptId)" disabled />
              </el-form-item>
            </el-col>
            
            <el-col :span="12" v-if="detailForm.status === 'PASSED'">
              <el-form-item label="数据权限">
                <el-input v-model="detailForm.scopeText" disabled />
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.status === 'PASSED' && detailForm.subDeptIds && detailForm.subDeptIds.length">
              <el-form-item label="副部门">
                <div class="tag-container">
                  <el-tag v-for="deptId in detailForm.subDeptIds" :key="deptId" size="medium" type="info" effect="plain"
                    class="dept-tag">
                    {{ getDeptPath(deptId) }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.status === 'PASSED'">
              <el-form-item label="已分配角色">
                <div class="tag-container">
                  <el-tag v-for="roleId in detailForm.assignRoles" :key="roleId" size="medium" type="success"
                    effect="plain" class="role-tag">
                    {{ getRoleName(roleId) }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div v-if="detailForm.status === 'PENDING'" class="form-section">
          <div class="section-title">审核操作</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="审核结果" required>
                <el-radio-group v-model="detailForm.auditStatus" class="audit-radio-group">
                  <el-radio label="PASSED">
                    <i class="el-icon-check" style="color: #67C23A"></i>
                    通过
                  </el-radio>
                  <el-radio label="FAILED">
                    <i class="el-icon-close" style="color: #F56C6C"></i>
                    拒绝
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.auditStatus === 'PASSED'">
              <el-form-item label="主部门" required>
                <userDeptCascader v-model="detailForm.deptId"
                  placeholder="请选择主部门" 
                  :props="{
                    checkStrictly: true,
                    value: 'id',
                    label: 'name',
                    children: 'children',
                    emitPath: false,
                    expandTrigger: 'hover'
                  }" 
                  @change="changeDept">
                </userDeptCascader>
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.auditStatus === 'PASSED' && detailForm.scope === 'CUSTOM'">
              <el-form-item label="副部门">
                <userDeptCascader v-if="refreshDeptIds"
                  v-model="detailForm.subDeptIds" 
                  placeholder="请选择副部门(可选)" 
                  :props="{
                    checkStrictly: true,
                    value: 'id',
                    label: 'name',
                    children: 'children',
                    multiple: true,
                    emitPath: false,
                    expandTrigger: 'hover'
                  }" 
                  @disabledMethod="(m) => m.id == detailForm.deptId">
                </userDeptCascader>
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.auditStatus === 'PASSED'">
              <el-form-item label="数据权限" required>
                <el-select v-model="detailForm.scope" placeholder="请选择数据权限">
                  <el-option v-for="item in scopes" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.auditStatus === 'PASSED'">
              <el-form-item label="分配角色" required>
                <el-select v-model="detailForm.roleIds" multiple placeholder="请选择角色" class="role-select">
                  <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id">
                    <i class="el-icon-user" />
                    <span style="margin-left: 8px">{{ item.name }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="24" v-if="detailForm.auditStatus === 'FAILED'">
              <el-form-item label="拒绝理由" required>
                <el-input v-model="detailForm.failedReason" type="textarea" :rows="3" placeholder="请输入拒绝理由"
                  maxlength="200" show-word-limit />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">
          {{ detailForm.status === 'PENDING' ? '取 消' : '关 闭' }}
        </el-button>
        <el-button v-if="detailForm.status === 'PENDING'" type="primary" @click="submitDetailAudit" :loading="submitLoading">
          提交审核
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRegistrationList, getRoleList, auditRegistration } from '@system/api/sys/registration'
import { getDeptTree } from '@system/api/sys/organization'

export default {
  name: 'RegistrationAudit',
  data() {
    return {
      url: process.env.VUE_APP_FILE_URL,
      loading: false,
      submitLoading: false,
      refreshDeptIds: true,
      registrationList: [],
      roleList: [],
      total: 0,
      queryParams: {
        keyword: '',
        status: '',
        current: 1,
        size: 10
      },
      auditDialogVisible: false,
      auditForm: {
        id: null,
        status: '',
        roleIds: []
      },
      deptTree: [],
      deptPathMap: new Map(),
      detailDialogVisible: false,
      detailForm: {
        id: null,
        username: '',
        name: '',
        phone: '',
        email: '',
        deptId: null,        // 主部门ID
        subDeptIds: [],      // 副部门ID列表
        scope: '',
        scopeText: '',
        picUrl: '',
        createTime: '',
        updateTime: '',
        status: '',
        auditStatus: 'PASSED',
        roleIds: [],
        failedReason: '',
        assignRoles: []
      },
      deptTreeOptions: [],
      scopes: [
        { value: "ALL", label: "全部数据", disabled: false },
        { value: "DEPT", label: "部门数据", disabled: false },
        { value: "DEPTS", label: "部门及以下", disabled: false },
        { value: "SELF", label: "仅本人", disabled: false },
        { value: "CUSTOM", label: "自定义", disabled: false },
      ],
      selectedRegistrations: []
    }
  },
  created() {
    this.getList()
    this.getRoles()
    this.getDeptTreeData()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        // 确保部门树数据已加载
        if (!this.deptTree.length) {
          await this.getDeptTreeData()
        }

        // 构建查询参数，过滤掉空值
        const params = {}
        Object.keys(this.queryParams).forEach(key => {
          if (this.queryParams[key] !== '' &&
            this.queryParams[key] !== null &&
            this.queryParams[key] !== undefined) {
            params[key] = this.queryParams[key]
          }
        })

        const data = await getRegistrationList(params)
        this.registrationList = data?.records || []
        this.total = data.total || 0
      } catch (error) {
        console.error('获取注册列表失败:', error)
        this.$message.error('获取注册列表失败')
      }
      this.loading = false
    },
    async getRoles() {
      try {
        // 获取所有角色
        const data = await getRoleList({
          current: 1,
          size: -1
        })
        this.roleList = data?.records || []
      } catch (error) {
        console.error('获取角色列表失败:', error)
        this.$message.error('获取角色列表失败')
      }
    },
    handleAudit(row, status) {
      this.auditForm = {
        id: row.id,
        status,
        roleIds: []
      }
      this.auditDialogVisible = true
    },
    async submitAudit() {
      if (this.auditForm.status === 'PASSED' && this.auditForm.roleIds.length === 0) {
        this.$message.warning('请选择至少一个角色')
        return
      }

      try {
        await auditRegistration(this.auditForm)
        this.$message.success('审核成功')
        this.auditDialogVisible = false
        this.getList()
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error(error.message || '审核失败')
      }
    },
    getStatusType(status) {
      const statusMap = {
        PENDING: 'warning',
        PASSED: 'success',
        FAILED: 'danger'
      }
      return statusMap[status]
    },
    getStatusText(status) {
      const statusMap = {
        PENDING: '待审核',
        PASSED: '已通过',
        FAILED: '已拒绝'
      }
      return statusMap[status]
    },
    handleSizeChange(val) {
      this.queryParams.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.current = val
      this.getList()
    },
    refreshList() {
      this.getList()
    },
    handleSearch() {
      this.queryParams.current = 1
      this.getList()
    },
    getRoleName(roleId) {
      const role = this.roleList.find(item => item.id === roleId)
      return role ? role.name : ''
    },
    async getDeptTreeData() {
      try {
        const data = await getDeptTree()
        this.deptTree = data || []
        this.buildDeptPathMap(this.deptTree)
        this.deptTreeOptions = this.buildDeptTreeOptions(this.deptTree)
      } catch (error) {
        console.error('获取部门树失败:', error)
        this.$message.error('获取部门树失败')
      }
    },
    buildDeptPathMap(deptList, parentPath = '') {
      deptList.forEach(dept => {
        const currentPath = parentPath ? `${parentPath}/${dept.name}` : dept.name
        this.deptPathMap.set(dept.id, currentPath)

        if (dept.children && dept.children.length) {
          this.buildDeptPathMap(dept.children, currentPath)
        }
      })
    },
    getDeptPath(deptId) {
      if (!deptId) return '-'
      return this.deptPathMap.get(deptId) || '-'
    },
    async handleViewDetail(row) {
      // 确保部门树数据已加载
      if (!this.deptTree.length) {
        await this.getDeptTreeData()
      }

      const scopeObj = this.scopes.find(f => f.value === row.scope) || { label: '' }

      // 处理副部门数据 - 排除主部门ID，避免重复
      const subDeptIds = Array.isArray(row.deptIds) 
        ? row.deptIds.filter(id => id !== row.deptId) 
        : [];

      this.detailForm = {
        id: row.id,
        username: row.username,
        name: row.name,
        phone: row.phone,
        email: row.email,
        deptId: row.deptId,
        subDeptIds: subDeptIds,
        scope: row.scope,
        scopeText: scopeObj.label,
        picUrl: row.picUrl,
        createTime: row.createTime,
        updateTime: row.updateTime,
        status: row.status,
        failedReason: row.failedReason || '',
        assignRoles: row.assignRoles || [],
        auditStatus: 'PASSED',
        roleIds: []
      }
      this.detailDialogVisible = true
    },
    async submitDetailAudit() {
      if (this.detailForm.auditStatus === 'PASSED') {
        if (!this.detailForm.deptId) {
          this.$message.warning('请选择主部门')
          return
        }
        if (this.detailForm.roleIds.length === 0) {
          this.$message.warning('请选择至少一个角色')
          return
        }
        if (!this.detailForm.scope) {
          this.$message.warning('请选择数据权限')
          return
        }
      }

      if (this.detailForm.auditStatus === 'FAILED' && !this.detailForm.failedReason.trim()) {
        this.$message.warning('请输入拒绝理由')
        return
      }

      // 合并主部门和副部门为权限部门列表
      // deptIds 用于后端处理权限，包含主部门和所有副部门
      const deptIds = [this.detailForm.deptId, ...this.detailForm.subDeptIds]

      try {
        await auditRegistration({
          id: this.detailForm.id,
          status: this.detailForm.auditStatus,
          deptId: this.detailForm.deptId,    // 主部门ID
          deptIds: deptIds,                  // 权限部门ID列表(主部门+副部门)
          roleIds: this.detailForm.roleIds,
          scope: this.detailForm.scope,
          failedReason: this.detailForm.failedReason
        })

        this.$message.success('审核成功')
        this.detailDialogVisible = false
        this.getList()
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error(error.message || '审核失败')
      }
    },
    buildDeptTreeOptions(deptList) {
      const options = []
      const buildOptions = (deptList, parentId = null) => {
        deptList.forEach(dept => {
          const option = {
            id: dept.id,
            name: dept.name,
            children: []
          }
          if (parentId) {
            const parent = options.find(item => item.id === parentId)
            if (parent) {
              parent.children.push(option)
            }
          }
          if (dept.children && dept.children.length) {
            buildOptions(dept.children, dept.id)
          }
          options.push(option)
        })
      }
      buildOptions(deptList)
      return options
    },
    getDeptIcon(data) {
      if (data.children && data.children.length) {
        return 'el-icon-folder'
      }
      return 'el-icon-document'
    },
    filterDept(value, node) {
      if (!value) return true
      return node.label.toLowerCase().includes(value.toLowerCase())
    },
    handleDeptFocus() {
      // 处理部门选择器聚焦时的逻辑
    },

    changeDept() {
      // 当主部门变更时，从副部门列表中移除主部门ID，避免重复
      this.detailForm.subDeptIds = this.detailForm.subDeptIds.filter(f => f != this.detailForm.deptId)
      this.refreshDeptIds = false
      setTimeout(() => {
        this.refreshDeptIds = true
      }, 0)
    },

    getVerifyType(verifyType) {
      return { 'sms': '手机号', 'email': '邮箱' }[verifyType]
    },

    handleSelectionChange(selectedRows) {
      this.selectedRegistrations = selectedRows.map(row => row.id)
    }
  }
}
</script>

<style lang="scss" scoped>
.registration-container {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  height: calc(100vh - 100px);
  box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    margin: 0;
    padding: 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #eef1f7;
    padding-bottom: 20px;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }

      .header-subtitle {
        font-size: 14px;
        color: #909399;
        margin-left: 12px;
      }
    }
    
    .header-tools {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .unified-search {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .search-input {
          width: 280px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              font-size: 14px;
              border: 1px solid #e0e5ee;
              transition: all 0.3s ease;
              padding-left: 36px;
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              background: #f9fafc;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }

            .el-input__prefix {
              left: 12px;
              .el-icon-search {
                font-size: 16px;
                line-height: 36px;
                color: #8492a6;
              }
            }

            .el-input-group__append {
              background-color: #f9fafc;
              border-left: 1px solid #e0e5ee;
              padding: 0;
              border-top-right-radius: 8px;
              border-bottom-right-radius: 8px;

              .el-button {
                margin: 0;
                height: 34px;
                border: none;
                padding: 0 16px;
                border-radius: 0 8px 8px 0;
                background: transparent;
                font-weight: 500;
                color: #409EFF;
                box-shadow: none;
                position: static;
                overflow: visible;

                &:hover {
                  background-color: #ecf5ff;
                  color: #66b1ff;
                  box-shadow: none;
                  transform: none;
                }
              }
            }
          }
        }

        .status-select {
          width: 120px;
          ::v-deep {
            .el-input__inner {
              height: 36px;
              line-height: 36px;
              border-radius: 8px;
              background: #f9fafc;
              border: 1px solid #e0e5ee;

              &:hover {
                border-color: #c0d0e9;
                background: #f5f7fa;
              }

              &:focus {
                background: #fff;
                border-color: #409EFF;
                box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
              }
            }
          }
        }
      }
      
      .el-button {
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
        position: relative;
        border-radius: 8px;
        background-color: #409EFF;
        border-color: #409EFF;
        overflow: hidden;
        z-index: 1;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        height: 36px;
        font-size: 14px;

        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
          z-index: -1;
        }

        &:hover {
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }

        i {
          margin-right: 6px;
          font-size: 14px;
        }
      }
    }
  }

  .table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;

    ::v-deep .el-table {
      flex: 1;
      display: flex;
      flex-direction: column;
      table-layout: auto;

      .el-table__inner-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .el-table__header-wrapper {
        flex-shrink: 0;
        
        th {
          background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
          font-weight: 600;
          color: #1a1f36;
          height: 44px;
          padding: 8px 0;
          
          .cell {
            font-size: 14px;
            line-height: 28px;
          }
        }
      }

      .el-table__body-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        &::-webkit-scrollbar {
          width: 6px;
          height: 0;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background: rgba(144, 147, 153, 0.3);

          &:hover {
            background: rgba(144, 147, 153, 0.5);
          }
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        td {
          padding: 8px 0;
          
          .cell {
            line-height: 1.5;
          }
        }
      }
      
      tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f7f9fc !important;
        }
        
        &.current-row {
          td {
            background: #ecf5ff !important;
          }
        }
      }
    }

    .pagination-container {
      margin: 0;
      margin-right: 12px;
      padding: 12px 24px;
      background: #fff;
      border-top: 1px solid #eef1f7;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      height: 56px;
      box-sizing: border-box;
      
      ::v-deep .el-pagination {
        padding: 0;
        font-weight: normal;
        white-space: nowrap;
        
        .btn-prev, 
        .btn-next,
        .el-pager li {
          margin: 0 4px;
          min-width: 32px;
          border-radius: 4px;
          border: 1px solid #e0e5ee;
          
          &:not(.disabled):hover {
            border-color: #409EFF;
          }
          
          &.active {
            background-color: #409EFF;
            border-color: #409EFF;
            color: #fff;
          }
        }
        
        .el-pagination__total,
        .el-pagination__sizes {
          margin-right: 16px;
        }
        
        .el-pagination__jump {
          margin-left: 16px;
        }
        
        .el-select .el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
            padding-right: 25px;
          }
        }
        
        .el-pagination__editor.el-input {
          margin: 0 8px;
          
          .el-input__inner {
            border-radius: 4px;
            height: 28px;
            line-height: 28px;
          }
        }
      }
    }
  }
}

.user-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        .el-input,
        .el-select,
        .el-cascader {
          width: 100%;
        }

        .el-input__inner {
          border-radius: 10px;
          height: 38px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-select,
        .el-cascader {
          .el-input {
            width: 100%;
          }
        }
      }
    }
  }
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .dept-tag,
  .role-tag {
    margin: 4px;
    border-radius: 4px;
    padding: 0 12px;
    height: 28px;
    line-height: 26px;
    border: 1px solid #e0e5ee;
    background: #f8f9fb;
    color: #606266;
    transition: all 0.3s;

    &:hover {
      color: #409EFF;
      border-color: #409EFF;
      background: #ecf5ff;
    }
  }
}

::v-deep {
  .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
    margin-top: 8vh !important;
    max-height: 84vh;
    display: flex;
    flex-direction: column;

    .el-dialog__header {
      padding: 24px;
      border-bottom: 1px solid #ebeef5;
      margin: 0;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: #1a1f36;
        position: relative;
        padding-left: 12px;
        
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 18px;
          background: linear-gradient(to bottom, #409EFF, #64B5F6);
          border-radius: 3px;
        }
      }
      
      .el-dialog__headerbtn {
        top: 20px;
        right: 20px;
        
        .el-dialog__close {
          font-size: 18px;
          color: #909399;
          font-weight: bold;
          
          &:hover {
            color: #409EFF;
          }
        }
      }
    }

    .el-dialog__body {
      padding: 30px 24px;
      overflow-y: auto;
      background: #f8f9fb;

      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 3px;
        
        &:hover {
          background: rgba(144, 147, 153, 0.5);
        }
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }

    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #ebeef5;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);
      
      .el-button {
        padding: 9px 20px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

        & + .el-button {
          margin-left: 12px;
        }

        &--default {
          border-color: #dcdfe6;
          background: linear-gradient(to bottom, #fff, #f9fafc);
          
          &:hover {
            border-color: #c0c4cc;
            color: #606266;
            background: #f5f7fa;
          }
        }

        &--primary {
          background: #409EFF;
          border-color: #409EFF;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          
          &:hover {
            transform: translateY(-2px);
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
  }
}

.user-cell, .phone-cell, .email-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  i {
    color: #409EFF;
    font-size: 16px;
  }
  
  span {
    color: #1a1f36;
    font-weight: 500;
  }
}

.el-avatar {
  background: linear-gradient(135deg, #409EFF, #64B5F6);
  color: #fff;
  font-size: 14px;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
  }
}
</style>