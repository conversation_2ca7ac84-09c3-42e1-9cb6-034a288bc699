<template>
    <div>
        <AutoPage @formChange="formChange" @updateRow="updateRow" :options="pageOptions" :form-rule="formRule" :table-data="tableData" :page-size="5" :defaultQuery="{id: '666', orders: {'id': 'desc'}, nulls: {'id': false}}">
            <template #tablestatus="rows">
                {{ rows.row.status + 'design' }}
            </template>
            <template #tablecreateTime="rows">
                {{ rows.row.createTime + 'design' }}
            </template>
        </AutoPage>
    </div>
</template>

<script>
/**
     * 参考form-create官网rule配置
     * type:'editor'    //富文本组件
     * type:'json'      //json
     * type:'uploader'  //上传组件
     * type:'treeSelect' //树形下拉框
     * type:'cascader2'  //elementui级联选择器
     *      props选项中的value,label对应valueProp,labelProp
     * type:'svgIcon'   //svg图标
     * 自定义字段
     * isSearch: true,              //是否为search内容
         isTable: true,               //是否为table内容
        isScope: false,              //是否自定义表格内容
        isSearchValidate: [],        //search列 validate
        isSearchCol: {              //search列 col
            md: { span: 10 }
        },
        isHidden       //默认全部展示到form中, true表示不展示到form

    change事件字段说明
        field 字段名
        value 组件值
        rule rule
        api api
        setFlag 为true时是主动修改
        说明： 当表单组件的值在组件内部发生变化时触发
    updateRow  点击修改按钮触发


    "button": [     //table按钮  options-->table-->button
      {
        "label"   //名称
        "route"   //跳转路由，传参#开头
        "icon"   //图标
        "permi"   //权限  ['device:info:delete']
      }
    ]
*/
    import AutoPage from './AutoPage.vue'

    export default {
        components: {AutoPage},
        data() {

            return {
                pageOptions: {
                    api: '/sys/account',  //必填
                    search: {
                        isShow: true,
                        showReset: true
                    },

                    table: {}
                },
                drawer: {  //drawer配置 默认值
                    direction: 'rtl',
                    size: '30%',
                    withHeader: true,
                    isScope: false, // 自定义drawer内容  默认false使用form-create, true 自定义
                },
                formRule: [
                    {
                    type: "input",
                    field: "username",
                    className: "username-dom",
                    title: "字段1 username",
                    value: "",
                    isSearch: true,              //是否为search内容
                    isTable: true,               //是否为table内容
                    isScope: false,              //是否自定义表格内容
                    isSearchValidate: [],        //search列 validate
                    isSearchCol: {              //search列 col
                        md: { span: 10 }
                    },
                    props: {
                        placeholder: '请输入',
                        disabled: false,
                        readonly: false,
                        clearable: true
                    },
                    validate: [
                        {
                        trigger: 'blur',
                        required: true,
                        message: '不能为空！'
                        }
                    ],
                    col: {
                        md: { span: 18 }
                    }
                    },
                    {
                    type: "select", // 生成组件的名称(就是表单的名称：如input，radio，checkbox，select，slider等)
                    field: "accountType", // 表单组件的字段名称(就是表单的name属性，注：该必须唯一),自定义组件可以不配置
                    className: "accountType-dom", // 设置组件的class属性
                    title: "字段Type", // 组件的名称, 选填
                    value: '', // 表单组件的字段值(就是表单的value值),自定义组件可以不用设置
                    isSearch: true,
                    isTable: true,
                    isScope: false, //自定义表格内容
                    isSearchValidate: [],
                    isSearchCol: {
                        md: { span: 10 }
                    },
                    isSearchProps: {
                        multiple: true
                    },
                    options: [
                        {"value": "0", "label": "accountType0", "disabled": false},
                        {"value": "1", "label": "accountType1", "disabled": false},
                        {"value": "2", "label": "accountType2", "disabled": false},
                    ],
                    props: {
                        multiple: false,
                        placeholder: "请输入",
                        disabled: false,
                        readonly: false,
                        clearable: true // 是否显示清空按钮
                    },
                    validate: [
                        {
                        trigger: "blur",
                        required: true,
                        message: "不能为空！"
                        }
                    ],
                    col: {
                        md: { span: 18 }
                    }
                    },
                    // {
                    // type: "DatePicker", // 生成组件的名称(就是表单的名称：如input，radio，checkbox，select，slider等)
                    // field: "createTime", // 表单组件的字段名称(就是表单的name属性，注：该必须唯一),自定义组件可以不配置
                    // className: "post-code-dom", // 设置组件的class属性
                    // title: "字段时间", // 组件的名称, 选填
                    // value: '', // 表单组件的字段值(就是表单的value值),自定义组件可以不用设置
                    // isSearch: true,
                    // isTable: true,
                    // isSearchValidate: [],
                    // isSearchCol: {
                    //     md: { span: 8 }
                    // },
                    // props: {
                    //     type: 'datetimerange',
                    //     // placeholder: "请输入岗位编码！",
                    //     disabled: false,
                    //     readonly: false,
                    //     clearable: true // 是否显示清空按钮
                    // },
                    // validate: [
                    //     {
                    //     trigger: "blur",
                    //     required: true,
                    //     message: "不能为空！"
                    //     }
                    // ],
                    // // col: {
                    // //     md: { span: 18 }
                    // // }
                    // }
                ]
            }
        },
        methods: {
            updateRow(type, row) {
                console.log('--------------->updateRow', type, row)
            },
            formChange(field, value, rule, api, setFlag) {
                console.log('-------------------->formchange', field, value, rule, api, setFlag)
            },
        }
    }
</script>

<style lang="scss" scoped>

</style>
