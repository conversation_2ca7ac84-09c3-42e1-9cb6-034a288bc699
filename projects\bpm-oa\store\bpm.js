
/**
 * 
 * loginUser必要结构
 *      {
          id: id,
          name: name,
          avatar: img,
          type: 'user'
        }
 * 
 * 
 */


export default {
  namespaced: true, // 开启命名空间
  state: {
    nodeMap: new Map(),
    formItemMap: new Map(),
    isEdit: null,
    loginUser: JSON.parse(localStorage.getItem('bpm-loginUser') || '{}'),
    selectedNode: {},
    selectFormItem: null,
    design: {},
  },
  mutations: {
    selectedNode(state, val) {
      state.selectedNode = val
    },
    loadForm(state, val) {
      state.design = val
    },
    setIsEdit(state, val) {
      state.isEdit = val
    }
  },
  getters: {},
  actions: {},
  modules: {}
}
