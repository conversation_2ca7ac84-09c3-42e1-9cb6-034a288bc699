import request from '@/utils/request'

const api = CONSTANT.SYSTEM

// 获取系统配置列表
export function getConfigList(params) {
  return request({
    url: api + '/sys/config/list',
    method: 'get',
    params
  })
}

// 创建系统配置
export function createConfig(data) {
  return request({
    url: api + '/sys/config',
    method: 'post',
    data
  })
}

// 更新系统配置
export function updateConfig(data) {
  return request({
    url: api + '/sys/config',
    method: 'put',
    data
  })
}

// 删除系统配置
export function deleteConfig(id) {
  return request({
    url: api + `/sys/config/${id}`,
    method: 'delete'
  })
}

// 获取分组名称列表
export function getGroupNames() {
  return request({
    url: api + '/sys/config/groupNames',
    method: 'get',
    params: {
      groupName: 'groupName'
    }
  })
}

// 根据key获取配置
export function getConfig<PERSON>y<PERSON><PERSON>(key) {
  return request({
    url: api + '/sys/config/key',
    method: 'get',
    params: { key }
  })
} 