const path = require('path');
const glob = require('glob')
const { merge } = require("webpack-merge");

// 项目文件夹
const PATH = path.resolve(__dirname, '../projects');
// 全部项目入口 js 文件
const entryFiles = glob.sync(PATH + '/*/main.js').map(path => path.replace(/\\/g, '/'));


const args = process.argv.slice(2)
const params = require('minimist')(args)
console.log('自定义指令:', params)

// 当前项目名
let splitNames = [];
let libs = [];
const projectName = (function () {
  let pn = params.project || params['_'][params['_'].length - 1]
  pn = ['serve', 'build'].includes(pn) ? undefined : pn
  if (pn && pn.indexOf(',') > 0) {
    splitNames = pn.split(',')
    return undefined
  } else {
    if (pn) {
      splitNames = [pn]
    }
  }

  let lib = params.libs
  if (lib && lib.indexOf(',') > 0) {
    libs = lib.split(',')
  } else {
    if (lib) {
      libs = [lib]
    }
  }

  return pn
})()

if (['serve', 'build'].includes(args[0]) && libs.length < 1) {
  try {
    let libConfig = require(`../projects/${projectName}/lib.config.js`)
    console.log(`项目libs配置读取: `, libConfig)
    let ls = libConfig.libs
    if (ls && ls.length > 0) {
      libs = [...ls]
    }
  } catch (err) {
    if (projectName) {
      console.warn(`项目未配置lib.config.js文件`)
    }
  }
}

// 全部项目名
const projectNames = entryFiles.filter(f => {
  let parentDir = path.dirname(f)
  return require('fs').existsSync(`${parentDir}/index.html`)
}).map(filePath => {
  let fileList = filePath.split('/');
  return fileList[fileList.length - 2];
});

//多选项目名
const targetNames = projectNames.filter(f => {
  if (Array.isArray(splitNames) && splitNames.length > 0) {
    return splitNames.includes(f)
  }
  return true
})


if (params.project && targetNames.length < 1) {
  throw '请输入正确的项目名称，项目名称是 projects 下的文件夹名'
}

process.env.npm_config_project_name = projectName

module.exports = {
  name: projectName,
  names: projectNames,
  targetNames: targetNames,
  projectLibs: libs,
  pages: customConfig => {
    let command = ''; // 执行的命令
    if (process.env.NODE_ENV == 'production') {
      command = 'build';
    } else if (process.env.NODE_ENV == 'development') {
      command = 'serve';
    }

    let map = {};
    if (projectName || command === 'build' || (!params.project && params['_'].length > 1)) {
      if (command === 'build') {
        if (!targetNames.includes(projectName)) {
          throw `请输入项目名称，项目名称是 projects 下的文件夹名 npm run build projectName`
        }
      } else {
        if (!params.project) {
          throw `请输入项目名称，项目名称是 projects 下的文件夹名 npm run serve -- --project=projectName`
        }
      }

      map = {
        index: {
          // page的入口
          entry: `${PATH}/${projectName}/main.js`,
          // 模板来源
          template: `${PATH}/${projectName}/index.html`,
          // 在 dist/index.html 的输出
          filename: 'index.html',
          chunks: ['chunk-vendors', 'chunk-libs', 'chunk-common', 'chunk-elementUI', 'index']
        }
      }
    } else {
      /**
       * 本地启动时，未输入项目名，则启动全部
       *
       * 项目访问地址是：http://localhost:端口/项目名
       */
      targetNames.forEach(name => {
        map[name] = {
          // page的入口
          entry: `${PATH}/${name}/main.js`,
          // 模板来源
          template: `${PATH}/${name}/index.html`,
          // 在 dist/index.html 的输出
          filename: name + '.html',
          // 如果不加这行则每个页面都会引入共享的js脚本
          chunks: ['chunk-vendors', 'chunk-libs', 'chunk-common', 'chunk-elementUI', name],
          inject: true,
        };
      });
    }

    for (let key in map) {
      let conf = map[key];
      if (customConfig) {
        conf = merge(conf, customConfig);
      }

      if (process.env.NODE_ENV === 'production') {
        conf = merge(conf, {
          minify: {
            removeComments: true, // 删除 html 中的注释代码
            // collapseWhitespace: true, // 删除 html 中的空白符
            // removeAttributeQuotes: true // 删除 html 元素中属性的引号
          },
          chunksSortMode: 'manual'// 按 manual 的顺序引入
        });
      }
      map[key] = conf;
    }

    return map;
  },
}
