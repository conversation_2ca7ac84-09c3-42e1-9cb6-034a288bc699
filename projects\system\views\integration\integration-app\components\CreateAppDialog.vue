
<template>
  <el-dialog
    :visible.sync="visible"
    title="创建应用"
    width="95vw"
    top="2.5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="create-app-dialog"
    @close="handleClose"
  >
    <div class="create-app-container">
      <!-- 左侧：应用信息填写 -->
      <div class="app-info-section">
        <div class="section-header">
          <h2>创建空白应用</h2>
          <p>选择应用类型</p>
        </div>

        <!-- 应用类型选择 -->
        <div class="app-type-selection">
          <div class="type-cards">
            <div
              class="type-card"
              :class="{ active: appForm.type === 'PROXY' }"
              @click="selectAppType('PROXY')"
            >
              <div class="type-icon proxy-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                  <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="type-info">
                <h4>代理应用</h4>
                <p>统一的第三方系统认证代理服务</p>
              </div>
            </div>

            <div
              class="type-card"
              :class="{ active: appForm.type === 'FLOW' }"
              @click="selectAppType('FLOW')"
            >
              <div class="type-icon flow-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M21 16V8A2 2 0 0 0 19 6H5A2 2 0 0 0 3 8V16A2 2 0 0 0 5 18H19A2 2 0 0 0 21 16Z" stroke="currentColor" stroke-width="2"/>
                  <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="type-info">
                <h4>流程应用</h4>
                <p>面向系统集成的数据流处理应用</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 应用基本信息表单 -->
        <div class="app-form-section" v-if="appForm.type">
          <div class="form-header">
            <h3>应用信息</h3>
            <p>填写应用基本信息</p>
          </div>

          <el-form :model="appForm" :rules="formRules" ref="appForm" class="app-form">
            <div class="form-row">
              <el-form-item label="应用名称" prop="name" required class="form-item-half">
                <el-input
                  v-model="appForm.name"
                  placeholder="请输入应用名称"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="应用编码" prop="code" required class="form-item-half">
                <el-input
                  v-model="appForm.code"
                  placeholder="请输入应用编码"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>
            </div>

            <el-form-item label="应用描述" prop="description">
              <el-input
                v-model="appForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入应用描述（可选）"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <div class="form-row">
              <el-form-item label="应用分组" prop="groupName" class="form-item-half">
                <el-select
                  v-model="appForm.groupName"
                  placeholder="请选择或输入应用分组（可选）"
                  filterable
                  allow-create
                  default-first-option
                  clearable
                  class="group-select"
                >
                  <el-option
                    v-for="group in appGroups"
                    :key="group.name"
                    :label="`${group.name} (${group.count})`"
                    :value="group.name"
                  />
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="应用标签" prop="tags">
              <div class="tags-container">
                <el-tag
                  v-for="tag in appForm.tags"
                  :key="tag"
                  closable
                  @close="removeTag(tag)"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-if="inputVisible"
                  v-model="inputValue"
                  ref="saveTagInput"
                  size="small"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                  class="tag-input"
                />
                <el-button v-else size="small" class="add-tag-btn" @click="showInput">
                  <i class="el-icon-plus"></i> 添加标签
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>



      </div>

      <!-- 右侧：预览区域 -->
      <div class="preview-section">
        <div class="preview-header">
          <h3>{{ getPreviewTitle() }}</h3>
          <p>{{ getPreviewDescription() }}</p>
        </div>

        <div class="preview-content">
          <!-- 代理应用预览 -->
          <div v-if="appForm.type === 'PROXY'" class="proxy-preview">
            <div class="proxy-architecture">
              <div class="architecture-title">
                <h4>集成系统认证代理</h4>
                <p>统一认证入口，透明代理访问</p>
              </div>

              <div class="proxy-flow">
                <!-- 用户 -->
                <div class="flow-item user-item">
                  <div class="flow-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                      <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="#1890ff" stroke-width="2"/>
                      <circle cx="12" cy="7" r="4" stroke="#1890ff" stroke-width="2"/>
                    </svg>
                  </div>
                  <span>用户</span>
                </div>

                <!-- 箭头 -->
                <div class="flow-arrow">
                  <svg width="40" height="20" viewBox="0 0 40 20" fill="none">
                    <path d="M5 10H35M30 5L35 10L30 15" stroke="#52c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>

                <!-- 集成平台 -->
                <div class="flow-item platform-item">
                  <div class="flow-icon platform-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                      <rect x="3" y="3" width="18" height="18" rx="2" stroke="#52c41a" stroke-width="2"/>
                      <path d="M9 9H15M9 13H15" stroke="#52c41a
                      " stroke-width="2" stroke-linecap="round"/>
                    </svg>
                  </div>
                  <span>集成平台</span>
                  <div class="platform-badge">统一认证</div>
                </div>

                <!-- 箭头 -->
                <div class="flow-arrow">
                  <svg width="40" height="20" viewBox="0 0 40 20" fill="none">
                    <path d="M5 10H35M30 5L35 10L30 15" stroke="#52c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>

                <!-- 第三方系统 -->
                <div class="flow-item third-party-item">
                  <div class="flow-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#722ed1" stroke-width="2" stroke-linejoin="round"/>
                      <path d="M2 17L12 22L22 17" stroke="#722ed1" stroke-width="2" stroke-linejoin="round"/>
                      <path d="M2 12L12 17L22 12" stroke="#722ed1" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <span>第三方系统</span>
                </div>
              </div>

              <!-- 访问方式对比 -->
              <div class="access-comparison">
                <div class="comparison-title">
                  <h4>访问方式对比</h4>
                  <p>简化接口调用，保持原有路径结构</p>
                </div>

                <div class="comparison-items">
                  <div class="comparison-item original">
                    <div class="item-header">
                      <div class="item-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M18 6L6 18M6 6L18 18" stroke="#ff4d4f" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                      </div>
                      <span class="item-label">原始访问</span>
                    </div>
                    <div class="item-content">
                      <code>http://第三方域名/功能接口地址 + 第三方token</code>
                    </div>
                  </div>

                  <div class="comparison-arrow">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M7 17L17 7M17 7H7M17 7V17" stroke="#52c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>

                  <div class="comparison-item proxy">
                    <div class="item-header">
                      <div class="item-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M9 12L11 14L15 10" stroke="#52c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#52c41a" stroke-width="2"/>
                        </svg>
                      </div>
                      <span class="item-label">代理访问</span>
                    </div>
                    <div class="item-content">
                      <code>http://集成平台地址/proxy/集成应用code/功能接口地址</code>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 功能特性 -->
              <div class="proxy-features">
                <div class="feature-item">
                  <div class="feature-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M9 12L11 14L15 10" stroke="#52c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#52c41a" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="feature-text">
                    <h5>统一认证入口</h5>
                    <p>一次认证，访问所有集成系统</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M12 22S8 18 8 13A4 4 0 0 1 16 13C16 18 12 22 12 22Z" stroke="#1890ff" stroke-width="2"/>
                      <circle cx="12" cy="13" r="1" stroke="#1890ff" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="feature-text">
                    <h5>透明代理访问</h5>
                    <p>保持原有接口路径不变</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" stroke="#fa8c16" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="feature-text">
                    <h5>多步骤认证</h5>
                    <p>支持复杂认证流程编排</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 流程应用预览 -->
          <div v-else-if="appForm.type === 'FLOW'" class="flow-preview">
            <div class="flow-mockup">
              <div class="mockup-header">
                <div class="mockup-tabs">
                  <span class="tab active">流程设计</span>
                  <span class="tab">测试运行</span>
                  <span class="tab">日志</span>
                </div>
              </div>
              <div class="mockup-content">
                <div class="workflow-nodes">
                  <div class="node start-node">开始</div>
                  <div class="node-connector"></div>
                  <div class="node process-node">数据处理</div>
                  <div class="node-connector"></div>
                  <div class="node end-node">结束</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 默认状态 -->
          <div v-else class="default-preview">
            <div class="empty-state">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#d9d9d9" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="#d9d9d9" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="#d9d9d9" stroke-width="2" stroke-linejoin="round"/>
              </svg>
              <p>请选择应用类型查看详情</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button
        size="large"
        class="cancel-btn"
        @click="handleClose"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="large"
        class="create-btn"
        :disabled="!canCreate"
        @click="handleCreate"
        :loading="creating"
      >
        <i class="el-icon-plus" v-if="!creating"></i>
        {{ creating ? '创建中...' : '创建应用' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createIntegrationApp, getIntegrationAppCount } from '@system/api/integration/integration-app'

export default {
  name: 'CreateAppDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      creating: false,
      inputVisible: false,
      inputValue: '',
      appGroups: [], // 应用分组列表
      appForm: {
        name: '',
        code: '',
        type: 'PROXY', // 默认设置为代理应用
        description: '',
        groupName: '',
        tags: []
      },
      formRules: {
        name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { min: 2, max: 50, message: '应用名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入应用编码', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '应用编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' },
          { min: 2, max: 30, message: '应用编码长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择应用类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 对话框打开时，确保默认值和样式正确
        this.$nextTick(() => {
          this.initDialog()
          this.loadAppGroups()
        })
      }
    }
  },
  computed: {
    canCreate() {
      return this.appForm.name && this.appForm.code && this.appForm.type
    }
  },
  methods: {
    initDialog() {
      // 初始化对话框，确保样式正确
      // 重置表单但保持默认类型
      const defaultType = 'PROXY'
      this.appForm = {
        name: '',
        code: '',
        type: defaultType,
        description: '',
        groupName: '',
        tags: []
      }
      this.inputVisible = false
      this.inputValue = ''
      this.creating = false
    },

    // 加载应用分组数据
    async loadAppGroups() {
      try {
        const response = await getIntegrationAppCount({
          groups: ['groupName']
        })

        if (Array.isArray(response)) {
          this.appGroups = response.map(item => ({
            name: item.groupName || '未分组',
            count: item.count || 0
          })).filter(group => group.name && group.name !== '未分组' || group.count > 0)
        } else {
          console.warn('Unexpected response format for app groups:', response)
          this.appGroups = []
        }
      } catch (error) {
        console.error('加载应用分组失败:', error)
        this.appGroups = []
      }
    },

    selectAppType(type) {
      this.appForm.type = type
    },

    getPreviewTitle() {
      switch (this.appForm.type) {
        case 'PROXY':
          return '代理应用架构'
        case 'FLOW':
          return '流程应用工作台'
        default:
          return '应用预览'
      }
    },

    getPreviewDescription() {
      switch (this.appForm.type) {
        case 'PROXY':
          return '提供统一的第三方系统认证代理服务，实现用户无感知的接口访问体验'
        case 'FLOW':
          return '基于工作流编排，从零开始构建和持续改进你的应用'
        default:
          return '选择应用类型查看详细信息'
      }
    },

    async handleCreate() {
      if (!this.appForm.type) {
        this.$message.warning('请先选择应用类型')
        return
      }

      this.$refs.appForm.validate(async (valid) => {
        if (valid) {
          this.creating = true

          try {
            // 调用真实的创建接口
            const response = await createIntegrationApp({
              name: this.appForm.name,
              code: this.appForm.code,
              type: this.appForm.type,
              description: this.appForm.description || '',
              groupName: this.appForm.groupName || '',
              tags: this.appForm.tags || []
            })

            this.$message.success('应用创建成功')
            this.$emit('created', {
              ...this.appForm,
              id: response.data?.id // 如果接口返回了ID
            })
            this.handleClose()
          } catch (error) {
            console.error('创建应用失败:', error)
            this.$message.error(error.message || '创建应用失败，请重试')
          } finally {
            this.creating = false
          }
        } else {
          this.$message.warning('请完善必填信息')
        }
      })
    },

    handleClose() {
      this.resetForm()
      this.creating = false
      this.$emit('update:visible', false)
    },

    resetForm() {
      // 重置表单但保持默认类型
      this.appForm = {
        name: '',
        code: '',
        type: 'PROXY', // 保持默认类型
        description: '',
        groupName: '',
        tags: []
      }
      this.inputVisible = false
      this.inputValue = ''
      if (this.$refs.appForm) {
        this.$refs.appForm.resetFields()
      }
    },

    showInput() {
      this.inputVisible = true
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm() {
      const inputValue = this.inputValue
      if (inputValue && !this.appForm.tags.includes(inputValue)) {
        this.appForm.tags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },

    removeTag(tag) {
      this.appForm.tags.splice(this.appForm.tags.indexOf(tag), 1)
    },

    handleImportTemplate() {
      this.$message.info('应用模板功能即将上线')
    },

    handleImportDSL() {
      this.$message.info('DSL导入功能即将上线')
    }
  }
}
</script>

<style lang="scss" scoped>
// 修改蒙版颜色
::v-deep .el-dialog__wrapper {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(4px);
}

::v-deep .create-app-dialog {
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
  margin-bottom: 2.5vh !important;

  .el-dialog {
    border-radius: 16px !important;
    margin: 0 !important;
  }

  .el-dialog__header {
    padding: 24px 24px 0;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }
}

.create-app-container {
  display: flex;
  height: 85vh;
  min-height: 600px;
}

.app-info-section {
  flex: 1;
  padding: 32px;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
  background: #fafbfc;

  .section-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #8c8c8c;
      margin: 0;
    }
  }

  .app-type-selection {
    margin-bottom: 40px;

    .type-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }

    .disabled-types {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .type-card {
      background: white;
      border: 2px solid #e8e8e8;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:hover:not(.disabled) {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        transform: translateY(-2px);
      }

      &.active {
        border-color: #1890ff;
        background: linear-gradient(135deg, #f6f8ff, #e6f7ff);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);

        &::after {
          content: '✓';
          position: absolute;
          top: 12px;
          right: 12px;
          width: 20px;
          height: 20px;
          background: #1890ff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
        }
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #f5f5f5;

        &:hover {
          transform: none;
          box-shadow: none;
          border-color: #e8e8e8;
        }
      }

      .type-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;

        svg {
          width: 24px;
          height: 24px;
          color: white;
        }

        i {
          font-size: 24px;
          color: white;
        }

        &.proxy-icon {
          background: linear-gradient(135deg, #1890ff, #40a9ff);
        }

        &.flow-icon {
          background: linear-gradient(135deg, #13c2c2, #36cfc9);
        }
      }

      .type-info {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin: 0 0 8px 0;
        }

        p {
          font-size: 13px;
          color: #8c8c8c;
          margin: 0;
          line-height: 1.5;
        }
      }
    }
  }

  .app-form-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .form-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 4px 0;
      }

      p {
        font-size: 13px;
        color: #8c8c8c;
        margin: 0;
      }
    }

    .app-form {
      .form-row {
        display: flex;
        gap: 16px;

        .form-item-half {
          flex: 1;
        }

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 0;
        }
      }

      .el-form-item {
        margin-bottom: 20px;
      }

      .el-form-item__label {
        font-weight: 500;
        color: #262626;
        font-size: 14px;
      }

      .el-input, .el-select, .el-textarea {
        .el-input__inner, .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #e8e8e8;
          transition: all 0.2s ease;
          font-size: 14px;

          &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }

      .group-select {
        width: 100%;

        .el-input__inner {
          border-radius: 8px;
          border: 1px solid #e8e8e8;
          transition: all 0.2s ease;
          font-size: 14px;

          &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }

      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;

        .tag-item {
          border-radius: 6px;
          background: #f6f8ff;
          border-color: #d6e4ff;
          color: #1890ff;
        }

        .tag-input {
          width: 120px;
        }

        .add-tag-btn {
          border: 1px dashed #d9d9d9;
          background: transparent;
          color: #8c8c8c;
          border-radius: 6px;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }
    }
  }




}

.preview-section {
  flex: 1;
  padding: 24px;
  background: #fafafa;

  .preview-header {
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 4px 0;
    }

    p {
      font-size: 13px;
      color: #8c8c8c;
      margin: 0;
      line-height: 1.4;
    }
  }

  .preview-content {
    height: calc(100% - 60px);

    @media (max-width: 768px) {
      height: calc(100% - 50px);
    }

    // 代理应用预览样式
    .proxy-preview {
      height: 100%;
      padding: 24px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow-y: auto;

      .proxy-architecture {
        .architecture-title {
          text-align: center;
          margin-bottom: 32px;

          h4 {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin: 0 0 8px 0;
          }

          p {
            font-size: 14px;
            color: #8c8c8c;
            margin: 0;
          }
        }

        .proxy-flow {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 16px;
          margin-bottom: 40px;
          flex-wrap: wrap;

          @media (max-width: 768px) {
            flex-direction: column;
            gap: 12px;
            margin-bottom: 24px;
          }

          .flow-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            position: relative;

            .flow-icon {
              width: 56px;
              height: 56px;
              border-radius: 12px;
              background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
              border: 2px solid #e6f7ff;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
              }
            }

            span {
              font-size: 13px;
              font-weight: 500;
              color: #262626;
            }

            &.platform-item {
              .flow-icon {
                background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
                border-color: #1890ff;
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);

                svg {
                  color: #1890ff;
                }
              }

              .platform-badge {
                position: absolute;
                top: -8px;
                right: -8px;
                background: linear-gradient(135deg, #1890ff, #40a9ff);
                color: white;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 8px;
                font-weight: 500;
                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
              }
            }
          }

          .flow-arrow {
            display: flex;
            align-items: center;
            margin: 0 8px;

            @media (max-width: 768px) {
              transform: rotate(90deg);
              margin: 8px 0;
            }
          }
        }

        .access-comparison {
          margin-bottom: 32px;

          .comparison-title {
            text-align: center;
            margin-bottom: 24px;

            h4 {
              font-size: 16px;
              font-weight: 600;
              color: #262626;
              margin: 0 0 8px 0;
            }

            p {
              font-size: 13px;
              color: #8c8c8c;
              margin: 0;
            }
          }

          .comparison-items {
            display: flex;
            flex-direction: column;
            gap: 16px;
            align-items: center;

            .comparison-item {
              width: 100%;
              max-width: 400px;
              padding: 16px;
              border-radius: 8px;
              border: 2px solid;

              .item-header {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;

                .item-icon {
                  width: 20px;
                  height: 20px;
                  border-radius: 4px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }

                .item-label {
                  font-size: 13px;
                  font-weight: 600;
                }
              }

              .item-content {
                code {
                  display: block;
                  padding: 8px 12px;
                  background: #f8f9fa;
                  border-radius: 4px;
                  font-size: 12px;
                  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                  line-height: 1.4;
                  word-break: break-all;
                  border: 1px solid #e8e8e8;
                }
              }

              &.original {
                border-color: #ffccc7;
                background: #fff2f0;

                .item-header {
                  .item-icon {
                    background: #fff2f0;
                    color: #ff4d4f;
                  }

                  .item-label {
                    color: #ff4d4f;
                  }
                }
              }

              &.proxy {
                border-color: #b7eb8f;
                background: #f6ffed;

                .item-header {
                  .item-icon {
                    background: #f6ffed;
                    color: #52c41a;
                  }

                  .item-label {
                    color: #52c41a;
                  }
                }

                .item-content code {
                  background: #f6ffed;
                  border-color: #b7eb8f;
                }
              }
            }

            .comparison-arrow {
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 8px 0;

              svg {
                color: #52c41a;
              }
            }
          }
        }

        .proxy-features {
          .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 20px;
            padding: 16px;
            background: #fafbfc;
            border-radius: 8px;
            border-left: 3px solid #1890ff;

            .feature-icon {
              flex-shrink: 0;
              width: 32px;
              height: 32px;
              border-radius: 6px;
              background: white;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .feature-text {
              h5 {
                font-size: 14px;
                font-weight: 600;
                color: #262626;
                margin: 0 0 4px 0;
              }

              p {
                font-size: 13px;
                color: #8c8c8c;
                margin: 0;
                line-height: 1.4;
              }
            }
          }
        }
      }
    }

    // 流程应用预览样式
    .flow-preview {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px;

      .flow-mockup {
        width: 100%;
        max-width: 400px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .mockup-header {
          background: #f8f9fa;
          padding: 12px 16px;
          border-bottom: 1px solid #e8e8e8;

          .mockup-tabs {
            display: flex;
            gap: 16px;

            .tab {
              font-size: 12px;
              color: #8c8c8c;
              padding: 4px 0;
              cursor: pointer;
              position: relative;

              &.active {
                color: #1890ff;
                font-weight: 500;

                &::after {
                  content: '';
                  position: absolute;
                  bottom: -12px;
                  left: 0;
                  right: 0;
                  height: 2px;
                  background: #1890ff;
                }
              }
            }
          }
        }

        .mockup-content {
          padding: 24px;

          .workflow-nodes {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .node {
              padding: 8px 16px;
              border-radius: 6px;
              font-size: 12px;
              font-weight: 500;
              color: white;
              text-align: center;
              min-width: 80px;

              &.start-node {
                background: #52c41a;
              }

              &.process-node {
                background: #1890ff;
              }

              &.end-node {
                background: #8c8c8c;
              }
            }

            .node-connector {
              width: 2px;
              height: 16px;
              background: #d9d9d9;
            }
          }
        }
      }
    }

    // 默认状态样式
    .default-preview {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .empty-state {
        text-align: center;
        color: #8c8c8c;

        svg {
          margin-bottom: 16px;
        }

        p {
          font-size: 14px;
          margin: 0;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  @media (max-width: 768px) {
    flex-direction: column-reverse;
    gap: 8px;

    .el-button {
      width: 100%;
      margin: 0;
    }
  }

  .el-button {
    min-width: 100px;
    height: 40px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.cancel-btn {
      background: #fff;
      border: 1px solid #d9d9d9;
      color: #595959;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }

    &.create-btn {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border: none;
      color: white;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #40a9ff, #69c0ff);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        transform: translateY(-1px);
      }

      &:disabled {
        background: #f5f5f5;
        color: #bfbfbf;
        box-shadow: none;
        cursor: not-allowed;
      }

      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
