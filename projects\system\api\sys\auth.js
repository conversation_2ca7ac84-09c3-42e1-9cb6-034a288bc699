
import request from '@/utils/request'

// 获取启用的连接器列表
export function getEnabledConnectors() {
  return request({
    url: 'system/auth/connector/config/list',
    method: 'get',
    params: {
      enabled: true
    }
  })
}

// 获取连接器二维码
export function getConnectorQrCode(qrCodeKey, connectorId) {
  return request({
    url: 'system/external/auth/qrCode',
    method: 'get',
    params: { qrCodeKey, connectorId },
    responseType: 'blob'  // 接收二进制图片数据
  })
}

// 执行授权跳转
export function doAuth(connectorId, redirectUri) {
  return request({
    url: 'system/external/auth/doAuth',
    method: 'get',
    params: { connectorId, redirectUri }
  })
}

// 轮询
export function poll(qrCodeKey) {
  return request({
    url: 'system/external/auth/poll',
    method: 'get',
    params: { qrCodeKey }
  })
}

// 获取插件列表
export function getPlugins() {
  return request({
    url: 'system/actuator/plugin',
    method: 'get'
  })
}

// 获取二维码信息
export function getQrCode(connectorId) {
  return request({
    url: 'system/external/auth/qrCode',
    method: 'get',
    params: { connectorId }
  })
}

// 获取微信二维码图片
export function getWechatQrCode(ticket) {
  // 直接返回完整的URL，让前端直接使用
  return `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${encodeURIComponent(ticket)}`;
}

// 轮询登录状态
export function pollLoginStatus(qrCodeKey) {
  return request({
    url: 'system/external/wechat/official/poll',
    method: 'get',
    params: { qrCodeKey }
  })
}

// 注册接口
export function register(data) {
  return request({
    url: 'system/register',
    method: 'post',
    data
  })
}

// 验证身份（用于重置密码第一步）
export function verifyResetCode(data) {
  return request({
    url: 'system/verify/identity',
    method: 'post',
    data: {
      identifier: data.email || data.phone, // 根据找回方式使用邮箱或手机号
      verifyCode: data.code,
      verifyType: data.email ? 'email' : 'phone',
      verifyScene: 'resetPassword'
    }
  })
}

// 重置密码（用于重置密码第二步）
export function resetPassword(data) {
  return request({
    url: 'system/resetPassword',
    method: 'put',  // 改为 PUT 方法
    headers: {
      'X-Temp-Token': localStorage.getItem('tempToken')
    },
    data: {
      password: data.password,
      confirmPassword: data.confirmPassword  // 添加确认密码字段
    }
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: 'system/changePassword',
    method: 'put',
    data
  })
}

// 解绑第三方账号
export function unbindAccount(data) {
  return request({
    url: 'system/external/auth/unbind',
    method: 'post',
    data
  })
}

// 获取绑定二维码
export function getBindQrCode(connectorId, redirectUri) {
  return request({
    url: 'system/external/auth/bind',
    method: 'post',
    data: { connectorId, redirectUri }
  })
}

// 轮询绑定状态
export function pollBindStatus(qrCodeKey) {
  return request({
    url: 'system/external/wechat/official/poll',
    method: 'get',
    params: { qrCodeKey }
  })
} 