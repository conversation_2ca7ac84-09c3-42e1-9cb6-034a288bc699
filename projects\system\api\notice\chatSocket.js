import {Client} from '@stomp/stompjs'
import '@basics/layout/components/Notice/StompClientEx.js'
import {getToken} from '@/utils/auth'
import store from '@/store'

// WebSocket服务器URL
const getServerUrl = () => {
  // 是否为生产
  const isProduction = process.env.NODE_ENV === 'production'
  if(!isProduction){
    // 从环境变量中获取测试环境的WebSocket URL
    return process.env.VUE_APP_NOTICE_WS_URL
  }
  const isHttps = window.location.protocol === 'https:';
  return (isHttps ? 'wss://' : 'ws://') + window.location.host + '/im/ws';
}

const getCurrentUserName = () => {
  return store.getters.username|| 'anonymous'
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的时间
 */
const formatTime = (time) => {
  const date = new Date(time)
  if (isNaN(date.getTime())) {
    return time
  }

  const now = new Date()
  const diff = (now - date) / 1000 / 60

  if (diff < 60) {
    return `${Math.floor(diff)} 分钟前`
  } else if (diff < 1440) {
    return `${Math.floor(diff / 60)} 小时前`
  } else if (diff < 10080) {
    const days = Math.floor(diff / 1440)
    return `${days} 天前`
  } else {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
}

/**
 * 创建一个Stomp客户端管理类
 */
class ChatSocketService {
  constructor() {
    this.stompClient = null
    this.connected = false
    this.messageHandlers = new Map()
    this.subscriptions = new Map()
    this.heartbeatMonitorTimer = null
    this.connectionFailed = false
    this.maxRetryAttempts = 3
    this.retryCount = 0
  }

  /**
   * 初始化WebSocket连接
   * @returns {Promise} 连接成功后的Promise
   */
  connect() {
    return new Promise((resolve, reject) => {
      // 如果之前已经确定连接失败，直接拒绝
      if (this.connectionFailed) {
        reject(new Error('WebSocket连接已被标记为不可用'))
        return
      }

      if (this.connected && this.stompClient?.connected) {
        resolve(this.stompClient)
        return
      }

      // 如果已经达到最大重试次数，直接拒绝
      if (this.retryCount >= this.maxRetryAttempts) {
        this.connectionFailed = true
        reject(new Error(`WebSocket连接失败: 已达到最大重试次数(${this.maxRetryAttempts})`))
        return
      }

      try {
        // 服务器URL，添加认证Token
        const serverUrl = `${getServerUrl()}?Authorization=${getToken()}`

        // 创建STOMP客户端
        this.stompClient = new Client({
          brokerURL: serverUrl,
          reconnectDelay: 5000, // 重连延迟，使用stomp.js内置的重连机制
          heartbeatIncoming: 60000,
          heartbeatOutgoing: 60000,
          // 禁用自动重连
          maxRetries: this.maxRetryAttempts - this.retryCount
          // 启用调试，可以在控制台看到更多详细信息
          // debug: function(str) {
          //   console.log('STOMP: ' + str);
          // }
        })

        // 设置连接成功回调
        this.stompClient.onConnect = frame => {
          console.log('WebSocket连接成功!')
          console.log('服务器心跳配置:', frame.headers['heart-beat'])
          this.connected = true
          this.connectionFailed = false
          this.retryCount = 0

          // 启动心跳监控
          this.startHeartbeatMonitor()

          resolve(this.stompClient)
        }

        // 设置错误回调
        this.stompClient.onStompError = frame => {
          console.error('WebSocket连接失败:', frame.headers.message)
          this.connected = false
          this.retryCount++
          
          if (this.retryCount >= this.maxRetryAttempts) {
            this.connectionFailed = true
            console.error(`WebSocket连接已达到最大重试次数(${this.maxRetryAttempts})，不再尝试重连`)
          }
          
          reject(new Error(`WebSocket连接错误: ${frame.headers.message}`))
        }

        // 设置断线回调
        this.stompClient.onWebSocketClose = (evt) => {
          console.log('WebSocket连接已关闭，状态码:', evt ? evt.code : 'unknown')
          this.connected = false
          this.retryCount++
          
          if (this.retryCount >= this.maxRetryAttempts) {
            this.connectionFailed = true
            console.error(`WebSocket连接已达到最大重试次数(${this.maxRetryAttempts})，不再尝试重连`)
            
            // 清理连接资源
            if (this.stompClient) {
              this.stompClient.deactivate({ forceBefore: true })
              this.stompClient = null
            }
          }
        }

        // 激活连接
        console.log('正在连接WebSocket...')
        this.stompClient.activate()

      } catch (e) {
        console.error('初始化WebSocket错误:', e)
        this.connected = false
        this.retryCount++
        
        if (this.retryCount >= this.maxRetryAttempts) {
          this.connectionFailed = true
        }
        
        reject(e)
      }
    })
  }

  /**
   * 启动心跳监控，跟踪心跳状态
   */
  startHeartbeatMonitor() {
    // 清除现有的心跳监控
    if (this.heartbeatMonitorTimer) {
      clearInterval(this.heartbeatMonitorTimer)
    }

    // 每10秒检查一次连接状态
    this.heartbeatMonitorTimer = setInterval(() => {
      if (this.stompClient && this.connected) {
        // console.log('心跳监控: 连接正常')
      } else {
        console.log('心跳监控: 连接已断开')
        // 如果连接已断开但状态未更新，进行重连
        if (!this.connected) {
          clearInterval(this.heartbeatMonitorTimer)
          this.heartbeatMonitorTimer = null
        }
      }
    }, 10000)
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.stompClient && this.connected) {
      console.log('断开WebSocket连接')

      try {
        // 使用receipt来确认断开操作完成
        this.stompClient.deactivate({ forceBefore: true })
        console.log('STOMP客户端已停用')
      } catch (e) {
        console.error('断开连接时出错:', e)
      }

      this.stompClient = null
      this.connected = false

      // 清除所有计时器
      if (this.heartbeatMonitorTimer) {
        clearInterval(this.heartbeatMonitorTimer)
        this.heartbeatMonitorTimer = null
      }

      // 清除所有订阅
      this.subscriptions.clear()
      this.messageHandlers.clear()

      console.log('所有WebSocket资源已清理')
    }
  }

  /**
   * 订阅群组消息
   * @param {String} groupId 群组ID
   * @param {Function} callback 消息处理回调函数
   * @returns {Promise<Object>} 订阅对象
   */
  subscribeGroupMessages(groupId, callback) {
    const topic = `/topic/group/${groupId}`
    return this.subscribe(topic, callback)
  }

  /**
   * 通用订阅方法
   * @param {String} destination 订阅地址
   * @param {Function} callback 消息处理回调函数
   * @returns {Promise<Object>} 订阅对象的Promise
   */
  subscribe(destination, callback) {
    return new Promise((resolve, reject) => {
      if (!destination) {
        reject(new Error('订阅地址不能为空'))
        return
      }

      if (!callback || typeof callback !== 'function') {
        reject(new Error('回调函数不能为空且必须是函数'))
        return
      }

      // 如果之前已确定连接失败，则直接拒绝
      if (this.connectionFailed) {
        reject(new Error('WebSocket服务不可用，无法订阅'))
        return
      }

      // 检查是否已有该订阅
      if (this.subscriptions.has(destination)) {
        console.log(`已存在订阅: ${destination}，直接返回`)
        resolve(this.subscriptions.get(destination))
        return
      }

      // 尝试连接
      this.connect()
          .then(client => {
            console.log(`正在订阅: ${destination}`)
            const subscription = client.subscribe(destination, message => {
              try {
                const body = JSON.parse(message.body)
                console.log(`收到消息[${destination}]:`, body)
                callback(body, message)
              } catch (e) {
                console.error(`解析消息错误[${destination}]:`, e, message.body)
                callback(null, message)
              }
            })

            // 保存处理函数，以便后续可以复用
            this.messageHandlers.set(destination, callback)
            this.subscriptions.set(destination, subscription)

            console.log(`已成功订阅: ${destination}`)
            resolve(subscription)
          })
          .catch(err => {
            console.error(`订阅失败[${destination}]:`, err.message)
            reject(err)
          })
    })
  }

  /**
   * 取消订阅
   * @param {String} destination 订阅地址
   */
  unsubscribe(destination) {
    const subscription = this.subscriptions.get(destination)
    if (subscription) {
      subscription.unsubscribe()
      this.subscriptions.delete(destination)
      this.messageHandlers.delete(destination)
      console.log(`已取消订阅: ${destination}`)
    }
  }

  /**
   * 取消所有订阅
   */
  unsubscribeAll() {
    this.subscriptions.forEach((subscription, destination) => {
      this.unsubscribe(destination)
    })
  }

  /**
   * 发送广播消息
   * @param {Object} message 消息内容
   * @returns {Promise} 发送结果Promise
   */
  sendBroadcastMessage(message) {
    // 创建符合后端Message结构的消息对象
    const broadcastMessage = {
      ...message,
      chatType: 'BROADCAST',
      contentType: message.contentType || 'TEXT',
      fromUsername: getCurrentUserName(),
      receiver: 'ALL', // 广播消息接收者为ALL
    }

    return this.sendMessage(broadcastMessage)
  }

  /**
   * 发送私信消息
   * @param {String} receiverName 接收者用户名称
   * @param {Object} message 消息内容
   * @returns {Promise} 发送结果Promise
   */
  sendPrivateMessage(receiverName, message) {
    // 创建符合后端Message结构的消息对象
    const privateMessage = {
      ...message,
      chatType: 'PRIVATE',
      contentType: message.contentType || 'TEXT',
      fromUsername: getCurrentUserName(),
      receiver: receiverName
    }

    return this.sendMessage(privateMessage)
  }

  /**
   * 发送群组消息
   * @param {String} groupId 群组ID
   * @param {Object} message 消息内容
   * @returns {Promise} 发送结果Promise
   */
  sendGroupMessage(groupId, message) {
    // 创建符合后端Message结构的消息对象
    const groupMessage = {
      ...message,
      chatType: 'GROUP',
      contentType: message.contentType || 'TEXT',
      fromUsername: getCurrentUserName(),
      receiver: groupId
    }

    return this.sendMessage(groupMessage)
  }

  /**
   * 统一消息发送方法，使用后端的/send端点
   * @param {Object} message 符合后端结构的消息对象
   * @returns {Promise} 发送结果Promise
   */
  sendMessage(message) {
    return new Promise((resolve, reject) => {
      if (!message.chatType) {
        reject(new Error('消息类型不能为空'))
        return
      }

      if (!message.receiver) {
        reject(new Error('接收者不能为空'))
        return
      }

      // 如果之前已确定连接失败，则直接拒绝
      if (this.connectionFailed) {
        reject(new Error('WebSocket服务不可用，无法发送消息'))
        return
      }

      this.connect()
          .then(client => {
            const destination = '/app/send'
            client.publish({
              destination,
              body: JSON.stringify(message)
            })

            console.log(`消息已发送[${message.chatType}]:`, message)
            resolve(true)
          })
          .catch(err => {
            reject(err)
          })
    })
  }

  /**
   * 发送消息到指定用户（兼容旧接口）
   * @param {String} receiverName 接收者用户名称 (principal.getName())
   * @param {Object} message 消息内容
   * @returns {Promise} 发送结果Promise
   */
  sendToUser(receiverName, message) {
    console.log('使用sendToUser发送消息到用户:', receiverName, message)
    return this.sendPrivateMessage(receiverName, message)
  }

  /**
   * 发送消息到系统频道（兼容旧接口）
   * @param {Object} message 消息内容
   * @returns {Promise} 发送结果Promise
   */
  sendSystemMessage(message) {
    console.log('使用sendSystemMessage发送广播消息:', message)
    return this.sendBroadcastMessage(message)
  }

  /**
   * 处理新消息
   * @param {Object} message 消息对象
   * @returns {Object} 处理后的消息对象
   */
  handleNewMessage(message) {
    return {
      id: message.id || Date.now(),
      title: this.getMessageTitle(message),
      content: message.content || '',
      time: message.sendTime || new Date().toISOString(),
      read: false,
      type: this.getMessageType(message),
      sender: message.fromUsername || '系统',
      link: message.link || '',
      displayName: message.displayName || message.fromUsername || '系统',
      disPlayPicUrl: message.disPlayPicUrl || '',
      groupSpeakerDisplayName: message.groupSpeakerDisplayName || '',
      chatType: message.chatType
    }
  }

  /**
   * 获取消息类型
   * @param {Object} message 消息对象
   * @returns {String} 消息类型
   */
  getMessageType(message) {
    if (message.type) return message.type

    if (message.chatType === 'BROADCAST') return 'system'
    if (message.chatType === 'PRIVATE') return 'admin'
    if (message.chatType === 'GROUP') return 'group'

    return 'system'
  }

  /**
   * 获取消息标题
   * @param {Object} message 消息对象
   * @returns {String} 消息标题
   */
  getMessageTitle(message) {
    if (message.title) return message.title

    if (message.chatType === 'BROADCAST') {
      return '系统广播'
    } else if (message.chatType === 'PRIVATE') {
      return `来自 ${message.fromUsername} 的消息`
    } else if (message.chatType === 'GROUP') {
      const groupId = message.groupTopic ? message.groupTopic.split('/').pop() : message.receiver
      return `群组消息: ${groupId}`
    }
    return '系统通知'
  }

  /**
   * 获取通知类型
   * @param {Object} message 消息对象
   * @returns {String} 通知类型
   */
  getNotificationType(message) {
    if (message.chatType === 'BROADCAST') return 'info'
    if (message.chatType === 'PRIVATE') return 'success'
    if (message.chatType === 'GROUP') return 'warning'

    return 'info'
  }
}

// 创建单例
const chatSocketService = new ChatSocketService()

export { formatTime }
export default chatSocketService 