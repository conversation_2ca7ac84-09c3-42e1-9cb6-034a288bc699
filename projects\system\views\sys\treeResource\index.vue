<template>
  <div class="resource-management">
    <!-- 左侧菜单树 -->
    <div class="resource-tree">
      <div class="tree-header">
        <div class="header-title">
          <span>菜单列表</span>
          <el-button type="primary" size="small" @click="handleAdd">
            <i class="el-icon-plus"></i> 新增资源
          </el-button>
        </div>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索资源名称"
            prefix-icon="el-icon-search"
            clearable
            size="small"
            @clear="handleSearchClear"
          />
        </div>
        <el-tooltip class="tree-tip" content="您可以拖拽菜单调整顺序和层级关系，菜单只能放在应用或目录下" placement="top">
          <div class="tip-box">
            <i class="el-icon-info"></i>
            <span>支持拖拽调整顺序和层级</span>
          </div>
        </el-tooltip>
      </div>
      <div class="tree-container">
        <el-tree
          ref="resourceTree"
          :data="treeData"
          :props="defaultProps"
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          draggable
          :allow-drop="allowDrop"
          :allow-drag="allowDrag"
          :default-expanded-keys="expandedKeys"
          @node-drag-start="handleDragStart"
          @node-drag-enter="handleDragEnter"
          @node-drag-leave="handleDragLeave"
          @node-drag-end="handleDragEnd"
          @node-drop="handleDrop"
          @node-click="handleNodeClick"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <div class="node-content">
              <svg-icon v-if="data.icon" :icon-class="data.icon" class="node-icon" />
              <i v-else class="el-icon-menu"></i>
              <span :class="{ 'highlight': isHighlighted(node, searchKeyword) }">
                {{ node.label }}
              </span>
              <el-tag size="mini" :type="getTypeTag(data.type)" class="resource-type">
                {{ getTypeLabel(data.type) }}
              </el-tag>
            </div>
            <div class="node-actions">
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  size="mini"
                  type="text"
                  @click.stop="handleView(data)"
                >
                  <i class="el-icon-view"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip content="复制" placement="top">
                <el-button
                  size="mini"
                  type="text"
                  @click.stop="handleCopy(data)"
                >
                  <i class="el-icon-document-copy"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <el-button
                  size="mini"
                  type="text"
                  @click.stop="handleEdit(data)"
                >
                  <i class="el-icon-edit"></i>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button
                  size="mini"
                  type="text"
                  class="danger"
                  @click.stop="handleDelete(node, data)"
                >
                  <i class="el-icon-delete"></i>
                </el-button>
              </el-tooltip>
            </div>
          </span>
        </el-tree>
      </div>
    </div>

    <!-- 右侧资源详情 -->
    <div class="resource-detail" v-if="currentResource">
      <div class="detail-content">
        <!-- 基本信息区域 -->
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-info"></i>
            <span>基本信息</span>
            <div class="section-actions">
              <el-button 
                type="primary" 
                size="small"
                icon="el-icon-document-copy"
                class="action-button"
                @click="handleCopy(currentResource)"
              >
                复制资源
              </el-button>
              <el-button 
                type="primary" 
                size="small"
                icon="el-icon-edit"
                class="action-button"
                @click="handleEdit(currentResource)"
              >
                编辑资源
              </el-button>
              <el-button 
                type="danger" 
                size="small"
                icon="el-icon-delete"
                class="action-button"
                @click="handleDelete(null, currentResource)"
              >
                删除资源
              </el-button>
            </div>
          </div>
          <div class="info-list">
            <div class="info-item">
              <label>资源名称：</label>
              <div class="value-with-icon">
                <svg-icon v-if="currentResource.icon" :icon-class="currentResource.icon" class="node-icon" />
                <i v-else class="el-icon-menu"></i>
                <span>{{ currentResource.name }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>资源类型：</label>
              <div class="value-with-icon">
                <i class="el-icon-collection-tag"></i>
                <el-tag size="small" :type="getTypeTag(currentResource.type)">
                  {{ getTypeLabel(currentResource.type) }}
                </el-tag>
              </div>
            </div>
            <div class="info-item">
              <label>排序号：</label>
              <div class="value-with-icon">
                <i class="el-icon-sort"></i>
                <span>{{ currentResource.sort }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>路由路径：</label>
              <div class="value-with-icon">
                <i class="el-icon-link"></i>
                <span>{{ currentResource.path || '无' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>组件路径：</label>
              <div class="value-with-icon">
                <i class="el-icon-files"></i>
                <span>{{ currentResource.component || '无' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>权限标识：</label>
              <div class="value-with-icon">
                <i class="el-icon-key"></i>
                <span>{{ currentResource.perms || '无' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>状态：</label>
              <div class="value-with-icon">
                <i class="el-icon-circle-check" v-if="currentResource.status"></i>
                <i class="el-icon-circle-close" v-else></i>
                <span>{{ currentResource.status ? '启用' : '禁用' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>可见性：</label>
              <div class="value-with-icon">
                <i class="el-icon-view" v-if="currentResource.visible"></i>
                <i class="el-icon-hide" v-else></i>
                <span>{{ currentResource.visible ? '显示' : '隐藏' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>缓存：</label>
              <div class="value-with-icon">
                <i class="el-icon-document-copy" v-if="currentResource.cacheStatus"></i>
                <i class="el-icon-document" v-else></i>
                <span>{{ currentResource.cacheStatus ? '开启' : '关闭' }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>新窗口：</label>
              <div class="value-with-icon">
                <i class="el-icon-top-right" v-if="currentResource.windowOpen"></i>
                <i class="el-icon-back" v-else></i>
                <span>{{ currentResource.windowOpen ? '是' : '否' }}</span>
              </div>
            </div>
            <div class="info-item full">
              <label>备注说明：</label>
              <div class="value-with-icon">
                <i class="el-icon-document"></i>
                <span>{{ currentResource.remark || '暂无描述' }}</span>
              </div>
            </div>
            <div class="info-item full">
              <label>完整路由路径：</label>
              <div class="value-with-icon">
                <i class="el-icon-connection"></i>
                <el-tag size="medium" type="danger" effect="plain">
                  {{ fullResourcePath }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 子资源区域 -->
        <div class="info-section">
          <div class="section-title">
            <i class="el-icon-menu"></i>
            <span>子资源</span>
            <span class="resource-count" v-if="currentResource.children && currentResource.children.length > 0">
              共 {{ currentResource.children.length }} 个
            </span>
            <div class="section-actions" v-if="currentResource">
              <el-button 
                type="primary" 
                size="small"
                icon="el-icon-plus"
                class="action-button"
                @click="handleAddChild"
              >
                添加子资源
              </el-button>
            </div>
          </div>
          
          <!-- 子资源为空时的缺省图 -->
          <div v-if="!currentResource.children || currentResource.children.length === 0" class="empty-children">
            <div class="empty-image">
              <i class="el-icon-folder-opened"></i>
            </div>
            <div class="empty-text">暂无子资源</div>
            <el-button type="primary" size="small" @click="handleAddChild">
              <i class="el-icon-plus"></i> 添加子资源
            </el-button>
          </div>
          
          <!-- 有子资源时显示表格 -->
          <div v-else class="table-container" style="height: 100%;">
            <el-table
              :data="paginatedChildren"
              style="width: 100%"
              border
              stripe
              highlight-current-row
              height="calc(100% - 110px)"
              class="custom-table"
            >
              <el-table-column prop="name" label="资源名称" min-width="120">
                <template slot-scope="{ row }">
                  <div class="resource-info">
                    <svg-icon v-if="row.icon" :icon-class="row.icon" class="node-icon" />
                    <i v-else class="el-icon-menu"></i>
                    <span>{{ row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="资源类型" width="100" align="center">
                <template slot-scope="{ row }">
                  <el-tag size="medium" :type="getTypeTag(row.type)" effect="plain">
                    {{ getTypeLabel(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="path" label="路由路径" min-width="150" show-overflow-tooltip />
              <el-table-column prop="sort" label="排序号" width="80" align="center" />
              <el-table-column label="操作" width="150" align="center" class-name="operation-column">
                <template slot-scope="{ row }">
                  <el-tooltip content="查看详情" placement="top">
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleView(row)"
                    >
                      <i class="el-icon-view"></i>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="复制" placement="top">
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleCopy(row)"
                    >
                      <i class="el-icon-document-copy"></i>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="编辑" placement="top">
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleEdit(row)"
                    >
                      <i class="el-icon-edit"></i>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top">
                    <el-button
                      size="mini"
                      type="text"
                      class="danger"
                      @click="handleDelete(null, row)"
                    >
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <div class="pagination-container">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="currentResource.children ? currentResource.children.length : 0"
                background
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资源表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
      class="svg-icon-dialog"
    >
      <el-form
        ref="resourceForm"
        :model="resourceForm"
        :rules="formRules"
        label-width="100px"
        class="resource-form"
      >
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="资源名称" prop="name">
                <el-input v-model="resourceForm.name" :disabled="dialogType === 'view'" placeholder="请输入资源名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上级资源" prop="parentId">
                <el-cascader
                  v-model="resourceForm.parentId"
                  :options="filteredTreeData"
                  :props="{
                    value: 'id',
                    label: 'name',
                    children: 'children',
                    checkStrictly: true,
                    emitPath: false
                  }"
                  :disabled="dialogType === 'view'"
                  clearable
                  placeholder="请选择上级资源"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="资源类型" prop="type">
                <el-select v-model="resourceForm.type" :disabled="dialogType === 'view'" placeholder="请选择资源类型">
                  <el-option label="应用" value="APP" />
                  <el-option label="目录" value="DIR" />
                  <el-option label="菜单" value="MENU" />
                  <el-option label="按钮" value="BUTTON" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序号" prop="sort">
                <el-input-number v-model="resourceForm.sort" :disabled="dialogType === 'view'" :min="0" :max="99999" />
              </el-form-item>
            </el-col>
          
            <!-- 应用类型时新增LOGO和LOGO文字字段 -->
            <template v-if="resourceForm.type === 'APP'">
              <el-col :span="12">
                <el-form-item label="Logo" prop="logo">
                  <el-upload
                    v-if="dialogType !== 'view'"
                    class="logo-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="beforeLogoUpload"
                    :http-request="handleLogoUpload"
                    :on-success="handleLogoSuccess"
                    :on-error="handleLogoError"
                    accept="image/png,image/jpeg"
                  >
                    <div class="upload-container">
                      <img v-if="resourceForm.logo" :src="url + '/' + resourceForm.logo" class="uploaded-logo">
                      <div v-else class="upload-placeholder">
                        <i class="el-icon-picture-outline"></i>
                        <span>Logo图标</span>
                      </div>
                    </div>
                  </el-upload>
                        <div v-else class="logo-uploader">
        <div class="el-upload">
          <div class="upload-container">
            <img v-if="resourceForm.logo" :src="url + '/' + resourceForm.logo" class="uploaded-logo" />
            <div v-else class="upload-placeholder">
              <i class="el-icon-picture-outline"></i>
              <span>暂无Logo</span>
            </div>
          </div>
        </div>
      </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="Logo标题" prop="logoTitle">
                  <el-upload
                    v-if="dialogType !== 'view'"
                    class="logo-uploader logo-text-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="beforeLogoTextUpload"
                    :http-request="handleLogoTextUpload"
                    :on-success="handleLogoTextSuccess"
                    :on-error="handleLogoTextError"
                    accept="image/png,image/jpeg"
                  >
                    <div class="upload-container">
                      <img v-if="resourceForm.logoTitle" :src="url + '/' + resourceForm.logoTitle" class="uploaded-logo">
                      <div v-else class="upload-placeholder">
                        <i class="el-icon-picture"></i>
                        <span>Logo标题图片</span>
                      </div>
                    </div>
                  </el-upload>
                        <div v-else class="logo-uploader logo-text-uploader">
        <div class="el-upload">
          <div class="upload-container">
            <img v-if="resourceForm.logoTitle" :src="url + '/' + resourceForm.logoTitle" class="uploaded-logo" />
            <div v-else class="upload-placeholder">
              <i class="el-icon-picture"></i>
              <span>暂无Logo标题图片</span>
            </div>
          </div>
        </div>
      </div>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="12">
              <el-form-item label="图标" prop="icon">
                <SvgIconEditor v-model="resourceForm.icon" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 按钮类型的资源配置 -->
        <div class="form-section" v-if="resourceForm.type === 'BUTTON'">
          <div class="section-title">按钮配置</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="权限标识" prop="perms" required>
                <el-input v-model="resourceForm.perms" :disabled="dialogType === 'view'" placeholder="请输入权限标识" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 非按钮类型的路由配置 -->
        <div class="form-section" v-if="resourceForm.type !== 'BUTTON'">
          <div class="section-title">路由配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="路由路径" prop="path">
                <el-input v-model="resourceForm.path" :disabled="dialogType === 'view'" placeholder="请输入路由路径" />
              </el-form-item>
            </el-col>
            
            <!-- 仅菜单类型显示的字段 -->
            <el-col :span="12" v-if="resourceForm.type === 'MENU'">
              <el-form-item label="模式" prop="mode">
                <el-select v-model="resourceForm.mode" :disabled="dialogType === 'view'" placeholder="请选择模式">
                  <el-option label="普通" :value="0" />
                  <el-option label="外链" :value="1" />
                  <el-option label="表单" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            
            <!-- 应用和目录类型固定为普通模式 -->
            <el-col :span="12" v-if="resourceForm.type === 'APP' || resourceForm.type === 'DIR'">
              <el-form-item label="模式">
                <el-input value="普通" disabled />
              </el-form-item>
            </el-col>
            
            <!-- 仅菜单类型显示的字段 -->
            <el-col :span="24" v-if="resourceForm.type === 'MENU'">
              <el-form-item label="组件路径" prop="component">
                <el-input v-model="resourceForm.component" :disabled="dialogType === 'view'" placeholder="请输入组件路径" />
              </el-form-item>
            </el-col>
            
            <!-- 仅菜单类型显示的字段 -->
            <el-col :span="12" v-if="resourceForm.type === 'MENU' || resourceForm.type === 'APP'">
              <el-form-item label="激活菜单" prop="activeMenu">
                <el-input v-model="resourceForm.activeMenu" :disabled="dialogType === 'view'" placeholder="请输入激活菜单" />
              </el-form-item>
            </el-col>
            
            <!-- 仅菜单类型显示的字段 -->
            <el-col :span="12" v-if="resourceForm.type === 'MENU'">
              <el-form-item label="权限标识" prop="perms">
                <el-input v-model="resourceForm.perms" :disabled="dialogType === 'view'" placeholder="请输入权限标识" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <div class="section-title">其他配置</div>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="启用">
                <el-switch v-model="resourceForm.status" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="可见性">
                <el-switch v-model="resourceForm.visible" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="新窗口打开">
                <el-switch v-model="resourceForm.windowOpen" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="缓存">
                <el-switch v-model="resourceForm.cacheStatus" :disabled="dialogType === 'view'" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注说明" prop="remark">
                <el-input
                  type="textarea"
                  v-model="resourceForm.remark"
                  :disabled="dialogType === 'view'"
                  :rows="3"
                  placeholder="请输入备注说明"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ dialogType === 'view' ? '关闭' : '取 消' }}</el-button>
        <el-button v-if="dialogType !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getResourceTreeList, addResource, updateResource, deleteResource, batchUpdateResource } from '@system/api/sys/treeResource'
import SvgIconEditor from '@/components/auto-page/SvgIcon/index.vue'
import UploadHelper from '@/components/auto-page/fileUpload/uploadHelper.js'

export default {
  name: 'TreeResource',
  components:{
    SvgIconEditor 
  },

  data() {
    // 自定义验证函数：确保菜单只能在应用和目录下
    const validateParentType = (rule, value, callback) => {
      if (this.resourceForm.type === 'MENU' && value) {
        // 根据parentId查找父节点
        const findNodeById = (nodes, id) => {
          for (let node of nodes) {
            if (node.id === id) {
              return node
            }
            if (node.children && node.children.length > 0) {
              const found = findNodeById(node.children, id)
              if (found) return found
            }
          }
          return null
        }
        
        const parentNode = findNodeById(this.treeData, value)
        
        if (parentNode && parentNode.type !== 'APP' && parentNode.type !== 'DIR') {
          callback(new Error('菜单只能放在应用或目录下'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    
    return {
      searchKeyword: '',
      treeData: [],
      expandedKeys: [], // 新增：用于保存展开的节点key
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      dialogVisible: false,
      dialogType: 'add', // add, edit, view
      submitLoading: false,
      resourceForm: {
        name: '',
        parentId: null,
        type: 'MENU',
        sort: 0,
        path: '',
        component: '',
        perms: '',
        icon: '',
        status: true,
        visible: true,
        cacheStatus: false,
        mode: 0,
        activeMenu: '',
        remark: '',
        windowOpen: false,
        logo: '', // 新增logo字段
        logoTitle: '' // 新增logoTitle字段
      },
      formRules: {
        name: [
          { required: true, message: '请输入资源名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择资源类型', trigger: 'change' }
        ],
        parentId: [
          { validator: validateParentType, trigger: 'change' }
        ],
        path: [
          { required: true, message: '请输入路由路径', trigger: 'blur' }
        ],
        component: [
          { 
            required: true, 
            message: '请输入组件路径', 
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.resourceForm.type === 'MENU' && !value) {
                callback(new Error('请输入组件路径'))
              } else {
                callback()
              }
            }
          }
        ],
        perms: [
          { 
            validator: (rule, value, callback) => {
              if ((this.resourceForm.type === 'BUTTON') && !value) {
                callback(new Error('请输入权限标识'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        mode: [
          { 
            required: true, 
            message: '请选择模式', 
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.resourceForm.type === 'MENU' && value === undefined) {
                callback(new Error('请选择模式'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      typeLabels: {
        APP: '应用',
        MENU: '菜单',
        BUTTON: '按钮',
        DIR: '目录'
      },
      currentResource: null, // 当前选中的资源
      currentPage: 1, // 当前页码
      pageSize: 10 // 每页显示条数
    }
  },
  computed: {
    dialogTitle() {
      const titles = {
        add: '新增资源',
        edit: '编辑资源',
        view: '资源详情'
      }
      return titles[this.dialogType] || ''
    },
    
    // 计算完整路由路径
    fullResourcePath() {
      if (!this.currentResource) return '';
      
      // 如果当前资源没有路径，返回无
      if (!this.currentResource.path) return '无';
      
      // 查找父级路径
      const findParentPath = (nodes, parentId) => {
        if (!parentId || parentId === 0) return '';
        
        for (let node of nodes) {
          if (node.id === parentId) {
            // 如果找到父节点
            const parentPath = node.path || '';
            // 递归查找更上层的父节点路径
            const upperPath = findParentPath(this.treeData, node.parentId);
            
            // 拼接路径，确保中间只有一个反斜杠
            if (!upperPath) return parentPath;
            if (!parentPath) return upperPath;
            
            // 处理路径拼接
            const normUpperPath = upperPath.endsWith('/') ? upperPath.slice(0, -1) : upperPath;
            const normParentPath = parentPath.startsWith('/') ? parentPath : '/' + parentPath;
            
            return normUpperPath + normParentPath;
          }
          
          if (node.children && node.children.length > 0) {
            const result = findParentPath(node.children, parentId);
            if (result) return result;
          }
        }
        
        return '';
      };
      
      // 获取当前资源的路径
      const currentPath = this.currentResource.path;
      
      // 如果当前资源没有父级，直接返回当前路径
      if (!this.currentResource.parentId || this.currentResource.parentId === 0) {
        return currentPath;
      }
      
      // 查找父级完整路径
      const parentPath = findParentPath(this.treeData, this.currentResource.parentId);
      
      // 拼接完整路径，确保中间只有一个反斜杠
      if (!parentPath) return currentPath;
      
      const normParentPath = parentPath.endsWith('/') ? parentPath.slice(0, -1) : parentPath;
      const normCurrentPath = currentPath.startsWith('/') ? currentPath : '/' + currentPath;
      
      return normParentPath + normCurrentPath;
    },
    
    // 根据当前表单类型，过滤上级节点选项
    filteredTreeData() {
      // 深拷贝原始树数据
      const deepClone = (obj) => {
        if (obj === null || typeof obj !== 'object') return obj;
        
        const copy = Array.isArray(obj) ? [] : {};
        for (let key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            copy[key] = deepClone(obj[key]);
          }
        }
        return copy;
      }
      
      const filterTreeNodes = (nodes) => {
        if (!nodes || !nodes.length) return [];
        
        return nodes.filter(node => {
          // 不同类型资源的限制
          if (this.resourceForm.type === 'MENU') {
            // 菜单只能放在应用或目录下
            if (node.type !== 'APP' && node.type !== 'DIR') {
              return false;
            }
          } else if (this.resourceForm.type === 'BUTTON') {
            // 按钮只能放在菜单下
            if (node.type !== 'MENU') {
              return false;
            }
          }
          
          // 不能选择自己作为父节点
          if (this.resourceForm.id && node.id === this.resourceForm.id) {
            return false;
          }
          
          // 递归处理子节点
          if (node.children && node.children.length) {
            const filteredChildren = filterTreeNodes(node.children);
            node.children = filteredChildren;
          }
          
          return true;
        });
      };
      
      const clonedTree = deepClone(this.treeData);
      return filterTreeNodes(clonedTree);
    },
    
    // 分页显示子资源
    paginatedChildren() {
      if (!this.currentResource || !this.currentResource.children) {
        return [];
      }
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.currentResource.children.slice(start, end);
    },
    url() {
      return process.env.VUE_APP_FILE_URL
    }
  },
  watch: {
    searchKeyword(val) {
      this.$refs.resourceTree.filter(val)
    },
    'resourceForm.type'(newVal) {
      // 当资源类型改变时，重新验证父级资源
      if (this.$refs.resourceForm) {
        this.$nextTick(() => {
          this.$refs.resourceForm.validateField('parentId')
        })
      }
      
      // 根据资源类型设置默认值
      if (newVal === 'APP' || newVal === 'DIR') {
        // 应用和目录类型，固定模式为普通(0)
        this.resourceForm.mode = 0
      }
      
      // 如果是按钮类型，清空路由相关字段
      if (newVal === 'BUTTON') {
        this.resourceForm.path = ''
        this.resourceForm.component = ''
        this.resourceForm.mode = 0
        this.resourceForm.activeMenu = ''
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    // 通过JavaScript调整SvgIconEditor的popover宽度
    this.$nextTick(() => {
      // 注册MutationObserver监听DOM变化
      const observer = new MutationObserver(mutations => {
        const popoverList = document.querySelectorAll('.el-popover[x-placement^="bottom"]')
        popoverList.forEach(popover => {
          // 查找包含IconSelect的popover
          if (popover.querySelector('.icon-body')) {
            popover.style.width = '520px'
            popover.style.maxWidth = '520px'
          }
        })
      })
      
      // 开始观察body元素变化
      observer.observe(document.body, {
        childList: true,
        subtree: true
      })
      
      // 组件销毁时断开观察器
      this.$once('hook:beforeDestroy', () => {
        observer.disconnect()
      })
    })
  },
  methods: {
    // 获取资源树列表
    async getList() {
      try {
        const data = await getResourceTreeList()
        this.treeData = data || []
        
        // 如果没有初始化 expandedKeys，确保它是一个数组
        if (!this.expandedKeys) {
          this.expandedKeys = []
        }
        
        // 如果有数据，选中第一个节点
        if (this.treeData && this.treeData.length > 0) {
          this.$nextTick(() => {
            if (this.$refs.resourceTree) {
              // 先恢复展开状态
              this.restoreExpandedState()
              
              // 如果当前没有选中节点，则选中第一个
              if (!this.currentResource) {
                const firstNode = this.treeData[0]
                this.$refs.resourceTree.setCurrentKey(firstNode.id)
                this.handleNodeClick(firstNode)
              } else {
                // 保持当前选中的节点
                this.$refs.resourceTree.setCurrentKey(this.currentResource.id)
              }
            }
          })
        }
      } catch (error) {
        console.error('获取资源列表失败', error)
        this.$message.error('获取资源列表失败')
      }
    },

    // 搜索过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase())
    },

    // 清除搜索
    handleSearchClear() {
      this.$refs.resourceTree.filter('')
    },

    // 判断节点是否高亮
    isHighlighted(node, keyword) {
      if (!keyword) return false
      return node.label.toLowerCase().includes(keyword.toLowerCase())
    },

    // 获取资源类型标签样式
    getTypeTag(type) {
      const types = {
        APP: 'primary',
        DIR: 'info',
        MENU: 'success',
        BUTTON: 'warning'
      }
      return types[type] || 'info'
    },

    // 获取资源类型中文标签
    getTypeLabel(type) {
      return this.typeLabels[type] || type
    },

    // 格式化备注信息，超过长度显示省略号
    formatRemark(remark) {
      if (!remark) return ''
      const maxLength = 20
      return remark.length > maxLength ? remark.slice(0, maxLength) + '...' : remark
    },

    // 点击节点，设置当前选中的资源
    handleNodeClick(data) {
      this.currentResource = data
      this.currentPage = 1 // 重置分页
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1; // 重置到第一页
    },
    
    // 当前页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    // 允许拖拽的节点
    allowDrag(node) {
      // 所有类型的节点都可以拖拽，在allowDrop中进行更精确的限制
      return true
    },

    // 允许放置的位置
    allowDrop(draggingNode, dropNode, type) {
      // 如果不是放在内部，则始终允许（排序）
      if (type !== 'inner') {
        return true
      }
      
      // 拖动的是菜单类型，只能放在应用和目录下
      if (draggingNode.data.type === 'MENU') {
        return dropNode.data.type === 'APP' || dropNode.data.type === 'DIR'
      }
      
      // 按钮类型的节点不允许放在其内部
      if (dropNode.data.type === 'BUTTON') {
        return false
      }
      
      return true
    },

    // 开始拖拽
    handleDragStart(node, ev) {
      console.log('drag start', node)
    },

    // 拖拽进入其他节点
    handleDragEnter(draggingNode, dropNode, ev) {
      console.log('tree drag enter: ', dropNode.label)
    },

    // 拖拽离开节点
    handleDragLeave(draggingNode, dropNode, ev) {
      console.log('tree drag leave: ', dropNode.label)
    },

    // 拖拽结束
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      console.log('tree drag end: ', dropType)
      // 如果没有成功放置（dropNode为null），提供相应的提示信息
      if (!dropNode) {
        if (draggingNode.data.type === 'MENU') {
          this.$message.warning('菜单只能放在应用或目录下')
        } else if (dropType === 'inner' && draggingNode.data.type === 'BUTTON') {
          this.$message.warning('按钮不能作为父级节点')
        }
      }
    },

    // 放置节点
    async handleDrop(draggingNode, dropNode, dropType, ev) {
      if (!draggingNode || !dropNode) {
        console.warn('拖拽操作缺少必要的节点信息');
        return;
      }

      const draggingData = draggingNode.data
      const dropData = dropNode.data

      if (!draggingData || !dropData) {
        console.warn('拖拽操作缺少必要的数据信息');
        return;
      }

      // 计算新的 parentId 和 sort
      let newParentId = 0
      let newSort = 0

      if (dropType === 'inner') {
        // 放到目标节点内部
        newParentId = dropData.id
        newSort = dropData.children ? dropData.children.length : 0
      } else {
        // 放到目标节点的前面或后面
        newParentId = dropData.parentId || 0
        
        if (dropType === 'before') {
          // 放到目标节点前面，将 sort 设置为目标节点的 sort
          newSort = dropData.sort
        } else {
          // 放到目标节点后面，将 sort 设置为目标节点的 sort + 1
          newSort = dropData.sort + 1
        }
      }

      // 在更新前保存当前节点的展开状态
      this.saveExpandedState()
      
      // 记住当前选中的节点
      const currentId = draggingData.id

      try {
        // 显示加载提示
        // const loadingInstance = this.$loading({
        //   lock: true,
        //   text: '正在更新节点位置...',
        //   spinner: 'el-icon-loading',
        //   background: 'rgba(0, 0, 0, 0.7)'
        // })
        
        // 为了避免 sort 值相同导致的问题，先获取同级所有节点
        let siblingNodes = []
        
        // 查找兄弟节点
        const findSiblings = (nodes, parentId) => {
          for (let node of nodes) {
            if (node.id === parentId) {
              return node.children || []
            }
            if (node.children && node.children.length > 0) {
              const found = findSiblings(node.children, parentId)
              if (found.length > 0) return found
            }
          }
          if (parentId === 0 || parentId === null) {
            return this.treeData.filter(item => !item.parentId || item.parentId === 0)
          }
          return []
        }
        
        siblingNodes = findSiblings(this.treeData, newParentId)
        
        // 创建更新操作队列
        const updateQueue = []
        
        // 添加拖拽节点更新
        updateQueue.push({
          ...draggingData,
          parentId: newParentId,
          sort: newSort
        })
        
        // 如果是前插或后插，需要更新其他受影响节点的排序
        if (dropType !== 'inner') {
          // 找到受影响的节点（排序值需要调整的节点）
          const affectedNodes = siblingNodes.filter(node => {
            // 排除当前拖动的节点
            if (node.id === draggingData.id) return false
            
            if (dropType === 'before') {
              // 所有原本排序值 >= 目标节点排序值的其他节点需要+1
              return node.sort >= newSort
            } else {
              // 所有原本排序值 > 目标节点排序值的其他节点需要+1
              return node.sort > dropData.sort
            }
          })
          
          // 将所有需要更新的节点添加到队列
          affectedNodes.forEach(node => {
            updateQueue.push({
              ...node,
              sort: node.sort + 1
            })
          })
        }
        
        try {
          // 尝试使用批量更新接口
          try {
            await batchUpdateResource(updateQueue)
          } catch (batchError) {
            console.warn('批量更新接口失败，回退到单个更新模式', batchError)
            // 如果批量更新不可用或失败，逐个更新所有需要变更的节点
            for (const updateData of updateQueue) {
              await updateResource(updateData)
            }
          }
          
          this.$message.success('移动成功')
          
          // 关闭加载提示
          // loadingInstance.close()
          
          // 重新加载树数据
          await this.getList()
          
          // 确保当前节点仍然选中
          this.$nextTick(() => {
            if (this.$refs.resourceTree) {
              this.$refs.resourceTree.setCurrentKey(currentId)
              // 查找更新后的节点数据
              const findNodeById = (nodes, id) => {
                for (let node of nodes) {
                  if (node.id === id) {
                    return node
                  }
                  if (node.children && node.children.length > 0) {
                    const found = findNodeById(node.children, id)
                    if (found) return found
                  }
                }
                return null
              }
              
              const updatedNode = findNodeById(this.treeData, currentId)
              if (updatedNode) {
                this.handleNodeClick(updatedNode)
              }
            }
          })
        } catch (error) {
          console.error('批量更新节点失败', error)
          // loadingInstance.close()
          this.$message.error('移动失败：批量更新节点时出错')
          // 重新加载树数据，恢复原状
          await this.getList()
        }
      } catch (error) {
        console.error('移动处理失败', error)
        this.$message.error('移动失败：' + (error.message || '未知错误'))
        // 重新加载树数据，恢复原状
        await this.getList()
      }
    },

    // 新增资源
    handleAdd() {
      this.dialogType = 'add'
      this.resourceForm = {
        name: '',
        parentId: null,
        type: 'MENU',
        sort: 0,
        path: '',
        component: '',
        perms: '',
        icon: '',
        status: true,
        visible: true,
        cacheStatus: false,
        mode: 0,
        activeMenu: '',
        remark: '',
        windowOpen: false,
        logo: '', // 新增logo字段
        logoTitle: '' // 新增logoTitle字段
      }
      this.dialogVisible = true
    },

    // 编辑资源
    handleEdit(data) {
      this.dialogType = 'edit'
      this.resourceForm = { ...data }
      
      // 确保应用和目录类型的模式为普通(0)
      if (data.type === 'APP' || data.type === 'DIR') {
        this.resourceForm.mode = 0
      }
      
      this.dialogVisible = true
    },

    // 查看资源
    handleView(data) {
      this.dialogType = 'view'
      this.resourceForm = { ...data }
      this.dialogVisible = true
    },

    // 删除资源
    handleDelete(node, data) {
      const hasChildren = node && node.childNodes && node.childNodes.length > 0
      const message = hasChildren 
        ? '确定要删除该资源及其所有子资源吗？'
        : '确定要删除该资源吗？'

      this.$confirm(message, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteResource(data.id)
          this.$message.success('删除成功')
          this.getList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {})
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.resourceForm.validate()
        
        this.submitLoading = true
        const submitData = { ...this.resourceForm }
        
        if (!submitData.parentId) {
          submitData.parentId = 0
        }
        
        // 去除组件路径前后空格
        if (submitData.component) {
          submitData.component = submitData.component.trim()
        }
        
        // 根据资源类型处理不同的数据
        if (submitData.type === 'APP' || submitData.type === 'DIR') {
          // 应用和目录类型固定模式为普通(0)
          submitData.mode = 0
          // 清空不需要的字段
          submitData.component = ''
          submitData.perms = ''
          // 目录类型才清空激活菜单
          if (submitData.type === 'DIR') {
            submitData.activeMenu = ''
          }
        } else if (submitData.type === 'BUTTON') {
          // 按钮类型清空路由相关字段
          submitData.path = ''
          submitData.component = ''
          submitData.mode = 0
          submitData.activeMenu = ''
          // 保留权限标识字段
        }
        
        // 额外验证：如果是菜单类型，确保其父节点是应用或目录
        if (submitData.type === 'MENU' && submitData.parentId !== 0) {
          const findNodeById = (nodes, id) => {
            for (let node of nodes) {
              if (node.id === id) {
                return node
              }
              if (node.children && node.children.length > 0) {
                const found = findNodeById(node.children, id)
                if (found) return found
              }
            }
            return null
          }
          
          const parentNode = findNodeById(this.treeData, submitData.parentId)
          
          if (parentNode && parentNode.type !== 'APP' && parentNode.type !== 'DIR') {
            this.$message.error('菜单只能放在应用或目录下')
            this.submitLoading = false
            return
          }
        }
        
        try {
          if (this.dialogType === 'add') {
            await addResource(submitData)
            this.$message.success('新增资源成功')
          } else {
            await updateResource(submitData)
            this.$message.success('更新资源成功')
          }
          
          this.dialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error(this.dialogType === 'add' ? '新增资源失败' : '更新资源失败')
        } finally {
          this.submitLoading = false
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    },

    // 保存当前展开的节点状态
    saveExpandedState() {
      if (this.$refs.resourceTree && this.$refs.resourceTree.store && this.$refs.resourceTree.store.states) {
        // 获取当前树的所有展开节点
        const expandedNodes = this.$refs.resourceTree.store.states.expandedKeys || []
        this.expandedKeys = [...expandedNodes]
      }
    },

    // 恢复之前展开的节点状态
    restoreExpandedState() {
      if (this.$refs.resourceTree && this.expandedKeys && this.expandedKeys.length > 0) {
        // 一次性设置所有展开的节点
        this.$nextTick(() => {
          if (this.$refs.resourceTree && this.$refs.resourceTree.store) {
            // 直接将保存的展开状态设置到树组件
            this.$refs.resourceTree.store.defaultExpandedKeys = [...this.expandedKeys];
            // 强制更新视图
            this.$refs.resourceTree.updateKeyChildren();
          }
        });
      }
    },

    // 节点展开时的处理
    handleNodeExpand(data, node) {
      // 将展开的节点 id 添加到 expandedKeys 数组中
      if (data && data.id && this.expandedKeys && !this.expandedKeys.includes(data.id)) {
        this.expandedKeys.push(data.id);
      }
    },

    // 节点收起时的处理
    handleNodeCollapse(data, node) {
      // 从 expandedKeys 数组中移除收起的节点 id
      if (data && data.id && this.expandedKeys) {
        const index = this.expandedKeys.indexOf(data.id);
        if (index !== -1) {
          this.expandedKeys.splice(index, 1);
        }
      }
    },

    // 新增子资源
    handleAddChild() {
      this.dialogType = 'add'
      this.resourceForm = {
        name: '',
        parentId: this.currentResource.id,
        type: 'MENU',
        sort: 0,
        path: '',
        component: '',
        perms: '',
        icon: '',
        status: true,
        visible: true,
        cacheStatus: false,
        mode: 0,
        activeMenu: '',
        remark: '',
        windowOpen: false
      }
      this.dialogVisible = true
    },

    // 复制资源
    handleCopy(data) {
      this.dialogType = 'add'
      // 创建数据副本，移除id
      const dataCopy = { ...data }
      delete dataCopy.id
      
      // 设置资源名称为原名称+"_复制"
      dataCopy.name = dataCopy.name + '_复制'
      
      // 设置表单数据
      this.resourceForm = dataCopy
      
      // 打开对话框
      this.dialogVisible = true
      
      // 提示用户
      this.$message.success('已创建资源副本，请修改后保存')
    },

    // Logo上传前验证
    beforeLogoUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传Logo图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },

    // Logo上传处理
    handleLogoUpload({ file, onProgress, onSuccess, onError }) {
      new UploadHelper().upload(
        file,
        (progress) => {
          onProgress({ percent: progress });
        },
        (url) => {
          onSuccess({ url });
        },
        onError
      );
    },

    // Logo上传成功
    handleLogoSuccess(res) {
      const logo = res.url;
      this.$set(this.resourceForm, 'logo', logo);
      this.$message.success('Logo上传成功');
    },

    // Logo上传失败
    handleLogoError() {
      this.$message.error('Logo上传失败');
    },

    // Logo标题图片上传前验证
    beforeLogoTextUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传Logo标题图片图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },

    // Logo标题图片上传处理
    handleLogoTextUpload({ file, onProgress, onSuccess, onError }) {
      new UploadHelper().upload(
        file,
        (progress) => {
          onProgress({ percent: progress });
        },
        (url) => {
          onSuccess({ url });
        },
        onError
      );
    },

    // Logo标题图片上传成功
    handleLogoTextSuccess(res) {
      const logoTitle = res.url;
      this.$set(this.resourceForm, 'logoTitle', logoTitle);
      this.$message.success('Logo标题图片上传成功');
    },

    // Logo标题图片上传失败
    handleLogoTextError() {
      this.$message.error('Logo标题图片上传失败');
    },
  }
}
</script>

<style lang="scss">
/* 此处不再需要全局样式覆写 */
</style>

<style lang="scss" scoped>
.resource-management {
  height: calc(100vh - 100px);
  background: inherit;
  margin: 24px;
  border-radius: 20px;
  padding: 0;
  overflow: hidden;
  display: flex;
  gap: 24px;

  .resource-tree {
    width: 320px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

    .tree-header {
      padding: 10px;
      border-bottom: 1px solid #eef1f7;
      background: linear-gradient(to right, #fcfcfd, #f9fafc);

      .header-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        span {
          font-size: 18px;
          font-weight: 600;
          color: #1a1f36;
          letter-spacing: 0.5px;
          position: relative;
          padding-left: 12px;
          
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: linear-gradient(to bottom, #409EFF, #64B5F6);
            border-radius: 3px;
          }
        }

        .el-button {
          padding: 9px 18px;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
          position: relative;
          border-radius: 10px;
          background-color: #409EFF;
          border-color: #409EFF;
          overflow: hidden;
          z-index: 1;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
            z-index: -1;
          }

          &:hover {
            transform: translateY(-2px);
            background-color: #5aacff;
            border-color: #5aacff;
            box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }

      .search-box {
        margin-bottom: 15px;
        ::v-deep .el-input {
          width: 100%;

          .el-input__inner {
            border-radius: 10px;
            height: 36px;
            background: #f7f9fc;
            border: 1px solid #e7ebf3;
            
            &:focus {
              background: #fff;
              border-color: #409EFF;
              box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
            }
            
            &:hover {
              border-color: #c0d0e9;
            }
          }
          
          .el-input__prefix {
            left: 10px;
            i {
              color: #8492a6;
            }
          }
        }
      }

      .tip-box {
        display: inline-flex;
        align-items: center;
        background-color: #f0f9ff;
        padding: 6px 12px;
        border-radius: 8px;
        border: 1px dashed #a0cfff;
        cursor: help;
        transition: all 0.3s ease;
        
        &:hover {
          background-color: #ecf5ff;
          border-color: #409EFF;
        }
        
        i {
          color: #409EFF;
          font-size: 14px;
          margin-right: 6px;
        }
        
        span {
          color: #606266;
          font-size: 13px;
          white-space: nowrap;
        }
      }
    }

    .tree-container {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background-color: #fff;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      ::v-deep .el-tree {
        background: transparent;
        
        .el-tree-node__content {
          height: 32px;
          border-radius: 8px;
          margin: 2px 0;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #f5f7fa;
          }

          .custom-tree-node {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;
            
            .node-content {
              display: flex;
              align-items: center;
              gap: 8px;
              position: relative;

              .node-icon {
                height: 16px;
                width: 16px;
                color: #409EFF;
              }

              i {
                font-size: 14px;
                color: #409EFF;
              }

              .resource-type {
                margin-left: 8px;
                margin-right: 4px;
                border-radius: 10px;
                padding: 2px 6px;
                font-size: 11px;
                font-weight: 500;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 18px;
                line-height: 18px;
              }
            }
            
            .node-actions {
              display: none;
              gap: 2px;

              .el-button {
                padding: 1px 2px;
                margin: 0;
                
                i {
                  font-size: 13px;
                  margin: 0;
                }

                &.danger {
                  color: #F56C6C;
                }
              }
            }
          }
        }

        .el-tree-node.is-current > .el-tree-node__content {
          background-color: #ecf5ff !important;
          color: #409EFF;
          font-weight: 500;
          box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
          
          .node-content {
            span {
              color: #409EFF;
            }
          }

          .node-actions {
            display: flex;
          }
        }

        // 拖拽相关样式
        .el-tree-node__drop-prev {
          border-top: 2px solid #409EFF;
          margin-top: -1px;
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: -8px;
            width: 100%;
            height: 16px;
            background-color: rgba(64, 158, 255, 0.1);
            z-index: -1;
          }
        }

        .el-tree-node__drop-next {
          border-bottom: 2px solid #409EFF;
          margin-bottom: -1px;
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -8px;
            width: 100%;
            height: 16px;
            background-color: rgba(64, 158, 255, 0.1);
            z-index: -1;
          }
        }

        .el-tree-node__drop-inner {
          background-color: #ecf5ff;
          border: 2px solid #409EFF;
          margin: -2px;
          box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
        }

        .is-dragging {
          .el-tree-node__content {
            background-color: #f5f7fa;
            opacity: 0.8;
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  .resource-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    overflow: hidden;

    .detail-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
      height: 100%;
      padding: 0;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 6px;
      }

      .info-section {
        background-color: #fff;
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(31, 45, 61, 0.07);

        &:first-child {
          flex-shrink: 0;
        }

        &:last-child {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          font-size: 16px;
          font-weight: 600;
          color: #1a1f36;
          padding-left: 12px;
          border-left: 3px solid #409EFF;
          letter-spacing: 0.5px;

          i {
            margin-right: 8px;
            color: #409EFF;
          }

          .section-actions {
            margin-left: auto;
            display: flex;
            gap: 12px;

            .action-button {
              padding: 6px 12px;
              font-weight: 500;
              transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
              border-radius: 8px;
              
              &.el-button--primary {
                background-color: #409EFF;
                border-color: #409EFF;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
                
                &:hover {
                  transform: translateY(-2px);
                  background-color: #5aacff;
                  border-color: #5aacff;
                  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                }
              }

              &.el-button--danger {
                background-color: #F56C6C;
                border-color: #F56C6C;
                box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
                
                &:hover {
                  transform: translateY(-2px);
                  background-color: #f78989;
                  border-color: #f78989;
                  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
                }
              }

              i {
                margin-right: 4px;
                font-size: 14px;
              }
            }
          }

          .resource-count {
            margin-left: 20px;
            font-size: 14px;
            color: #8492a6;
            font-weight: normal;
          }
        }

        .info-list {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;

          .info-item {
            display: flex;
            align-items: center;

            &.full {
              grid-column: span 2;
            }

            label {
              width: 90px;
              color: #606266;
              font-weight: 500;
            }

            .value-with-icon {
              flex: 1;
              display: flex;
              align-items: center;
              gap: 8px;

              i {
                font-size: 14px;
                color: #409EFF;
                opacity: 0.8;
              }

              .node-icon {
                height: 16px;
                width: 16px;
                color: #409EFF;
              }

              span {
                color: #2c3e50;
                line-height: 1.4;
              }
              
              .el-tag {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 24px;
                line-height: 24px;
                padding: 0 12px;
                border-radius: 12px;
              }
            }
          }
        }

        .children-table-container {
          // 移除之前的样式
        }

        .table-container {
          height: 100%;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .custom-table {
          margin-bottom: 0;
          
          ::v-deep {
            .el-table__header-wrapper {
              th {
                background: linear-gradient(to right, #f7f9fc, #f3f6fa) !important;
                font-weight: 600;
                color: #1a1f36;
                height: 40px;
                padding: 8px 0;
              }
            }
            
            .el-table__body-wrapper {
              td {
                padding: 6px 0;
              }

              &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
              }
              
              &::-webkit-scrollbar-thumb {
                border-radius: 3px;
                background: rgba(144, 147, 153, 0.3);
                
                &:hover {
                  background: rgba(144, 147, 153, 0.5);
                }
              }
              
              &::-webkit-scrollbar-track {
                background: transparent;
              }
            }
            
            .resource-info {
              display: flex;
              align-items: center;
              gap: 8px;

              i {
                color: #409EFF;
                font-size: 14px;
              }
              
              .node-icon {
                height: 16px;
                width: 16px;
                color: #409EFF;
              }
              
              span {
                font-size: 14px;
              }
            }

            .el-tag {
              border-radius: 12px;
              padding: 0 10px;
              height: 22px;
              line-height: 22px;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .operation-column {
              .cell {
                white-space: nowrap;
                display: flex;
                justify-content: center;
                gap: 4px;

                .el-button {
                  padding: 2px 4px;
                  font-size: 12px;
                  margin: 0;
                  height: 24px;
                  line-height: 1;
                  display: inline-flex;
                  align-items: center;
                  
                  i {
                    margin-right: 0;
                    font-size: 13px;
                  }

                  &.danger {
                    color: #F56C6C;
                  }
                }
              }
            }
          }
        }

        .pagination-container {
          margin-bottom: 20px;
          padding: 16px 0 0;
          display: flex;
          justify-content: flex-end;
          background: #fff;
          border-top: 1px solid #f0f2f5;
          
          ::v-deep .el-pagination {
            padding: 0;
            font-weight: normal;
            
            .btn-prev,
            .btn-next,
            .el-pager li {
              background: transparent;
              border: 1px solid #e0e5ee;
              
              &:hover:not(.disabled) {
                color: #409EFF;
                border-color: #409EFF;
              }
              
              &.active {
                background: #409EFF;
                border-color: #409EFF;
                color: #fff;
              }
            }
            
            .el-pagination__sizes {
              margin-right: 15px;
              
              .el-input__inner {
                border-radius: 4px;
                border-color: #e0e5ee;
                
                &:hover {
                  border-color: #c0d0e9;
                }
              }
            }
            
            .el-pagination__total {
              margin-right: 15px;
            }
            
            .el-pagination__jump {
              margin-left: 15px;
              
              .el-input__inner {
                border-radius: 4px;
                border-color: #e0e5ee;
                
                &:hover {
                  border-color: #c0d0e9;
                }
              }
            }
          }
        }

        .empty-children {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 50px 0;
          text-align: center;
          
          .empty-image {
            font-size: 64px;
            color: #c0c4cc;
            margin-bottom: 20px;
            
            i {
              background: linear-gradient(45deg, #e0e6f1, #f3f6fb);
              border-radius: 50%;
              padding: 20px;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            }
          }
          
          .empty-text {
            color: #909399;
            font-size: 16px;
            margin-bottom: 20px;
          }
          
          .el-button {
            margin-top: 10px;
            padding: 9px 20px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
            border-radius: 8px;
            background-color: #409EFF;
            border-color: #409EFF;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            
            &:hover {
              transform: translateY(-2px);
              background-color: #5aacff;
              border-color: #5aacff;
              box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
            }
            
            i {
              margin-right: 6px;
            }
          }
        }
      }
    }
  }
}

.resource-form {
  .form-section {
    background-color: #f8f9fb;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: inset 0 -1px 0.15625vw rgba(0, 0, 0, 0.05), inset 0 1px 0.15625vw rgba(0, 0, 0, 0.05);

    &:hover {
      background-color: #f5f7fa;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a1f36;
      margin-bottom: 24px;
      padding-left: 12px;
      border-left: 3px solid #409EFF;
      letter-spacing: 0.5px;
    }

    ::v-deep {
      .el-form-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__label {
          font-weight: 500;
          color: #606266;
        }

        .el-input,
        .el-select,
        .el-cascader {
          width: 100%;
        }

        .el-input__inner {
          border-radius: 8px;
          height: 36px;
          background: #fff;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }

        .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #e0e5ee;
          
          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.15);
          }
          
          &:hover {
            border-color: #c0d0e9;
          }
        }
      }
    }
  }
}

::v-deep .el-dialog {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  margin-top: 8vh !important;
  max-height: 84vh;
  display: flex;
  flex-direction: column;

  .el-dialog__header {
    padding: 24px;
    border-bottom: 1px solid #ebeef5;
    margin: 0;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1f36;
      position: relative;
      padding-left: 12px;
      
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 18px;
        background: linear-gradient(to bottom, #409EFF, #64B5F6);
        border-radius: 3px;
      }
    }
    
    .el-dialog__headerbtn {
      top: 20px;
      right: 20px;
      
      .el-dialog__close {
        font-size: 18px;
        color: #909399;
        font-weight: bold;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 30px 24px;
    overflow-y: auto;
    background: #f8f9fb;

    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(144, 147, 153, 0.3);
      border-radius: 3px;
      
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
    background: linear-gradient(to right, #fcfcfd, #f9fafc);
    
    .el-button {
      padding: 9px 20px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

      & + .el-button {
        margin-left: 12px;
      }

      &--default {
        border-color: #dcdfe6;
        background: linear-gradient(to bottom, #fff, #f9fafc);
        
        &:hover {
          border-color: #c0c4cc;
          color: #606266;
          background: #f5f7fa;
        }
      }

      &--primary {
        background: #409EFF;
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        
        &:hover {
          transform: translateY(-2px);
          background-color: #5aacff;
          border-color: #5aacff;
          box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

.highlight {
  color: #409EFF;
  font-weight: 600;
  position: relative;
  z-index: 1;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: rgba(64, 158, 255, 0.2);
    z-index: -1;
    border-radius: 3px;
  }
}

.logo-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  ::v-deep .el-upload {
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    width: 180px;
    height: 100px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9fafc;

    &:hover {
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
      transform: translateY(-2px);
      
      .upload-placeholder {
        color: #409EFF;
        border-color: #409EFF;
      }
      
      .uploaded-logo {
        transform: scale(1.03);
      }
    }
  }
  
  .upload-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  
  .upload-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    border: 1px dashed #dcdfe6;
    border-radius: 8px;
    transition: all 0.3s;
    
    i {
      font-size: 24px;
      margin-bottom: 8px;
    }
    
    span {
      font-size: 13px;
    }
  }
  
  .uploaded-logo {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
    border-radius: 8px;
    transition: all 0.3s;
  }
  
  .view-mode {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
}

.logo-text-uploader {
  height: 100px;
  
  ::v-deep .el-upload {
    background: #f5f7fa;
  }
}
</style>
