.print {
    font-family: 宋体;
    position: relative;


    .result {
        position: absolute;
        width: 100px;
        height: 100px;
        right: 30px;
        top: 10px;
    }

    .qr-code {
        position: absolute;
        text-align: center;
        top: 0;
        right: 0;
    }
}

h2 {
    text-align: center;
}


.tit {
    text-align: center;
    margin-top: -13px;
}

.header {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    margin-bottom: 10px;

    div {
        padding: 5px 0;
    }
}

table {
    font-size: 16px;
    width: 100%;
    border-collapse: collapse;
    padding: 2px;

    th {
        width: 15%;
    }
}

table tr th,
table tr td {
    text-align: left;
    border: 1px solid #464648;
    padding: 5px 10px;
    height: 40px;
    font-size: 16px;
}

.footer {
    font-size: 16px;
    margin-top: 20px;

    div {
        display: inline-block;
        width: 50%;
    }

    div:last-child {
        text-align: right;
    }
}

.process-list {
    .base-info {
        position: relative;

        &>div {
            position: absolute;
        }

        div:nth-child(3) {
            right: 0;
        }

        div:nth-child(2) {
            right: 200px;
        }

        .sign {
            position: absolute;
            left: 50px;
            width: 110px;
            height: 50px;
        }
    }

    .comment {
        margin-left: 10px;
        margin-top: 40px;
    }
}

.audit-info {
    height: 100px;
    position: relative;

    &>div {
        position: absolute;
    }

    div:nth-child(3) {
        bottom: 0;
        left: 0;
    }

    div:nth-child(2) {
        left: 50px;
        top: 20px;
    }

    div:nth-child(4) {
        bottom: 10px;
        right: 0;
        font-size: 11px!important;
    }

    img {
        position: absolute;
        left: 50px;
        width: 110px;
        height: 50px;
    }
}

.goWay {
    display: flex;
    margin-right: 15px;
    .kuang {
        width: 15px;
        height: 15px;
        border: 1px solid #464648;
        position: relative;
        margin-right: 5px;
    }

    .act {
        position: absolute;
        width: 5px;
        height: 10px;
        border-right: 2px solid #464648;
        border-bottom: 2px solid #464648;
        transform: rotate(40deg);
        left:3px
    }
}

// ::v-deep .print-checkbox .el-checkbox .el-checkbox__label {
//     font-size: 13px;
// }

// ::v-deep.el-checkbox .el-checkbox__input .el-checkbox__inner {
//     width: 13px;
//     height: 13px;
// }