<template>
  <div class="chat-options">
    <!-- 移动端折叠按钮 -->
    <div class="mobile-toggle" @click="toggleOptions">
      <span>聊天选项</span>
      <i :class="['el-icon-arrow-down', { 'is-expanded': isExpanded }]"></i>
    </div>
    <!-- 选项内容区域 -->
    <div class="options-content" :class="{ 'is-expanded': isExpanded }">
      <div class="option-group">
        <label>大模型：</label>
        <el-select v-model="curModel" @change="$emit('model-change', curModel)" :disabled="loading" size="small">
          <el-option value="" disabled>请选择模型</el-option>
          <el-option v-for="model in models" :key="model.id" :value="model.id" :label="model.id">
          </el-option>
        </el-select>
      </div>
      <div class="option-group">
        <label>知识库：</label>
        <el-select v-model="selectedKnowledgeBase" @change="handleKnowledgeBaseChange" size="small">
          <el-option value="">不使用知识库</el-option>
          <el-option v-for="kb in knowledgeBases" :key="kb.kb_name" :value="kb.kb_name" :label="kb.kb_name">
          </el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>

<script>
import api from '../../api'

export default {
  name: 'ChatOptions',
  props: {
    model: String,
    models: Array,
    loading: Boolean
  },
  data() {
    return {
      knowledgeBases: [],
      curModel: this.model,
      selectedKnowledgeBase: '',
      isExpanded: false // 添加展开状态控制
    }
  },
  watch: {
    model(newValue) {
      this.curModel = newValue
    }
  },
  methods: {
    async loadKnowledgeBases() {
      try {
        const response = await api.knowledge.getList()
        this.knowledgeBases = response.data || []
      } catch (error) {
        console.error('加载知识库列表失败：', error)
        this.knowledgeBases = []
      }
    },
    handleKnowledgeBaseChange() {
      this.$emit('knowledge-base-change', this.selectedKnowledgeBase)
    },
    toggleOptions() {
      this.isExpanded = !this.isExpanded;
    }
  },
  created() {
    this.loadKnowledgeBases()
  }
}
</script>

<style scoped>
.chat-options {
  display: flex;
  gap: 20px;
  padding: 10px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 移动端折叠按钮样式 */
.mobile-toggle {
  display: none;
  width: 100%;
  padding: 12px 14px;
  background: #fff;
  border-radius: 12px;
  cursor: pointer;
  font-size: 15px;
  color: #4a5568;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  margin: 0;
  box-sizing: border-box;
}

.mobile-toggle span {
  font-weight: 500;
}

.mobile-toggle i {
  transition: transform 0.3s ease;
  font-size: 15px;
}

.mobile-toggle i.is-expanded {
  transform: rotate(180deg);
}

/* 选项内容区域 */
.options-content {
  display: flex;
  gap: 20px;
  width: 100%;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

select {
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  outline: none;
  transition: all 0.3s;
}

select:focus {
  border-color: #42b983;
}

select:disabled {
  background: #f5f7fa;
  cursor: not-allowed;
}

@media screen and (max-width: 768px) {
  .chat-options {
    flex-direction: column;
    gap: 0;
    padding: 8px;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
  }

  .mobile-toggle {
    display: flex;
    width: 100%;
    margin: 0 auto;
  }

  .options-content {
    display: none;
    flex-direction: column;
    gap: 10px;
    padding: 10px 0;
    width: 100%;
  }

  .options-content.is-expanded {
    display: flex;
  }

  .option-group {
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }

  .option-group :deep(.el-select) {
    width: 80%;
  }

  .option-group label {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
    min-width: 50px;
  }

  /* 下拉框样式优化 */
  .option-group :deep(.el-input__inner) {
    font-size: 14px;
    height: 32px!important;
    line-height: 32px;
  }

  .option-group :deep(.el-input__suffix) {
    line-height: 32px;
  }

  .mobile-toggle {
    padding: 10px 12px;
    font-size: 14px;
    height: 38px;
    line-height: 1;
  }

  .mobile-toggle:active {
    background: #f8f9fa;
  }

  .mobile-toggle i {
    font-size: 14px;
    color: #666;
  }

  .mobile-toggle span {
    font-weight: 500;
  }

  .options-content {
    padding-top: 8px;
  }
}

/* 下拉框样式 */
.option-group :deep(.el-input__inner) {
  border-radius: 10px !important;
}

@media screen and (max-width: 768px) {
  .mobile-toggle {
    border-radius: 10px;
  }

  .option-group :deep(.el-input__inner) {
    border-radius: 8px !important;
  }
}
</style>

<style>
/* 添加全局样式以影响下拉菜单 */
@media screen and (max-width: 768px) {
  .el-select-dropdown__item {
    font-size: 14px !important;
    height: 30px !important;
    line-height: 36px !important;
    padding: 0 12px !important;
    width: 100% !important;
  }

  .el-select-dropdown {
  }

  .el-select-dropdown__list {
    padding: 4px 0 !important;
  }
}

/* 下拉菜单样式 */
.el-select-dropdown {
  border-radius: 12px !important;
  overflow: hidden;
}

@media screen and (max-width: 768px) {
  .el-select-dropdown {
    border-radius: 10px !important;
  }
}
</style>
